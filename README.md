# agnes-ui

运营质控管理平台UI。  

## 安装依赖 
```
如果该项目需要引入公司内部的某个npm包， 需要在.npmrc文件中配置npm源    
npm -g install yarn 

yarn install
如果遇到error ids@1.0.3: The engine "node" is incompatible with this module. Expected version >16 Got 14.20.1
执行 yarn config set ignore-engines true 后再install
```
 
### 本地运行 
```
yarn serve
```

### 编译打包
```
yarn build
 --在package.json中，执行脚本进行打包和上传
   pkg：打包
   uploadDevNexus：上传部署包到nexus仓库dev环境，用于开发环境部署
   uploadReleaseNexus：上传部署包到nexus仓库release环境，用于生产环境部署
   deploy：npm包发布到公司制品库（npm仓库），用于项目引入
```

### [前端开发规范](http://wiki.goupwith.com/pages/viewpage.action?pageId=10683002)

#### 命名注意事项：
1. 以下情况使用小写字母+横杠分隔的kebab-case命名方式： 
> 文件夹&文件名
> 样式名
> 书写在template中的组件名

2. 以下情况使用小写开头的驼峰命名，Camel命名方式
> 方法名
> 变量名

3. Class命名使用大写开头的驼峰命名, Pascal命名方式：
> Class名

4. 全局/模块级的常量使用 大写、下划线分隔命名

#### 常用组件
1. gf-dict-select       字典下拉框
2. gf-dict-radio-group  字典选项组
3. dialog-footer        对话框底部操作栏
4. gf-grid              agGrid表格组件封装


#### 异常处理规范
```js
try {
    await this.$api.modelConfigApi.saveModel(this.form);
  
    this.$msg.success('保存成功');
    this.$dialog.close(this);
} catch (reason) {
    this.$msg.error(reason);
}  
```

#### 提交时使用遮罩
1. 全局遮罩       this.$app.blockingApp(p)
2. 当前页面遮罩    this.$app.blockingView(p)
```js
try {
    const p = this.$api.modelConfigApi.saveModel(this.form);
    await this.$app.blockingApp(p);
 
    this.$msg.success('保存成功');
    this.$dialog.close(this);
} catch (reason) {
    this.$msg.error(reason);
}

```

#### 模块介绍
```
    agnes-ac        执行中心
    agnes-acnt      账户
    agnes-biz       业务模块
    agnes-bpmn      流程引擎
    agnes-channel   渠道
    agnes-common    公共组件
    agnes-dc        文件处理
    agnes-dop       基础功能模块
    agnes-ec        业务事件
    agnes-mc        管理人
    agnes-remind    消息中心
    agnes-tc        任务中心
    datav           大屏
```

#### 其他注意事项
1. 使用=== 替代== 判断相等操作，且保证变量类型相同
2. 尽量使用 async/await语法，避免过多的Promise的回调嵌套
3. 空值判断的代码优化：
```
...
if (x!=='' && x!==null && x!==undefined)  等同于  if (x)

...
if (!x){
    x="0"
}
等同于
x = x || '0'  

```

4. 使用lodash工具类方法，避免重复造轮子
[参考链接](https://www.lodashjs.com/)

## new
由于低码原因，拉取代码后需进行如下配置
### 调整amis-core源码
```js
//amis-core/esm/factory.js 注释掉53行代码
if (renderersMap[config.name]) {
    // throw new Error("The renderer with name \"".concat(config.name, "\" has already exists, please try another name!"));
}
```
### node 内存超出报错
原因: node限制内存使用导致
解决方案
```
yarn  fix-memory-limit 
//或者 npm run  fix-memory-limit 
```


# 测试 流水线4
