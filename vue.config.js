const { defineConfig } = require('@vue/cli-service');
const CssMinimizerPlugin = require('css-minimizer-webpack-plugin');
const TerserPlugin = require('terser-webpack-plugin');
const isDev = process.env.NODE_ENV === 'development';
const path = require('path');
const plugins = require('./plugns/plugins');

module.exports = defineConfig({
  productionSourceMap: isDev,
  transpileDependencies: ['lezer-feel', '@hexop/data-pipe-ui', 'quill'],
  lintOnSave: false,
  runtimeCompiler: true,
  configureWebpack: {
    optimization: {
      minimize: !isDev,
      minimizer: [
        // css压缩也可以写到optimization.minimizer里面，效果一样的
        new CssMinimizerPlugin(),
        // 当生产模式会默认开启TerserPlugin，但是我们需要进行其他配置，就要重新写了
        new TerserPlugin({
          parallel: require('os').cpus().length // 开启多进程
        })
      ]
    },
    cache: isDev
      ? {
          type: 'filesystem',
          allowCollectingMemory: true
        }
      : undefined,
    resolve: {
      alias: {
        projectConfig: path.resolve('./src/modules/datav/components/biz/work-bench-comp/config')
      },
      fallback: {
        path: require.resolve('path-browserify'),
        stream: require.resolve('stream-browserify'),
        timers: false
      }
    },
    plugins
  },
  chainWebpack: config => {
    if (!isDev) {
      config.mode = 'production';
      config.devtool = 'nosources-source-map';
      config.module
        .rule('js')
        .test(/\.js$/)
        .use('thread-loader')
        .loader('thread-loader')
        .options({
          workers: require('os').cpus().length - 1
        })
        .end();
    }
    config.set('externals', {
      jquery: 'jQuery',
      'element-ui': 'ELEMENT',
      echarts: 'echarts',
      vue: 'Vue',
      'vue-router': 'VueRouter',
      lodash: '_',
      screenfull: 'screenfull',
      'vue-drag-resize': 'VueDragResize'
    });
  },

  devServer: {
    port: 8006,
    hot: true,
    client: {
      overlay: false
    },
    proxy: {
      // '/api/xcp-page': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/xcp-page': '/'
      //     }
      // },
      // '/api/static': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/static': '/'
      //     }
      // },
      // '/api/xcp-model': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/xcp-model': '/'
      //     }
      // },
      // '/api/xcp-office': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/xcp-model': '/'
      //     }
      // },
      //
      // '/api/xcp-server': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/xcp-server': '/'
      //     }
      // },
      // '/api/xcp': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/xcp': '/xcp'
      //     }
      // },
      // '/api/agnes-biz': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/agnes-biz': '/'
      //     }
      // },
      // '/api/ecm-server': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/ecm-server': '/'
      //     }
      // },
      // '/api/agnes-ec': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/agnes-ec': '/'
      //     }
      // },
      // '/api/agnes-remind': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/agnes-remind': '/'
      //     }
      // },
      // '/api/agnes-ac': {
      //     // target: '',
      //     target: ':9002',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/agnes-ac': '/'
      //     }
      // },
      // '/api/agnes-app': {
      //     target: 'http://************:9003',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/agnes-app': '/'
      //     }
      // },
      // '/api/agnes-datav': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/agnes-datav': '/'
      //     }
      // },
      // '/api/data-pipe': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/data-pipe': '/'
      //     }
      // },
      // '/api/data-pipe': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/data-pipe': '/'
      //     }
      // },
      // '/api/dop-bpmn': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/dop-bpmn': '/'
      //     }
      // },
      // '/dop-kpi/': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      // },
      // '/api/dop-channel': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //         pathRewrite: {
      //             '^/api/dop-channel': '/'
      //         }
      // },
      // '/api/agnes-audit': {
      //     target: '',
      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/agnes-audit': '/'
      //     }
      // },
      // '/api/agnes-biz': {
      // target: 'http://************:9109/agnes-sh',
      // target: 'http://agnes.hexop.cn:9000/api',
      // target: 'http://***********:8080/api',
      // target: 'http://agnes.hexop.cn:8000/api',
      // target: 'http://172.18.24.232:8080/api',
      // target: 'http://172.131.128.4:8070/api',
      // target: 'http://172.18.25.250:8080/api',
      // target: 'http://172.18.25.168:8080/api',
      // target: 'http://172.18.26.254:8080/api',
      // target: 'http://172.18.1.160:8080/api',
      // target: 'http://172.18.2.5:8006/api',
      // target: 'http://172.18.2.4:8006//api',
      // target: 'http://172.131.128.6:8080/api',
      // ws: true,
      // changeOrigin: true,
      // pathRewrite: {
      //     '^/api/agnes-biz': '/'
      // }
      // },
      // '/api/agnes-biz': {
      //     // target: 'http://************:9109/agnes-sh',
      //     // target: 'http://agnes.hexop.cn:9000/api',
      //     target: 'http://172.18.25.250:8080/api',
      //     // target: 'http://172.18.25.168:8080/api',
      //     // target: 'http://172.18.26.254:8080/api',
      //     // target: 'http://172.18.1.160:8080/api',
      //     // target: 'http://172.18.2.5:8006/api',
      //     // target: 'http://172.18.2.4:8006//api',
      //     // target: 'http://172.131.128.6:8080/api',

      //     ws: true,
      //     changeOrigin: true,
      //     pathRewrite: {
      //         '^/api/agnes-biz': '/'
      //     }
      // },
      '/api': {
        // target: 'http://172.18.25.250:8080/api',
        // target: 'http://172.131.128.4:8070/api',
        target: 'http://172.18.26.172:8080/api',
        // target: 'http://172.18.26.254:8080/api',
        // target: 'http://172.21.0.157:8080/api',
        // target: 'http://172.18.26.45:8080/api',
        // target: 'http://172.18.24.232:8080/api',
        // target: 'http://172.131.128.6:8080/api',
        // target: 'http://172.18.24.204:8080/api',
        // target: 'http://172.21.0.52:8080/api',
        // target: 'http://172.18.26.172:8080/api',
        ws: true,
        changeOrigin: true,
        pathRewrite: {
          '^/api/': '/'
        }
      }
    }
  },
  pages: {
    index: {
      // page 的入口
      entry: 'app/main.js'
    }
  }
});
