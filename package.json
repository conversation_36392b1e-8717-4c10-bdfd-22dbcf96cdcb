{"name": "@hexop/agnes-ui", "version": "1.16.8-beta.11", "private": false, "main": "app/index.js", "license": "ISC", "author": "a<PERSON><PERSON>", "files": ["app", "src", "plugns", "dist"], "scripts": {"serve": "vue-cli-service serve", "build": "cross-env NODE_OPTIONS=--max_old_space_size=8192 vue-cli-service build --report --mode production", "build:local": "SET NODE_OPTIONS=--openssl-legacy-provider && vue-cli-service build --report --mode production", "lint": "vue-cli-service lint -f json -o report.json", "deploy": "yarn publish --registry=http://repo.hexinfo.cn:8000/artifactory/api/npm/agnes-npm-dev/ --network-timeout 600000", "test:unit": "vue-cli-service test:unit", "unit": "run-s lint test:unit", "api": "swagger-api http://localhost:8004/v2/api-docs", "pkg": "yarn install && yarn build && echo 1.16.6> ./dist/version && tar czvf agnes-ui-1.16.6.tgz ./dist/*", "pkg:local": "tar czvf agnes-ui-1.16.6.tgz ./agnes-ui/*", "uploadDevNexus": "curl -v -u admin:admin123 --upload-file agnes-ui-1.16.6.tgz http://***********:8081/repository/agnes-dev/agnes-ui/agnes-ui-1.16.6.tgz", "uploadReleaseNexus": "curl -v -u admin:admin123 --upload-file agnes-ui-1.16.6.tgz http://***********:8081/repository/agnes-release/agnes-ui/agnes-ui-1.16.6.tgz", "uploadReleaseNexus:local": "curl -v -u admin:admin123 --upload-file agnes-ui-1.16.6.tgz http://***********:8081/repository/agnes-release/agnes-ui/agnes-ui-1.16.6.tgz", "fix-memory-limit": "cross-env LIMIT=5120 increase-memory-limit", "test": " echo 1.16.6> ./dist/version && tar czvf agnes-ui-1.16.6.tgz ./dist/*"}, "publishConfig": {"registry": "http://repo.hexops.cn/artifactory/api/npm/agnes-npm-dev"}, "resolutions": {"element-ui": "2.15.14", "async-validator": "4.0.4", "crypto-js": "4.2.0", "@ag-grid-enterprise/charts": "31.3.3", "vue": "2.7.16", "axios": "1.7.4"}, "overrides": {"element-ui": "2.15.14", "async-validator": "4.0.4", "crypto-js": "4.2.0", "@ag-grid-enterprise/charts": "31.3.3", "vue": "2.7.16", "axios": "1.7.4"}, "dependencies": {"@hexop/data-pipe-ui": "1.1.9-beta.39", "@babel/plugin-proposal-optional-chaining": "^7.21.0", "@hex/bpmn-ui": "0.0.18", "@hex/gf-ui-datav": "^1.1.1", "@hex/swagger-api": "1.0.6", "@hex/xcp-sdk-ui": "2.0.3", "@hex/xcp-server-ui": "0.1.35-1", "@hexinfo/ares-ui": "5.19.3", "@hexinfo/x-doc-ui": "^2.4.9", "@hexinfo/xcp-server-ui": "0.6.0-beta.672", "@hexop/data-audit-ui": "1.2.14-beta.2", "@agnes/file-deal-ui": "1.4.10-beta.8", "@hexop/kms-ui": "1.0.1-beta.436", "@jiaminghi/data-view": "^2.9.0", "@vue/composition-api": "^1.7.0", "@wchbrad/vue-easy-tree": "^1.0.12", "bpmn-js-properties-panel": "5.13.0", "camunda-bpmn-moddle": "7.0.1", "compression-webpack-plugin": "^6.1.1", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "core-js": "^3.8.3", "d3": "^3.5.17", "dayjs": "^1.10.8", "lodash": "^4.17.15", "monaco-editor": "0.30.1", "monaco-editor-webpack-plugin": "6.0.0", "object-assign": "^4.1.1", "pdfvuer": "1.9.2", "pinyin-match": "^1.1.1", "pretty-data": "0.40.0", "screenfull": "^5.0.2", "v-charts": "^1.19.0", "v-infinite-scroll": "^1.0.4", "vue": "^2.6.14", "vue-class-component": "^7.2.3", "vue-codemirror": "^4.0.6", "vue-drag-resize": "^1.3.2", "vue-grid-layout": "^2.3.7", "vue-property-decorator": "^9.1.2", "vue-router": "^3.5.1", "vue-seamless-scroll": "^1.1.23", "vue-virtual-scroll-list": "^2.3.5", "vuedraggable": "2.23.2", "vuex": "^3.6.2", "xml2js": "0.6.2", "echarts-liquidfill": "3.1.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^5.4.0", "@typescript-eslint/parser": "^5.4.0", "@vue/babel-preset-jsx": "^1.4.0", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-e2e-cypress": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-plugin-typescript": "~5.0.0", "@vue/cli-plugin-vuex": "~5.0.0", "@vue/cli-service": "~5.0.0", "@vue/eslint-config-typescript": "^9.1.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "cross-env": "^7.0.3", "cypress": "^9.7.0", "eslint": "^7.32.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^8.0.3", "less": "^4.0.0", "less-loader": "^8.0.0", "lint-staged": "^11.1.2", "path-browserify": "^1.0.1", "prettier": "^2.4.1", "stream-browserify": "^3.0.0", "thread-loader": "3.0.4", "typescript": "~4.5.5", "vue-template-compiler": "^2.6.14"}, "packageManager": "pnpm@10.13.1+sha512.37ebf1a5c7a30d5fabe0c5df44ee8da4c965ca0c5af3dbab28c3a1681b70a256218d05c81c9c0dcf767ef6b8551eb5b960042b9ed4300c59242336377e01cfad"}