/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
(function(t,e){"object"===typeof exports&&"object"===typeof module?module.exports=t.pdfjsLib=e():"function"===typeof define&&define.amd?define("pdfjs-dist/build/pdf",[],(()=>t.pdfjsLib=e())):"object"===typeof exports?exports["pdfjs-dist/build/pdf"]=t.pdfjsLib=e():t["pdfjs-dist/build/pdf"]=t.pdfjsLib=e()})(globalThis,(()=>(()=>{"use strict";var __webpack_modules__=[,(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.VerbosityLevel=e.Util=e.UnknownErrorException=e.UnexpectedResponseException=e.TextRenderingMode=e.RenderingIntentFlag=e.PromiseCapability=e.PermissionFlag=e.PasswordResponses=e.PasswordException=e.PageActionEventType=e.OPS=e.MissingPDFException=e.MAX_IMAGE_SIZE_TO_CACHE=e.LINE_FACTOR=e.LINE_DESCENT_FACTOR=e.InvalidPDFException=e.ImageKind=e.IDENTITY_MATRIX=e.FormatError=e.FeatureTest=e.FONT_IDENTITY_MATRIX=e.DocumentActionEventType=e.CMapCompressionType=e.BaseException=e.BASELINE_FACTOR=e.AnnotationType=e.AnnotationReplyType=e.AnnotationPrefix=e.AnnotationMode=e.AnnotationFlag=e.AnnotationFieldFlag=e.AnnotationEditorType=e.AnnotationEditorPrefix=e.AnnotationEditorParamsType=e.AnnotationBorderStyleType=e.AnnotationActionEventType=e.AbortException=void 0,e.assert=L,e.bytesToString=X,e.createValidAbsoluteUrl=j,e.getModificationDate=ht,e.getUuid=ft,e.getVerbosityLevel=F,e.info=D,e.isArrayBuffer=lt,e.isArrayEqual=ct,e.isNodeJS=void 0,e.normalizeUnicode=gt,e.objectFromMap=Q,e.objectSize=J,e.setVerbosityLevel=R,e.shadow=B,e.string32=Y,e.stringToBytes=K,e.stringToPDFString=rt,e.stringToUTF8String=at,e.unreachable=O,e.utf8StringToString=ot,e.warn=I,i(2),i(84),i(86),i(87),i(89),i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123);const n="object"===typeof process&&process+""==="[object process]"&&!process.versions.nw&&!(process.versions.electron&&process.type&&"browser"!==process.type);e.isNodeJS=n;const s=[1,0,0,1,0,0];e.IDENTITY_MATRIX=s;const r=[.001,0,0,.001,0,0];e.FONT_IDENTITY_MATRIX=r;const a=1e7;e.MAX_IMAGE_SIZE_TO_CACHE=a;const o=1.35;e.LINE_FACTOR=o;const l=.35;e.LINE_DESCENT_FACTOR=l;const c=l/o;e.BASELINE_FACTOR=c;const h={ANY:1,DISPLAY:2,PRINT:4,SAVE:8,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};e.RenderingIntentFlag=h;const d={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};e.AnnotationMode=d;const u="pdfjs_internal_editor_";e.AnnotationEditorPrefix=u;const p={DISABLE:-1,NONE:0,FREETEXT:3,STAMP:13,INK:15};e.AnnotationEditorType=p;const g={RESIZE:1,CREATE:2,FREETEXT_SIZE:11,FREETEXT_COLOR:12,FREETEXT_OPACITY:13,INK_COLOR:21,INK_THICKNESS:22,INK_OPACITY:23};e.AnnotationEditorParamsType=g;const f={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};e.PermissionFlag=f;const m={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};e.TextRenderingMode=m;const b={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};e.ImageKind=b;const v={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};e.AnnotationType=v;const _={GROUP:"Group",REPLY:"R"};e.AnnotationReplyType=_;const y={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};e.AnnotationFlag=y;const A={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};e.AnnotationFieldFlag=A;const S={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};e.AnnotationBorderStyleType=S;const x={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};e.AnnotationActionEventType=x;const E={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};e.DocumentActionEventType=E;const w={O:"PageOpen",C:"PageClose"};e.PageActionEventType=w;const C={ERRORS:0,WARNINGS:1,INFOS:5};e.VerbosityLevel=C;const T={NONE:0,BINARY:1};e.CMapCompressionType=T;const P={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotation:80,endAnnotation:81,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};e.OPS=P;const k={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};e.PasswordResponses=k;let M=C.WARNINGS;function R(t){Number.isInteger(t)&&(M=t)}function F(){return M}function D(t){M>=C.INFOS&&console.log(`Info: ${t}`)}function I(t){M>=C.WARNINGS&&console.log(`Warning: ${t}`)}function O(t){throw new Error(t)}function L(t,e){t||O(e)}function N(t){switch(t?.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}function j(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(!t)return null;try{if(i&&"string"===typeof t){if(i.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e?.length>=2&&(t=`http://${t}`)}if(i.tryConvertEncoding)try{t=at(t)}catch{}}const n=e?new URL(t,e):new URL(t);if(N(n))return n}catch{}return null}function B(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return Object.defineProperty(t,e,{value:i,enumerable:!n,configurable:!0,writable:!1}),i}const U=function(){function t(e,i){this.constructor===t&&O("Cannot initialize BaseException."),this.message=e,this.name=i}return t.prototype=new Error,t.constructor=t,t}();e.BaseException=U;class z extends U{constructor(t,e){super(t,"PasswordException"),this.code=e}}e.PasswordException=z;class H extends U{constructor(t,e){super(t,"UnknownErrorException"),this.details=e}}e.UnknownErrorException=H;class W extends U{constructor(t){super(t,"InvalidPDFException")}}e.InvalidPDFException=W;class q extends U{constructor(t){super(t,"MissingPDFException")}}e.MissingPDFException=q;class G extends U{constructor(t,e){super(t,"UnexpectedResponseException"),this.status=e}}e.UnexpectedResponseException=G;class V extends U{constructor(t){super(t,"FormatError")}}e.FormatError=V;class $ extends U{constructor(t){super(t,"AbortException")}}function X(t){"object"===typeof t&&void 0!==t?.length||O("Invalid argument for bytesToString");const e=t.length,i=8192;if(e<i)return String.fromCharCode.apply(null,t);const n=[];for(let s=0;s<e;s+=i){const r=Math.min(s+i,e),a=t.subarray(s,r);n.push(String.fromCharCode.apply(null,a))}return n.join("")}function K(t){"string"!==typeof t&&O("Invalid argument for stringToBytes");const e=t.length,i=new Uint8Array(e);for(let n=0;n<e;++n)i[n]=255&t.charCodeAt(n);return i}function Y(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,255&t)}function J(t){return Object.keys(t).length}function Q(t){const e=Object.create(null);for(const[i,n]of t)e[i]=n;return e}function Z(){const t=new Uint8Array(4);t[0]=1;const e=new Uint32Array(t.buffer,0,1);return 1===e[0]}function tt(){try{return new Function(""),!0}catch{return!1}}e.AbortException=$;class et{static get isLittleEndian(){return B(this,"isLittleEndian",Z())}static get isEvalSupported(){return B(this,"isEvalSupported",tt())}static get isOffscreenCanvasSupported(){return B(this,"isOffscreenCanvasSupported","undefined"!==typeof OffscreenCanvas)}static get platform(){return"undefined"===typeof navigator?B(this,"platform",{isWin:!1,isMac:!1}):B(this,"platform",{isWin:navigator.platform.includes("Win"),isMac:navigator.platform.includes("Mac")})}static get isCSSRoundSupported(){return B(this,"isCSSRoundSupported",globalThis.CSS?.supports?.("width: round(1.5px, 1px)"))}}e.FeatureTest=et;const it=[...Array(256).keys()].map((t=>t.toString(16).padStart(2,"0")));class nt{static makeHexColor(t,e,i){return`#${it[t]}${it[e]}${it[i]}`}static scaleMinMax(t,e){let i;t[0]?(t[0]<0&&(i=e[0],e[0]=e[1],e[1]=i),e[0]*=t[0],e[1]*=t[0],t[3]<0&&(i=e[2],e[2]=e[3],e[3]=i),e[2]*=t[3],e[3]*=t[3]):(i=e[0],e[0]=e[2],e[2]=i,i=e[1],e[1]=e[3],e[3]=i,t[1]<0&&(i=e[2],e[2]=e[3],e[3]=i),e[2]*=t[1],e[3]*=t[1],t[2]<0&&(i=e[0],e[0]=e[1],e[1]=i),e[0]*=t[2],e[1]*=t[2]),e[0]+=t[4],e[1]+=t[4],e[2]+=t[5],e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){const i=t[0]*e[0]+t[1]*e[2]+e[4],n=t[0]*e[1]+t[1]*e[3]+e[5];return[i,n]}static applyInverseTransform(t,e){const i=e[0]*e[3]-e[1]*e[2],n=(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/i,s=(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/i;return[n,s]}static getAxialAlignedBoundingBox(t,e){const i=this.applyTransform(t,e),n=this.applyTransform(t.slice(2,4),e),s=this.applyTransform([t[0],t[3]],e),r=this.applyTransform([t[2],t[1]],e);return[Math.min(i[0],n[0],s[0],r[0]),Math.min(i[1],n[1],s[1],r[1]),Math.max(i[0],n[0],s[0],r[0]),Math.max(i[1],n[1],s[1],r[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],i=t[0]*e[0]+t[1]*e[2],n=t[0]*e[1]+t[1]*e[3],s=t[2]*e[0]+t[3]*e[2],r=t[2]*e[1]+t[3]*e[3],a=(i+r)/2,o=Math.sqrt((i+r)**2-4*(i*r-s*n))/2,l=a+o||1,c=a-o||1;return[Math.sqrt(l),Math.sqrt(c)]}static normalizeRect(t){const e=t.slice(0);return t[0]>t[2]&&(e[0]=t[2],e[2]=t[0]),t[1]>t[3]&&(e[1]=t[3],e[3]=t[1]),e}static intersect(t,e){const i=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),n=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(i>n)return null;const s=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),r=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return s>r?null:[i,s,n,r]}static bezierBoundingBox(t,e,i,n,s,r,a,o){const l=[],c=[[],[]];let h,d,u,p,g,f,m,b;for(let A=0;A<2;++A)if(0===A?(d=6*t-12*i+6*s,h=-3*t+9*i-9*s+3*a,u=3*i-3*t):(d=6*e-12*n+6*r,h=-3*e+9*n-9*r+3*o,u=3*n-3*e),Math.abs(h)<1e-12){if(Math.abs(d)<1e-12)continue;p=-u/d,0<p&&p<1&&l.push(p)}else m=d*d-4*u*h,b=Math.sqrt(m),m<0||(g=(-d+b)/(2*h),0<g&&g<1&&l.push(g),f=(-d-b)/(2*h),0<f&&f<1&&l.push(f));let v,_=l.length;const y=_;while(_--)p=l[_],v=1-p,c[0][_]=v*v*v*t+3*v*v*p*i+3*v*p*p*s+p*p*p*a,c[1][_]=v*v*v*e+3*v*v*p*n+3*v*p*p*r+p*p*p*o;return c[0][y]=t,c[1][y]=e,c[0][y+1]=a,c[1][y+1]=o,c[0].length=c[1].length=y+2,[Math.min(...c[0]),Math.min(...c[1]),Math.max(...c[0]),Math.max(...c[1])]}}e.Util=nt;const st=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function rt(t){if(t[0]>="ï"){let e;if("þ"===t[0]&&"ÿ"===t[1]?e="utf-16be":"ÿ"===t[0]&&"þ"===t[1]?e="utf-16le":"ï"===t[0]&&"»"===t[1]&&"¿"===t[2]&&(e="utf-8"),e)try{const i=new TextDecoder(e,{fatal:!0}),n=K(t);return i.decode(n)}catch(i){I(`stringToPDFString: "${i}".`)}}const e=[];for(let n=0,s=t.length;n<s;n++){const i=st[t.charCodeAt(n)];e.push(i?String.fromCharCode(i):t.charAt(n))}return e.join("")}function at(t){return decodeURIComponent(escape(t))}function ot(t){return unescape(encodeURIComponent(t))}function lt(t){return"object"===typeof t&&void 0!==t?.byteLength}function ct(t,e){if(t.length!==e.length)return!1;for(let i=0,n=t.length;i<n;i++)if(t[i]!==e[i])return!1;return!0}function ht(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:new Date;const e=[t.getUTCFullYear().toString(),(t.getUTCMonth()+1).toString().padStart(2,"0"),t.getUTCDate().toString().padStart(2,"0"),t.getUTCHours().toString().padStart(2,"0"),t.getUTCMinutes().toString().padStart(2,"0"),t.getUTCSeconds().toString().padStart(2,"0")];return e.join("")}class dt{#t=!1;constructor(){this.promise=new Promise(((t,e)=>{this.resolve=e=>{this.#t=!0,t(e)},this.reject=t=>{this.#t=!0,e(t)}}))}get settled(){return this.#t}}e.PromiseCapability=dt;let ut=null,pt=null;function gt(t){return ut||(ut=/([\u00a0\u00b5\u037e\u0eb3\u2000-\u200a\u202f\u2126\ufb00-\ufb04\ufb06\ufb20-\ufb36\ufb38-\ufb3c\ufb3e\ufb40-\ufb41\ufb43-\ufb44\ufb46-\ufba1\ufba4-\ufba9\ufbae-\ufbb1\ufbd3-\ufbdc\ufbde-\ufbe7\ufbea-\ufbf8\ufbfc-\ufbfd\ufc00-\ufc5d\ufc64-\ufcf1\ufcf5-\ufd3d\ufd88\ufdf4\ufdfa-\ufdfb\ufe71\ufe77\ufe79\ufe7b\ufe7d]+)|(\ufb05+)/gu,pt=new Map([["ﬅ","ſt"]])),t.replaceAll(ut,((t,e,i)=>e?e.normalize("NFKC"):pt.get(i)))}function ft(){if("undefined"!==typeof crypto&&"function"===typeof crypto?.randomUUID)return crypto.randomUUID();const t=new Uint8Array(32);if("undefined"!==typeof crypto&&"function"===typeof crypto?.getRandomValues)crypto.getRandomValues(t);else for(let e=0;e<32;e++)t[e]=Math.floor(255*Math.random());return X(t)}const mt="pdfjs_internal_id_";e.AnnotationPrefix=mt},(t,e,i)=>{var n=i(3),s=i(4),r=i(69),a=i(70),o="WebAssembly",l=s[o],c=7!==Error("e",{cause:7}).cause,h=function(t,e){var i={};i[t]=a(t,e,c),n({global:!0,constructor:!0,arity:1,forced:c},i)},d=function(t,e){if(l&&l[t]){var i={};i[t]=a(o+"."+t,e,c),n({target:o,stat:!0,constructor:!0,arity:1,forced:c},i)}};h("Error",(function(t){return function(e){return r(t,this,arguments)}})),h("EvalError",(function(t){return function(e){return r(t,this,arguments)}})),h("RangeError",(function(t){return function(e){return r(t,this,arguments)}})),h("ReferenceError",(function(t){return function(e){return r(t,this,arguments)}})),h("SyntaxError",(function(t){return function(e){return r(t,this,arguments)}})),h("TypeError",(function(t){return function(e){return r(t,this,arguments)}})),h("URIError",(function(t){return function(e){return r(t,this,arguments)}})),d("CompileError",(function(t){return function(e){return r(t,this,arguments)}})),d("LinkError",(function(t){return function(e){return r(t,this,arguments)}})),d("RuntimeError",(function(t){return function(e){return r(t,this,arguments)}}))},(t,e,i)=>{var n=i(4),s=i(5).f,r=i(44),a=i(48),o=i(38),l=i(56),c=i(68);t.exports=function(t,e){var i,h,d,u,p,g,f=t.target,m=t.global,b=t.stat;if(h=m?n:b?n[f]||o(f,{}):(n[f]||{}).prototype,h)for(d in e){if(p=e[d],t.dontCallGetSet?(g=s(h,d),u=g&&g.value):u=h[d],i=c(m?d:f+(b?".":"#")+d,t.forced),!i&&void 0!==u){if(typeof p==typeof u)continue;l(p,u)}(t.sham||u&&u.sham)&&r(p,"sham",!0),a(h,d,p,t)}}},function(t){var e=function(t){return t&&t.Math===Math&&t};t.exports=e("object"==typeof globalThis&&globalThis)||e("object"==typeof window&&window)||e("object"==typeof self&&self)||e("object"==typeof global&&global)||function(){return this}()||this||Function("return this")()},(t,e,i)=>{var n=i(6),s=i(8),r=i(10),a=i(11),o=i(12),l=i(18),c=i(39),h=i(42),d=Object.getOwnPropertyDescriptor;e.f=n?d:function(t,e){if(t=o(t),e=l(e),h)try{return d(t,e)}catch(i){}if(c(t,e))return a(!s(r.f,t,e),t[e])}},(t,e,i)=>{var n=i(7);t.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},t=>{t.exports=function(t){try{return!!t()}catch(e){return!0}}},(t,e,i)=>{var n=i(9),s=Function.prototype.call;t.exports=n?s.bind(s):function(){return s.apply(s,arguments)}},(t,e,i)=>{var n=i(7);t.exports=!n((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")}))},(t,e)=>{var i={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,s=n&&!i.call({1:2},1);e.f=s?function(t){var e=n(this,t);return!!e&&e.enumerable}:i},t=>{t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},(t,e,i)=>{var n=i(13),s=i(16);t.exports=function(t){return n(s(t))}},(t,e,i)=>{var n=i(14),s=i(7),r=i(15),a=Object,o=n("".split);t.exports=s((function(){return!a("z").propertyIsEnumerable(0)}))?function(t){return"String"===r(t)?o(t,""):a(t)}:a},(t,e,i)=>{var n=i(9),s=Function.prototype,r=s.call,a=n&&s.bind.bind(r,r);t.exports=n?a:function(t){return function(){return r.apply(t,arguments)}}},(t,e,i)=>{var n=i(14),s=n({}.toString),r=n("".slice);t.exports=function(t){return r(s(t),8,-1)}},(t,e,i)=>{var n=i(17),s=TypeError;t.exports=function(t){if(n(t))throw s("Can't call method on "+t);return t}},t=>{t.exports=function(t){return null===t||void 0===t}},(t,e,i)=>{var n=i(19),s=i(23);t.exports=function(t){var e=n(t,"string");return s(e)?e:e+""}},(t,e,i)=>{var n=i(8),s=i(20),r=i(23),a=i(30),o=i(33),l=i(34),c=TypeError,h=l("toPrimitive");t.exports=function(t,e){if(!s(t)||r(t))return t;var i,l=a(t,h);if(l){if(void 0===e&&(e="default"),i=n(l,t,e),!s(i)||r(i))return i;throw c("Can't convert object to primitive value")}return void 0===e&&(e="number"),o(t,e)}},(t,e,i)=>{var n=i(21),s=i(22),r=s.all;t.exports=s.IS_HTMLDDA?function(t){return"object"==typeof t?null!==t:n(t)||t===r}:function(t){return"object"==typeof t?null!==t:n(t)}},(t,e,i)=>{var n=i(22),s=n.all;t.exports=n.IS_HTMLDDA?function(t){return"function"==typeof t||t===s}:function(t){return"function"==typeof t}},t=>{var e="object"==typeof document&&document.all,i="undefined"==typeof e&&void 0!==e;t.exports={all:e,IS_HTMLDDA:i}},(t,e,i)=>{var n=i(24),s=i(21),r=i(25),a=i(26),o=Object;t.exports=a?function(t){return"symbol"==typeof t}:function(t){var e=n("Symbol");return s(e)&&r(e.prototype,o(t))}},(t,e,i)=>{var n=i(4),s=i(21),r=function(t){return s(t)?t:void 0};t.exports=function(t,e){return arguments.length<2?r(n[t]):n[t]&&n[t][e]}},(t,e,i)=>{var n=i(14);t.exports=n({}.isPrototypeOf)},(t,e,i)=>{var n=i(27);t.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},(t,e,i)=>{var n=i(28),s=i(7),r=i(4),a=r.String;t.exports=!!Object.getOwnPropertySymbols&&!s((function(){var t=Symbol("symbol detection");return!a(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},(t,e,i)=>{var n,s,r=i(4),a=i(29),o=r.process,l=r.Deno,c=o&&o.versions||l&&l.version,h=c&&c.v8;h&&(n=h.split("."),s=n[0]>0&&n[0]<4?1:+(n[0]+n[1])),!s&&a&&(n=a.match(/Edge\/(\d+)/),(!n||n[1]>=74)&&(n=a.match(/Chrome\/(\d+)/),n&&(s=+n[1]))),t.exports=s},t=>{t.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},(t,e,i)=>{var n=i(31),s=i(17);t.exports=function(t,e){var i=t[e];return s(i)?void 0:n(i)}},(t,e,i)=>{var n=i(21),s=i(32),r=TypeError;t.exports=function(t){if(n(t))return t;throw r(s(t)+" is not a function")}},t=>{var e=String;t.exports=function(t){try{return e(t)}catch(i){return"Object"}}},(t,e,i)=>{var n=i(8),s=i(21),r=i(20),a=TypeError;t.exports=function(t,e){var i,o;if("string"===e&&s(i=t.toString)&&!r(o=n(i,t)))return o;if(s(i=t.valueOf)&&!r(o=n(i,t)))return o;if("string"!==e&&s(i=t.toString)&&!r(o=n(i,t)))return o;throw a("Can't convert object to primitive value")}},(t,e,i)=>{var n=i(4),s=i(35),r=i(39),a=i(41),o=i(27),l=i(26),c=n.Symbol,h=s("wks"),d=l?c["for"]||c:c&&c.withoutSetter||a;t.exports=function(t){return r(h,t)||(h[t]=o&&r(c,t)?c[t]:d("Symbol."+t)),h[t]}},(t,e,i)=>{var n=i(36),s=i(37);(t.exports=function(t,e){return s[t]||(s[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.32.2",mode:n?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.32.2/LICENSE",source:"https://github.com/zloirock/core-js"})},t=>{t.exports=!1},(t,e,i)=>{var n=i(4),s=i(38),r="__core-js_shared__",a=n[r]||s(r,{});t.exports=a},(t,e,i)=>{var n=i(4),s=Object.defineProperty;t.exports=function(t,e){try{s(n,t,{value:e,configurable:!0,writable:!0})}catch(i){n[t]=e}return e}},(t,e,i)=>{var n=i(14),s=i(40),r=n({}.hasOwnProperty);t.exports=Object.hasOwn||function(t,e){return r(s(t),e)}},(t,e,i)=>{var n=i(16),s=Object;t.exports=function(t){return s(n(t))}},(t,e,i)=>{var n=i(14),s=0,r=Math.random(),a=n(1..toString);t.exports=function(t){return"Symbol("+(void 0===t?"":t)+")_"+a(++s+r,36)}},(t,e,i)=>{var n=i(6),s=i(7),r=i(43);t.exports=!n&&!s((function(){return 7!==Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a}))},(t,e,i)=>{var n=i(4),s=i(20),r=n.document,a=s(r)&&s(r.createElement);t.exports=function(t){return a?r.createElement(t):{}}},(t,e,i)=>{var n=i(6),s=i(45),r=i(11);t.exports=n?function(t,e,i){return s.f(t,e,r(1,i))}:function(t,e,i){return t[e]=i,t}},(t,e,i)=>{var n=i(6),s=i(42),r=i(46),a=i(47),o=i(18),l=TypeError,c=Object.defineProperty,h=Object.getOwnPropertyDescriptor,d="enumerable",u="configurable",p="writable";e.f=n?r?function(t,e,i){if(a(t),e=o(e),a(i),"function"===typeof t&&"prototype"===e&&"value"in i&&p in i&&!i[p]){var n=h(t,e);n&&n[p]&&(t[e]=i.value,i={configurable:u in i?i[u]:n[u],enumerable:d in i?i[d]:n[d],writable:!1})}return c(t,e,i)}:c:function(t,e,i){if(a(t),e=o(e),a(i),s)try{return c(t,e,i)}catch(n){}if("get"in i||"set"in i)throw l("Accessors not supported");return"value"in i&&(t[e]=i.value),t}},(t,e,i)=>{var n=i(6),s=i(7);t.exports=n&&s((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},(t,e,i)=>{var n=i(20),s=String,r=TypeError;t.exports=function(t){if(n(t))return t;throw r(s(t)+" is not an object")}},(t,e,i)=>{var n=i(21),s=i(45),r=i(49),a=i(38);t.exports=function(t,e,i,o){o||(o={});var l=o.enumerable,c=void 0!==o.name?o.name:e;if(n(i)&&r(i,c,o),o.global)l?t[e]=i:a(e,i);else{try{o.unsafe?t[e]&&(l=!0):delete t[e]}catch(h){}l?t[e]=i:s.f(t,e,{value:i,enumerable:!1,configurable:!o.nonConfigurable,writable:!o.nonWritable})}return t}},(t,e,i)=>{var n=i(14),s=i(7),r=i(21),a=i(39),o=i(6),l=i(50).CONFIGURABLE,c=i(51),h=i(52),d=h.enforce,u=h.get,p=String,g=Object.defineProperty,f=n("".slice),m=n("".replace),b=n([].join),v=o&&!s((function(){return 8!==g((function(){}),"length",{value:8}).length})),_=String(String).split("String"),y=t.exports=function(t,e,i){"Symbol("===f(p(e),0,7)&&(e="["+m(p(e),/^Symbol\(([^)]*)\)/,"$1")+"]"),i&&i.getter&&(e="get "+e),i&&i.setter&&(e="set "+e),(!a(t,"name")||l&&t.name!==e)&&(o?g(t,"name",{value:e,configurable:!0}):t.name=e),v&&i&&a(i,"arity")&&t.length!==i.arity&&g(t,"length",{value:i.arity});try{i&&a(i,"constructor")&&i.constructor?o&&g(t,"prototype",{writable:!1}):t.prototype&&(t.prototype=void 0)}catch(s){}var n=d(t);return a(n,"source")||(n.source=b(_,"string"==typeof e?e:"")),t};Function.prototype.toString=y((function(){return r(this)&&u(this).source||c(this)}),"toString")},(t,e,i)=>{var n=i(6),s=i(39),r=Function.prototype,a=n&&Object.getOwnPropertyDescriptor,o=s(r,"name"),l=o&&"something"===function(){}.name,c=o&&(!n||n&&a(r,"name").configurable);t.exports={EXISTS:o,PROPER:l,CONFIGURABLE:c}},(t,e,i)=>{var n=i(14),s=i(21),r=i(37),a=n(Function.toString);s(r.inspectSource)||(r.inspectSource=function(t){return a(t)}),t.exports=r.inspectSource},(t,e,i)=>{var n,s,r,a=i(53),o=i(4),l=i(20),c=i(44),h=i(39),d=i(37),u=i(54),p=i(55),g="Object already initialized",f=o.TypeError,m=o.WeakMap,b=function(t){return r(t)?s(t):n(t,{})},v=function(t){return function(e){var i;if(!l(e)||(i=s(e)).type!==t)throw f("Incompatible receiver, "+t+" required");return i}};if(a||d.state){var _=d.state||(d.state=new m);_.get=_.get,_.has=_.has,_.set=_.set,n=function(t,e){if(_.has(t))throw f(g);return e.facade=t,_.set(t,e),e},s=function(t){return _.get(t)||{}},r=function(t){return _.has(t)}}else{var y=u("state");p[y]=!0,n=function(t,e){if(h(t,y))throw f(g);return e.facade=t,c(t,y,e),e},s=function(t){return h(t,y)?t[y]:{}},r=function(t){return h(t,y)}}t.exports={set:n,get:s,has:r,enforce:b,getterFor:v}},(t,e,i)=>{var n=i(4),s=i(21),r=n.WeakMap;t.exports=s(r)&&/native code/.test(String(r))},(t,e,i)=>{var n=i(35),s=i(41),r=n("keys");t.exports=function(t){return r[t]||(r[t]=s(t))}},t=>{t.exports={}},(t,e,i)=>{var n=i(39),s=i(57),r=i(5),a=i(45);t.exports=function(t,e,i){for(var o=s(e),l=a.f,c=r.f,h=0;h<o.length;h++){var d=o[h];n(t,d)||i&&n(i,d)||l(t,d,c(e,d))}}},(t,e,i)=>{var n=i(24),s=i(14),r=i(58),a=i(67),o=i(47),l=s([].concat);t.exports=n("Reflect","ownKeys")||function(t){var e=r.f(o(t)),i=a.f;return i?l(e,i(t)):e}},(t,e,i)=>{var n=i(59),s=i(66),r=s.concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return n(t,r)}},(t,e,i)=>{var n=i(14),s=i(39),r=i(12),a=i(60).indexOf,o=i(55),l=n([].push);t.exports=function(t,e){var i,n=r(t),c=0,h=[];for(i in n)!s(o,i)&&s(n,i)&&l(h,i);while(e.length>c)s(n,i=e[c++])&&(~a(h,i)||l(h,i));return h}},(t,e,i)=>{var n=i(12),s=i(61),r=i(64),a=function(t){return function(e,i,a){var o,l=n(e),c=r(l),h=s(a,c);if(t&&i!==i){while(c>h)if(o=l[h++],o!==o)return!0}else for(;c>h;h++)if((t||h in l)&&l[h]===i)return t||h||0;return!t&&-1}};t.exports={includes:a(!0),indexOf:a(!1)}},(t,e,i)=>{var n=i(62),s=Math.max,r=Math.min;t.exports=function(t,e){var i=n(t);return i<0?s(i+e,0):r(i,e)}},(t,e,i)=>{var n=i(63);t.exports=function(t){var e=+t;return e!==e||0===e?0:n(e)}},t=>{var e=Math.ceil,i=Math.floor;t.exports=Math.trunc||function(t){var n=+t;return(n>0?i:e)(n)}},(t,e,i)=>{var n=i(65);t.exports=function(t){return n(t.length)}},(t,e,i)=>{var n=i(62),s=Math.min;t.exports=function(t){return t>0?s(n(t),9007199254740991):0}},t=>{t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},(t,e)=>{e.f=Object.getOwnPropertySymbols},(t,e,i)=>{var n=i(7),s=i(21),r=/#|\.prototype\./,a=function(t,e){var i=l[o(t)];return i===h||i!==c&&(s(e)?n(e):!!e)},o=a.normalize=function(t){return String(t).replace(r,".").toLowerCase()},l=a.data={},c=a.NATIVE="N",h=a.POLYFILL="P";t.exports=a},(t,e,i)=>{var n=i(9),s=Function.prototype,r=s.apply,a=s.call;t.exports="object"==typeof Reflect&&Reflect.apply||(n?a.bind(r):function(){return a.apply(r,arguments)})},(t,e,i)=>{var n=i(24),s=i(39),r=i(44),a=i(25),o=i(71),l=i(56),c=i(74),h=i(75),d=i(76),u=i(80),p=i(81),g=i(6),f=i(36);t.exports=function(t,e,i,m){var b="stackTraceLimit",v=m?2:1,_=t.split("."),y=_[_.length-1],A=n.apply(null,_);if(A){var S=A.prototype;if(!f&&s(S,"cause")&&delete S.cause,!i)return A;var x=n("Error"),E=e((function(t,e){var i=d(m?e:t,void 0),n=m?new A(t):new A;return void 0!==i&&r(n,"message",i),p(n,E,n.stack,2),this&&a(S,this)&&h(n,this,E),arguments.length>v&&u(n,arguments[v]),n}));if(E.prototype=S,"Error"!==y?o?o(E,x):l(E,x,{name:!0}):g&&b in A&&(c(E,A,b),c(E,A,"prepareStackTrace")),l(E,A),!f)try{S.name!==y&&r(S,"name",y),S.constructor=E}catch(w){}return E}}},(t,e,i)=>{var n=i(72),s=i(47),r=i(73);t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,i={};try{t=n(Object.prototype,"__proto__","set"),t(i,[]),e=i instanceof Array}catch(a){}return function(i,n){return s(i),r(n),e?t(i,n):i.__proto__=n,i}}():void 0)},(t,e,i)=>{var n=i(14),s=i(31);t.exports=function(t,e,i){try{return n(s(Object.getOwnPropertyDescriptor(t,e)[i]))}catch(r){}}},(t,e,i)=>{var n=i(21),s=String,r=TypeError;t.exports=function(t){if("object"==typeof t||n(t))return t;throw r("Can't set "+s(t)+" as a prototype")}},(t,e,i)=>{var n=i(45).f;t.exports=function(t,e,i){i in t||n(t,i,{configurable:!0,get:function(){return e[i]},set:function(t){e[i]=t}})}},(t,e,i)=>{var n=i(21),s=i(20),r=i(71);t.exports=function(t,e,i){var a,o;return r&&n(a=e.constructor)&&a!==i&&s(o=a.prototype)&&o!==i.prototype&&r(t,o),t}},(t,e,i)=>{var n=i(77);t.exports=function(t,e){return void 0===t?arguments.length<2?"":e:n(t)}},(t,e,i)=>{var n=i(78),s=String;t.exports=function(t){if("Symbol"===n(t))throw TypeError("Cannot convert a Symbol value to a string");return s(t)}},(t,e,i)=>{var n=i(79),s=i(21),r=i(15),a=i(34),o=a("toStringTag"),l=Object,c="Arguments"===r(function(){return arguments}()),h=function(t,e){try{return t[e]}catch(i){}};t.exports=n?r:function(t){var e,i,n;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(i=h(e=l(t),o))?i:c?r(e):"Object"===(n=r(e))&&s(e.callee)?"Arguments":n}},(t,e,i)=>{var n=i(34),s=n("toStringTag"),r={};r[s]="z",t.exports="[object z]"===String(r)},(t,e,i)=>{var n=i(20),s=i(44);t.exports=function(t,e){n(e)&&"cause"in e&&s(t,"cause",e.cause)}},(t,e,i)=>{var n=i(44),s=i(82),r=i(83),a=Error.captureStackTrace;t.exports=function(t,e,i,o){r&&(a?a(t,e):n(t,"stack",s(i,o)))}},(t,e,i)=>{var n=i(14),s=Error,r=n("".replace),a=function(t){return String(s(t).stack)}("zxcasd"),o=/\n\s*at [^:]*:[^\n]*/,l=o.test(a);t.exports=function(t,e){if(l&&"string"==typeof t&&!s.prepareStackTrace)while(e--)t=r(t,o,"");return t}},(t,e,i)=>{var n=i(7),s=i(11);t.exports=!n((function(){var t=Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",s(1,7)),7!==t.stack)}))},(t,e,i)=>{var n=i(48),s=i(14),r=i(77),a=i(85),o=URLSearchParams,l=o.prototype,c=s(l.append),h=s(l["delete"]),d=s(l.forEach),u=s([].push),p=new o("a=1&a=2&b=3");p["delete"]("a",1),p["delete"]("b",void 0),p+""!=="a=2"&&n(l,"delete",(function(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return h(this,t);var n=[];d(this,(function(t,e){u(n,{key:e,value:t})})),a(e,1);var s,o=r(t),l=r(i),p=0,g=0,f=!1,m=n.length;while(p<m)s=n[p++],f||s.key===o?(f=!0,h(this,s.key)):g++;while(g<m)s=n[g++],s.key===o&&s.value===l||c(this,s.key,s.value)}),{enumerable:!0,unsafe:!0})},t=>{var e=TypeError;t.exports=function(t,i){if(t<i)throw e("Not enough arguments");return t}},(t,e,i)=>{var n=i(48),s=i(14),r=i(77),a=i(85),o=URLSearchParams,l=o.prototype,c=s(l.getAll),h=s(l.has),d=new o("a=1");!d.has("a",2)&&d.has("a",void 0)||n(l,"has",(function(t){var e=arguments.length,i=e<2?void 0:arguments[1];if(e&&void 0===i)return h(this,t);var n=c(this,t);a(e,1);var s=r(i),o=0;while(o<n.length)if(n[o++]===s)return!0;return!1}),{enumerable:!0,unsafe:!0})},(t,e,i)=>{var n=i(6),s=i(14),r=i(88),a=URLSearchParams.prototype,o=s(a.forEach);n&&!("size"in a)&&r(a,"size",{get:function(){var t=0;return o(this,(function(){t++})),t},configurable:!0,enumerable:!0})},(t,e,i)=>{var n=i(49),s=i(45);t.exports=function(t,e,i){return i.get&&n(i.get,e,{getter:!0}),i.set&&n(i.set,e,{setter:!0}),s.f(t,e,i)}},(t,e,i)=>{var n=i(3),s=i(40),r=i(64),a=i(90),o=i(92),l=i(7),c=l((function(){return 4294967297!==[].push.call({length:4294967296},1)})),h=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(t){return t instanceof TypeError}},d=c||!h();n({target:"Array",proto:!0,arity:1,forced:d},{push:function(t){var e=s(this),i=r(e),n=arguments.length;o(i+n);for(var l=0;l<n;l++)e[i]=arguments[l],i++;return a(e,i),i}})},(t,e,i)=>{var n=i(6),s=i(91),r=TypeError,a=Object.getOwnPropertyDescriptor,o=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}();t.exports=o?function(t,e){if(s(t)&&!a(t,"length").writable)throw r("Cannot set read only .length");return t.length=e}:function(t,e){return t.length=e}},(t,e,i)=>{var n=i(15);t.exports=Array.isArray||function(t){return"Array"===n(t)}},t=>{var e=TypeError,i=9007199254740991;t.exports=function(t){if(t>i)throw e("Maximum allowed index exceeded");return t}},(t,e,i)=>{var n=i(94),s=i(98).findLast,r=n.aTypedArray,a=n.exportTypedArrayMethod;a("findLast",(function(t){return s(r(this),t,arguments.length>1?arguments[1]:void 0)}))},(t,e,i)=>{var n,s,r,a=i(95),o=i(6),l=i(4),c=i(21),h=i(20),d=i(39),u=i(78),p=i(32),g=i(44),f=i(48),m=i(88),b=i(25),v=i(96),_=i(71),y=i(34),A=i(41),S=i(52),x=S.enforce,E=S.get,w=l.Int8Array,C=w&&w.prototype,T=l.Uint8ClampedArray,P=T&&T.prototype,k=w&&v(w),M=C&&v(C),R=Object.prototype,F=l.TypeError,D=y("toStringTag"),I=A("TYPED_ARRAY_TAG"),O="TypedArrayConstructor",L=a&&!!_&&"Opera"!==u(l.opera),N=!1,j={Int8Array:1,Uint8Array:1,Uint8ClampedArray:1,Int16Array:2,Uint16Array:2,Int32Array:4,Uint32Array:4,Float32Array:4,Float64Array:8},B={BigInt64Array:8,BigUint64Array:8},U=function(t){if(!h(t))return!1;var e=u(t);return"DataView"===e||d(j,e)||d(B,e)},z=function(t){var e=v(t);if(h(e)){var i=E(e);return i&&d(i,O)?i[O]:z(e)}},H=function(t){if(!h(t))return!1;var e=u(t);return d(j,e)||d(B,e)},W=function(t){if(H(t))return t;throw F("Target is not a typed array")},q=function(t){if(c(t)&&(!_||b(k,t)))return t;throw F(p(t)+" is not a typed array constructor")},G=function(t,e,i,n){if(o){if(i)for(var s in j){var r=l[s];if(r&&d(r.prototype,t))try{delete r.prototype[t]}catch(a){try{r.prototype[t]=e}catch(c){}}}M[t]&&!i||f(M,t,i?e:L&&C[t]||e,n)}},V=function(t,e,i){var n,s;if(o){if(_){if(i)for(n in j)if(s=l[n],s&&d(s,t))try{delete s[t]}catch(r){}if(k[t]&&!i)return;try{return f(k,t,i?e:L&&k[t]||e)}catch(r){}}for(n in j)s=l[n],!s||s[t]&&!i||f(s,t,e)}};for(n in j)s=l[n],r=s&&s.prototype,r?x(r)[O]=s:L=!1;for(n in B)s=l[n],r=s&&s.prototype,r&&(x(r)[O]=s);if((!L||!c(k)||k===Function.prototype)&&(k=function(){throw F("Incorrect invocation")},L))for(n in j)l[n]&&_(l[n],k);if((!L||!M||M===R)&&(M=k.prototype,L))for(n in j)l[n]&&_(l[n].prototype,M);if(L&&v(P)!==M&&_(P,M),o&&!d(M,D))for(n in N=!0,m(M,D,{configurable:!0,get:function(){return h(this)?this[I]:void 0}}),j)l[n]&&g(l[n],I,n);t.exports={NATIVE_ARRAY_BUFFER_VIEWS:L,TYPED_ARRAY_TAG:N&&I,aTypedArray:W,aTypedArrayConstructor:q,exportTypedArrayMethod:G,exportTypedArrayStaticMethod:V,getTypedArrayConstructor:z,isView:U,isTypedArray:H,TypedArray:k,TypedArrayPrototype:M}},t=>{t.exports="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof DataView},(t,e,i)=>{var n=i(39),s=i(21),r=i(40),a=i(54),o=i(97),l=a("IE_PROTO"),c=Object,h=c.prototype;t.exports=o?c.getPrototypeOf:function(t){var e=r(t);if(n(e,l))return e[l];var i=e.constructor;return s(i)&&e instanceof i?i.prototype:e instanceof c?h:null}},(t,e,i)=>{var n=i(7);t.exports=!n((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},(t,e,i)=>{var n=i(99),s=i(13),r=i(40),a=i(64),o=function(t){var e=1===t;return function(i,o,l){var c,h,d=r(i),u=s(d),p=n(o,l),g=a(u);while(g-- >0)if(c=u[g],h=p(c,g,d),h)switch(t){case 0:return c;case 1:return g}return e?-1:void 0}};t.exports={findLast:o(0),findLastIndex:o(1)}},(t,e,i)=>{var n=i(100),s=i(31),r=i(9),a=n(n.bind);t.exports=function(t,e){return s(t),void 0===e?t:r?a(t,e):function(){return t.apply(e,arguments)}}},(t,e,i)=>{var n=i(15),s=i(14);t.exports=function(t){if("Function"===n(t))return s(t)}},(t,e,i)=>{var n=i(94),s=i(98).findLastIndex,r=n.aTypedArray,a=n.exportTypedArrayMethod;a("findLastIndex",(function(t){return s(r(this),t,arguments.length>1?arguments[1]:void 0)}))},(t,e,i)=>{var n=i(4),s=i(8),r=i(94),a=i(64),o=i(103),l=i(40),c=i(7),h=n.RangeError,d=n.Int8Array,u=d&&d.prototype,p=u&&u.set,g=r.aTypedArray,f=r.exportTypedArrayMethod,m=!c((function(){var t=new Uint8ClampedArray(2);return s(p,t,{length:1,0:3},1),3!==t[1]})),b=m&&r.NATIVE_ARRAY_BUFFER_VIEWS&&c((function(){var t=new d(2);return t.set(1),t.set("2",1),0!==t[0]||2!==t[1]}));f("set",(function(t){g(this);var e=o(arguments.length>1?arguments[1]:void 0,1),i=l(t);if(m)return s(p,this,i,e);var n=this.length,r=a(i),c=0;if(r+e>n)throw h("Wrong length");while(c<r)this[e+c]=i[c++]}),!m||b)},(t,e,i)=>{var n=i(104),s=RangeError;t.exports=function(t,e){var i=n(t);if(i%e)throw s("Wrong offset");return i}},(t,e,i)=>{var n=i(62),s=RangeError;t.exports=function(t){var e=n(t);if(e<0)throw s("The argument can't be less than 0");return e}},(t,e,i)=>{var n=i(106),s=i(94),r=s.aTypedArray,a=s.exportTypedArrayMethod,o=s.getTypedArrayConstructor;a("toReversed",(function(){return n(r(this),o(this))}))},(t,e,i)=>{var n=i(64);t.exports=function(t,e){for(var i=n(t),s=new e(i),r=0;r<i;r++)s[r]=t[i-r-1];return s}},(t,e,i)=>{var n=i(94),s=i(14),r=i(31),a=i(108),o=n.aTypedArray,l=n.getTypedArrayConstructor,c=n.exportTypedArrayMethod,h=s(n.TypedArrayPrototype.sort);c("toSorted",(function(t){void 0!==t&&r(t);var e=o(this),i=a(l(e),e);return h(i,t)}))},(t,e,i)=>{var n=i(64);t.exports=function(t,e){var i=0,s=n(e),r=new t(s);while(s>i)r[i]=e[i++];return r}},(t,e,i)=>{var n=i(110),s=i(94),r=i(111),a=i(62),o=i(112),l=s.aTypedArray,c=s.getTypedArrayConstructor,h=s.exportTypedArrayMethod,d=!!function(){try{new Int8Array(1)["with"](2,{valueOf:function(){throw 8}})}catch(t){return 8===t}}();h("with",{with:function(t,e){var i=l(this),s=a(t),h=r(i)?o(e):+e;return n(i,c(i),s,h)}}["with"],!d)},(t,e,i)=>{var n=i(64),s=i(62),r=RangeError;t.exports=function(t,e,i,a){var o=n(t),l=s(i),c=l<0?o+l:l;if(c>=o||c<0)throw r("Incorrect index");for(var h=new e(o),d=0;d<o;d++)h[d]=d===c?a:t[d];return h}},(t,e,i)=>{var n=i(78);t.exports=function(t){var e=n(t);return"BigInt64Array"===e||"BigUint64Array"===e}},(t,e,i)=>{var n=i(19),s=TypeError;t.exports=function(t){var e=n(t,"number");if("number"==typeof e)throw s("Can't convert number to bigint");return BigInt(e)}},(t,e,i)=>{var n=i(6),s=i(88),r=i(114),a=ArrayBuffer.prototype;n&&!("detached"in a)&&s(a,"detached",{configurable:!0,get:function(){return r(this)}})},(t,e,i)=>{var n=i(14),s=i(115),r=n(ArrayBuffer.prototype.slice);t.exports=function(t){if(0!==s(t))return!1;try{return r(t,0,0),!1}catch(e){return!0}}},(t,e,i)=>{var n=i(72),s=i(15),r=TypeError;t.exports=n(ArrayBuffer.prototype,"byteLength","get")||function(t){if("ArrayBuffer"!==s(t))throw r("ArrayBuffer expected");return t.byteLength}},(t,e,i)=>{var n=i(3),s=i(117);s&&n({target:"ArrayBuffer",proto:!0},{transfer:function(){return s(this,arguments.length?arguments[0]:void 0,!0)}})},(t,e,i)=>{var n=i(4),s=i(14),r=i(72),a=i(118),o=i(114),l=i(115),c=i(119),h=n.TypeError,d=n.structuredClone,u=n.ArrayBuffer,p=n.DataView,g=Math.min,f=u.prototype,m=p.prototype,b=s(f.slice),v=r(f,"resizable","get"),_=r(f,"maxByteLength","get"),y=s(m.getInt8),A=s(m.setInt8);t.exports=c&&function(t,e,i){var n=l(t),s=void 0===e?n:a(e),r=!v||!v(t);if(o(t))throw h("ArrayBuffer is detached");var c=d(t,{transfer:[t]});if(n===s&&(i||r))return c;if(n>=s&&(!i||r))return b(c,0,s);for(var f=i&&!r&&_?{maxByteLength:_(c)}:void 0,m=new u(s,f),S=new p(c),x=new p(m),E=g(s,n),w=0;w<E;w++)A(x,w,y(S,w));return m}},(t,e,i)=>{var n=i(62),s=i(65),r=RangeError;t.exports=function(t){if(void 0===t)return 0;var e=n(t),i=s(e);if(e!==i)throw r("Wrong length or index");return i}},(t,e,i)=>{var n=i(4),s=i(7),r=i(28),a=i(120),o=i(121),l=i(122),c=n.structuredClone;t.exports=!!c&&!s((function(){if(o&&r>92||l&&r>94||a&&r>97)return!1;var t=new ArrayBuffer(8),e=c(t,{transfer:[t]});return 0!==t.byteLength||8!==e.byteLength}))},(t,e,i)=>{var n=i(121),s=i(122);t.exports=!n&&!s&&"object"==typeof window&&"object"==typeof document},t=>{t.exports="object"==typeof Deno&&Deno&&"object"==typeof Deno.version},(t,e,i)=>{var n=i(4),s=i(15);t.exports="process"===s(n.process)},(t,e,i)=>{var n=i(3),s=i(117);s&&n({target:"ArrayBuffer",proto:!0},{transferToFixedLength:function(){return s(this,arguments.length?arguments[0]:void 0,!1)}})},(__unused_webpack_module,exports,__w_pdfjs_require__)=>{Object.defineProperty(exports,"__esModule",{value:!0}),exports.RenderTask=exports.PDFWorkerUtil=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDocumentLoadingTask=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultFilterFactory=exports.DefaultCanvasFactory=exports.DefaultCMapReaderFactory=void 0,Object.defineProperty(exports,"SVGGraphics",{enumerable:!0,get:function(){return _displaySvg.SVGGraphics}}),exports.build=void 0,exports.getDocument=getDocument,exports.version=void 0,__w_pdfjs_require__(84),__w_pdfjs_require__(86),__w_pdfjs_require__(87),__w_pdfjs_require__(2),__w_pdfjs_require__(93),__w_pdfjs_require__(101),__w_pdfjs_require__(102),__w_pdfjs_require__(105),__w_pdfjs_require__(107),__w_pdfjs_require__(109),__w_pdfjs_require__(113),__w_pdfjs_require__(116),__w_pdfjs_require__(123),__w_pdfjs_require__(89),__w_pdfjs_require__(125),__w_pdfjs_require__(136),__w_pdfjs_require__(138),__w_pdfjs_require__(141),__w_pdfjs_require__(143),__w_pdfjs_require__(145),__w_pdfjs_require__(147),__w_pdfjs_require__(149),__w_pdfjs_require__(152);var _util=__w_pdfjs_require__(1),_annotation_storage=__w_pdfjs_require__(163),_display_utils=__w_pdfjs_require__(168),_font_loader=__w_pdfjs_require__(171),_displayNode_utils=__w_pdfjs_require__(172),_canvas=__w_pdfjs_require__(173),_worker_options=__w_pdfjs_require__(176),_message_handler=__w_pdfjs_require__(177),_metadata=__w_pdfjs_require__(178),_optional_content_config=__w_pdfjs_require__(179),_transport_stream=__w_pdfjs_require__(180),_displayFetch_stream=__w_pdfjs_require__(181),_displayNetwork=__w_pdfjs_require__(184),_displayNode_stream=__w_pdfjs_require__(185),_displaySvg=__w_pdfjs_require__(186),_xfa_text=__w_pdfjs_require__(194);const DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100,DELAYED_CLEANUP_TIMEOUT=5e3,DefaultCanvasFactory=_util.isNodeJS?_displayNode_utils.NodeCanvasFactory:_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;const DefaultCMapReaderFactory=_util.isNodeJS?_displayNode_utils.NodeCMapReaderFactory:_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;const DefaultFilterFactory=_util.isNodeJS?_displayNode_utils.NodeFilterFactory:_display_utils.DOMFilterFactory;exports.DefaultFilterFactory=DefaultFilterFactory;const DefaultStandardFontDataFactory=_util.isNodeJS?_displayNode_utils.NodeStandardFontDataFactory:_display_utils.DOMStandardFontDataFactory;function getDocument(t){if("string"===typeof t||t instanceof URL?t={url:t}:(0,_util.isArrayBuffer)(t)&&(t={data:t}),"object"!==typeof t)throw new Error("Invalid parameter in getDocument, need parameter object.");if(!t.url&&!t.data&&!t.range)throw new Error("Invalid parameter object: need either .data, .range or .url");const e=new PDFDocumentLoadingTask,{docId:i}=e,n=t.url?getUrlProp(t.url):null,s=t.data?getDataProp(t.data):null,r=t.httpHeaders||null,a=!0===t.withCredentials,o=t.password??null,l=t.range instanceof PDFDataRangeTransport?t.range:null,c=Number.isInteger(t.rangeChunkSize)&&t.rangeChunkSize>0?t.rangeChunkSize:DEFAULT_RANGE_CHUNK_SIZE;let h=t.worker instanceof PDFWorker?t.worker:null;const d=t.verbosity,u="string"!==typeof t.docBaseUrl||(0,_display_utils.isDataScheme)(t.docBaseUrl)?null:t.docBaseUrl,p="string"===typeof t.cMapUrl?t.cMapUrl:null,g=!1!==t.cMapPacked,f=t.CMapReaderFactory||DefaultCMapReaderFactory,m="string"===typeof t.standardFontDataUrl?t.standardFontDataUrl:null,b=t.StandardFontDataFactory||DefaultStandardFontDataFactory,v=!0!==t.stopAtErrors,_=Number.isInteger(t.maxImageSize)&&t.maxImageSize>-1?t.maxImageSize:-1,y=!1!==t.isEvalSupported,A="boolean"===typeof t.isOffscreenCanvasSupported?t.isOffscreenCanvasSupported:!_util.isNodeJS,S=Number.isInteger(t.canvasMaxAreaInBytes)?t.canvasMaxAreaInBytes:-1,x="boolean"===typeof t.disableFontFace?t.disableFontFace:_util.isNodeJS,E=!0===t.fontExtraProperties,w=!0===t.enableXfa,C=t.ownerDocument||globalThis.document,T=!0===t.disableRange,P=!0===t.disableStream,k=!0===t.disableAutoFetch,M=!0===t.pdfBug,R=l?l.length:t.length??NaN,F="boolean"===typeof t.useSystemFonts?t.useSystemFonts:!_util.isNodeJS&&!x,D="boolean"===typeof t.useWorkerFetch?t.useWorkerFetch:f===_display_utils.DOMCMapReaderFactory&&b===_display_utils.DOMStandardFontDataFactory&&p&&m&&(0,_display_utils.isValidFetchUrl)(p,document.baseURI)&&(0,_display_utils.isValidFetchUrl)(m,document.baseURI),I=t.canvasFactory||new DefaultCanvasFactory({ownerDocument:C}),O=t.filterFactory||new DefaultFilterFactory({docId:i,ownerDocument:C}),L=null;(0,_util.setVerbosityLevel)(d);const N={canvasFactory:I,filterFactory:O};if(D||(N.cMapReaderFactory=new f({baseUrl:p,isCompressed:g}),N.standardFontDataFactory=new b({baseUrl:m})),!h){const t={verbosity:d,port:_worker_options.GlobalWorkerOptions.workerPort};h=t.port?PDFWorker.fromPort(t):new PDFWorker(t),e._worker=h}const j={docId:i,apiVersion:"3.11.174",data:s,password:o,disableAutoFetch:k,rangeChunkSize:c,length:R,docBaseUrl:u,enableXfa:w,evaluatorOptions:{maxImageSize:_,disableFontFace:x,ignoreErrors:v,isEvalSupported:y,isOffscreenCanvasSupported:A,canvasMaxAreaInBytes:S,fontExtraProperties:E,useSystemFonts:F,cMapUrl:D?p:null,standardFontDataUrl:D?m:null}},B={ignoreErrors:v,isEvalSupported:y,disableFontFace:x,fontExtraProperties:E,enableXfa:w,ownerDocument:C,disableAutoFetch:k,pdfBug:M,styleElement:L};return h.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");const t=_fetchDocument(h,j),o=new Promise((function(t){let e;if(l)e=new _transport_stream.PDFDataTransportStream({length:R,initialData:l.initialData,progressiveDone:l.progressiveDone,contentDispositionFilename:l.contentDispositionFilename,disableRange:T,disableStream:P},l);else if(!s){const t=t=>_util.isNodeJS?new _displayNode_stream.PDFNodeStream(t):(0,_display_utils.isValidFetchUrl)(t.url)?new _displayFetch_stream.PDFFetchStream(t):new _displayNetwork.PDFNetworkStream(t);e=t({url:n,length:R,httpHeaders:r,withCredentials:a,rangeChunkSize:c,disableRange:T,disableStream:P})}t(e)}));return Promise.all([t,o]).then((function(t){let[n,s]=t;if(e.destroyed)throw new Error("Loading aborted");const r=new _message_handler.MessageHandler(i,n,h.port),a=new WorkerTransport(r,e,s,B,N);e._transport=a,r.send("Ready",null)}))})).catch(e._capability.reject),e}async function _fetchDocument(t,e){if(t.destroyed)throw new Error("Worker was destroyed");const i=await t.messageHandler.sendWithPromise("GetDocRequest",e,e.data?[e.data.buffer]:null);if(t.destroyed)throw new Error("Worker was destroyed");return i}function getUrlProp(t){if(t instanceof URL)return t.href;try{return new URL(t,window.location).href}catch{if(_util.isNodeJS&&"string"===typeof t)return t}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.")}function getDataProp(t){if(_util.isNodeJS&&"undefined"!==typeof Buffer&&t instanceof Buffer)throw new Error("Please provide binary data as `Uint8Array`, rather than `Buffer`.");if(t instanceof Uint8Array&&t.byteLength===t.buffer.byteLength)return t;if("string"===typeof t)return(0,_util.stringToBytes)(t);if("object"===typeof t&&!isNaN(t?.length)||(0,_util.isArrayBuffer)(t))return new Uint8Array(t);throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.")}exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory;class PDFDocumentLoadingTask{static#e=0;constructor(){this._capability=new _util.PromiseCapability,this._transport=null,this._worker=null,this.docId="d"+PDFDocumentLoadingTask.#e++,this.destroyed=!1,this.onPassword=null,this.onProgress=null}get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;try{this._worker?.port&&(this._worker._pendingDestroy=!0),await(this._transport?.destroy())}catch(t){throw this._worker?.port&&delete this._worker._pendingDestroy,t}this._transport=null,this._worker&&(this._worker.destroy(),this._worker=null)}}exports.PDFDocumentLoadingTask=PDFDocumentLoadingTask;class PDFDataRangeTransport{constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;this.length=t,this.initialData=e,this.progressiveDone=i,this.contentDispositionFilename=n,this._rangeListeners=[],this._progressListeners=[],this._progressiveReadListeners=[],this._progressiveDoneListeners=[],this._readyCapability=new _util.PromiseCapability}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const i of this._rangeListeners)i(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const i of this._progressListeners)i(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}exports.PDFDataRangeTransport=PDFDataRangeTransport;class PDFDocumentProxy{constructor(t,e){this._pdfInfo=t,this._transport=e,Object.defineProperty(this,"getJavaScript",{value:()=>((0,_display_utils.deprecated)("`PDFDocumentProxy.getJavaScript`, please use `PDFDocumentProxy.getJSActions` instead."),this.getJSActions().then((t=>{if(!t)return t;const e=[];for(const i in t)e.push(...t[i]);return e})))})}get annotationStorage(){return this._transport.annotationStorage}get filterFactory(){return this._transport.filterFactory}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig(){return this._transport.getOptionalContentConfig()}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}saveDocument(){return this._transport.saveDocument()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}exports.PDFDocumentProxy=PDFDocumentProxy;class PDFPageProxy{#i=null;#n=!1;constructor(t,e,i){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];this._pageIndex=t,this._pageInfo=e,this._transport=i,this._stats=n?new _display_utils.StatTimer:null,this._pdfBug=n,this.commonObjs=i.commonObjs,this.objs=new PDFObjects,this._maybeCleanupAfterRender=!1,this._intentStates=new Map,this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport(){let{scale:t,rotation:e=this.rotate,offsetX:i=0,offsetY:n=0,dontFlip:s=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new _display_utils.PageViewport({viewBox:this.view,scale:t,rotation:e,offsetX:i,offsetY:n,dontFlip:s})}getAnnotations(){let{intent:t="display"}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const e=this._transport.getRenderingIntent(t);return this._transport.getAnnotations(this._pageIndex,e.renderingIntent)}getJSActions(){return this._transport.getPageJSActions(this._pageIndex)}get filterFactory(){return this._transport.filterFactory}get isPureXfa(){return(0,_util.shadow)(this,"isPureXfa",!!this._transport._htmlForXfa)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render(t){let{canvasContext:e,viewport:i,intent:n="display",annotationMode:s=_util.AnnotationMode.ENABLE,transform:r=null,background:a=null,optionalContentConfigPromise:o=null,annotationCanvasMap:l=null,pageColors:c=null,printAnnotationStorage:h=null}=t;this._stats?.time("Overall");const d=this._transport.getRenderingIntent(n,s,h);this.#n=!1,this.#s(),o||(o=this._transport.getOptionalContentConfig());let u=this._intentStates.get(d.cacheKey);u||(u=Object.create(null),this._intentStates.set(d.cacheKey,u)),u.streamReaderCancelTimeout&&(clearTimeout(u.streamReaderCancelTimeout),u.streamReaderCancelTimeout=null);const p=!!(d.renderingIntent&_util.RenderingIntentFlag.PRINT);u.displayReadyCapability||(u.displayReadyCapability=new _util.PromiseCapability,u.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(d));const g=t=>{u.renderTasks.delete(f),(this._maybeCleanupAfterRender||p)&&(this.#n=!0),this.#r(!p),t?(f.capability.reject(t),this._abortOperatorList({intentState:u,reason:t instanceof Error?t:new Error(t)})):f.capability.resolve(),this._stats?.timeEnd("Rendering"),this._stats?.timeEnd("Overall")},f=new InternalRenderTask({callback:g,params:{canvasContext:e,viewport:i,transform:r,background:a},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:l,operatorList:u.operatorList,pageIndex:this._pageIndex,canvasFactory:this._transport.canvasFactory,filterFactory:this._transport.filterFactory,useRequestAnimationFrame:!p,pdfBug:this._pdfBug,pageColors:c});(u.renderTasks||=new Set).add(f);const m=f.task;return Promise.all([u.displayReadyCapability.promise,o]).then((t=>{let[e,i]=t;this.destroyed?g():(this._stats?.time("Rendering"),f.initializeGraphics({transparency:e,optionalContentConfig:i}),f.operatorListChanged())})).catch(g),m}getOperatorList(){let{intent:t="display",annotationMode:e=_util.AnnotationMode.ENABLE,printAnnotationStorage:i=null}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};function n(){a.operatorList.lastChunk&&(a.opListReadCapability.resolve(a.operatorList),a.renderTasks.delete(r))}const s=this._transport.getRenderingIntent(t,e,i,!0);let r,a=this._intentStates.get(s.cacheKey);return a||(a=Object.create(null),this._intentStates.set(s.cacheKey,a)),a.opListReadCapability||(r=Object.create(null),r.operatorListChanged=n,a.opListReadCapability=new _util.PromiseCapability,(a.renderTasks||=new Set).add(r),a.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null},this._stats?.time("Page Request"),this._pumpOperatorList(s)),a.opListReadCapability.promise}streamTextContent(){let{includeMarkedContent:t=!1,disableNormalization:e=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const i=100;return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,includeMarkedContent:!0===t,disableNormalization:!0===e},{highWaterMark:i,size(t){return t.items.length}})}getTextContent(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this._transport._htmlForXfa)return this.getXfa().then((t=>_xfa_text.XfaText.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,i){function n(){s.read().then((function(e){let{value:i,done:s}=e;s?t(r):(Object.assign(r.styles,i.styles),r.items.push(...i.items),n())}),i)}const s=e.getReader(),r={items:[],styles:Object.create(null)};n()}))}getStructTree(){return this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values())if(this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0}),!e.opListReadCapability)for(const i of e.renderTasks)t.push(i.completed),i.cancel();return this.objs.clear(),this.#n=!1,this.#s(),Promise.all(t)}cleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.#n=!0;const e=this.#r(!1);return t&&e&&(this._stats&&=new _display_utils.StatTimer),e}#r(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.#s(),!this.#n||this.destroyed)return!1;if(t)return this.#i=setTimeout((()=>{this.#i=null,this.#r(!1)}),DELAYED_CLEANUP_TIMEOUT),!1;for(const{renderTasks:e,operatorList:i}of this._intentStates.values())if(e.size>0||!i.lastChunk)return!1;return this._intentStates.clear(),this.objs.clear(),this.#n=!1,!0}#s(){this.#i&&(clearTimeout(this.#i),this.#i=null)}_startRenderPage(t,e){const i=this._intentStates.get(e);i&&(this._stats?.timeEnd("Page Request"),i.displayReadyCapability?.resolve(t))}_renderPageChunk(t,e){for(let i=0,n=t.length;i<n;i++)e.operatorList.fnArray.push(t.fnArray[i]),e.operatorList.argsArray.push(t.argsArray[i]);e.operatorList.lastChunk=t.lastChunk,e.operatorList.separateAnnots=t.separateAnnots;for(const i of e.renderTasks)i.operatorListChanged();t.lastChunk&&this.#r(!0)}_pumpOperatorList(t){let{renderingIntent:e,cacheKey:i,annotationStorageSerializable:n}=t;const{map:s,transfers:r}=n,a=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:e,cacheKey:i,annotationStorage:s},r),o=a.getReader(),l=this._intentStates.get(i);l.streamReader=o;const c=()=>{o.read().then((t=>{let{value:e,done:i}=t;i?l.streamReader=null:this._transport.destroyed||(this._renderPageChunk(e,l),c())}),(t=>{if(l.streamReader=null,!this._transport.destroyed){if(l.operatorList){l.operatorList.lastChunk=!0;for(const t of l.renderTasks)t.operatorListChanged();this.#r(!0)}if(l.displayReadyCapability)l.displayReadyCapability.reject(t);else{if(!l.opListReadCapability)throw t;l.opListReadCapability.reject(t)}}}))};c()}_abortOperatorList(t){let{intentState:e,reason:i,force:n=!1}=t;if(e.streamReader){if(e.streamReaderCancelTimeout&&(clearTimeout(e.streamReaderCancelTimeout),e.streamReaderCancelTimeout=null),!n){if(e.renderTasks.size>0)return;if(i instanceof _display_utils.RenderingCancelledException){let t=RENDERING_CANCELLED_TIMEOUT;return i.extraDelay>0&&i.extraDelay<1e3&&(t+=i.extraDelay),void(e.streamReaderCancelTimeout=setTimeout((()=>{e.streamReaderCancelTimeout=null,this._abortOperatorList({intentState:e,reason:i,force:!0})}),t))}}if(e.streamReader.cancel(new _util.AbortException(i.message)).catch((()=>{})),e.streamReader=null,!this._transport.destroyed){for(const[t,i]of this._intentStates)if(i===e){this._intentStates.delete(t);break}this.cleanup()}}}get stats(){return this._stats}}exports.PDFPageProxy=PDFPageProxy;class LoopbackPort{#a=new Set;#o=Promise.resolve();postMessage(t,e){const i={data:structuredClone(t,null)};this.#o.then((()=>{for(const t of this.#a)t.call(this,i)}))}addEventListener(t,e){this.#a.add(e)}removeEventListener(t,e){this.#a.delete(e)}terminate(){this.#a.clear()}}exports.LoopbackPort=LoopbackPort;const PDFWorkerUtil={isWorkerDisabled:!1,fallbackWorkerSrc:null,fakeWorkerId:0};if(exports.PDFWorkerUtil=PDFWorkerUtil,_util.isNodeJS&&"function"===typeof require)PDFWorkerUtil.isWorkerDisabled=!0,PDFWorkerUtil.fallbackWorkerSrc="./pdf.worker.js";else if("object"===typeof document){const t=document?.currentScript?.src;t&&(PDFWorkerUtil.fallbackWorkerSrc=t.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}PDFWorkerUtil.isSameOrigin=function(t,e){let i;try{if(i=new URL(t),!i.origin||"null"===i.origin)return!1}catch{return!1}const n=new URL(e,i);return i.origin===n.origin},PDFWorkerUtil.createCDNWrapper=function(t){const e=`importScripts("${t}");`;return URL.createObjectURL(new Blob([e]))};class PDFWorker{static#l;constructor(){let{name:t=null,port:e=null,verbosity:i=(0,_util.getVerbosityLevel)()}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this.name=t,this.destroyed=!1,this.verbosity=i,this._readyCapability=new _util.PromiseCapability,this._port=null,this._webWorker=null,this._messageHandler=null,e){if(PDFWorker.#l?.has(e))throw new Error("Cannot use more than one PDFWorker per port.");return(PDFWorker.#l||=new WeakMap).set(e,this),void this._initializeFromPort(e)}this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t,this._messageHandler=new _message_handler.MessageHandler("main","worker",t),this._messageHandler.on("ready",(function(){})),this._readyCapability.resolve(),this._messageHandler.send("configure",{verbosity:this.verbosity})}_initialize(){if(!PDFWorkerUtil.isWorkerDisabled&&!PDFWorker._mainThreadWorkerMessageHandler){let{workerSrc:t}=PDFWorker;try{PDFWorkerUtil.isSameOrigin(window.location.href,t)||(t=PDFWorkerUtil.createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t),i=new _message_handler.MessageHandler("main","worker",e),n=()=>{e.removeEventListener("error",s),i.destroy(),e.terminate(),this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},s=()=>{this._webWorker||n()};e.addEventListener("error",s),i.on("test",(t=>{e.removeEventListener("error",s),this.destroyed?n():t?(this._messageHandler=i,this._port=e,this._webWorker=e,this._readyCapability.resolve(),i.send("configure",{verbosity:this.verbosity})):(this._setupFakeWorker(),i.destroy(),e.terminate())})),i.on("ready",(t=>{if(e.removeEventListener("error",s),this.destroyed)n();else try{r()}catch{this._setupFakeWorker()}}));const r=()=>{const t=new Uint8Array;i.send("test",t,[t.buffer])};return void r()}catch{(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){PDFWorkerUtil.isWorkerDisabled||((0,_util.warn)("Setting up fake worker."),PDFWorkerUtil.isWorkerDisabled=!0),PDFWorker._setupFakeWorkerGlobal.then((t=>{if(this.destroyed)return void this._readyCapability.reject(new Error("Worker was destroyed"));const e=new LoopbackPort;this._port=e;const i="fake"+PDFWorkerUtil.fakeWorkerId++,n=new _message_handler.MessageHandler(i+"_worker",i,e);t.setup(n,e);const s=new _message_handler.MessageHandler(i,i+"_worker",e);this._messageHandler=s,this._readyCapability.resolve(),s.send("configure",{verbosity:this.verbosity})})).catch((t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0,this._webWorker&&(this._webWorker.terminate(),this._webWorker=null),PDFWorker.#l?.delete(this._port),this._port=null,this._messageHandler&&(this._messageHandler.destroy(),this._messageHandler=null)}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");const e=this.#l?.get(t.port);if(e){if(e._pendingDestroy)throw new Error("PDFWorker.fromPort - the worker is being destroyed.\nPlease remember to await `PDFDocumentLoadingTask.destroy()`-calls.");return e}return new PDFWorker(t)}static get workerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(null!==PDFWorkerUtil.fallbackWorkerSrc)return _util.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.'),PDFWorkerUtil.fallbackWorkerSrc;throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _mainThreadWorkerMessageHandler(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch{return null}}static get _setupFakeWorkerGlobal(){const loader=async()=>{const mainWorkerMessageHandler=this._mainThreadWorkerMessageHandler;if(mainWorkerMessageHandler)return mainWorkerMessageHandler;if(_util.isNodeJS&&"function"===typeof require){const worker=eval("require")(this.workerSrc);return worker.WorkerMessageHandler}return await(0,_display_utils.loadScript)(this.workerSrc),window.pdfjsWorker.WorkerMessageHandler};return(0,_util.shadow)(this,"_setupFakeWorkerGlobal",loader())}}exports.PDFWorker=PDFWorker;class WorkerTransport{#c=new Map;#h=new Map;#d=new Map;#u=null;constructor(t,e,i,n,s){this.messageHandler=t,this.loadingTask=e,this.commonObjs=new PDFObjects,this.fontLoader=new _font_loader.FontLoader({ownerDocument:n.ownerDocument,styleElement:n.styleElement}),this._params=n,this.canvasFactory=s.canvasFactory,this.filterFactory=s.filterFactory,this.cMapReaderFactory=s.cMapReaderFactory,this.standardFontDataFactory=s.standardFontDataFactory,this.destroyed=!1,this.destroyCapability=null,this._networkStream=i,this._fullReader=null,this._lastProgress=null,this.downloadInfoCapability=new _util.PromiseCapability,this.setupMessageHandler()}#p(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const i=this.#c.get(t);if(i)return i;const n=this.messageHandler.sendWithPromise(t,e);return this.#c.set(t,n),n}get annotationStorage(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}getRenderingIntent(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_util.AnnotationMode.ENABLE,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],s=_util.RenderingIntentFlag.DISPLAY,r=_annotation_storage.SerializableEmpty;switch(t){case"any":s=_util.RenderingIntentFlag.ANY;break;case"display":break;case"print":s=_util.RenderingIntentFlag.PRINT;break;default:(0,_util.warn)(`getRenderingIntent - invalid intent: ${t}`)}switch(e){case _util.AnnotationMode.DISABLE:s+=_util.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case _util.AnnotationMode.ENABLE:break;case _util.AnnotationMode.ENABLE_FORMS:s+=_util.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case _util.AnnotationMode.ENABLE_STORAGE:s+=_util.RenderingIntentFlag.ANNOTATIONS_STORAGE;const t=s&_util.RenderingIntentFlag.PRINT&&i instanceof _annotation_storage.PrintAnnotationStorage?i:this.annotationStorage;r=t.serializable;break;default:(0,_util.warn)(`getRenderingIntent - invalid annotationMode: ${e}`)}return n&&(s+=_util.RenderingIntentFlag.OPLIST),{renderingIntent:s,cacheKey:`${s}_${r.hash}`,annotationStorageSerializable:r}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0,this.destroyCapability=new _util.PromiseCapability,this.#u?.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const i of this.#h.values())t.push(i._destroy());this.#h.clear(),this.#d.clear(),this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);return t.push(e),Promise.all(t).then((()=>{this.commonObjs.clear(),this.fontLoader.clear(),this.#c.clear(),this.filterFactory.destroy(),this._networkStream?.cancelAllRequests(new _util.AbortException("Worker was terminated.")),this.messageHandler&&(this.messageHandler.destroy(),this.messageHandler=null),this.destroyCapability.resolve()}),this.destroyCapability.reject),this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{(0,_util.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available."),this._fullReader=this._networkStream.getFullReader(),this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}},e.onPull=()=>{this._fullReader.read().then((function(t){let{value:i,done:n}=t;n?e.close():((0,_util.assert)("object"===typeof i&&null!==i&&void 0!==i.byteLength,"GetReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(i),1,[i]))})).catch((t=>{e.error(t)}))},e.onCancel=t=>{this._fullReader.cancel(t),e.ready.catch((t=>{if(!this.destroyed)throw t}))}})),t.on("ReaderHeadersReady",(t=>{const i=new _util.PromiseCapability,n=this._fullReader;return n.headersReady.then((()=>{n.isStreamingSupported&&n.isRangeSupported||(this._lastProgress&&e.onProgress?.(this._lastProgress),n.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}),i.resolve({isStreamingSupported:n.isStreamingSupported,isRangeSupported:n.isRangeSupported,contentLength:n.contentLength})}),i.reject),i.promise})),t.on("GetRangeReader",((t,e)=>{(0,_util.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const i=this._networkStream.getRangeReader(t.begin,t.end);i?(e.onPull=()=>{i.read().then((function(t){let{value:i,done:n}=t;n?e.close():((0,_util.assert)("object"===typeof i&&null!==i&&void 0!==i.byteLength,"GetRangeReader - expected an ArrayBuffer."),e.enqueue(new Uint8Array(i),1,[i]))})).catch((t=>{e.error(t)}))},e.onCancel=t=>{i.cancel(t),e.ready.catch((t=>{if(!this.destroyed)throw t}))}):e.close()})),t.on("GetDoc",(t=>{let{pdfInfo:i}=t;this._numPages=i.numPages,this._htmlForXfa=i.htmlForXfa,delete i.htmlForXfa,e._capability.resolve(new PDFDocumentProxy(i,this))})),t.on("DocException",(function(t){let i;switch(t.name){case"PasswordException":i=new _util.PasswordException(t.message,t.code);break;case"InvalidPDFException":i=new _util.InvalidPDFException(t.message);break;case"MissingPDFException":i=new _util.MissingPDFException(t.message);break;case"UnexpectedResponseException":i=new _util.UnexpectedResponseException(t.message,t.status);break;case"UnknownErrorException":i=new _util.UnknownErrorException(t.message,t.details);break;default:(0,_util.unreachable)("DocException - expected a valid Error.")}e._capability.reject(i)})),t.on("PasswordRequest",(t=>{if(this.#u=new _util.PromiseCapability,e.onPassword){const n=t=>{t instanceof Error?this.#u.reject(t):this.#u.resolve({password:t})};try{e.onPassword(n,t.code)}catch(i){this.#u.reject(i)}}else this.#u.reject(new _util.PasswordException(t.message,t.code));return this.#u.promise})),t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length}),this.downloadInfoCapability.resolve(t)})),t.on("StartRenderPage",(t=>{if(this.destroyed)return;const e=this.#h.get(t.pageIndex);e._startRenderPage(t.transparency,t.cacheKey)})),t.on("commonobj",(e=>{let[i,n,s]=e;if(!this.destroyed&&!this.commonObjs.has(i))switch(n){case"Font":const e=this._params;if("error"in s){const t=s.error;(0,_util.warn)(`Error during font loading: ${t}`),this.commonObjs.resolve(i,t);break}const r=e.pdfBug&&globalThis.FontInspector?.enabled?(t,e)=>globalThis.FontInspector.fontAdded(t,e):null,a=new _font_loader.FontFaceObject(s,{isEvalSupported:e.isEvalSupported,disableFontFace:e.disableFontFace,ignoreErrors:e.ignoreErrors,inspectFont:r});this.fontLoader.bind(a).catch((e=>t.sendWithPromise("FontFallback",{id:i}))).finally((()=>{!e.fontExtraProperties&&a.data&&(a.data=null),this.commonObjs.resolve(i,a)}));break;case"FontPath":case"Image":case"Pattern":this.commonObjs.resolve(i,s);break;default:throw new Error(`Got unknown common object type ${n}`)}})),t.on("obj",(t=>{let[e,i,n,s]=t;if(this.destroyed)return;const r=this.#h.get(i);if(!r.objs.has(e))switch(n){case"Image":if(r.objs.resolve(e,s),s){let t;if(s.bitmap){const{width:e,height:i}=s;t=e*i*4}else t=s.data?.length||0;t>_util.MAX_IMAGE_SIZE_TO_CACHE&&(r._maybeCleanupAfterRender=!0)}break;case"Pattern":r.objs.resolve(e,s);break;default:throw new Error(`Got unknown object type ${n}`)}})),t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})})),t.on("FetchBuiltInCMap",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.cMapReaderFactory?this.cMapReaderFactory.fetch(t):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter.")))),t.on("FetchStandardFontData",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.standardFontDataFactory?this.standardFontDataFactory.fetch(t):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter."))))}getData(){return this.messageHandler.sendWithPromise("GetData",null)}saveDocument(){this.annotationStorage.size<=0&&(0,_util.warn)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");const{map:t,transfers:e}=this.annotationStorage.serializable;return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:t,filename:this._fullReader?.filename??null},e).finally((()=>{this.annotationStorage.resetModified()}))}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,i=this.#d.get(e);if(i)return i;const n=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((t=>{if(this.destroyed)throw new Error("Transport destroyed");const i=new PDFPageProxy(e,t,this,this._params.pdfBug);return this.#h.set(e,i),i}));return this.#d.set(e,n),n}getPageIndex(t){return"object"!==typeof t||null===t||!Number.isInteger(t.num)||t.num<0||!Number.isInteger(t.gen)||t.gen<0?Promise.reject(new Error("Invalid pageIndex request.")):this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen})}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}getFieldObjects(){return this.#p("GetFieldObjects")}hasJSActions(){return this.#p("HasJSActions")}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!==typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getDocJSActions(){return this.#p("GetDocJSActions")}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then((t=>new _optional_content_config.OptionalContentConfig(t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){const t="GetMetadata",e=this.#c.get(t);if(e)return e;const i=this.messageHandler.sendWithPromise(t,null).then((t=>({info:t[0],metadata:t[1]?new _metadata.Metadata(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})));return this.#c.set(t,i),i}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.destroyed){await this.messageHandler.sendWithPromise("Cleanup",null);for(const t of this.#h.values()){const e=t.cleanup();if(!e)throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear(),t||this.fontLoader.clear(),this.#c.clear(),this.filterFactory.destroy(!0)}}get loadingParams(){const{disableAutoFetch:t,enableXfa:e}=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:t,enableXfa:e})}}class PDFObjects{#g=Object.create(null);#f(t){return this.#g[t]||={capability:new _util.PromiseCapability,data:null}}get(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(e){const i=this.#f(t);return i.capability.promise.then((()=>e(i.data))),null}const i=this.#g[t];if(!i?.capability.settled)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return i.data}has(t){const e=this.#g[t];return e?.capability.settled||!1}resolve(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const i=this.#f(t);i.data=e,i.capability.resolve()}clear(){for(const t in this.#g){const{data:e}=this.#g[t];e?.bitmap?.close()}this.#g=Object.create(null)}}class RenderTask{#m=null;constructor(t){this.#m=t,this.onContinue=null}get promise(){return this.#m.capability.promise}cancel(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;this.#m.cancel(null,t)}get separateAnnots(){const{separateAnnots:t}=this.#m.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#m;return t.form||t.canvas&&e?.size>0}}exports.RenderTask=RenderTask;class InternalRenderTask{static#b=new WeakSet;constructor(t){let{callback:e,params:i,objs:n,commonObjs:s,annotationCanvasMap:r,operatorList:a,pageIndex:o,canvasFactory:l,filterFactory:c,useRequestAnimationFrame:h=!1,pdfBug:d=!1,pageColors:u=null}=t;this.callback=e,this.params=i,this.objs=n,this.commonObjs=s,this.annotationCanvasMap=r,this.operatorListIdx=null,this.operatorList=a,this._pageIndex=o,this.canvasFactory=l,this.filterFactory=c,this._pdfBug=d,this.pageColors=u,this.running=!1,this.graphicsReadyCallback=null,this.graphicsReady=!1,this._useRequestAnimationFrame=!0===h&&"undefined"!==typeof window,this.cancelled=!1,this.capability=new _util.PromiseCapability,this.task=new RenderTask(this),this._cancelBound=this.cancel.bind(this),this._continueBound=this._continue.bind(this),this._scheduleNextBound=this._scheduleNext.bind(this),this._nextBound=this._next.bind(this),this._canvas=i.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics(t){let{transparency:e=!1,optionalContentConfig:i}=t;if(this.cancelled)return;if(this._canvas){if(InternalRenderTask.#b.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");InternalRenderTask.#b.add(this._canvas)}this._pdfBug&&globalThis.StepperManager?.enabled&&(this.stepper=globalThis.StepperManager.create(this._pageIndex),this.stepper.init(this.operatorList),this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint());const{canvasContext:n,viewport:s,transform:r,background:a}=this.params;this.gfx=new _canvas.CanvasGraphics(n,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:i},this.annotationCanvasMap,this.pageColors),this.gfx.beginDrawing({transform:r,viewport:s,transparency:e,background:a}),this.operatorListIdx=0,this.graphicsReady=!0,this.graphicsReadyCallback?.()}cancel(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;this.running=!1,this.cancelled=!0,this.gfx?.endDrawing(),InternalRenderTask.#b.delete(this._canvas),this.callback(t||new _display_utils.RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,e))}operatorListChanged(){this.graphicsReady?(this.stepper?.updateOperatorList(this.operatorList),this.running||this._continue()):this.graphicsReadyCallback||=this._continueBound}_continue(){this.running=!0,this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame((()=>{this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper),this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1,this.operatorList.lastChunk&&(this.gfx.endDrawing(),InternalRenderTask.#b.delete(this._canvas),this.callback())))}}const version="3.11.174";exports.version=version;const build="ce8716743";exports.build=build},(t,e,i)=>{var n=i(3),s=i(126),r=i(135);n({target:"Set",proto:!0,real:!0,forced:!r("difference")},{difference:s})},(t,e,i)=>{var n=i(127),s=i(128),r=i(129),a=i(132),o=i(133),l=i(130),c=i(131),h=s.has,d=s.remove;t.exports=function(t){var e=n(this),i=o(t),s=r(e);return a(e)<=i.size?l(e,(function(t){i.includes(t)&&d(s,t)})):c(i.getIterator(),(function(t){h(e,t)&&d(s,t)})),s}},(t,e,i)=>{var n=i(128).has;t.exports=function(t){return n(t),t}},(t,e,i)=>{var n=i(14),s=Set.prototype;t.exports={Set:Set,add:n(s.add),has:n(s.has),remove:n(s["delete"]),proto:s}},(t,e,i)=>{var n=i(128),s=i(130),r=n.Set,a=n.add;t.exports=function(t){var e=new r;return s(t,(function(t){a(e,t)})),e}},(t,e,i)=>{var n=i(14),s=i(131),r=i(128),a=r.Set,o=r.proto,l=n(o.forEach),c=n(o.keys),h=c(new a).next;t.exports=function(t,e,i){return i?s({iterator:c(t),next:h},e):l(t,e)}},(t,e,i)=>{var n=i(8);t.exports=function(t,e,i){var s,r,a=i?t:t.iterator,o=t.next;while(!(s=n(o,a)).done)if(r=e(s.value),void 0!==r)return r}},(t,e,i)=>{var n=i(72),s=i(128);t.exports=n(s.proto,"size","get")||function(t){return t.size}},(t,e,i)=>{var n=i(31),s=i(47),r=i(8),a=i(62),o=i(134),l="Invalid size",c=RangeError,h=TypeError,d=Math.max,u=function(t,e,i,n){this.set=t,this.size=e,this.has=i,this.keys=n};u.prototype={getIterator:function(){return o(s(r(this.keys,this.set)))},includes:function(t){return r(this.has,this.set,t)}},t.exports=function(t){s(t);var e=+t.size;if(e!==e)throw h(l);var i=a(e);if(i<0)throw c(l);return new u(t,d(i,0),n(t.has),n(t.keys))}},t=>{t.exports=function(t){return{iterator:t,next:t.next,done:!1}}},(t,e,i)=>{var n=i(24),s=function(t){return{size:t,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}};t.exports=function(t){var e=n("Set");try{(new e)[t](s(0));try{return(new e)[t](s(-1)),!1}catch(i){return!0}}catch(r){return!1}}},(t,e,i)=>{var n=i(3),s=i(7),r=i(137),a=i(135),o=!a("intersection")||s((function(){return"3,2"!==Array.from(new Set([1,2,3]).intersection(new Set([3,2])))}));n({target:"Set",proto:!0,real:!0,forced:o},{intersection:r})},(t,e,i)=>{var n=i(127),s=i(128),r=i(132),a=i(133),o=i(130),l=i(131),c=s.Set,h=s.add,d=s.has;t.exports=function(t){var e=n(this),i=a(t),s=new c;return r(e)>i.size?l(i.getIterator(),(function(t){d(e,t)&&h(s,t)})):o(e,(function(t){i.includes(t)&&h(s,t)})),s}},(t,e,i)=>{var n=i(3),s=i(139),r=i(135);n({target:"Set",proto:!0,real:!0,forced:!r("isDisjointFrom")},{isDisjointFrom:s})},(t,e,i)=>{var n=i(127),s=i(128).has,r=i(132),a=i(133),o=i(130),l=i(131),c=i(140);t.exports=function(t){var e=n(this),i=a(t);if(r(e)<=i.size)return!1!==o(e,(function(t){if(i.includes(t))return!1}),!0);var h=i.getIterator();return!1!==l(h,(function(t){if(s(e,t))return c(h,"normal",!1)}))}},(t,e,i)=>{var n=i(8),s=i(47),r=i(30);t.exports=function(t,e,i){var a,o;s(t);try{if(a=r(t,"return"),!a){if("throw"===e)throw i;return i}a=n(a,t)}catch(l){o=!0,a=l}if("throw"===e)throw i;if(o)throw a;return s(a),i}},(t,e,i)=>{var n=i(3),s=i(142),r=i(135);n({target:"Set",proto:!0,real:!0,forced:!r("isSubsetOf")},{isSubsetOf:s})},(t,e,i)=>{var n=i(127),s=i(132),r=i(130),a=i(133);t.exports=function(t){var e=n(this),i=a(t);return!(s(e)>i.size)&&!1!==r(e,(function(t){if(!i.includes(t))return!1}),!0)}},(t,e,i)=>{var n=i(3),s=i(144),r=i(135);n({target:"Set",proto:!0,real:!0,forced:!r("isSupersetOf")},{isSupersetOf:s})},(t,e,i)=>{var n=i(127),s=i(128).has,r=i(132),a=i(133),o=i(131),l=i(140);t.exports=function(t){var e=n(this),i=a(t);if(r(e)<i.size)return!1;var c=i.getIterator();return!1!==o(c,(function(t){if(!s(e,t))return l(c,"normal",!1)}))}},(t,e,i)=>{var n=i(3),s=i(146),r=i(135);n({target:"Set",proto:!0,real:!0,forced:!r("symmetricDifference")},{symmetricDifference:s})},(t,e,i)=>{var n=i(127),s=i(128),r=i(129),a=i(133),o=i(131),l=s.add,c=s.has,h=s.remove;t.exports=function(t){var e=n(this),i=a(t).getIterator(),s=r(e);return o(i,(function(t){c(e,t)?h(s,t):l(s,t)})),s}},(t,e,i)=>{var n=i(3),s=i(148),r=i(135);n({target:"Set",proto:!0,real:!0,forced:!r("union")},{union:s})},(t,e,i)=>{var n=i(127),s=i(128).add,r=i(129),a=i(133),o=i(131);t.exports=function(t){var e=n(this),i=a(t).getIterator(),l=r(e);return o(i,(function(t){s(l,t)})),l}},(t,e,i)=>{var n=i(3),s=i(4),r=i(24),a=i(11),o=i(45).f,l=i(39),c=i(150),h=i(75),d=i(76),u=i(151),p=i(82),g=i(6),f=i(36),m="DOMException",b=r("Error"),v=r(m),_=function(){c(this,y);var t=arguments.length,e=d(t<1?void 0:arguments[0]),i=d(t<2?void 0:arguments[1],"Error"),n=new v(e,i),s=b(e);return s.name=m,o(n,"stack",a(1,p(s.stack,1))),h(n,this,_),n},y=_.prototype=v.prototype,A="stack"in b(m),S="stack"in new v(1,2),x=v&&g&&Object.getOwnPropertyDescriptor(s,m),E=!!x&&!(x.writable&&x.configurable),w=A&&!E&&!S;n({global:!0,constructor:!0,forced:f||w},{DOMException:w?_:v});var C=r(m),T=C.prototype;if(T.constructor!==C)for(var P in f||o(T,"constructor",a(1,C)),u)if(l(u,P)){var k=u[P],M=k.s;l(C,M)||o(C,M,a(6,k.c))}},(t,e,i)=>{var n=i(25),s=TypeError;t.exports=function(t,e){if(n(e,t))return t;throw s("Incorrect invocation")}},t=>{t.exports={IndexSizeError:{s:"INDEX_SIZE_ERR",c:1,m:1},DOMStringSizeError:{s:"DOMSTRING_SIZE_ERR",c:2,m:0},HierarchyRequestError:{s:"HIERARCHY_REQUEST_ERR",c:3,m:1},WrongDocumentError:{s:"WRONG_DOCUMENT_ERR",c:4,m:1},InvalidCharacterError:{s:"INVALID_CHARACTER_ERR",c:5,m:1},NoDataAllowedError:{s:"NO_DATA_ALLOWED_ERR",c:6,m:0},NoModificationAllowedError:{s:"NO_MODIFICATION_ALLOWED_ERR",c:7,m:1},NotFoundError:{s:"NOT_FOUND_ERR",c:8,m:1},NotSupportedError:{s:"NOT_SUPPORTED_ERR",c:9,m:1},InUseAttributeError:{s:"INUSE_ATTRIBUTE_ERR",c:10,m:1},InvalidStateError:{s:"INVALID_STATE_ERR",c:11,m:1},SyntaxError:{s:"SYNTAX_ERR",c:12,m:1},InvalidModificationError:{s:"INVALID_MODIFICATION_ERR",c:13,m:1},NamespaceError:{s:"NAMESPACE_ERR",c:14,m:1},InvalidAccessError:{s:"INVALID_ACCESS_ERR",c:15,m:1},ValidationError:{s:"VALIDATION_ERR",c:16,m:0},TypeMismatchError:{s:"TYPE_MISMATCH_ERR",c:17,m:1},SecurityError:{s:"SECURITY_ERR",c:18,m:1},NetworkError:{s:"NETWORK_ERR",c:19,m:1},AbortError:{s:"ABORT_ERR",c:20,m:1},URLMismatchError:{s:"URL_MISMATCH_ERR",c:21,m:1},QuotaExceededError:{s:"QUOTA_EXCEEDED_ERR",c:22,m:1},TimeoutError:{s:"TIMEOUT_ERR",c:23,m:1},InvalidNodeTypeError:{s:"INVALID_NODE_TYPE_ERR",c:24,m:1},DataCloneError:{s:"DATA_CLONE_ERR",c:25,m:1}}},(t,e,i)=>{var n=i(36),s=i(3),r=i(4),a=i(24),o=i(14),l=i(7),c=i(41),h=i(21),d=i(153),u=i(17),p=i(20),g=i(23),f=i(154),m=i(47),b=i(78),v=i(39),_=i(159),y=i(44),A=i(64),S=i(85),x=i(160),E=i(162),w=i(128),C=i(83),T=i(119),P=r.Object,k=r.Array,M=r.Date,R=r.Error,F=r.EvalError,D=r.RangeError,I=r.ReferenceError,O=r.SyntaxError,L=r.TypeError,N=r.URIError,j=r.PerformanceMark,B=r.WebAssembly,U=B&&B.CompileError||R,z=B&&B.LinkError||R,H=B&&B.RuntimeError||R,W=a("DOMException"),q=E.Map,G=E.has,V=E.get,$=E.set,X=w.Set,K=w.add,Y=a("Object","keys"),J=o([].push),Q=o((!0).valueOf),Z=o(1..valueOf),tt=o("".valueOf),et=o(M.prototype.getTime),it=c("structuredClone"),nt="DataCloneError",st="Transferring",rt=function(t){return!l((function(){var e=new r.Set([7]),i=t(e),n=t(P(7));return i===e||!i.has(7)||"object"!=typeof n||7!==+n}))&&t},at=function(t,e){return!l((function(){var i=new e,n=t({a:i,b:i});return!(n&&n.a===n.b&&n.a instanceof e&&n.a.stack===i.stack)}))},ot=function(t){return!l((function(){var e=t(new r.AggregateError([1],it,{cause:3}));return"AggregateError"!==e.name||1!==e.errors[0]||e.message!==it||3!==e.cause}))},lt=r.structuredClone,ct=n||!at(lt,R)||!at(lt,W)||!ot(lt),ht=!lt&&rt((function(t){return new j(it,{detail:t}).detail})),dt=rt(lt)||ht,ut=function(t){throw new W("Uncloneable type: "+t,nt)},pt=function(t,e){throw new W((e||"Cloning")+" of "+t+" cannot be properly polyfilled in this engine",nt)},gt=function(t,e){return dt||pt(e),dt(t)},ft=function(){var t;try{t=new r.DataTransfer}catch(e){try{t=new r.ClipboardEvent("").clipboardData}catch(i){}}return t&&t.items&&t.files?t:null},mt=function(t,e,i){if(G(e,t))return V(e,t);var n,s,a,o,l,c,h=i||b(t);if("SharedArrayBuffer"===h)n=dt?dt(t):t;else{var d=r.DataView;d||"function"==typeof t.slice||pt("ArrayBuffer");try{if("function"!=typeof t.slice||t.resizable){s=t.byteLength,a="maxByteLength"in t?{maxByteLength:t.maxByteLength}:void 0,n=new ArrayBuffer(s,a),o=new d(t),l=new d(n);for(c=0;c<s;c++)l.setUint8(c,o.getUint8(c))}else n=t.slice(0)}catch(u){throw new W("ArrayBuffer is detached",nt)}}return $(e,t,n),n},bt=function(t,e,i,n,s){var a=r[e];return p(a)||pt(e),new a(mt(t.buffer,s),i,n)},vt=function(t,e,i){this.object=t,this.type=e,this.metadata=i},_t=function(t,e,i){if(g(t)&&ut("Symbol"),!p(t))return t;if(e){if(G(e,t))return V(e,t)}else e=new q;var n,s,o,l,c,d,u,f,m=b(t);switch(m){case"Array":o=k(A(t));break;case"Object":o={};break;case"Map":o=new q;break;case"Set":o=new X;break;case"RegExp":o=new RegExp(t.source,x(t));break;case"Error":switch(s=t.name,s){case"AggregateError":o=a("AggregateError")([]);break;case"EvalError":o=F();break;case"RangeError":o=D();break;case"ReferenceError":o=I();break;case"SyntaxError":o=O();break;case"TypeError":o=L();break;case"URIError":o=N();break;case"CompileError":o=U();break;case"LinkError":o=z();break;case"RuntimeError":o=H();break;default:o=R()}break;case"DOMException":o=new W(t.message,t.name);break;case"ArrayBuffer":case"SharedArrayBuffer":o=i?new vt(t,m):mt(t,e,m);break;case"DataView":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float16Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":d="DataView"===m?t.byteLength:t.length,o=i?new vt(t,m,{offset:t.byteOffset,length:d}):bt(t,m,t.byteOffset,d,e);break;case"DOMQuad":try{o=new DOMQuad(_t(t.p1,e,i),_t(t.p2,e,i),_t(t.p3,e,i),_t(t.p4,e,i))}catch(S){o=gt(t,m)}break;case"File":if(dt)try{o=dt(t),b(o)!==m&&(o=void 0)}catch(S){}if(!o)try{o=new File([t],t.name,t)}catch(S){}o||pt(m);break;case"FileList":if(l=ft(),l){for(c=0,d=A(t);c<d;c++)l.items.add(_t(t[c],e,i));o=l.files}else o=gt(t,m);break;case"ImageData":try{o=new ImageData(_t(t.data,e,i),t.width,t.height,{colorSpace:t.colorSpace})}catch(S){o=gt(t,m)}break;default:if(dt)o=dt(t);else switch(m){case"BigInt":o=P(t.valueOf());break;case"Boolean":o=P(Q(t));break;case"Number":o=P(Z(t));break;case"String":o=P(tt(t));break;case"Date":o=new M(et(t));break;case"Blob":try{o=t.slice(0,t.size,t.type)}catch(S){pt(m)}break;case"DOMPoint":case"DOMPointReadOnly":n=r[m];try{o=n.fromPoint?n.fromPoint(t):new n(t.x,t.y,t.z,t.w)}catch(S){pt(m)}break;case"DOMRect":case"DOMRectReadOnly":n=r[m];try{o=n.fromRect?n.fromRect(t):new n(t.x,t.y,t.width,t.height)}catch(S){pt(m)}break;case"DOMMatrix":case"DOMMatrixReadOnly":n=r[m];try{o=n.fromMatrix?n.fromMatrix(t):new n(t)}catch(S){pt(m)}break;case"AudioData":case"VideoFrame":h(t.clone)||pt(m);try{o=t.clone()}catch(S){ut(m)}break;case"CropTarget":case"CryptoKey":case"FileSystemDirectoryHandle":case"FileSystemFileHandle":case"FileSystemHandle":case"GPUCompilationInfo":case"GPUCompilationMessage":case"ImageBitmap":case"RTCCertificate":case"WebAssembly.Module":pt(m);default:ut(m)}}switch($(e,t,o),m){case"Array":case"Object":for(u=Y(t),c=0,d=A(u);c<d;c++)f=u[c],_(o,f,_t(t[f],e,i));break;case"Map":t.forEach((function(t,n){$(o,_t(n,e,i),_t(t,e,i))}));break;case"Set":t.forEach((function(t){K(o,_t(t,e,i))}));break;case"Error":y(o,"message",_t(t.message,e,i)),v(t,"cause")&&y(o,"cause",_t(t.cause,e,i)),"AggregateError"===s&&(o.errors=_t(t.errors,e,i));case"DOMException":C&&y(o,"stack",_t(t.stack,e,i))}return o},yt=function(t,e){if(!p(t))return t;if(G(e,t))return V(e,t);var i,n,s,r,a,o,l,c;if(t instanceof vt)switch(i=t.type,n=t.object,i){case"ArrayBuffer":case"SharedArrayBuffer":c=mt(n,e,i);break;case"DataView":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float16Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":s=t.metadata,c=bt(n,i,s.offset,s.length,e)}else switch(b(t)){case"Array":case"Object":for(o=Y(t),r=0,a=A(o);r<a;r++)l=o[r],t[l]=yt(t[l],e);break;case"Map":c=new q,t.forEach((function(t,i){$(c,yt(i,e),yt(t,e))}));break;case"Set":c=new X,t.forEach((function(t){K(c,yt(t,e))}));break;case"Error":t.message=yt(t.message,e),v(t,"cause")&&(t.cause=yt(t.cause,e)),"AggregateError"===t.name&&(t.errors=yt(t.errors,e));case"DOMException":C&&(t.stack=yt(t.stack,e))}return $(e,t,c||t),c||t},At=function(t,e){if(!p(t))throw L("Transfer option cannot be converted to a sequence");var i=[];f(t,(function(t){J(i,m(t))}));var n,s,a,o,l,c,u=0,g=A(i),v=[];while(u<g)if(n=i[u++],s=b(n),"ArrayBuffer"!==s){if(G(e,n))throw new W("Duplicate transferable",nt);if(T)o=lt(n,{transfer:[n]});else switch(s){case"ImageBitmap":a=r.OffscreenCanvas,d(a)||pt(s,st);try{l=new a(n.width,n.height),c=l.getContext("bitmaprenderer"),c.transferFromImageBitmap(n),o=l.transferToImageBitmap()}catch(_){}break;case"AudioData":case"VideoFrame":h(n.clone)&&h(n.close)||pt(s,st);try{o=n.clone(),n.close()}catch(_){}break;case"MediaSourceHandle":case"MessagePort":case"OffscreenCanvas":case"ReadableStream":case"TransformStream":case"WritableStream":pt(s,st)}if(void 0===o)throw new W("This object cannot be transferred: "+s,nt);$(e,n,o)}else J(v,n);return v},St=function(t,e){var i,n,s=0,r=A(t);while(s<r){if(i=t[s++],G(e,i))throw new W("Duplicate transferable",nt);T?n=lt(i,{transfer:[i]}):(h(i.transfer)||pt("ArrayBuffer",st),n=i.transfer()),$(e,i,n)}};s({global:!0,enumerable:!0,sham:!T,forced:ct},{structuredClone:function(t){var e,i,n=S(arguments.length,1)>1&&!u(arguments[1])?m(arguments[1]):void 0,s=n?n.transfer:void 0,r=!1;void 0!==s&&(e=new q,i=At(s,e),r=!!A(i));var a=_t(t,e,r);return r&&(e=new q,St(s,e),a=yt(a,e)),a}})},(t,e,i)=>{var n=i(14),s=i(7),r=i(21),a=i(78),o=i(24),l=i(51),c=function(){},h=[],d=o("Reflect","construct"),u=/^\s*(?:class|function)\b/,p=n(u.exec),g=!u.exec(c),f=function(t){if(!r(t))return!1;try{return d(c,h,t),!0}catch(e){return!1}},m=function(t){if(!r(t))return!1;switch(a(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return g||!!p(u,l(t))}catch(e){return!0}};m.sham=!0,t.exports=!d||s((function(){var t;return f(f.call)||!f(Object)||!f((function(){t=!0}))||t}))?m:f},(t,e,i)=>{var n=i(99),s=i(8),r=i(47),a=i(32),o=i(155),l=i(64),c=i(25),h=i(157),d=i(158),u=i(140),p=TypeError,g=function(t,e){this.stopped=t,this.result=e},f=g.prototype;t.exports=function(t,e,i){var m,b,v,_,y,A,S,x=i&&i.that,E=!(!i||!i.AS_ENTRIES),w=!(!i||!i.IS_RECORD),C=!(!i||!i.IS_ITERATOR),T=!(!i||!i.INTERRUPTED),P=n(e,x),k=function(t){return m&&u(m,"normal",t),new g(!0,t)},M=function(t){return E?(r(t),T?P(t[0],t[1],k):P(t[0],t[1])):T?P(t,k):P(t)};if(w)m=t.iterator;else if(C)m=t;else{if(b=d(t),!b)throw p(a(t)+" is not iterable");if(o(b)){for(v=0,_=l(t);_>v;v++)if(y=M(t[v]),y&&c(f,y))return y;return new g(!1)}m=h(t,b)}A=w?t.next:m.next;while(!(S=s(A,m)).done){try{y=M(S.value)}catch(R){u(m,"throw",R)}if("object"==typeof y&&y&&c(f,y))return y}return new g(!1)}},(t,e,i)=>{var n=i(34),s=i(156),r=n("iterator"),a=Array.prototype;t.exports=function(t){return void 0!==t&&(s.Array===t||a[r]===t)}},t=>{t.exports={}},(t,e,i)=>{var n=i(8),s=i(31),r=i(47),a=i(32),o=i(158),l=TypeError;t.exports=function(t,e){var i=arguments.length<2?o(t):e;if(s(i))return r(n(i,t));throw l(a(t)+" is not iterable")}},(t,e,i)=>{var n=i(78),s=i(30),r=i(17),a=i(156),o=i(34),l=o("iterator");t.exports=function(t){if(!r(t))return s(t,l)||s(t,"@@iterator")||a[n(t)]}},(t,e,i)=>{var n=i(18),s=i(45),r=i(11);t.exports=function(t,e,i){var a=n(e);a in t?s.f(t,a,r(0,i)):t[a]=i}},(t,e,i)=>{var n=i(8),s=i(39),r=i(25),a=i(161),o=RegExp.prototype;t.exports=function(t){var e=t.flags;return void 0!==e||"flags"in o||s(t,"flags")||!r(o,t)?e:n(a,t)}},(t,e,i)=>{var n=i(47);t.exports=function(){var t=n(this),e="";return t.hasIndices&&(e+="d"),t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.unicodeSets&&(e+="v"),t.sticky&&(e+="y"),e}},(t,e,i)=>{var n=i(14),s=Map.prototype;t.exports={Map:Map,set:n(s.set),get:n(s.get),has:n(s.has),remove:n(s["delete"]),proto:s}},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.SerializableEmpty=e.PrintAnnotationStorage=e.AnnotationStorage=void 0,i(89),i(149),i(152);var n=i(1),s=i(164),r=i(170);const a=Object.freeze({map:null,hash:"",transfers:void 0});e.SerializableEmpty=a;class o{#v=!1;#_=new Map;constructor(){this.onSetModified=null,this.onResetModified=null,this.onAnnotationEditor=null}getValue(t,e){const i=this.#_.get(t);return void 0===i?e:Object.assign(e,i)}getRawValue(t){return this.#_.get(t)}remove(t){if(this.#_.delete(t),0===this.#_.size&&this.resetModified(),"function"===typeof this.onAnnotationEditor){for(const t of this.#_.values())if(t instanceof s.AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(t,e){const i=this.#_.get(t);let n=!1;if(void 0!==i)for(const[s,r]of Object.entries(e))i[s]!==r&&(n=!0,i[s]=r);else n=!0,this.#_.set(t,e);n&&this.#y(),e instanceof s.AnnotationEditor&&"function"===typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this.#_.has(t)}getAll(){return this.#_.size>0?(0,n.objectFromMap)(this.#_):null}setAll(t){for(const[e,i]of Object.entries(t))this.setValue(e,i)}get size(){return this.#_.size}#y(){this.#v||(this.#v=!0,"function"===typeof this.onSetModified&&this.onSetModified())}resetModified(){this.#v&&(this.#v=!1,"function"===typeof this.onResetModified&&this.onResetModified())}get print(){return new l(this)}get serializable(){if(0===this.#_.size)return a;const t=new Map,e=new r.MurmurHash3_64,i=[],n=Object.create(null);let o=!1;for(const[r,a]of this.#_){const i=a instanceof s.AnnotationEditor?a.serialize(!1,n):a;i&&(t.set(r,i),e.update(`${r}:${JSON.stringify(i)}`),o||=!!i.bitmap)}if(o)for(const s of t.values())s.bitmap&&i.push(s.bitmap);return t.size>0?{map:t,hash:e.hexdigest(),transfers:i}:a}}e.AnnotationStorage=o;class l extends o{#A;constructor(t){super();const{map:e,hash:i,transfers:n}=t.serializable,s=structuredClone(e,null);this.#A={map:s,hash:i,transfers:n}}get print(){(0,n.unreachable)("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#A}}e.PrintAnnotationStorage=l},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AnnotationEditor=void 0,i(89),i(2);var n=i(165),s=i(1),r=i(168);class a{#S="";#x=!1;#E=null;#w=null;#C=null;#T=!1;#P=null;#k=this.focusin.bind(this);#M=this.focusout.bind(this);#R=!1;#F=!1;#D=!1;_initialOptions=Object.create(null);_uiManager=null;_focusEventsAllowed=!0;_l10nPromise=null;#I=!1;#O=a._zIndex++;static _borderLineWidth=-1;static _colorManager=new n.ColorManager;static _zIndex=1;static SMALL_EDITOR_SIZE=0;constructor(t){this.constructor===a&&(0,s.unreachable)("Cannot initialize AnnotationEditor."),this.parent=t.parent,this.id=t.id,this.width=this.height=null,this.pageIndex=t.parent.pageIndex,this.name=t.name,this.div=null,this._uiManager=t.uiManager,this.annotationElementId=null,this._willKeepAspectRatio=!1,this._initialOptions.isCentered=t.isCentered,this._structTreeParentId=null;const{rotation:e,rawDims:{pageWidth:i,pageHeight:n,pageX:r,pageY:o}}=this.parent.viewport;this.rotation=e,this.pageRotation=(360+e-this._uiManager.viewParameters.rotation)%360,this.pageDimensions=[i,n],this.pageTranslation=[r,o];const[l,c]=this.parentDimensions;this.x=t.x/l,this.y=t.y/c,this.isAttachedToDOM=!1,this.deleted=!1}get editorType(){return Object.getPrototypeOf(this).constructor._type}static get _defaultLineColor(){return(0,s.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}static deleteAnnotationElement(t){const e=new o({id:t.parent.getNextId(),parent:t.parent,uiManager:t._uiManager});e.annotationElementId=t.annotationElementId,e.deleted=!0,e._uiManager.addToAnnotationStorage(e)}static initialize(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(a._l10nPromise||=new Map(["editor_alt_text_button_label","editor_alt_text_edit_button_label","editor_alt_text_decorative_tooltip"].map((e=>[e,t.get(e)]))),e?.strings)for(const n of e.strings)a._l10nPromise.set(n,t.get(n));if(-1!==a._borderLineWidth)return;const i=getComputedStyle(document.documentElement);a._borderLineWidth=parseFloat(i.getPropertyValue("--outline-width"))||0}static updateDefaultParams(t,e){}static get defaultPropertiesToUpdate(){return[]}static isHandlingMimeForPasting(t){return!1}static paste(t,e){(0,s.unreachable)("Not implemented")}get propertiesToUpdate(){return[]}get _isDraggable(){return this.#I}set _isDraggable(t){this.#I=t,this.div?.classList.toggle("draggable",t)}center(){const[t,e]=this.pageDimensions;switch(this.parentRotation){case 90:this.x-=this.height*e/(2*t),this.y+=this.width*t/(2*e);break;case 180:this.x+=this.width/2,this.y+=this.height/2;break;case 270:this.x+=this.height*e/(2*t),this.y-=this.width*t/(2*e);break;default:this.x-=this.width/2,this.y-=this.height/2;break}this.fixAndSetPosition()}addCommands(t){this._uiManager.addCommands(t)}get currentLayer(){return this._uiManager.currentLayer}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#O}setParent(t){null!==t&&(this.pageIndex=t.pageIndex,this.pageDimensions=t.pageDimensions),this.parent=t}focusin(t){this._focusEventsAllowed&&(this.#R?this.#R=!1:this.parent.setSelected(this))}focusout(t){if(!this._focusEventsAllowed)return;if(!this.isAttachedToDOM)return;const e=t.relatedTarget;e?.closest(`#${this.id}`)||(t.preventDefault(),this.parent?.isMultipleSelection||this.commitOrRemove())}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.addToAnnotationStorage()}addToAnnotationStorage(){this._uiManager.addToAnnotationStorage(this)}setAt(t,e,i,n){const[s,r]=this.parentDimensions;[i,n]=this.screenToPageTranslation(i,n),this.x=(t+i)/s,this.y=(e+n)/r,this.fixAndSetPosition()}#L(t,e,i){let[n,s]=t;[e,i]=this.screenToPageTranslation(e,i),this.x+=e/n,this.y+=i/s,this.fixAndSetPosition()}translate(t,e){this.#L(this.parentDimensions,t,e)}translateInPage(t,e){this.#L(this.pageDimensions,t,e),this.div.scrollIntoView({block:"nearest"})}drag(t,e){const[i,n]=this.parentDimensions;if(this.x+=t/i,this.y+=e/n,this.parent&&(this.x<0||this.x>1||this.y<0||this.y>1)){const{x:t,y:e}=this.div.getBoundingClientRect();this.parent.findNewParent(this,t,e)&&(this.x-=Math.floor(this.x),this.y-=Math.floor(this.y))}let{x:s,y:r}=this;const[a,o]=this.#N();s+=a,r+=o,this.div.style.left=`${(100*s).toFixed(2)}%`,this.div.style.top=`${(100*r).toFixed(2)}%`,this.div.scrollIntoView({block:"nearest"})}#N(){const[t,e]=this.parentDimensions,{_borderLineWidth:i}=a,n=i/t,s=i/e;switch(this.rotation){case 90:return[-n,s];case 180:return[n,s];case 270:return[n,-s];default:return[-n,-s]}}fixAndSetPosition(){const[t,e]=this.pageDimensions;let{x:i,y:n,width:s,height:r}=this;switch(s*=t,r*=e,i*=t,n*=e,this.rotation){case 0:i=Math.max(0,Math.min(t-s,i)),n=Math.max(0,Math.min(e-r,n));break;case 90:i=Math.max(0,Math.min(t-r,i)),n=Math.min(e,Math.max(s,n));break;case 180:i=Math.min(t,Math.max(s,i)),n=Math.min(e,Math.max(r,n));break;case 270:i=Math.min(t,Math.max(r,i)),n=Math.max(0,Math.min(e-s,n));break}this.x=i/=t,this.y=n/=e;const[a,o]=this.#N();i+=a,n+=o;const{style:l}=this.div;l.left=`${(100*i).toFixed(2)}%`,l.top=`${(100*n).toFixed(2)}%`,this.moveInDOM()}static#j(t,e,i){switch(i){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}screenToPageTranslation(t,e){return a.#j(t,e,this.parentRotation)}pageTranslationToScreen(t,e){return a.#j(t,e,360-this.parentRotation)}#B(t){switch(t){case 90:{const[t,e]=this.pageDimensions;return[0,-t/e,e/t,0]}case 180:return[-1,0,0,-1];case 270:{const[t,e]=this.pageDimensions;return[0,t/e,-e/t,0]}default:return[1,0,0,1]}}get parentScale(){return this._uiManager.viewParameters.realScale}get parentRotation(){return(this._uiManager.viewParameters.rotation+this.pageRotation)%360}get parentDimensions(){const{parentScale:t,pageDimensions:[e,i]}=this,n=e*t,r=i*t;return s.FeatureTest.isCSSRoundSupported?[Math.round(n),Math.round(r)]:[n,r]}setDims(t,e){const[i,n]=this.parentDimensions;this.div.style.width=`${(100*t/i).toFixed(2)}%`,this.#T||(this.div.style.height=`${(100*e/n).toFixed(2)}%`),this.#E?.classList.toggle("small",t<a.SMALL_EDITOR_SIZE||e<a.SMALL_EDITOR_SIZE)}fixDims(){const{style:t}=this.div,{height:e,width:i}=t,n=i.endsWith("%"),s=!this.#T&&e.endsWith("%");if(n&&s)return;const[r,a]=this.parentDimensions;n||(t.width=`${(100*parseFloat(i)/r).toFixed(2)}%`),this.#T||s||(t.height=`${(100*parseFloat(e)/a).toFixed(2)}%`)}getInitialTranslation(){return[0,0]}#U(){if(this.#P)return;this.#P=document.createElement("div"),this.#P.classList.add("resizers");const t=["topLeft","topRight","bottomRight","bottomLeft"];this._willKeepAspectRatio||t.push("topMiddle","middleRight","bottomMiddle","middleLeft");for(const e of t){const t=document.createElement("div");this.#P.append(t),t.classList.add("resizer",e),t.addEventListener("pointerdown",this.#z.bind(this,e)),t.addEventListener("contextmenu",r.noContextMenu)}this.div.prepend(this.#P)}#z(t,e){e.preventDefault();const{isMac:i}=s.FeatureTest.platform;if(0!==e.button||e.ctrlKey&&i)return;const n=this.#H.bind(this,t),r=this._isDraggable;this._isDraggable=!1;const a={passive:!0,capture:!0};window.addEventListener("pointermove",n,a);const o=this.x,l=this.y,c=this.width,h=this.height,d=this.parent.div.style.cursor,u=this.div.style.cursor;this.div.style.cursor=this.parent.div.style.cursor=window.getComputedStyle(e.target).cursor;const p=()=>{this._isDraggable=r,window.removeEventListener("pointerup",p),window.removeEventListener("blur",p),window.removeEventListener("pointermove",n,a),this.parent.div.style.cursor=d,this.div.style.cursor=u;const t=this.x,e=this.y,i=this.width,s=this.height;t===o&&e===l&&i===c&&s===h||this.addCommands({cmd:()=>{this.width=i,this.height=s,this.x=t,this.y=e;const[n,r]=this.parentDimensions;this.setDims(n*i,r*s),this.fixAndSetPosition()},undo:()=>{this.width=c,this.height=h,this.x=o,this.y=l;const[t,e]=this.parentDimensions;this.setDims(t*c,e*h),this.fixAndSetPosition()},mustExec:!0})};window.addEventListener("pointerup",p),window.addEventListener("blur",p)}#H(t,e){const[i,n]=this.parentDimensions,s=this.x,r=this.y,o=this.width,l=this.height,c=a.MIN_SIZE/i,h=a.MIN_SIZE/n,d=t=>Math.round(1e4*t)/1e4,u=this.#B(this.rotation),p=(t,e)=>[u[0]*t+u[2]*e,u[1]*t+u[3]*e],g=this.#B(360-this.rotation),f=(t,e)=>[g[0]*t+g[2]*e,g[1]*t+g[3]*e];let m,b,v=!1,_=!1;switch(t){case"topLeft":v=!0,m=(t,e)=>[0,0],b=(t,e)=>[t,e];break;case"topMiddle":m=(t,e)=>[t/2,0],b=(t,e)=>[t/2,e];break;case"topRight":v=!0,m=(t,e)=>[t,0],b=(t,e)=>[0,e];break;case"middleRight":_=!0,m=(t,e)=>[t,e/2],b=(t,e)=>[0,e/2];break;case"bottomRight":v=!0,m=(t,e)=>[t,e],b=(t,e)=>[0,0];break;case"bottomMiddle":m=(t,e)=>[t/2,e],b=(t,e)=>[t/2,0];break;case"bottomLeft":v=!0,m=(t,e)=>[0,e],b=(t,e)=>[t,0];break;case"middleLeft":_=!0,m=(t,e)=>[0,e/2],b=(t,e)=>[t,e/2];break}const y=m(o,l),A=b(o,l);let S=p(...A);const x=d(s+S[0]),E=d(r+S[1]);let w=1,C=1,[T,P]=this.screenToPageTranslation(e.movementX,e.movementY);if([T,P]=f(T/i,P/n),v){const t=Math.hypot(o,l);w=C=Math.max(Math.min(Math.hypot(A[0]-y[0]-T,A[1]-y[1]-P)/t,1/o,1/l),c/o,h/l)}else _?w=Math.max(c,Math.min(1,Math.abs(A[0]-y[0]-T)))/o:C=Math.max(h,Math.min(1,Math.abs(A[1]-y[1]-P)))/l;const k=d(o*w),M=d(l*C);S=p(...b(k,M));const R=x-S[0],F=E-S[1];this.width=k,this.height=M,this.x=R,this.y=F,this.setDims(i*k,n*M),this.fixAndSetPosition()}async addAltTextButton(){if(this.#E)return;const t=this.#E=document.createElement("button");t.className="altText";const e=await a._l10nPromise.get("editor_alt_text_button_label");if(t.textContent=e,t.setAttribute("aria-label",e),t.tabIndex="0",t.addEventListener("contextmenu",r.noContextMenu),t.addEventListener("pointerdown",(t=>t.stopPropagation())),t.addEventListener("click",(t=>{t.preventDefault(),this._uiManager.editAltText(this)}),{capture:!0}),t.addEventListener("keydown",(e=>{e.target===t&&"Enter"===e.key&&(e.preventDefault(),this._uiManager.editAltText(this))})),this.#W(),this.div.append(t),!a.SMALL_EDITOR_SIZE){const e=40;a.SMALL_EDITOR_SIZE=Math.min(128,Math.round(t.getBoundingClientRect().width*(1+e/100)))}}async#W(){const t=this.#E;if(!t)return;if(!this.#S&&!this.#x)return t.classList.remove("done"),void this.#w?.remove();a._l10nPromise.get("editor_alt_text_edit_button_label").then((e=>{t.setAttribute("aria-label",e)}));let e=this.#w;if(!e){this.#w=e=document.createElement("span"),e.className="tooltip",e.setAttribute("role","tooltip");const i=e.id=`alt-text-tooltip-${this.id}`;t.setAttribute("aria-describedby",i);const n=100;t.addEventListener("mouseenter",(()=>{this.#C=setTimeout((()=>{this.#C=null,this.#w.classList.add("show"),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"alt_text_tooltip"}}})}),n)})),t.addEventListener("mouseleave",(()=>{clearTimeout(this.#C),this.#C=null,this.#w?.classList.remove("show")}))}t.classList.add("done"),e.innerText=this.#x?await a._l10nPromise.get("editor_alt_text_decorative_tooltip"):this.#S,e.parentNode||t.append(e)}getClientDimensions(){return this.div.getBoundingClientRect()}get altTextData(){return{altText:this.#S,decorative:this.#x}}set altTextData(t){let{altText:e,decorative:i}=t;this.#S===e&&this.#x===i||(this.#S=e,this.#x=i,this.#W())}render(){this.div=document.createElement("div"),this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360),this.div.className=this.name,this.div.setAttribute("id",this.id),this.div.setAttribute("tabIndex",0),this.setInForeground(),this.div.addEventListener("focusin",this.#k),this.div.addEventListener("focusout",this.#M);const[t,e]=this.parentDimensions;this.parentRotation%180!==0&&(this.div.style.maxWidth=`${(100*e/t).toFixed(2)}%`,this.div.style.maxHeight=`${(100*t/e).toFixed(2)}%`);const[i,s]=this.getInitialTranslation();return this.translate(i,s),(0,n.bindEvents)(this,this.div,["pointerdown"]),this.div}pointerdown(t){const{isMac:e}=s.FeatureTest.platform;0!==t.button||t.ctrlKey&&e?t.preventDefault():(this.#R=!0,this.#q(t))}#q(t){if(!this._isDraggable)return;const e=this._uiManager.isSelected(this);let i,n;this._uiManager.setUpDragSession(),e&&(i={passive:!0,capture:!0},n=t=>{const[e,i]=this.screenToPageTranslation(t.movementX,t.movementY);this._uiManager.dragSelectedEditors(e,i)},window.addEventListener("pointermove",n,i));const r=()=>{if(window.removeEventListener("pointerup",r),window.removeEventListener("blur",r),e&&window.removeEventListener("pointermove",n,i),this.#R=!1,!this._uiManager.endDragSession()){const{isMac:e}=s.FeatureTest.platform;t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this)}};window.addEventListener("pointerup",r),window.addEventListener("blur",r)}moveInDOM(){this.parent?.moveEditorInDOM(this)}_setParentAndPosition(t,e,i){t.changeParent(this),this.x=e,this.y=i,this.fixAndSetPosition()}getRect(t,e){const i=this.parentScale,[n,s]=this.pageDimensions,[r,a]=this.pageTranslation,o=t/i,l=e/i,c=this.x*n,h=this.y*s,d=this.width*n,u=this.height*s;switch(this.rotation){case 0:return[c+o+r,s-h-l-u+a,c+o+d+r,s-h-l+a];case 90:return[c+l+r,s-h+o+a,c+l+u+r,s-h+o+d+a];case 180:return[c-o-d+r,s-h+l+a,c-o+r,s-h+l+u+a];case 270:return[c-l-u+r,s-h-o-d+a,c-l+r,s-h-o+a];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[i,n,s,r]=t,a=s-i,o=r-n;switch(this.rotation){case 0:return[i,e-r,a,o];case 90:return[i,e-n,o,a];case 180:return[s,e-n,a,o];case 270:return[s,e-r,o,a];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){this.#D=!0}disableEditMode(){this.#D=!1}isInEditMode(){return this.#D}shouldGetKeyboardEvents(){return!1}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){this.div?.addEventListener("focusin",this.#k),this.div?.addEventListener("focusout",this.#M)}serialize(){(0,s.unreachable)("An editor must be serializable")}static deserialize(t,e,i){const n=new this.prototype.constructor({parent:e,id:e.getNextId(),uiManager:i});n.rotation=t.rotation;const[s,r]=n.pageDimensions,[a,o,l,c]=n.getRectInCurrentCoords(t.rect,r);return n.x=a/s,n.y=o/r,n.width=l/s,n.height=c/r,n}remove(){this.div.removeEventListener("focusin",this.#k),this.div.removeEventListener("focusout",this.#M),this.isEmpty()||this.commit(),this.parent?this.parent.remove(this):this._uiManager.removeEditor(this),this.#E?.remove(),this.#E=null,this.#w=null}get isResizable(){return!1}makeResizable(){this.isResizable&&(this.#U(),this.#P.classList.remove("hidden"))}select(){this.makeResizable(),this.div?.classList.add("selectedEditor")}unselect(){this.#P?.classList.add("hidden"),this.div?.classList.remove("selectedEditor"),this.div?.contains(document.activeElement)&&this._uiManager.currentLayer.div.focus()}updateParams(t,e){}disableEditing(){this.#E&&(this.#E.hidden=!0)}enableEditing(){this.#E&&(this.#E.hidden=!1)}enterInEditMode(){}get contentDiv(){return this.div}get isEditing(){return this.#F}set isEditing(t){this.#F=t,this.parent&&(t?(this.parent.setSelected(this),this.parent.setActiveEditor(this)):this.parent.setActiveEditor(null))}setAspectRatio(t,e){this.#T=!0;const i=t/e,{style:n}=this.div;n.aspectRatio=i,n.height="auto"}static get MIN_SIZE(){return 16}}e.AnnotationEditor=a;class o extends a{constructor(t){super(t),this.annotationElementId=t.annotationElementId,this.deleted=!0}serialize(){return{id:this.annotationElementId,deleted:!0,pageIndex:this.pageIndex}}}},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.KeyboardManager=e.CommandManager=e.ColorManager=e.AnnotationEditorUIManager=void 0,e.bindEvents=r,e.opacityToHex=a,i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123),i(2),i(89),i(125),i(136),i(138),i(141),i(143),i(145),i(147),i(166);var n=i(1),s=i(168);function r(t,e,i){for(const n of i)e.addEventListener(n,t[n].bind(t))}function a(t){return Math.round(Math.min(255,Math.max(1,255*t))).toString(16).padStart(2,"0")}class o{#G=0;getId(){return`${n.AnnotationEditorPrefix}${this.#G++}`}}class l{#V=(0,n.getUuid)();#G=0;#$=null;static get _isSVGFittingCanvas(){const t='data:image/svg+xml;charset=UTF-8,<svg viewBox="0 0 1 1" width="1" height="1" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" style="fill:red;"/></svg>',e=new OffscreenCanvas(1,3),i=e.getContext("2d"),s=new Image;s.src=t;const r=s.decode().then((()=>(i.drawImage(s,0,0,1,1,0,0,1,3),0===new Uint32Array(i.getImageData(0,0,1,1).data.buffer)[0])));return(0,n.shadow)(this,"_isSVGFittingCanvas",r)}async#X(t,e){this.#$||=new Map;let i=this.#$.get(t);if(null===i)return null;if(i?.bitmap)return i.refCounter+=1,i;try{let t;if(i||={bitmap:null,id:`image_${this.#V}_${this.#G++}`,refCounter:0,isSvg:!1},"string"===typeof e){i.url=e;const n=await fetch(e);if(!n.ok)throw new Error(n.statusText);t=await n.blob()}else t=i.file=e;if("image/svg+xml"===t.type){const e=l._isSVGFittingCanvas,n=new FileReader,s=new Image,r=new Promise(((t,r)=>{s.onload=()=>{i.bitmap=s,i.isSvg=!0,t()},n.onload=async()=>{const t=i.svgUrl=n.result;s.src=await e?`${t}#svgView(preserveAspectRatio(none))`:t},s.onerror=n.onerror=r}));n.readAsDataURL(t),await r}else i.bitmap=await createImageBitmap(t);i.refCounter=1}catch(n){console.error(n),i=null}return this.#$.set(t,i),i&&this.#$.set(i.id,i),i}async getFromFile(t){const{lastModified:e,name:i,size:n,type:s}=t;return this.#X(`${e}_${i}_${n}_${s}`,t)}async getFromUrl(t){return this.#X(t,t)}async getFromId(t){this.#$||=new Map;const e=this.#$.get(t);return e?e.bitmap?(e.refCounter+=1,e):e.file?this.getFromFile(e.file):this.getFromUrl(e.url):null}getSvgUrl(t){const e=this.#$.get(t);return e?.isSvg?e.svgUrl:null}deleteId(t){this.#$||=new Map;const e=this.#$.get(t);e&&(e.refCounter-=1,0===e.refCounter&&(e.bitmap=null))}isValidId(t){return t.startsWith(`image_${this.#V}_`)}}class c{#K=[];#Y=!1;#J;#Q=-1;constructor(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:128;this.#J=t}add(t){let{cmd:e,undo:i,mustExec:n,type:s=NaN,overwriteIfSameType:r=!1,keepUndo:a=!1}=t;if(n&&e(),this.#Y)return;const o={cmd:e,undo:i,type:s};if(-1===this.#Q)return this.#K.length>0&&(this.#K.length=0),this.#Q=0,void this.#K.push(o);if(r&&this.#K[this.#Q].type===s)return a&&(o.undo=this.#K[this.#Q].undo),void(this.#K[this.#Q]=o);const l=this.#Q+1;l===this.#J?this.#K.splice(0,1):(this.#Q=l,l<this.#K.length&&this.#K.splice(l)),this.#K.push(o)}undo(){-1!==this.#Q&&(this.#Y=!0,this.#K[this.#Q].undo(),this.#Y=!1,this.#Q-=1)}redo(){this.#Q<this.#K.length-1&&(this.#Q+=1,this.#Y=!0,this.#K[this.#Q].cmd(),this.#Y=!1)}hasSomethingToUndo(){return-1!==this.#Q}hasSomethingToRedo(){return this.#Q<this.#K.length-1}destroy(){this.#K=null}}e.CommandManager=c;class h{constructor(t){this.buffer=[],this.callbacks=new Map,this.allKeys=new Set;const{isMac:e}=n.FeatureTest.platform;for(const[i,n,s={}]of t)for(const t of i){const i=t.startsWith("mac+");e&&i?(this.callbacks.set(t.slice(4),{callback:n,options:s}),this.allKeys.add(t.split("+").at(-1))):e||i||(this.callbacks.set(t,{callback:n,options:s}),this.allKeys.add(t.split("+").at(-1)))}}#Z(t){t.altKey&&this.buffer.push("alt"),t.ctrlKey&&this.buffer.push("ctrl"),t.metaKey&&this.buffer.push("meta"),t.shiftKey&&this.buffer.push("shift"),this.buffer.push(t.key);const e=this.buffer.join("+");return this.buffer.length=0,e}exec(t,e){if(!this.allKeys.has(e.key))return;const i=this.callbacks.get(this.#Z(e));if(!i)return;const{callback:n,options:{bubbles:s=!1,args:r=[],checker:a=null}}=i;a&&!a(t,e)||(n.bind(t,...r)(),s||(e.stopPropagation(),e.preventDefault()))}}e.KeyboardManager=h;class d{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);return(0,s.getColorValues)(t),(0,n.shadow)(this,"_colors",t)}convert(t){const e=(0,s.getRGB)(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[i,n]of this._colors)if(n.every(((t,i)=>t===e[i])))return d._colorsMapping.get(i);return e}getHexCode(t){const e=this._colors.get(t);return e?n.Util.makeHexColor(...e):t}}e.ColorManager=d;class u{#tt=null;#et=new Map;#it=new Map;#nt=null;#st=null;#rt=new c;#at=0;#ot=new Set;#lt=null;#ct=null;#ht=new Set;#dt=null;#ut=new o;#pt=!1;#gt=!1;#ft=null;#mt=n.AnnotationEditorType.NONE;#bt=new Set;#vt=null;#_t=this.blur.bind(this);#yt=this.focus.bind(this);#At=this.copy.bind(this);#St=this.cut.bind(this);#xt=this.paste.bind(this);#Et=this.keydown.bind(this);#wt=this.onEditingAction.bind(this);#Ct=this.onPageChanging.bind(this);#Tt=this.onScaleChanging.bind(this);#Pt=this.onRotationChanging.bind(this);#kt={isEditing:!1,isEmpty:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1};#Mt=[0,0];#Rt=null;#Ft=null;#Dt=null;static TRANSLATE_SMALL=1;static TRANSLATE_BIG=10;static get _keyboardManager(){const t=u.prototype,e=t=>{const{activeElement:e}=document;return e&&t.#Ft.contains(e)&&t.hasSomethingToControl()},i=this.TRANSLATE_SMALL,s=this.TRANSLATE_BIG;return(0,n.shadow)(this,"_keyboardManager",new h([[["ctrl+a","mac+meta+a"],t.selectAll],[["ctrl+z","mac+meta+z"],t.undo],[["ctrl+y","ctrl+shift+z","mac+meta+shift+z","ctrl+shift+Z","mac+meta+shift+Z"],t.redo],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete","mac+Delete"],t.delete],[["Escape","mac+Escape"],t.unselectAll],[["ArrowLeft","mac+ArrowLeft"],t.translateSelectedEditors,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t.translateSelectedEditors,{args:[-s,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t.translateSelectedEditors,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t.translateSelectedEditors,{args:[s,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t.translateSelectedEditors,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t.translateSelectedEditors,{args:[0,-s],checker:e}],[["ArrowDown","mac+ArrowDown"],t.translateSelectedEditors,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t.translateSelectedEditors,{args:[0,s],checker:e}]]))}constructor(t,e,i,n,r,a){this.#Ft=t,this.#Dt=e,this.#nt=i,this._eventBus=n,this._eventBus._on("editingaction",this.#wt),this._eventBus._on("pagechanging",this.#Ct),this._eventBus._on("scalechanging",this.#Tt),this._eventBus._on("rotationchanging",this.#Pt),this.#st=r.annotationStorage,this.#dt=r.filterFactory,this.#vt=a,this.viewParameters={realScale:s.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:0}}destroy(){this.#It(),this.#Ot(),this._eventBus._off("editingaction",this.#wt),this._eventBus._off("pagechanging",this.#Ct),this._eventBus._off("scalechanging",this.#Tt),this._eventBus._off("rotationchanging",this.#Pt);for(const t of this.#it.values())t.destroy();this.#it.clear(),this.#et.clear(),this.#ht.clear(),this.#tt=null,this.#bt.clear(),this.#rt.destroy(),this.#nt.destroy()}get hcmFilter(){return(0,n.shadow)(this,"hcmFilter",this.#vt?this.#dt.addHCMFilter(this.#vt.foreground,this.#vt.background):"none")}get direction(){return(0,n.shadow)(this,"direction",getComputedStyle(this.#Ft).direction)}editAltText(t){this.#nt?.editAltText(this,t)}onPageChanging(t){let{pageNumber:e}=t;this.#at=e-1}focusMainContainer(){this.#Ft.focus()}findParent(t,e){for(const i of this.#it.values()){const{x:n,y:s,width:r,height:a}=i.div.getBoundingClientRect();if(t>=n&&t<=n+r&&e>=s&&e<=s+a)return i}return null}disableUserSelect(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];this.#Dt.classList.toggle("noUserSelect",t)}addShouldRescale(t){this.#ht.add(t)}removeShouldRescale(t){this.#ht.delete(t)}onScaleChanging(t){let{scale:e}=t;this.commitOrRemove(),this.viewParameters.realScale=e*s.PixelsPerInch.PDF_TO_CSS_UNITS;for(const i of this.#ht)i.onScaleChanging()}onRotationChanging(t){let{pagesRotation:e}=t;this.commitOrRemove(),this.viewParameters.rotation=e}addToAnnotationStorage(t){t.isEmpty()||!this.#st||this.#st.has(t.id)||this.#st.setValue(t.id,t)}#Lt(){window.addEventListener("focus",this.#yt),window.addEventListener("blur",this.#_t)}#Ot(){window.removeEventListener("focus",this.#yt),window.removeEventListener("blur",this.#_t)}blur(){if(!this.hasSelection)return;const{activeElement:t}=document;for(const e of this.#bt)if(e.div.contains(t)){this.#ft=[e,t],e._focusEventsAllowed=!1;break}}focus(){if(!this.#ft)return;const[t,e]=this.#ft;this.#ft=null,e.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0}),e.focus()}#Nt(){window.addEventListener("keydown",this.#Et,{capture:!0})}#It(){window.removeEventListener("keydown",this.#Et,{capture:!0})}#jt(){document.addEventListener("copy",this.#At),document.addEventListener("cut",this.#St),document.addEventListener("paste",this.#xt)}#Bt(){document.removeEventListener("copy",this.#At),document.removeEventListener("cut",this.#St),document.removeEventListener("paste",this.#xt)}addEditListeners(){this.#Nt(),this.#jt()}removeEditListeners(){this.#It(),this.#Bt()}copy(t){if(t.preventDefault(),this.#tt?.commitOrRemove(),!this.hasSelection)return;const e=[];for(const i of this.#bt){const t=i.serialize(!0);t&&e.push(t)}0!==e.length&&t.clipboardData.setData("application/pdfjs",JSON.stringify(e))}cut(t){this.copy(t),this.delete()}paste(t){t.preventDefault();const{clipboardData:e}=t;for(const n of e.items)for(const t of this.#ct)if(t.isHandlingMimeForPasting(n.type))return void t.paste(n,this.currentLayer);let i=e.getData("application/pdfjs");if(!i)return;try{i=JSON.parse(i)}catch(r){return void(0,n.warn)(`paste: "${r.message}".`)}if(!Array.isArray(i))return;this.unselectAll();const s=this.currentLayer;try{const t=[];for(const r of i){const e=s.deserialize(r);if(!e)return;t.push(e)}const e=()=>{for(const e of t)this.#Ut(e);this.#zt(t)},n=()=>{for(const e of t)e.remove()};this.addCommands({cmd:e,undo:n,mustExec:!0})}catch(r){(0,n.warn)(`paste: "${r.message}".`)}}keydown(t){this.getActive()?.shouldGetKeyboardEvents()||u._keyboardManager.exec(this,t)}onEditingAction(t){["undo","redo","delete","selectAll"].includes(t.name)&&this[t.name]()}#Ht(t){const e=Object.entries(t).some((t=>{let[e,i]=t;return this.#kt[e]!==i}));e&&this._eventBus.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#kt,t)})}#Wt(t){this._eventBus.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){t?(this.#Lt(),this.#Nt(),this.#jt(),this.#Ht({isEditing:this.#mt!==n.AnnotationEditorType.NONE,isEmpty:this.#qt(),hasSomethingToUndo:this.#rt.hasSomethingToUndo(),hasSomethingToRedo:this.#rt.hasSomethingToRedo(),hasSelectedEditor:!1})):(this.#Ot(),this.#It(),this.#Bt(),this.#Ht({isEditing:!1}),this.disableUserSelect(!1))}registerEditorTypes(t){if(!this.#ct){this.#ct=t;for(const t of this.#ct)this.#Wt(t.defaultPropertiesToUpdate)}}getId(){return this.#ut.getId()}get currentLayer(){return this.#it.get(this.#at)}getLayer(t){return this.#it.get(t)}get currentPageIndex(){return this.#at}addLayer(t){this.#it.set(t.pageIndex,t),this.#pt?t.enable():t.disable()}removeLayer(t){this.#it.delete(t.pageIndex)}updateMode(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.#mt!==t){if(this.#mt=t,t===n.AnnotationEditorType.NONE)return this.setEditingState(!1),void this.#Gt();this.setEditingState(!0),this.#Vt(),this.unselectAll();for(const e of this.#it.values())e.updateMode(t);if(e)for(const t of this.#et.values())if(t.annotationElementId===e){this.setSelected(t),t.enterInEditMode();break}}}updateToolbar(t){t!==this.#mt&&this._eventBus.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){if(this.#ct)if(t!==n.AnnotationEditorParamsType.CREATE){for(const i of this.#bt)i.updateParams(t,e);for(const i of this.#ct)i.updateDefaultParams(t,e)}else this.currentLayer.addNewEditor(t)}enableWaiting(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.#gt!==t){this.#gt=t;for(const e of this.#it.values())t?e.disableClick():e.enableClick(),e.div.classList.toggle("waiting",t)}}#Vt(){if(!this.#pt){this.#pt=!0;for(const t of this.#it.values())t.enable()}}#Gt(){if(this.unselectAll(),this.#pt){this.#pt=!1;for(const t of this.#it.values())t.disable()}}getEditors(t){const e=[];for(const i of this.#et.values())i.pageIndex===t&&e.push(i);return e}getEditor(t){return this.#et.get(t)}addEditor(t){this.#et.set(t.id,t)}removeEditor(t){this.#et.delete(t.id),this.unselect(t),t.annotationElementId&&this.#ot.has(t.annotationElementId)||this.#st?.remove(t.id)}addDeletedAnnotationElement(t){this.#ot.add(t.annotationElementId),t.deleted=!0}isDeletedAnnotationElement(t){return this.#ot.has(t)}removeDeletedAnnotationElement(t){this.#ot.delete(t.annotationElementId),t.deleted=!1}#Ut(t){const e=this.#it.get(t.pageIndex);e?e.addOrRebuild(t):this.addEditor(t)}setActiveEditor(t){this.#tt!==t&&(this.#tt=t,t&&this.#Wt(t.propertiesToUpdate))}toggleSelected(t){if(this.#bt.has(t))return this.#bt.delete(t),t.unselect(),void this.#Ht({hasSelectedEditor:this.hasSelection});this.#bt.add(t),t.select(),this.#Wt(t.propertiesToUpdate),this.#Ht({hasSelectedEditor:!0})}setSelected(t){for(const e of this.#bt)e!==t&&e.unselect();this.#bt.clear(),this.#bt.add(t),t.select(),this.#Wt(t.propertiesToUpdate),this.#Ht({hasSelectedEditor:!0})}isSelected(t){return this.#bt.has(t)}unselect(t){t.unselect(),this.#bt.delete(t),this.#Ht({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#bt.size}undo(){this.#rt.undo(),this.#Ht({hasSomethingToUndo:this.#rt.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#qt()})}redo(){this.#rt.redo(),this.#Ht({hasSomethingToUndo:!0,hasSomethingToRedo:this.#rt.hasSomethingToRedo(),isEmpty:this.#qt()})}addCommands(t){this.#rt.add(t),this.#Ht({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#qt()})}#qt(){if(0===this.#et.size)return!0;if(1===this.#et.size)for(const t of this.#et.values())return t.isEmpty();return!1}delete(){if(this.commitOrRemove(),!this.hasSelection)return;const t=[...this.#bt],e=()=>{for(const e of t)e.remove()},i=()=>{for(const e of t)this.#Ut(e)};this.addCommands({cmd:e,undo:i,mustExec:!0})}commitOrRemove(){this.#tt?.commitOrRemove()}hasSomethingToControl(){return this.#tt||this.hasSelection}#zt(t){this.#bt.clear();for(const e of t)e.isEmpty()||(this.#bt.add(e),e.select());this.#Ht({hasSelectedEditor:!0})}selectAll(){for(const t of this.#bt)t.commit();this.#zt(this.#et.values())}unselectAll(){if(this.#tt)this.#tt.commitOrRemove();else if(this.hasSelection){for(const t of this.#bt)t.unselect();this.#bt.clear(),this.#Ht({hasSelectedEditor:!1})}}translateSelectedEditors(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(i||this.commitOrRemove(),!this.hasSelection)return;this.#Mt[0]+=t,this.#Mt[1]+=e;const[n,s]=this.#Mt,r=[...this.#bt],a=1e3;this.#Rt&&clearTimeout(this.#Rt),this.#Rt=setTimeout((()=>{this.#Rt=null,this.#Mt[0]=this.#Mt[1]=0,this.addCommands({cmd:()=>{for(const t of r)this.#et.has(t.id)&&t.translateInPage(n,s)},undo:()=>{for(const t of r)this.#et.has(t.id)&&t.translateInPage(-n,-s)},mustExec:!1})}),a);for(const o of r)o.translateInPage(t,e)}setUpDragSession(){if(this.hasSelection){this.disableUserSelect(!0),this.#lt=new Map;for(const t of this.#bt)this.#lt.set(t,{savedX:t.x,savedY:t.y,savedPageIndex:t.pageIndex,newX:0,newY:0,newPageIndex:-1})}}endDragSession(){if(!this.#lt)return!1;this.disableUserSelect(!1);const t=this.#lt;this.#lt=null;let e=!1;for(const[{x:n,y:s,pageIndex:r},a]of t)a.newX=n,a.newY=s,a.newPageIndex=r,e||=n!==a.savedX||s!==a.savedY||r!==a.savedPageIndex;if(!e)return!1;const i=(t,e,i,n)=>{if(this.#et.has(t.id)){const s=this.#it.get(n);s?t._setParentAndPosition(s,e,i):(t.pageIndex=n,t.x=e,t.y=i)}};return this.addCommands({cmd:()=>{for(const[e,{newX:n,newY:s,newPageIndex:r}]of t)i(e,n,s,r)},undo:()=>{for(const[e,{savedX:n,savedY:s,savedPageIndex:r}]of t)i(e,n,s,r)},mustExec:!0}),!0}dragSelectedEditors(t,e){if(this.#lt)for(const i of this.#lt.keys())i.drag(t,e)}rebuild(t){if(null===t.parent){const e=this.getLayer(t.pageIndex);e?(e.changeParent(t),e.addOrRebuild(t)):(this.addEditor(t),this.addToAnnotationStorage(t),t.rebuild())}else t.parent.addOrRebuild(t)}isActive(t){return this.#tt===t}getActive(){return this.#tt}getMode(){return this.#mt}get imageManager(){return(0,n.shadow)(this,"imageManager",new l)}}e.AnnotationEditorUIManager=u},(t,e,i)=>{var n=i(3),s=i(6),r=i(4),a=i(24),o=i(14),l=i(8),c=i(21),h=i(20),d=i(91),u=i(39),p=i(77),g=i(64),f=i(159),m=i(7),b=i(167),v=i(27),_=r.JSON,y=r.Number,A=r.SyntaxError,S=_&&_.parse,x=a("Object","keys"),E=Object.getOwnPropertyDescriptor,w=o("".charAt),C=o("".slice),T=o(/./.exec),P=o([].push),k=/^\d$/,M=/^[1-9]$/,R=/^(?:-|\d)$/,F=/^[\t\n\r ]$/,D=0,I=1,O=function(t,e){t=p(t);var i=new B(t,0,""),n=i.parse(),s=n.value,r=i.skip(F,n.end);if(r<t.length)throw A('Unexpected extra character: "'+w(t,r)+'" after the parsed data at: '+r);return c(e)?L({"":s},"",e,n):s},L=function(t,e,i,n){var s,r,a,o,c,p=t[e],f=n&&p===n.value,m=f&&"string"==typeof n.source?{source:n.source}:{};if(h(p)){var b=d(p),v=f?n.nodes:b?[]:{};if(b)for(s=v.length,a=g(p),o=0;o<a;o++)N(p,o,L(p,""+o,i,o<s?v[o]:void 0));else for(r=x(p),a=g(r),o=0;o<a;o++)c=r[o],N(p,c,L(p,c,i,u(v,c)?v[c]:void 0))}return l(i,t,e,p,m)},N=function(t,e,i){if(s){var n=E(t,e);if(n&&!n.configurable)return}void 0===i?delete t[e]:f(t,e,i)},j=function(t,e,i,n){this.value=t,this.end=e,this.source=i,this.nodes=n},B=function(t,e){this.source=t,this.index=e};B.prototype={fork:function(t){return new B(this.source,t)},parse:function(){var t=this.source,e=this.skip(F,this.index),i=this.fork(e),n=w(t,e);if(T(R,n))return i.number();switch(n){case"{":return i.object();case"[":return i.array();case'"':return i.string();case"t":return i.keyword(!0);case"f":return i.keyword(!1);case"n":return i.keyword(null)}throw A('Unexpected character: "'+n+'" at: '+e)},node:function(t,e,i,n,s){return new j(e,n,t?null:C(this.source,i,n),s)},object:function(){var t=this.source,e=this.index+1,i=!1,n={},s={};while(e<t.length){if(e=this.until(['"',"}"],e),"}"===w(t,e)&&!i){e++;break}var r=this.fork(e).string(),a=r.value;e=r.end,e=this.until([":"],e)+1,e=this.skip(F,e),r=this.fork(e).parse(),f(s,a,r),f(n,a,r.value),e=this.until([",","}"],r.end);var o=w(t,e);if(","===o)i=!0,e++;else if("}"===o){e++;break}}return this.node(I,n,this.index,e,s)},array:function(){var t=this.source,e=this.index+1,i=!1,n=[],s=[];while(e<t.length){if(e=this.skip(F,e),"]"===w(t,e)&&!i){e++;break}var r=this.fork(e).parse();if(P(s,r),P(n,r.value),e=this.until([",","]"],r.end),","===w(t,e))i=!0,e++;else if("]"===w(t,e)){e++;break}}return this.node(I,n,this.index,e,s)},string:function(){var t=this.index,e=b(this.source,this.index+1);return this.node(D,e.value,t,e.end)},number:function(){var t=this.source,e=this.index,i=e;if("-"===w(t,i)&&i++,"0"===w(t,i))i++;else{if(!T(M,w(t,i)))throw A("Failed to parse number at: "+i);i=this.skip(k,++i)}if("."===w(t,i)&&(i=this.skip(k,++i)),"e"===w(t,i)||"E"===w(t,i)){i++,"+"!==w(t,i)&&"-"!==w(t,i)||i++;var n=i;if(i=this.skip(k,i),n===i)throw A("Failed to parse number's exponent value at: "+i)}return this.node(D,y(C(t,e,i)),e,i)},keyword:function(t){var e=""+t,i=this.index,n=i+e.length;if(C(this.source,i,n)!==e)throw A("Failed to parse value at: "+i);return this.node(D,t,i,n)},skip:function(t,e){for(var i=this.source;e<i.length;e++)if(!T(t,w(i,e)))break;return e},until:function(t,e){e=this.skip(F,e);for(var i=w(this.source,e),n=0;n<t.length;n++)if(t[n]===i)return e;throw A('Unexpected character: "'+i+'" at: '+e)}};var U=m((function(){var t,e="9007199254740993";return S(e,(function(e,i,n){t=n.source})),t!==e})),z=v&&!m((function(){return 1/S("-0 \t")!==-1/0}));n({target:"JSON",stat:!0,forced:U},{parse:function(t,e){return z&&!c(e)?S(t):O(t,e)}})},(t,e,i)=>{var n=i(14),s=i(39),r=SyntaxError,a=parseInt,o=String.fromCharCode,l=n("".charAt),c=n("".slice),h=n(/./.exec),d={'\\"':'"',"\\\\":"\\","\\/":"/","\\b":"\b","\\f":"\f","\\n":"\n","\\r":"\r","\\t":"\t"},u=/^[\da-f]{4}$/i,p=/^[\u0000-\u001F]$/;t.exports=function(t,e){var i=!0,n="";while(e<t.length){var g=l(t,e);if("\\"===g){var f=c(t,e,e+2);if(s(d,f))n+=d[f],e+=2;else{if("\\u"!==f)throw r('Unknown escape sequence: "'+f+'"');e+=2;var m=c(t,e,e+4);if(!h(u,m))throw r("Bad Unicode escape at: "+e);n+=o(a(m,16)),e+=4}}else{if('"'===g){i=!1,e++;break}if(h(p,g))throw r("Bad control character in string literal at: "+e);n+=g,e++}}if(i)throw r("Unterminated string at: "+e);return{value:n,end:e}}},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.StatTimer=e.RenderingCancelledException=e.PixelsPerInch=e.PageViewport=e.PDFDateString=e.DOMStandardFontDataFactory=e.DOMSVGFactory=e.DOMFilterFactory=e.DOMCanvasFactory=e.DOMCMapReaderFactory=void 0,e.deprecated=x,e.getColorValues=P,e.getCurrentTransform=k,e.getCurrentTransformInverse=M,e.getFilenameFromUrl=b,e.getPdfFilenameFromUrl=v,e.getRGB=T,e.getXfaPageViewport=C,e.isDataScheme=f,e.isPdfFile=m,e.isValidFetchUrl=y,e.loadScript=S,e.noContextMenu=A,e.setLayerDimensions=R,i(2),i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123),i(89),i(84),i(86),i(87);var n=i(169),s=i(1);const r="http://www.w3.org/2000/svg";class a{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}e.PixelsPerInch=a;class o extends n.BaseFilterFactory{#$t;#Xt;#e;#Kt;#Yt;#Jt;#Qt;#Zt;#te;#ee;#G=0;constructor(){let{docId:t,ownerDocument:e=globalThis.document}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super(),this.#e=t,this.#Kt=e}get#$(){return this.#$t||=new Map}get#ie(){if(!this.#Xt){const t=this.#Kt.createElement("div"),{style:e}=t;e.visibility="hidden",e.contain="strict",e.width=e.height=0,e.position="absolute",e.top=e.left=0,e.zIndex=-1;const i=this.#Kt.createElementNS(r,"svg");i.setAttribute("width",0),i.setAttribute("height",0),this.#Xt=this.#Kt.createElementNS(r,"defs"),t.append(i),i.append(this.#Xt),this.#Kt.body.append(t)}return this.#Xt}addFilter(t){if(!t)return"none";let e,i,n,s,r=this.#$.get(t);if(r)return r;if(1===t.length){const r=t[0],a=new Array(256);for(let t=0;t<256;t++)a[t]=r[t]/255;s=e=i=n=a.join(",")}else{const[r,a,o]=t,l=new Array(256),c=new Array(256),h=new Array(256);for(let t=0;t<256;t++)l[t]=r[t]/255,c[t]=a[t]/255,h[t]=o[t]/255;e=l.join(","),i=c.join(","),n=h.join(","),s=`${e}${i}${n}`}if(r=this.#$.get(s),r)return this.#$.set(t,r),r;const a=`g_${this.#e}_transfer_map_${this.#G++}`,o=`url(#${a})`;this.#$.set(t,o),this.#$.set(s,o);const l=this.#ne(a);return this.#se(e,i,n,l),o}addHCMFilter(t,e){const i=`${t}-${e}`;if(this.#Jt===i)return this.#Qt;if(this.#Jt=i,this.#Qt="none",this.#Yt?.remove(),!t||!e)return this.#Qt;const n=this.#re(t);t=s.Util.makeHexColor(...n);const r=this.#re(e);if(e=s.Util.makeHexColor(...r),this.#ie.style.color="","#000000"===t&&"#ffffff"===e||t===e)return this.#Qt;const a=new Array(256);for(let s=0;s<=255;s++){const t=s/255;a[s]=t<=.03928?t/12.92:((t+.055)/1.055)**2.4}const o=a.join(","),l=`g_${this.#e}_hcm_filter`,c=this.#Zt=this.#ne(l);this.#se(o,o,o,c),this.#ae(c);const h=(t,e)=>{const i=n[t]/255,s=r[t]/255,a=new Array(e+1);for(let n=0;n<=e;n++)a[n]=i+n/e*(s-i);return a.join(",")};return this.#se(h(0,5),h(1,5),h(2,5),c),this.#Qt=`url(#${l})`,this.#Qt}addHighlightHCMFilter(t,e,i,n){const s=`${t}-${e}-${i}-${n}`;if(this.#te===s)return this.#ee;if(this.#te=s,this.#ee="none",this.#Zt?.remove(),!t||!e)return this.#ee;const[r,a]=[t,e].map(this.#re.bind(this));let o=Math.round(.2126*r[0]+.7152*r[1]+.0722*r[2]),l=Math.round(.2126*a[0]+.7152*a[1]+.0722*a[2]),[c,h]=[i,n].map(this.#re.bind(this));l<o&&([o,l,c,h]=[l,o,h,c]),this.#ie.style.color="";const d=(t,e,i)=>{const n=new Array(256),s=(l-o)/i,r=t/255,a=(e-t)/(255*i);let c=0;for(let l=0;l<=i;l++){const t=Math.round(o+l*s),e=r+l*a;for(let i=c;i<=t;i++)n[i]=e;c=t+1}for(let o=c;o<256;o++)n[o]=n[c-1];return n.join(",")},u=`g_${this.#e}_hcm_highlight_filter`,p=this.#Zt=this.#ne(u);return this.#ae(p),this.#se(d(c[0],h[0],5),d(c[1],h[1],5),d(c[2],h[2],5),p),this.#ee=`url(#${u})`,this.#ee}destroy(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];t&&(this.#Qt||this.#ee)||(this.#Xt&&(this.#Xt.parentNode.parentNode.remove(),this.#Xt=null),this.#$t&&(this.#$t.clear(),this.#$t=null),this.#G=0)}#ae(t){const e=this.#Kt.createElementNS(r,"feColorMatrix");e.setAttribute("type","matrix"),e.setAttribute("values","0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0.2126 0.7152 0.0722 0 0 0 0 0 1 0"),t.append(e)}#ne(t){const e=this.#Kt.createElementNS(r,"filter");return e.setAttribute("color-interpolation-filters","sRGB"),e.setAttribute("id",t),this.#ie.append(e),e}#oe(t,e,i){const n=this.#Kt.createElementNS(r,e);n.setAttribute("type","discrete"),n.setAttribute("tableValues",i),t.append(n)}#se(t,e,i,n){const s=this.#Kt.createElementNS(r,"feComponentTransfer");n.append(s),this.#oe(s,"feFuncR",t),this.#oe(s,"feFuncG",e),this.#oe(s,"feFuncB",i)}#re(t){return this.#ie.style.color=t,T(getComputedStyle(this.#ie).getPropertyValue("color"))}}e.DOMFilterFactory=o;class l extends n.BaseCanvasFactory{constructor(){let{ownerDocument:t=globalThis.document}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};super(),this._document=t}_createCanvas(t,e){const i=this._document.createElement("canvas");return i.width=t,i.height=e,i}}async function c(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(y(t,document.baseURI)){const i=await fetch(t);if(!i.ok)throw new Error(i.statusText);return e?new Uint8Array(await i.arrayBuffer()):(0,s.stringToBytes)(await i.text())}return new Promise(((i,n)=>{const r=new XMLHttpRequest;r.open("GET",t,!0),e&&(r.responseType="arraybuffer"),r.onreadystatechange=()=>{if(r.readyState===XMLHttpRequest.DONE){if(200===r.status||0===r.status){let t;if(e&&r.response?t=new Uint8Array(r.response):!e&&r.responseText&&(t=(0,s.stringToBytes)(r.responseText)),t)return void i(t)}n(new Error(r.statusText))}},r.send(null)}))}e.DOMCanvasFactory=l;class h extends n.BaseCMapReaderFactory{_fetchData(t,e){return c(t,this.isCompressed).then((t=>({cMapData:t,compressionType:e})))}}e.DOMCMapReaderFactory=h;class d extends n.BaseStandardFontDataFactory{_fetchData(t){return c(t,!0)}}e.DOMStandardFontDataFactory=d;class u extends n.BaseSVGFactory{_createSVG(t){return document.createElementNS(r,t)}}e.DOMSVGFactory=u;class p{constructor(t){let{viewBox:e,scale:i,rotation:n,offsetX:s=0,offsetY:r=0,dontFlip:a=!1}=t;this.viewBox=e,this.scale=i,this.rotation=n,this.offsetX=s,this.offsetY=r;const o=(e[2]+e[0])/2,l=(e[3]+e[1])/2;let c,h,d,u,p,g,f,m;switch(n%=360,n<0&&(n+=360),n){case 180:c=-1,h=0,d=0,u=1;break;case 90:c=0,h=1,d=1,u=0;break;case 270:c=0,h=-1,d=-1,u=0;break;case 0:c=1,h=0,d=0,u=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}a&&(d=-d,u=-u),0===c?(p=Math.abs(l-e[1])*i+s,g=Math.abs(o-e[0])*i+r,f=(e[3]-e[1])*i,m=(e[2]-e[0])*i):(p=Math.abs(o-e[0])*i+s,g=Math.abs(l-e[1])*i+r,f=(e[2]-e[0])*i,m=(e[3]-e[1])*i),this.transform=[c*i,h*i,d*i,u*i,p-c*i*o-d*i*l,g-h*i*o-u*i*l],this.width=f,this.height=m}get rawDims(){const{viewBox:t}=this;return(0,s.shadow)(this,"rawDims",{pageWidth:t[2]-t[0],pageHeight:t[3]-t[1],pageX:t[0],pageY:t[1]})}clone(){let{scale:t=this.scale,rotation:e=this.rotation,offsetX:i=this.offsetX,offsetY:n=this.offsetY,dontFlip:s=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return new p({viewBox:this.viewBox.slice(),scale:t,rotation:e,offsetX:i,offsetY:n,dontFlip:s})}convertToViewportPoint(t,e){return s.Util.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=s.Util.applyTransform([t[0],t[1]],this.transform),i=s.Util.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],i[0],i[1]]}convertToPdfPoint(t,e){return s.Util.applyInverseTransform([t,e],this.transform)}}e.PageViewport=p;class g extends s.BaseException{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;super(t,"RenderingCancelledException"),this.extraDelay=e}}function f(t){const e=t.length;let i=0;while(i<e&&""===t[i].trim())i++;return"data:"===t.substring(i,i+5).toLowerCase()}function m(t){return"string"===typeof t&&/\.pdf$/i.test(t)}function b(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return e||([t]=t.split(/[#?]/,1)),t.substring(t.lastIndexOf("/")+1)}function v(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"document.pdf";if("string"!==typeof t)return e;if(f(t))return(0,s.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.'),e;const i=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/,n=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,r=i.exec(t);let a=n.exec(r[1])||n.exec(r[2])||n.exec(r[3]);if(a&&(a=a[0],a.includes("%")))try{a=n.exec(decodeURIComponent(a))[0]}catch{}return a||e}e.RenderingCancelledException=g;class _{started=Object.create(null);times=[];time(t){t in this.started&&(0,s.warn)(`Timer is already running for ${t}`),this.started[t]=Date.now()}timeEnd(t){t in this.started||(0,s.warn)(`Timer has not been started for ${t}`),this.times.push({name:t,start:this.started[t],end:Date.now()}),delete this.started[t]}toString(){const t=[];let e=0;for(const{name:i}of this.times)e=Math.max(i.length,e);for(const{name:i,start:n,end:s}of this.times)t.push(`${i.padEnd(e)} ${s-n}ms\n`);return t.join("")}}function y(t,e){try{const{protocol:i}=e?new URL(t,e):new URL(t);return"http:"===i||"https:"===i}catch{return!1}}function A(t){t.preventDefault()}function S(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return new Promise(((i,n)=>{const s=document.createElement("script");s.src=t,s.onload=function(t){e&&s.remove(),i(t)},s.onerror=function(){n(new Error(`Cannot load script at: ${s.src}`))},(document.head||document.documentElement).append(s)}))}function x(t){console.log("Deprecated API usage: "+t)}let E;e.StatTimer=_;class w{static toDateObject(t){if(!t||"string"!==typeof t)return null;E||=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?");const e=E.exec(t);if(!e)return null;const i=parseInt(e[1],10);let n=parseInt(e[2],10);n=n>=1&&n<=12?n-1:0;let s=parseInt(e[3],10);s=s>=1&&s<=31?s:1;let r=parseInt(e[4],10);r=r>=0&&r<=23?r:0;let a=parseInt(e[5],10);a=a>=0&&a<=59?a:0;let o=parseInt(e[6],10);o=o>=0&&o<=59?o:0;const l=e[7]||"Z";let c=parseInt(e[8],10);c=c>=0&&c<=23?c:0;let h=parseInt(e[9],10)||0;return h=h>=0&&h<=59?h:0,"-"===l?(r+=c,a+=h):"+"===l&&(r-=c,a-=h),new Date(Date.UTC(i,n,s,r,a,o))}}function C(t,e){let{scale:i=1,rotation:n=0}=e;const{width:s,height:r}=t.attributes.style,a=[0,0,parseInt(s),parseInt(r)];return new p({viewBox:a,scale:i,rotation:n})}function T(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}return t.startsWith("rgb(")?t.slice(4,-1).split(",").map((t=>parseInt(t))):t.startsWith("rgba(")?t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3):((0,s.warn)(`Not a valid color format: "${t}"`),[0,0,0])}function P(t){const e=document.createElement("span");e.style.visibility="hidden",document.body.append(e);for(const i of t.keys()){e.style.color=i;const n=window.getComputedStyle(e).color;t.set(i,T(n))}e.remove()}function k(t){const{a:e,b:i,c:n,d:s,e:r,f:a}=t.getTransform();return[e,i,n,s,r,a]}function M(t){const{a:e,b:i,c:n,d:s,e:r,f:a}=t.getTransform().invertSelf();return[e,i,n,s,r,a]}function R(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=!(arguments.length>3&&void 0!==arguments[3])||arguments[3];if(e instanceof p){const{pageWidth:n,pageHeight:r}=e.rawDims,{style:a}=t,o=s.FeatureTest.isCSSRoundSupported,l=`var(--scale-factor) * ${n}px`,c=`var(--scale-factor) * ${r}px`,h=o?`round(${l}, 1px)`:`calc(${l})`,d=o?`round(${c}, 1px)`:`calc(${c})`;i&&e.rotation%180!==0?(a.width=d,a.height=h):(a.width=h,a.height=d)}n&&t.setAttribute("data-main-rotation",e.rotation)}e.PDFDateString=w},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.BaseStandardFontDataFactory=e.BaseSVGFactory=e.BaseFilterFactory=e.BaseCanvasFactory=e.BaseCMapReaderFactory=void 0,i(2);var n=i(1);class s{constructor(){this.constructor===s&&(0,n.unreachable)("Cannot initialize BaseFilterFactory.")}addFilter(t){return"none"}addHCMFilter(t,e){return"none"}addHighlightHCMFilter(t,e,i,n){return"none"}destroy(){}}e.BaseFilterFactory=s;class r{constructor(){this.constructor===r&&(0,n.unreachable)("Cannot initialize BaseCanvasFactory.")}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const i=this._createCanvas(t,e);return{canvas:i,context:i.getContext("2d")}}reset(t,e,i){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||i<=0)throw new Error("Invalid canvas size");t.canvas.width=e,t.canvas.height=i}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0,t.canvas.height=0,t.canvas=null,t.context=null}_createCanvas(t,e){(0,n.unreachable)("Abstract method `_createCanvas` called.")}}e.BaseCanvasFactory=r;class a{constructor(t){let{baseUrl:e=null,isCompressed:i=!0}=t;this.constructor===a&&(0,n.unreachable)("Cannot initialize BaseCMapReaderFactory."),this.baseUrl=e,this.isCompressed=i}async fetch(t){let{name:e}=t;if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!e)throw new Error("CMap name must be specified.");const i=this.baseUrl+e+(this.isCompressed?".bcmap":""),s=this.isCompressed?n.CMapCompressionType.BINARY:n.CMapCompressionType.NONE;return this._fetchData(i,s).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${i}`)}))}_fetchData(t,e){(0,n.unreachable)("Abstract method `_fetchData` called.")}}e.BaseCMapReaderFactory=a;class o{constructor(t){let{baseUrl:e=null}=t;this.constructor===o&&(0,n.unreachable)("Cannot initialize BaseStandardFontDataFactory."),this.baseUrl=e}async fetch(t){let{filename:e}=t;if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!e)throw new Error("Font filename must be specified.");const i=`${this.baseUrl}${e}`;return this._fetchData(i).catch((t=>{throw new Error(`Unable to load font data at: ${i}`)}))}_fetchData(t){(0,n.unreachable)("Abstract method `_fetchData` called.")}}e.BaseStandardFontDataFactory=o;class l{constructor(){this.constructor===l&&(0,n.unreachable)("Cannot initialize BaseSVGFactory.")}create(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const n=this._createSVG("svg:svg");return n.setAttribute("version","1.1"),i||(n.setAttribute("width",`${t}px`),n.setAttribute("height",`${e}px`)),n.setAttribute("preserveAspectRatio","none"),n.setAttribute("viewBox",`0 0 ${t} ${e}`),n}createElement(t){if("string"!==typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){(0,n.unreachable)("Abstract method `_createSVG` called.")}}e.BaseSVGFactory=l},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MurmurHash3_64=void 0,i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123),i(2);var n=i(1);const s=3285377520,r=4294901760,a=65535;class o{constructor(t){this.h1=t?4294967295&t:s,this.h2=t?4294967295&t:s}update(t){let e,i;if("string"===typeof t){e=new Uint8Array(2*t.length),i=0;for(let n=0,s=t.length;n<s;n++){const s=t.charCodeAt(n);s<=255?e[i++]=s:(e[i++]=s>>>8,e[i++]=255&s)}}else{if(!(0,n.isArrayBuffer)(t))throw new Error("Wrong data format in MurmurHash3_64_update. Input must be a string or array.");e=t.slice(),i=e.byteLength}const s=i>>2,o=i-4*s,l=new Uint32Array(e.buffer,0,s);let c=0,h=0,d=this.h1,u=this.h2;const p=3432918353,g=461845907,f=p&a,m=g&a;for(let n=0;n<s;n++)1&n?(c=l[n],c=c*p&r|c*f&a,c=c<<15|c>>>17,c=c*g&r|c*m&a,d^=c,d=d<<13|d>>>19,d=5*d+3864292196):(h=l[n],h=h*p&r|h*f&a,h=h<<15|h>>>17,h=h*g&r|h*m&a,u^=h,u=u<<13|u>>>19,u=5*u+3864292196);switch(c=0,o){case 3:c^=e[4*s+2]<<16;case 2:c^=e[4*s+1]<<8;case 1:c^=e[4*s],c=c*p&r|c*f&a,c=c<<15|c>>>17,c=c*g&r|c*m&a,1&s?d^=c:u^=c}this.h1=d,this.h2=u}hexdigest(){let t=this.h1,e=this.h2;return t^=e>>>1,t=3981806797*t&r|36045*t&a,e=4283543511*e&r|(2950163797*(e<<16|t>>>16)&r)>>>16,t^=e>>>1,t=444984403*t&r|60499*t&a,e=3301882366*e&r|(3120437893*(e<<16|t>>>16)&r)>>>16,t^=e>>>1,(t>>>0).toString(16).padStart(8,"0")+(e>>>0).toString(16).padStart(8,"0")}}e.MurmurHash3_64=o},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.FontLoader=e.FontFaceObject=void 0,i(125),i(136),i(138),i(141),i(143),i(145),i(147),i(89),i(149);var n=i(1);class s{#le=new Set;constructor(t){let{ownerDocument:e=globalThis.document,styleElement:i=null}=t;this._document=e,this.nativeFontFaces=new Set,this.styleElement=null,this.loadingRequests=[],this.loadTestFontId=0}addNativeFontFace(t){this.nativeFontFaces.add(t),this._document.fonts.add(t)}removeNativeFontFace(t){this.nativeFontFaces.delete(t),this._document.fonts.delete(t)}insertRule(t){this.styleElement||(this.styleElement=this._document.createElement("style"),this._document.documentElement.getElementsByTagName("head")[0].append(this.styleElement));const e=this.styleElement.sheet;e.insertRule(t,e.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.clear(),this.#le.clear(),this.styleElement&&(this.styleElement.remove(),this.styleElement=null)}async loadSystemFont(t){if(t&&!this.#le.has(t.loadedName))if((0,n.assert)(!this.disableFontFace,"loadSystemFont shouldn't be called when `disableFontFace` is set."),this.isFontLoadingAPISupported){const{loadedName:e,src:i,style:s}=t,r=new FontFace(e,i,s);this.addNativeFontFace(r);try{await r.load(),this.#le.add(e)}catch{(0,n.warn)(`Cannot load system font: ${t.baseFontName}, installing it could help to improve PDF rendering.`),this.removeNativeFontFace(r)}}else(0,n.unreachable)("Not implemented: loadSystemFont without the Font Loading API.")}async bind(t){if(t.attached||t.missingFile&&!t.systemFontInfo)return;if(t.attached=!0,t.systemFontInfo)return void await this.loadSystemFont(t.systemFontInfo);if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(i){throw(0,n.warn)(`Failed to load font '${e.family}': '${i}'.`),t.disableFontFace=!0,i}}return}const e=t.createFontFaceRule();if(e){if(this.insertRule(e),this.isSyncFontLoadingSupported)return;await new Promise((e=>{const i=this._queueLoadingCallback(e);this._prepareFontLoadEvent(t,i)}))}}get isFontLoadingAPISupported(){const t=!!this._document?.fonts;return(0,n.shadow)(this,"isFontLoadingAPISupported",t)}get isSyncFontLoadingSupported(){let t=!1;return(n.isNodeJS||"undefined"!==typeof navigator&&/Mozilla\/5.0.*?rv:\d+.*? Gecko/.test(navigator.userAgent))&&(t=!0),(0,n.shadow)(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){function e(){(0,n.assert)(!s.done,"completeRequest() cannot be called twice."),s.done=!0;while(i.length>0&&i[0].done){const t=i.shift();setTimeout(t.callback,0)}}const{loadingRequests:i}=this,s={done:!1,complete:e,callback:t};return i.push(s),s}get _loadTestFont(){const t=atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==");return(0,n.shadow)(this,"_loadTestFont",t)}_prepareFontLoadEvent(t,e){function i(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function s(t,e,i,n){const s=t.substring(0,e),r=t.substring(e+i);return s+n+r}let r,a;const o=this._document.createElement("canvas");o.width=1,o.height=1;const l=o.getContext("2d");let c=0;function h(t,e){if(++c>30)return(0,n.warn)("Load test font never loaded."),void e();l.font="30px "+t,l.fillText(".",0,20);const i=l.getImageData(0,0,1,1);i.data[3]>0?e():setTimeout(h.bind(null,t,e))}const d=`lt${Date.now()}${this.loadTestFontId++}`;let u=this._loadTestFont;const p=976;u=s(u,p,d.length,d);const g=16,f=1482184792;let m=i(u,g);for(r=0,a=d.length-3;r<a;r+=4)m=m-f+i(d,r)|0;r<d.length&&(m=m-f+i(d+"XXX",r)|0),u=s(u,g,4,(0,n.string32)(m));const b=`url(data:font/opentype;base64,${btoa(u)});`,v=`@font-face {font-family:"${d}";src:${b}}`;this.insertRule(v);const _=this._document.createElement("div");_.style.visibility="hidden",_.style.width=_.style.height="10px",_.style.position="absolute",_.style.top=_.style.left="0px";for(const n of[t.loadedName,d]){const t=this._document.createElement("span");t.textContent="Hi",t.style.fontFamily=n,_.append(t)}this._document.body.append(_),h(d,(()=>{_.remove(),e.complete()}))}}e.FontLoader=s;class r{constructor(t,e){let{isEvalSupported:i=!0,disableFontFace:n=!1,ignoreErrors:s=!1,inspectFont:r=null}=e;this.compiledGlyphs=Object.create(null);for(const a in t)this[a]=t[a];this.isEvalSupported=!1!==i,this.disableFontFace=!0===n,this.ignoreErrors=!0===s,this._inspectFont=r}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`),t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});return this._inspectFont?.(this),t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=(0,n.bytesToString)(this.data),e=`url(data:${this.mimetype};base64,${btoa(t)});`;let i;if(this.cssFontInfo){let t=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(t+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`),i=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${t}src:${e}}`}else i=`@font-face {font-family:"${this.loadedName}";src:${e}}`;return this._inspectFont?.(this,e),i}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];let i;try{i=t.get(this.loadedName+"_path_"+e)}catch(s){if(!this.ignoreErrors)throw s;return(0,n.warn)(`getPathGenerator - ignoring character: "${s}".`),this.compiledGlyphs[e]=function(t,e){}}if(this.isEvalSupported&&n.FeatureTest.isEvalSupported){const t=[];for(const e of i){const i=void 0!==e.args?e.args.join(","):"";t.push("c.",e.cmd,"(",i,");\n")}return this.compiledGlyphs[e]=new Function("c","size",t.join(""))}return this.compiledGlyphs[e]=function(t,e){for(const n of i)"scale"===n.cmd&&(n.args=[e,-e]),t[n.cmd].apply(t,n.args)}}}e.FontFaceObject=r},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.NodeStandardFontDataFactory=e.NodeFilterFactory=e.NodeCanvasFactory=e.NodeCMapReaderFactory=void 0,i(2),i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123);var n=i(169),s=i(1);(function(){if(!globalThis.DOMMatrix&&s.isNodeJS)try{globalThis.DOMMatrix=require("canvas").DOMMatrix}catch(t){(0,s.warn)(`Cannot polyfill \`DOMMatrix\`, rendering may be broken: "${t}".`)}})(),function(){if(!globalThis.Path2D&&s.isNodeJS)try{const{CanvasRenderingContext2D:t}=require("canvas"),{polyfillPath2D:e}=require("path2d-polyfill");globalThis.CanvasRenderingContext2D=t,e(globalThis)}catch(t){(0,s.warn)(`Cannot polyfill \`Path2D\`, rendering may be broken: "${t}".`)}}();const r=function(t){return new Promise(((e,i)=>{const n=require("fs");n.readFile(t,((t,n)=>{!t&&n?e(new Uint8Array(n)):i(new Error(t))}))}))};class a extends n.BaseFilterFactory{}e.NodeFilterFactory=a;class o extends n.BaseCanvasFactory{_createCanvas(t,e){const i=require("canvas");return i.createCanvas(t,e)}}e.NodeCanvasFactory=o;class l extends n.BaseCMapReaderFactory{_fetchData(t,e){return r(t).then((t=>({cMapData:t,compressionType:e})))}}e.NodeCMapReaderFactory=l;class c extends n.BaseStandardFontDataFactory{_fetchData(t){return r(t)}}e.NodeStandardFontDataFactory=c},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.CanvasGraphics=void 0,i(2),i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123),i(89);var n=i(1),s=i(168),r=i(174),a=i(175);const o=16,l=100,c=4096,h=15,d=10,u=1e3,p=16;function g(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save,t.__originalRestore=t.restore,t.__originalRotate=t.rotate,t.__originalScale=t.scale,t.__originalTranslate=t.translate,t.__originalTransform=t.transform,t.__originalSetTransform=t.setTransform,t.__originalResetTransform=t.resetTransform,t.__originalClip=t.clip,t.__originalMoveTo=t.moveTo,t.__originalLineTo=t.lineTo,t.__originalBezierCurveTo=t.bezierCurveTo,t.__originalRect=t.rect,t.__originalClosePath=t.closePath,t.__originalBeginPath=t.beginPath,t._removeMirroring=()=>{t.save=t.__originalSave,t.restore=t.__originalRestore,t.rotate=t.__originalRotate,t.scale=t.__originalScale,t.translate=t.__originalTranslate,t.transform=t.__originalTransform,t.setTransform=t.__originalSetTransform,t.resetTransform=t.__originalResetTransform,t.clip=t.__originalClip,t.moveTo=t.__originalMoveTo,t.lineTo=t.__originalLineTo,t.bezierCurveTo=t.__originalBezierCurveTo,t.rect=t.__originalRect,t.closePath=t.__originalClosePath,t.beginPath=t.__originalBeginPath,delete t._removeMirroring},t.save=function(){e.save(),this.__originalSave()},t.restore=function(){e.restore(),this.__originalRestore()},t.translate=function(t,i){e.translate(t,i),this.__originalTranslate(t,i)},t.scale=function(t,i){e.scale(t,i),this.__originalScale(t,i)},t.transform=function(t,i,n,s,r,a){e.transform(t,i,n,s,r,a),this.__originalTransform(t,i,n,s,r,a)},t.setTransform=function(t,i,n,s,r,a){e.setTransform(t,i,n,s,r,a),this.__originalSetTransform(t,i,n,s,r,a)},t.resetTransform=function(){e.resetTransform(),this.__originalResetTransform()},t.rotate=function(t){e.rotate(t),this.__originalRotate(t)},t.clip=function(t){e.clip(t),this.__originalClip(t)},t.moveTo=function(t,i){e.moveTo(t,i),this.__originalMoveTo(t,i)},t.lineTo=function(t,i){e.lineTo(t,i),this.__originalLineTo(t,i)},t.bezierCurveTo=function(t,i,n,s,r,a){e.bezierCurveTo(t,i,n,s,r,a),this.__originalBezierCurveTo(t,i,n,s,r,a)},t.rect=function(t,i,n,s){e.rect(t,i,n,s),this.__originalRect(t,i,n,s)},t.closePath=function(){e.closePath(),this.__originalClosePath()},t.beginPath=function(){e.beginPath(),this.__originalBeginPath()}}class f{constructor(t){this.canvasFactory=t,this.cache=Object.create(null)}getCanvas(t,e,i){let n;return void 0!==this.cache[t]?(n=this.cache[t],this.canvasFactory.reset(n,e,i)):(n=this.canvasFactory.create(e,i),this.cache[t]=n),n}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e),delete this.cache[t]}}}function m(t,e,i,n,r,a,o,l,c,h){const[d,u,p,g,f,m]=(0,s.getCurrentTransform)(t);if(0===u&&0===p){const s=o*d+f,b=Math.round(s),v=l*g+m,_=Math.round(v),y=(o+c)*d+f,A=Math.abs(Math.round(y)-b)||1,S=(l+h)*g+m,x=Math.abs(Math.round(S)-_)||1;return t.setTransform(Math.sign(d),0,0,Math.sign(g),b,_),t.drawImage(e,i,n,r,a,0,0,A,x),t.setTransform(d,u,p,g,f,m),[A,x]}if(0===d&&0===g){const s=l*p+f,b=Math.round(s),v=o*u+m,_=Math.round(v),y=(l+h)*p+f,A=Math.abs(Math.round(y)-b)||1,S=(o+c)*u+m,x=Math.abs(Math.round(S)-_)||1;return t.setTransform(0,Math.sign(u),Math.sign(p),0,b,_),t.drawImage(e,i,n,r,a,0,0,x,A),t.setTransform(d,u,p,g,f,m),[x,A]}t.drawImage(e,i,n,r,a,o,l,c,h);const b=Math.hypot(d,u),v=Math.hypot(p,g);return[b*c,v*h]}function b(t){const{width:e,height:i}=t;if(e>u||i>u)return null;const n=1e3,s=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),r=e+1;let a,o,l,c=new Uint8Array(r*(i+1));const h=e+7&-8;let d=new Uint8Array(h*i),p=0;for(const u of t.data){let t=128;while(t>0)d[p++]=u&t?0:255,t>>=1}let g=0;for(p=0,0!==d[p]&&(c[0]=1,++g),o=1;o<e;o++)d[p]!==d[p+1]&&(c[o]=d[p]?2:1,++g),p++;for(0!==d[p]&&(c[o]=2,++g),a=1;a<i;a++){p=a*h,l=a*r,d[p-h]!==d[p]&&(c[l]=d[p]?1:8,++g);let t=(d[p]?4:0)+(d[p-h]?8:0);for(o=1;o<e;o++)t=(t>>2)+(d[p+1]?4:0)+(d[p-h+1]?8:0),s[t]&&(c[l+o]=s[t],++g),p++;if(d[p-h]!==d[p]&&(c[l+o]=d[p]?2:4,++g),g>n)return null}for(p=h*(i-1),l=a*r,0!==d[p]&&(c[l]=8,++g),o=1;o<e;o++)d[p]!==d[p+1]&&(c[l+o]=d[p]?4:8,++g),p++;if(0!==d[p]&&(c[l+o]=4,++g),g>n)return null;const f=new Int32Array([0,r,-1,0,-r,0,0,0,1]),m=new Path2D;for(a=0;g&&a<=i;a++){let t=a*r;const i=t+e;while(t<i&&!c[t])t++;if(t===i)continue;m.moveTo(t%r,a);const n=t;let s=c[t];do{const e=f[s];do{t+=e}while(!c[t]);const i=c[t];5!==i&&10!==i?(s=i,c[t]=0):(s=i&51*s>>4,c[t]&=s>>2|s<<2),m.lineTo(t%r,t/r|0),c[t]||--g}while(n!==t);--a}d=null,c=null;const b=function(t){t.save(),t.scale(1/e,-1/i),t.translate(0,-i),t.fill(m),t.beginPath(),t.restore()};return b}class v{constructor(t,e){this.alphaIsShape=!1,this.fontSize=0,this.fontSizeScale=1,this.textMatrix=n.IDENTITY_MATRIX,this.textMatrixScale=1,this.fontMatrix=n.FONT_IDENTITY_MATRIX,this.leading=0,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRenderingMode=n.TextRenderingMode.FILL,this.textRise=0,this.fillColor="#000000",this.strokeColor="#000000",this.patternFill=!1,this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.activeSMask=null,this.transferMaps="none",this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);return t.clipBox=this.clipBox.slice(),t}setCurrentPoint(t,e){this.x=t,this.y=e}updatePathMinMax(t,e,i){[e,i]=n.Util.applyTransform([e,i],t),this.minX=Math.min(this.minX,e),this.minY=Math.min(this.minY,i),this.maxX=Math.max(this.maxX,e),this.maxY=Math.max(this.maxY,i)}updateRectMinMax(t,e){const i=n.Util.applyTransform(e,t),s=n.Util.applyTransform(e.slice(2),t);this.minX=Math.min(this.minX,i[0],s[0]),this.minY=Math.min(this.minY,i[1],s[1]),this.maxX=Math.max(this.maxX,i[0],s[0]),this.maxY=Math.max(this.maxY,i[1],s[1])}updateScalingPathMinMax(t,e){n.Util.scaleMinMax(t,e),this.minX=Math.min(this.minX,e[0]),this.maxX=Math.max(this.maxX,e[1]),this.minY=Math.min(this.minY,e[2]),this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,i,s,r,a,o,l,c,h){const d=n.Util.bezierBoundingBox(e,i,s,r,a,o,l,c);if(h)return h[0]=Math.min(h[0],d[0],d[2]),h[1]=Math.max(h[1],d[0],d[2]),h[2]=Math.min(h[2],d[1],d[3]),void(h[3]=Math.max(h[3],d[1],d[3]));this.updateRectMinMax(t,d)}getPathBoundingBox(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r.PathType.FILL,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const i=[this.minX,this.minY,this.maxX,this.maxY];if(t===r.PathType.STROKE){e||(0,n.unreachable)("Stroke bounding box must include transform.");const t=n.Util.singularValueDecompose2dScale(e),s=t[0]*this.lineWidth/2,r=t[1]*this.lineWidth/2;i[0]-=s,i[1]-=r,i[2]+=s,i[3]+=r}return i}updateClipFromPath(){const t=n.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t,this.minX=1/0,this.minY=1/0,this.maxX=0,this.maxY=0}getClippedPathBoundingBox(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:r.PathType.FILL,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return n.Util.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function _(t,e){if("undefined"!==typeof ImageData&&e instanceof ImageData)return void t.putImageData(e,0,0);const i=e.height,s=e.width,r=i%p,a=(i-r)/p,o=0===r?a:a+1,l=t.createImageData(s,p);let c,h=0;const d=e.data,u=l.data;let g,f,m,b;if(e.kind===n.ImageKind.GRAYSCALE_1BPP){const e=d.byteLength,i=new Uint32Array(u.buffer,0,u.byteLength>>2),b=i.length,v=s+7>>3,_=4294967295,y=n.FeatureTest.isLittleEndian?4278190080:255;for(g=0;g<o;g++){for(m=g<a?p:r,c=0,f=0;f<m;f++){const t=e-h;let n=0;const r=t>v?s:8*t-7,a=-8&r;let o=0,l=0;for(;n<a;n+=8)l=d[h++],i[c++]=128&l?_:y,i[c++]=64&l?_:y,i[c++]=32&l?_:y,i[c++]=16&l?_:y,i[c++]=8&l?_:y,i[c++]=4&l?_:y,i[c++]=2&l?_:y,i[c++]=1&l?_:y;for(;n<r;n++)0===o&&(l=d[h++],o=128),i[c++]=l&o?_:y,o>>=1}while(c<b)i[c++]=0;t.putImageData(l,0,g*p)}}else if(e.kind===n.ImageKind.RGBA_32BPP){for(f=0,b=s*p*4,g=0;g<a;g++)u.set(d.subarray(h,h+b)),h+=b,t.putImageData(l,0,f),f+=p;g<o&&(b=s*r*4,u.set(d.subarray(h,h+b)),t.putImageData(l,0,f))}else{if(e.kind!==n.ImageKind.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);for(m=p,b=s*m,g=0;g<o;g++){for(g>=a&&(m=r,b=s*m),c=0,f=b;f--;)u[c++]=d[h++],u[c++]=d[h++],u[c++]=d[h++],u[c++]=255;t.putImageData(l,0,g*p)}}}function y(t,e){if(e.bitmap)return void t.drawImage(e.bitmap,0,0);const i=e.height,n=e.width,s=i%p,r=(i-s)/p,o=0===s?r:r+1,l=t.createImageData(n,p);let c=0;const h=e.data,d=l.data;for(let u=0;u<o;u++){const e=u<r?p:s;({srcPos:c}=(0,a.convertBlackAndWhiteToRGBA)({src:h,srcPos:c,dest:d,width:n,height:e,nonBlackColor:0})),t.putImageData(l,0,u*p)}}function A(t,e){const i=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font","filter"];for(const n of i)void 0!==t[n]&&(e[n]=t[n]);void 0!==t.setLineDash&&(e.setLineDash(t.getLineDash()),e.lineDashOffset=t.lineDashOffset)}function S(t){if(t.strokeStyle=t.fillStyle="#000000",t.fillRule="nonzero",t.globalAlpha=1,t.lineWidth=1,t.lineCap="butt",t.lineJoin="miter",t.miterLimit=10,t.globalCompositeOperation="source-over",t.font="10px sans-serif",void 0!==t.setLineDash&&(t.setLineDash([]),t.lineDashOffset=0),!n.isNodeJS){const{filter:e}=t;"none"!==e&&""!==e&&(t.filter="none")}}function x(t,e,i,n){const s=t.length;for(let r=3;r<s;r+=4){const s=t[r];if(0===s)t[r-3]=e,t[r-2]=i,t[r-1]=n;else if(s<255){const a=255-s;t[r-3]=t[r-3]*s+e*a>>8,t[r-2]=t[r-2]*s+i*a>>8,t[r-1]=t[r-1]*s+n*a>>8}}}function E(t,e,i){const n=t.length,s=1/255;for(let r=3;r<n;r+=4){const n=i?i[t[r]]:t[r];e[r]=e[r]*n*s|0}}function w(t,e,i){const n=t.length;for(let s=3;s<n;s+=4){const n=77*t[s-3]+152*t[s-2]+28*t[s-1];e[s]=i?e[s]*i[n>>8]>>8:e[s]*n>>16}}function C(t,e,i,n,s,r,a,o,l,c,h){const d=!!r,u=d?r[0]:0,p=d?r[1]:0,g=d?r[2]:0,f="Luminosity"===s?w:E,m=1048576,b=Math.min(n,Math.ceil(m/i));for(let v=0;v<n;v+=b){const s=Math.min(b,n-v),r=t.getImageData(o-c,v+(l-h),i,s),m=e.getImageData(o,v+l,i,s);d&&x(r.data,u,p,g),f(r.data,m.data,a),e.putImageData(m,o,v+l)}}function T(t,e,i,n){const s=n[0],r=n[1],a=n[2]-s,o=n[3]-r;0!==a&&0!==o&&(C(e.context,i,a,o,e.subtype,e.backdrop,e.transferMap,s,r,e.offsetX,e.offsetY),t.save(),t.globalAlpha=1,t.globalCompositeOperation="source-over",t.setTransform(1,0,0,1,0,0),t.drawImage(i.canvas,0,0),t.restore())}function P(t,e){const i=n.Util.singularValueDecompose2dScale(t);i[0]=Math.fround(i[0]),i[1]=Math.fround(i[1]);const r=Math.fround((globalThis.devicePixelRatio||1)*s.PixelsPerInch.PDF_TO_CSS_UNITS);return void 0!==e?e:i[0]<=r||i[1]<=r}const k=["butt","round","square"],M=["miter","round","bevel"],R={},F={};class D{constructor(t,e,i,n,s,r,a,o){let{optionalContentConfig:l,markedContentStack:c=null}=r;this.ctx=t,this.current=new v(this.ctx.canvas.width,this.ctx.canvas.height),this.stateStack=[],this.pendingClip=null,this.pendingEOFill=!1,this.res=null,this.xobjs=null,this.commonObjs=e,this.objs=i,this.canvasFactory=n,this.filterFactory=s,this.groupStack=[],this.processingType3=null,this.baseTransform=null,this.baseTransformStack=[],this.groupLevel=0,this.smaskStack=[],this.smaskCounter=0,this.tempSMask=null,this.suspendedCtx=null,this.contentVisible=!0,this.markedContentStack=c||[],this.optionalContentConfig=l,this.cachedCanvases=new f(this.canvasFactory),this.cachedPatterns=new Map,this.annotationCanvasMap=a,this.viewportScale=1,this.outputScaleX=1,this.outputScaleY=1,this.pageColors=o,this._cachedScaleForStroking=[-1,0],this._cachedGetSinglePixelWidth=null,this._cachedBitmapsMap=new Map}getObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return"string"===typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing(t){let{transform:e,viewport:i,transparency:n=!1,background:r=null}=t;const a=this.ctx.canvas.width,o=this.ctx.canvas.height,l=this.ctx.fillStyle;if(this.ctx.fillStyle=r||"#ffffff",this.ctx.fillRect(0,0,a,o),this.ctx.fillStyle=l,n){const t=this.cachedCanvases.getCanvas("transparent",a,o);this.compositeCtx=this.ctx,this.transparentCanvas=t.canvas,this.ctx=t.context,this.ctx.save(),this.ctx.transform(...(0,s.getCurrentTransform)(this.compositeCtx))}this.ctx.save(),S(this.ctx),e&&(this.ctx.transform(...e),this.outputScaleX=e[0],this.outputScaleY=e[0]),this.ctx.transform(...i.transform),this.viewportScale=i.scale,this.baseTransform=(0,s.getCurrentTransform)(this.ctx)}executeOperatorList(t,e,i,s){const r=t.argsArray,a=t.fnArray;let o=e||0;const l=r.length;if(l===o)return o;const c=l-o>d&&"function"===typeof i,u=c?Date.now()+h:0;let p=0;const g=this.commonObjs,f=this.objs;let m;while(1){if(void 0!==s&&o===s.nextBreakPoint)return s.breakIt(o,i),o;if(m=a[o],m!==n.OPS.dependency)this[m].apply(this,r[o]);else for(const t of r[o]){const e=t.startsWith("g_")?g:f;if(!e.has(t))return e.get(t,i),o}if(o++,o===l)return o;if(c&&++p>d){if(Date.now()>u)return i(),o;p=0}}}#ce(){while(this.stateStack.length||this.inSMaskMode)this.restore();this.ctx.restore(),this.transparentCanvas&&(this.ctx=this.compositeCtx,this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.drawImage(this.transparentCanvas,0,0),this.ctx.restore(),this.transparentCanvas=null)}endDrawing(){this.#ce(),this.cachedCanvases.clear(),this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!==typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear(),this.#he()}#he(){if(this.pageColors){const t=this.filterFactory.addHCMFilter(this.pageColors.foreground,this.pageColors.background);if("none"!==t){const e=this.ctx.filter;this.ctx.filter=t,this.ctx.drawImage(this.ctx.canvas,0,0),this.ctx.filter=e}}}_scaleImage(t,e){const i=t.width,n=t.height;let s,r,a=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=i,c=n,h="prescale1";while(a>2&&l>1||o>2&&c>1){let e=l,i=c;a>2&&l>1&&(e=l>=16384?Math.floor(l/2)-1||1:Math.ceil(l/2),a/=l/e),o>2&&c>1&&(i=c>=16384?Math.floor(c/2)-1||1:Math.ceil(c)/2,o/=c/i),s=this.cachedCanvases.getCanvas(h,e,i),r=s.context,r.clearRect(0,0,e,i),r.drawImage(t,0,0,l,c,0,0,e,i),t=s.canvas,l=e,c=i,h="prescale1"===h?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:c}}_createMaskCanvas(t){const e=this.ctx,{width:i,height:a}=t,o=this.current.fillColor,l=this.current.patternFill,c=(0,s.getCurrentTransform)(e);let h,d,u,p;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer;d=JSON.stringify(l?c:[c.slice(0,4),o]),h=this._cachedBitmapsMap.get(e),h||(h=new Map,this._cachedBitmapsMap.set(e,h));const i=h.get(d);if(i&&!l){const t=Math.round(Math.min(c[0],c[2])+c[4]),e=Math.round(Math.min(c[1],c[3])+c[5]);return{canvas:i,offsetX:t,offsetY:e}}u=i}u||(p=this.cachedCanvases.getCanvas("maskCanvas",i,a),y(p.context,t));let g=n.Util.transform(c,[1/i,0,0,-1/a,0,0]);g=n.Util.transform(g,[1,0,0,1,0,-a]);const f=n.Util.applyTransform([0,0],g),b=n.Util.applyTransform([i,a],g),v=n.Util.normalizeRect([f[0],f[1],b[0],b[1]]),_=Math.round(v[2]-v[0])||1,A=Math.round(v[3]-v[1])||1,S=this.cachedCanvases.getCanvas("fillCanvas",_,A),x=S.context,E=Math.min(f[0],b[0]),w=Math.min(f[1],b[1]);x.translate(-E,-w),x.transform(...g),u||(u=this._scaleImage(p.canvas,(0,s.getCurrentTransformInverse)(x)),u=u.img,h&&l&&h.set(d,u)),x.imageSmoothingEnabled=P((0,s.getCurrentTransform)(x),t.interpolate),m(x,u,0,0,u.width,u.height,0,0,i,a),x.globalCompositeOperation="source-in";const C=n.Util.transform((0,s.getCurrentTransformInverse)(x),[1,0,0,1,-E,-w]);return x.fillStyle=l?o.getPattern(e,this,C,r.PathType.FILL):o,x.fillRect(0,0,i,a),h&&!l&&(this.cachedCanvases.delete("fillCanvas"),h.set(d,S.canvas)),{canvas:S.canvas,offsetX:Math.round(E),offsetY:Math.round(w)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking[0]=-1),this.current.lineWidth=t,this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=k[t]}setLineJoin(t){this.ctx.lineJoin=M[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const i=this.ctx;void 0!==i.setLineDash&&(i.setLineDash(t),i.lineDashOffset=e)}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=i;break;case"ca":this.current.fillAlpha=i,this.ctx.globalAlpha=i;break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null,this.tempSMask=null,this.checkSMaskState();break;case"TR":this.ctx.filter=this.current.transferMaps=this.filterFactory.addFilter(i);break}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,i="smaskGroupAt"+this.groupLevel,n=this.cachedCanvases.getCanvas(i,t,e);this.suspendedCtx=this.ctx,this.ctx=n.context;const r=this.ctx;r.setTransform(...(0,s.getCurrentTransform)(this.suspendedCtx)),A(this.suspendedCtx,r),g(r,this.suspendedCtx),this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring(),A(this.ctx,this.suspendedCtx),this.ctx=this.suspendedCtx,this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;t?(t[0]=Math.floor(t[0]),t[1]=Math.floor(t[1]),t[2]=Math.ceil(t[2]),t[3]=Math.ceil(t[3])):t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask,i=this.suspendedCtx;T(i,e,this.ctx,t),this.ctx.save(),this.ctx.setTransform(1,0,0,1,0,0),this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height),this.ctx.restore()}save(){this.inSMaskMode?(A(this.ctx,this.suspendedCtx),this.suspendedCtx.save()):this.ctx.save();const t=this.current;this.stateStack.push(t),this.current=t.clone()}restore(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode(),0!==this.stateStack.length&&(this.current=this.stateStack.pop(),this.inSMaskMode?(this.suspendedCtx.restore(),A(this.suspendedCtx,this.ctx)):this.ctx.restore(),this.checkSMaskState(),this.pendingClip=null,this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null)}transform(t,e,i,n,s,r){this.ctx.transform(t,e,i,n,s,r),this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null}constructPath(t,e,i){const r=this.ctx,a=this.current;let o,l,c=a.x,h=a.y;const d=(0,s.getCurrentTransform)(r),u=0===d[0]&&0===d[3]||0===d[1]&&0===d[2],p=u?i.slice(0):null;for(let s=0,g=0,f=t.length;s<f;s++)switch(0|t[s]){case n.OPS.rectangle:c=e[g++],h=e[g++];const t=e[g++],i=e[g++],s=c+t,f=h+i;r.moveTo(c,h),0===t||0===i?r.lineTo(s,f):(r.lineTo(s,h),r.lineTo(s,f),r.lineTo(c,f)),u||a.updateRectMinMax(d,[c,h,s,f]),r.closePath();break;case n.OPS.moveTo:c=e[g++],h=e[g++],r.moveTo(c,h),u||a.updatePathMinMax(d,c,h);break;case n.OPS.lineTo:c=e[g++],h=e[g++],r.lineTo(c,h),u||a.updatePathMinMax(d,c,h);break;case n.OPS.curveTo:o=c,l=h,c=e[g+4],h=e[g+5],r.bezierCurveTo(e[g],e[g+1],e[g+2],e[g+3],c,h),a.updateCurvePathMinMax(d,o,l,e[g],e[g+1],e[g+2],e[g+3],c,h,p),g+=6;break;case n.OPS.curveTo2:o=c,l=h,r.bezierCurveTo(c,h,e[g],e[g+1],e[g+2],e[g+3]),a.updateCurvePathMinMax(d,o,l,c,h,e[g],e[g+1],e[g+2],e[g+3],p),c=e[g+2],h=e[g+3],g+=4;break;case n.OPS.curveTo3:o=c,l=h,c=e[g+2],h=e[g+3],r.bezierCurveTo(e[g],e[g+1],c,h,c,h),a.updateCurvePathMinMax(d,o,l,e[g],e[g+1],c,h,c,h,p),g+=4;break;case n.OPS.closePath:r.closePath();break}u&&a.updateScalingPathMinMax(d,p),a.setCurrentPoint(c,h)}closePath(){this.ctx.closePath()}stroke(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const e=this.ctx,i=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha,this.contentVisible&&("object"===typeof i&&i?.getPattern?(e.save(),e.strokeStyle=i.getPattern(e,this,(0,s.getCurrentTransformInverse)(e),r.PathType.STROKE),this.rescaleAndStroke(!1),e.restore()):this.rescaleAndStroke(!0)),t&&this.consumePath(this.current.getClippedPathBoundingBox()),e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath(),this.stroke()}fill(){let t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];const e=this.ctx,i=this.current.fillColor,n=this.current.patternFill;let a=!1;n&&(e.save(),e.fillStyle=i.getPattern(e,this,(0,s.getCurrentTransformInverse)(e),r.PathType.FILL),a=!0);const o=this.current.getClippedPathBoundingBox();this.contentVisible&&null!==o&&(this.pendingEOFill?(e.fill("evenodd"),this.pendingEOFill=!1):e.fill()),a&&e.restore(),t&&this.consumePath(o)}eoFill(){this.pendingEOFill=!0,this.fill()}fillStroke(){this.fill(!1),this.stroke(!1),this.consumePath()}eoFillStroke(){this.pendingEOFill=!0,this.fillStroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0,this.closePath(),this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=R}eoClip(){this.pendingClip=F}beginText(){this.current.textMatrix=n.IDENTITY_MATRIX,this.current.textMatrixScale=1,this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0!==t){e.save(),e.beginPath();for(const i of t)e.setTransform(...i.transform),e.translate(i.x,i.y),i.addToPath(e,i.fontSize);e.restore(),e.clip(),e.beginPath(),delete this.pendingTextPaths}else e.beginPath()}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const i=this.commonObjs.get(t),s=this.current;if(!i)throw new Error(`Can't find font for ${t}`);if(s.fontMatrix=i.fontMatrix||n.FONT_IDENTITY_MATRIX,0!==s.fontMatrix[0]&&0!==s.fontMatrix[3]||(0,n.warn)("Invalid font matrix for font "+t),e<0?(e=-e,s.fontDirection=-1):s.fontDirection=1,this.current.font=i,this.current.fontSize=e,i.isType3Font)return;const r=i.loadedName||"sans-serif",a=i.systemFontInfo?.css||`"${r}", ${i.fallbackName}`;let c="normal";i.black?c="900":i.bold&&(c="bold");const h=i.italic?"italic":"normal";let d=e;e<o?d=o:e>l&&(d=l),this.current.fontSizeScale=e/d,this.ctx.font=`${h} ${c} ${d}px ${a}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t,this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}setTextMatrix(t,e,i,n,s,r){this.current.textMatrix=[t,e,i,n,s,r],this.current.textMatrixScale=Math.hypot(t,e),this.current.x=this.current.lineX=0,this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,i,r){const a=this.ctx,o=this.current,l=o.font,c=o.textRenderingMode,h=o.fontSize/o.fontSizeScale,d=c&n.TextRenderingMode.FILL_STROKE_MASK,u=!!(c&n.TextRenderingMode.ADD_TO_PATH_FLAG),p=o.patternFill&&!l.missingFile;let g;if((l.disableFontFace||u||p)&&(g=l.getPathGenerator(this.commonObjs,t)),l.disableFontFace||p?(a.save(),a.translate(e,i),a.beginPath(),g(a,h),r&&a.setTransform(...r),d!==n.TextRenderingMode.FILL&&d!==n.TextRenderingMode.FILL_STROKE||a.fill(),d!==n.TextRenderingMode.STROKE&&d!==n.TextRenderingMode.FILL_STROKE||a.stroke(),a.restore()):(d!==n.TextRenderingMode.FILL&&d!==n.TextRenderingMode.FILL_STROKE||a.fillText(t,e,i),d!==n.TextRenderingMode.STROKE&&d!==n.TextRenderingMode.FILL_STROKE||a.strokeText(t,e,i)),u){const t=this.pendingTextPaths||=[];t.push({transform:(0,s.getCurrentTransform)(a),x:e,y:i,fontSize:h,addToPath:g})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1),t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let i=!1;for(let n=3;n<e.length;n+=4)if(e[n]>0&&e[n]<255){i=!0;break}return(0,n.shadow)(this,"isFontSubpixelAAEnabled",i)}showText(t){const e=this.current,i=e.font;if(i.isType3Font)return this.showType3Text(t);const a=e.fontSize;if(0===a)return;const o=this.ctx,l=e.fontSizeScale,c=e.charSpacing,h=e.wordSpacing,d=e.fontDirection,u=e.textHScale*d,p=t.length,g=i.vertical,f=g?1:-1,m=i.defaultVMetrics,b=a*e.fontMatrix[0],v=e.textRenderingMode===n.TextRenderingMode.FILL&&!i.disableFontFace&&!e.patternFill;let _;if(o.save(),o.transform(...e.textMatrix),o.translate(e.x,e.y+e.textRise),d>0?o.scale(u,-1):o.scale(u,1),e.patternFill){o.save();const t=e.fillColor.getPattern(o,this,(0,s.getCurrentTransformInverse)(o),r.PathType.FILL);_=(0,s.getCurrentTransform)(o),o.restore(),o.fillStyle=t}let y=e.lineWidth;const A=e.textMatrixScale;if(0===A||0===y){const t=e.textRenderingMode&n.TextRenderingMode.FILL_STROKE_MASK;t!==n.TextRenderingMode.STROKE&&t!==n.TextRenderingMode.FILL_STROKE||(y=this.getSinglePixelWidth())}else y/=A;if(1!==l&&(o.scale(l,l),y/=l),o.lineWidth=y,i.isInvalidPDFjsFont){const i=[];let n=0;for(const e of t)i.push(e.unicode),n+=e.width;return o.fillText(i.join(""),0,0),e.x+=n*b*u,o.restore(),void this.compose()}let S,x=0;for(S=0;S<p;++S){const e=t[S];if("number"===typeof e){x+=f*e*a/1e3;continue}let n=!1;const s=(e.isSpace?h:0)+c,r=e.fontChar,u=e.accent;let p,y,A=e.width;if(g){const t=e.vmetric||m,i=-(e.vmetric?t[1]:.5*A)*b,n=t[2]*b;A=t?-t[0]:A,p=i/l,y=(x+n)/l}else p=x/l,y=0;if(i.remeasure&&A>0){const t=1e3*o.measureText(r).width/a*l;if(A<t&&this.isFontSubpixelAAEnabled){const e=A/t;n=!0,o.save(),o.scale(e,1),p/=e}else A!==t&&(p+=(A-t)/2e3*a/l)}if(this.contentVisible&&(e.isInFont||i.missingFile))if(v&&!u)o.fillText(r,p,y);else if(this.paintChar(r,p,y,_),u){const t=p+a*u.offset.x/l,e=y-a*u.offset.y/l;this.paintChar(u.fontChar,t,e,_)}const E=g?A*b-s*d:A*b+s*d;x+=E,n&&o.restore()}g?e.y-=x:e.x+=x*u,o.restore(),this.compose()}showType3Text(t){const e=this.ctx,i=this.current,s=i.font,r=i.fontSize,a=i.fontDirection,o=s.vertical?1:-1,l=i.charSpacing,c=i.wordSpacing,h=i.textHScale*a,d=i.fontMatrix||n.FONT_IDENTITY_MATRIX,u=t.length,p=i.textRenderingMode===n.TextRenderingMode.INVISIBLE;let g,f,m,b;if(!p&&0!==r){for(this._cachedScaleForStroking[0]=-1,this._cachedGetSinglePixelWidth=null,e.save(),e.transform(...i.textMatrix),e.translate(i.x,i.y),e.scale(h,a),g=0;g<u;++g){if(f=t[g],"number"===typeof f){b=o*f*r/1e3,this.ctx.translate(b,0),i.x+=b*h;continue}const a=(f.isSpace?c:0)+l,u=s.charProcOperatorList[f.operatorListId];if(!u){(0,n.warn)(`Type3 character "${f.operatorListId}" is not available.`);continue}this.contentVisible&&(this.processingType3=f,this.save(),e.scale(r,r),e.transform(...d),this.executeOperatorList(u),this.restore());const p=n.Util.applyTransform([f.width,0],d);m=p[0]*r+a,e.translate(m,0),i.x+=m*h}e.restore(),this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,i,n,s,r){this.ctx.rect(i,n,s-i,r-n),this.ctx.clip(),this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const i=t[1],n=this.baseTransform||(0,s.getCurrentTransform)(this.ctx),a={createCanvasGraphics:t=>new D(t,this.commonObjs,this.objs,this.canvasFactory,this.filterFactory,{optionalContentConfig:this.optionalContentConfig,markedContentStack:this.markedContentStack})};e=new r.TilingPattern(t,i,this.ctx,a,n)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments),this.current.patternFill=!0}setStrokeRGBColor(t,e,i){const s=n.Util.makeHexColor(t,e,i);this.ctx.strokeStyle=s,this.current.strokeColor=s}setFillRGBColor(t,e,i){const s=n.Util.makeHexColor(t,e,i);this.ctx.fillStyle=s,this.current.fillColor=s,this.current.patternFill=!1}_getPattern(t){let e,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return this.cachedPatterns.has(t)?e=this.cachedPatterns.get(t):(e=(0,r.getShadingPattern)(this.getObject(t)),this.cachedPatterns.set(t,e)),i&&(e.matrix=i),e}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const i=this._getPattern(t);e.fillStyle=i.getPattern(e,this,(0,s.getCurrentTransformInverse)(e),r.PathType.SHADING);const a=(0,s.getCurrentTransformInverse)(e);if(a){const{width:t,height:i}=e.canvas,[s,r,o,l]=n.Util.getAxialAlignedBoundingBox([0,0,t,i],a);this.ctx.fillRect(s,r,o-s,l-r)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox()),this.restore()}beginInlineImage(){(0,n.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,n.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible&&(this.save(),this.baseTransformStack.push(this.baseTransform),Array.isArray(t)&&6===t.length&&this.transform(...t),this.baseTransform=(0,s.getCurrentTransform)(this.ctx),e)){const t=e[2]-e[0],i=e[3]-e[1];this.ctx.rect(e[0],e[1],t,i),this.current.updateRectMinMax((0,s.getCurrentTransform)(this.ctx),e),this.clip(),this.endPath()}}paintFormXObjectEnd(){this.contentVisible&&(this.restore(),this.baseTransform=this.baseTransformStack.pop())}beginGroup(t){if(!this.contentVisible)return;this.save(),this.inSMaskMode&&(this.endSMaskMode(),this.current.activeSMask=null);const e=this.ctx;t.isolated||(0,n.info)("TODO: Support non-isolated groups."),t.knockout&&(0,n.warn)("Knockout groups not supported.");const i=(0,s.getCurrentTransform)(e);if(t.matrix&&e.transform(...t.matrix),!t.bbox)throw new Error("Bounding box is required.");let r=n.Util.getAxialAlignedBoundingBox(t.bbox,(0,s.getCurrentTransform)(e));const a=[0,0,e.canvas.width,e.canvas.height];r=n.Util.intersect(r,a)||[0,0,0,0];const o=Math.floor(r[0]),l=Math.floor(r[1]);let h=Math.max(Math.ceil(r[2])-o,1),d=Math.max(Math.ceil(r[3])-l,1),u=1,p=1;h>c&&(u=h/c,h=c),d>c&&(p=d/c,d=c),this.current.startNewPathAndClipBox([0,0,h,d]);let g="groupAt"+this.groupLevel;t.smask&&(g+="_smask_"+this.smaskCounter++%2);const f=this.cachedCanvases.getCanvas(g,h,d),m=f.context;m.scale(1/u,1/p),m.translate(-o,-l),m.transform(...i),t.smask?this.smaskStack.push({canvas:f.canvas,context:m,offsetX:o,offsetY:l,scaleX:u,scaleY:p,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null}):(e.setTransform(1,0,0,1,0,0),e.translate(o,l),e.scale(u,p),e.save()),A(e,m),this.ctx=m,this.setGState([["BM","source-over"],["ca",1],["CA",1]]),this.groupStack.push(e),this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,i=this.groupStack.pop();if(this.ctx=i,this.ctx.imageSmoothingEnabled=!1,t.smask)this.tempSMask=this.smaskStack.pop(),this.restore();else{this.ctx.restore();const t=(0,s.getCurrentTransform)(this.ctx);this.restore(),this.ctx.save(),this.ctx.setTransform(...t);const i=n.Util.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t);this.ctx.drawImage(e.canvas,0,0),this.ctx.restore(),this.compose(i)}}beginAnnotation(t,e,i,r,a){if(this.#ce(),S(this.ctx),this.ctx.save(),this.save(),this.baseTransform&&this.ctx.setTransform(...this.baseTransform),Array.isArray(e)&&4===e.length){const r=e[2]-e[0],o=e[3]-e[1];if(a&&this.annotationCanvasMap){i=i.slice(),i[4]-=e[0],i[5]-=e[1],e=e.slice(),e[0]=e[1]=0,e[2]=r,e[3]=o;const[a,l]=n.Util.singularValueDecompose2dScale((0,s.getCurrentTransform)(this.ctx)),{viewportScale:c}=this,h=Math.ceil(r*this.outputScaleX*c),d=Math.ceil(o*this.outputScaleY*c);this.annotationCanvas=this.canvasFactory.create(h,d);const{canvas:u,context:p}=this.annotationCanvas;this.annotationCanvasMap.set(t,u),this.annotationCanvas.savedCtx=this.ctx,this.ctx=p,this.ctx.save(),this.ctx.setTransform(a,0,0,-l,0,o*l),S(this.ctx)}else S(this.ctx),this.ctx.rect(e[0],e[1],r,o),this.ctx.clip(),this.endPath()}this.current=new v(this.ctx.canvas.width,this.ctx.canvas.height),this.transform(...i),this.transform(...r)}endAnnotation(){this.annotationCanvas&&(this.ctx.restore(),this.#he(),this.ctx=this.annotationCanvas.savedCtx,delete this.annotationCanvas.savedCtx,delete this.annotationCanvas)}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;t=this.getObject(t.data,t),t.count=e;const i=this.ctx,n=this.processingType3;if(n&&(void 0===n.compiled&&(n.compiled=b(t)),n.compiled))return void n.compiled(i);const s=this._createMaskCanvas(t),r=s.canvas;i.save(),i.setTransform(1,0,0,1,0,0),i.drawImage(r,s.offsetX,s.offsetY),i.restore(),this.compose()}paintImageMaskXObjectRepeat(t,e){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4?arguments[4]:void 0,o=arguments.length>5?arguments[5]:void 0;if(!this.contentVisible)return;t=this.getObject(t.data,t);const l=this.ctx;l.save();const c=(0,s.getCurrentTransform)(l);l.transform(e,i,r,a,0,0);const h=this._createMaskCanvas(t);l.setTransform(1,0,0,1,h.offsetX-c[4],h.offsetY-c[5]);for(let s=0,d=o.length;s<d;s+=2){const t=n.Util.transform(c,[e,i,r,a,o[s],o[s+1]]),[d,u]=n.Util.applyTransform([0,0],t);l.drawImage(h.canvas,d,u)}l.restore(),this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,i=this.current.fillColor,n=this.current.patternFill;for(const a of t){const{data:t,width:o,height:l,transform:c}=a,h=this.cachedCanvases.getCanvas("maskCanvas",o,l),d=h.context;d.save();const u=this.getObject(t,a);y(d,u),d.globalCompositeOperation="source-in",d.fillStyle=n?i.getPattern(d,this,(0,s.getCurrentTransformInverse)(e),r.PathType.FILL):i,d.fillRect(0,0,o,l),d.restore(),e.save(),e.transform(...c),e.scale(1,-1),m(e,h.canvas,0,0,o,l,0,-1,1,1),e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):(0,n.warn)("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,i,s){if(!this.contentVisible)return;const r=this.getObject(t);if(!r)return void(0,n.warn)("Dependent image isn't ready yet");const a=r.width,o=r.height,l=[];for(let n=0,c=s.length;n<c;n+=2)l.push({transform:[e,0,0,i,s[n],s[n+1]],x:0,y:0,w:a,h:o});this.paintInlineImageXObjectGroup(r,l)}applyTransferMapsToCanvas(t){return"none"!==this.current.transferMaps&&(t.filter=this.current.transferMaps,t.drawImage(t.canvas,0,0),t.filter="none"),t.canvas}applyTransferMapsToBitmap(t){if("none"===this.current.transferMaps)return t.bitmap;const{bitmap:e,width:i,height:n}=t,s=this.cachedCanvases.getCanvas("inlineImage",i,n),r=s.context;return r.filter=this.current.transferMaps,r.drawImage(e,0,0),r.filter="none",s.canvas}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,i=t.height,r=this.ctx;if(this.save(),!n.isNodeJS){const{filter:t}=r;"none"!==t&&""!==t&&(r.filter="none")}let a;if(r.scale(1/e,-1/i),t.bitmap)a=this.applyTransferMapsToBitmap(t);else if("function"===typeof HTMLElement&&t instanceof HTMLElement||!t.data)a=t;else{const n=this.cachedCanvases.getCanvas("inlineImage",e,i),s=n.context;_(s,t),a=this.applyTransferMapsToCanvas(s)}const o=this._scaleImage(a,(0,s.getCurrentTransformInverse)(r));r.imageSmoothingEnabled=P((0,s.getCurrentTransform)(r),t.interpolate),m(r,o.img,0,0,o.paintWidth,o.paintHeight,0,-i,e,i),this.compose(),this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const i=this.ctx;let n;if(t.bitmap)n=t.bitmap;else{const e=t.width,i=t.height,s=this.cachedCanvases.getCanvas("inlineImage",e,i),r=s.context;_(r,t),n=this.applyTransferMapsToCanvas(r)}for(const s of e)i.save(),i.transform(...s.transform),i.scale(1,-1),m(i,n,s.x,s.y,s.w,s.h,0,-1,1,1),i.restore();this.compose()}paintSolidColorImageMask(){this.contentVisible&&(this.ctx.fillRect(0,0,1,1),this.compose())}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0}),this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop(),this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath(),this.pendingClip||this.compose(t);const i=this.ctx;this.pendingClip&&(e||(this.pendingClip===F?i.clip("evenodd"):i.clip()),this.pendingClip=null),this.current.startNewPathAndClipBox(this.current.clipBox),i.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=(0,s.getCurrentTransform)(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),i=Math.hypot(t[0],t[2]),n=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(i,n)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(-1===this._cachedScaleForStroking[0]){const{lineWidth:t}=this.current,{a:e,b:i,c:n,d:s}=this.ctx.getTransform();let r,a;if(0===i&&0===n){const i=Math.abs(e),n=Math.abs(s);if(i===n)if(0===t)r=a=1/i;else{const e=i*t;r=a=e<1?1/e:1}else if(0===t)r=1/i,a=1/n;else{const e=i*t,s=n*t;r=e<1?1/e:1,a=s<1?1/s:1}}else{const o=Math.abs(e*s-i*n),l=Math.hypot(e,i),c=Math.hypot(n,s);if(0===t)r=c/o,a=l/o;else{const e=t*o;r=c>e?c/e:1,a=l>e?l/e:1}}this._cachedScaleForStroking[0]=r,this._cachedScaleForStroking[1]=a}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:i}=this.current,[n,s]=this.getScaleForStroking();if(e.lineWidth=i||1,1===n&&1===s)return void e.stroke();const r=e.getLineDash();if(t&&e.save(),e.scale(n,s),r.length>0){const t=Math.max(n,s);e.setLineDash(r.map((e=>e/t))),e.lineDashOffset/=t}e.stroke(),t&&e.restore()}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}e.CanvasGraphics=D;for(const I in n.OPS)void 0!==D.prototype[I]&&(D.prototype[n.OPS[I]]=D.prototype[I])},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TilingPattern=e.PathType=void 0,e.getShadingPattern=p,i(2);var n=i(1),s=i(168);const r={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};function a(t,e){if(!e)return;const i=e[2]-e[0],n=e[3]-e[1],s=new Path2D;s.rect(e[0],e[1],i,n),t.clip(s)}e.PathType=r;class o{constructor(){this.constructor===o&&(0,n.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,n.unreachable)("Abstract method `getPattern` called.")}}class l extends o{constructor(t){super(),this._type=t[1],this._bbox=t[2],this._colorStops=t[3],this._p0=t[4],this._p1=t[5],this._r0=t[6],this._r1=t[7],this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const i of this._colorStops)e.addColorStop(i[0],i[1]);return e}getPattern(t,e,i,o){let l;if(o===r.STROKE||o===r.FILL){const r=e.current.getClippedPathBoundingBox(o,(0,s.getCurrentTransform)(t))||[0,0,0,0],c=Math.ceil(r[2]-r[0])||1,h=Math.ceil(r[3]-r[1])||1,d=e.cachedCanvases.getCanvas("pattern",c,h,!0),u=d.context;u.clearRect(0,0,u.canvas.width,u.canvas.height),u.beginPath(),u.rect(0,0,u.canvas.width,u.canvas.height),u.translate(-r[0],-r[1]),i=n.Util.transform(i,[1,0,0,1,r[0],r[1]]),u.transform(...e.baseTransform),this.matrix&&u.transform(...this.matrix),a(u,this._bbox),u.fillStyle=this._createGradient(u),u.fill(),l=t.createPattern(d.canvas,"no-repeat");const p=new DOMMatrix(i);l.setTransform(p)}else a(t,this._bbox),l=this._createGradient(t);return l}}function c(t,e,i,n,s,r,a,o){const l=e.coords,c=e.colors,h=t.data,d=4*t.width;let u;l[i+1]>l[n+1]&&(u=i,i=n,n=u,u=r,r=a,a=u),l[n+1]>l[s+1]&&(u=n,n=s,s=u,u=a,a=o,o=u),l[i+1]>l[n+1]&&(u=i,i=n,n=u,u=r,r=a,a=u);const p=(l[i]+e.offsetX)*e.scaleX,g=(l[i+1]+e.offsetY)*e.scaleY,f=(l[n]+e.offsetX)*e.scaleX,m=(l[n+1]+e.offsetY)*e.scaleY,b=(l[s]+e.offsetX)*e.scaleX,v=(l[s+1]+e.offsetY)*e.scaleY;if(g>=v)return;const _=c[r],y=c[r+1],A=c[r+2],S=c[a],x=c[a+1],E=c[a+2],w=c[o],C=c[o+1],T=c[o+2],P=Math.round(g),k=Math.round(v);let M,R,F,D,I,O,L,N;for(let j=P;j<=k;j++){if(j<m){const t=j<g?0:(g-j)/(g-m);M=p-(p-f)*t,R=_-(_-S)*t,F=y-(y-x)*t,D=A-(A-E)*t}else{let t;t=j>v?1:m===v?0:(m-j)/(m-v),M=f-(f-b)*t,R=S-(S-w)*t,F=x-(x-C)*t,D=E-(E-T)*t}let t;t=j<g?0:j>v?1:(g-j)/(g-v),I=p-(p-b)*t,O=_-(_-w)*t,L=y-(y-C)*t,N=A-(A-T)*t;const e=Math.round(Math.min(M,I)),i=Math.round(Math.max(M,I));let n=d*j+4*e;for(let s=e;s<=i;s++)t=(M-s)/(M-I),t<0?t=0:t>1&&(t=1),h[n++]=R-(R-O)*t|0,h[n++]=F-(F-L)*t|0,h[n++]=D-(D-N)*t|0,h[n++]=255}}function h(t,e,i){const n=e.coords,s=e.colors;let r,a;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(n.length/o)-1,h=o-1;for(r=0;r<l;r++){let e=r*o;for(let r=0;r<h;r++,e++)c(t,i,n[e],n[e+1],n[e+o],s[e],s[e+1],s[e+o]),c(t,i,n[e+o+1],n[e+1],n[e+o],s[e+o+1],s[e+1],s[e+o])}break;case"triangles":for(r=0,a=n.length;r<a;r+=3)c(t,i,n[r],n[r+1],n[r+2],s[r],s[r+1],s[r+2]);break;default:throw new Error("illegal figure")}}class d extends o{constructor(t){super(),this._coords=t[2],this._colors=t[3],this._figures=t[4],this._bounds=t[5],this._bbox=t[7],this._background=t[8],this.matrix=null}_createMeshCanvas(t,e,i){const n=1.1,s=3e3,r=2,a=Math.floor(this._bounds[0]),o=Math.floor(this._bounds[1]),l=Math.ceil(this._bounds[2])-a,c=Math.ceil(this._bounds[3])-o,d=Math.min(Math.ceil(Math.abs(l*t[0]*n)),s),u=Math.min(Math.ceil(Math.abs(c*t[1]*n)),s),p=l/d,g=c/u,f={coords:this._coords,colors:this._colors,offsetX:-a,offsetY:-o,scaleX:1/p,scaleY:1/g},m=d+2*r,b=u+2*r,v=i.getCanvas("mesh",m,b,!1),_=v.context,y=_.createImageData(d,u);if(e){const t=y.data;for(let i=0,n=t.length;i<n;i+=4)t[i]=e[0],t[i+1]=e[1],t[i+2]=e[2],t[i+3]=255}for(const S of this._figures)h(y,S,f);_.putImageData(y,r,r);const A=v.canvas;return{canvas:A,offsetX:a-r*p,offsetY:o-r*g,scaleX:p,scaleY:g}}getPattern(t,e,i,o){let l;if(a(t,this._bbox),o===r.SHADING)l=n.Util.singularValueDecompose2dScale((0,s.getCurrentTransform)(t));else if(l=n.Util.singularValueDecompose2dScale(e.baseTransform),this.matrix){const t=n.Util.singularValueDecompose2dScale(this.matrix);l=[l[0]*t[0],l[1]*t[1]]}const c=this._createMeshCanvas(l,o===r.SHADING?null:this._background,e.cachedCanvases);return o!==r.SHADING&&(t.setTransform(...e.baseTransform),this.matrix&&t.transform(...this.matrix)),t.translate(c.offsetX,c.offsetY),t.scale(c.scaleX,c.scaleY),t.createPattern(c.canvas,"no-repeat")}}class u extends o{getPattern(){return"hotpink"}}function p(t){switch(t[0]){case"RadialAxial":return new l(t);case"Mesh":return new d(t);case"Dummy":return new u}throw new Error(`Unknown IR type: ${t[0]}`)}const g={COLORED:1,UNCOLORED:2};class f{static MAX_PATTERN_SIZE=3e3;constructor(t,e,i,n,s){this.operatorList=t[2],this.matrix=t[3]||[1,0,0,1,0,0],this.bbox=t[4],this.xstep=t[5],this.ystep=t[6],this.paintType=t[7],this.tilingType=t[8],this.color=e,this.ctx=i,this.canvasGraphicsFactory=n,this.baseTransform=s}createPatternCanvas(t){const e=this.operatorList,i=this.bbox,r=this.xstep,a=this.ystep,o=this.paintType,l=this.tilingType,c=this.color,h=this.canvasGraphicsFactory;(0,n.info)("TilingType: "+l);const d=i[0],u=i[1],p=i[2],g=i[3],f=n.Util.singularValueDecompose2dScale(this.matrix),m=n.Util.singularValueDecompose2dScale(this.baseTransform),b=[f[0]*m[0],f[1]*m[1]],v=this.getSizeAndScale(r,this.ctx.canvas.width,b[0]),_=this.getSizeAndScale(a,this.ctx.canvas.height,b[1]),y=t.cachedCanvases.getCanvas("pattern",v.size,_.size,!0),A=y.context,S=h.createCanvasGraphics(A);S.groupLevel=t.groupLevel,this.setFillAndStrokeStyleToContext(S,o,c);let x=d,E=u,w=p,C=g;return d<0&&(x=0,w+=Math.abs(d)),u<0&&(E=0,C+=Math.abs(u)),A.translate(-v.scale*x,-_.scale*E),S.transform(v.scale,0,0,_.scale,0,0),A.save(),this.clipBbox(S,x,E,w,C),S.baseTransform=(0,s.getCurrentTransform)(S.ctx),S.executeOperatorList(e),S.endDrawing(),{canvas:y.canvas,scaleX:v.scale,scaleY:_.scale,offsetX:x,offsetY:E}}getSizeAndScale(t,e,i){t=Math.abs(t);const n=Math.max(f.MAX_PATTERN_SIZE,e);let s=Math.ceil(t*i);return s>=n?s=n:i=s/t,{scale:i,size:s}}clipBbox(t,e,i,n,r){const a=n-e,o=r-i;t.ctx.rect(e,i,a,o),t.current.updateRectMinMax((0,s.getCurrentTransform)(t.ctx),[e,i,n,r]),t.clip(),t.endPath()}setFillAndStrokeStyleToContext(t,e,i){const s=t.ctx,r=t.current;switch(e){case g.COLORED:const t=this.ctx;s.fillStyle=t.fillStyle,s.strokeStyle=t.strokeStyle,r.fillColor=t.fillStyle,r.strokeColor=t.strokeStyle;break;case g.UNCOLORED:const a=n.Util.makeHexColor(i[0],i[1],i[2]);s.fillStyle=a,s.strokeStyle=a,r.fillColor=a,r.strokeColor=a;break;default:throw new n.FormatError(`Unsupported paint type: ${e}`)}}getPattern(t,e,i,s){let a=i;s!==r.SHADING&&(a=n.Util.transform(a,e.baseTransform),this.matrix&&(a=n.Util.transform(a,this.matrix)));const o=this.createPatternCanvas(e);let l=new DOMMatrix(a);l=l.translate(o.offsetX,o.offsetY),l=l.scale(1/o.scaleX,1/o.scaleY);const c=t.createPattern(o.canvas,"repeat");return c.setTransform(l),c}}e.TilingPattern=f},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.convertBlackAndWhiteToRGBA=r,e.convertToRGBA=s,e.grayToRGBA=o,i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123);var n=i(1);function s(t){switch(t.kind){case n.ImageKind.GRAYSCALE_1BPP:return r(t);case n.ImageKind.RGB_24BPP:return a(t)}return null}function r(t){let{src:e,srcPos:i=0,dest:s,width:r,height:a,nonBlackColor:o=4294967295,inverseDecode:l=!1}=t;const c=n.FeatureTest.isLittleEndian?4278190080:255,[h,d]=l?[o,c]:[c,o],u=r>>3,p=7&r,g=e.length;s=new Uint32Array(s.buffer);let f=0;for(let n=0;n<a;n++){for(const n=i+u;i<n;i++){const t=i<g?e[i]:255;s[f++]=128&t?d:h,s[f++]=64&t?d:h,s[f++]=32&t?d:h,s[f++]=16&t?d:h,s[f++]=8&t?d:h,s[f++]=4&t?d:h,s[f++]=2&t?d:h,s[f++]=1&t?d:h}if(0===p)continue;const t=i<g?e[i++]:255;for(let e=0;e<p;e++)s[f++]=t&1<<7-e?d:h}return{srcPos:i,destPos:f}}function a(t){let{src:e,srcPos:i=0,dest:s,destPos:r=0,width:a,height:o}=t,l=0;const c=e.length>>2,h=new Uint32Array(e.buffer,i,c);if(n.FeatureTest.isLittleEndian){for(;l<c-2;l+=3,r+=4){const t=h[l],e=h[l+1],i=h[l+2];s[r]=4278190080|t,s[r+1]=t>>>24|e<<8|4278190080,s[r+2]=e>>>16|i<<16|4278190080,s[r+3]=i>>>8|4278190080}for(let t=4*l,i=e.length;t<i;t+=3)s[r++]=e[t]|e[t+1]<<8|e[t+2]<<16|4278190080}else{for(;l<c-2;l+=3,r+=4){const t=h[l],e=h[l+1],i=h[l+2];s[r]=255|t,s[r+1]=t<<24|e>>>8|255,s[r+2]=e<<16|i>>>16|255,s[r+3]=i<<8|255}for(let t=4*l,i=e.length;t<i;t+=3)s[r++]=e[t]<<24|e[t+1]<<16|e[t+2]<<8|255}return{srcPos:i,destPos:r}}function o(t,e){if(n.FeatureTest.isLittleEndian)for(let i=0,n=t.length;i<n;i++)e[i]=65793*t[i]|4278190080;else for(let i=0,n=t.length;i<n;i++)e[i]=16843008*t[i]|255}},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.GlobalWorkerOptions=void 0;const i=Object.create(null);e.GlobalWorkerOptions=i,i.workerPort=null,i.workerSrc=""},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.MessageHandler=void 0,i(2);var n=i(1);const s={UNKNOWN:0,DATA:1,ERROR:2},r={UNKNOWN:0,CANCEL:1,CANCEL_COMPLETE:2,CLOSE:3,ENQUEUE:4,ERROR:5,PULL:6,PULL_COMPLETE:7,START_COMPLETE:8};function a(t){switch(t instanceof Error||"object"===typeof t&&null!==t||(0,n.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.'),t.name){case"AbortException":return new n.AbortException(t.message);case"MissingPDFException":return new n.MissingPDFException(t.message);case"PasswordException":return new n.PasswordException(t.message,t.code);case"UnexpectedResponseException":return new n.UnexpectedResponseException(t.message,t.status);case"UnknownErrorException":return new n.UnknownErrorException(t.message,t.details);default:return new n.UnknownErrorException(t.message,t.toString())}}class o{constructor(t,e,i){this.sourceName=t,this.targetName=e,this.comObj=i,this.callbackId=1,this.streamId=1,this.streamSinks=Object.create(null),this.streamControllers=Object.create(null),this.callbackCapabilities=Object.create(null),this.actionHandler=Object.create(null),this._onComObjOnMessage=t=>{const e=t.data;if(e.targetName!==this.sourceName)return;if(e.stream)return void this.#de(e);if(e.callback){const t=e.callbackId,i=this.callbackCapabilities[t];if(!i)throw new Error(`Cannot resolve callback ${t}`);if(delete this.callbackCapabilities[t],e.callback===s.DATA)i.resolve(e.data);else{if(e.callback!==s.ERROR)throw new Error("Unexpected callback case");i.reject(a(e.reason))}return}const n=this.actionHandler[e.action];if(!n)throw new Error(`Unknown action from worker: ${e.action}`);if(e.callbackId){const t=this.sourceName,r=e.sourceName;new Promise((function(t){t(n(e.data))})).then((function(n){i.postMessage({sourceName:t,targetName:r,callback:s.DATA,callbackId:e.callbackId,data:n})}),(function(n){i.postMessage({sourceName:t,targetName:r,callback:s.ERROR,callbackId:e.callbackId,reason:a(n)})}))}else e.streamId?this.#ue(e):n(e.data)},i.addEventListener("message",this._onComObjOnMessage)}on(t,e){const i=this.actionHandler;if(i[t])throw new Error(`There is already an actionName called "${t}"`);i[t]=e}send(t,e,i){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},i)}sendWithPromise(t,e,i){const s=this.callbackId++,r=new n.PromiseCapability;this.callbackCapabilities[s]=r;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:s,data:e},i)}catch(a){r.reject(a)}return r.promise}sendWithStream(t,e,i,s){const o=this.streamId++,l=this.sourceName,c=this.targetName,h=this.comObj;return new ReadableStream({start:i=>{const r=new n.PromiseCapability;return this.streamControllers[o]={controller:i,startCall:r,pullCall:null,cancelCall:null,isClosed:!1},h.postMessage({sourceName:l,targetName:c,action:t,streamId:o,data:e,desiredSize:i.desiredSize},s),r.promise},pull:t=>{const e=new n.PromiseCapability;return this.streamControllers[o].pullCall=e,h.postMessage({sourceName:l,targetName:c,stream:r.PULL,streamId:o,desiredSize:t.desiredSize}),e.promise},cancel:t=>{(0,n.assert)(t instanceof Error,"cancel must have a valid reason");const e=new n.PromiseCapability;return this.streamControllers[o].cancelCall=e,this.streamControllers[o].isClosed=!0,h.postMessage({sourceName:l,targetName:c,stream:r.CANCEL,streamId:o,reason:a(t)}),e.promise}},i)}#ue(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,o=this.comObj,l=this,c=this.actionHandler[t.action],h={enqueue(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1,l=arguments.length>2?arguments[2]:void 0;if(this.isCancelled)return;const c=this.desiredSize;this.desiredSize-=a,c>0&&this.desiredSize<=0&&(this.sinkCapability=new n.PromiseCapability,this.ready=this.sinkCapability.promise),o.postMessage({sourceName:i,targetName:s,stream:r.ENQUEUE,streamId:e,chunk:t},l)},close(){this.isCancelled||(this.isCancelled=!0,o.postMessage({sourceName:i,targetName:s,stream:r.CLOSE,streamId:e}),delete l.streamSinks[e])},error(t){(0,n.assert)(t instanceof Error,"error must have a valid reason"),this.isCancelled||(this.isCancelled=!0,o.postMessage({sourceName:i,targetName:s,stream:r.ERROR,streamId:e,reason:a(t)}))},sinkCapability:new n.PromiseCapability,onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};h.sinkCapability.resolve(),h.ready=h.sinkCapability.promise,this.streamSinks[e]=h,new Promise((function(e){e(c(t.data,h))})).then((function(){o.postMessage({sourceName:i,targetName:s,stream:r.START_COMPLETE,streamId:e,success:!0})}),(function(t){o.postMessage({sourceName:i,targetName:s,stream:r.START_COMPLETE,streamId:e,reason:a(t)})}))}#de(t){const e=t.streamId,i=this.sourceName,s=t.sourceName,o=this.comObj,l=this.streamControllers[e],c=this.streamSinks[e];switch(t.stream){case r.START_COMPLETE:t.success?l.startCall.resolve():l.startCall.reject(a(t.reason));break;case r.PULL_COMPLETE:t.success?l.pullCall.resolve():l.pullCall.reject(a(t.reason));break;case r.PULL:if(!c){o.postMessage({sourceName:i,targetName:s,stream:r.PULL_COMPLETE,streamId:e,success:!0});break}c.desiredSize<=0&&t.desiredSize>0&&c.sinkCapability.resolve(),c.desiredSize=t.desiredSize,new Promise((function(t){t(c.onPull?.())})).then((function(){o.postMessage({sourceName:i,targetName:s,stream:r.PULL_COMPLETE,streamId:e,success:!0})}),(function(t){o.postMessage({sourceName:i,targetName:s,stream:r.PULL_COMPLETE,streamId:e,reason:a(t)})}));break;case r.ENQUEUE:if((0,n.assert)(l,"enqueue should have stream controller"),l.isClosed)break;l.controller.enqueue(t.chunk);break;case r.CLOSE:if((0,n.assert)(l,"close should have stream controller"),l.isClosed)break;l.isClosed=!0,l.controller.close(),this.#pe(l,e);break;case r.ERROR:(0,n.assert)(l,"error should have stream controller"),l.controller.error(a(t.reason)),this.#pe(l,e);break;case r.CANCEL_COMPLETE:t.success?l.cancelCall.resolve():l.cancelCall.reject(a(t.reason)),this.#pe(l,e);break;case r.CANCEL:if(!c)break;new Promise((function(e){e(c.onCancel?.(a(t.reason)))})).then((function(){o.postMessage({sourceName:i,targetName:s,stream:r.CANCEL_COMPLETE,streamId:e,success:!0})}),(function(t){o.postMessage({sourceName:i,targetName:s,stream:r.CANCEL_COMPLETE,streamId:e,reason:a(t)})})),c.sinkCapability.reject(a(t.reason)),c.isCancelled=!0,delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async#pe(t,e){await Promise.allSettled([t.startCall?.promise,t.pullCall?.promise,t.cancelCall?.promise]),delete this.streamControllers[e]}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}e.MessageHandler=o},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.Metadata=void 0;var n=i(1);class s{#ge;#fe;constructor(t){let{parsedData:e,rawData:i}=t;this.#ge=e,this.#fe=i}getRaw(){return this.#fe}get(t){return this.#ge.get(t)??null}getAll(){return(0,n.objectFromMap)(this.#ge)}has(t){return this.#ge.has(t)}}e.Metadata=s},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.OptionalContentConfig=void 0;var n=i(1),s=i(170);const r=Symbol("INTERNAL");class a{#me=!0;constructor(t,e){this.name=t,this.intent=e}get visible(){return this.#me}_setVisible(t,e){t!==r&&(0,n.unreachable)("Internal method `_setVisible` called."),this.#me=e}}class o{#be=null;#ve=new Map;#_e=null;#ye=null;constructor(t){if(this.name=null,this.creator=null,null!==t){this.name=t.name,this.creator=t.creator,this.#ye=t.order;for(const e of t.groups)this.#ve.set(e.id,new a(e.name,e.intent));if("OFF"===t.baseState)for(const t of this.#ve.values())t._setVisible(r,!1);for(const e of t.on)this.#ve.get(e)._setVisible(r,!0);for(const e of t.off)this.#ve.get(e)._setVisible(r,!1);this.#_e=this.getHash()}}#Ae(t){const e=t.length;if(e<2)return!0;const i=t[0];for(let s=1;s<e;s++){const e=t[s];let r;if(Array.isArray(e))r=this.#Ae(e);else{if(!this.#ve.has(e))return(0,n.warn)(`Optional content group not found: ${e}`),!0;r=this.#ve.get(e).visible}switch(i){case"And":if(!r)return!1;break;case"Or":if(r)return!0;break;case"Not":return!r;default:return!0}}return"And"===i}isVisible(t){if(0===this.#ve.size)return!0;if(!t)return(0,n.warn)("Optional content group not defined."),!0;if("OCG"===t.type)return this.#ve.has(t.id)?this.#ve.get(t.id).visible:((0,n.warn)(`Optional content group not found: ${t.id}`),!0);if("OCMD"===t.type){if(t.expression)return this.#Ae(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#ve.has(e))return(0,n.warn)(`Optional content group not found: ${e}`),!0;if(this.#ve.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#ve.has(e))return(0,n.warn)(`Optional content group not found: ${e}`),!0;if(!this.#ve.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#ve.has(e))return(0,n.warn)(`Optional content group not found: ${e}`),!0;if(!this.#ve.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#ve.has(e))return(0,n.warn)(`Optional content group not found: ${e}`),!0;if(this.#ve.get(e).visible)return!1}return!0}return(0,n.warn)(`Unknown optional content policy ${t.policy}.`),!0}return(0,n.warn)(`Unknown group type ${t.type}.`),!0}setVisibility(t){let e=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];this.#ve.has(t)?(this.#ve.get(t)._setVisible(r,!!e),this.#be=null):(0,n.warn)(`Optional content group not found: ${t}`)}get hasInitialVisibility(){return null===this.#_e||this.getHash()===this.#_e}getOrder(){return this.#ve.size?this.#ye?this.#ye.slice():[...this.#ve.keys()]:null}getGroups(){return this.#ve.size>0?(0,n.objectFromMap)(this.#ve):null}getGroup(t){return this.#ve.get(t)||null}getHash(){if(null!==this.#be)return this.#be;const t=new s.MurmurHash3_64;for(const[e,i]of this.#ve)t.update(`${e}:${i.visible}`);return this.#be=t.hexdigest()}}e.OptionalContentConfig=o},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFDataTransportStream=void 0,i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123),i(89);var n=i(1),s=i(168);class r{constructor(t,e){let{length:i,initialData:s,progressiveDone:r=!1,contentDispositionFilename:a=null,disableRange:o=!1,disableStream:l=!1}=t;if((0,n.assert)(e,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.'),this._queuedChunks=[],this._progressiveDone=r,this._contentDispositionFilename=a,s?.length>0){const t=s instanceof Uint8Array&&s.byteLength===s.buffer.byteLength?s.buffer:new Uint8Array(s).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=e,this._isStreamingSupported=!l,this._isRangeSupported=!o,this._contentLength=i,this._fullRequestReader=null,this._rangeReaders=[],this._pdfDataRangeTransport.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})})),this._pdfDataRangeTransport.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})})),this._pdfDataRangeTransport.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})})),this._pdfDataRangeTransport.addProgressiveDoneListener((()=>{this._onProgressiveDone()})),this._pdfDataRangeTransport.transportReady()}_onReceiveData(t){let{begin:e,chunk:i}=t;const s=i instanceof Uint8Array&&i.byteLength===i.buffer.byteLength?i.buffer:new Uint8Array(i).buffer;if(void 0===e)this._fullRequestReader?this._fullRequestReader._enqueue(s):this._queuedChunks.push(s);else{const t=this._rangeReaders.some((function(t){return t._begin===e&&(t._enqueue(s),!0)}));(0,n.assert)(t,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){void 0===t.total?this._rangeReaders[0]?.onProgress?.({loaded:t.loaded}):this._fullRequestReader?.onProgress?.({loaded:t.loaded,total:t.total})}_onProgressiveDone(){this._fullRequestReader?.progressiveDone(),this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){(0,n.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;return this._queuedChunks=null,new a(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new o(this,t,e);return this._pdfDataRangeTransport.requestDataRange(t,e),this._rangeReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}}e.PDFDataTransportStream=r;class a{constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null;this._stream=t,this._done=i||!1,this._filename=(0,s.isPdfFile)(n)?n:null,this._queuedChunks=e||[],this._loaded=0;for(const s of this._queuedChunks)this._loaded+=s.byteLength;this._requests=[],this._headersReady=Promise.resolve(),t._fullRequestReader=this,this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){const e=this._requests.shift();e.resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){const t=this._queuedChunks.shift();return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=new n.PromiseCapability;return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class o{constructor(t,e,i){this._stream=t,this._begin=e,this._end=i,this._queuedChunk=null,this._requests=[],this._done=!1,this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{const e=this._requests.shift();e.resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0,this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=new n.PromiseCapability;return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._stream._removeRangeReader(this)}}},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFFetchStream=void 0,i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123),i(89);var n=i(1),s=i(182);function r(t,e,i){return{method:"GET",headers:t,signal:i.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function a(t){const e=new Headers;for(const i in t){const n=t[i];void 0!==n&&e.append(i,n)}return e}function o(t){return t instanceof Uint8Array?t.buffer:"object"===typeof t&&null!==t&&void 0!==t.byteLength?t:((0,n.warn)(`getArrayBuffer - unexpected data format: ${t}`),new Uint8Array(t).buffer)}class l{constructor(t){this.source=t,this.isHttp=/^https?:/i.test(t.url),this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return(0,n.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once."),this._fullRequestReader=new c(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=new h(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}e.PDFFetchStream=l;class c{constructor(t){this._stream=t,this._reader=null,this._loaded=0,this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1,this._contentLength=e.length,this._headersCapability=new n.PromiseCapability,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._abortController=new AbortController,this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._headers=a(this._stream.httpHeaders);const i=e.url;fetch(i,r(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,s.validateResponseStatus)(t.status))throw(0,s.createResponseStatusError)(t.status,i);this._reader=t.body.getReader(),this._headersCapability.resolve();const e=e=>t.headers.get(e),{allowRangeRequests:r,suggestedLength:a}=(0,s.validateRangeRequestCapabilities)({getResponseHeader:e,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=r,this._contentLength=a||this._contentLength,this._filename=(0,s.extractFilenameFromHeader)(e),!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new n.AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject),this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded,total:this._contentLength}),{value:o(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}class h{constructor(t,e,i){this._stream=t,this._reader=null,this._loaded=0;const o=t.source;this._withCredentials=o.withCredentials||!1,this._readCapability=new n.PromiseCapability,this._isStreamingSupported=!o.disableStream,this._abortController=new AbortController,this._headers=a(this._stream.httpHeaders),this._headers.append("Range",`bytes=${e}-${i-1}`);const l=o.url;fetch(l,r(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,s.validateResponseStatus)(t.status))throw(0,s.createResponseStatusError)(t.status,l);this._readCapability.resolve(),this._reader=t.body.getReader()})).catch(this._readCapability.reject),this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();return e?{value:t,done:e}:(this._loaded+=t.byteLength,this.onProgress?.({loaded:this._loaded}),{value:o(t),done:!1})}cancel(t){this._reader?.cancel(t),this._abortController.abort()}}},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.createResponseStatusError=l,e.extractFilenameFromHeader=o,e.validateRangeRequestCapabilities=a,e.validateResponseStatus=c;var n=i(1),s=i(183),r=i(168);function a(t){let{getResponseHeader:e,isHttp:i,rangeChunkSize:n,disableRange:s}=t;const r={allowRangeRequests:!1,suggestedLength:void 0},a=parseInt(e("Content-Length"),10);if(!Number.isInteger(a))return r;if(r.suggestedLength=a,a<=2*n)return r;if(s||!i)return r;if("bytes"!==e("Accept-Ranges"))return r;const o=e("Content-Encoding")||"identity";return"identity"!==o||(r.allowRangeRequests=!0),r}function o(t){const e=t("Content-Disposition");if(e){let t=(0,s.getFilenameFromContentDispositionHeader)(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch{}if((0,r.isPdfFile)(t))return t}return null}function l(t,e){return 404===t||0===t&&e.startsWith("file:")?new n.MissingPDFException('Missing PDF "'+e+'".'):new n.UnexpectedResponseException(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t)}function c(t){return 200===t||206===t}},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getFilenameFromContentDispositionHeader=s,i(89),i(149);var n=i(1);function s(t){let e=!0,i=s("filename\\*","i").exec(t);if(i){i=i[1];let t=l(i);return t=unescape(t),t=c(t),t=h(t),a(t)}if(i=o(t),i){const t=h(i);return a(t)}if(i=s("filename","i").exec(t),i){i=i[1];let t=l(i);return t=h(t),a(t)}function s(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function r(t,i){if(t){if(!/^[\x00-\xFF]+$/.test(i))return i;try{const s=new TextDecoder(t,{fatal:!0}),r=(0,n.stringToBytes)(i);i=s.decode(r),e=!1}catch{}}return i}function a(t){return e&&/[\x80-\xff]/.test(t)&&(t=r("utf-8",t),e&&(t=r("iso-8859-1",t))),t}function o(t){const e=[];let i;const n=s("filename\\*((?!0\\d)\\d+)(\\*?)","ig");while(null!==(i=n.exec(t))){let[,t,n,s]=i;if(t=parseInt(t,10),t in e){if(0===t)break}else e[t]=[n,s]}const r=[];for(let s=0;s<e.length;++s){if(!(s in e))break;let[t,i]=e[s];i=l(i),t&&(i=unescape(i),0===s&&(i=c(i))),r.push(i)}return r.join("")}function l(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const i=e[t].indexOf('"');-1!==i&&(e[t]=e[t].slice(0,i),e.length=t+1),e[t]=e[t].replaceAll(/\\(.)/g,"$1")}t=e.join('"')}return t}function c(t){const e=t.indexOf("'");if(-1===e)return t;const i=t.slice(0,e),n=t.slice(e+1),s=n.replace(/^[^']*'/,"");return r(i,s)}function h(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replaceAll(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,i,n){if("q"===i||"Q"===i)return n=n.replaceAll("_"," "),n=n.replaceAll(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})),r(e,n);try{n=atob(n)}catch{}return r(e,n)}))}return""}},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFNetworkStream=void 0,i(89);var n=i(1),s=i(182);const r=200,a=206;function o(t){const e=t.response;return"string"!==typeof e?e:(0,n.stringToBytes)(e).buffer}class l{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.url=t,this.isHttp=/^https?:/i.test(t),this.httpHeaders=this.isHttp&&e.httpHeaders||Object.create(null),this.withCredentials=e.withCredentials||!1,this.currXhrId=0,this.pendingRequests=Object.create(null)}requestRange(t,e,i){const n={begin:t,end:e};for(const s in i)n[s]=i[s];return this.request(n)}requestFull(t){return this.request(t)}request(t){const e=new XMLHttpRequest,i=this.currXhrId++,n=this.pendingRequests[i]={xhr:e};e.open("GET",this.url),e.withCredentials=this.withCredentials;for(const s in this.httpHeaders){const t=this.httpHeaders[s];void 0!==t&&e.setRequestHeader(s,t)}return this.isHttp&&"begin"in t&&"end"in t?(e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`),n.expectedStatus=a):n.expectedStatus=r,e.responseType="arraybuffer",t.onError&&(e.onerror=function(i){t.onError(e.status)}),e.onreadystatechange=this.onStateChange.bind(this,i),e.onprogress=this.onProgress.bind(this,i),n.onHeadersReceived=t.onHeadersReceived,n.onDone=t.onDone,n.onError=t.onError,n.onProgress=t.onProgress,e.send(null),i}onProgress(t,e){const i=this.pendingRequests[t];i&&i.onProgress?.(e)}onStateChange(t,e){const i=this.pendingRequests[t];if(!i)return;const n=i.xhr;if(n.readyState>=2&&i.onHeadersReceived&&(i.onHeadersReceived(),delete i.onHeadersReceived),4!==n.readyState)return;if(!(t in this.pendingRequests))return;if(delete this.pendingRequests[t],0===n.status&&this.isHttp)return void i.onError?.(n.status);const s=n.status||r,l=s===r&&i.expectedStatus===a;if(!l&&s!==i.expectedStatus)return void i.onError?.(n.status);const c=o(n);if(s===a){const t=n.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);i.onDone({begin:parseInt(e[1],10),chunk:c})}else c?i.onDone({begin:0,chunk:c}):i.onError?.(n.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t],e.abort()}}class c{constructor(t){this._source=t,this._manager=new l(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials}),this._rangeChunkSize=t.rangeChunkSize,this._fullRequestReader=null,this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){return(0,n.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once."),this._fullRequestReader=new h(this._manager,this._source),this._fullRequestReader}getRangeReader(t,e){const i=new d(this._manager,t,e);return i.onClosed=this._onRangeRequestReaderClosed.bind(this),this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}e.PDFNetworkStream=c;class h{constructor(t,e){this._manager=t;const i={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=e.url,this._fullRequestId=t.requestFull(i),this._headersReceivedCapability=new n.PromiseCapability,this._disableRange=e.disableRange||!1,this._contentLength=e.length,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!1,this._isRangeSupported=!1,this._cachedChunks=[],this._requests=[],this._done=!1,this._storedError=void 0,this._filename=null,this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t),i=t=>e.getResponseHeader(t),{allowRangeRequests:n,suggestedLength:r}=(0,s.validateRangeRequestCapabilities)({getResponseHeader:i,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});n&&(this._isRangeSupported=!0),this._contentLength=r||this._contentLength,this._filename=(0,s.extractFilenameFromHeader)(i),this._isRangeSupported&&this._manager.abortRequest(t),this._headersReceivedCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){const e=this._requests.shift();e.resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);if(this._done=!0,!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=(0,s.createResponseStatusError)(t,this._url),this._headersReceivedCapability.reject(this._storedError);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){const t=this._cachedChunks.shift();return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=new n.PromiseCapability;return this._requests.push(t),t.promise}cancel(t){this._done=!0,this._headersReceivedCapability.reject(t);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId),this._fullRequestReader=null}}class d{constructor(t,e,i){this._manager=t;const n={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url,this._requestId=t.requestRange(e,i,n),this._requests=[],this._queuedChunk=null,this._done=!1,this._storedError=void 0,this.onProgress=null,this.onClosed=null}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){const t=this._requests.shift();t.resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const i of this._requests)i.resolve({value:void 0,done:!0});this._requests.length=0,this._close()}_onError(t){this._storedError=(0,s.createResponseStatusError)(t,this._url);for(const e of this._requests)e.reject(this._storedError);this._requests.length=0,this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;return this._queuedChunk=null,{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=new n.PromiseCapability;return this._requests.push(t),t.promise}cancel(t){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0,this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId),this._close()}}},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.PDFNodeStream=void 0,i(89),i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123);var n=i(1),s=i(182);const r=/^file:\/\/\/[a-zA-Z]:\//;function a(t){const e=require("url"),i=e.parse(t);return"file:"===i.protocol||i.host?i:/^[a-z]:[/\\]/i.test(t)?e.parse(`file:///${t}`):(i.host||(i.protocol="file:"),i)}class o{constructor(t){this.source=t,this.url=a(t.url),this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol,this.isFsUrl="file:"===this.url.protocol,this.httpHeaders=this.isHttp&&t.httpHeaders||{},this._fullRequestReader=null,this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){return(0,n.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once."),this._fullRequestReader=this.isFsUrl?new p(this):new d(this),this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const i=this.isFsUrl?new g(this,t,e):new u(this,t,e);return this._rangeRequestReaders.push(i),i}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}}e.PDFNodeStream=o;class l{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null;const e=t.source;this._contentLength=e.length,this._loaded=0,this._filename=null,this._disableRange=e.disableRange||!1,this._rangeChunkSize=e.rangeChunkSize,this._rangeChunkSize||this._disableRange||(this._disableRange=!0),this._isStreamingSupported=!e.disableStream,this._isRangeSupported=!e.disableRange,this._readableStream=null,this._readCapability=new n.PromiseCapability,this._headersCapability=new n.PromiseCapability}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t)return this._readCapability=new n.PromiseCapability,this.read();this._loaded+=t.length,this.onProgress?.({loaded:this._loaded,total:this._contentLength});const e=new Uint8Array(t).buffer;return{value:e,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",(()=>{this._readCapability.resolve()})),t.on("end",(()=>{t.destroy(),this._done=!0,this._readCapability.resolve()})),t.on("error",(t=>{this._error(t)})),!this._isStreamingSupported&&this._isRangeSupported&&this._error(new n.AbortException("streaming is disabled")),this._storedError&&this._readableStream.destroy(this._storedError)}}class c{constructor(t){this._url=t.url,this._done=!1,this._storedError=null,this.onProgress=null,this._loaded=0,this._readableStream=null,this._readCapability=new n.PromiseCapability;const e=t.source;this._isStreamingSupported=!e.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){if(await this._readCapability.promise,this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t)return this._readCapability=new n.PromiseCapability,this.read();this._loaded+=t.length,this.onProgress?.({loaded:this._loaded});const e=new Uint8Array(t).buffer;return{value:e,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t,this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t,t.on("readable",(()=>{this._readCapability.resolve()})),t.on("end",(()=>{t.destroy(),this._done=!0,this._readCapability.resolve()})),t.on("error",(t=>{this._error(t)})),this._storedError&&this._readableStream.destroy(this._storedError)}}function h(t,e){return{protocol:t.protocol,auth:t.auth,host:t.hostname,port:t.port,path:t.path,method:"GET",headers:e}}class d extends l{constructor(t){super(t);const e=e=>{if(404===e.statusCode){const t=new n.MissingPDFException(`Missing PDF "${this._url}".`);return this._storedError=t,void this._headersCapability.reject(t)}this._headersCapability.resolve(),this._setReadableStream(e);const i=t=>this._readableStream.headers[t.toLowerCase()],{allowRangeRequests:r,suggestedLength:a}=(0,s.validateRangeRequestCapabilities)({getResponseHeader:i,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=r,this._contentLength=a||this._contentLength,this._filename=(0,s.extractFilenameFromHeader)(i)};if(this._request=null,"http:"===this._url.protocol){const i=require("http");this._request=i.request(h(this._url,t.httpHeaders),e)}else{const i=require("https");this._request=i.request(h(this._url,t.httpHeaders),e)}this._request.on("error",(t=>{this._storedError=t,this._headersCapability.reject(t)})),this._request.end()}}class u extends c{constructor(t,e,i){super(t),this._httpHeaders={};for(const n in t.httpHeaders){const e=t.httpHeaders[n];void 0!==e&&(this._httpHeaders[n]=e)}this._httpHeaders.Range=`bytes=${e}-${i-1}`;const s=t=>{if(404!==t.statusCode)this._setReadableStream(t);else{const t=new n.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t}};if(this._request=null,"http:"===this._url.protocol){const t=require("http");this._request=t.request(h(this._url,this._httpHeaders),s)}else{const t=require("https");this._request=t.request(h(this._url,this._httpHeaders),s)}this._request.on("error",(t=>{this._storedError=t})),this._request.end()}}class p extends l{constructor(t){super(t);let e=decodeURIComponent(this._url.path);r.test(this._url.href)&&(e=e.replace(/^\//,""));const i=require("fs");i.lstat(e,((t,s)=>{if(t)return"ENOENT"===t.code&&(t=new n.MissingPDFException(`Missing PDF "${e}".`)),this._storedError=t,void this._headersCapability.reject(t);this._contentLength=s.size,this._setReadableStream(i.createReadStream(e)),this._headersCapability.resolve()}))}}class g extends c{constructor(t,e,i){super(t);let n=decodeURIComponent(this._url.path);r.test(this._url.href)&&(n=n.replace(/^\//,""));const s=require("fs");this._setReadableStream(s.createReadStream(n,{start:e,end:i-1}))}}},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.SVGGraphics=void 0,i(84),i(86),i(87),i(93),i(101),i(102),i(105),i(107),i(109),i(113),i(116),i(123),i(2),i(89),i(187);var n=i(168),s=i(1);const r={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},a="http://www.w3.org/XML/1998/namespace",o="http://www.w3.org/1999/xlink",l=["butt","round","square"],c=["miter","round","bevel"],h=function(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];if(URL.createObjectURL&&"undefined"!==typeof Blob&&!i)return URL.createObjectURL(new Blob([t],{type:e}));const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let s=`data:${e};base64,`;for(let r=0,a=t.length;r<a;r+=3){const e=255&t[r],i=255&t[r+1],o=255&t[r+2],l=e>>2,c=(3&e)<<4|i>>4,h=r+1<a?(15&i)<<2|o>>6:64,d=r+2<a?63&o:64;s+=n[l]+n[c]+n[h]+n[d]}return s},d=function(){const t=new Uint8Array([137,80,78,71,13,10,26,10]),e=12,i=new Int32Array(256);for(let s=0;s<256;s++){let t=s;for(let e=0;e<8;e++)t=1&t?3988292384^t>>1&2147483647:t>>1&2147483647;i[s]=t}function n(t,e,n){let s=-1;for(let r=e;r<n;r++){const e=255&(s^t[r]),n=i[e];s=s>>>8^n}return~s}function r(t,e,i,s){let r=s;const a=e.length;i[r]=a>>24&255,i[r+1]=a>>16&255,i[r+2]=a>>8&255,i[r+3]=255&a,r+=4,i[r]=255&t.charCodeAt(0),i[r+1]=255&t.charCodeAt(1),i[r+2]=255&t.charCodeAt(2),i[r+3]=255&t.charCodeAt(3),r+=4,i.set(e,r),r+=e.length;const o=n(i,s+4,r);i[r]=o>>24&255,i[r+1]=o>>16&255,i[r+2]=o>>8&255,i[r+3]=255&o}function a(t,e,i){let n=1,s=0;for(let r=e;r<i;++r)n=(n+(255&t[r]))%65521,s=(s+n)%65521;return s<<16|n}function o(t){if(!s.isNodeJS)return l(t);try{const e=parseInt(process.versions.node)>=8?t:Buffer.from(t),i=require("zlib").deflateSync(e,{level:9});return i instanceof Uint8Array?i:new Uint8Array(i)}catch(e){(0,s.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+e)}return l(t)}function l(t){let e=t.length;const i=65535,n=Math.ceil(e/i),s=new Uint8Array(2+e+5*n+4);let r=0;s[r++]=120,s[r++]=156;let o=0;while(e>i)s[r++]=0,s[r++]=255,s[r++]=255,s[r++]=0,s[r++]=0,s.set(t.subarray(o,o+i),r),r+=i,o+=i,e-=i;s[r++]=1,s[r++]=255&e,s[r++]=e>>8&255,s[r++]=255&~e,s[r++]=(65535&~e)>>8&255,s.set(t.subarray(o),r),r+=t.length-o;const l=a(t,0,t.length);return s[r++]=l>>24&255,s[r++]=l>>16&255,s[r++]=l>>8&255,s[r++]=255&l,s}function c(i,n,a,l){const c=i.width,d=i.height;let u,p,g;const f=i.data;switch(n){case s.ImageKind.GRAYSCALE_1BPP:p=0,u=1,g=c+7>>3;break;case s.ImageKind.RGB_24BPP:p=2,u=8,g=3*c;break;case s.ImageKind.RGBA_32BPP:p=6,u=8,g=4*c;break;default:throw new Error("invalid format")}const m=new Uint8Array((1+g)*d);let b=0,v=0;for(let t=0;t<d;++t)m[b++]=0,m.set(f.subarray(v,v+g),b),v+=g,b+=g;if(n===s.ImageKind.GRAYSCALE_1BPP&&l){b=0;for(let t=0;t<d;t++){b++;for(let t=0;t<g;t++)m[b++]^=255}}const _=new Uint8Array([c>>24&255,c>>16&255,c>>8&255,255&c,d>>24&255,d>>16&255,d>>8&255,255&d,u,p,0,0,0]),y=o(m),A=t.length+3*e+_.length+y.length,S=new Uint8Array(A);let x=0;return S.set(t,x),x+=t.length,r("IHDR",_,S,x),x+=e+_.length,r("IDATA",y,S,x),x+=e+y.length,r("IEND",new Uint8Array(0),S,x),h(S,"image/png",a)}return function(t,e,i){const n=void 0===t.kind?s.ImageKind.GRAYSCALE_1BPP:t.kind;return c(t,n,e,i)}}();class u{constructor(){this.fontSizeScale=1,this.fontWeight=r.fontWeight,this.fontSize=0,this.textMatrix=s.IDENTITY_MATRIX,this.fontMatrix=s.FONT_IDENTITY_MATRIX,this.leading=0,this.textRenderingMode=s.TextRenderingMode.FILL,this.textMatrixScale=1,this.x=0,this.y=0,this.lineX=0,this.lineY=0,this.charSpacing=0,this.wordSpacing=0,this.textHScale=1,this.textRise=0,this.fillColor=r.fillColor,this.strokeColor="#000000",this.fillAlpha=1,this.strokeAlpha=1,this.lineWidth=1,this.lineJoin="",this.lineCap="",this.miterLimit=0,this.dashArray=[],this.dashPhase=0,this.dependencies=[],this.activeClipUrl=null,this.clipGroup=null,this.maskId=""}clone(){return Object.create(this)}setCurrentPoint(t,e){this.x=t,this.y=e}}function p(t){let e=[];const i=[];for(const n of t)"save"!==n.fn?"restore"===n.fn?e=i.pop():e.push(n):(e.push({fnId:92,fn:"group",items:[]}),i.push(e),e=e.at(-1).items);return e}function g(t){if(Number.isInteger(t))return t.toString();const e=t.toFixed(10);let i=e.length-1;if("0"!==e[i])return e;do{i--}while("0"===e[i]);return e.substring(0,"."===e[i]?i:i+1)}function f(t){if(0===t[4]&&0===t[5]){if(0===t[1]&&0===t[2])return 1===t[0]&&1===t[3]?"":`scale(${g(t[0])} ${g(t[3])})`;if(t[0]===t[3]&&t[1]===-t[2]){const e=180*Math.acos(t[0])/Math.PI;return`rotate(${g(e)})`}}else if(1===t[0]&&0===t[1]&&0===t[2]&&1===t[3])return`translate(${g(t[4])} ${g(t[5])})`;return`matrix(${g(t[0])} ${g(t[1])} ${g(t[2])} ${g(t[3])} ${g(t[4])} ${g(t[5])})`}let m=0,b=0,v=0;class _{constructor(t,e){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];(0,n.deprecated)("The SVG back-end is no longer maintained and *may* be removed in the future."),this.svgFactory=new n.DOMSVGFactory,this.current=new u,this.transformMatrix=s.IDENTITY_MATRIX,this.transformStack=[],this.extraStack=[],this.commonObjs=t,this.objs=e,this.pendingClip=null,this.pendingEOFill=!1,this.embedFonts=!1,this.embeddedFonts=Object.create(null),this.cssStyle=null,this.forceDataSchema=!!i,this._operatorIdMapping=[];for(const n in s.OPS)this._operatorIdMapping[s.OPS[n]]=n}getObject(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;return"string"===typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}save(){this.transformStack.push(this.transformMatrix);const t=this.current;this.extraStack.push(t),this.current=t.clone()}restore(){this.transformMatrix=this.transformStack.pop(),this.current=this.extraStack.pop(),this.pendingClip=null,this.tgrp=null}group(t){this.save(),this.executeOpTree(t),this.restore()}loadDependencies(t){const e=t.fnArray,i=t.argsArray;for(let n=0,r=e.length;n<r;n++)if(e[n]===s.OPS.dependency)for(const t of i[n]){const e=t.startsWith("g_")?this.commonObjs:this.objs,i=new Promise((i=>{e.get(t,i)}));this.current.dependencies.push(i)}return Promise.all(this.current.dependencies)}transform(t,e,i,n,r,a){const o=[t,e,i,n,r,a];this.transformMatrix=s.Util.transform(this.transformMatrix,o),this.tgrp=null}getSVG(t,e){this.viewport=e;const i=this._initialize(e);return this.loadDependencies(t).then((()=>(this.transformMatrix=s.IDENTITY_MATRIX,this.executeOpTree(this.convertOpList(t)),i)))}convertOpList(t){const e=this._operatorIdMapping,i=t.argsArray,n=t.fnArray,s=[];for(let r=0,a=n.length;r<a;r++){const t=n[r];s.push({fnId:t,fn:e[t],args:i[r]})}return p(s)}executeOpTree(t){for(const e of t){const t=e.fn,i=e.fnId,n=e.args;switch(0|i){case s.OPS.beginText:this.beginText();break;case s.OPS.dependency:break;case s.OPS.setLeading:this.setLeading(n);break;case s.OPS.setLeadingMoveText:this.setLeadingMoveText(n[0],n[1]);break;case s.OPS.setFont:this.setFont(n);break;case s.OPS.showText:this.showText(n[0]);break;case s.OPS.showSpacedText:this.showText(n[0]);break;case s.OPS.endText:this.endText();break;case s.OPS.moveText:this.moveText(n[0],n[1]);break;case s.OPS.setCharSpacing:this.setCharSpacing(n[0]);break;case s.OPS.setWordSpacing:this.setWordSpacing(n[0]);break;case s.OPS.setHScale:this.setHScale(n[0]);break;case s.OPS.setTextMatrix:this.setTextMatrix(n[0],n[1],n[2],n[3],n[4],n[5]);break;case s.OPS.setTextRise:this.setTextRise(n[0]);break;case s.OPS.setTextRenderingMode:this.setTextRenderingMode(n[0]);break;case s.OPS.setLineWidth:this.setLineWidth(n[0]);break;case s.OPS.setLineJoin:this.setLineJoin(n[0]);break;case s.OPS.setLineCap:this.setLineCap(n[0]);break;case s.OPS.setMiterLimit:this.setMiterLimit(n[0]);break;case s.OPS.setFillRGBColor:this.setFillRGBColor(n[0],n[1],n[2]);break;case s.OPS.setStrokeRGBColor:this.setStrokeRGBColor(n[0],n[1],n[2]);break;case s.OPS.setStrokeColorN:this.setStrokeColorN(n);break;case s.OPS.setFillColorN:this.setFillColorN(n);break;case s.OPS.shadingFill:this.shadingFill(n[0]);break;case s.OPS.setDash:this.setDash(n[0],n[1]);break;case s.OPS.setRenderingIntent:this.setRenderingIntent(n[0]);break;case s.OPS.setFlatness:this.setFlatness(n[0]);break;case s.OPS.setGState:this.setGState(n[0]);break;case s.OPS.fill:this.fill();break;case s.OPS.eoFill:this.eoFill();break;case s.OPS.stroke:this.stroke();break;case s.OPS.fillStroke:this.fillStroke();break;case s.OPS.eoFillStroke:this.eoFillStroke();break;case s.OPS.clip:this.clip("nonzero");break;case s.OPS.eoClip:this.clip("evenodd");break;case s.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case s.OPS.paintImageXObject:this.paintImageXObject(n[0]);break;case s.OPS.paintInlineImageXObject:this.paintInlineImageXObject(n[0]);break;case s.OPS.paintImageMaskXObject:this.paintImageMaskXObject(n[0]);break;case s.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(n[0],n[1]);break;case s.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case s.OPS.closePath:this.closePath();break;case s.OPS.closeStroke:this.closeStroke();break;case s.OPS.closeFillStroke:this.closeFillStroke();break;case s.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case s.OPS.nextLine:this.nextLine();break;case s.OPS.transform:this.transform(n[0],n[1],n[2],n[3],n[4],n[5]);break;case s.OPS.constructPath:this.constructPath(n[0],n[1]);break;case s.OPS.endPath:this.endPath();break;case 92:this.group(e.items);break;default:(0,s.warn)(`Unimplemented operator ${t}`);break}}}setWordSpacing(t){this.current.wordSpacing=t}setCharSpacing(t){this.current.charSpacing=t}nextLine(){this.moveText(0,this.current.leading)}setTextMatrix(t,e,i,n,s,r){const a=this.current;a.textMatrix=a.lineMatrix=[t,e,i,n,s,r],a.textMatrixScale=Math.hypot(t,e),a.x=a.lineX=0,a.y=a.lineY=0,a.xcoords=[],a.ycoords=[],a.tspan=this.svgFactory.createElement("svg:tspan"),a.tspan.setAttributeNS(null,"font-family",a.fontFamily),a.tspan.setAttributeNS(null,"font-size",`${g(a.fontSize)}px`),a.tspan.setAttributeNS(null,"y",g(-a.y)),a.txtElement=this.svgFactory.createElement("svg:text"),a.txtElement.append(a.tspan)}beginText(){const t=this.current;t.x=t.lineX=0,t.y=t.lineY=0,t.textMatrix=s.IDENTITY_MATRIX,t.lineMatrix=s.IDENTITY_MATRIX,t.textMatrixScale=1,t.tspan=this.svgFactory.createElement("svg:tspan"),t.txtElement=this.svgFactory.createElement("svg:text"),t.txtgrp=this.svgFactory.createElement("svg:g"),t.xcoords=[],t.ycoords=[]}moveText(t,e){const i=this.current;i.x=i.lineX+=t,i.y=i.lineY+=e,i.xcoords=[],i.ycoords=[],i.tspan=this.svgFactory.createElement("svg:tspan"),i.tspan.setAttributeNS(null,"font-family",i.fontFamily),i.tspan.setAttributeNS(null,"font-size",`${g(i.fontSize)}px`),i.tspan.setAttributeNS(null,"y",g(-i.y))}showText(t){const e=this.current,i=e.font,n=e.fontSize;if(0===n)return;const o=e.fontSizeScale,l=e.charSpacing,c=e.wordSpacing,h=e.fontDirection,d=e.textHScale*h,u=i.vertical,p=u?1:-1,m=i.defaultVMetrics,b=n*e.fontMatrix[0];let v=0;for(const s of t){if(null===s){v+=h*c;continue}if("number"===typeof s){v+=p*s*n/1e3;continue}const t=(s.isSpace?c:0)+l,r=s.fontChar;let a,d,g=s.width;if(u){let t;const e=s.vmetric||m;t=s.vmetric?e[1]:.5*g,t=-t*b;const i=e[2]*b;g=e?-e[0]:g,a=t/o,d=(v+i)/o}else a=v/o,d=0;(s.isInFont||i.missingFile)&&(e.xcoords.push(e.x+a),u&&e.ycoords.push(-e.y+d),e.tspan.textContent+=r);const f=u?g*b-t*h:g*b+t*h;v+=f}e.tspan.setAttributeNS(null,"x",e.xcoords.map(g).join(" ")),u?e.tspan.setAttributeNS(null,"y",e.ycoords.map(g).join(" ")):e.tspan.setAttributeNS(null,"y",g(-e.y)),u?e.y-=v:e.x+=v*d,e.tspan.setAttributeNS(null,"font-family",e.fontFamily),e.tspan.setAttributeNS(null,"font-size",`${g(e.fontSize)}px`),e.fontStyle!==r.fontStyle&&e.tspan.setAttributeNS(null,"font-style",e.fontStyle),e.fontWeight!==r.fontWeight&&e.tspan.setAttributeNS(null,"font-weight",e.fontWeight);const _=e.textRenderingMode&s.TextRenderingMode.FILL_STROKE_MASK;if(_===s.TextRenderingMode.FILL||_===s.TextRenderingMode.FILL_STROKE?(e.fillColor!==r.fillColor&&e.tspan.setAttributeNS(null,"fill",e.fillColor),e.fillAlpha<1&&e.tspan.setAttributeNS(null,"fill-opacity",e.fillAlpha)):e.textRenderingMode===s.TextRenderingMode.ADD_TO_PATH?e.tspan.setAttributeNS(null,"fill","transparent"):e.tspan.setAttributeNS(null,"fill","none"),_===s.TextRenderingMode.STROKE||_===s.TextRenderingMode.FILL_STROKE){const t=1/(e.textMatrixScale||1);this._setStrokeAttributes(e.tspan,t)}let y=e.textMatrix;0!==e.textRise&&(y=y.slice(),y[5]+=e.textRise),e.txtElement.setAttributeNS(null,"transform",`${f(y)} scale(${g(d)}, -1)`),e.txtElement.setAttributeNS(a,"xml:space","preserve"),e.txtElement.append(e.tspan),e.txtgrp.append(e.txtElement),this._ensureTransformGroup().append(e.txtElement)}setLeadingMoveText(t,e){this.setLeading(-e),this.moveText(t,e)}addFontStyle(t){if(!t.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');this.cssStyle||(this.cssStyle=this.svgFactory.createElement("svg:style"),this.cssStyle.setAttributeNS(null,"type","text/css"),this.defs.append(this.cssStyle));const e=h(t.data,t.mimetype,this.forceDataSchema);this.cssStyle.textContent+=`@font-face { font-family: "${t.loadedName}"; src: url(${e}); }\n`}setFont(t){const e=this.current,i=this.commonObjs.get(t[0]);let n=t[1];e.font=i,!this.embedFonts||i.missingFile||this.embeddedFonts[i.loadedName]||(this.addFontStyle(i),this.embeddedFonts[i.loadedName]=i),e.fontMatrix=i.fontMatrix||s.FONT_IDENTITY_MATRIX;let r="normal";i.black?r="900":i.bold&&(r="bold");const a=i.italic?"italic":"normal";n<0?(n=-n,e.fontDirection=-1):e.fontDirection=1,e.fontSize=n,e.fontFamily=i.loadedName,e.fontWeight=r,e.fontStyle=a,e.tspan=this.svgFactory.createElement("svg:tspan"),e.tspan.setAttributeNS(null,"y",g(-e.y)),e.xcoords=[],e.ycoords=[]}endText(){const t=this.current;t.textRenderingMode&s.TextRenderingMode.ADD_TO_PATH_FLAG&&t.txtElement?.hasChildNodes()&&(t.element=t.txtElement,this.clip("nonzero"),this.endPath())}setLineWidth(t){t>0&&(this.current.lineWidth=t)}setLineCap(t){this.current.lineCap=l[t]}setLineJoin(t){this.current.lineJoin=c[t]}setMiterLimit(t){this.current.miterLimit=t}setStrokeAlpha(t){this.current.strokeAlpha=t}setStrokeRGBColor(t,e,i){this.current.strokeColor=s.Util.makeHexColor(t,e,i)}setFillAlpha(t){this.current.fillAlpha=t}setFillRGBColor(t,e,i){this.current.fillColor=s.Util.makeHexColor(t,e,i),this.current.tspan=this.svgFactory.createElement("svg:tspan"),this.current.xcoords=[],this.current.ycoords=[]}setStrokeColorN(t){this.current.strokeColor=this._makeColorN_Pattern(t)}setFillColorN(t){this.current.fillColor=this._makeColorN_Pattern(t)}shadingFill(t){const{width:e,height:i}=this.viewport,n=s.Util.inverseTransform(this.transformMatrix),[r,a,o,l]=s.Util.getAxialAlignedBoundingBox([0,0,e,i],n),c=this.svgFactory.createElement("svg:rect");c.setAttributeNS(null,"x",r),c.setAttributeNS(null,"y",a),c.setAttributeNS(null,"width",o-r),c.setAttributeNS(null,"height",l-a),c.setAttributeNS(null,"fill",this._makeShadingPattern(t)),this.current.fillAlpha<1&&c.setAttributeNS(null,"fill-opacity",this.current.fillAlpha),this._ensureTransformGroup().append(c)}_makeColorN_Pattern(t){return"TilingPattern"===t[0]?this._makeTilingPattern(t):this._makeShadingPattern(t)}_makeTilingPattern(t){const e=t[1],i=t[2],n=t[3]||s.IDENTITY_MATRIX,[r,a,o,l]=t[4],c=t[5],h=t[6],d=t[7],u="shading"+v++,[p,g,f,m]=s.Util.normalizeRect([...s.Util.applyTransform([r,a],n),...s.Util.applyTransform([o,l],n)]),[b,_]=s.Util.singularValueDecompose2dScale(n),y=c*b,A=h*_,S=this.svgFactory.createElement("svg:pattern");S.setAttributeNS(null,"id",u),S.setAttributeNS(null,"patternUnits","userSpaceOnUse"),S.setAttributeNS(null,"width",y),S.setAttributeNS(null,"height",A),S.setAttributeNS(null,"x",`${p}`),S.setAttributeNS(null,"y",`${g}`);const x=this.svg,E=this.transformMatrix,w=this.current.fillColor,C=this.current.strokeColor,T=this.svgFactory.create(f-p,m-g);if(this.svg=T,this.transformMatrix=n,2===d){const t=s.Util.makeHexColor(...e);this.current.fillColor=t,this.current.strokeColor=t}return this.executeOpTree(this.convertOpList(i)),this.svg=x,this.transformMatrix=E,this.current.fillColor=w,this.current.strokeColor=C,S.append(T.childNodes[0]),this.defs.append(S),`url(#${u})`}_makeShadingPattern(t){switch("string"===typeof t&&(t=this.objs.get(t)),t[0]){case"RadialAxial":const e="shading"+v++,i=t[3];let n;switch(t[1]){case"axial":const i=t[4],s=t[5];n=this.svgFactory.createElement("svg:linearGradient"),n.setAttributeNS(null,"id",e),n.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),n.setAttributeNS(null,"x1",i[0]),n.setAttributeNS(null,"y1",i[1]),n.setAttributeNS(null,"x2",s[0]),n.setAttributeNS(null,"y2",s[1]);break;case"radial":const r=t[4],a=t[5],o=t[6],l=t[7];n=this.svgFactory.createElement("svg:radialGradient"),n.setAttributeNS(null,"id",e),n.setAttributeNS(null,"gradientUnits","userSpaceOnUse"),n.setAttributeNS(null,"cx",a[0]),n.setAttributeNS(null,"cy",a[1]),n.setAttributeNS(null,"r",l),n.setAttributeNS(null,"fx",r[0]),n.setAttributeNS(null,"fy",r[1]),n.setAttributeNS(null,"fr",o);break;default:throw new Error(`Unknown RadialAxial type: ${t[1]}`)}for(const t of i){const e=this.svgFactory.createElement("svg:stop");e.setAttributeNS(null,"offset",t[0]),e.setAttributeNS(null,"stop-color",t[1]),n.append(e)}return this.defs.append(n),`url(#${e})`;case"Mesh":return(0,s.warn)("Unimplemented pattern Mesh"),null;case"Dummy":return"hotpink";default:throw new Error(`Unknown IR type: ${t[0]}`)}}setDash(t,e){this.current.dashArray=t,this.current.dashPhase=e}constructPath(t,e){const i=this.current;let n=i.x,r=i.y,a=[],o=0;for(const l of t)switch(0|l){case s.OPS.rectangle:n=e[o++],r=e[o++];const t=e[o++],i=e[o++],l=n+t,c=r+i;a.push("M",g(n),g(r),"L",g(l),g(r),"L",g(l),g(c),"L",g(n),g(c),"Z");break;case s.OPS.moveTo:n=e[o++],r=e[o++],a.push("M",g(n),g(r));break;case s.OPS.lineTo:n=e[o++],r=e[o++],a.push("L",g(n),g(r));break;case s.OPS.curveTo:n=e[o+4],r=e[o+5],a.push("C",g(e[o]),g(e[o+1]),g(e[o+2]),g(e[o+3]),g(n),g(r)),o+=6;break;case s.OPS.curveTo2:a.push("C",g(n),g(r),g(e[o]),g(e[o+1]),g(e[o+2]),g(e[o+3])),n=e[o+2],r=e[o+3],o+=4;break;case s.OPS.curveTo3:n=e[o+2],r=e[o+3],a.push("C",g(e[o]),g(e[o+1]),g(n),g(r),g(n),g(r)),o+=4;break;case s.OPS.closePath:a.push("Z");break}a=a.join(" "),i.path&&t.length>0&&t[0]!==s.OPS.rectangle&&t[0]!==s.OPS.moveTo?a=i.path.getAttributeNS(null,"d")+a:(i.path=this.svgFactory.createElement("svg:path"),this._ensureTransformGroup().append(i.path)),i.path.setAttributeNS(null,"d",a),i.path.setAttributeNS(null,"fill","none"),i.element=i.path,i.setCurrentPoint(n,r)}endPath(){const t=this.current;if(t.path=null,!this.pendingClip)return;if(!t.element)return void(this.pendingClip=null);const e="clippath"+m++,i=this.svgFactory.createElement("svg:clipPath");i.setAttributeNS(null,"id",e),i.setAttributeNS(null,"transform",f(this.transformMatrix));const n=t.element.cloneNode(!0);if("evenodd"===this.pendingClip?n.setAttributeNS(null,"clip-rule","evenodd"):n.setAttributeNS(null,"clip-rule","nonzero"),this.pendingClip=null,i.append(n),this.defs.append(i),t.activeClipUrl){t.clipGroup=null;for(const t of this.extraStack)t.clipGroup=null;i.setAttributeNS(null,"clip-path",t.activeClipUrl)}t.activeClipUrl=`url(#${e})`,this.tgrp=null}clip(t){this.pendingClip=t}closePath(){const t=this.current;if(t.path){const e=`${t.path.getAttributeNS(null,"d")}Z`;t.path.setAttributeNS(null,"d",e)}}setLeading(t){this.current.leading=-t}setTextRise(t){this.current.textRise=t}setTextRenderingMode(t){this.current.textRenderingMode=t}setHScale(t){this.current.textHScale=t/100}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,i]of t)switch(e){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i);break;case"CA":this.setStrokeAlpha(i);break;case"ca":this.setFillAlpha(i);break;default:(0,s.warn)(`Unimplemented graphic state operator ${e}`);break}}fill(){const t=this.current;t.element&&(t.element.setAttributeNS(null,"fill",t.fillColor),t.element.setAttributeNS(null,"fill-opacity",t.fillAlpha),this.endPath())}stroke(){const t=this.current;t.element&&(this._setStrokeAttributes(t.element),t.element.setAttributeNS(null,"fill","none"),this.endPath())}_setStrokeAttributes(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;const i=this.current;let n=i.dashArray;1!==e&&n.length>0&&(n=n.map((function(t){return e*t}))),t.setAttributeNS(null,"stroke",i.strokeColor),t.setAttributeNS(null,"stroke-opacity",i.strokeAlpha),t.setAttributeNS(null,"stroke-miterlimit",g(i.miterLimit)),t.setAttributeNS(null,"stroke-linecap",i.lineCap),t.setAttributeNS(null,"stroke-linejoin",i.lineJoin),t.setAttributeNS(null,"stroke-width",g(e*i.lineWidth)+"px"),t.setAttributeNS(null,"stroke-dasharray",n.map(g).join(" ")),t.setAttributeNS(null,"stroke-dashoffset",g(e*i.dashPhase)+"px")}eoFill(){this.current.element?.setAttributeNS(null,"fill-rule","evenodd"),this.fill()}fillStroke(){this.stroke(),this.fill()}eoFillStroke(){this.current.element?.setAttributeNS(null,"fill-rule","evenodd"),this.fillStroke()}closeStroke(){this.closePath(),this.stroke()}closeFillStroke(){this.closePath(),this.fillStroke()}closeEOFillStroke(){this.closePath(),this.eoFillStroke()}paintSolidColorImageMask(){const t=this.svgFactory.createElement("svg:rect");t.setAttributeNS(null,"x","0"),t.setAttributeNS(null,"y","0"),t.setAttributeNS(null,"width","1px"),t.setAttributeNS(null,"height","1px"),t.setAttributeNS(null,"fill",this.current.fillColor),this._ensureTransformGroup().append(t)}paintImageXObject(t){const e=this.getObject(t);e?this.paintInlineImageXObject(e):(0,s.warn)(`Dependent image with object ID ${t} is not ready yet`)}paintInlineImageXObject(t,e){const i=t.width,n=t.height,s=d(t,this.forceDataSchema,!!e),r=this.svgFactory.createElement("svg:rect");r.setAttributeNS(null,"x","0"),r.setAttributeNS(null,"y","0"),r.setAttributeNS(null,"width",g(i)),r.setAttributeNS(null,"height",g(n)),this.current.element=r,this.clip("nonzero");const a=this.svgFactory.createElement("svg:image");a.setAttributeNS(o,"xlink:href",s),a.setAttributeNS(null,"x","0"),a.setAttributeNS(null,"y",g(-n)),a.setAttributeNS(null,"width",g(i)+"px"),a.setAttributeNS(null,"height",g(n)+"px"),a.setAttributeNS(null,"transform",`scale(${g(1/i)} ${g(-1/n)})`),e?e.append(a):this._ensureTransformGroup().append(a)}paintImageMaskXObject(t){const e=this.getObject(t.data,t);if(e.bitmap)return void(0,s.warn)("paintImageMaskXObject: ImageBitmap support is not implemented, ensure that the `isOffscreenCanvasSupported` API parameter is disabled.");const i=this.current,n=e.width,r=e.height,a=i.fillColor;i.maskId="mask"+b++;const o=this.svgFactory.createElement("svg:mask");o.setAttributeNS(null,"id",i.maskId);const l=this.svgFactory.createElement("svg:rect");l.setAttributeNS(null,"x","0"),l.setAttributeNS(null,"y","0"),l.setAttributeNS(null,"width",g(n)),l.setAttributeNS(null,"height",g(r)),l.setAttributeNS(null,"fill",a),l.setAttributeNS(null,"mask",`url(#${i.maskId})`),this.defs.append(o),this._ensureTransformGroup().append(l),this.paintInlineImageXObject(e,o)}paintFormXObjectBegin(t,e){if(Array.isArray(t)&&6===t.length&&this.transform(t[0],t[1],t[2],t[3],t[4],t[5]),e){const t=e[2]-e[0],i=e[3]-e[1],n=this.svgFactory.createElement("svg:rect");n.setAttributeNS(null,"x",e[0]),n.setAttributeNS(null,"y",e[1]),n.setAttributeNS(null,"width",g(t)),n.setAttributeNS(null,"height",g(i)),this.current.element=n,this.clip("nonzero"),this.endPath()}}paintFormXObjectEnd(){}_initialize(t){const e=this.svgFactory.create(t.width,t.height),i=this.svgFactory.createElement("svg:defs");e.append(i),this.defs=i;const n=this.svgFactory.createElement("svg:g");return n.setAttributeNS(null,"transform",f(t.transform)),e.append(n),this.svg=n,e}_ensureClipGroup(){if(!this.current.clipGroup){const t=this.svgFactory.createElement("svg:g");t.setAttributeNS(null,"clip-path",this.current.activeClipUrl),this.svg.append(t),this.current.clipGroup=t}return this.current.clipGroup}_ensureTransformGroup(){return this.tgrp||(this.tgrp=this.svgFactory.createElement("svg:g"),this.tgrp.setAttributeNS(null,"transform",f(this.transformMatrix)),this.current.activeClipUrl?this._ensureClipGroup().append(this.tgrp):this.svg.append(this.tgrp)),this.tgrp}}e.SVGGraphics=_},(t,e,i)=>{var n=i(3),s=i(188),r=i(193);n({target:"Array",proto:!0},{group:function(t){var e=arguments.length>1?arguments[1]:void 0;return s(this,t,e)}}),r("group")},(t,e,i)=>{var n=i(99),s=i(14),r=i(13),a=i(40),o=i(18),l=i(64),c=i(189),h=i(108),d=Array,u=s([].push);t.exports=function(t,e,i,s){for(var p,g,f,m=a(t),b=r(m),v=n(e,i),_=c(null),y=l(b),A=0;y>A;A++)f=b[A],g=o(v(f,A,m)),g in _?u(_[g],f):_[g]=[f];if(s&&(p=s(m),p!==d))for(g in _)_[g]=h(p,_[g]);return _}},(t,e,i)=>{var n,s=i(47),r=i(190),a=i(66),o=i(55),l=i(192),c=i(43),h=i(54),d=">",u="<",p="prototype",g="script",f=h("IE_PROTO"),m=function(){},b=function(t){return u+g+d+t+u+"/"+g+d},v=function(t){t.write(b("")),t.close();var e=t.parentWindow.Object;return t=null,e},_=function(){var t,e=c("iframe"),i="java"+g+":";return e.style.display="none",l.appendChild(e),e.src=String(i),t=e.contentWindow.document,t.open(),t.write(b("document.F=Object")),t.close(),t.F},y=function(){try{n=new ActiveXObject("htmlfile")}catch(e){}y="undefined"!=typeof document?document.domain&&n?v(n):_():v(n);var t=a.length;while(t--)delete y[p][a[t]];return y()};o[f]=!0,t.exports=Object.create||function(t,e){var i;return null!==t?(m[p]=s(t),i=new m,m[p]=null,i[f]=t):i=y(),void 0===e?i:r.f(i,e)}},(t,e,i)=>{var n=i(6),s=i(46),r=i(45),a=i(47),o=i(12),l=i(191);e.f=n&&!s?Object.defineProperties:function(t,e){a(t);var i,n=o(e),s=l(e),c=s.length,h=0;while(c>h)r.f(t,i=s[h++],n[i]);return t}},(t,e,i)=>{var n=i(59),s=i(66);t.exports=Object.keys||function(t){return n(t,s)}},(t,e,i)=>{var n=i(24);t.exports=n("document","documentElement")},(t,e,i)=>{var n=i(34),s=i(189),r=i(45).f,a=n("unscopables"),o=Array.prototype;void 0===o[a]&&r(o,a,{configurable:!0,value:s(null)}),t.exports=function(t){o[a][t]=!0}},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.XfaText=void 0,i(89);class n{static textContent(t){const e=[],i={items:e,styles:Object.create(null)};function s(t){if(!t)return;let i=null;const r=t.name;if("#text"===r)i=t.value;else{if(!n.shouldBuildText(r))return;t?.attributes?.textContent?i=t.attributes.textContent:t.value&&(i=t.value)}if(null!==i&&e.push({str:i}),t.children)for(const e of t.children)s(e)}return s(t),i}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}e.XfaText=n},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.TextLayerRenderTask=void 0,e.renderTextLayer=f,e.updateTextLayer=m,i(89),i(2);var n=i(1),s=i(168);const r=1e5,a=30,o=.8,l=new Map;function c(t,e){let i;if(e&&n.FeatureTest.isOffscreenCanvasSupported)i=new OffscreenCanvas(t,t).getContext("2d",{alpha:!1});else{const e=document.createElement("canvas");e.width=e.height=t,i=e.getContext("2d",{alpha:!1})}return i}function h(t,e){const i=l.get(t);if(i)return i;const n=c(a,e);n.font=`${a}px ${t}`;const s=n.measureText("");let r=s.fontBoundingBoxAscent,h=Math.abs(s.fontBoundingBoxDescent);if(r){const e=r/(r+h);return l.set(t,e),n.canvas.width=n.canvas.height=0,e}n.strokeStyle="red",n.clearRect(0,0,a,a),n.strokeText("g",0,0);let d=n.getImageData(0,0,a,a).data;h=0;for(let o=d.length-1-3;o>=0;o-=4)if(d[o]>0){h=Math.ceil(o/4/a);break}n.clearRect(0,0,a,a),n.strokeText("A",0,a),d=n.getImageData(0,0,a,a).data,r=0;for(let o=0,l=d.length;o<l;o+=4)if(d[o]>0){r=a-Math.floor(o/4/a);break}if(n.canvas.width=n.canvas.height=0,r){const e=r/(r+h);return l.set(t,e),e}return l.set(t,o),o}function d(t,e,i){const s=document.createElement("span"),r={angle:0,canvasWidth:0,hasText:""!==e.str,hasEOL:e.hasEOL,fontSize:0};t._textDivs.push(s);const a=n.Util.transform(t._transform,e.transform);let o=Math.atan2(a[1],a[0]);const l=i[e.fontName];l.vertical&&(o+=Math.PI/2);const c=Math.hypot(a[2],a[3]),d=c*h(l.fontFamily,t._isOffscreenCanvasSupported);let u,p;0===o?(u=a[4],p=a[5]-d):(u=a[4]+d*Math.sin(o),p=a[5]-d*Math.cos(o));const g="calc(var(--scale-factor)*",f=s.style;t._container===t._rootContainer?(f.left=`${(100*u/t._pageWidth).toFixed(2)}%`,f.top=`${(100*p/t._pageHeight).toFixed(2)}%`):(f.left=`${g}${u.toFixed(2)}px)`,f.top=`${g}${p.toFixed(2)}px)`),f.fontSize=`${g}${c.toFixed(2)}px)`,f.fontFamily=l.fontFamily,r.fontSize=c,s.setAttribute("role","presentation"),s.textContent=e.str,s.dir=e.dir,t._fontInspectorEnabled&&(s.dataset.fontName=e.fontName),0!==o&&(r.angle=o*(180/Math.PI));let m=!1;if(e.str.length>1)m=!0;else if(" "!==e.str&&e.transform[0]!==e.transform[3]){const t=Math.abs(e.transform[0]),i=Math.abs(e.transform[3]);t!==i&&Math.max(t,i)/Math.min(t,i)>1.5&&(m=!0)}m&&(r.canvasWidth=l.vertical?e.height:e.width),t._textDivProperties.set(s,r),t._isReadableStream&&t._layoutText(s)}function u(t){const{div:e,scale:i,properties:n,ctx:s,prevFontSize:r,prevFontFamily:a}=t,{style:o}=e;let l="";if(0!==n.canvasWidth&&n.hasText){const{fontFamily:c}=o,{canvasWidth:h,fontSize:d}=n;r===d&&a===c||(s.font=`${d*i}px ${c}`,t.prevFontSize=d,t.prevFontFamily=c);const{width:u}=s.measureText(e.textContent);u>0&&(l=`scaleX(${h*i/u})`)}0!==n.angle&&(l=`rotate(${n.angle}deg) ${l}`),l.length>0&&(o.transform=l)}function p(t){if(t._canceled)return;const e=t._textDivs,i=t._capability,n=e.length;if(n>r)i.resolve();else{if(!t._isReadableStream)for(const i of e)t._layoutText(i);i.resolve()}}class g{constructor(t){let{textContentSource:e,container:i,viewport:r,textDivs:a,textDivProperties:o,textContentItemsStr:l,isOffscreenCanvasSupported:h}=t;this._textContentSource=e,this._isReadableStream=e instanceof ReadableStream,this._container=this._rootContainer=i,this._textDivs=a||[],this._textContentItemsStr=l||[],this._isOffscreenCanvasSupported=h,this._fontInspectorEnabled=!!globalThis.FontInspector?.enabled,this._reader=null,this._textDivProperties=o||new WeakMap,this._canceled=!1,this._capability=new n.PromiseCapability,this._layoutTextParams={prevFontSize:null,prevFontFamily:null,div:null,scale:r.scale*(globalThis.devicePixelRatio||1),properties:null,ctx:c(0,h)};const{pageWidth:d,pageHeight:u,pageX:p,pageY:g}=r.rawDims;this._transform=[1,0,0,-1,-p,g+u],this._pageWidth=d,this._pageHeight=u,(0,s.setLayerDimensions)(i,r),this._capability.promise.finally((()=>{this._layoutTextParams=null})).catch((()=>{}))}get promise(){return this._capability.promise}cancel(){this._canceled=!0,this._reader&&(this._reader.cancel(new n.AbortException("TextLayer task cancelled.")).catch((()=>{})),this._reader=null),this._capability.reject(new n.AbortException("TextLayer task cancelled."))}_processItems(t,e){for(const i of t)if(void 0!==i.str)this._textContentItemsStr.push(i.str),d(this,i,e);else if("beginMarkedContentProps"===i.type||"beginMarkedContent"===i.type){const t=this._container;this._container=document.createElement("span"),this._container.classList.add("markedContent"),null!==i.id&&this._container.setAttribute("id",`${i.id}`),t.append(this._container)}else"endMarkedContent"===i.type&&(this._container=this._container.parentNode)}_layoutText(t){const e=this._layoutTextParams.properties=this._textDivProperties.get(t);if(this._layoutTextParams.div=t,u(this._layoutTextParams),e.hasText&&this._container.append(t),e.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation"),this._container.append(t)}}_render(){const t=new n.PromiseCapability;let e=Object.create(null);if(this._isReadableStream){const i=()=>{this._reader.read().then((n=>{let{value:s,done:r}=n;r?t.resolve():(Object.assign(e,s.styles),this._processItems(s.items,e),i())}),t.reject)};this._reader=this._textContentSource.getReader(),i()}else{if(!this._textContentSource)throw new Error('No "textContentSource" parameter specified.');{const{items:e,styles:i}=this._textContentSource;this._processItems(e,i),t.resolve()}}t.promise.then((()=>{e=null,p(this)}),this._capability.reject)}}function f(t){t.textContentSource||!t.textContent&&!t.textContentStream||((0,s.deprecated)("The TextLayerRender `textContent`/`textContentStream` parameters will be removed in the future, please use `textContentSource` instead."),t.textContentSource=t.textContent||t.textContentStream);const{container:e,viewport:i}=t,n=getComputedStyle(e),r=n.getPropertyValue("visibility"),a=parseFloat(n.getPropertyValue("--scale-factor"));"visible"===r&&(!a||Math.abs(a-i.scale)>1e-5)&&console.error("The `--scale-factor` CSS-variable must be set, to the same value as `viewport.scale`, either on the `container`-element itself or higher up in the DOM.");const o=new g(t);return o._render(),o}function m(t){let{container:e,viewport:i,textDivs:n,textDivProperties:r,isOffscreenCanvasSupported:a,mustRotate:o=!0,mustRescale:l=!0}=t;if(o&&(0,s.setLayerDimensions)(e,{rotation:i.rotation}),l){const t=c(0,a),e=i.scale*(globalThis.devicePixelRatio||1),s={prevFontSize:null,prevFontFamily:null,div:null,scale:e,properties:null,ctx:t};for(const i of n)s.properties=r.get(i),s.div=i,u(s)}}e.TextLayerRenderTask=g},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.AnnotationEditorLayer=void 0,i(125),i(136),i(138),i(141),i(143),i(145),i(147);var n=i(1),s=i(164),r=i(197),a=i(202),o=i(168),l=i(203);class c{#Se;#xe=!1;#Ee=null;#we=this.pointerup.bind(this);#Ce=this.pointerdown.bind(this);#Te=new Map;#Pe=!1;#ke=!1;#Me=!1;#Re;static _initialized=!1;constructor(t){let{uiManager:e,pageIndex:i,div:n,accessibilityManager:s,annotationLayer:o,viewport:h,l10n:d}=t;const u=[r.FreeTextEditor,a.InkEditor,l.StampEditor];if(!c._initialized){c._initialized=!0;for(const t of u)t.initialize(d)}e.registerEditorTypes(u),this.#Re=e,this.pageIndex=i,this.div=n,this.#Se=s,this.#Ee=o,this.viewport=h,this.#Re.addLayer(this)}get isEmpty(){return 0===this.#Te.size}updateToolbar(t){this.#Re.updateToolbar(t)}updateMode(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.#Re.getMode();this.#Fe(),t===n.AnnotationEditorType.INK?(this.addInkEditorIfNeeded(!1),this.disableClick()):this.enableClick(),t!==n.AnnotationEditorType.NONE&&(this.div.classList.toggle("freeTextEditing",t===n.AnnotationEditorType.FREETEXT),this.div.classList.toggle("inkEditing",t===n.AnnotationEditorType.INK),this.div.classList.toggle("stampEditing",t===n.AnnotationEditorType.STAMP),this.div.hidden=!1)}addInkEditorIfNeeded(t){if(!t&&this.#Re.getMode()!==n.AnnotationEditorType.INK)return;if(!t)for(const i of this.#Te.values())if(i.isEmpty())return void i.setInBackground();const e=this.#De({offsetX:0,offsetY:0},!1);e.setInBackground()}setEditingState(t){this.#Re.setEditingState(t)}addCommands(t){this.#Re.addCommands(t)}enable(){this.div.style.pointerEvents="auto";const t=new Set;for(const i of this.#Te.values())i.enableEditing(),i.annotationElementId&&t.add(i.annotationElementId);if(!this.#Ee)return;const e=this.#Ee.getEditableAnnotations();for(const i of e){if(i.hide(),this.#Re.isDeletedAnnotationElement(i.data.id))continue;if(t.has(i.data.id))continue;const e=this.deserialize(i);e&&(this.addOrRebuild(e),e.enableEditing())}}disable(){this.#Me=!0,this.div.style.pointerEvents="none";const t=new Set;for(const e of this.#Te.values())e.disableEditing(),e.annotationElementId&&null===e.serialize()?(this.getEditableAnnotation(e.annotationElementId)?.show(),e.remove()):t.add(e.annotationElementId);if(this.#Ee){const e=this.#Ee.getEditableAnnotations();for(const i of e){const{id:e}=i.data;t.has(e)||this.#Re.isDeletedAnnotationElement(e)||i.show()}}this.#Fe(),this.isEmpty&&(this.div.hidden=!0),this.#Me=!1}getEditableAnnotation(t){return this.#Ee?.getEditableAnnotation(t)||null}setActiveEditor(t){const e=this.#Re.getActive();e!==t&&this.#Re.setActiveEditor(t)}enableClick(){this.div.addEventListener("pointerdown",this.#Ce),this.div.addEventListener("pointerup",this.#we)}disableClick(){this.div.removeEventListener("pointerdown",this.#Ce),this.div.removeEventListener("pointerup",this.#we)}attach(t){this.#Te.set(t.id,t);const{annotationElementId:e}=t;e&&this.#Re.isDeletedAnnotationElement(e)&&this.#Re.removeDeletedAnnotationElement(t)}detach(t){this.#Te.delete(t.id),this.#Se?.removePointerInTextLayer(t.contentDiv),!this.#Me&&t.annotationElementId&&this.#Re.addDeletedAnnotationElement(t)}remove(t){this.detach(t),this.#Re.removeEditor(t),t.div.contains(document.activeElement)&&setTimeout((()=>{this.#Re.focusMainContainer()}),0),t.div.remove(),t.isAttachedToDOM=!1,this.#ke||this.addInkEditorIfNeeded(!1)}changeParent(t){t.parent!==this&&(t.annotationElementId&&(this.#Re.addDeletedAnnotationElement(t.annotationElementId),s.AnnotationEditor.deleteAnnotationElement(t),t.annotationElementId=null),this.attach(t),t.parent?.detach(t),t.setParent(this),t.div&&t.isAttachedToDOM&&(t.div.remove(),this.div.append(t.div)))}add(t){if(this.changeParent(t),this.#Re.addEditor(t),this.attach(t),!t.isAttachedToDOM){const e=t.render();this.div.append(e),t.isAttachedToDOM=!0}t.fixAndSetPosition(),t.onceAdded(),this.#Re.addToAnnotationStorage(t)}moveEditorInDOM(t){if(!t.isAttachedToDOM)return;const{activeElement:e}=document;t.div.contains(e)&&(t._focusEventsAllowed=!1,setTimeout((()=>{t.div.contains(document.activeElement)?t._focusEventsAllowed=!0:(t.div.addEventListener("focusin",(()=>{t._focusEventsAllowed=!0}),{once:!0}),e.focus())}),0)),t._structTreeParentId=this.#Se?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addOrRebuild(t){t.needsToBeRebuilt()?t.rebuild():this.add(t)}addUndoableEditor(t){const e=()=>t._uiManager.rebuild(t),i=()=>{t.remove()};this.addCommands({cmd:e,undo:i,mustExec:!1})}getNextId(){return this.#Re.getId()}#Ie(t){switch(this.#Re.getMode()){case n.AnnotationEditorType.FREETEXT:return new r.FreeTextEditor(t);case n.AnnotationEditorType.INK:return new a.InkEditor(t);case n.AnnotationEditorType.STAMP:return new l.StampEditor(t)}return null}pasteEditor(t,e){this.#Re.updateToolbar(t),this.#Re.updateMode(t);const{offsetX:i,offsetY:n}=this.#Oe(),s=this.getNextId(),r=this.#Ie({parent:this,id:s,x:i,y:n,uiManager:this.#Re,isCentered:!0,...e});r&&this.add(r)}deserialize(t){switch(t.annotationType??t.annotationEditorType){case n.AnnotationEditorType.FREETEXT:return r.FreeTextEditor.deserialize(t,this,this.#Re);case n.AnnotationEditorType.INK:return a.InkEditor.deserialize(t,this,this.#Re);case n.AnnotationEditorType.STAMP:return l.StampEditor.deserialize(t,this,this.#Re)}return null}#De(t,e){const i=this.getNextId(),n=this.#Ie({parent:this,id:i,x:t.offsetX,y:t.offsetY,uiManager:this.#Re,isCentered:e});return n&&this.add(n),n}#Oe(){const{x:t,y:e,width:i,height:n}=this.div.getBoundingClientRect(),s=Math.max(0,t),r=Math.max(0,e),a=Math.min(window.innerWidth,t+i),o=Math.min(window.innerHeight,e+n),l=(s+a)/2-t,c=(r+o)/2-e,[h,d]=this.viewport.rotation%180===0?[l,c]:[c,l];return{offsetX:h,offsetY:d}}addNewEditor(){this.#De(this.#Oe(),!0)}setSelected(t){this.#Re.setSelected(t)}toggleSelected(t){this.#Re.toggleSelected(t)}isSelected(t){return this.#Re.isSelected(t)}unselect(t){this.#Re.unselect(t)}pointerup(t){const{isMac:e}=n.FeatureTest.platform;0!==t.button||t.ctrlKey&&e||t.target===this.div&&this.#Pe&&(this.#Pe=!1,this.#xe?this.#Re.getMode()!==n.AnnotationEditorType.STAMP?this.#De(t,!1):this.#Re.unselectAll():this.#xe=!0)}pointerdown(t){if(this.#Pe)return void(this.#Pe=!1);const{isMac:e}=n.FeatureTest.platform;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;this.#Pe=!0;const i=this.#Re.getActive();this.#xe=!i||i.isEmpty()}findNewParent(t,e,i){const n=this.#Re.findParent(e,i);return null!==n&&n!==this&&(n.changeParent(t),!0)}destroy(){this.#Re.getActive()?.parent===this&&(this.#Re.commitOrRemove(),this.#Re.setActiveEditor(null));for(const t of this.#Te.values())this.#Se?.removePointerInTextLayer(t.contentDiv),t.setParent(null),t.isAttachedToDOM=!1,t.div.remove();this.div=null,this.#Te.clear(),this.#Re.removeLayer(this)}#Fe(){this.#ke=!0;for(const t of this.#Te.values())t.isEmpty()&&t.remove();this.#ke=!1}render(t){let{viewport:e}=t;this.viewport=e,(0,o.setLayerDimensions)(this.div,e);for(const i of this.#Re.getEditors(this.pageIndex))this.add(i);this.updateMode()}update(t){let{viewport:e}=t;this.#Re.commitOrRemove(),this.viewport=e,(0,o.setLayerDimensions)(this.div,{rotation:e.rotation}),this.updateMode()}get pageDimensions(){const{pageWidth:t,pageHeight:e}=this.viewport.rawDims;return[t,e]}}e.AnnotationEditorLayer=c},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.FreeTextEditor=void 0,i(89);var n=i(1),s=i(165),r=i(164),a=i(198);class o extends r.AnnotationEditor{#Le=this.editorDivBlur.bind(this);#Ne=this.editorDivFocus.bind(this);#je=this.editorDivInput.bind(this);#Be=this.editorDivKeydown.bind(this);#Ue;#ze="";#He=`${this.id}-editor`;#We;#qe=null;static _freeTextDefaultContent="";static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static get _keyboardManager(){const t=o.prototype,e=t=>t.isEmpty(),i=s.AnnotationEditorUIManager.TRANSLATE_SMALL,r=s.AnnotationEditorUIManager.TRANSLATE_BIG;return(0,n.shadow)(this,"_keyboardManager",new s.KeyboardManager([[["ctrl+s","mac+meta+s","ctrl+p","mac+meta+p"],t.commitOrRemove,{bubbles:!0}],[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],t.commitOrRemove],[["ArrowLeft","mac+ArrowLeft"],t._translateEmpty,{args:[-i,0],checker:e}],[["ctrl+ArrowLeft","mac+shift+ArrowLeft"],t._translateEmpty,{args:[-r,0],checker:e}],[["ArrowRight","mac+ArrowRight"],t._translateEmpty,{args:[i,0],checker:e}],[["ctrl+ArrowRight","mac+shift+ArrowRight"],t._translateEmpty,{args:[r,0],checker:e}],[["ArrowUp","mac+ArrowUp"],t._translateEmpty,{args:[0,-i],checker:e}],[["ctrl+ArrowUp","mac+shift+ArrowUp"],t._translateEmpty,{args:[0,-r],checker:e}],[["ArrowDown","mac+ArrowDown"],t._translateEmpty,{args:[0,i],checker:e}],[["ctrl+ArrowDown","mac+shift+ArrowDown"],t._translateEmpty,{args:[0,r],checker:e}]]))}static _type="freetext";constructor(t){super({...t,name:"freeTextEditor"}),this.#Ue=t.color||o._defaultColor||r.AnnotationEditor._defaultLineColor,this.#We=t.fontSize||o._defaultFontSize}static initialize(t){r.AnnotationEditor.initialize(t,{strings:["free_text2_default_content","editor_free_text2_aria_label"]});const e=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(e.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case n.AnnotationEditorParamsType.FREETEXT_SIZE:o._defaultFontSize=e;break;case n.AnnotationEditorParamsType.FREETEXT_COLOR:o._defaultColor=e;break}}updateParams(t,e){switch(t){case n.AnnotationEditorParamsType.FREETEXT_SIZE:this.#Ge(e);break;case n.AnnotationEditorParamsType.FREETEXT_COLOR:this.#Ve(e);break}}static get defaultPropertiesToUpdate(){return[[n.AnnotationEditorParamsType.FREETEXT_SIZE,o._defaultFontSize],[n.AnnotationEditorParamsType.FREETEXT_COLOR,o._defaultColor||r.AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[n.AnnotationEditorParamsType.FREETEXT_SIZE,this.#We],[n.AnnotationEditorParamsType.FREETEXT_COLOR,this.#Ue]]}#Ge(t){const e=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--scale-factor))`,this.translate(0,-(t-this.#We)*this.parentScale),this.#We=t,this.#$e()},i=this.#We;this.addCommands({cmd:()=>{e(t)},undo:()=>{e(i)},mustExec:!0,type:n.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#Ve(t){const e=this.#Ue;this.addCommands({cmd:()=>{this.#Ue=this.editorDiv.style.color=t},undo:()=>{this.#Ue=this.editorDiv.style.color=e},mustExec:!0,type:n.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}_translateEmpty(t,e){this._uiManager.translateSelectedEditors(t,e,!0)}getInitialTranslation(){const t=this.parentScale;return[-o._internalPadding*t,-(o._internalPadding+this.#We)*t]}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.isAttachedToDOM||this.parent.add(this)))}enableEditMode(){this.isInEditMode()||(this.parent.setEditingState(!1),this.parent.updateToolbar(n.AnnotationEditorType.FREETEXT),super.enableEditMode(),this.overlayDiv.classList.remove("enabled"),this.editorDiv.contentEditable=!0,this._isDraggable=!1,this.div.removeAttribute("aria-activedescendant"),this.editorDiv.addEventListener("keydown",this.#Be),this.editorDiv.addEventListener("focus",this.#Ne),this.editorDiv.addEventListener("blur",this.#Le),this.editorDiv.addEventListener("input",this.#je))}disableEditMode(){this.isInEditMode()&&(this.parent.setEditingState(!0),super.disableEditMode(),this.overlayDiv.classList.add("enabled"),this.editorDiv.contentEditable=!1,this.div.setAttribute("aria-activedescendant",this.#He),this._isDraggable=!0,this.editorDiv.removeEventListener("keydown",this.#Be),this.editorDiv.removeEventListener("focus",this.#Ne),this.editorDiv.removeEventListener("blur",this.#Le),this.editorDiv.removeEventListener("input",this.#je),this.div.focus({preventScroll:!0}),this.isEditing=!1,this.parent.div.classList.add("freeTextEditing"))}focusin(t){this._focusEventsAllowed&&(super.focusin(t),t.target!==this.editorDiv&&this.editorDiv.focus())}onceAdded(){this.width?this.#Xe():(this.enableEditMode(),this.editorDiv.focus(),this._initialOptions?.isCentered&&this.center(),this._initialOptions=null)}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1,this.parent&&(this.parent.setEditingState(!0),this.parent.div.classList.add("freeTextEditing")),super.remove()}#Ke(){const t=this.editorDiv.getElementsByTagName("div");if(0===t.length)return this.editorDiv.innerText;const e=[];for(const i of t)e.push(i.innerText.replace(/\r\n?|\n/,""));return e.join("\n")}#$e(){const[t,e]=this.parentDimensions;let i;if(this.isAttachedToDOM)i=this.div.getBoundingClientRect();else{const{currentLayer:t,div:e}=this,n=e.style.display;e.style.display="hidden",t.div.append(this.div),i=e.getBoundingClientRect(),e.remove(),e.style.display=n}this.rotation%180===this.parentRotation%180?(this.width=i.width/t,this.height=i.height/e):(this.width=i.height/t,this.height=i.width/e),this.fixAndSetPosition()}commit(){if(!this.isInEditMode())return;super.commit(),this.disableEditMode();const t=this.#ze,e=this.#ze=this.#Ke().trimEnd();if(t===e)return;const i=t=>{this.#ze=t,t?(this.#Ye(),this._uiManager.rebuild(this),this.#$e()):this.remove()};this.addCommands({cmd:()=>{i(e)},undo:()=>{i(t)},mustExec:!1}),this.#$e()}shouldGetKeyboardEvents(){return this.isInEditMode()}enterInEditMode(){this.enableEditMode(),this.editorDiv.focus()}dblclick(t){this.enterInEditMode()}keydown(t){t.target===this.div&&"Enter"===t.key&&(this.enterInEditMode(),t.preventDefault())}editorDivKeydown(t){o._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}editorDivInput(t){this.parent.div.classList.toggle("freeTextEditing",this.isEmpty())}disableEditing(){this.editorDiv.setAttribute("role","comment"),this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox"),this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let t,e;this.width&&(t=this.x,e=this.y),super.render(),this.editorDiv=document.createElement("div"),this.editorDiv.className="internal",this.editorDiv.setAttribute("id",this.#He),this.enableEditing(),r.AnnotationEditor._l10nPromise.get("editor_free_text2_aria_label").then((t=>this.editorDiv?.setAttribute("aria-label",t))),r.AnnotationEditor._l10nPromise.get("free_text2_default_content").then((t=>this.editorDiv?.setAttribute("default-content",t))),this.editorDiv.contentEditable=!0;const{style:i}=this.editorDiv;if(i.fontSize=`calc(${this.#We}px * var(--scale-factor))`,i.color=this.#Ue,this.div.append(this.editorDiv),this.overlayDiv=document.createElement("div"),this.overlayDiv.classList.add("overlay","enabled"),this.div.append(this.overlayDiv),(0,s.bindEvents)(this,this.div,["dblclick","keydown"]),this.width){const[i,n]=this.parentDimensions;if(this.annotationElementId){const{position:s}=this.#qe;let[r,a]=this.getInitialTranslation();[r,a]=this.pageTranslationToScreen(r,a);const[o,l]=this.pageDimensions,[c,h]=this.pageTranslation;let d,u;switch(this.rotation){case 0:d=t+(s[0]-c)/o,u=e+this.height-(s[1]-h)/l;break;case 90:d=t+(s[0]-c)/o,u=e-(s[1]-h)/l,[r,a]=[a,-r];break;case 180:d=t-this.width+(s[0]-c)/o,u=e-(s[1]-h)/l,[r,a]=[-r,-a];break;case 270:d=t+(s[0]-c-this.height*l)/o,u=e+(s[1]-h-this.width*o)/l,[r,a]=[-a,r];break}this.setAt(d*i,u*n,r,a)}else this.setAt(t*i,e*n,this.width*i,this.height*n);this.#Ye(),this._isDraggable=!0,this.editorDiv.contentEditable=!1}else this._isDraggable=!1,this.editorDiv.contentEditable=!0;return this.div}#Ye(){if(this.editorDiv.replaceChildren(),this.#ze)for(const t of this.#ze.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br")),this.editorDiv.append(e)}}get contentDiv(){return this.editorDiv}static deserialize(t,e,i){let s=null;if(t instanceof a.FreeTextAnnotationElement){const{data:{defaultAppearanceData:{fontSize:e,fontColor:i},rect:r,rotation:a,id:o},textContent:l,textPosition:c,parent:{page:{pageNumber:h}}}=t;if(!l||0===l.length)return null;s=t={annotationType:n.AnnotationEditorType.FREETEXT,color:Array.from(i),fontSize:e,value:l.join("\n"),position:c,pageIndex:h-1,rect:r,rotation:a,id:o,deleted:!1}}const r=super.deserialize(t,e,i);return r.#We=t.fontSize,r.#Ue=n.Util.makeHexColor(...t.color),r.#ze=t.value,r.annotationElementId=t.id||null,r.#qe=s,r}serialize(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.isEmpty())return null;if(this.deleted)return{pageIndex:this.pageIndex,id:this.annotationElementId,deleted:!0};const e=o._internalPadding*this.parentScale,i=this.getRect(e,e),s=r.AnnotationEditor._colorManager.convert(this.isAttachedToDOM?getComputedStyle(this.editorDiv).color:this.#Ue),a={annotationType:n.AnnotationEditorType.FREETEXT,color:s,fontSize:this.#We,value:this.#ze,pageIndex:this.pageIndex,rect:i,rotation:this.rotation,structTreeParentId:this._structTreeParentId};return t?a:this.annotationElementId&&!this.#Je(a)?null:(a.id=this.annotationElementId,a)}#Je(t){const{value:e,fontSize:i,color:n,rect:s,pageIndex:r}=this.#qe;return t.value!==e||t.fontSize!==i||t.rect.some(((t,e)=>Math.abs(t-s[e])>=1))||t.color.some(((t,e)=>t!==n[e]))||t.pageIndex!==r}#Xe(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.annotationElementId)return;if(this.#$e(),!t&&(0===this.width||0===this.height))return void setTimeout((()=>this.#Xe(!0)),0);const e=o._internalPadding*this.parentScale;this.#qe.rect=this.getRect(e,e)}}e.FreeTextEditor=o},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.StampAnnotationElement=e.InkAnnotationElement=e.FreeTextAnnotationElement=e.AnnotationLayer=void 0,i(89),i(125),i(136),i(138),i(141),i(143),i(145),i(147);var n=i(1),s=i(168),r=i(163),a=i(199),o=i(200),l=i(201);const c=1e3,h=9,d=new WeakSet;function u(t){return{width:t[2]-t[0],height:t[3]-t[1]}}class p{static create(t){const e=t.data.annotationType;switch(e){case n.AnnotationType.LINK:return new f(t);case n.AnnotationType.TEXT:return new m(t);case n.AnnotationType.WIDGET:const e=t.data.fieldType;switch(e){case"Tx":return new v(t);case"Btn":return t.data.radioButton?new A(t):t.data.checkBox?new y(t):new S(t);case"Ch":return new x(t);case"Sig":return new _(t)}return new b(t);case n.AnnotationType.POPUP:return new E(t);case n.AnnotationType.FREETEXT:return new C(t);case n.AnnotationType.LINE:return new T(t);case n.AnnotationType.SQUARE:return new P(t);case n.AnnotationType.CIRCLE:return new k(t);case n.AnnotationType.POLYLINE:return new M(t);case n.AnnotationType.CARET:return new F(t);case n.AnnotationType.INK:return new D(t);case n.AnnotationType.POLYGON:return new R(t);case n.AnnotationType.HIGHLIGHT:return new I(t);case n.AnnotationType.UNDERLINE:return new O(t);case n.AnnotationType.SQUIGGLY:return new L(t);case n.AnnotationType.STRIKEOUT:return new N(t);case n.AnnotationType.STAMP:return new j(t);case n.AnnotationType.FILEATTACHMENT:return new B(t);default:return new g(t)}}}class g{#Qe=!1;constructor(t){let{isRenderable:e=!1,ignoreBorder:i=!1,createQuadrilaterals:n=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.isRenderable=e,this.data=t.data,this.layer=t.layer,this.linkService=t.linkService,this.downloadManager=t.downloadManager,this.imageResourcesPath=t.imageResourcesPath,this.renderForms=t.renderForms,this.svgFactory=t.svgFactory,this.annotationStorage=t.annotationStorage,this.enableScripting=t.enableScripting,this.hasJSActions=t.hasJSActions,this._fieldObjects=t.fieldObjects,this.parent=t.parent,e&&(this.container=this._createContainer(i)),n&&this._createQuadrilaterals()}static _hasPopupData(t){let{titleObj:e,contentsObj:i,richText:n}=t;return!!(e?.str||i?.str||n?.str)}get hasPopupData(){return g._hasPopupData(this.data)}_createContainer(t){const{data:e,parent:{page:i,viewport:s}}=this,r=document.createElement("section");r.setAttribute("data-annotation-id",e.id),this instanceof b||(r.tabIndex=c),r.style.zIndex=this.parent.zIndex++,this.data.popupRef&&r.setAttribute("aria-haspopup","dialog"),e.noRotate&&r.classList.add("norotate");const{pageWidth:a,pageHeight:o,pageX:l,pageY:h}=s.rawDims;if(!e.rect||this instanceof E){const{rotation:t}=e;return e.hasOwnCanvas||0===t||this.setRotation(t,r),r}const{width:d,height:p}=u(e.rect),g=n.Util.normalizeRect([e.rect[0],i.view[3]-e.rect[1]+i.view[1],e.rect[2],i.view[3]-e.rect[3]+i.view[1]]);if(!t&&e.borderStyle.width>0){r.style.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,i=e.borderStyle.verticalCornerRadius;if(t>0||i>0){const e=`calc(${t}px * var(--scale-factor)) / calc(${i}px * var(--scale-factor))`;r.style.borderRadius=e}else if(this instanceof A){const t=`calc(${d}px * var(--scale-factor)) / calc(${p}px * var(--scale-factor))`;r.style.borderRadius=t}switch(e.borderStyle.style){case n.AnnotationBorderStyleType.SOLID:r.style.borderStyle="solid";break;case n.AnnotationBorderStyleType.DASHED:r.style.borderStyle="dashed";break;case n.AnnotationBorderStyleType.BEVELED:(0,n.warn)("Unimplemented border style: beveled");break;case n.AnnotationBorderStyleType.INSET:(0,n.warn)("Unimplemented border style: inset");break;case n.AnnotationBorderStyleType.UNDERLINE:r.style.borderBottomStyle="solid";break;default:break}const s=e.borderColor||null;s?(this.#Qe=!0,r.style.borderColor=n.Util.makeHexColor(0|s[0],0|s[1],0|s[2])):r.style.borderWidth=0}r.style.left=100*(g[0]-l)/a+"%",r.style.top=100*(g[1]-h)/o+"%";const{rotation:f}=e;return e.hasOwnCanvas||0===f?(r.style.width=100*d/a+"%",r.style.height=100*p/o+"%"):this.setRotation(f,r),r}setRotation(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.container;if(!this.data.rect)return;const{pageWidth:i,pageHeight:n}=this.parent.viewport.rawDims,{width:s,height:r}=u(this.data.rect);let a,o;t%180===0?(a=100*s/i,o=100*r/n):(a=100*r/i,o=100*s/n),e.style.width=`${a}%`,e.style.height=`${o}%`,e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const t=(t,e,i)=>{const n=i.detail[t],s=n[0],r=n.slice(1);i.target.style[e]=a.ColorConverters[`${s}_HTML`](r),this.annotationStorage.setValue(this.data.id,{[e]:a.ColorConverters[`${s}_rgb`](r)})};return(0,n.shadow)(this,"_commonActions",{display:t=>{const{display:e}=t.detail,i=e%2===1;this.container.style.visibility=i?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noView:i,noPrint:1===e||2===e})},print:t=>{this.annotationStorage.setValue(this.data.id,{noPrint:!t.detail.print})},hidden:t=>{const{hidden:e}=t.detail;this.container.style.visibility=e?"hidden":"visible",this.annotationStorage.setValue(this.data.id,{noPrint:e,noView:e})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.target.disabled=t.detail.readonly},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:e=>{t("bgColor","backgroundColor",e)},fillColor:e=>{t("fillColor","backgroundColor",e)},fgColor:e=>{t("fgColor","color",e)},textColor:e=>{t("textColor","color",e)},borderColor:e=>{t("borderColor","borderColor",e)},strokeColor:e=>{t("strokeColor","borderColor",e)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e),this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const i=this._commonActions;for(const n of Object.keys(e.detail)){const s=t[n]||i[n];s?.(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const i=this._commonActions;for(const[n,s]of Object.entries(e)){const r=i[n];if(r){const i={detail:{[n]:s},target:t};r(i),delete e[n]}}}_createQuadrilaterals(){if(!this.container)return;const{quadPoints:t}=this.data;if(!t)return;const[e,i,n,s]=this.data.rect;if(1===t.length){const[,{x:r,y:a},{x:o,y:l}]=t[0];if(n===r&&s===a&&e===o&&i===l)return}const{style:r}=this.container;let a;if(this.#Qe){const{borderColor:t,borderWidth:e}=r;r.borderWidth=0,a=["url('data:image/svg+xml;utf8,",'<svg xmlns="http://www.w3.org/2000/svg"',' preserveAspectRatio="none" viewBox="0 0 1 1">',`<g fill="transparent" stroke="${t}" stroke-width="${e}">`],this.container.classList.add("hasBorder")}const o=n-e,l=s-i,{svgFactory:c}=this,h=c.createElement("svg");h.classList.add("quadrilateralsContainer"),h.setAttribute("width",0),h.setAttribute("height",0);const d=c.createElement("defs");h.append(d);const u=c.createElement("clipPath"),p=`clippath_${this.data.id}`;u.setAttribute("id",p),u.setAttribute("clipPathUnits","objectBoundingBox"),d.append(u);for(const[,{x:g,y:f},{x:m,y:b}]of t){const t=c.createElement("rect"),i=(m-e)/o,n=(s-f)/l,r=(g-m)/o,h=(f-b)/l;t.setAttribute("x",i),t.setAttribute("y",n),t.setAttribute("width",r),t.setAttribute("height",h),u.append(t),a?.push(`<rect vector-effect="non-scaling-stroke" x="${i}" y="${n}" width="${r}" height="${h}"/>`)}this.#Qe&&(a.push("</g></svg>')"),r.backgroundImage=a.join("")),this.container.append(h),this.container.style.clipPath=`url(#${p})`}_createPopup(){const{container:t,data:e}=this;t.setAttribute("aria-haspopup","dialog");const i=new E({data:{color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,parentRect:e.rect,borderStyle:0,id:`popup_${e.id}`,rotation:e.rotation},parent:this.parent,elements:[this]});this.parent.div.append(i.render())}render(){(0,n.unreachable)("Abstract method `AnnotationElement.render` called")}_getElementsByName(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const i=[];if(this._fieldObjects){const s=this._fieldObjects[t];if(s)for(const{page:t,id:r,exportValues:a}of s){if(-1===t)continue;if(r===e)continue;const s="string"===typeof a?a:null,o=document.querySelector(`[data-element-id="${r}"]`);!o||d.has(o)?i.push({id:r,exportValue:s,domElement:o}):(0,n.warn)(`_getElementsByName - element not allowed: ${r}`)}return i}for(const n of document.getElementsByName(t)){const{exportValue:t}=n,s=n.getAttribute("data-element-id");s!==e&&(d.has(n)&&i.push({id:s,exportValue:t,domElement:n}))}return i}show(){this.container&&(this.container.hidden=!1),this.popup?.maybeShow()}hide(){this.container&&(this.container.hidden=!0),this.popup?.forceHide()}getElementsToTriggerPopup(){return this.container}addHighlightArea(){const t=this.getElementsToTriggerPopup();if(Array.isArray(t))for(const e of t)e.classList.add("highlightArea");else t.classList.add("highlightArea")}_editOnDoubleClick(){const{annotationEditorType:t,data:{id:e}}=this;this.container.addEventListener("dblclick",(()=>{this.linkService.eventBus?.dispatch("switchannotationeditormode",{source:this,mode:t,editId:e})}))}}class f extends g{constructor(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0}),this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,i=document.createElement("a");i.setAttribute("data-element-id",t.id);let n=!1;return t.url?(e.addLinkAttributes(i,t.url,t.newWindow),n=!0):t.action?(this._bindNamedAction(i,t.action),n=!0):t.attachment?(this._bindAttachment(i,t.attachment),n=!0):t.setOCGState?(this.#Ze(i,t.setOCGState),n=!0):t.dest?(this._bindLink(i,t.dest),n=!0):(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions&&(this._bindJSAction(i,t),n=!0),t.resetForm?(this._bindResetFormAction(i,t.resetForm),n=!0):this.isTooltipOnly&&!n&&(this._bindLink(i,""),n=!0)),this.container.classList.add("linkAnnotation"),n&&this.container.append(i),this.container}#ti(){this.container.setAttribute("data-internal-link","")}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e),t.onclick=()=>(e&&this.linkService.goToDestination(e),!1),(e||""===e)&&this.#ti()}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeNamedAction(e),!1),this.#ti()}_bindAttachment(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.downloadManager?.openOrDownloadData(this.container,e.content,e.filename),!1),this.#ti()}#Ze(t,e){t.href=this.linkService.getAnchorUrl(""),t.onclick=()=>(this.linkService.executeSetOCGState(e),!1),this.#ti()}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const i=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const n of Object.keys(e.actions)){const s=i.get(n);s&&(t[s]=()=>(this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:n}}),!1))}t.onclick||(t.onclick=()=>!1),this.#ti()}_bindResetFormAction(t,e){const i=t.onclick;if(i||(t.href=this.linkService.getAnchorUrl("")),this.#ti(),!this._fieldObjects)return(0,n.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.'),void(i||(t.onclick=()=>!1));t.onclick=()=>{i?.();const{fields:t,refs:s,include:r}=e,a=[];if(0!==t.length||0!==s.length){const e=new Set(s);for(const i of t){const t=this._fieldObjects[i]||[];for(const{id:i}of t)e.add(i)}for(const t of Object.values(this._fieldObjects))for(const i of t)e.has(i.id)===r&&a.push(i)}else for(const e of Object.values(this._fieldObjects))a.push(...e);const o=this.annotationStorage,l=[];for(const e of a){const{id:t}=e;switch(l.push(t),e.type){case"text":{const i=e.defaultValue||"";o.setValue(t,{value:i});break}case"checkbox":case"radiobutton":{const i=e.defaultValue===e.exportValues;o.setValue(t,{value:i});break}case"combobox":case"listbox":{const i=e.defaultValue||"";o.setValue(t,{value:i});break}default:continue}const i=document.querySelector(`[data-element-id="${t}"]`);i&&(d.has(i)?i.dispatchEvent(new Event("resetform")):(0,n.warn)(`_bindResetFormAction - element not allowed: ${t}`))}return this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:l,name:"ResetForm"}}),!1}}}class m extends g{constructor(t){super(t,{isRenderable:!0})}render(){this.container.classList.add("textAnnotation");const t=document.createElement("img");return t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg",t.alt="[{{type}} Annotation]",t.dataset.l10nId="text_annotation_type",t.dataset.l10nArgs=JSON.stringify({type:this.data.name}),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.append(t),this.container}}class b extends g{render(){return this.data.alternativeText&&(this.container.title=this.data.alternativeText),this.container}showElementAndHideCanvas(t){this.data.hasOwnCanvas&&("CANVAS"===t.previousSibling?.nodeName&&(t.previousSibling.hidden=!0),t.hidden=!1)}_getKeyModifier(t){const{isWin:e,isMac:i}=n.FeatureTest.platform;return e&&t.ctrlKey||i&&t.metaKey}_setEventListener(t,e,i,n,s){i.includes("mouse")?t.addEventListener(i,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:n,value:s(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(i,(t=>{if("blur"===i){if(!e.focused||!t.relatedTarget)return;e.focused=!1}else if("focus"===i){if(e.focused)return;e.focused=!0}s&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:n,value:s(t)}})}))}_setEventListeners(t,e,i,n){for(const[s,r]of i)("Action"===r||this.data.actions?.[r])&&("Focus"!==r&&"Blur"!==r||(e||={focused:!1}),this._setEventListener(t,e,s,r,n),"Focus"!==r||this.data.actions?.Blur?"Blur"!==r||this.data.actions?.Focus||this._setEventListener(t,e,"focus","Focus",null):this._setEventListener(t,e,"blur","Blur",null))}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":n.Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:i}=this.data.defaultAppearanceData,s=this.data.defaultAppearanceData.fontSize||h,r=t.style;let a;const o=2,l=t=>Math.round(10*t)/10;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]-o),e=Math.round(t/(n.LINE_FACTOR*s))||1,i=t/e;a=Math.min(s,l(i/n.LINE_FACTOR))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]-o);a=Math.min(s,l(t/n.LINE_FACTOR))}r.fontSize=`calc(${a}px * var(--scale-factor))`,r.color=n.Util.makeHexColor(i[0],i[1],i[2]),null!==this.data.textAlignment&&(r.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required"),t.setAttribute("aria-required",e)}}class v extends b{constructor(t){const e=t.renderForms||!t.data.hasAppearance&&!!t.data.fieldValue;super(t,{isRenderable:e})}setPropertyOnSiblings(t,e,i,n){const s=this.annotationStorage;for(const r of this._getElementsByName(t.name,t.id))r.domElement&&(r.domElement[e]=i),s.setValue(r.id,{[n]:i})}render(){const t=this.annotationStorage,e=this.data.id;this.container.classList.add("textWidgetAnnotation");let i=null;if(this.renderForms){const n=t.getValue(e,{value:this.data.fieldValue});let s=n.value||"";const r=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;r&&s.length>r&&(s=s.slice(0,r));let a=n.formattedValue||this.data.textContent?.join("\n")||null;a&&this.data.comb&&(a=a.replaceAll(/\s+/g,""));const o={userValue:s,formattedValue:a,lastCommittedValue:null,commitKey:1,focused:!1};this.data.multiLine?(i=document.createElement("textarea"),i.textContent=a??s,this.data.doNotScroll&&(i.style.overflowY="hidden")):(i=document.createElement("input"),i.type="text",i.setAttribute("value",a??s),this.data.doNotScroll&&(i.style.overflowX="hidden")),this.data.hasOwnCanvas&&(i.hidden=!0),d.add(i),i.setAttribute("data-element-id",e),i.disabled=this.data.readOnly,i.name=this.data.fieldName,i.tabIndex=c,this._setRequired(i,this.data.required),r&&(i.maxLength=r),i.addEventListener("input",(n=>{t.setValue(e,{value:n.target.value}),this.setPropertyOnSiblings(i,"value",n.target.value,"value"),o.formattedValue=null})),i.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";i.value=o.userValue=e,o.formattedValue=null}));let l=t=>{const{formattedValue:e}=o;null!==e&&void 0!==e&&(t.target.value=e),t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){i.addEventListener("focus",(t=>{if(o.focused)return;const{target:e}=t;o.userValue&&(e.value=o.userValue),o.lastCommittedValue=e.value,o.commitKey=1,o.focused=!0})),i.addEventListener("updatefromsandbox",(i=>{this.showElementAndHideCanvas(i.target);const n={value(i){o.userValue=i.detail.value??"",t.setValue(e,{value:o.userValue.toString()}),i.target.value=o.userValue},formattedValue(i){const{formattedValue:n}=i.detail;o.formattedValue=n,null!==n&&void 0!==n&&i.target!==document.activeElement&&(i.target.value=n),t.setValue(e,{formattedValue:n})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:i=>{const{charLimit:n}=i.detail,{target:s}=i;if(0===n)return void s.removeAttribute("maxLength");s.setAttribute("maxLength",n);let r=o.userValue;!r||r.length<=n||(r=r.slice(0,n),s.value=o.userValue=r,t.setValue(e,{value:r}),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,willCommit:!0,commitKey:1,selStart:s.selectionStart,selEnd:s.selectionEnd}}))}};this._dispatchEventFromSandbox(n,i)})),i.addEventListener("keydown",(t=>{o.commitKey=1;let i=-1;if("Escape"===t.key?i=0:"Enter"!==t.key||this.data.multiLine?"Tab"===t.key&&(o.commitKey=3):i=2,-1===i)return;const{value:n}=t.target;o.lastCommittedValue!==n&&(o.lastCommittedValue=n,o.userValue=n,this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,willCommit:!0,commitKey:i,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}))}));const n=l;l=null,i.addEventListener("blur",(t=>{if(!o.focused||!t.relatedTarget)return;o.focused=!1;const{value:i}=t.target;o.userValue=i,o.lastCommittedValue!==i&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,willCommit:!0,commitKey:o.commitKey,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}}),n(t)})),this.data.actions?.Keystroke&&i.addEventListener("beforeinput",(t=>{o.lastCommittedValue=null;const{data:i,target:n}=t,{value:s,selectionStart:r,selectionEnd:a}=n;let l=r,c=a;switch(t.inputType){case"deleteWordBackward":{const t=s.substring(0,r).match(/\w*[^\w]*$/);t&&(l-=t[0].length);break}case"deleteWordForward":{const t=s.substring(r).match(/^[^\w]*\w*/);t&&(c+=t[0].length);break}case"deleteContentBackward":r===a&&(l-=1);break;case"deleteContentForward":r===a&&(c+=1);break}t.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,change:i||"",willCommit:!1,selStart:l,selEnd:c}})})),this._setEventListeners(i,o,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}if(l&&i.addEventListener("blur",l),this.data.comb){const t=this.data.rect[2]-this.data.rect[0],e=t/r;i.classList.add("comb"),i.style.letterSpacing=`calc(${e}px * var(--scale-factor) - 1ch)`}}else i=document.createElement("div"),i.textContent=this.data.fieldValue,i.style.verticalAlign="middle",i.style.display="table-cell";return this._setTextStyle(i),this._setBackgroundColor(i),this._setDefaultPropertiesFromJS(i),this.container.append(i),this.container}}class _ extends b{constructor(t){super(t,{isRenderable:!!t.data.hasOwnCanvas})}}class y extends b{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,i=e.id;let n=t.getValue(i,{value:e.exportValue===e.fieldValue}).value;"string"===typeof n&&(n="Off"!==n,t.setValue(i,{value:n})),this.container.classList.add("buttonWidgetAnnotation","checkBox");const s=document.createElement("input");return d.add(s),s.setAttribute("data-element-id",i),s.disabled=e.readOnly,this._setRequired(s,this.data.required),s.type="checkbox",s.name=e.fieldName,n&&s.setAttribute("checked",!0),s.setAttribute("exportValue",e.exportValue),s.tabIndex=c,s.addEventListener("change",(n=>{const{name:s,checked:r}=n.target;for(const a of this._getElementsByName(s,i)){const i=r&&a.exportValue===e.exportValue;a.domElement&&(a.domElement.checked=i),t.setValue(a.id,{value:i})}t.setValue(i,{value:r})})),s.addEventListener("resetform",(t=>{const i=e.defaultFieldValue||"Off";t.target.checked=i===e.exportValue})),this.enableScripting&&this.hasJSActions&&(s.addEventListener("updatefromsandbox",(e=>{const n={value(e){e.target.checked="Off"!==e.detail.value,t.setValue(i,{value:e.target.checked})}};this._dispatchEventFromSandbox(n,e)})),this._setEventListeners(s,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))),this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class A extends b{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("buttonWidgetAnnotation","radioButton");const t=this.annotationStorage,e=this.data,i=e.id;let n=t.getValue(i,{value:e.fieldValue===e.buttonValue}).value;"string"===typeof n&&(n=n!==e.buttonValue,t.setValue(i,{value:n}));const s=document.createElement("input");if(d.add(s),s.setAttribute("data-element-id",i),s.disabled=e.readOnly,this._setRequired(s,this.data.required),s.type="radio",s.name=e.fieldName,n&&s.setAttribute("checked",!0),s.tabIndex=c,s.addEventListener("change",(e=>{const{name:n,checked:s}=e.target;for(const r of this._getElementsByName(n,i))t.setValue(r.id,{value:!1});t.setValue(i,{value:s})})),s.addEventListener("resetform",(t=>{const i=e.defaultFieldValue;t.target.checked=null!==i&&void 0!==i&&i===e.buttonValue})),this.enableScripting&&this.hasJSActions){const n=e.buttonValue;s.addEventListener("updatefromsandbox",(e=>{const s={value:e=>{const s=n===e.detail.value;for(const n of this._getElementsByName(e.target.name)){const e=s&&n.id===i;n.domElement&&(n.domElement.checked=e),t.setValue(n.id,{value:e})}}};this._dispatchEventFromSandbox(s,e)})),this._setEventListeners(s,null,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}return this._setBackgroundColor(s),this._setDefaultPropertiesFromJS(s),this.container.append(s),this.container}}class S extends f{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.classList.add("buttonWidgetAnnotation","pushButton"),this.data.alternativeText&&(t.title=this.data.alternativeText);const e=t.lastChild;return this.enableScripting&&this.hasJSActions&&e&&(this._setDefaultPropertiesFromJS(e),e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))),t}}class x extends b{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.classList.add("choiceWidgetAnnotation");const t=this.annotationStorage,e=this.data.id,i=t.getValue(e,{value:this.data.fieldValue}),n=document.createElement("select");d.add(n),n.setAttribute("data-element-id",e),n.disabled=this.data.readOnly,this._setRequired(n,this.data.required),n.name=this.data.fieldName,n.tabIndex=c;let s=this.data.combo&&this.data.options.length>0;this.data.combo||(n.size=this.data.options.length,this.data.multiSelect&&(n.multiple=!0)),n.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const i of n.options)i.selected=i.value===e}));for(const c of this.data.options){const t=document.createElement("option");t.textContent=c.displayValue,t.value=c.exportValue,i.value.includes(c.exportValue)&&(t.setAttribute("selected",!0),s=!1),n.append(t)}let r=null;if(s){const t=document.createElement("option");t.value=" ",t.setAttribute("hidden",!0),t.setAttribute("selected",!0),n.prepend(t),r=()=>{t.remove(),n.removeEventListener("input",r),r=null},n.addEventListener("input",r)}const a=t=>{const e=t?"value":"textContent",{options:i,multiple:s}=n;return s?Array.prototype.filter.call(i,(t=>t.selected)).map((t=>t[e])):-1===i.selectedIndex?null:i[i.selectedIndex][e]};let o=a(!1);const l=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};return this.enableScripting&&this.hasJSActions?(n.addEventListener("updatefromsandbox",(i=>{const s={value(i){r?.();const s=i.detail.value,l=new Set(Array.isArray(s)?s:[s]);for(const t of n.options)t.selected=l.has(t.value);t.setValue(e,{value:a(!0)}),o=a(!1)},multipleSelection(t){n.multiple=!0},remove(i){const s=n.options,r=i.detail.remove;if(s[r].selected=!1,n.remove(r),s.length>0){const t=Array.prototype.findIndex.call(s,(t=>t.selected));-1===t&&(s[0].selected=!0)}t.setValue(e,{value:a(!0),items:l(i)}),o=a(!1)},clear(i){while(0!==n.length)n.remove(0);t.setValue(e,{value:null,items:[]}),o=a(!1)},insert(i){const{index:s,displayValue:r,exportValue:c}=i.detail.insert,h=n.children[s],d=document.createElement("option");d.textContent=r,d.value=c,h?h.before(d):n.append(d),t.setValue(e,{value:a(!0),items:l(i)}),o=a(!1)},items(i){const{items:s}=i.detail;while(0!==n.length)n.remove(0);for(const t of s){const{displayValue:e,exportValue:i}=t,s=document.createElement("option");s.textContent=e,s.value=i,n.append(s)}n.options.length>0&&(n.options[0].selected=!0),t.setValue(e,{value:a(!0),items:l(i)}),o=a(!1)},indices(i){const n=new Set(i.detail.indices);for(const t of i.target.options)t.selected=n.has(t.index);t.setValue(e,{value:a(!0)}),o=a(!1)},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(s,i)})),n.addEventListener("input",(i=>{const n=a(!0);t.setValue(e,{value:n}),i.preventDefault(),this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:o,changeEx:n,willCommit:!1,commitKey:1,keyDown:!1}})})),this._setEventListeners(n,null,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"],["input","Validate"]],(t=>t.target.value))):n.addEventListener("input",(function(i){t.setValue(e,{value:a(!0)})})),this.data.combo&&this._setTextStyle(n),this._setBackgroundColor(n),this._setDefaultPropertiesFromJS(n),this.container.append(n),this.container}}class E extends g{constructor(t){const{data:e,elements:i}=t;super(t,{isRenderable:g._hasPopupData(e)}),this.elements=i}render(){this.container.classList.add("popupAnnotation");const t=new w({container:this.container,color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText,rect:this.data.rect,parentRect:this.data.parentRect||null,parent:this.parent,elements:this.elements,open:this.data.open}),e=[];for(const i of this.elements)i.popup=t,e.push(i.data.id),i.addHighlightArea();return this.container.setAttribute("aria-controls",e.map((t=>`${n.AnnotationPrefix}${t}`)).join(",")),this.container}}class w{#ei=null;#ii=this.#ni.bind(this);#si=this.#ri.bind(this);#ai=this.#oi.bind(this);#li=this.#ci.bind(this);#Ue=null;#Ft=null;#hi=null;#di=null;#ui=null;#pi=null;#gi=!1;#fi=null;#mi=null;#bi=null;#vi=null;#_i=!1;constructor(t){let{container:e,color:i,elements:n,titleObj:r,modificationDate:a,contentsObj:o,richText:l,parent:c,rect:h,parentRect:d,open:u}=t;this.#Ft=e,this.#vi=r,this.#hi=o,this.#bi=l,this.#ui=c,this.#Ue=i,this.#mi=h,this.#pi=d,this.#di=n;const p=s.PDFDateString.toDateObject(a);p&&(this.#ei=c.l10n.get("annotation_date_string",{date:p.toLocaleDateString(),time:p.toLocaleTimeString()})),this.trigger=n.flatMap((t=>t.getElementsToTriggerPopup()));for(const s of this.trigger)s.addEventListener("click",this.#li),s.addEventListener("mouseenter",this.#ai),s.addEventListener("mouseleave",this.#si),s.classList.add("popupTriggerArea");for(const s of n)s.container?.addEventListener("keydown",this.#ii);this.#Ft.hidden=!0,u&&this.#ci()}render(){if(this.#fi)return;const{page:{view:t},viewport:{rawDims:{pageWidth:e,pageHeight:i,pageX:s,pageY:r}}}=this.#ui,a=this.#fi=document.createElement("div");if(a.className="popup",this.#Ue){const t=a.style.outlineColor=n.Util.makeHexColor(...this.#Ue);if(CSS.supports("background-color","color-mix(in srgb, red 30%, white)"))a.style.backgroundColor=`color-mix(in srgb, ${t} 30%, white)`;else{const t=.7;a.style.backgroundColor=n.Util.makeHexColor(...this.#Ue.map((e=>Math.floor(t*(255-e)+e))))}}const o=document.createElement("span");o.className="header";const c=document.createElement("h1");if(o.append(c),({dir:c.dir,str:c.textContent}=this.#vi),a.append(o),this.#ei){const t=document.createElement("span");t.classList.add("popupDate"),this.#ei.then((e=>{t.textContent=e})),o.append(t)}const h=this.#hi,d=this.#bi;if(!d?.str||h?.str&&h.str!==d.str){const t=this._formatContents(h);a.append(t)}else l.XfaLayer.render({xfaHtml:d.html,intent:"richText",div:a}),a.lastChild.classList.add("richText","popupContent");let u=!!this.#pi,p=u?this.#pi:this.#mi;for(const l of this.#di)if(!p||null!==n.Util.intersect(l.data.rect,p)){p=l.data.rect,u=!0;break}const g=n.Util.normalizeRect([p[0],t[3]-p[1]+t[1],p[2],t[3]-p[3]+t[1]]),f=5,m=u?p[2]-p[0]+f:0,b=g[0]+m,v=g[1],{style:_}=this.#Ft;_.left=100*(b-s)/e+"%",_.top=100*(v-r)/i+"%",this.#Ft.append(a)}_formatContents(t){let{str:e,dir:i}=t;const n=document.createElement("p");n.classList.add("popupContent"),n.dir=i;const s=e.split(/(?:\r\n?|\n)/);for(let r=0,a=s.length;r<a;++r){const t=s[r];n.append(document.createTextNode(t)),r<a-1&&n.append(document.createElement("br"))}return n}#ni(t){t.altKey||t.shiftKey||t.ctrlKey||t.metaKey||("Enter"===t.key||"Escape"===t.key&&this.#gi)&&this.#ci()}#ci(){this.#gi=!this.#gi,this.#gi?(this.#oi(),this.#Ft.addEventListener("click",this.#li),this.#Ft.addEventListener("keydown",this.#ii)):(this.#ri(),this.#Ft.removeEventListener("click",this.#li),this.#Ft.removeEventListener("keydown",this.#ii))}#oi(){this.#fi||this.render(),this.isVisible?this.#gi&&this.#Ft.classList.add("focused"):(this.#Ft.hidden=!1,this.#Ft.style.zIndex=parseInt(this.#Ft.style.zIndex)+1e3)}#ri(){this.#Ft.classList.remove("focused"),!this.#gi&&this.isVisible&&(this.#Ft.hidden=!0,this.#Ft.style.zIndex=parseInt(this.#Ft.style.zIndex)-1e3)}forceHide(){this.#_i=this.isVisible,this.#_i&&(this.#Ft.hidden=!0)}maybeShow(){this.#_i&&(this.#_i=!1,this.#Ft.hidden=!1)}get isVisible(){return!1===this.#Ft.hidden}}class C extends g{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.textContent=t.data.textContent,this.textPosition=t.data.textPosition,this.annotationEditorType=n.AnnotationEditorType.FREETEXT}render(){if(this.container.classList.add("freeTextAnnotation"),this.textContent){const t=document.createElement("div");t.classList.add("annotationTextContent"),t.setAttribute("role","comment");for(const e of this.textContent){const i=document.createElement("span");i.textContent=e,t.append(i)}this.container.append(t)}return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this._editOnDoubleClick(),this.container}}e.FreeTextAnnotationElement=C;class T extends g{#yi=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("lineAnnotation");const t=this.data,{width:e,height:i}=u(t.rect),n=this.svgFactory.create(e,i,!0),s=this.#yi=this.svgFactory.createElement("svg:line");return s.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]),s.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]),s.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]),s.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]),s.setAttribute("stroke-width",t.borderStyle.width||1),s.setAttribute("stroke","transparent"),s.setAttribute("fill","transparent"),n.append(s),this.container.append(n),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#yi}addHighlightArea(){this.container.classList.add("highlightArea")}}class P extends g{#Ai=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("squareAnnotation");const t=this.data,{width:e,height:i}=u(t.rect),n=this.svgFactory.create(e,i,!0),s=t.borderStyle.width,r=this.#Ai=this.svgFactory.createElement("svg:rect");return r.setAttribute("x",s/2),r.setAttribute("y",s/2),r.setAttribute("width",e-s),r.setAttribute("height",i-s),r.setAttribute("stroke-width",s||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),n.append(r),this.container.append(n),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#Ai}addHighlightArea(){this.container.classList.add("highlightArea")}}class k extends g{#Si=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){this.container.classList.add("circleAnnotation");const t=this.data,{width:e,height:i}=u(t.rect),n=this.svgFactory.create(e,i,!0),s=t.borderStyle.width,r=this.#Si=this.svgFactory.createElement("svg:ellipse");return r.setAttribute("cx",e/2),r.setAttribute("cy",i/2),r.setAttribute("rx",e/2-s/2),r.setAttribute("ry",i/2-s/2),r.setAttribute("stroke-width",s||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),n.append(r),this.container.append(n),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#Si}addHighlightArea(){this.container.classList.add("highlightArea")}}class M extends g{#xi=null;constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="polylineAnnotation",this.svgElementName="svg:polyline"}render(){this.container.classList.add(this.containerClassName);const t=this.data,{width:e,height:i}=u(t.rect),n=this.svgFactory.create(e,i,!0);let s=[];for(const a of t.vertices){const e=a.x-t.rect[0],i=t.rect[3]-a.y;s.push(e+","+i)}s=s.join(" ");const r=this.#xi=this.svgFactory.createElement(this.svgElementName);return r.setAttribute("points",s),r.setAttribute("stroke-width",t.borderStyle.width||1),r.setAttribute("stroke","transparent"),r.setAttribute("fill","transparent"),n.append(r),this.container.append(n),!t.popupRef&&this.hasPopupData&&this._createPopup(),this.container}getElementsToTriggerPopup(){return this.#xi}addHighlightArea(){this.container.classList.add("highlightArea")}}class R extends M{constructor(t){super(t),this.containerClassName="polygonAnnotation",this.svgElementName="svg:polygon"}}class F extends g{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("caretAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}class D extends g{#Ei=[];constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0}),this.containerClassName="inkAnnotation",this.svgElementName="svg:polyline",this.annotationEditorType=n.AnnotationEditorType.INK}render(){this.container.classList.add(this.containerClassName);const t=this.data,{width:e,height:i}=u(t.rect),n=this.svgFactory.create(e,i,!0);for(const s of t.inkLists){let e=[];for(const n of s){const i=n.x-t.rect[0],s=t.rect[3]-n.y;e.push(`${i},${s}`)}e=e.join(" ");const i=this.svgFactory.createElement(this.svgElementName);this.#Ei.push(i),i.setAttribute("points",e),i.setAttribute("stroke-width",t.borderStyle.width||1),i.setAttribute("stroke","transparent"),i.setAttribute("fill","transparent"),!t.popupRef&&this.hasPopupData&&this._createPopup(),n.append(i)}return this.container.append(n),this.container}getElementsToTriggerPopup(){return this.#Ei}addHighlightArea(){this.container.classList.add("highlightArea")}}e.InkAnnotationElement=D;class I extends g{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("highlightAnnotation"),this.container}}class O extends g{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("underlineAnnotation"),this.container}}class L extends g{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("squigglyAnnotation"),this.container}}class N extends g{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0,createQuadrilaterals:!0})}render(){return!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container.classList.add("strikeoutAnnotation"),this.container}}class j extends g{constructor(t){super(t,{isRenderable:!0,ignoreBorder:!0})}render(){return this.container.classList.add("stampAnnotation"),!this.data.popupRef&&this.hasPopupData&&this._createPopup(),this.container}}e.StampAnnotationElement=j;class B extends g{#wi=null;constructor(t){super(t,{isRenderable:!0});const{filename:e,content:i}=this.data.file;this.filename=(0,s.getFilenameFromUrl)(e,!0),this.content=i,this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,filename:e,content:i})}render(){this.container.classList.add("fileAttachmentAnnotation");const{container:t,data:e}=this;let i;e.hasAppearance||0===e.fillAlpha?i=document.createElement("div"):(i=document.createElement("img"),i.src=`${this.imageResourcesPath}annotation-${/paperclip/i.test(e.name)?"paperclip":"pushpin"}.svg`,e.fillAlpha&&e.fillAlpha<1&&(i.style=`filter: opacity(${Math.round(100*e.fillAlpha)}%);`)),i.addEventListener("dblclick",this.#Ci.bind(this)),this.#wi=i;const{isMac:s}=n.FeatureTest.platform;return t.addEventListener("keydown",(t=>{"Enter"===t.key&&(s?t.metaKey:t.ctrlKey)&&this.#Ci()})),!e.popupRef&&this.hasPopupData?this._createPopup():i.classList.add("popupTriggerArea"),t.append(i),t}getElementsToTriggerPopup(){return this.#wi}addHighlightArea(){this.container.classList.add("highlightArea")}#Ci(){this.downloadManager?.openOrDownloadData(this.container,this.content,this.filename)}}class U{#Se=null;#Ti=null;#Pi=new Map;constructor(t){let{div:e,accessibilityManager:i,annotationCanvasMap:n,l10n:s,page:r,viewport:a}=t;this.div=e,this.#Se=i,this.#Ti=n,this.l10n=s,this.page=r,this.viewport=a,this.zIndex=0,this.l10n||=o.NullL10n}#ki(t,e){const i=t.firstChild||t;i.id=`${n.AnnotationPrefix}${e}`,this.div.append(t),this.#Se?.moveElementInDOM(this.div,t,i,!1)}async render(t){const{annotations:e}=t,i=this.div;(0,s.setLayerDimensions)(i,this.viewport);const a=new Map,o={data:null,layer:i,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new s.DOMSVGFactory,annotationStorage:t.annotationStorage||new r.AnnotationStorage,enableScripting:!0===t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,parent:this,elements:null};for(const s of e){if(s.noHTML)continue;const t=s.annotationType===n.AnnotationType.POPUP;if(t){const t=a.get(s.id);if(!t)continue;o.elements=t}else{const{width:t,height:e}=u(s.rect);if(t<=0||e<=0)continue}o.data=s;const e=p.create(o);if(!e.isRenderable)continue;if(!t&&s.popupRef){const t=a.get(s.popupRef);t?t.push(e):a.set(s.popupRef,[e])}e.annotationEditorType>0&&this.#Pi.set(e.data.id,e);const i=e.render();s.hidden&&(i.style.visibility="hidden"),this.#ki(i,s.id)}this.#Mi(),await this.l10n.translate(i)}update(t){let{viewport:e}=t;const i=this.div;this.viewport=e,(0,s.setLayerDimensions)(i,{rotation:e.rotation}),this.#Mi(),i.hidden=!1}#Mi(){if(!this.#Ti)return;const t=this.div;for(const[e,i]of this.#Ti){const n=t.querySelector(`[data-annotation-id="${e}"]`);if(!n)continue;const{firstChild:s}=n;s?"CANVAS"===s.nodeName?s.replaceWith(i):s.before(i):n.append(i)}this.#Ti.clear()}getEditableAnnotations(){return Array.from(this.#Pi.values())}getEditableAnnotation(t){return this.#Pi.get(t)}}e.AnnotationLayer=U},(t,e)=>{function i(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}function n(t){return Math.max(0,Math.min(255,255*t))}Object.defineProperty(e,"__esModule",{value:!0}),e.ColorConverters=void 0;class s{static CMYK_G(t){let[e,i,n,s]=t;return["G",1-Math.min(1,.3*e+.59*n+.11*i+s)]}static G_CMYK(t){let[e]=t;return["CMYK",0,0,0,1-e]}static G_RGB(t){let[e]=t;return["RGB",e,e,e]}static G_rgb(t){let[e]=t;return e=n(e),[e,e,e]}static G_HTML(t){let[e]=t;const n=i(e);return`#${n}${n}${n}`}static RGB_G(t){let[e,i,n]=t;return["G",.3*e+.59*i+.11*n]}static RGB_rgb(t){return t.map(n)}static RGB_HTML(t){return`#${t.map(i).join("")}`}static T_HTML(){return"#00000000"}static T_rgb(){return[null]}static CMYK_RGB(t){let[e,i,n,s]=t;return["RGB",1-Math.min(1,e+s),1-Math.min(1,n+s),1-Math.min(1,i+s)]}static CMYK_rgb(t){let[e,i,s,r]=t;return[n(1-Math.min(1,e+r)),n(1-Math.min(1,s+r)),n(1-Math.min(1,i+r))]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK(t){let[e,i,n]=t;const s=1-e,r=1-i,a=1-n,o=Math.min(s,r,a);return["CMYK",s,r,a,o]}}e.ColorConverters=s},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.NullL10n=void 0,e.getL10nFallback=n;const i={of_pages:"of {{pagesCount}}",page_of_pages:"({{pageNumber}} of {{pagesCount}})",document_properties_kb:"{{size_kb}} KB ({{size_b}} bytes)",document_properties_mb:"{{size_mb}} MB ({{size_b}} bytes)",document_properties_date_string:"{{date}}, {{time}}",document_properties_page_size_unit_inches:"in",document_properties_page_size_unit_millimeters:"mm",document_properties_page_size_orientation_portrait:"portrait",document_properties_page_size_orientation_landscape:"landscape",document_properties_page_size_name_a3:"A3",document_properties_page_size_name_a4:"A4",document_properties_page_size_name_letter:"Letter",document_properties_page_size_name_legal:"Legal",document_properties_page_size_dimension_string:"{{width}} × {{height}} {{unit}} ({{orientation}})",document_properties_page_size_dimension_name_string:"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})",document_properties_linearized_yes:"Yes",document_properties_linearized_no:"No",additional_layers:"Additional Layers",page_landmark:"Page {{page}}",thumb_page_title:"Page {{page}}",thumb_page_canvas:"Thumbnail of Page {{page}}",find_reached_top:"Reached top of document, continued from bottom",find_reached_bottom:"Reached end of document, continued from top","find_match_count[one]":"{{current}} of {{total}} match","find_match_count[other]":"{{current}} of {{total}} matches","find_match_count_limit[one]":"More than {{limit}} match","find_match_count_limit[other]":"More than {{limit}} matches",find_not_found:"Phrase not found",page_scale_width:"Page Width",page_scale_fit:"Page Fit",page_scale_auto:"Automatic Zoom",page_scale_actual:"Actual Size",page_scale_percent:"{{scale}}%",loading_error:"An error occurred while loading the PDF.",invalid_file_error:"Invalid or corrupted PDF file.",missing_file_error:"Missing PDF file.",unexpected_response_error:"Unexpected server response.",rendering_error:"An error occurred while rendering the page.",annotation_date_string:"{{date}}, {{time}}",printing_not_supported:"Warning: Printing is not fully supported by this browser.",printing_not_ready:"Warning: The PDF is not fully loaded for printing.",web_fonts_disabled:"Web fonts are disabled: unable to use embedded PDF fonts.",free_text2_default_content:"Start typing…",editor_free_text2_aria_label:"Text Editor",editor_ink2_aria_label:"Draw Editor",editor_ink_canvas_aria_label:"User-created image",editor_alt_text_button_label:"Alt text",editor_alt_text_edit_button_label:"Edit alt text",editor_alt_text_decorative_tooltip:"Marked as decorative"};function n(t,e){switch(t){case"find_match_count":t=`find_match_count[${1===e.total?"one":"other"}]`;break;case"find_match_count_limit":t=`find_match_count_limit[${1===e.limit?"one":"other"}]`;break}return i[t]||""}function s(t,e){return e?t.replaceAll(/\{\{\s*(\w+)\s*\}\}/g,((t,i)=>i in e?e[i]:"{{"+i+"}}")):t}i.print_progress_percent="{{progress}}%";const r={async getLanguage(){return"en-us"},async getDirection(){return"ltr"},async get(t){let e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:n(t,e);return s(i,e)},async translate(t){}};e.NullL10n=r},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.XfaLayer=void 0,i(89);var n=i(194);class s{static setupStorage(t,e,i,n,s){const r=n.getValue(e,{value:null});switch(i.name){case"textarea":if(null!==r.value&&(t.textContent=r.value),"print"===s)break;t.addEventListener("input",(t=>{n.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===i.attributes.type||"checkbox"===i.attributes.type){if(r.value===i.attributes.xfaOn?t.setAttribute("checked",!0):r.value===i.attributes.xfaOff&&t.removeAttribute("checked"),"print"===s)break;t.addEventListener("change",(t=>{n.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{if(null!==r.value&&t.setAttribute("value",r.value),"print"===s)break;t.addEventListener("input",(t=>{n.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==r.value){t.setAttribute("value",r.value);for(const t of i.children)t.attributes.value===r.value?t.attributes.selected=!0:t.attributes.hasOwnProperty("selected")&&delete t.attributes.selected}t.addEventListener("input",(t=>{const i=t.target.options,s=-1===i.selectedIndex?"":i[i.selectedIndex].value;n.setValue(e,{value:s})}));break}}static setAttributes(t){let{html:e,element:i,storage:n=null,intent:s,linkService:r}=t;const{attributes:a}=i,o=e instanceof HTMLAnchorElement;"radio"===a.type&&(a.name=`${a.name}-${s}`);for(const[l,c]of Object.entries(a))if(null!==c&&void 0!==c)switch(l){case"class":c.length&&e.setAttribute(l,c.join(" "));break;case"dataId":break;case"id":e.setAttribute("data-element-id",c);break;case"style":Object.assign(e.style,c);break;case"textContent":e.textContent=c;break;default:(!o||"href"!==l&&"newWindow"!==l)&&e.setAttribute(l,c)}o&&r.addLinkAttributes(e,a.href,a.newWindow),n&&a.dataId&&this.setupStorage(e,a.dataId,i,n)}static render(t){const e=t.annotationStorage,i=t.linkService,s=t.xfaHtml,r=t.intent||"display",a=document.createElement(s.name);s.attributes&&this.setAttributes({html:a,element:s,intent:r,linkService:i});const o=[[s,-1,a]],l=t.div;if(l.append(a),t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;l.style.transform=e}"richText"!==r&&l.setAttribute("class","xfaLayer xfaFont");const c=[];while(o.length>0){const[t,s,a]=o.at(-1);if(s+1===t.children.length){o.pop();continue}const l=t.children[++o.at(-1)[1]];if(null===l)continue;const{name:h}=l;if("#text"===h){const t=document.createTextNode(l.value);c.push(t),a.append(t);continue}const d=l?.attributes?.xmlns?document.createElementNS(l.attributes.xmlns,h):document.createElement(h);if(a.append(d),l.attributes&&this.setAttributes({html:d,element:l,storage:e,intent:r,linkService:i}),l.children&&l.children.length>0)o.push([l,-1,d]);else if(l.value){const t=document.createTextNode(l.value);n.XfaText.shouldBuildText(h)&&c.push(t),d.append(t)}}for(const n of l.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))n.setAttribute("readOnly",!0);return{textDivs:c}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e,t.div.hidden=!1}}e.XfaLayer=s},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.InkEditor=void 0,i(89),i(2);var n=i(1),s=i(164),r=i(198),a=i(168),o=i(165);class l extends s.AnnotationEditor{#Ri=0;#Fi=0;#Di=this.canvasPointermove.bind(this);#Ii=this.canvasPointerleave.bind(this);#Oi=this.canvasPointerup.bind(this);#Li=this.canvasPointerdown.bind(this);#Ni=new Path2D;#ji=!1;#Bi=!1;#Ui=!1;#zi=null;#Hi=0;#Wi=0;#qi=null;static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=1;static _type="ink";constructor(t){super({...t,name:"inkEditor"}),this.color=t.color||null,this.thickness=t.thickness||null,this.opacity=t.opacity||null,this.paths=[],this.bezierPath2D=[],this.allRawPaths=[],this.currentPath=[],this.scaleFactor=1,this.translationX=this.translationY=0,this.x=0,this.y=0,this._willKeepAspectRatio=!0}static initialize(t){s.AnnotationEditor.initialize(t,{strings:["editor_ink_canvas_aria_label","editor_ink2_aria_label"]})}static updateDefaultParams(t,e){switch(t){case n.AnnotationEditorParamsType.INK_THICKNESS:l._defaultThickness=e;break;case n.AnnotationEditorParamsType.INK_COLOR:l._defaultColor=e;break;case n.AnnotationEditorParamsType.INK_OPACITY:l._defaultOpacity=e/100;break}}updateParams(t,e){switch(t){case n.AnnotationEditorParamsType.INK_THICKNESS:this.#Gi(e);break;case n.AnnotationEditorParamsType.INK_COLOR:this.#Ve(e);break;case n.AnnotationEditorParamsType.INK_OPACITY:this.#Vi(e);break}}static get defaultPropertiesToUpdate(){return[[n.AnnotationEditorParamsType.INK_THICKNESS,l._defaultThickness],[n.AnnotationEditorParamsType.INK_COLOR,l._defaultColor||s.AnnotationEditor._defaultLineColor],[n.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*l._defaultOpacity)]]}get propertiesToUpdate(){return[[n.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||l._defaultThickness],[n.AnnotationEditorParamsType.INK_COLOR,this.color||l._defaultColor||s.AnnotationEditor._defaultLineColor],[n.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(this.opacity??l._defaultOpacity))]]}#Gi(t){const e=this.thickness;this.addCommands({cmd:()=>{this.thickness=t,this.#$i()},undo:()=>{this.thickness=e,this.#$i()},mustExec:!0,type:n.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})}#Ve(t){const e=this.color;this.addCommands({cmd:()=>{this.color=t,this.#Xi()},undo:()=>{this.color=e,this.#Xi()},mustExec:!0,type:n.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})}#Vi(t){t/=100;const e=this.opacity;this.addCommands({cmd:()=>{this.opacity=t,this.#Xi()},undo:()=>{this.opacity=e,this.#Xi()},mustExec:!0,type:n.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})}rebuild(){this.parent&&(super.rebuild(),null!==this.div&&(this.canvas||(this.#Ki(),this.#Yi()),this.isAttachedToDOM||(this.parent.add(this),this.#Ji()),this.#$i()))}remove(){null!==this.canvas&&(this.isEmpty()||this.commit(),this.canvas.width=this.canvas.height=0,this.canvas.remove(),this.canvas=null,this.#zi.disconnect(),this.#zi=null,super.remove())}setParent(t){!this.parent&&t?this._uiManager.removeShouldRescale(this):this.parent&&null===t&&this._uiManager.addShouldRescale(this),super.setParent(t)}onScaleChanging(){const[t,e]=this.parentDimensions,i=this.width*t,n=this.height*e;this.setDimensions(i,n)}enableEditMode(){this.#ji||null===this.canvas||(super.enableEditMode(),this._isDraggable=!1,this.canvas.addEventListener("pointerdown",this.#Li))}disableEditMode(){this.isInEditMode()&&null!==this.canvas&&(super.disableEditMode(),this._isDraggable=!this.isEmpty(),this.div.classList.remove("editing"),this.canvas.removeEventListener("pointerdown",this.#Li))}onceAdded(){this._isDraggable=!this.isEmpty()}isEmpty(){return 0===this.paths.length||1===this.paths.length&&0===this.paths[0].length}#Qi(){const{parentRotation:t,parentDimensions:[e,i]}=this;switch(t){case 90:return[0,i,i,e];case 180:return[e,i,e,i];case 270:return[e,0,i,e];default:return[0,0,e,i]}}#Zi(){const{ctx:t,color:e,opacity:i,thickness:n,parentScale:s,scaleFactor:r}=this;t.lineWidth=n*s/r,t.lineCap="round",t.lineJoin="round",t.miterLimit=10,t.strokeStyle=`${e}${(0,o.opacityToHex)(i)}`}#tn(t,e){this.canvas.addEventListener("contextmenu",a.noContextMenu),this.canvas.addEventListener("pointerleave",this.#Ii),this.canvas.addEventListener("pointermove",this.#Di),this.canvas.addEventListener("pointerup",this.#Oi),this.canvas.removeEventListener("pointerdown",this.#Li),this.isEditing=!0,this.#Ui||(this.#Ui=!0,this.#Ji(),this.thickness||=l._defaultThickness,this.color||=l._defaultColor||s.AnnotationEditor._defaultLineColor,this.opacity??=l._defaultOpacity),this.currentPath.push([t,e]),this.#Bi=!1,this.#Zi(),this.#qi=()=>{this.#en(),this.#qi&&window.requestAnimationFrame(this.#qi)},window.requestAnimationFrame(this.#qi)}#in(t,e){const[i,n]=this.currentPath.at(-1);if(this.currentPath.length>1&&t===i&&e===n)return;const s=this.currentPath;let r=this.#Ni;if(s.push([t,e]),this.#Bi=!0,s.length<=2)return r.moveTo(...s[0]),void r.lineTo(t,e);3===s.length&&(this.#Ni=r=new Path2D,r.moveTo(...s[0])),this.#nn(r,...s.at(-3),...s.at(-2),t,e)}#sn(){if(0===this.currentPath.length)return;const t=this.currentPath.at(-1);this.#Ni.lineTo(...t)}#rn(t,e){let i;if(this.#qi=null,t=Math.min(Math.max(t,0),this.canvas.width),e=Math.min(Math.max(e,0),this.canvas.height),this.#in(t,e),this.#sn(),1!==this.currentPath.length)i=this.#an();else{const n=[t,e];i=[[n,n.slice(),n.slice(),n]]}const n=this.#Ni,s=this.currentPath;this.currentPath=[],this.#Ni=new Path2D;const r=()=>{this.allRawPaths.push(s),this.paths.push(i),this.bezierPath2D.push(n),this.rebuild()},a=()=>{this.allRawPaths.pop(),this.paths.pop(),this.bezierPath2D.pop(),0===this.paths.length?this.remove():(this.canvas||(this.#Ki(),this.#Yi()),this.#$i())};this.addCommands({cmd:r,undo:a,mustExec:!0})}#en(){if(!this.#Bi)return;this.#Bi=!1;const t=Math.ceil(this.thickness*this.parentScale),e=this.currentPath.slice(-3),i=e.map((t=>t[0])),n=e.map((t=>t[1])),{ctx:s}=(Math.min(...i),Math.max(...i),Math.min(...n),Math.max(...n),this);s.save(),s.clearRect(0,0,this.canvas.width,this.canvas.height);for(const r of this.bezierPath2D)s.stroke(r);s.stroke(this.#Ni),s.restore()}#nn(t,e,i,n,s,r,a){const o=(e+n)/2,l=(i+s)/2,c=(n+r)/2,h=(s+a)/2;t.bezierCurveTo(o+2*(n-o)/3,l+2*(s-l)/3,c+2*(n-c)/3,h+2*(s-h)/3,c,h)}#an(){const t=this.currentPath;if(t.length<=2)return[[t[0],t[0],t.at(-1),t.at(-1)]];const e=[];let i,[n,s]=t[0];for(i=1;i<t.length-2;i++){const[r,a]=t[i],[o,l]=t[i+1],c=(r+o)/2,h=(a+l)/2,d=[n+2*(r-n)/3,s+2*(a-s)/3],u=[c+2*(r-c)/3,h+2*(a-h)/3];e.push([[n,s],d,u,[c,h]]),[n,s]=[c,h]}const[r,a]=t[i],[o,l]=t[i+1],c=[n+2*(r-n)/3,s+2*(a-s)/3],h=[o+2*(r-o)/3,l+2*(a-l)/3];return e.push([[n,s],c,h,[o,l]]),e}#Xi(){if(this.isEmpty())return void this.#on();this.#Zi();const{canvas:t,ctx:e}=this;e.setTransform(1,0,0,1,0,0),e.clearRect(0,0,t.width,t.height),this.#on();for(const i of this.bezierPath2D)e.stroke(i)}commit(){this.#ji||(super.commit(),this.isEditing=!1,this.disableEditMode(),this.setInForeground(),this.#ji=!0,this.div.classList.add("disabled"),this.#$i(!0),this.makeResizable(),this.parent.addInkEditorIfNeeded(!0),this.moveInDOM(),this.div.focus({preventScroll:!0}))}focusin(t){this._focusEventsAllowed&&(super.focusin(t),this.enableEditMode())}canvasPointerdown(t){0===t.button&&this.isInEditMode()&&!this.#ji&&(this.setInForeground(),t.preventDefault(),"mouse"!==t.type&&this.div.focus(),this.#tn(t.offsetX,t.offsetY))}canvasPointermove(t){t.preventDefault(),this.#in(t.offsetX,t.offsetY)}canvasPointerup(t){t.preventDefault(),this.#ln(t)}canvasPointerleave(t){this.#ln(t)}#ln(t){this.canvas.removeEventListener("pointerleave",this.#Ii),this.canvas.removeEventListener("pointermove",this.#Di),this.canvas.removeEventListener("pointerup",this.#Oi),this.canvas.addEventListener("pointerdown",this.#Li),setTimeout((()=>{this.canvas.removeEventListener("contextmenu",a.noContextMenu)}),10),this.#rn(t.offsetX,t.offsetY),this.addToAnnotationStorage(),this.setInBackground()}#Ki(){this.canvas=document.createElement("canvas"),this.canvas.width=this.canvas.height=0,this.canvas.className="inkEditorCanvas",s.AnnotationEditor._l10nPromise.get("editor_ink_canvas_aria_label").then((t=>this.canvas?.setAttribute("aria-label",t))),this.div.append(this.canvas),this.ctx=this.canvas.getContext("2d")}#Yi(){this.#zi=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.setDimensions(e.width,e.height)})),this.#zi.observe(this.div)}get isResizable(){return!this.isEmpty()&&this.#ji}render(){if(this.div)return this.div;let t,e;this.width&&(t=this.x,e=this.y),super.render(),s.AnnotationEditor._l10nPromise.get("editor_ink2_aria_label").then((t=>this.div?.setAttribute("aria-label",t)));const[i,n,r,a]=this.#Qi();if(this.setAt(i,n,0,0),this.setDims(r,a),this.#Ki(),this.width){const[i,n]=this.parentDimensions;this.setAspectRatio(this.width*i,this.height*n),this.setAt(t*i,e*n,this.width*i,this.height*n),this.#Ui=!0,this.#Ji(),this.setDims(this.width*i,this.height*n),this.#Xi(),this.div.classList.add("disabled")}else this.div.classList.add("editing"),this.enableEditMode();return this.#Yi(),this.div}#Ji(){if(!this.#Ui)return;const[t,e]=this.parentDimensions;this.canvas.width=Math.ceil(this.width*t),this.canvas.height=Math.ceil(this.height*e),this.#on()}setDimensions(t,e){const i=Math.round(t),n=Math.round(e);if(this.#Hi===i&&this.#Wi===n)return;this.#Hi=i,this.#Wi=n,this.canvas.style.visibility="hidden";const[s,r]=this.parentDimensions;this.width=t/s,this.height=e/r,this.fixAndSetPosition(),this.#ji&&this.#cn(t,e),this.#Ji(),this.#Xi(),this.canvas.style.visibility="visible",this.fixDims()}#cn(t,e){const i=this.#hn(),n=(t-i)/this.#Fi,s=(e-i)/this.#Ri;this.scaleFactor=Math.min(n,s)}#on(){const t=this.#hn()/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+t,this.translationY*this.scaleFactor+t)}static#dn(t){const e=new Path2D;for(let i=0,n=t.length;i<n;i++){const[n,s,r,a]=t[i];0===i&&e.moveTo(...n),e.bezierCurveTo(s[0],s[1],r[0],r[1],a[0],a[1])}return e}static#un(t,e,i){const[n,s,r,a]=e;switch(i){case 0:for(let e=0,i=t.length;e<i;e+=2)t[e]+=n,t[e+1]=a-t[e+1];break;case 90:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=t[e+1]+n,t[e+1]=i+s}break;case 180:for(let e=0,i=t.length;e<i;e+=2)t[e]=r-t[e],t[e+1]+=s;break;case 270:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=r-t[e+1],t[e+1]=a-i}break;default:throw new Error("Invalid rotation")}return t}static#pn(t,e,i){const[n,s,r,a]=e;switch(i){case 0:for(let e=0,i=t.length;e<i;e+=2)t[e]-=n,t[e+1]=a-t[e+1];break;case 90:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=t[e+1]-s,t[e+1]=i-n}break;case 180:for(let e=0,i=t.length;e<i;e+=2)t[e]=r-t[e],t[e+1]-=s;break;case 270:for(let e=0,i=t.length;e<i;e+=2){const i=t[e];t[e]=a-t[e+1],t[e+1]=r-i}break;default:throw new Error("Invalid rotation")}return t}#gn(t,e,i,n){const s=[],r=this.thickness/2,a=t*e+r,o=t*i+r;for(const c of this.paths){const e=[],i=[];for(let n=0,s=c.length;n<s;n++){const[r,l,h,d]=c[n],u=t*r[0]+a,p=t*r[1]+o,g=t*l[0]+a,f=t*l[1]+o,m=t*h[0]+a,b=t*h[1]+o,v=t*d[0]+a,_=t*d[1]+o;0===n&&(e.push(u,p),i.push(u,p)),e.push(g,f,m,b,v,_),i.push(g,f),n===s-1&&i.push(v,_)}s.push({bezier:l.#un(e,n,this.rotation),points:l.#un(i,n,this.rotation)})}return s}#fn(){let t=1/0,e=-1/0,i=1/0,s=-1/0;for(const r of this.paths)for(const[a,o,l,c]of r){const r=n.Util.bezierBoundingBox(...a,...o,...l,...c);t=Math.min(t,r[0]),i=Math.min(i,r[1]),e=Math.max(e,r[2]),s=Math.max(s,r[3])}return[t,i,e,s]}#hn(){return this.#ji?Math.ceil(this.thickness*this.parentScale):0}#$i(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(this.isEmpty())return;if(!this.#ji)return void this.#Xi();const e=this.#fn(),i=this.#hn();this.#Fi=Math.max(s.AnnotationEditor.MIN_SIZE,e[2]-e[0]),this.#Ri=Math.max(s.AnnotationEditor.MIN_SIZE,e[3]-e[1]);const n=Math.ceil(i+this.#Fi*this.scaleFactor),r=Math.ceil(i+this.#Ri*this.scaleFactor),[a,o]=this.parentDimensions;this.width=n/a,this.height=r/o,this.setAspectRatio(n,r);const l=this.translationX,c=this.translationY;this.translationX=-e[0],this.translationY=-e[1],this.#Ji(),this.#Xi(),this.#Hi=n,this.#Wi=r,this.setDims(n,r);const h=t?i/this.scaleFactor/2:0;this.translate(l-this.translationX-h,c-this.translationY-h)}static deserialize(t,e,i){if(t instanceof r.InkAnnotationElement)return null;const a=super.deserialize(t,e,i);a.thickness=t.thickness,a.color=n.Util.makeHexColor(...t.color),a.opacity=t.opacity;const[o,c]=a.pageDimensions,h=a.width*o,d=a.height*c,u=a.parentScale,p=t.thickness/2;a.#ji=!0,a.#Hi=Math.round(h),a.#Wi=Math.round(d);const{paths:g,rect:f,rotation:m}=t;for(let{bezier:n}of g){n=l.#pn(n,f,m);const t=[];a.paths.push(t);let e=u*(n[0]-p),i=u*(n[1]-p);for(let r=2,a=n.length;r<a;r+=6){const s=u*(n[r]-p),a=u*(n[r+1]-p),o=u*(n[r+2]-p),l=u*(n[r+3]-p),c=u*(n[r+4]-p),h=u*(n[r+5]-p);t.push([[e,i],[s,a],[o,l],[c,h]]),e=c,i=h}const s=this.#dn(t);a.bezierPath2D.push(s)}const b=a.#fn();return a.#Fi=Math.max(s.AnnotationEditor.MIN_SIZE,b[2]-b[0]),a.#Ri=Math.max(s.AnnotationEditor.MIN_SIZE,b[3]-b[1]),a.#cn(h,d),a}serialize(){if(this.isEmpty())return null;const t=this.getRect(0,0),e=s.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:n.AnnotationEditorType.INK,color:e,thickness:this.thickness,opacity:this.opacity,paths:this.#gn(this.scaleFactor/this.parentScale,this.translationX,this.translationY,t),pageIndex:this.pageIndex,rect:t,rotation:this.rotation,structTreeParentId:this._structTreeParentId}}}e.InkEditor=l},(t,e,i)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.StampEditor=void 0,i(149),i(152);var n=i(1),s=i(164),r=i(168),a=i(198);class o extends s.AnnotationEditor{#mn=null;#bn=null;#vn=null;#_n=null;#yn=null;#An=null;#zi=null;#Sn=null;#xn=!1;#En=!1;static _type="stamp";constructor(t){super({...t,name:"stampEditor"}),this.#_n=t.bitmapUrl,this.#yn=t.bitmapFile}static initialize(t){s.AnnotationEditor.initialize(t)}static get supportedTypes(){const t=["apng","avif","bmp","gif","jpeg","png","svg+xml","webp","x-icon"];return(0,n.shadow)(this,"supportedTypes",t.map((t=>`image/${t}`)))}static get supportedTypesStr(){return(0,n.shadow)(this,"supportedTypesStr",this.supportedTypes.join(","))}static isHandlingMimeForPasting(t){return this.supportedTypes.includes(t)}static paste(t,e){e.pasteEditor(n.AnnotationEditorType.STAMP,{bitmapFile:t.getAsFile()})}#wn(t){let e=arguments.length>1&&void 0!==arguments[1]&&arguments[1];t?(this.#mn=t.bitmap,e||(this.#bn=t.id,this.#xn=t.isSvg),this.#Ki()):this.remove()}#Cn(){this.#vn=null,this._uiManager.enableWaiting(!1),this.#An&&this.div.focus()}#Tn(){if(this.#bn)return this._uiManager.enableWaiting(!0),void this._uiManager.imageManager.getFromId(this.#bn).then((t=>this.#wn(t,!0))).finally((()=>this.#Cn()));if(this.#_n){const t=this.#_n;return this.#_n=null,this._uiManager.enableWaiting(!0),void(this.#vn=this._uiManager.imageManager.getFromUrl(t).then((t=>this.#wn(t))).finally((()=>this.#Cn())))}if(this.#yn){const t=this.#yn;return this.#yn=null,this._uiManager.enableWaiting(!0),void(this.#vn=this._uiManager.imageManager.getFromFile(t).then((t=>this.#wn(t))).finally((()=>this.#Cn())))}const t=document.createElement("input");t.type="file",t.accept=o.supportedTypesStr,this.#vn=new Promise((e=>{t.addEventListener("change",(async()=>{if(t.files&&0!==t.files.length){this._uiManager.enableWaiting(!0);const e=await this._uiManager.imageManager.getFromFile(t.files[0]);this.#wn(e)}else this.remove();e()})),t.addEventListener("cancel",(()=>{this.remove(),e()}))})).finally((()=>this.#Cn())),t.click()}remove(){this.#bn&&(this.#mn=null,this._uiManager.imageManager.deleteId(this.#bn),this.#An?.remove(),this.#An=null,this.#zi?.disconnect(),this.#zi=null),super.remove()}rebuild(){this.parent?(super.rebuild(),null!==this.div&&(this.#bn&&this.#Tn(),this.isAttachedToDOM||this.parent.add(this))):this.#bn&&this.#Tn()}onceAdded(){this._isDraggable=!0,this.div.focus()}isEmpty(){return!(this.#vn||this.#mn||this.#_n||this.#yn)}get isResizable(){return!0}render(){if(this.div)return this.div;let t,e;if(this.width&&(t=this.x,e=this.y),super.render(),this.div.hidden=!0,this.#mn?this.#Ki():this.#Tn(),this.width){const[i,n]=this.parentDimensions;this.setAt(t*i,e*n,this.width*i,this.height*n)}return this.div}#Ki(){const{div:t}=this;let{width:e,height:i}=this.#mn;const[n,s]=this.pageDimensions,r=.75;if(this.width)e=this.width*n,i=this.height*s;else if(e>r*n||i>r*s){const t=Math.min(r*n/e,r*s/i);e*=t,i*=t}const[a,o]=this.parentDimensions;this.setDims(e*a/n,i*o/s),this._uiManager.enableWaiting(!1);const l=this.#An=document.createElement("canvas");t.append(l),t.hidden=!1,this.#Pn(e,i),this.#Yi(),this.#En||(this.parent.addUndoableEditor(this),this.#En=!0),this._uiManager._eventBus.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.editorType,data:{action:"inserted_image"}}}),this.addAltTextButton()}#kn(t,e){const[i,n]=this.parentDimensions;this.width=t/i,this.height=e/n,this.setDims(t,e),this._initialOptions?.isCentered?this.center():this.fixAndSetPosition(),this._initialOptions=null,null!==this.#Sn&&clearTimeout(this.#Sn);const s=200;this.#Sn=setTimeout((()=>{this.#Sn=null,this.#Pn(t,e)}),s)}#Mn(t,e){const{width:i,height:n}=this.#mn;let s=i,r=n,a=this.#mn;while(s>2*t||r>2*e){const i=s,n=r;s>2*t&&(s=s>=16384?Math.floor(s/2)-1:Math.ceil(s/2)),r>2*e&&(r=r>=16384?Math.floor(r/2)-1:Math.ceil(r/2));const o=new OffscreenCanvas(s,r),l=o.getContext("2d");l.drawImage(a,0,0,i,n,0,0,s,r),a=o.transferToImageBitmap()}return a}#Pn(t,e){t=Math.ceil(t),e=Math.ceil(e);const i=this.#An;if(!i||i.width===t&&i.height===e)return;i.width=t,i.height=e;const n=this.#xn?this.#mn:this.#Mn(t,e),s=i.getContext("2d");s.filter=this._uiManager.hcmFilter,s.drawImage(n,0,0,n.width,n.height,0,0,t,e)}#Rn(t){if(t){if(this.#xn){const t=this._uiManager.imageManager.getSvgUrl(this.#bn);if(t)return t}const t=document.createElement("canvas");({width:t.width,height:t.height}=this.#mn);const e=t.getContext("2d");return e.drawImage(this.#mn,0,0),t.toDataURL()}if(this.#xn){const[t,e]=this.pageDimensions,i=Math.round(this.width*t*r.PixelsPerInch.PDF_TO_CSS_UNITS),n=Math.round(this.height*e*r.PixelsPerInch.PDF_TO_CSS_UNITS),s=new OffscreenCanvas(i,n),a=s.getContext("2d");return a.drawImage(this.#mn,0,0,this.#mn.width,this.#mn.height,0,0,i,n),s.transferToImageBitmap()}return structuredClone(this.#mn)}#Yi(){this.#zi=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.#kn(e.width,e.height)})),this.#zi.observe(this.div)}static deserialize(t,e,i){if(t instanceof a.StampAnnotationElement)return null;const n=super.deserialize(t,e,i),{rect:s,bitmapUrl:r,bitmapId:o,isSvg:l,accessibilityData:c}=t;o&&i.imageManager.isValidId(o)?n.#bn=o:n.#_n=r,n.#xn=l;const[h,d]=n.pageDimensions;return n.width=(s[2]-s[0])/h,n.height=(s[3]-s[1])/d,c&&(n.altTextData=c),n}serialize(){let t=arguments.length>0&&void 0!==arguments[0]&&arguments[0],e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(this.isEmpty())return null;const i={annotationType:n.AnnotationEditorType.STAMP,bitmapId:this.#bn,pageIndex:this.pageIndex,rect:this.getRect(0,0),rotation:this.rotation,isSvg:this.#xn,structTreeParentId:this._structTreeParentId};if(t)return i.bitmapUrl=this.#Rn(!0),i.accessibilityData=this.altTextData,i;const{decorative:s,altText:r}=this.altTextData;if(!s&&r&&(i.accessibilityData={type:"Figure",alt:r}),null===e)return i;e.stamps||=new Map;const a=this.#xn?(i.rect[2]-i.rect[0])*(i.rect[3]-i.rect[1]):null;if(e.stamps.has(this.#bn)){if(this.#xn){const t=e.stamps.get(this.#bn);a>t.area&&(t.area=a,t.serialized.bitmap.close(),t.serialized.bitmap=this.#Rn(!1))}}else e.stamps.set(this.#bn,{area:a,serialized:i}),i.bitmap=this.#Rn(!1);return i}}e.StampEditor=o}],__webpack_module_cache__={};function __w_pdfjs_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var i=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t].call(i.exports,i,i.exports,__w_pdfjs_require__),i.exports}var __webpack_exports__={};return(()=>{var t=__webpack_exports__;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"AbortException",{enumerable:!0,get:function(){return e.AbortException}}),Object.defineProperty(t,"AnnotationEditorLayer",{enumerable:!0,get:function(){return r.AnnotationEditorLayer}}),Object.defineProperty(t,"AnnotationEditorParamsType",{enumerable:!0,get:function(){return e.AnnotationEditorParamsType}}),Object.defineProperty(t,"AnnotationEditorType",{enumerable:!0,get:function(){return e.AnnotationEditorType}}),Object.defineProperty(t,"AnnotationEditorUIManager",{enumerable:!0,get:function(){return a.AnnotationEditorUIManager}}),Object.defineProperty(t,"AnnotationLayer",{enumerable:!0,get:function(){return o.AnnotationLayer}}),Object.defineProperty(t,"AnnotationMode",{enumerable:!0,get:function(){return e.AnnotationMode}}),Object.defineProperty(t,"CMapCompressionType",{enumerable:!0,get:function(){return e.CMapCompressionType}}),Object.defineProperty(t,"DOMSVGFactory",{enumerable:!0,get:function(){return n.DOMSVGFactory}}),Object.defineProperty(t,"FeatureTest",{enumerable:!0,get:function(){return e.FeatureTest}}),Object.defineProperty(t,"GlobalWorkerOptions",{enumerable:!0,get:function(){return l.GlobalWorkerOptions}}),Object.defineProperty(t,"ImageKind",{enumerable:!0,get:function(){return e.ImageKind}}),Object.defineProperty(t,"InvalidPDFException",{enumerable:!0,get:function(){return e.InvalidPDFException}}),Object.defineProperty(t,"MissingPDFException",{enumerable:!0,get:function(){return e.MissingPDFException}}),Object.defineProperty(t,"OPS",{enumerable:!0,get:function(){return e.OPS}}),Object.defineProperty(t,"PDFDataRangeTransport",{enumerable:!0,get:function(){return i.PDFDataRangeTransport}}),Object.defineProperty(t,"PDFDateString",{enumerable:!0,get:function(){return n.PDFDateString}}),Object.defineProperty(t,"PDFWorker",{enumerable:!0,get:function(){return i.PDFWorker}}),Object.defineProperty(t,"PasswordResponses",{enumerable:!0,get:function(){return e.PasswordResponses}}),Object.defineProperty(t,"PermissionFlag",{enumerable:!0,get:function(){return e.PermissionFlag}}),Object.defineProperty(t,"PixelsPerInch",{enumerable:!0,get:function(){return n.PixelsPerInch}}),Object.defineProperty(t,"PromiseCapability",{enumerable:!0,get:function(){return e.PromiseCapability}}),Object.defineProperty(t,"RenderingCancelledException",{enumerable:!0,get:function(){return n.RenderingCancelledException}}),Object.defineProperty(t,"SVGGraphics",{enumerable:!0,get:function(){return i.SVGGraphics}}),Object.defineProperty(t,"UnexpectedResponseException",{enumerable:!0,get:function(){return e.UnexpectedResponseException}}),Object.defineProperty(t,"Util",{enumerable:!0,get:function(){return e.Util}}),Object.defineProperty(t,"VerbosityLevel",{enumerable:!0,get:function(){return e.VerbosityLevel}}),Object.defineProperty(t,"XfaLayer",{enumerable:!0,get:function(){return c.XfaLayer}}),Object.defineProperty(t,"build",{enumerable:!0,get:function(){return i.build}}),Object.defineProperty(t,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return e.createValidAbsoluteUrl}}),Object.defineProperty(t,"getDocument",{enumerable:!0,get:function(){return i.getDocument}}),Object.defineProperty(t,"getFilenameFromUrl",{enumerable:!0,get:function(){return n.getFilenameFromUrl}}),Object.defineProperty(t,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return n.getPdfFilenameFromUrl}}),Object.defineProperty(t,"getXfaPageViewport",{enumerable:!0,get:function(){return n.getXfaPageViewport}}),Object.defineProperty(t,"isDataScheme",{enumerable:!0,get:function(){return n.isDataScheme}}),Object.defineProperty(t,"isPdfFile",{enumerable:!0,get:function(){return n.isPdfFile}}),Object.defineProperty(t,"loadScript",{enumerable:!0,get:function(){return n.loadScript}}),Object.defineProperty(t,"noContextMenu",{enumerable:!0,get:function(){return n.noContextMenu}}),Object.defineProperty(t,"normalizeUnicode",{enumerable:!0,get:function(){return e.normalizeUnicode}}),Object.defineProperty(t,"renderTextLayer",{enumerable:!0,get:function(){return s.renderTextLayer}}),Object.defineProperty(t,"setLayerDimensions",{enumerable:!0,get:function(){return n.setLayerDimensions}}),Object.defineProperty(t,"shadow",{enumerable:!0,get:function(){return e.shadow}}),Object.defineProperty(t,"updateTextLayer",{enumerable:!0,get:function(){return s.updateTextLayer}}),Object.defineProperty(t,"version",{enumerable:!0,get:function(){return i.version}});var e=__w_pdfjs_require__(1),i=__w_pdfjs_require__(124),n=__w_pdfjs_require__(168),s=__w_pdfjs_require__(195),r=__w_pdfjs_require__(196),a=__w_pdfjs_require__(165),o=__w_pdfjs_require__(198),l=__w_pdfjs_require__(176),c=__w_pdfjs_require__(201)})(),__webpack_exports__})()));