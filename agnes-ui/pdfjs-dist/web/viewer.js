/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2023 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
(()=>{"use strict";var e=[,(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GenericCom=void 0,i(2);var n=i(75),s=i(160),r=i(161),o=i(162),a=i(164);const l={};t.GenericCom=l;class d extends s.BasePreferences{async _writeToStorage(e){localStorage.setItem("pdfjs.preferences",JSON.stringify(e))}async _readFromStorage(e){return JSON.parse(localStorage.getItem("pdfjs.preferences"))}}class h extends n.DefaultExternalServices{static createDownloadManager(){return new r.DownloadManager}static createPreferences(){return new d}static createL10n(e){let{locale:t="en-US"}=e;return new o.GenericL10n(t)}static createScripting(e){let{sandboxBundleSrc:t}=e;return new a.GenericScripting(t)}}n.PDFViewerApplication.externalServices=h},(e,t,i)=>{var n=i(3),s=i(6),r=i(4),o=i(24),a=i(14),l=i(8),d=i(21),h=i(20),c=i(69),u=i(39),p=i(70),g=i(64),f=i(73),m=i(7),v=i(74),w=i(27),b=r.JSON,_=r.Number,y=r.SyntaxError,P=b&&b.parse,E=o("Object","keys"),S=Object.getOwnPropertyDescriptor,C=a("".charAt),L=a("".slice),T=a(/./.exec),M=a([].push),I=/^\d$/,x=/^[1-9]$/,A=/^(?:-|\d)$/,D=/^[\t\n\r ]$/,N=0,k=1,B=function(e,t){e=p(e);var i=new F(e,0,""),n=i.parse(),s=n.value,r=i.skip(D,n.end);if(r<e.length)throw y('Unexpected extra character: "'+C(e,r)+'" after the parsed data at: '+r);return d(t)?O({"":s},"",t,n):s},O=function(e,t,i,n){var s,r,o,a,d,p=e[t],f=n&&p===n.value,m=f&&"string"==typeof n.source?{source:n.source}:{};if(h(p)){var v=c(p),w=f?n.nodes:v?[]:{};if(v)for(s=w.length,o=g(p),a=0;a<o;a++)V(p,a,O(p,""+a,i,a<s?w[a]:void 0));else for(r=E(p),o=g(r),a=0;a<o;a++)d=r[a],V(p,d,O(p,d,i,u(w,d)?w[d]:void 0))}return l(i,e,t,p,m)},V=function(e,t,i){if(s){var n=S(e,t);if(n&&!n.configurable)return}void 0===i?delete e[t]:f(e,t,i)},R=function(e,t,i,n){this.value=e,this.end=t,this.source=i,this.nodes=n},F=function(e,t){this.source=e,this.index=t};F.prototype={fork:function(e){return new F(this.source,e)},parse:function(){var e=this.source,t=this.skip(D,this.index),i=this.fork(t),n=C(e,t);if(T(A,n))return i.number();switch(n){case"{":return i.object();case"[":return i.array();case'"':return i.string();case"t":return i.keyword(!0);case"f":return i.keyword(!1);case"n":return i.keyword(null)}throw y('Unexpected character: "'+n+'" at: '+t)},node:function(e,t,i,n,s){return new R(t,n,e?null:L(this.source,i,n),s)},object:function(){var e=this.source,t=this.index+1,i=!1,n={},s={};while(t<e.length){if(t=this.until(['"',"}"],t),"}"===C(e,t)&&!i){t++;break}var r=this.fork(t).string(),o=r.value;t=r.end,t=this.until([":"],t)+1,t=this.skip(D,t),r=this.fork(t).parse(),f(s,o,r),f(n,o,r.value),t=this.until([",","}"],r.end);var a=C(e,t);if(","===a)i=!0,t++;else if("}"===a){t++;break}}return this.node(k,n,this.index,t,s)},array:function(){var e=this.source,t=this.index+1,i=!1,n=[],s=[];while(t<e.length){if(t=this.skip(D,t),"]"===C(e,t)&&!i){t++;break}var r=this.fork(t).parse();if(M(s,r),M(n,r.value),t=this.until([",","]"],r.end),","===C(e,t))i=!0,t++;else if("]"===C(e,t)){t++;break}}return this.node(k,n,this.index,t,s)},string:function(){var e=this.index,t=v(this.source,this.index+1);return this.node(N,t.value,e,t.end)},number:function(){var e=this.source,t=this.index,i=t;if("-"===C(e,i)&&i++,"0"===C(e,i))i++;else{if(!T(x,C(e,i)))throw y("Failed to parse number at: "+i);i=this.skip(I,++i)}if("."===C(e,i)&&(i=this.skip(I,++i)),"e"===C(e,i)||"E"===C(e,i)){i++,"+"!==C(e,i)&&"-"!==C(e,i)||i++;var n=i;if(i=this.skip(I,i),n===i)throw y("Failed to parse number's exponent value at: "+i)}return this.node(N,_(L(e,t,i)),t,i)},keyword:function(e){var t=""+e,i=this.index,n=i+t.length;if(L(this.source,i,n)!==t)throw y("Failed to parse value at: "+i);return this.node(N,e,i,n)},skip:function(e,t){for(var i=this.source;t<i.length;t++)if(!T(e,C(i,t)))break;return t},until:function(e,t){t=this.skip(D,t);for(var i=C(this.source,t),n=0;n<e.length;n++)if(e[n]===i)return t;throw y('Unexpected character: "'+i+'" at: '+t)}};var U=m((function(){var e,t="9007199254740993";return P(t,(function(t,i,n){e=n.source})),e!==t})),z=w&&!m((function(){return 1/P("-0 \t")!==-1/0}));n({target:"JSON",stat:!0,forced:U},{parse:function(e,t){return z&&!d(t)?P(e):B(e,t)}})},(e,t,i)=>{var n=i(4),s=i(5).f,r=i(44),o=i(48),a=i(38),l=i(56),d=i(68);e.exports=function(e,t){var i,h,c,u,p,g,f=e.target,m=e.global,v=e.stat;if(h=m?n:v?n[f]||a(f,{}):(n[f]||{}).prototype,h)for(c in t){if(p=t[c],e.dontCallGetSet?(g=s(h,c),u=g&&g.value):u=h[c],i=d(m?c:f+(v?".":"#")+c,e.forced),!i&&void 0!==u){if(typeof p==typeof u)continue;l(p,u)}(e.sham||u&&u.sham)&&r(p,"sham",!0),o(h,c,p,e)}}},function(e){var t=function(e){return e&&e.Math===Math&&e};e.exports=t("object"==typeof globalThis&&globalThis)||t("object"==typeof window&&window)||t("object"==typeof self&&self)||t("object"==typeof global&&global)||function(){return this}()||this||Function("return this")()},(e,t,i)=>{var n=i(6),s=i(8),r=i(10),o=i(11),a=i(12),l=i(18),d=i(39),h=i(42),c=Object.getOwnPropertyDescriptor;t.f=n?c:function(e,t){if(e=a(e),t=l(t),h)try{return c(e,t)}catch(i){}if(d(e,t))return o(!s(r.f,e,t),e[t])}},(e,t,i)=>{var n=i(7);e.exports=!n((function(){return 7!==Object.defineProperty({},1,{get:function(){return 7}})[1]}))},e=>{e.exports=function(e){try{return!!e()}catch(t){return!0}}},(e,t,i)=>{var n=i(9),s=Function.prototype.call;e.exports=n?s.bind(s):function(){return s.apply(s,arguments)}},(e,t,i)=>{var n=i(7);e.exports=!n((function(){var e=function(){}.bind();return"function"!=typeof e||e.hasOwnProperty("prototype")}))},(e,t)=>{var i={}.propertyIsEnumerable,n=Object.getOwnPropertyDescriptor,s=n&&!i.call({1:2},1);t.f=s?function(e){var t=n(this,e);return!!t&&t.enumerable}:i},e=>{e.exports=function(e,t){return{enumerable:!(1&e),configurable:!(2&e),writable:!(4&e),value:t}}},(e,t,i)=>{var n=i(13),s=i(16);e.exports=function(e){return n(s(e))}},(e,t,i)=>{var n=i(14),s=i(7),r=i(15),o=Object,a=n("".split);e.exports=s((function(){return!o("z").propertyIsEnumerable(0)}))?function(e){return"String"===r(e)?a(e,""):o(e)}:o},(e,t,i)=>{var n=i(9),s=Function.prototype,r=s.call,o=n&&s.bind.bind(r,r);e.exports=n?o:function(e){return function(){return r.apply(e,arguments)}}},(e,t,i)=>{var n=i(14),s=n({}.toString),r=n("".slice);e.exports=function(e){return r(s(e),8,-1)}},(e,t,i)=>{var n=i(17),s=TypeError;e.exports=function(e){if(n(e))throw s("Can't call method on "+e);return e}},e=>{e.exports=function(e){return null===e||void 0===e}},(e,t,i)=>{var n=i(19),s=i(23);e.exports=function(e){var t=n(e,"string");return s(t)?t:t+""}},(e,t,i)=>{var n=i(8),s=i(20),r=i(23),o=i(30),a=i(33),l=i(34),d=TypeError,h=l("toPrimitive");e.exports=function(e,t){if(!s(e)||r(e))return e;var i,l=o(e,h);if(l){if(void 0===t&&(t="default"),i=n(l,e,t),!s(i)||r(i))return i;throw d("Can't convert object to primitive value")}return void 0===t&&(t="number"),a(e,t)}},(e,t,i)=>{var n=i(21),s=i(22),r=s.all;e.exports=s.IS_HTMLDDA?function(e){return"object"==typeof e?null!==e:n(e)||e===r}:function(e){return"object"==typeof e?null!==e:n(e)}},(e,t,i)=>{var n=i(22),s=n.all;e.exports=n.IS_HTMLDDA?function(e){return"function"==typeof e||e===s}:function(e){return"function"==typeof e}},e=>{var t="object"==typeof document&&document.all,i="undefined"==typeof t&&void 0!==t;e.exports={all:t,IS_HTMLDDA:i}},(e,t,i)=>{var n=i(24),s=i(21),r=i(25),o=i(26),a=Object;e.exports=o?function(e){return"symbol"==typeof e}:function(e){var t=n("Symbol");return s(t)&&r(t.prototype,a(e))}},(e,t,i)=>{var n=i(4),s=i(21),r=function(e){return s(e)?e:void 0};e.exports=function(e,t){return arguments.length<2?r(n[e]):n[e]&&n[e][t]}},(e,t,i)=>{var n=i(14);e.exports=n({}.isPrototypeOf)},(e,t,i)=>{var n=i(27);e.exports=n&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},(e,t,i)=>{var n=i(28),s=i(7),r=i(4),o=r.String;e.exports=!!Object.getOwnPropertySymbols&&!s((function(){var e=Symbol("symbol detection");return!o(e)||!(Object(e)instanceof Symbol)||!Symbol.sham&&n&&n<41}))},(e,t,i)=>{var n,s,r=i(4),o=i(29),a=r.process,l=r.Deno,d=a&&a.versions||l&&l.version,h=d&&d.v8;h&&(n=h.split("."),s=n[0]>0&&n[0]<4?1:+(n[0]+n[1])),!s&&o&&(n=o.match(/Edge\/(\d+)/),(!n||n[1]>=74)&&(n=o.match(/Chrome\/(\d+)/),n&&(s=+n[1]))),e.exports=s},e=>{e.exports="undefined"!=typeof navigator&&String(navigator.userAgent)||""},(e,t,i)=>{var n=i(31),s=i(17);e.exports=function(e,t){var i=e[t];return s(i)?void 0:n(i)}},(e,t,i)=>{var n=i(21),s=i(32),r=TypeError;e.exports=function(e){if(n(e))return e;throw r(s(e)+" is not a function")}},e=>{var t=String;e.exports=function(e){try{return t(e)}catch(i){return"Object"}}},(e,t,i)=>{var n=i(8),s=i(21),r=i(20),o=TypeError;e.exports=function(e,t){var i,a;if("string"===t&&s(i=e.toString)&&!r(a=n(i,e)))return a;if(s(i=e.valueOf)&&!r(a=n(i,e)))return a;if("string"!==t&&s(i=e.toString)&&!r(a=n(i,e)))return a;throw o("Can't convert object to primitive value")}},(e,t,i)=>{var n=i(4),s=i(35),r=i(39),o=i(41),a=i(27),l=i(26),d=n.Symbol,h=s("wks"),c=l?d["for"]||d:d&&d.withoutSetter||o;e.exports=function(e){return r(h,e)||(h[e]=a&&r(d,e)?d[e]:c("Symbol."+e)),h[e]}},(e,t,i)=>{var n=i(36),s=i(37);(e.exports=function(e,t){return s[e]||(s[e]=void 0!==t?t:{})})("versions",[]).push({version:"3.32.2",mode:n?"pure":"global",copyright:"© 2014-2023 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.32.2/LICENSE",source:"https://github.com/zloirock/core-js"})},e=>{e.exports=!1},(e,t,i)=>{var n=i(4),s=i(38),r="__core-js_shared__",o=n[r]||s(r,{});e.exports=o},(e,t,i)=>{var n=i(4),s=Object.defineProperty;e.exports=function(e,t){try{s(n,e,{value:t,configurable:!0,writable:!0})}catch(i){n[e]=t}return t}},(e,t,i)=>{var n=i(14),s=i(40),r=n({}.hasOwnProperty);e.exports=Object.hasOwn||function(e,t){return r(s(e),t)}},(e,t,i)=>{var n=i(16),s=Object;e.exports=function(e){return s(n(e))}},(e,t,i)=>{var n=i(14),s=0,r=Math.random(),o=n(1..toString);e.exports=function(e){return"Symbol("+(void 0===e?"":e)+")_"+o(++s+r,36)}},(e,t,i)=>{var n=i(6),s=i(7),r=i(43);e.exports=!n&&!s((function(){return 7!==Object.defineProperty(r("div"),"a",{get:function(){return 7}}).a}))},(e,t,i)=>{var n=i(4),s=i(20),r=n.document,o=s(r)&&s(r.createElement);e.exports=function(e){return o?r.createElement(e):{}}},(e,t,i)=>{var n=i(6),s=i(45),r=i(11);e.exports=n?function(e,t,i){return s.f(e,t,r(1,i))}:function(e,t,i){return e[t]=i,e}},(e,t,i)=>{var n=i(6),s=i(42),r=i(46),o=i(47),a=i(18),l=TypeError,d=Object.defineProperty,h=Object.getOwnPropertyDescriptor,c="enumerable",u="configurable",p="writable";t.f=n?r?function(e,t,i){if(o(e),t=a(t),o(i),"function"===typeof e&&"prototype"===t&&"value"in i&&p in i&&!i[p]){var n=h(e,t);n&&n[p]&&(e[t]=i.value,i={configurable:u in i?i[u]:n[u],enumerable:c in i?i[c]:n[c],writable:!1})}return d(e,t,i)}:d:function(e,t,i){if(o(e),t=a(t),o(i),s)try{return d(e,t,i)}catch(n){}if("get"in i||"set"in i)throw l("Accessors not supported");return"value"in i&&(e[t]=i.value),e}},(e,t,i)=>{var n=i(6),s=i(7);e.exports=n&&s((function(){return 42!==Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype}))},(e,t,i)=>{var n=i(20),s=String,r=TypeError;e.exports=function(e){if(n(e))return e;throw r(s(e)+" is not an object")}},(e,t,i)=>{var n=i(21),s=i(45),r=i(49),o=i(38);e.exports=function(e,t,i,a){a||(a={});var l=a.enumerable,d=void 0!==a.name?a.name:t;if(n(i)&&r(i,d,a),a.global)l?e[t]=i:o(t,i);else{try{a.unsafe?e[t]&&(l=!0):delete e[t]}catch(h){}l?e[t]=i:s.f(e,t,{value:i,enumerable:!1,configurable:!a.nonConfigurable,writable:!a.nonWritable})}return e}},(e,t,i)=>{var n=i(14),s=i(7),r=i(21),o=i(39),a=i(6),l=i(50).CONFIGURABLE,d=i(51),h=i(52),c=h.enforce,u=h.get,p=String,g=Object.defineProperty,f=n("".slice),m=n("".replace),v=n([].join),w=a&&!s((function(){return 8!==g((function(){}),"length",{value:8}).length})),b=String(String).split("String"),_=e.exports=function(e,t,i){"Symbol("===f(p(t),0,7)&&(t="["+m(p(t),/^Symbol\(([^)]*)\)/,"$1")+"]"),i&&i.getter&&(t="get "+t),i&&i.setter&&(t="set "+t),(!o(e,"name")||l&&e.name!==t)&&(a?g(e,"name",{value:t,configurable:!0}):e.name=t),w&&i&&o(i,"arity")&&e.length!==i.arity&&g(e,"length",{value:i.arity});try{i&&o(i,"constructor")&&i.constructor?a&&g(e,"prototype",{writable:!1}):e.prototype&&(e.prototype=void 0)}catch(s){}var n=c(e);return o(n,"source")||(n.source=v(b,"string"==typeof t?t:"")),e};Function.prototype.toString=_((function(){return r(this)&&u(this).source||d(this)}),"toString")},(e,t,i)=>{var n=i(6),s=i(39),r=Function.prototype,o=n&&Object.getOwnPropertyDescriptor,a=s(r,"name"),l=a&&"something"===function(){}.name,d=a&&(!n||n&&o(r,"name").configurable);e.exports={EXISTS:a,PROPER:l,CONFIGURABLE:d}},(e,t,i)=>{var n=i(14),s=i(21),r=i(37),o=n(Function.toString);s(r.inspectSource)||(r.inspectSource=function(e){return o(e)}),e.exports=r.inspectSource},(e,t,i)=>{var n,s,r,o=i(53),a=i(4),l=i(20),d=i(44),h=i(39),c=i(37),u=i(54),p=i(55),g="Object already initialized",f=a.TypeError,m=a.WeakMap,v=function(e){return r(e)?s(e):n(e,{})},w=function(e){return function(t){var i;if(!l(t)||(i=s(t)).type!==e)throw f("Incompatible receiver, "+e+" required");return i}};if(o||c.state){var b=c.state||(c.state=new m);b.get=b.get,b.has=b.has,b.set=b.set,n=function(e,t){if(b.has(e))throw f(g);return t.facade=e,b.set(e,t),t},s=function(e){return b.get(e)||{}},r=function(e){return b.has(e)}}else{var _=u("state");p[_]=!0,n=function(e,t){if(h(e,_))throw f(g);return t.facade=e,d(e,_,t),t},s=function(e){return h(e,_)?e[_]:{}},r=function(e){return h(e,_)}}e.exports={set:n,get:s,has:r,enforce:v,getterFor:w}},(e,t,i)=>{var n=i(4),s=i(21),r=n.WeakMap;e.exports=s(r)&&/native code/.test(String(r))},(e,t,i)=>{var n=i(35),s=i(41),r=n("keys");e.exports=function(e){return r[e]||(r[e]=s(e))}},e=>{e.exports={}},(e,t,i)=>{var n=i(39),s=i(57),r=i(5),o=i(45);e.exports=function(e,t,i){for(var a=s(t),l=o.f,d=r.f,h=0;h<a.length;h++){var c=a[h];n(e,c)||i&&n(i,c)||l(e,c,d(t,c))}}},(e,t,i)=>{var n=i(24),s=i(14),r=i(58),o=i(67),a=i(47),l=s([].concat);e.exports=n("Reflect","ownKeys")||function(e){var t=r.f(a(e)),i=o.f;return i?l(t,i(e)):t}},(e,t,i)=>{var n=i(59),s=i(66),r=s.concat("length","prototype");t.f=Object.getOwnPropertyNames||function(e){return n(e,r)}},(e,t,i)=>{var n=i(14),s=i(39),r=i(12),o=i(60).indexOf,a=i(55),l=n([].push);e.exports=function(e,t){var i,n=r(e),d=0,h=[];for(i in n)!s(a,i)&&s(n,i)&&l(h,i);while(t.length>d)s(n,i=t[d++])&&(~o(h,i)||l(h,i));return h}},(e,t,i)=>{var n=i(12),s=i(61),r=i(64),o=function(e){return function(t,i,o){var a,l=n(t),d=r(l),h=s(o,d);if(e&&i!==i){while(d>h)if(a=l[h++],a!==a)return!0}else for(;d>h;h++)if((e||h in l)&&l[h]===i)return e||h||0;return!e&&-1}};e.exports={includes:o(!0),indexOf:o(!1)}},(e,t,i)=>{var n=i(62),s=Math.max,r=Math.min;e.exports=function(e,t){var i=n(e);return i<0?s(i+t,0):r(i,t)}},(e,t,i)=>{var n=i(63);e.exports=function(e){var t=+e;return t!==t||0===t?0:n(t)}},e=>{var t=Math.ceil,i=Math.floor;e.exports=Math.trunc||function(e){var n=+e;return(n>0?i:t)(n)}},(e,t,i)=>{var n=i(65);e.exports=function(e){return n(e.length)}},(e,t,i)=>{var n=i(62),s=Math.min;e.exports=function(e){return e>0?s(n(e),9007199254740991):0}},e=>{e.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},(e,t)=>{t.f=Object.getOwnPropertySymbols},(e,t,i)=>{var n=i(7),s=i(21),r=/#|\.prototype\./,o=function(e,t){var i=l[a(e)];return i===h||i!==d&&(s(t)?n(t):!!t)},a=o.normalize=function(e){return String(e).replace(r,".").toLowerCase()},l=o.data={},d=o.NATIVE="N",h=o.POLYFILL="P";e.exports=o},(e,t,i)=>{var n=i(15);e.exports=Array.isArray||function(e){return"Array"===n(e)}},(e,t,i)=>{var n=i(71),s=String;e.exports=function(e){if("Symbol"===n(e))throw TypeError("Cannot convert a Symbol value to a string");return s(e)}},(e,t,i)=>{var n=i(72),s=i(21),r=i(15),o=i(34),a=o("toStringTag"),l=Object,d="Arguments"===r(function(){return arguments}()),h=function(e,t){try{return e[t]}catch(i){}};e.exports=n?r:function(e){var t,i,n;return void 0===e?"Undefined":null===e?"Null":"string"==typeof(i=h(t=l(e),a))?i:d?r(t):"Object"===(n=r(t))&&s(t.callee)?"Arguments":n}},(e,t,i)=>{var n=i(34),s=n("toStringTag"),r={};r[s]="z",e.exports="[object z]"===String(r)},(e,t,i)=>{var n=i(18),s=i(45),r=i(11);e.exports=function(e,t,i){var o=n(t);o in e?s.f(e,o,r(0,i)):e[o]=i}},(e,t,i)=>{var n=i(14),s=i(39),r=SyntaxError,o=parseInt,a=String.fromCharCode,l=n("".charAt),d=n("".slice),h=n(/./.exec),c={'\\"':'"',"\\\\":"\\","\\/":"/","\\b":"\b","\\f":"\f","\\n":"\n","\\r":"\r","\\t":"\t"},u=/^[\da-f]{4}$/i,p=/^[\u0000-\u001F]$/;e.exports=function(e,t){var i=!0,n="";while(t<e.length){var g=l(e,t);if("\\"===g){var f=d(e,t,t+2);if(s(c,f))n+=c[f],t+=2;else{if("\\u"!==f)throw r('Unknown escape sequence: "'+f+'"');t+=2;var m=d(e,t,t+4);if(!h(u,m))throw r("Bad Unicode escape at: "+t);n+=a(o(m,16)),t+=4}}else{if('"'===g){i=!1,t++;break}if(h(p,g))throw r("Bad control character in string literal at: "+t);n+=g,t++}}if(i)throw r("Unterminated string at: "+t);return{value:n,end:t}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFViewerApplication=t.PDFPrintServiceFactory=t.DefaultExternalServices=void 0,i(76),i(89),i(92),i(94),i(95);var n=i(97),s=i(122),r=i(123),o=i(124),a=i(125),l=i(126),d=i(127),h=i(128),c=i(129),u=i(130),p=i(132),g=i(134),f=i(135),m=i(136),v=i(138),w=i(139),b=i(140),_=i(141),y=i(142),P=i(143),E=i(144),S=i(145),C=i(147),L=i(157),T=i(158),M=i(159);const I=1e4,x=1e3,A={UNKNOWN:-1,PREVIOUS:0,INITIAL:1},D={AUTOMATIC:0,LIGHT:1,DARK:2};class N{constructor(){throw new Error("Cannot initialize DefaultExternalServices.")}static updateFindControlState(e){}static updateFindMatchesCount(e){}static initPassiveLoading(e){}static reportTelemetry(e){}static createDownloadManager(){throw new Error("Not implemented: createDownloadManager")}static createPreferences(){throw new Error("Not implemented: createPreferences")}static createL10n(e){throw new Error("Not implemented: createL10n")}static createScripting(e){throw new Error("Not implemented: createScripting")}static get supportsPinchToZoom(){return(0,s.shadow)(this,"supportsPinchToZoom",!0)}static get supportsIntegratedFind(){return(0,s.shadow)(this,"supportsIntegratedFind",!1)}static get supportsDocumentFonts(){return(0,s.shadow)(this,"supportsDocumentFonts",!0)}static get supportedMouseWheelZoomModifierKeys(){return(0,s.shadow)(this,"supportedMouseWheelZoomModifierKeys",{ctrlKey:!0,metaKey:!0})}static get isInAutomation(){return(0,s.shadow)(this,"isInAutomation",!1)}static updateEditorStates(e){throw new Error("Not implemented: updateEditorStates")}static get canvasMaxAreaInBytes(){return(0,s.shadow)(this,"canvasMaxAreaInBytes",-1)}static getNimbusExperimentData(){return(0,s.shadow)(this,"getNimbusExperimentData",Promise.resolve(null))}}t.DefaultExternalServices=N;const k={initialBookmark:document.location.hash.substring(1),_initializedCapability:new s.PromiseCapability,appConfig:null,pdfDocument:null,pdfLoadingTask:null,printService:null,pdfViewer:null,pdfThumbnailViewer:null,pdfRenderingQueue:null,pdfPresentationMode:null,pdfDocumentProperties:null,pdfLinkService:null,pdfHistory:null,pdfSidebar:null,pdfOutlineViewer:null,pdfAttachmentViewer:null,pdfLayerViewer:null,pdfCursorTools:null,pdfScriptingManager:null,store:null,downloadManager:null,overlayManager:null,preferences:null,toolbar:null,secondaryToolbar:null,eventBus:null,l10n:null,annotationEditorParams:null,isInitialViewSet:!1,downloadComplete:!1,isViewerEmbedded:window.parent!==window,url:"",baseUrl:"",_downloadUrl:"",externalServices:N,_boundEvents:Object.create(null),documentInfo:null,metadata:null,_contentDispositionFilename:null,_contentLength:null,_saveInProgress:!1,_wheelUnusedTicks:0,_wheelUnusedFactor:1,_touchUnusedTicks:0,_touchUnusedFactor:1,_PDFBug:null,_hasAnnotationEditors:!1,_title:document.title,_printAnnotationStoragePromise:null,_touchInfo:null,_isCtrlKeyDown:!1,_nimbusDataPromise:null,async initialize(e){this.preferences=this.externalServices.createPreferences(),this.appConfig=e,await this._initializeOptions(),this._forceCssTheme(),await this._initializeL10n(),this.isViewerEmbedded&&r.AppOptions.get("externalLinkTarget")===a.LinkTarget.NONE&&r.AppOptions.set("externalLinkTarget",a.LinkTarget.TOP),await this._initializeViewerComponents(),this.bindEvents(),this.bindWindowEvents();const t=e.appContainer||document.documentElement;this.l10n.translate(t).then((()=>{this.eventBus.dispatch("localized",{source:this})})),this._initializedCapability.resolve()},async _initializeOptions(){if(r.AppOptions.get("disablePreferences"))r.AppOptions.get("pdfBugEnabled")&&await this._parseHashParams();else{r.AppOptions._hasUserOptions()&&console.warn('_initializeOptions: The Preferences may override manually set AppOptions; please use the "disablePreferences"-option in order to prevent that.');try{r.AppOptions.setAll(await this.preferences.getAll())}catch(e){console.error(`_initializeOptions: "${e.message}".`)}r.AppOptions.get("pdfBugEnabled")&&await this._parseHashParams()}},async _parseHashParams(){const e=document.location.hash.substring(1);if(!e)return;const{mainContainer:t,viewerContainer:i}=this.appConfig,s=(0,n.parseQueryString)(e);if("true"===s.get("disableworker"))try{await O()}catch(o){console.error(`_parseHashParams: "${o.message}".`)}if(s.has("disablerange")&&r.AppOptions.set("disableRange","true"===s.get("disablerange")),s.has("disablestream")&&r.AppOptions.set("disableStream","true"===s.get("disablestream")),s.has("disableautofetch")&&r.AppOptions.set("disableAutoFetch","true"===s.get("disableautofetch")),s.has("disablefontface")&&r.AppOptions.set("disableFontFace","true"===s.get("disablefontface")),s.has("disablehistory")&&r.AppOptions.set("disableHistory","true"===s.get("disablehistory")),s.has("verbosity")&&r.AppOptions.set("verbosity",0|s.get("verbosity")),s.has("textlayer"))switch(s.get("textlayer")){case"off":r.AppOptions.set("textLayerMode",n.TextLayerMode.DISABLE);break;case"visible":case"shadow":case"hover":i.classList.add(`textLayer-${s.get("textlayer")}`);try{await V(this),this._PDFBug.loadCSS()}catch(o){console.error(`_parseHashParams: "${o.message}".`)}break}if(s.has("pdfbug")){r.AppOptions.set("pdfBug",!0),r.AppOptions.set("fontExtraProperties",!0);const e=s.get("pdfbug").split(",");try{await V(this),this._PDFBug.init(t,e)}catch(o){console.error(`_parseHashParams: "${o.message}".`)}}s.has("locale")&&r.AppOptions.set("locale",s.get("locale"))},async _initializeL10n(){this.l10n=this.externalServices.createL10n({locale:r.AppOptions.get("locale")});const e=await this.l10n.getDirection();document.getElementsByTagName("html")[0].dir=e},_forceCssTheme(){const e=r.AppOptions.get("viewerCssTheme");if(e!==D.AUTOMATIC&&Object.values(D).includes(e))try{const t=document.styleSheets[0],i=t?.cssRules||[];for(let n=0,s=i.length;n<s;n++){const s=i[n];if(s instanceof CSSMediaRule&&"(prefers-color-scheme: dark)"===s.media?.[0]){if(e===D.LIGHT)return void t.deleteRule(n);const i=/^@media \(prefers-color-scheme: dark\) {\n\s*([\w\s-.,:;/\\{}()]+)\n}$/.exec(s.cssText);return void(i?.[1]&&(t.deleteRule(n),t.insertRule(i[1],n)))}}}catch(t){console.error(`_forceCssTheme: "${t?.message}".`)}},async _initializeViewerComponents(){const{appConfig:e,externalServices:t,l10n:i}=this,M=t.isInAutomation?new o.AutomationEventBus:new o.EventBus;this.eventBus=M,this.overlayManager=new h.OverlayManager;const I=new y.PDFRenderingQueue;I.onIdle=this._cleanup.bind(this),this.pdfRenderingQueue=I;const x=new a.PDFLinkService({eventBus:M,externalLinkTarget:r.AppOptions.get("externalLinkTarget"),externalLinkRel:r.AppOptions.get("externalLinkRel"),ignoreDestinationZoom:r.AppOptions.get("ignoreDestinationZoom")});this.pdfLinkService=x;const A=t.createDownloadManager();this.downloadManager=A;const D=new m.PDFFindController({linkService:x,eventBus:M,updateMatchesCountOnProgress:!0});this.findController=D;const N=new P.PDFScriptingManager({eventBus:M,sandboxBundleSrc:r.AppOptions.get("sandboxBundleSrc"),externalServices:t,docProperties:this._scriptingDocProperties.bind(this)});this.pdfScriptingManager=N;const k=e.mainContainer,B=e.viewerContainer,O=r.AppOptions.get("annotationEditorMode"),V=r.AppOptions.get("isOffscreenCanvasSupported")&&s.FeatureTest.isOffscreenCanvasSupported,R=r.AppOptions.get("forcePageColors")||window.matchMedia("(forced-colors: active)").matches?{background:r.AppOptions.get("pageColorsBackground"),foreground:r.AppOptions.get("pageColorsForeground")}:null,F=e.altTextDialog?new l.AltTextManager(e.altTextDialog,k,this.overlayManager,M):null,U=new C.PDFViewer({container:k,viewer:B,eventBus:M,renderingQueue:I,linkService:x,downloadManager:A,altTextManager:F,findController:D,scriptingManager:r.AppOptions.get("enableScripting")&&N,l10n:i,textLayerMode:r.AppOptions.get("textLayerMode"),annotationMode:r.AppOptions.get("annotationMode"),annotationEditorMode:O,imageResourcesPath:r.AppOptions.get("imageResourcesPath"),enablePrintAutoRotate:r.AppOptions.get("enablePrintAutoRotate"),isOffscreenCanvasSupported:V,maxCanvasPixels:r.AppOptions.get("maxCanvasPixels"),enablePermissions:r.AppOptions.get("enablePermissions"),pageColors:R});if(this.pdfViewer=U,I.setViewer(U),x.setViewer(U),N.setViewer(U),e.sidebar?.thumbnailView&&(this.pdfThumbnailViewer=new S.PDFThumbnailViewer({container:e.sidebar.thumbnailView,eventBus:M,renderingQueue:I,linkService:x,l10n:i,pageColors:R}),I.setThumbnailViewer(this.pdfThumbnailViewer)),this.isViewerEmbedded||r.AppOptions.get("disableHistory")||(this.pdfHistory=new v.PDFHistory({linkService:x,eventBus:M}),x.setHistory(this.pdfHistory)),!this.supportsIntegratedFind&&e.findBar&&(this.findBar=new f.PDFFindBar(e.findBar,M,i)),e.annotationEditorParams)if(O!==s.AnnotationEditorType.DISABLE)r.AppOptions.get("enableStampEditor")&&V&&e.toolbar?.editorStampButton?.classList.remove("hidden"),this.annotationEditorParams=new d.AnnotationEditorParams(e.annotationEditorParams,M);else for(const n of["editorModeButtons","editorModeSeparator"])document.getElementById(n)?.classList.add("hidden");e.documentProperties&&(this.pdfDocumentProperties=new g.PDFDocumentProperties(e.documentProperties,this.overlayManager,M,i,(()=>this._docFilename))),e.secondaryToolbar?.cursorHandToolButton&&(this.pdfCursorTools=new p.PDFCursorTools({container:k,eventBus:M,cursorToolOnLoad:r.AppOptions.get("cursorToolOnLoad")})),e.toolbar&&(this.toolbar=new T.Toolbar(e.toolbar,M,i)),e.secondaryToolbar&&(this.secondaryToolbar=new L.SecondaryToolbar(e.secondaryToolbar,M)),this.supportsFullscreen&&e.secondaryToolbar?.presentationModeButton&&(this.pdfPresentationMode=new _.PDFPresentationMode({container:k,pdfViewer:U,eventBus:M})),e.passwordOverlay&&(this.passwordPrompt=new c.PasswordPrompt(e.passwordOverlay,this.overlayManager,i,this.isViewerEmbedded)),e.sidebar?.outlineView&&(this.pdfOutlineViewer=new b.PDFOutlineViewer({container:e.sidebar.outlineView,eventBus:M,linkService:x,downloadManager:A})),e.sidebar?.attachmentsView&&(this.pdfAttachmentViewer=new u.PDFAttachmentViewer({container:e.sidebar.attachmentsView,eventBus:M,downloadManager:A})),e.sidebar?.layersView&&(this.pdfLayerViewer=new w.PDFLayerViewer({container:e.sidebar.layersView,eventBus:M,l10n:i})),e.sidebar&&(this.pdfSidebar=new E.PDFSidebar({elements:e.sidebar,eventBus:M,l10n:i}),this.pdfSidebar.onToggled=this.forceRendering.bind(this),this.pdfSidebar.onUpdateThumbnails=()=>{for(const e of U.getCachedPageViews())e.renderingState===n.RenderingStates.FINISHED&&this.pdfThumbnailViewer.getThumbnail(e.id-1)?.setImage(e);this.pdfThumbnailViewer.scrollThumbnailIntoView(U.currentPageNumber)})},async run(e){await this.initialize(e);const{appConfig:t,eventBus:i}=this;let s;const o=document.location.search.substring(1),a=(0,n.parseQueryString)(o);s=a.get("file")??r.AppOptions.get("defaultUrl"),B(s),t.mainContainer.addEventListener("dragover",(function(e){e.preventDefault(),e.dataTransfer.dropEffect="copy"===e.dataTransfer.effectAllowed?"copy":"move"})),t.mainContainer.addEventListener("drop",(function(e){e.preventDefault();const{files:t}=e.dataTransfer;t&&0!==t.length&&i.dispatch("fileinputchange",{source:this,fileInput:e.dataTransfer})})),this.supportsDocumentFonts||(r.AppOptions.set("disableFontFace",!0),this.l10n.get("web_fonts_disabled").then((e=>{console.warn(e)}))),this.supportsPrinting||(t.toolbar?.print?.classList.add("hidden"),t.secondaryToolbar?.printButton.classList.add("hidden")),this.supportsFullscreen||t.secondaryToolbar?.presentationModeButton.classList.add("hidden"),this.supportsIntegratedFind&&t.toolbar?.viewFind?.classList.add("hidden"),t.mainContainer.addEventListener("transitionend",(function(e){e.target===this&&i.dispatch("resize",{source:this})}),!0),s?this.open({url:s}):this._hideViewBookmark()},get initialized(){return this._initializedCapability.settled},get initializedPromise(){return this._initializedCapability.promise},zoomIn(e,t){this.pdfViewer.isInPresentationMode||this.pdfViewer.increaseScale({drawingDelay:r.AppOptions.get("defaultZoomDelay"),steps:e,scaleFactor:t})},zoomOut(e,t){this.pdfViewer.isInPresentationMode||this.pdfViewer.decreaseScale({drawingDelay:r.AppOptions.get("defaultZoomDelay"),steps:e,scaleFactor:t})},zoomReset(){this.pdfViewer.isInPresentationMode||(this.pdfViewer.currentScaleValue=n.DEFAULT_SCALE_VALUE)},get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0},get page(){return this.pdfViewer.currentPageNumber},set page(e){this.pdfViewer.currentPageNumber=e},get supportsPrinting(){return Ve.instance.supportsPrinting},get supportsFullscreen(){return(0,s.shadow)(this,"supportsFullscreen",document.fullscreenEnabled)},get supportsPinchToZoom(){return this.externalServices.supportsPinchToZoom},get supportsIntegratedFind(){return this.externalServices.supportsIntegratedFind},get supportsDocumentFonts(){return this.externalServices.supportsDocumentFonts},get loadingBar(){const e=document.getElementById("loadingBar"),t=e?new n.ProgressBar(e):null;return(0,s.shadow)(this,"loadingBar",t)},get supportedMouseWheelZoomModifierKeys(){return this.externalServices.supportedMouseWheelZoomModifierKeys},initPassiveLoading(e){throw new Error("Not implemented: initPassiveLoading")},setTitleUsingUrl(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.url=e,this.baseUrl=e.split("#")[0],t&&(this._downloadUrl=t===e?this.baseUrl:t.split("#")[0]),(0,s.isDataScheme)(e)&&this._hideViewBookmark();let i=(0,s.getPdfFilenameFromUrl)(e,"");if(!i)try{i=decodeURIComponent((0,s.getFilenameFromUrl)(e))||e}catch{i=e}this.setTitle(i)},setTitle(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._title;if(this._title=e,this.isViewerEmbedded)return;const t=this._hasAnnotationEditors&&!this.pdfRenderingQueue.printing;document.title=`${t?"* ":""}${e}`},get _docFilename(){return this._contentDispositionFilename||(0,s.getPdfFilenameFromUrl)(this.url)},_hideViewBookmark(){const{secondaryToolbar:e}=this.appConfig;e?.viewBookmarkButton.classList.add("hidden"),e?.presentationModeButton.classList.contains("hidden")&&document.getElementById("viewBookmarkSeparator")?.classList.add("hidden")},async close(){if(this._unblockDocumentLoadEvent(),this._hideViewBookmark(),!this.pdfLoadingTask)return;if(this.pdfDocument?.annotationStorage.size>0&&this._annotationStorageModified)try{await this.save()}catch{}const e=[];e.push(this.pdfLoadingTask.destroy()),this.pdfLoadingTask=null,this.pdfDocument&&(this.pdfDocument=null,this.pdfThumbnailViewer?.setDocument(null),this.pdfViewer.setDocument(null),this.pdfLinkService.setDocument(null),this.pdfDocumentProperties?.setDocument(null)),this.pdfLinkService.externalLinkEnabled=!0,this.store=null,this.isInitialViewSet=!1,this.downloadComplete=!1,this.url="",this.baseUrl="",this._downloadUrl="",this.documentInfo=null,this.metadata=null,this._contentDispositionFilename=null,this._contentLength=null,this._saveInProgress=!1,this._hasAnnotationEditors=!1,e.push(this.pdfScriptingManager.destroyPromise,this.passwordPrompt.close()),this.setTitle(),this.pdfSidebar?.reset(),this.pdfOutlineViewer?.reset(),this.pdfAttachmentViewer?.reset(),this.pdfLayerViewer?.reset(),this.pdfHistory?.reset(),this.findBar?.reset(),this.toolbar?.reset(),this.secondaryToolbar?.reset(),this._PDFBug?.cleanup(),await Promise.all(e)},async open(e){let t=!1;"string"===typeof e?(e={url:e},t=!0):e?.byteLength&&(e={data:e},t=!0),t&&console.error("The `PDFViewerApplication.open` signature was updated, please use an object instead."),this.pdfLoadingTask&&await this.close();const i=r.AppOptions.getAll(r.OptionKind.WORKER);Object.assign(s.GlobalWorkerOptions,i),e.url&&this.setTitleUsingUrl(e.originalUrl||e.url,e.url);const n=r.AppOptions.getAll(r.OptionKind.API),o={canvasMaxAreaInBytes:this.externalServices.canvasMaxAreaInBytes,...n,...e},a=(0,s.getDocument)(o);return this.pdfLoadingTask=a,a.onPassword=(e,t)=>{this.isViewerEmbedded&&this._unblockDocumentLoadEvent(),this.pdfLinkService.externalLinkEnabled=!1,this.passwordPrompt.setUpdateCallback(e,t),this.passwordPrompt.open()},a.onProgress=e=>{let{loaded:t,total:i}=e;this.progress(t/i)},a.promise.then((e=>{this.load(e)}),(e=>{if(a!==this.pdfLoadingTask)return;let t="loading_error";return e instanceof s.InvalidPDFException?t="invalid_file_error":e instanceof s.MissingPDFException?t="missing_file_error":e instanceof s.UnexpectedResponseException&&(t="unexpected_response_error"),this.l10n.get(t).then((t=>{throw this._documentError(t,{message:e?.message}),e}))}))},_ensureDownloadComplete(){if(!this.pdfDocument||!this.downloadComplete)throw new Error("PDF document not downloaded.")},async download(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=this._downloadUrl,i=this._docFilename;try{this._ensureDownloadComplete();const n=await this.pdfDocument.getData(),s=new Blob([n],{type:"application/pdf"});await this.downloadManager.download(s,t,i,e)}catch{await this.downloadManager.downloadUrl(t,i,e)}},async save(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(this._saveInProgress)return;this._saveInProgress=!0,await this.pdfScriptingManager.dispatchWillSave();const t=this._downloadUrl,i=this._docFilename;try{this._ensureDownloadComplete();const n=await this.pdfDocument.saveDocument(),s=new Blob([n],{type:"application/pdf"});await this.downloadManager.download(s,t,i,e)}catch(n){console.error(`Error when saving the document: ${n.message}`),await this.download(e)}finally{await this.pdfScriptingManager.dispatchDidSave(),this._saveInProgress=!1}this._hasAnnotationEditors&&this.externalServices.reportTelemetry({type:"editing",data:{type:"save"}})},downloadOrSave(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.pdfDocument?.annotationStorage.size>0?this.save(e):this.download(e)},openInExternalApp(){this.downloadOrSave({openInExternalApp:!0})},_documentError(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this._unblockDocumentLoadEvent(),this._otherError(e,t),this.eventBus.dispatch("documenterror",{source:this,message:e,reason:t?.message??null})},_otherError(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const i=[`PDF.js v${s.version||"?"} (build: ${s.build||"?"})`];t&&(i.push(`Message: ${t.message}`),t.stack?i.push(`Stack: ${t.stack}`):(t.filename&&i.push(`File: ${t.filename}`),t.lineNumber&&i.push(`Line: ${t.lineNumber}`))),console.error(`${e}\n\n${i.join("\n")}`)},progress(e){if(!this.loadingBar||this.downloadComplete)return;const t=Math.round(100*e);t<=this.loadingBar.percent||(this.loadingBar.percent=t,(this.pdfDocument?.loadingParams.disableAutoFetch??r.AppOptions.get("disableAutoFetch"))&&this.loadingBar.setDisableAutoFetch())},load(e){this.pdfDocument=e,e.getDownloadInfo().then((e=>{let{length:t}=e;this._contentLength=t,this.downloadComplete=!0,this.loadingBar?.hide(),a.then((()=>{this.eventBus.dispatch("documentloaded",{source:this})}))}));const t=e.getPageLayout().catch((()=>{})),i=e.getPageMode().catch((()=>{})),s=e.getOpenAction().catch((()=>{}));this.toolbar?.setPagesCount(e.numPages,!1),this.secondaryToolbar?.setPagesCount(e.numPages),this.pdfLinkService.setDocument(e),this.pdfDocumentProperties?.setDocument(e);const o=this.pdfViewer;o.setDocument(e);const{firstPagePromise:a,onePageRendered:l,pagesPromise:d}=o;this.pdfThumbnailViewer?.setDocument(e);const h=(this.store=new M.ViewHistory(e.fingerprints[0])).getMultiple({page:null,zoom:n.DEFAULT_SCALE_VALUE,scrollLeft:"0",scrollTop:"0",rotation:null,sidebarView:n.SidebarView.UNKNOWN,scrollMode:n.ScrollMode.UNKNOWN,spreadMode:n.SpreadMode.UNKNOWN}).catch((()=>{}));a.then((a=>{this.loadingBar?.setWidth(this.appConfig.viewerContainer),this._initializeAnnotationStorageCallbacks(e),Promise.all([n.animationStarted,h,t,i,s]).then((async t=>{let[i,s,a,l,h]=t;const c=r.AppOptions.get("viewOnLoad");this._initializePdfHistory({fingerprint:e.fingerprints[0],viewOnLoad:c,initialDest:h?.dest});const u=this.initialBookmark,p=r.AppOptions.get("defaultZoomValue");let g=p?`zoom=${p}`:null,f=null,m=r.AppOptions.get("sidebarViewOnLoad"),v=r.AppOptions.get("scrollModeOnLoad"),w=r.AppOptions.get("spreadModeOnLoad");if(s?.page&&c!==A.INITIAL&&(g=`page=${s.page}&zoom=${p||s.zoom},${s.scrollLeft},${s.scrollTop}`,f=parseInt(s.rotation,10),m===n.SidebarView.UNKNOWN&&(m=0|s.sidebarView),v===n.ScrollMode.UNKNOWN&&(v=0|s.scrollMode),w===n.SpreadMode.UNKNOWN&&(w=0|s.spreadMode)),l&&m===n.SidebarView.UNKNOWN&&(m=(0,n.apiPageModeToSidebarView)(l)),a&&v===n.ScrollMode.UNKNOWN&&w===n.SpreadMode.UNKNOWN){const e=(0,n.apiPageLayoutToViewerModes)(a);w=e.spreadMode}this.setInitialView(g,{rotation:f,sidebarView:m,scrollMode:v,spreadMode:w}),this.eventBus.dispatch("documentinit",{source:this}),this.isViewerEmbedded||o.focus(),await Promise.race([d,new Promise((e=>{setTimeout(e,I)}))]),(u||g)&&(o.hasEqualPageSizes||(this.initialBookmark=u,o.currentScaleValue=o.currentScaleValue,this.setInitialView(g)))})).catch((()=>{this.setInitialView()})).then((function(){o.update()}))})),d.then((()=>{this._unblockDocumentLoadEvent(),this._initializeAutoPrint(e,s)}),(e=>{this.l10n.get("loading_error").then((t=>{this._documentError(t,{message:e?.message})}))})),l.then((t=>{this.externalServices.reportTelemetry({type:"pageInfo",timestamp:t.timestamp}),this.pdfOutlineViewer&&e.getOutline().then((t=>{e===this.pdfDocument&&this.pdfOutlineViewer.render({outline:t,pdfDocument:e})})),this.pdfAttachmentViewer&&e.getAttachments().then((t=>{e===this.pdfDocument&&this.pdfAttachmentViewer.render({attachments:t})})),this.pdfLayerViewer&&o.optionalContentConfigPromise.then((t=>{e===this.pdfDocument&&this.pdfLayerViewer.render({optionalContentConfig:t,pdfDocument:e})}))})),this._initializePageLabels(e),this._initializeMetadata(e)},async _scriptingDocProperties(e){return(this.documentInfo||(await new Promise((e=>{this.eventBus._on("metadataloaded",e,{once:!0})})),e===this.pdfDocument))&&(this._contentLength||(await new Promise((e=>{this.eventBus._on("documentloaded",e,{once:!0})})),e===this.pdfDocument))?{...this.documentInfo,baseURL:this.baseUrl,filesize:this._contentLength,filename:this._docFilename,metadata:this.metadata?.getRaw(),authors:this.metadata?.get("dc:creator"),numPages:this.pagesCount,URL:this.url}:null},async _initializeAutoPrint(e,t){const[i,s]=await Promise.all([t,this.pdfViewer.enableScripting?null:e.getJSActions()]);if(e!==this.pdfDocument)return;let r="Print"===i?.action;if(s){console.warn("Warning: JavaScript support is not enabled");for(const e in s){if(r)break;switch(e){case"WillClose":case"WillSave":case"DidSave":case"WillPrint":case"DidPrint":continue}r=s[e].some((e=>n.AutoPrintRegExp.test(e)))}}r&&this.triggerPrinting()},async _initializeMetadata(e){const{info:t,metadata:i,contentDispositionFilename:n,contentLength:r}=await e.getMetadata();if(e!==this.pdfDocument)return;this.documentInfo=t,this.metadata=i,this._contentDispositionFilename??=n,this._contentLength??=r,console.log(`PDF ${e.fingerprints[0]} [${t.PDFFormatVersion} ${(t.Producer||"-").trim()} / ${(t.Creator||"-").trim()}] (PDF.js: ${s.version||"?"} [${s.build||"?"}])`);let o=t.Title;const a=i?.get("dc:title");a&&("Untitled"===a||/[\uFFF0-\uFFFF]/g.test(a)||(o=a)),o?this.setTitle(`${o} - ${this._contentDispositionFilename||this._title}`):this._contentDispositionFilename&&this.setTitle(this._contentDispositionFilename),!t.IsXFAPresent||t.IsAcroFormPresent||e.isPureXfa?!t.IsAcroFormPresent&&!t.IsXFAPresent||this.pdfViewer.renderForms||console.warn("Warning: Interactive form support is not enabled"):e.loadingParams.enableXfa?console.warn("Warning: XFA Foreground documents are not supported"):console.warn("Warning: XFA support is not enabled"),t.IsSignaturesPresent&&console.warn("Warning: Digital signatures validation is not supported"),this.eventBus.dispatch("metadataloaded",{source:this})},async _initializePageLabels(e){const t=await e.getPageLabels();if(e!==this.pdfDocument)return;if(!t||r.AppOptions.get("disablePageLabels"))return;const i=t.length;let n=0,s=0;for(let r=0;r<i;r++){const e=t[r];if(e===(r+1).toString())n++;else{if(""!==e)break;s++}}if(n>=i||s>=i)return;const{pdfViewer:o,pdfThumbnailViewer:a,toolbar:l}=this;o.setPageLabels(t),a?.setPageLabels(t),l?.setPagesCount(i,!0),l?.setPageNumber(o.currentPageNumber,o.currentPageLabel)},_initializePdfHistory(e){let{fingerprint:t,viewOnLoad:i,initialDest:n=null}=e;this.pdfHistory&&(this.pdfHistory.initialize({fingerprint:t,resetHistory:i===A.INITIAL,updateUrl:r.AppOptions.get("historyUpdateUrl")}),this.pdfHistory.initialBookmark&&(this.initialBookmark=this.pdfHistory.initialBookmark,this.initialRotation=this.pdfHistory.initialRotation),n&&!this.initialBookmark&&i===A.UNKNOWN&&(this.initialBookmark=JSON.stringify(n),this.pdfHistory.push({explicitDest:n,pageNumber:null})))},_initializeAnnotationStorageCallbacks(e){if(e!==this.pdfDocument)return;const{annotationStorage:t}=e;t.onSetModified=()=>{window.addEventListener("beforeunload",Oe),this._annotationStorageModified=!0},t.onResetModified=()=>{window.removeEventListener("beforeunload",Oe),delete this._annotationStorageModified},t.onAnnotationEditor=e=>{this._hasAnnotationEditors=!!e,this.setTitle(),e&&this.externalServices.reportTelemetry({type:"editing",data:{type:e}})}},setInitialView(e){let{rotation:t,sidebarView:i,scrollMode:s,spreadMode:r}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const o=e=>{(0,n.isValidRotation)(e)&&(this.pdfViewer.pagesRotation=e)},a=(e,t)=>{(0,n.isValidScrollMode)(e)&&(this.pdfViewer.scrollMode=e),(0,n.isValidSpreadMode)(t)&&(this.pdfViewer.spreadMode=t)};this.isInitialViewSet=!0,this.pdfSidebar?.setInitialView(i),a(s,r),this.initialBookmark?(o(this.initialRotation),delete this.initialRotation,this.pdfLinkService.setHash(this.initialBookmark),this.initialBookmark=null):e&&(o(t),this.pdfLinkService.setHash(e)),this.toolbar?.setPageNumber(this.pdfViewer.currentPageNumber,this.pdfViewer.currentPageLabel),this.secondaryToolbar?.setPageNumber(this.pdfViewer.currentPageNumber),this.pdfViewer.currentScaleValue||(this.pdfViewer.currentScaleValue=n.DEFAULT_SCALE_VALUE)},_cleanup(){this.pdfDocument&&(this.pdfViewer.cleanup(),this.pdfThumbnailViewer?.cleanup(),this.pdfDocument.cleanup())},forceRendering(){this.pdfRenderingQueue.printing=!!this.printService,this.pdfRenderingQueue.isThumbnailViewEnabled=this.pdfSidebar?.visibleView===n.SidebarView.THUMBS,this.pdfRenderingQueue.renderHighestPriority()},beforePrint(){if(this._printAnnotationStoragePromise=this.pdfScriptingManager.dispatchWillPrint().catch((()=>{})).then((()=>this.pdfDocument?.annotationStorage.print)),this.printService)return;if(!this.supportsPrinting)return void this.l10n.get("printing_not_supported").then((e=>{this._otherError(e)}));if(!this.pdfViewer.pageViewsReady)return void this.l10n.get("printing_not_ready").then((e=>{window.alert(e)}));const e=this.pdfViewer.getPagesOverview(),t=this.appConfig.printContainer,i=r.AppOptions.get("printResolution"),n=this.pdfViewer.optionalContentConfigPromise,s=Ve.instance.createPrintService(this.pdfDocument,e,t,i,n,this._printAnnotationStoragePromise,this.l10n);this.printService=s,this.forceRendering(),this.setTitle(),s.layout(),this._hasAnnotationEditors&&this.externalServices.reportTelemetry({type:"editing",data:{type:"print"}})},afterPrint(){this._printAnnotationStoragePromise&&(this._printAnnotationStoragePromise.then((()=>{this.pdfScriptingManager.dispatchDidPrint()})),this._printAnnotationStoragePromise=null),this.printService&&(this.printService.destroy(),this.printService=null,this.pdfDocument?.annotationStorage.resetModified()),this.forceRendering(),this.setTitle()},rotatePages(e){this.pdfViewer.pagesRotation+=e},requestPresentationMode(){this.pdfPresentationMode?.request()},triggerPrinting(){this.supportsPrinting&&window.print()},bindEvents(){const{eventBus:e,_boundEvents:t}=this;t.beforePrint=this.beforePrint.bind(this),t.afterPrint=this.afterPrint.bind(this),e._on("resize",K),e._on("hashchange",Z),e._on("beforeprint",t.beforePrint),e._on("afterprint",t.afterPrint),e._on("pagerender",F),e._on("pagerendered",U),e._on("updateviewarea",$),e._on("pagechanging",Se),e._on("scalechanging",Pe),e._on("rotationchanging",Ee),e._on("sidebarviewchanged",j),e._on("pagemode",z),e._on("namedaction",H),e._on("presentationmodechanged",W),e._on("presentationmode",Y),e._on("switchannotationeditormode",J),e._on("switchannotationeditorparams",ee),e._on("print",te),e._on("download",ie),e._on("openinexternalapp",ne),e._on("firstpage",se),e._on("lastpage",re),e._on("nextpage",oe),e._on("previouspage",ae),e._on("zoomin",le),e._on("zoomout",de),e._on("zoomreset",he),e._on("pagenumberchanged",ce),e._on("scalechanged",ue),e._on("rotatecw",pe),e._on("rotateccw",ge),e._on("optionalcontentconfig",fe),e._on("switchscrollmode",me),e._on("scrollmodechanged",G),e._on("switchspreadmode",ve),e._on("spreadmodechanged",X),e._on("documentproperties",we),e._on("findfromurlhash",be),e._on("updatefindmatchescount",_e),e._on("updatefindcontrolstate",ye),r.AppOptions.get("pdfBug")&&(t.reportPageStatsPDFBug=R,e._on("pagerendered",t.reportPageStatsPDFBug),e._on("pagechanging",t.reportPageStatsPDFBug)),e._on("fileinputchange",Q),e._on("openfile",q)},bindWindowEvents(){const{eventBus:e,_boundEvents:t}=this;function i(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;e&&Ce(e);const n=window.matchMedia(`(resolution: ${window.devicePixelRatio||1}dppx)`);n.addEventListener("change",i,{once:!0}),t.removeWindowResolutionChange||=function(){n.removeEventListener("change",i),t.removeWindowResolutionChange=null}}i(),t.windowResize=()=>{e.dispatch("resize",{source:window})},t.windowHashChange=()=>{e.dispatch("hashchange",{source:window,hash:document.location.hash.substring(1)})},t.windowBeforePrint=()=>{e.dispatch("beforeprint",{source:window})},t.windowAfterPrint=()=>{e.dispatch("afterprint",{source:window})},t.windowUpdateFromSandbox=t=>{e.dispatch("updatefromsandbox",{source:window,detail:t.detail})},window.addEventListener("visibilitychange",Le),window.addEventListener("wheel",Ie,{passive:!1}),window.addEventListener("touchstart",xe,{passive:!1}),window.addEventListener("touchmove",Ae,{passive:!1}),window.addEventListener("touchend",De,{passive:!1}),window.addEventListener("click",Ne),window.addEventListener("keydown",Be),window.addEventListener("keyup",ke),window.addEventListener("resize",t.windowResize),window.addEventListener("hashchange",t.windowHashChange),window.addEventListener("beforeprint",t.windowBeforePrint),window.addEventListener("afterprint",t.windowAfterPrint),window.addEventListener("updatefromsandbox",t.windowUpdateFromSandbox)},unbindEvents(){const{eventBus:e,_boundEvents:t}=this;e._off("resize",K),e._off("hashchange",Z),e._off("beforeprint",t.beforePrint),e._off("afterprint",t.afterPrint),e._off("pagerender",F),e._off("pagerendered",U),e._off("updateviewarea",$),e._off("pagechanging",Se),e._off("scalechanging",Pe),e._off("rotationchanging",Ee),e._off("sidebarviewchanged",j),e._off("pagemode",z),e._off("namedaction",H),e._off("presentationmodechanged",W),e._off("presentationmode",Y),e._off("print",te),e._off("download",ie),e._off("openinexternalapp",ne),e._off("firstpage",se),e._off("lastpage",re),e._off("nextpage",oe),e._off("previouspage",ae),e._off("zoomin",le),e._off("zoomout",de),e._off("zoomreset",he),e._off("pagenumberchanged",ce),e._off("scalechanged",ue),e._off("rotatecw",pe),e._off("rotateccw",ge),e._off("optionalcontentconfig",fe),e._off("switchscrollmode",me),e._off("scrollmodechanged",G),e._off("switchspreadmode",ve),e._off("spreadmodechanged",X),e._off("documentproperties",we),e._off("findfromurlhash",be),e._off("updatefindmatchescount",_e),e._off("updatefindcontrolstate",ye),t.reportPageStatsPDFBug&&(e._off("pagerendered",t.reportPageStatsPDFBug),e._off("pagechanging",t.reportPageStatsPDFBug),t.reportPageStatsPDFBug=null),e._off("fileinputchange",Q),e._off("openfile",q),t.beforePrint=null,t.afterPrint=null},unbindWindowEvents(){const{_boundEvents:e}=this;window.removeEventListener("visibilitychange",Le),window.removeEventListener("wheel",Ie,{passive:!1}),window.removeEventListener("touchstart",xe,{passive:!1}),window.removeEventListener("touchmove",Ae,{passive:!1}),window.removeEventListener("touchend",De,{passive:!1}),window.removeEventListener("click",Ne),window.removeEventListener("keydown",Be),window.removeEventListener("keyup",ke),window.removeEventListener("resize",e.windowResize),window.removeEventListener("hashchange",e.windowHashChange),window.removeEventListener("beforeprint",e.windowBeforePrint),window.removeEventListener("afterprint",e.windowAfterPrint),window.removeEventListener("updatefromsandbox",e.windowUpdateFromSandbox),e.removeWindowResolutionChange?.(),e.windowResize=null,e.windowHashChange=null,e.windowBeforePrint=null,e.windowAfterPrint=null,e.windowUpdateFromSandbox=null},_accumulateTicks(e,t){(this[t]>0&&e<0||this[t]<0&&e>0)&&(this[t]=0),this[t]+=e;const i=Math.trunc(this[t]);return this[t]-=i,i},_accumulateFactor(e,t,i){if(1===t)return 1;(this[i]>1&&t<1||this[i]<1&&t>1)&&(this[i]=1);const n=Math.floor(e*t*this[i]*100)/(100*e);return this[i]=t/n,n},_centerAtPos(e,t,i){const{pdfViewer:n}=this,s=n.currentScale/e-1;if(0!==s){const[e,r]=n.containerTopLeft;n.container.scrollLeft+=(t-r)*s,n.container.scrollTop+=(i-e)*s}},_unblockDocumentLoadEvent(){document.blockUnblockOnload?.(!1),this._unblockDocumentLoadEvent=()=>{}},get scriptingReady(){return this.pdfScriptingManager.ready}};t.PDFViewerApplication=k;{const e=["null","http://mozilla.github.io","https://mozilla.github.io"];var B=function(t){if(t)try{const i=new URL(window.location.href).origin||"null";if(e.includes(i))return;const n=new URL(t,window.location.href).origin;if(n!==i)throw new Error("file origin does not match viewer's")}catch(i){throw k.l10n.get("loading_error").then((e=>{k._documentError(e,{message:i?.message})})),i}}}async function O(){s.GlobalWorkerOptions.workerSrc||=r.AppOptions.get("workerSrc"),await(0,s.loadScript)(s.PDFWorker.workerSrc)}async function V(e){const{debuggerScriptPath:t}=e.appConfig,{PDFBug:i}=await import(t);e._PDFBug=i}function R(e){let{pageNumber:t}=e;if(!globalThis.Stats?.enabled)return;const i=k.pdfViewer.getPageView(t-1);globalThis.Stats.add(t,i?.pdfPage?.stats)}function F(e){let{pageNumber:t}=e;t===k.page&&k.toolbar?.updateLoadingIndicatorState(!0)}function U(e){let{pageNumber:t,error:i}=e;if(t===k.page&&k.toolbar?.updateLoadingIndicatorState(!1),k.pdfSidebar?.visibleView===n.SidebarView.THUMBS){const e=k.pdfViewer.getPageView(t-1),i=k.pdfThumbnailViewer?.getThumbnail(t-1);e&&i?.setImage(e)}i&&k.l10n.get("rendering_error").then((e=>{k._otherError(e,i)}))}function z(e){let t,{mode:i}=e;switch(i){case"thumbs":t=n.SidebarView.THUMBS;break;case"bookmarks":case"outline":t=n.SidebarView.OUTLINE;break;case"attachments":t=n.SidebarView.ATTACHMENTS;break;case"layers":t=n.SidebarView.LAYERS;break;case"none":t=n.SidebarView.NONE;break;default:return void console.error('Invalid "pagemode" hash parameter: '+i)}k.pdfSidebar?.switchView(t,!0)}function H(e){switch(e.action){case"GoToPage":k.appConfig.toolbar?.pageNumber.select();break;case"Find":k.supportsIntegratedFind||k?.findBar.toggle();break;case"Print":k.triggerPrinting();break;case"SaveAs":k.downloadOrSave();break}}function W(e){k.pdfViewer.presentationModeState=e.state}function j(e){let{view:t}=e;k.pdfRenderingQueue.isThumbnailViewEnabled=t===n.SidebarView.THUMBS,k.isInitialViewSet&&k.store?.set("sidebarView",t).catch((()=>{}))}function $(e){let{location:t}=e;if(k.isInitialViewSet&&k.store?.setMultiple({page:t.pageNumber,zoom:t.scale,scrollLeft:t.left,scrollTop:t.top,rotation:t.rotation}).catch((()=>{})),k.appConfig.secondaryToolbar){const e=k.pdfLinkService.getAnchorUrl(t.pdfOpenParams);k.appConfig.secondaryToolbar.viewBookmarkButton.href=e}}function G(e){k.isInitialViewSet&&!k.pdfViewer.isInPresentationMode&&k.store?.set("scrollMode",e.mode).catch((()=>{}))}function X(e){k.isInitialViewSet&&!k.pdfViewer.isInPresentationMode&&k.store?.set("spreadMode",e.mode).catch((()=>{}))}function K(){const{pdfDocument:e,pdfViewer:t,pdfRenderingQueue:i}=k;if(i.printing&&window.matchMedia("print").matches)return;if(!e)return;const n=t.currentScaleValue;"auto"!==n&&"page-fit"!==n&&"page-width"!==n||(t.currentScaleValue=n),t.update()}function Z(e){const t=e.hash;t&&(k.isInitialViewSet?k.pdfHistory?.popStateInProgress||k.pdfLinkService.setHash(t):k.initialBookmark=t)}var Q=function(e){if(k.pdfViewer?.isInPresentationMode)return;const t=e.fileInput.files[0];k.open({url:URL.createObjectURL(t),originalUrl:t.name})},q=function(e){const t=k.appConfig.openFileInput;t.click()};function Y(){k.requestPresentationMode()}function J(e){k.pdfViewer.annotationEditorMode=e}function ee(e){k.pdfViewer.annotationEditorParams=e}function te(){k.triggerPrinting()}function ie(){k.downloadOrSave()}function ne(){k.openInExternalApp()}function se(){k.page=1}function re(){k.page=k.pagesCount}function oe(){k.pdfViewer.nextPage()}function ae(){k.pdfViewer.previousPage()}function le(){k.zoomIn()}function de(){k.zoomOut()}function he(){k.zoomReset()}function ce(e){const t=k.pdfViewer;""!==e.value&&k.pdfLinkService.goToPage(e.value),e.value!==t.currentPageNumber.toString()&&e.value!==t.currentPageLabel&&k.toolbar?.setPageNumber(t.currentPageNumber,t.currentPageLabel)}function ue(e){k.pdfViewer.currentScaleValue=e.value}function pe(){k.rotatePages(90)}function ge(){k.rotatePages(-90)}function fe(e){k.pdfViewer.optionalContentConfigPromise=e.promise}function me(e){k.pdfViewer.scrollMode=e.mode}function ve(e){k.pdfViewer.spreadMode=e.mode}function we(){k.pdfDocumentProperties?.open()}function be(e){k.eventBus.dispatch("find",{source:e.source,type:"",query:e.query,caseSensitive:!1,entireWord:!1,highlightAll:!0,findPrevious:!1,matchDiacritics:!0})}function _e(e){let{matchesCount:t}=e;k.supportsIntegratedFind?k.externalServices.updateFindMatchesCount(t):k.findBar.updateResultsCount(t)}function ye(e){let{state:t,previous:i,matchesCount:n,rawQuery:s}=e;k.supportsIntegratedFind?k.externalServices.updateFindControlState({result:t,findPrevious:i,matchesCount:n,rawQuery:s}):k.findBar?.updateUIState(t,i,n)}function Pe(e){k.toolbar?.setPageScale(e.presetValue,e.scale),k.pdfViewer.update()}function Ee(e){k.pdfThumbnailViewer&&(k.pdfThumbnailViewer.pagesRotation=e.pagesRotation),k.forceRendering(),k.pdfViewer.currentPageNumber=e.pageNumber}function Se(e){let{pageNumber:t,pageLabel:i}=e;k.toolbar?.setPageNumber(t,i),k.secondaryToolbar?.setPageNumber(t),k.pdfSidebar?.visibleView===n.SidebarView.THUMBS&&k.pdfThumbnailViewer?.scrollThumbnailIntoView(t);const s=k.pdfViewer.getPageView(t-1);k.toolbar?.updateLoadingIndicatorState(s?.renderingState===n.RenderingStates.RUNNING)}function Ce(e){k.pdfViewer.refresh()}function Le(e){"visible"===document.visibilityState&&Me()}let Te=null;function Me(){Te&&clearTimeout(Te),Te=setTimeout((function(){Te=null}),x)}function Ie(e){const{pdfViewer:t,supportedMouseWheelZoomModifierKeys:i,supportsPinchToZoom:s}=k;if(t.isInPresentationMode)return;const r=e.deltaMode;let o=Math.exp(-e.deltaY/100);const a=!1,l=e.ctrlKey&&!k._isCtrlKeyDown&&r===WheelEvent.DOM_DELTA_PIXEL&&0===e.deltaX&&(Math.abs(o-1)<.05||a)&&0===e.deltaZ;if(l||e.ctrlKey&&i.ctrlKey||e.metaKey&&i.metaKey){if(e.preventDefault(),Te||"hidden"===document.visibilityState||k.overlayManager.active)return;const i=t.currentScale;if(l&&s)if(o=k._accumulateFactor(i,o,"_wheelUnusedFactor"),o<1)k.zoomOut(null,o);else{if(!(o>1))return;k.zoomIn(null,o)}else{const t=(0,n.normalizeWheelEventDirection)(e);let i=0;if(r===WheelEvent.DOM_DELTA_LINE||r===WheelEvent.DOM_DELTA_PAGE)i=Math.abs(t)>=1?Math.sign(t):k._accumulateTicks(t,"_wheelUnusedTicks");else{const e=30;i=k._accumulateTicks(t/e,"_wheelUnusedTicks")}if(i<0)k.zoomOut(-i);else{if(!(i>0))return;k.zoomIn(i)}}k._centerAtPos(i,e.clientX,e.clientY)}else Me()}function xe(e){if(k.pdfViewer.isInPresentationMode||e.touches.length<2)return;if(e.preventDefault(),2!==e.touches.length||k.overlayManager.active)return void(k._touchInfo=null);let[t,i]=e.touches;t.identifier>i.identifier&&([t,i]=[i,t]),k._touchInfo={touch0X:t.pageX,touch0Y:t.pageY,touch1X:i.pageX,touch1Y:i.pageY}}function Ae(e){if(!k._touchInfo||2!==e.touches.length)return;const{pdfViewer:t,_touchInfo:i,supportsPinchToZoom:n}=k;let[s,r]=e.touches;s.identifier>r.identifier&&([s,r]=[r,s]);const{pageX:o,pageY:a}=s,{pageX:l,pageY:d}=r,{touch0X:h,touch0Y:c,touch1X:u,touch1Y:p}=i;if(Math.abs(h-o)<=1&&Math.abs(c-a)<=1&&Math.abs(u-l)<=1&&Math.abs(p-d)<=1)return;if(i.touch0X=o,i.touch0Y=a,i.touch1X=l,i.touch1Y=d,h===o&&c===a){const e=u-o,t=p-a,i=l-o,n=d-a,s=e*n-t*i;if(Math.abs(s)>.02*Math.hypot(e,t)*Math.hypot(i,n))return}else if(u===l&&p===d){const e=h-l,t=c-d,i=o-l,n=a-d,s=e*n-t*i;if(Math.abs(s)>.02*Math.hypot(e,t)*Math.hypot(i,n))return}else{const e=o-h,t=l-u,i=a-c,n=d-p,s=e*t+i*n;if(s>=0)return}e.preventDefault();const g=Math.hypot(o-l,a-d)||1,f=Math.hypot(h-u,c-p)||1,m=t.currentScale;if(n){const e=k._accumulateFactor(m,g/f,"_touchUnusedFactor");if(e<1)k.zoomOut(null,e);else{if(!(e>1))return;k.zoomIn(null,e)}}else{const e=30,t=k._accumulateTicks((g-f)/e,"_touchUnusedTicks");if(t<0)k.zoomOut(-t);else{if(!(t>0))return;k.zoomIn(t)}}k._centerAtPos(m,(o+l)/2,(a+d)/2)}function De(e){k._touchInfo&&(e.preventDefault(),k._touchInfo=null,k._touchUnusedTicks=0,k._touchUnusedFactor=1)}function Ne(e){if(!k.secondaryToolbar?.isOpen)return;const t=k.appConfig;(k.pdfViewer.containsElement(e.target)||t.toolbar?.container.contains(e.target)&&e.target!==t.secondaryToolbar?.toggleButton)&&k.secondaryToolbar.close()}function ke(e){"Control"===e.key&&(k._isCtrlKeyDown=!1)}function Be(e){if(k._isCtrlKeyDown="Control"===e.key,k.overlayManager.active)return;const{eventBus:t,pdfViewer:i}=k,s=i.isInPresentationMode;let r=!1,o=!1;const a=(e.ctrlKey?1:0)|(e.altKey?2:0)|(e.shiftKey?4:0)|(e.metaKey?8:0);if(1===a||8===a||5===a||12===a)switch(e.keyCode){case 70:k.supportsIntegratedFind||e.shiftKey||(k.findBar?.open(),r=!0);break;case 71:if(!k.supportsIntegratedFind){const{state:e}=k.findController;if(e){const i={source:window,type:"again",findPrevious:5===a||12===a};t.dispatch("find",{...e,...i})}r=!0}break;case 61:case 107:case 187:case 171:k.zoomIn(),r=!0;break;case 173:case 109:case 189:k.zoomOut(),r=!0;break;case 48:case 96:s||(setTimeout((function(){k.zoomReset()})),r=!1);break;case 38:(s||k.page>1)&&(k.page=1,r=!0,o=!0);break;case 40:(s||k.page<k.pagesCount)&&(k.page=k.pagesCount,r=!0,o=!0);break}if(1===a||8===a)switch(e.keyCode){case 83:t.dispatch("download",{source:window}),r=!0;break;case 79:t.dispatch("openfile",{source:window}),r=!0;break}if(3===a||10===a)switch(e.keyCode){case 80:k.requestPresentationMode(),r=!0,k.externalServices.reportTelemetry({type:"buttons",data:{id:"presentationModeKeyboard"}});break;case 71:k.appConfig.toolbar&&(k.appConfig.toolbar.pageNumber.select(),r=!0);break}if(r)return o&&!s&&i.focus(),void e.preventDefault();const l=(0,n.getActiveOrFocusedElement)(),d=l?.tagName.toUpperCase();if("INPUT"!==d&&"TEXTAREA"!==d&&"SELECT"!==d&&!l?.isContentEditable||27===e.keyCode){if(0===a){let t=0,a=!1;switch(e.keyCode){case 38:case 33:i.isVerticalScrollbarEnabled&&(a=!0),t=-1;break;case 8:s||(a=!0),t=-1;break;case 37:i.isHorizontalScrollbarEnabled&&(a=!0);case 75:case 80:t=-1;break;case 27:k.secondaryToolbar?.isOpen&&(k.secondaryToolbar.close(),r=!0),!k.supportsIntegratedFind&&k.findBar?.opened&&(k.findBar.close(),r=!0);break;case 40:case 34:i.isVerticalScrollbarEnabled&&(a=!0),t=1;break;case 13:case 32:s||(a=!0),t=1;break;case 39:i.isHorizontalScrollbarEnabled&&(a=!0);case 74:case 78:t=1;break;case 36:(s||k.page>1)&&(k.page=1,r=!0,o=!0);break;case 35:(s||k.page<k.pagesCount)&&(k.page=k.pagesCount,r=!0,o=!0);break;case 83:k.pdfCursorTools?.switchTool(n.CursorTool.SELECT);break;case 72:k.pdfCursorTools?.switchTool(n.CursorTool.HAND);break;case 82:k.rotatePages(90);break;case 115:k.pdfSidebar?.toggle();break}0===t||a&&"page-fit"!==i.currentScaleValue||(t>0?i.nextPage():i.previousPage(),r=!0)}if(4===a)switch(e.keyCode){case 13:case 32:if(!s&&"page-fit"!==i.currentScaleValue)break;i.previousPage(),r=!0;break;case 82:k.rotatePages(-90);break}r||s||(e.keyCode>=33&&e.keyCode<=40||32===e.keyCode&&"BUTTON"!==d)&&(o=!0),o&&!i.containsElement(l)&&i.focus(),r&&e.preventDefault()}}function Oe(e){return e.preventDefault(),e.returnValue="",!1}const Ve={instance:{supportsPrinting:!1,createPrintService(){throw new Error("Not implemented: createPrintService")}}};t.PDFPrintServiceFactory=Ve},(e,t,i)=>{var n=i(3),s=i(4),r=i(77),o=i(78),a="WebAssembly",l=s[a],d=7!==Error("e",{cause:7}).cause,h=function(e,t){var i={};i[e]=o(e,t,d),n({global:!0,constructor:!0,arity:1,forced:d},i)},c=function(e,t){if(l&&l[e]){var i={};i[e]=o(a+"."+e,t,d),n({target:a,stat:!0,constructor:!0,arity:1,forced:d},i)}};h("Error",(function(e){return function(t){return r(e,this,arguments)}})),h("EvalError",(function(e){return function(t){return r(e,this,arguments)}})),h("RangeError",(function(e){return function(t){return r(e,this,arguments)}})),h("ReferenceError",(function(e){return function(t){return r(e,this,arguments)}})),h("SyntaxError",(function(e){return function(t){return r(e,this,arguments)}})),h("TypeError",(function(e){return function(t){return r(e,this,arguments)}})),h("URIError",(function(e){return function(t){return r(e,this,arguments)}})),c("CompileError",(function(e){return function(t){return r(e,this,arguments)}})),c("LinkError",(function(e){return function(t){return r(e,this,arguments)}})),c("RuntimeError",(function(e){return function(t){return r(e,this,arguments)}}))},(e,t,i)=>{var n=i(9),s=Function.prototype,r=s.apply,o=s.call;e.exports="object"==typeof Reflect&&Reflect.apply||(n?o.bind(r):function(){return o.apply(r,arguments)})},(e,t,i)=>{var n=i(24),s=i(39),r=i(44),o=i(25),a=i(79),l=i(56),d=i(82),h=i(83),c=i(84),u=i(85),p=i(86),g=i(6),f=i(36);e.exports=function(e,t,i,m){var v="stackTraceLimit",w=m?2:1,b=e.split("."),_=b[b.length-1],y=n.apply(null,b);if(y){var P=y.prototype;if(!f&&s(P,"cause")&&delete P.cause,!i)return y;var E=n("Error"),S=t((function(e,t){var i=c(m?t:e,void 0),n=m?new y(e):new y;return void 0!==i&&r(n,"message",i),p(n,S,n.stack,2),this&&o(P,this)&&h(n,this,S),arguments.length>w&&u(n,arguments[w]),n}));if(S.prototype=P,"Error"!==_?a?a(S,E):l(S,E,{name:!0}):g&&v in y&&(d(S,y,v),d(S,y,"prepareStackTrace")),l(S,y),!f)try{P.name!==_&&r(P,"name",_),P.constructor=S}catch(C){}return S}}},(e,t,i)=>{var n=i(80),s=i(47),r=i(81);e.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var e,t=!1,i={};try{e=n(Object.prototype,"__proto__","set"),e(i,[]),t=i instanceof Array}catch(o){}return function(i,n){return s(i),r(n),t?e(i,n):i.__proto__=n,i}}():void 0)},(e,t,i)=>{var n=i(14),s=i(31);e.exports=function(e,t,i){try{return n(s(Object.getOwnPropertyDescriptor(e,t)[i]))}catch(r){}}},(e,t,i)=>{var n=i(21),s=String,r=TypeError;e.exports=function(e){if("object"==typeof e||n(e))return e;throw r("Can't set "+s(e)+" as a prototype")}},(e,t,i)=>{var n=i(45).f;e.exports=function(e,t,i){i in e||n(e,i,{configurable:!0,get:function(){return t[i]},set:function(e){t[i]=e}})}},(e,t,i)=>{var n=i(21),s=i(20),r=i(79);e.exports=function(e,t,i){var o,a;return r&&n(o=t.constructor)&&o!==i&&s(a=o.prototype)&&a!==i.prototype&&r(e,a),e}},(e,t,i)=>{var n=i(70);e.exports=function(e,t){return void 0===e?arguments.length<2?"":t:n(e)}},(e,t,i)=>{var n=i(20),s=i(44);e.exports=function(e,t){n(t)&&"cause"in t&&s(e,"cause",t.cause)}},(e,t,i)=>{var n=i(44),s=i(87),r=i(88),o=Error.captureStackTrace;e.exports=function(e,t,i,a){r&&(o?o(e,t):n(e,"stack",s(i,a)))}},(e,t,i)=>{var n=i(14),s=Error,r=n("".replace),o=function(e){return String(s(e).stack)}("zxcasd"),a=/\n\s*at [^:]*:[^\n]*/,l=a.test(o);e.exports=function(e,t){if(l&&"string"==typeof e&&!s.prepareStackTrace)while(t--)e=r(e,a,"");return e}},(e,t,i)=>{var n=i(7),s=i(11);e.exports=!n((function(){var e=Error("a");return!("stack"in e)||(Object.defineProperty(e,"stack",s(1,7)),7!==e.stack)}))},(e,t,i)=>{var n=i(3),s=i(40),r=i(64),o=i(90),a=i(91),l=i(7),d=l((function(){return 4294967297!==[].push.call({length:4294967296},1)})),h=function(){try{Object.defineProperty([],"length",{writable:!1}).push()}catch(e){return e instanceof TypeError}},c=d||!h();n({target:"Array",proto:!0,arity:1,forced:c},{push:function(e){var t=s(this),i=r(t),n=arguments.length;a(i+n);for(var l=0;l<n;l++)t[i]=arguments[l],i++;return o(t,i),i}})},(e,t,i)=>{var n=i(6),s=i(69),r=TypeError,o=Object.getOwnPropertyDescriptor,a=n&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(e){return e instanceof TypeError}}();e.exports=a?function(e,t){if(s(e)&&!o(e,"length").writable)throw r("Cannot set read only .length");return e.length=t}:function(e,t){return e.length=t}},e=>{var t=TypeError,i=9007199254740991;e.exports=function(e){if(e>i)throw t("Maximum allowed index exceeded");return e}},(e,t,i)=>{var n=i(48),s=i(14),r=i(70),o=i(93),a=URLSearchParams,l=a.prototype,d=s(l.append),h=s(l["delete"]),c=s(l.forEach),u=s([].push),p=new a("a=1&a=2&b=3");p["delete"]("a",1),p["delete"]("b",void 0),p+""!=="a=2"&&n(l,"delete",(function(e){var t=arguments.length,i=t<2?void 0:arguments[1];if(t&&void 0===i)return h(this,e);var n=[];c(this,(function(e,t){u(n,{key:t,value:e})})),o(t,1);var s,a=r(e),l=r(i),p=0,g=0,f=!1,m=n.length;while(p<m)s=n[p++],f||s.key===a?(f=!0,h(this,s.key)):g++;while(g<m)s=n[g++],s.key===a&&s.value===l||d(this,s.key,s.value)}),{enumerable:!0,unsafe:!0})},e=>{var t=TypeError;e.exports=function(e,i){if(e<i)throw t("Not enough arguments");return e}},(e,t,i)=>{var n=i(48),s=i(14),r=i(70),o=i(93),a=URLSearchParams,l=a.prototype,d=s(l.getAll),h=s(l.has),c=new a("a=1");!c.has("a",2)&&c.has("a",void 0)||n(l,"has",(function(e){var t=arguments.length,i=t<2?void 0:arguments[1];if(t&&void 0===i)return h(this,e);var n=d(this,e);o(t,1);var s=r(i),a=0;while(a<n.length)if(n[a++]===s)return!0;return!1}),{enumerable:!0,unsafe:!0})},(e,t,i)=>{var n=i(6),s=i(14),r=i(96),o=URLSearchParams.prototype,a=s(o.forEach);n&&!("size"in o)&&r(o,"size",{get:function(){var e=0;return a(this,(function(){e++})),e},configurable:!0,enumerable:!0})},(e,t,i)=>{var n=i(49),s=i(45);e.exports=function(e,t,i){return i.get&&n(i.get,t,{getter:!0}),i.set&&n(i.set,t,{setter:!0}),s.f(e,t,i)}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.animationStarted=t.VERTICAL_PADDING=t.UNKNOWN_SCALE=t.TextLayerMode=t.SpreadMode=t.SidebarView=t.ScrollMode=t.SCROLLBAR_PADDING=t.RenderingStates=t.ProgressBar=t.PresentationModeState=t.OutputScale=t.MIN_SCALE=t.MAX_SCALE=t.MAX_AUTO_SCALE=t.DEFAULT_SCALE_VALUE=t.DEFAULT_SCALE_DELTA=t.DEFAULT_SCALE=t.CursorTool=t.AutoPrintRegExp=void 0,t.apiPageLayoutToViewerModes=W,t.apiPageModeToSidebarView=j,t.approximateFraction=T,t.backtrackBeforeAllVisibleElements=x,t.binarySearchFirstItem=L,t.docStyle=void 0,t.getActiveOrFocusedElement=H,t.getPageSizeInches=I,t.getVisibleElements=A,t.isPortraitOrientation=V,t.isValidRotation=k,t.isValidScrollMode=B,t.isValidSpreadMode=O,t.normalizeWheelEventDelta=N,t.normalizeWheelEventDirection=D,t.parseQueryString=E,t.removeNullCharacters=C,t.roundToDivide=M,t.scrollIntoView=y,t.toggleCheckedBtn=$,t.toggleExpandedBtn=G,t.watchScroll=P,i(92),i(94),i(95),i(98),i(109),i(111),i(114),i(116),i(118),i(120),i(89);const n="auto";t.DEFAULT_SCALE_VALUE=n;const s=1;t.DEFAULT_SCALE=s;const r=1.1;t.DEFAULT_SCALE_DELTA=r;const o=.1;t.MIN_SCALE=o;const a=10;t.MAX_SCALE=a;const l=0;t.UNKNOWN_SCALE=l;const d=1.25;t.MAX_AUTO_SCALE=d;const h=40;t.SCROLLBAR_PADDING=h;const c=5;t.VERTICAL_PADDING=c;const u={INITIAL:0,RUNNING:1,PAUSED:2,FINISHED:3};t.RenderingStates=u;const p={UNKNOWN:0,NORMAL:1,CHANGING:2,FULLSCREEN:3};t.PresentationModeState=p;const g={UNKNOWN:-1,NONE:0,THUMBS:1,OUTLINE:2,ATTACHMENTS:3,LAYERS:4};t.SidebarView=g;const f={DISABLE:0,ENABLE:1,ENABLE_PERMISSIONS:2};t.TextLayerMode=f;const m={UNKNOWN:-1,VERTICAL:0,HORIZONTAL:1,WRAPPED:2,PAGE:3};t.ScrollMode=m;const v={UNKNOWN:-1,NONE:0,ODD:1,EVEN:2};t.SpreadMode=v;const w={SELECT:0,HAND:1,ZOOM:2};t.CursorTool=w;const b=/\bprint\s*\(/;t.AutoPrintRegExp=b;class _{constructor(){const e=window.devicePixelRatio||1;this.sx=e,this.sy=e}get scaled(){return 1!==this.sx||1!==this.sy}}function y(e,t){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2],n=e.offsetParent;if(!n)return void console.error("offsetParent is not set -- cannot scroll");let s=e.offsetTop+e.clientTop,r=e.offsetLeft+e.clientLeft;while(n.clientHeight===n.scrollHeight&&n.clientWidth===n.scrollWidth||i&&(n.classList.contains("markedContent")||"hidden"===getComputedStyle(n).overflow))if(s+=n.offsetTop,r+=n.offsetLeft,n=n.offsetParent,!n)return;t&&(void 0!==t.top&&(s+=t.top),void 0!==t.left&&(r+=t.left,n.scrollLeft=r)),n.scrollTop=s}function P(e,t){const i=function(i){s||(s=window.requestAnimationFrame((function(){s=null;const i=e.scrollLeft,r=n.lastX;i!==r&&(n.right=i>r),n.lastX=i;const o=e.scrollTop,a=n.lastY;o!==a&&(n.down=o>a),n.lastY=o,t(n)})))},n={right:!0,down:!0,lastX:e.scrollLeft,lastY:e.scrollTop,_eventHandler:i};let s=null;return e.addEventListener("scroll",i,!0),n}function E(e){const t=new Map;for(const[i,n]of new URLSearchParams(e))t.set(i.toLowerCase(),n);return t}t.OutputScale=_;const S=/[\x01-\x1F]/g;function C(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return"string"!==typeof e?(console.error("The argument must be a string."),e):(t&&(e=e.replaceAll(S," ")),e.replaceAll("\0",""))}function L(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=i,s=e.length-1;if(s<0||!t(e[s]))return e.length;if(t(e[n]))return n;while(n<s){const i=n+s>>1,r=e[i];t(r)?s=i:n=i+1}return n}function T(e){if(Math.floor(e)===e)return[e,1];const t=1/e,i=8;if(t>i)return[1,i];if(Math.floor(t)===t)return[1,t];const n=e>1?t:e;let s,r=0,o=1,a=1,l=1;while(1){const e=r+a,t=o+l;if(t>i)break;n<=e/t?(a=e,l=t):(r=e,o=t)}return s=n-r/o<a/l-n?n===e?[r,o]:[o,r]:n===e?[a,l]:[l,a],s}function M(e,t){const i=e%t;return 0===i?e:Math.round(e-i+t)}function I(e){let{view:t,userUnit:i,rotate:n}=e;const[s,r,o,a]=t,l=n%180!==0,d=(o-s)/72*i,h=(a-r)/72*i;return{width:l?h:d,height:l?d:h}}function x(e,t,i){if(e<2)return e;let n=t[e].div,s=n.offsetTop+n.clientTop;s>=i&&(n=t[e-1].div,s=n.offsetTop+n.clientTop);for(let r=e-2;r>=0;--r){if(n=t[r].div,n.offsetTop+n.clientTop+n.clientHeight<=s)break;e=r}return e}function A(e){let{scrollEl:t,views:i,sortByVisibility:n=!1,horizontal:s=!1,rtl:r=!1}=e;const o=t.scrollTop,a=o+t.clientHeight,l=t.scrollLeft,d=l+t.clientWidth;function h(e){const t=e.div,i=t.offsetTop+t.clientTop+t.clientHeight;return i>o}function c(e){const t=e.div,i=t.offsetLeft+t.clientLeft,n=i+t.clientWidth;return r?i<d:n>l}const u=[],p=new Set,g=i.length;let f=L(i,s?c:h);f>0&&f<g&&!s&&(f=x(f,i,o));let m=s?d:-1;for(let b=f;b<g;b++){const e=i[b],t=e.div,n=t.offsetLeft+t.clientLeft,r=t.offsetTop+t.clientTop,h=t.clientWidth,c=t.clientHeight,g=n+h,f=r+c;if(-1===m)f>=a&&(m=f);else if((s?n:r)>m)break;if(f<=o||r>=a||g<=l||n>=d)continue;const v=Math.max(0,o-r)+Math.max(0,f-a),w=Math.max(0,l-n)+Math.max(0,g-d),_=(c-v)/c,y=(h-w)/h,P=_*y*100|0;u.push({id:e.id,x:n,y:r,view:e,percent:P,widthPercent:100*y|0}),p.add(e.id)}const v=u[0],w=u.at(-1);return n&&u.sort((function(e,t){const i=e.percent-t.percent;return Math.abs(i)>.001?-i:e.id-t.id})),{first:v,last:w,views:u,ids:p}}function D(e){let t=Math.hypot(e.deltaX,e.deltaY);const i=Math.atan2(e.deltaY,e.deltaX);return-.25*Math.PI<i&&i<.75*Math.PI&&(t=-t),t}function N(e){const t=e.deltaMode;let i=D(e);const n=30,s=30;return t===WheelEvent.DOM_DELTA_PIXEL?i/=n*s:t===WheelEvent.DOM_DELTA_LINE&&(i/=s),i}function k(e){return Number.isInteger(e)&&e%90===0}function B(e){return Number.isInteger(e)&&Object.values(m).includes(e)&&e!==m.UNKNOWN}function O(e){return Number.isInteger(e)&&Object.values(v).includes(e)&&e!==v.UNKNOWN}function V(e){return e.width<=e.height}const R=new Promise((function(e){window.requestAnimationFrame(e)}));t.animationStarted=R;const F=document.documentElement.style;function U(e,t,i){return Math.min(Math.max(e,t),i)}t.docStyle=F;class z{#e=null;#t=null;#i=0;#n=null;#s=!0;constructor(e){this.#e=e.classList,this.#n=e.style}get percent(){return this.#i}set percent(e){this.#i=U(e,0,100),isNaN(e)?this.#e.add("indeterminate"):(this.#e.remove("indeterminate"),this.#n.setProperty("--progressBar-percent",`${this.#i}%`))}setWidth(e){if(!e)return;const t=e.parentNode,i=t.offsetWidth-e.offsetWidth;i>0&&this.#n.setProperty("--progressBar-end-offset",`${i}px`)}setDisableAutoFetch(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5e3;isNaN(this.#i)||(this.#t&&clearTimeout(this.#t),this.show(),this.#t=setTimeout((()=>{this.#t=null,this.hide()}),e))}hide(){this.#s&&(this.#s=!1,this.#e.add("hidden"))}show(){this.#s||(this.#s=!0,this.#e.remove("hidden"))}}function H(){let e=document,t=e.activeElement||e.querySelector(":focus");while(t?.shadowRoot)e=t.shadowRoot,t=e.activeElement||e.querySelector(":focus");return t}function W(e){let t=m.VERTICAL,i=v.NONE;switch(e){case"SinglePage":t=m.PAGE;break;case"OneColumn":break;case"TwoPageLeft":t=m.PAGE;case"TwoColumnLeft":i=v.ODD;break;case"TwoPageRight":t=m.PAGE;case"TwoColumnRight":i=v.EVEN;break}return{scrollMode:t,spreadMode:i}}function j(e){switch(e){case"UseNone":return g.NONE;case"UseThumbs":return g.THUMBS;case"UseOutlines":return g.OUTLINE;case"UseAttachments":return g.ATTACHMENTS;case"UseOC":return g.LAYERS}return g.NONE}function $(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;e.classList.toggle("toggled",t),e.setAttribute("aria-checked",t),i?.classList.toggle("hidden",!t)}function G(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;e.classList.toggle("toggled",t),e.setAttribute("aria-expanded",t),i?.classList.toggle("hidden",!t)}t.ProgressBar=z},(e,t,i)=>{var n=i(3),s=i(99),r=i(108);n({target:"Set",proto:!0,real:!0,forced:!r("difference")},{difference:s})},(e,t,i)=>{var n=i(100),s=i(101),r=i(102),o=i(105),a=i(106),l=i(103),d=i(104),h=s.has,c=s.remove;e.exports=function(e){var t=n(this),i=a(e),s=r(t);return o(t)<=i.size?l(t,(function(e){i.includes(e)&&c(s,e)})):d(i.getIterator(),(function(e){h(t,e)&&c(s,e)})),s}},(e,t,i)=>{var n=i(101).has;e.exports=function(e){return n(e),e}},(e,t,i)=>{var n=i(14),s=Set.prototype;e.exports={Set:Set,add:n(s.add),has:n(s.has),remove:n(s["delete"]),proto:s}},(e,t,i)=>{var n=i(101),s=i(103),r=n.Set,o=n.add;e.exports=function(e){var t=new r;return s(e,(function(e){o(t,e)})),t}},(e,t,i)=>{var n=i(14),s=i(104),r=i(101),o=r.Set,a=r.proto,l=n(a.forEach),d=n(a.keys),h=d(new o).next;e.exports=function(e,t,i){return i?s({iterator:d(e),next:h},t):l(e,t)}},(e,t,i)=>{var n=i(8);e.exports=function(e,t,i){var s,r,o=i?e:e.iterator,a=e.next;while(!(s=n(a,o)).done)if(r=t(s.value),void 0!==r)return r}},(e,t,i)=>{var n=i(80),s=i(101);e.exports=n(s.proto,"size","get")||function(e){return e.size}},(e,t,i)=>{var n=i(31),s=i(47),r=i(8),o=i(62),a=i(107),l="Invalid size",d=RangeError,h=TypeError,c=Math.max,u=function(e,t,i,n){this.set=e,this.size=t,this.has=i,this.keys=n};u.prototype={getIterator:function(){return a(s(r(this.keys,this.set)))},includes:function(e){return r(this.has,this.set,e)}},e.exports=function(e){s(e);var t=+e.size;if(t!==t)throw h(l);var i=o(t);if(i<0)throw d(l);return new u(e,c(i,0),n(e.has),n(e.keys))}},e=>{e.exports=function(e){return{iterator:e,next:e.next,done:!1}}},(e,t,i)=>{var n=i(24),s=function(e){return{size:e,has:function(){return!1},keys:function(){return{next:function(){return{done:!0}}}}}};e.exports=function(e){var t=n("Set");try{(new t)[e](s(0));try{return(new t)[e](s(-1)),!1}catch(i){return!0}}catch(r){return!1}}},(e,t,i)=>{var n=i(3),s=i(7),r=i(110),o=i(108),a=!o("intersection")||s((function(){return"3,2"!==Array.from(new Set([1,2,3]).intersection(new Set([3,2])))}));n({target:"Set",proto:!0,real:!0,forced:a},{intersection:r})},(e,t,i)=>{var n=i(100),s=i(101),r=i(105),o=i(106),a=i(103),l=i(104),d=s.Set,h=s.add,c=s.has;e.exports=function(e){var t=n(this),i=o(e),s=new d;return r(t)>i.size?l(i.getIterator(),(function(e){c(t,e)&&h(s,e)})):a(t,(function(e){i.includes(e)&&h(s,e)})),s}},(e,t,i)=>{var n=i(3),s=i(112),r=i(108);n({target:"Set",proto:!0,real:!0,forced:!r("isDisjointFrom")},{isDisjointFrom:s})},(e,t,i)=>{var n=i(100),s=i(101).has,r=i(105),o=i(106),a=i(103),l=i(104),d=i(113);e.exports=function(e){var t=n(this),i=o(e);if(r(t)<=i.size)return!1!==a(t,(function(e){if(i.includes(e))return!1}),!0);var h=i.getIterator();return!1!==l(h,(function(e){if(s(t,e))return d(h,"normal",!1)}))}},(e,t,i)=>{var n=i(8),s=i(47),r=i(30);e.exports=function(e,t,i){var o,a;s(e);try{if(o=r(e,"return"),!o){if("throw"===t)throw i;return i}o=n(o,e)}catch(l){a=!0,o=l}if("throw"===t)throw i;if(a)throw o;return s(o),i}},(e,t,i)=>{var n=i(3),s=i(115),r=i(108);n({target:"Set",proto:!0,real:!0,forced:!r("isSubsetOf")},{isSubsetOf:s})},(e,t,i)=>{var n=i(100),s=i(105),r=i(103),o=i(106);e.exports=function(e){var t=n(this),i=o(e);return!(s(t)>i.size)&&!1!==r(t,(function(e){if(!i.includes(e))return!1}),!0)}},(e,t,i)=>{var n=i(3),s=i(117),r=i(108);n({target:"Set",proto:!0,real:!0,forced:!r("isSupersetOf")},{isSupersetOf:s})},(e,t,i)=>{var n=i(100),s=i(101).has,r=i(105),o=i(106),a=i(104),l=i(113);e.exports=function(e){var t=n(this),i=o(e);if(r(t)<i.size)return!1;var d=i.getIterator();return!1!==a(d,(function(e){if(!s(t,e))return l(d,"normal",!1)}))}},(e,t,i)=>{var n=i(3),s=i(119),r=i(108);n({target:"Set",proto:!0,real:!0,forced:!r("symmetricDifference")},{symmetricDifference:s})},(e,t,i)=>{var n=i(100),s=i(101),r=i(102),o=i(106),a=i(104),l=s.add,d=s.has,h=s.remove;e.exports=function(e){var t=n(this),i=o(e).getIterator(),s=r(t);return a(i,(function(e){d(t,e)?h(s,e):l(s,e)})),s}},(e,t,i)=>{var n=i(3),s=i(121),r=i(108);n({target:"Set",proto:!0,real:!0,forced:!r("union")},{union:s})},(e,t,i)=>{var n=i(100),s=i(101).add,r=i(102),o=i(106),a=i(104);e.exports=function(e){var t=n(this),i=o(e).getIterator(),l=r(t);return a(i,(function(e){s(l,e)})),l}},e=>{e.exports=globalThis.pdfjsLib},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.compatibilityParams=t.OptionKind=t.AppOptions=void 0,i(76);const n=Object.create(null);t.compatibilityParams=n;{const e=navigator.userAgent||"",t=navigator.platform||"",i=navigator.maxTouchPoints||1,s=/Android/.test(e),r=/\b(iPad|iPhone|iPod)(?=;)/.test(e)||"MacIntel"===t&&i>1;(function(){(r||s)&&(n.maxCanvasPixels=5242880)})()}const s={VIEWER:2,API:4,WORKER:8,PREFERENCE:128};t.OptionKind=s;const r={annotationEditorMode:{value:0,kind:s.VIEWER+s.PREFERENCE},annotationMode:{value:2,kind:s.VIEWER+s.PREFERENCE},cursorToolOnLoad:{value:0,kind:s.VIEWER+s.PREFERENCE},defaultZoomDelay:{value:400,kind:s.VIEWER+s.PREFERENCE},defaultZoomValue:{value:"",kind:s.VIEWER+s.PREFERENCE},disableHistory:{value:!1,kind:s.VIEWER},disablePageLabels:{value:!1,kind:s.VIEWER+s.PREFERENCE},enablePermissions:{value:!1,kind:s.VIEWER+s.PREFERENCE},enablePrintAutoRotate:{value:!0,kind:s.VIEWER+s.PREFERENCE},enableScripting:{value:!0,kind:s.VIEWER+s.PREFERENCE},enableStampEditor:{value:!0,kind:s.VIEWER+s.PREFERENCE},externalLinkRel:{value:"noopener noreferrer nofollow",kind:s.VIEWER},externalLinkTarget:{value:0,kind:s.VIEWER+s.PREFERENCE},historyUpdateUrl:{value:!1,kind:s.VIEWER+s.PREFERENCE},ignoreDestinationZoom:{value:!1,kind:s.VIEWER+s.PREFERENCE},imageResourcesPath:{value:"./images/",kind:s.VIEWER},maxCanvasPixels:{value:16777216,kind:s.VIEWER},forcePageColors:{value:!1,kind:s.VIEWER+s.PREFERENCE},pageColorsBackground:{value:"Canvas",kind:s.VIEWER+s.PREFERENCE},pageColorsForeground:{value:"CanvasText",kind:s.VIEWER+s.PREFERENCE},pdfBugEnabled:{value:!1,kind:s.VIEWER+s.PREFERENCE},printResolution:{value:150,kind:s.VIEWER},sidebarViewOnLoad:{value:-1,kind:s.VIEWER+s.PREFERENCE},scrollModeOnLoad:{value:-1,kind:s.VIEWER+s.PREFERENCE},spreadModeOnLoad:{value:-1,kind:s.VIEWER+s.PREFERENCE},textLayerMode:{value:1,kind:s.VIEWER+s.PREFERENCE},viewerCssTheme:{value:0,kind:s.VIEWER+s.PREFERENCE},viewOnLoad:{value:0,kind:s.VIEWER+s.PREFERENCE},cMapPacked:{value:!0,kind:s.API},cMapUrl:{value:"../web/cmaps/",kind:s.API},disableAutoFetch:{value:!1,kind:s.API+s.PREFERENCE},disableFontFace:{value:!1,kind:s.API+s.PREFERENCE},disableRange:{value:!1,kind:s.API+s.PREFERENCE},disableStream:{value:!1,kind:s.API+s.PREFERENCE},docBaseUrl:{value:"",kind:s.API},enableXfa:{value:!0,kind:s.API+s.PREFERENCE},fontExtraProperties:{value:!1,kind:s.API},isEvalSupported:{value:!0,kind:s.API},isOffscreenCanvasSupported:{value:!0,kind:s.API},maxImageSize:{value:-1,kind:s.API},pdfBug:{value:!1,kind:s.API},standardFontDataUrl:{value:"../web/standard_fonts/",kind:s.API},verbosity:{value:1,kind:s.API},workerPort:{value:null,kind:s.WORKER},workerSrc:{value:"../build/pdf.worker.js",kind:s.WORKER}};r.defaultUrl={value:"compressed.tracemonkey-pldi-09.pdf",kind:s.VIEWER},r.disablePreferences={value:!1,kind:s.VIEWER},r.locale={value:navigator.language||"en-US",kind:s.VIEWER},r.sandboxBundleSrc={value:"../build/pdf.sandbox.js",kind:s.VIEWER};const o=Object.create(null);class a{constructor(){throw new Error("Cannot initialize AppOptions.")}static get(e){const t=o[e];if(void 0!==t)return t;const i=r[e];return void 0!==i?n[e]??i.value:void 0}static getAll(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;const t=Object.create(null);for(const i in r){const a=r[i];if(e){if(0===(e&a.kind))continue;if(e===s.PREFERENCE){const e=a.value,n=typeof e;if("boolean"===n||"string"===n||"number"===n&&Number.isInteger(e)){t[i]=e;continue}throw new Error(`Invalid type for preference: ${i}`)}}const l=o[i];t[i]=void 0!==l?l:n[i]??a.value}return t}static set(e,t){o[e]=t}static setAll(e){for(const t in e)o[t]=e[t]}static remove(e){delete o[e]}}t.AppOptions=a,a._hasUserOptions=function(){return Object.keys(o).length>0}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WaitOnType=t.EventBus=t.AutomationEventBus=void 0,t.waitOnEventOrTimeout=s,i(76),i(89);const n={EVENT:"event",TIMEOUT:"timeout"};function s(e){let{target:t,name:i,delay:s=0}=e;return new Promise((function(e,o){if("object"!==typeof t||!i||"string"!==typeof i||!(Number.isInteger(s)&&s>=0))throw new Error("waitOnEventOrTimeout - invalid parameters.");function a(n){t instanceof r?t._off(i,l):t.removeEventListener(i,l),h&&clearTimeout(h),e(n)}const l=a.bind(null,n.EVENT);t instanceof r?t._on(i,l):t.addEventListener(i,l);const d=a.bind(null,n.TIMEOUT),h=setTimeout(d,s)}))}t.WaitOnType=n;class r{#r=Object.create(null);on(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;this._on(e,t,{external:!0,once:i?.once})}off(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;this._off(e,t,{external:!0,once:i?.once})}dispatch(e,t){const i=this.#r[e];if(!i||0===i.length)return;let n;for(const{listener:s,external:r,once:o}of i.slice(0))o&&this._off(e,s),r?(n||=[]).push(s):s(t);if(n){for(const e of n)e(t);n=null}}_on(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;const n=this.#r[e]||=[];n.push({listener:t,external:!0===i?.external,once:!0===i?.once})}_off(e,t){const i=this.#r[e];if(i)for(let n=0,s=i.length;n<s;n++)if(i[n].listener===t)return void i.splice(n,1)}}t.EventBus=r;class o extends r{dispatch(e,t){throw new Error("Not implemented: AutomationEventBus.dispatch")}}t.AutomationEventBus=o},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleLinkService=t.PDFLinkService=t.LinkTarget=void 0,i(76),i(89),i(2);var n=i(97);const s="noopener noreferrer nofollow",r={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};function o(e){let{url:t,target:i,rel:n,enabled:o=!0}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!t||"string"!==typeof t)throw new Error('A valid "url" parameter must provided.');o?e.href=e.title=t:(e.href="",e.title=`Disabled: ${t}`,e.onclick=()=>!1);let a="";switch(i){case r.NONE:break;case r.SELF:a="_self";break;case r.BLANK:a="_blank";break;case r.PARENT:a="_parent";break;case r.TOP:a="_top";break}e.target=a,e.rel="string"===typeof n?n:s}t.LinkTarget=r;class a{#o=new Map;constructor(){let{eventBus:e,externalLinkTarget:t=null,externalLinkRel:i=null,ignoreDestinationZoom:n=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.eventBus=e,this.externalLinkTarget=t,this.externalLinkRel=i,this.externalLinkEnabled=!0,this._ignoreDestinationZoom=n,this.baseUrl=null,this.pdfDocument=null,this.pdfViewer=null,this.pdfHistory=null}setDocument(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.baseUrl=t,this.pdfDocument=e,this.#o.clear()}setViewer(e){this.pdfViewer=e}setHistory(e){this.pdfHistory=e}get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0}get page(){return this.pdfViewer.currentPageNumber}set page(e){this.pdfViewer.currentPageNumber=e}get rotation(){return this.pdfViewer.pagesRotation}set rotation(e){this.pdfViewer.pagesRotation=e}get isInPresentationMode(){return this.pdfViewer.isInPresentationMode}#a(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2?arguments[2]:void 0;const n=i[0];let s;if("object"===typeof n&&null!==n){if(s=this._cachedPageNumber(n),!s)return void this.pdfDocument.getPageIndex(n).then((s=>{this.cachePageRef(s+1,n),this.#a(e,t,i)})).catch((()=>{console.error(`PDFLinkService.#goToDestinationHelper: "${n}" is not a valid page reference, for dest="${e}".`)}))}else{if(!Number.isInteger(n))return void console.error(`PDFLinkService.#goToDestinationHelper: "${n}" is not a valid destination reference, for dest="${e}".`);s=n+1}!s||s<1||s>this.pagesCount?console.error(`PDFLinkService.#goToDestinationHelper: "${s}" is not a valid page number, for dest="${e}".`):(this.pdfHistory&&(this.pdfHistory.pushCurrentPosition(),this.pdfHistory.push({namedDest:t,explicitDest:i,pageNumber:s})),this.pdfViewer.scrollPageIntoView({pageNumber:s,destArray:i,ignoreDestinationZoom:this._ignoreDestinationZoom}))}async goToDestination(e){if(!this.pdfDocument)return;let t,i;"string"===typeof e?(t=e,i=await this.pdfDocument.getDestination(e)):(t=null,i=await e),Array.isArray(i)?this.#a(e,t,i):console.error(`PDFLinkService.goToDestination: "${i}" is not a valid destination array, for dest="${e}".`)}goToPage(e){if(!this.pdfDocument)return;const t="string"===typeof e&&this.pdfViewer.pageLabelToPageNumber(e)||0|e;Number.isInteger(t)&&t>0&&t<=this.pagesCount?(this.pdfHistory&&(this.pdfHistory.pushCurrentPosition(),this.pdfHistory.pushPage(t)),this.pdfViewer.scrollPageIntoView({pageNumber:t})):console.error(`PDFLinkService.goToPage: "${e}" is not a valid page.`)}addLinkAttributes(e,t){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];o(e,{url:t,target:i?r.BLANK:this.externalLinkTarget,rel:this.externalLinkRel,enabled:this.externalLinkEnabled})}getDestinationHash(e){if("string"===typeof e){if(e.length>0)return this.getAnchorUrl("#"+escape(e))}else if(Array.isArray(e)){const t=JSON.stringify(e);if(t.length>0)return this.getAnchorUrl("#"+escape(t))}return this.getAnchorUrl("")}getAnchorUrl(e){return this.baseUrl?this.baseUrl+e:e}setHash(e){if(!this.pdfDocument)return;let t,i;if(e.includes("=")){const s=(0,n.parseQueryString)(e);if(s.has("search")){const e=s.get("search").replaceAll('"',""),t="true"===s.get("phrase");this.eventBus.dispatch("findfromurlhash",{source:this,query:t?e:e.match(/\S+/g)})}if(s.has("page")&&(t=0|s.get("page")||1),s.has("zoom")){const e=s.get("zoom").split(","),t=e[0],n=parseFloat(t);t.includes("Fit")?"Fit"===t||"FitB"===t?i=[null,{name:t}]:"FitH"===t||"FitBH"===t||"FitV"===t||"FitBV"===t?i=[null,{name:t},e.length>1?0|e[1]:null]:"FitR"===t?5!==e.length?console.error('PDFLinkService.setHash: Not enough parameters for "FitR".'):i=[null,{name:t},0|e[1],0|e[2],0|e[3],0|e[4]]:console.error(`PDFLinkService.setHash: "${t}" is not a valid zoom value.`):i=[null,{name:"XYZ"},e.length>1?0|e[1]:null,e.length>2?0|e[2]:null,n?n/100:t]}i?this.pdfViewer.scrollPageIntoView({pageNumber:t||this.page,destArray:i,allowNegativeOffset:!0}):t&&(this.page=t),s.has("pagemode")&&this.eventBus.dispatch("pagemode",{source:this,mode:s.get("pagemode")}),s.has("nameddest")&&this.goToDestination(s.get("nameddest"))}else{i=unescape(e);try{i=JSON.parse(i),Array.isArray(i)||(i=i.toString())}catch{}if("string"===typeof i||a.#l(i))return void this.goToDestination(i);console.error(`PDFLinkService.setHash: "${unescape(e)}" is not a valid destination.`)}}executeNamedAction(e){switch(e){case"GoBack":this.pdfHistory?.back();break;case"GoForward":this.pdfHistory?.forward();break;case"NextPage":this.pdfViewer.nextPage();break;case"PrevPage":this.pdfViewer.previousPage();break;case"LastPage":this.page=this.pagesCount;break;case"FirstPage":this.page=1;break;default:break}this.eventBus.dispatch("namedaction",{source:this,action:e})}async executeSetOCGState(e){const t=this.pdfDocument,i=await this.pdfViewer.optionalContentConfigPromise;if(t!==this.pdfDocument)return;let n;for(const s of e.state){switch(s){case"ON":case"OFF":case"Toggle":n=s;continue}switch(n){case"ON":i.setVisibility(s,!0);break;case"OFF":i.setVisibility(s,!1);break;case"Toggle":const e=i.getGroup(s);e&&i.setVisibility(s,!e.visible);break}}this.pdfViewer.optionalContentConfigPromise=Promise.resolve(i)}cachePageRef(e,t){if(!t)return;const i=0===t.gen?`${t.num}R`:`${t.num}R${t.gen}`;this.#o.set(i,e)}_cachedPageNumber(e){if(!e)return null;const t=0===e.gen?`${e.num}R`:`${e.num}R${e.gen}`;return this.#o.get(t)||null}static#l(e){if(!Array.isArray(e))return!1;const t=e.length;if(t<2)return!1;const i=e[0];if(("object"!==typeof i||!Number.isInteger(i.num)||!Number.isInteger(i.gen))&&!(Number.isInteger(i)&&i>=0))return!1;const n=e[1];if("object"!==typeof n||"string"!==typeof n.name)return!1;let s=!0;switch(n.name){case"XYZ":if(5!==t)return!1;break;case"Fit":case"FitB":return 2===t;case"FitH":case"FitBH":case"FitV":case"FitBV":if(3!==t)return!1;break;case"FitR":if(6!==t)return!1;s=!1;break;default:return!1}for(let r=2;r<t;r++){const t=e[r];if(!("number"===typeof t||s&&null===t))return!1}return!0}}t.PDFLinkService=a;class l{constructor(){this.externalLinkEnabled=!0}get pagesCount(){return 0}get page(){return 0}set page(e){}get rotation(){return 0}set rotation(e){}get isInPresentationMode(){return!1}async goToDestination(e){}goToPage(e){}addLinkAttributes(e,t){o(e,{url:t,enabled:this.externalLinkEnabled})}getDestinationHash(e){return"#"}getAnchorUrl(e){return"#"}setHash(e){}executeNamedAction(e){}executeSetOCGState(e){}cachePageRef(e,t){}}t.SimpleLinkService=l},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AltTextManager=void 0;var n=i(122);class s{#d=this.#h.bind(this);#c=this.#u.bind(this);#p=this.#g.bind(this);#f=null;#m;#v;#w;#b=!1;#_;#y;#P;#E;#S;#C;#L=null;#T=null;#M=null;#I;#x=null;constructor(e,t,i,n){let{dialog:s,optionDescription:r,optionDecorative:o,textarea:a,cancelButton:l,saveButton:d}=e;this.#v=s,this.#_=r,this.#y=o,this.#S=a,this.#m=l,this.#E=d,this.#P=i,this.#w=n,this.#I=t,s.addEventListener("close",this.#A.bind(this)),s.addEventListener("contextmenu",(e=>{e.target!==this.#S&&e.preventDefault()})),l.addEventListener("click",this.#D.bind(this)),d.addEventListener("click",this.#N.bind(this)),r.addEventListener("change",this.#d),o.addEventListener("change",this.#d),this.#P.register(s)}get _elements(){return(0,n.shadow)(this,"_elements",[this.#_,this.#y,this.#S,this.#E,this.#m])}#k(){if(this.#T)return;const e=new n.DOMSVGFactory,t=this.#T=e.createElement("svg");t.setAttribute("width","0"),t.setAttribute("height","0");const i=e.createElement("defs");t.append(i);const s=e.createElement("mask");i.append(s),s.setAttribute("id","alttext-manager-mask"),s.setAttribute("maskContentUnits","objectBoundingBox");let r=e.createElement("rect");s.append(r),r.setAttribute("fill","white"),r.setAttribute("width","1"),r.setAttribute("height","1"),r.setAttribute("x","0"),r.setAttribute("y","0"),r=this.#M=e.createElement("rect"),s.append(r),r.setAttribute("fill","black"),this.#v.append(t)}async editAltText(e,t){if(this.#f||!t)return;this.#k(),this.#b=!1;for(const r of this._elements)r.addEventListener("click",this.#p);const{altText:i,decorative:n}=t.altTextData;!0===n?(this.#y.checked=!0,this.#_.checked=!1):(this.#y.checked=!1,this.#_.checked=!0),this.#L=this.#S.value=i?.trim()||"",this.#h(),this.#f=t,this.#C=e,this.#C.removeEditListeners(),this.#w._on("resize",this.#c);try{await this.#P.open(this.#v),this.#u()}catch(s){throw this.#A(),s}}#u(){if(!this.#f)return;const e=this.#v,{style:t}=e,{x:i,y:n,width:s,height:r}=this.#I.getBoundingClientRect(),{innerWidth:o,innerHeight:a}=window,{width:l,height:d}=e.getBoundingClientRect(),{x:h,y:c,width:u,height:p}=this.#f.getClientDimensions(),g=10,f="ltr"===this.#C.direction,m=Math.max(h,i),v=Math.min(h+u,i+s),w=Math.max(c,n),b=Math.min(c+p,n+r);this.#M.setAttribute("width",""+(v-m)/o),this.#M.setAttribute("height",""+(b-w)/a),this.#M.setAttribute("x",""+m/o),this.#M.setAttribute("y",""+w/a);let _=null,y=Math.max(c,0);y+=Math.min(a-(y+d),0),f?h+u+g+l<o?_=h+u+g:h>l+g&&(_=h-l-g):h>l+g?_=h-l-g:h+u+g+l<o&&(_=h+u+g),null===_&&(y=null,_=Math.max(h,0),_+=Math.min(o-(_+l),0),c>d+g?y=c-d-g:c+p+g+d<a&&(y=c+p+g)),null!==y?(e.classList.add("positioned"),f?t.left=`${_}px`:t.right=o-_-l+"px",t.top=`${y}px`):(e.classList.remove("positioned"),t.left="",t.top="")}#D(){this.#P.active===this.#v&&this.#P.close(this.#v)}#A(){this.#w.dispatch("reporttelemetry",{source:this,details:{type:"editing",subtype:this.#f.editorType,data:this.#x||{action:"alt_text_cancel",alt_text_keyboard:!this.#b}}}),this.#x=null,this.#B(),this.#C?.addEditListeners(),this.#w._off("resize",this.#c),this.#f=null,this.#C=null}#h(){this.#S.disabled=this.#y.checked}#N(){const e=this.#S.value.trim(),t=this.#y.checked;this.#f.altTextData={altText:e,decorative:t},this.#x={action:"alt_text_save",alt_text_description:!!e,alt_text_edit:!!this.#L&&this.#L!==e,alt_text_decorative:t,alt_text_keyboard:!this.#b},this.#D()}#g(e){0!==e.detail&&(this.#b=!0,this.#B())}#B(){for(const e of this._elements)e.removeEventListener("click",this.#p)}destroy(){this.#C=null,this.#D(),this.#T?.remove(),this.#T=this.#M=null}}t.AltTextManager=s},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationEditorParams=void 0;var n=i(122);class s{constructor(e,t){this.eventBus=t,this.#O(e)}#O(e){let{editorFreeTextFontSize:t,editorFreeTextColor:i,editorInkColor:s,editorInkThickness:r,editorInkOpacity:o,editorStampAddImage:a}=e;const l=(e,t)=>{this.eventBus.dispatch("switchannotationeditorparams",{source:this,type:n.AnnotationEditorParamsType[e],value:t})};t.addEventListener("input",(function(){l("FREETEXT_SIZE",this.valueAsNumber)})),i.addEventListener("input",(function(){l("FREETEXT_COLOR",this.value)})),s.addEventListener("input",(function(){l("INK_COLOR",this.value)})),r.addEventListener("input",(function(){l("INK_THICKNESS",this.valueAsNumber)})),o.addEventListener("input",(function(){l("INK_OPACITY",this.valueAsNumber)})),a.addEventListener("click",(()=>{l("CREATE")})),this.eventBus._on("annotationeditorparamschanged",(e=>{for(const[a,l]of e.details)switch(a){case n.AnnotationEditorParamsType.FREETEXT_SIZE:t.value=l;break;case n.AnnotationEditorParamsType.FREETEXT_COLOR:i.value=l;break;case n.AnnotationEditorParamsType.INK_COLOR:s.value=l;break;case n.AnnotationEditorParamsType.INK_THICKNESS:r.value=l;break;case n.AnnotationEditorParamsType.INK_OPACITY:o.value=l;break}}))}}t.AnnotationEditorParams=s},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.OverlayManager=void 0,i(76);class n{#V=new WeakMap;#R=null;get active(){return this.#R}async register(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if("object"!==typeof e)throw new Error("Not enough parameters.");if(this.#V.has(e))throw new Error("The overlay is already registered.");this.#V.set(e,{canForceClose:t}),e.addEventListener("cancel",(e=>{this.#R=null}))}async open(e){if(!this.#V.has(e))throw new Error("The overlay does not exist.");if(this.#R){if(this.#R===e)throw new Error("The overlay is already active.");if(!this.#V.get(e).canForceClose)throw new Error("Another overlay is currently active.");await this.close()}this.#R=e,e.showModal()}async close(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.#R;if(!this.#V.has(e))throw new Error("The overlay does not exist.");if(!this.#R)throw new Error("The overlay is currently not active.");if(this.#R!==e)throw new Error("Another overlay is currently active.");e.close(),this.#R=null}}t.OverlayManager=n},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PasswordPrompt=void 0,i(76);var n=i(122);class s{#F=null;#U=null;#z=null;constructor(e,t,i){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];this.dialog=e.dialog,this.label=e.label,this.input=e.input,this.submitButton=e.submitButton,this.cancelButton=e.cancelButton,this.overlayManager=t,this.l10n=i,this._isViewerEmbedded=n,this.submitButton.addEventListener("click",this.#H.bind(this)),this.cancelButton.addEventListener("click",this.close.bind(this)),this.input.addEventListener("keydown",(e=>{13===e.keyCode&&this.#H()})),this.overlayManager.register(this.dialog,!0),this.dialog.addEventListener("close",this.#W.bind(this))}async open(){this.#F&&await this.#F.promise,this.#F=new n.PromiseCapability;try{await this.overlayManager.open(this.dialog)}catch(t){throw this.#F.resolve(),t}const e=this.#z===n.PasswordResponses.INCORRECT_PASSWORD;this._isViewerEmbedded&&!e||this.input.focus(),this.label.textContent=await this.l10n.get("password_"+(e?"invalid":"label"))}async close(){this.overlayManager.active===this.dialog&&this.overlayManager.close(this.dialog)}#H(){const e=this.input.value;e?.length>0&&this.#j(e)}#W(){this.#j(new Error("PasswordPrompt cancelled.")),this.#F.resolve()}#j(e){this.#U&&(this.close(),this.input.value="",this.#U(e),this.#U=null)}async setUpdateCallback(e,t){this.#F&&await this.#F.promise,this.#U=e,this.#z=t}}t.PasswordPrompt=s},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFAttachmentViewer=void 0;var n=i(122),s=i(131),r=i(124);class o extends s.BaseTreeViewer{constructor(e){super(e),this.downloadManager=e.downloadManager,this.eventBus._on("fileattachmentannotation",this.#$.bind(this))}reset(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];super.reset(),this._attachments=null,e||(this._renderedCapability=new n.PromiseCapability),this._pendingDispatchEvent=!1}async _dispatchEvent(e){this._renderedCapability.resolve(),(0!==e||this._pendingDispatchEvent||(this._pendingDispatchEvent=!0,await(0,r.waitOnEventOrTimeout)({target:this.eventBus,name:"annotationlayerrendered",delay:1e3}),this._pendingDispatchEvent))&&(this._pendingDispatchEvent=!1,this.eventBus.dispatch("attachmentsloaded",{source:this,attachmentsCount:e}))}_bindLink(e,t){let{content:i,filename:n}=t;e.onclick=()=>(this.downloadManager.openOrDownloadData(e,i,n),!1)}render(e){let{attachments:t,keepRenderedCapability:i=!1}=e;if(this._attachments&&this.reset(i),this._attachments=t||null,!t)return void this._dispatchEvent(0);const s=document.createDocumentFragment();let r=0;for(const o in t){const e=t[o],i=e.content,a=(0,n.getFilenameFromUrl)(e.filename,!0),l=document.createElement("div");l.className="treeItem";const d=document.createElement("a");this._bindLink(d,{content:i,filename:a}),d.textContent=this._normalizeTextContent(a),l.append(d),s.append(l),r++}this._finishRendering(s,r)}#$(e){let{filename:t,content:i}=e;const n=this._renderedCapability.promise;n.then((()=>{if(n!==this._renderedCapability.promise)return;const e=this._attachments||Object.create(null);for(const i in e)if(t===i)return;e[t]={filename:t,content:i},this.render({attachments:e,keepRenderedCapability:!0})}))}}t.PDFAttachmentViewer=o},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BaseTreeViewer=void 0,i(76);var n=i(97);const s=-100,r="selected";class o{constructor(e){if(this.constructor===o)throw new Error("Cannot initialize BaseTreeViewer.");this.container=e.container,this.eventBus=e.eventBus,this.reset()}reset(){this._pdfDocument=null,this._lastToggleIsShow=!0,this._currentTreeItem=null,this.container.textContent="",this.container.classList.remove("treeWithDeepNesting")}_dispatchEvent(e){throw new Error("Not implemented: _dispatchEvent")}_bindLink(e,t){throw new Error("Not implemented: _bindLink")}_normalizeTextContent(e){return(0,n.removeNullCharacters)(e,!0)||"–"}_addToggleButton(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=document.createElement("div");i.className="treeItemToggler",t&&i.classList.add("treeItemsHidden"),i.onclick=t=>{if(t.stopPropagation(),i.classList.toggle("treeItemsHidden"),t.shiftKey){const t=!i.classList.contains("treeItemsHidden");this._toggleTreeItem(e,t)}},e.prepend(i)}_toggleTreeItem(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this._lastToggleIsShow=t;for(const i of e.querySelectorAll(".treeItemToggler"))i.classList.toggle("treeItemsHidden",!t)}_toggleAllTreeItems(){this._toggleTreeItem(this.container,!this._lastToggleIsShow)}_finishRendering(e,t){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];i&&(this.container.classList.add("treeWithDeepNesting"),this._lastToggleIsShow=!e.querySelector(".treeItemsHidden")),this.container.append(e),this._dispatchEvent(t)}render(e){throw new Error("Not implemented: render")}_updateCurrentTreeItem(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;this._currentTreeItem&&(this._currentTreeItem.classList.remove(r),this._currentTreeItem=null),e&&(e.classList.add(r),this._currentTreeItem=e)}_scrollToCurrentTreeItem(e){if(!e)return;let t=e.parentNode;while(t&&t!==this.container){if(t.classList.contains("treeItem")){const e=t.firstElementChild;e?.classList.remove("treeItemsHidden")}t=t.parentNode}this._updateCurrentTreeItem(e),this.container.scrollTo(e.offsetLeft,e.offsetTop+s)}}t.BaseTreeViewer=o},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFCursorTools=void 0;var n=i(122),s=i(97),r=i(133);class o{#R=s.CursorTool.SELECT;#G=null;constructor(e){let{container:t,eventBus:i,cursorToolOnLoad:n=s.CursorTool.SELECT}=e;this.container=t,this.eventBus=i,this.#X(),Promise.resolve().then((()=>{this.switchTool(n)}))}get activeTool(){return this.#R}switchTool(e){if(null!==this.#G)return;if(e===this.#R)return;const t=()=>{switch(this.#R){case s.CursorTool.SELECT:break;case s.CursorTool.HAND:this._handTool.deactivate();break;case s.CursorTool.ZOOM:}};switch(e){case s.CursorTool.SELECT:t();break;case s.CursorTool.HAND:t(),this._handTool.activate();break;case s.CursorTool.ZOOM:default:return void console.error(`switchTool: "${e}" is an unsupported value.`)}this.#R=e,this.eventBus.dispatch("cursortoolchanged",{source:this,tool:e})}#X(){this.eventBus._on("switchcursortool",(e=>{this.switchTool(e.tool)}));let e=n.AnnotationEditorType.NONE,t=s.PresentationModeState.NORMAL;const i=()=>{const e=this.#R;this.switchTool(s.CursorTool.SELECT),this.#G??=e},r=()=>{const i=this.#G;null!==i&&e===n.AnnotationEditorType.NONE&&t===s.PresentationModeState.NORMAL&&(this.#G=null,this.switchTool(i))};this.eventBus._on("secondarytoolbarreset",(i=>{null!==this.#G&&(e=n.AnnotationEditorType.NONE,t=s.PresentationModeState.NORMAL,r())})),this.eventBus._on("annotationeditormodechanged",(t=>{let{mode:s}=t;e=s,s===n.AnnotationEditorType.NONE?r():i()})),this.eventBus._on("presentationmodechanged",(e=>{let{state:n}=e;t=n,n===s.PresentationModeState.NORMAL?r():n===s.PresentationModeState.FULLSCREEN&&i()}))}get _handTool(){return(0,n.shadow)(this,"_handTool",new r.GrabToPan({element:this.container}))}}t.PDFCursorTools=o},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GrabToPan=void 0;const i="grab-to-pan-grab";class n{constructor(e){let{element:t}=e;this.element=t,this.document=t.ownerDocument,this.activate=this.activate.bind(this),this.deactivate=this.deactivate.bind(this),this.toggle=this.toggle.bind(this),this._onMouseDown=this.#K.bind(this),this._onMouseMove=this.#Z.bind(this),this._endPan=this.#Q.bind(this);const i=this.overlay=document.createElement("div");i.className="grab-to-pan-grabbing"}activate(){this.active||(this.active=!0,this.element.addEventListener("mousedown",this._onMouseDown,!0),this.element.classList.add(i))}deactivate(){this.active&&(this.active=!1,this.element.removeEventListener("mousedown",this._onMouseDown,!0),this._endPan(),this.element.classList.remove(i))}toggle(){this.active?this.deactivate():this.activate()}ignoreTarget(e){return e.matches("a[href], a[href] *, input, textarea, button, button *, select, option")}#K(e){if(0!==e.button||this.ignoreTarget(e.target))return;if(e.originalTarget)try{e.originalTarget.tagName}catch{return}this.scrollLeftStart=this.element.scrollLeft,this.scrollTopStart=this.element.scrollTop,this.clientXStart=e.clientX,this.clientYStart=e.clientY,this.document.addEventListener("mousemove",this._onMouseMove,!0),this.document.addEventListener("mouseup",this._endPan,!0),this.element.addEventListener("scroll",this._endPan,!0),e.preventDefault(),e.stopPropagation();const t=document.activeElement;t&&!t.contains(e.target)&&t.blur()}#Z(e){if(this.element.removeEventListener("scroll",this._endPan,!0),!(1&e.buttons))return void this._endPan();const t=e.clientX-this.clientXStart,i=e.clientY-this.clientYStart;this.element.scrollTo({top:this.scrollTopStart-i,left:this.scrollLeftStart-t,behavior:"instant"}),this.overlay.parentNode||document.body.append(this.overlay)}#Q(){this.element.removeEventListener("scroll",this._endPan,!0),this.document.removeEventListener("mousemove",this._onMouseMove,!0),this.document.removeEventListener("mouseup",this._endPan,!0),this.overlay.remove()}}t.GrabToPan=n},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFDocumentProperties=void 0;var n=i(97),s=i(122);const r="-",o=["en-us","en-lr","my"],a={"8.5x11":"Letter","8.5x14":"Legal"},l={"297x420":"A3","210x297":"A4"};function d(e,t,i){const n=t?e.width:e.height,s=t?e.height:e.width;return i[`${n}x${s}`]}class h{#q=null;constructor(e,t,i,n,s){let{dialog:r,fields:a,closeButton:l}=e;this.dialog=r,this.fields=a,this.overlayManager=t,this.l10n=n,this._fileNameLookup=s,this.#Y(),l.addEventListener("click",this.close.bind(this)),this.overlayManager.register(this.dialog),i._on("pagechanging",(e=>{this._currentPageNumber=e.pageNumber})),i._on("rotationchanging",(e=>{this._pagesRotation=e.pagesRotation})),this._isNonMetricLocale=!0,n.getLanguage().then((e=>{this._isNonMetricLocale=o.includes(e)}))}async open(){await Promise.all([this.overlayManager.open(this.dialog),this._dataAvailableCapability.promise]);const e=this._currentPageNumber,t=this._pagesRotation;if(this.#q&&e===this.#q._currentPageNumber&&t===this.#q._pagesRotation)return void this.#J();const{info:i,contentLength:s}=await this.pdfDocument.getMetadata(),[r,o,a,l,d,h]=await Promise.all([this._fileNameLookup(),this.#ee(s),this.#te(i.CreationDate),this.#te(i.ModDate),this.pdfDocument.getPage(e).then((e=>this.#ie((0,n.getPageSizeInches)(e),t))),this.#ne(i.IsLinearized)]);this.#q=Object.freeze({fileName:r,fileSize:o,title:i.Title,author:i.Author,subject:i.Subject,keywords:i.Keywords,creationDate:a,modificationDate:l,creator:i.Creator,producer:i.Producer,version:i.PDFFormatVersion,pageCount:this.pdfDocument.numPages,pageSize:d,linearized:h,_currentPageNumber:e,_pagesRotation:t}),this.#J();const{length:c}=await this.pdfDocument.getDownloadInfo();if(s===c)return;const u=Object.assign(Object.create(null),this.#q);u.fileSize=await this.#ee(c),this.#q=Object.freeze(u),this.#J()}async close(){this.overlayManager.close(this.dialog)}setDocument(e){this.pdfDocument&&(this.#Y(),this.#J(!0)),e&&(this.pdfDocument=e,this._dataAvailableCapability.resolve())}#Y(){this.pdfDocument=null,this.#q=null,this._dataAvailableCapability=new s.PromiseCapability,this._currentPageNumber=1,this._pagesRotation=0}#J(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!e&&this.#q){if(this.overlayManager.active===this.dialog)for(const t in this.fields){const e=this.#q[t];this.fields[t].textContent=e||0===e?e:r}}else for(const t in this.fields)this.fields[t].textContent=r}async#ee(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const t=e/1024,i=t/1024;if(t)return this.l10n.get("document_properties_"+(i>=1?"mb":"kb"),{size_mb:i>=1&&(+i.toPrecision(3)).toLocaleString(),size_kb:i<1&&(+t.toPrecision(3)).toLocaleString(),size_b:e.toLocaleString()})}async#ie(e,t){if(!e)return;t%180!==0&&(e={width:e.height,height:e.width});const i=(0,n.isPortraitOrientation)(e);let s={width:Math.round(100*e.width)/100,height:Math.round(100*e.height)/100},r={width:Math.round(25.4*e.width*10)/10,height:Math.round(25.4*e.height*10)/10},o=d(s,i,a)||d(r,i,l);if(!o&&(!Number.isInteger(r.width)||!Number.isInteger(r.height))){const t={width:25.4*e.width,height:25.4*e.height},n={width:Math.round(r.width),height:Math.round(r.height)};Math.abs(t.width-n.width)<.1&&Math.abs(t.height-n.height)<.1&&(o=d(n,i,l),o&&(s={width:Math.round(n.width/25.4*100)/100,height:Math.round(n.height/25.4*100)/100},r=n))}const[{width:h,height:c},u,p,g]=await Promise.all([this._isNonMetricLocale?s:r,this.l10n.get("document_properties_page_size_unit_"+(this._isNonMetricLocale?"inches":"millimeters")),o&&this.l10n.get(`document_properties_page_size_name_${o.toLowerCase()}`),this.l10n.get("document_properties_page_size_orientation_"+(i?"portrait":"landscape"))]);return this.l10n.get(`document_properties_page_size_dimension_${p?"name_":""}string`,{width:h.toLocaleString(),height:c.toLocaleString(),unit:u,name:p,orientation:g})}async#te(e){const t=s.PDFDateString.toDateObject(e);if(t)return this.l10n.get("document_properties_date_string",{date:t.toLocaleDateString(),time:t.toLocaleTimeString()})}#ne(e){return this.l10n.get("document_properties_linearized_"+(e?"yes":"no"))}}t.PDFDocumentProperties=h},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFindBar=void 0;var n=i(136),s=i(97);const r=1e3;class o{constructor(e,t,i){this.opened=!1,this.bar=e.bar,this.toggleButton=e.toggleButton,this.findField=e.findField,this.highlightAll=e.highlightAllCheckbox,this.caseSensitive=e.caseSensitiveCheckbox,this.matchDiacritics=e.matchDiacriticsCheckbox,this.entireWord=e.entireWordCheckbox,this.findMsg=e.findMsg,this.findResultsCount=e.findResultsCount,this.findPreviousButton=e.findPreviousButton,this.findNextButton=e.findNextButton,this.eventBus=t,this.l10n=i,this.toggleButton.addEventListener("click",(()=>{this.toggle()})),this.findField.addEventListener("input",(()=>{this.dispatchEvent("")})),this.bar.addEventListener("keydown",(e=>{switch(e.keyCode){case 13:e.target===this.findField&&this.dispatchEvent("again",e.shiftKey);break;case 27:this.close();break}})),this.findPreviousButton.addEventListener("click",(()=>{this.dispatchEvent("again",!0)})),this.findNextButton.addEventListener("click",(()=>{this.dispatchEvent("again",!1)})),this.highlightAll.addEventListener("click",(()=>{this.dispatchEvent("highlightallchange")})),this.caseSensitive.addEventListener("click",(()=>{this.dispatchEvent("casesensitivitychange")})),this.entireWord.addEventListener("click",(()=>{this.dispatchEvent("entirewordchange")})),this.matchDiacritics.addEventListener("click",(()=>{this.dispatchEvent("diacriticmatchingchange")})),this.eventBus._on("resize",this.#se.bind(this))}reset(){this.updateUIState()}dispatchEvent(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];this.eventBus.dispatch("find",{source:this,type:e,query:this.findField.value,caseSensitive:this.caseSensitive.checked,entireWord:this.entireWord.checked,highlightAll:this.highlightAll.checked,findPrevious:t,matchDiacritics:this.matchDiacritics.checked})}updateUIState(e,t,i){let s=Promise.resolve(""),r="";switch(e){case n.FindState.FOUND:break;case n.FindState.PENDING:r="pending";break;case n.FindState.NOT_FOUND:s=this.l10n.get("find_not_found"),r="notFound";break;case n.FindState.WRAPPED:s=this.l10n.get("find_reached_"+(t?"top":"bottom"));break}this.findField.setAttribute("data-status",r),this.findField.setAttribute("aria-invalid",e===n.FindState.NOT_FOUND),s.then((e=>{this.findMsg.setAttribute("data-status",r),this.findMsg.textContent=e,this.#se()})),this.updateResultsCount(i)}updateResultsCount(){let{current:e=0,total:t=0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const i=r;let n=Promise.resolve("");if(t>0)if(t>i){let e="find_match_count_limit";n=this.l10n.get(e,{limit:i})}else{let i="find_match_count";n=this.l10n.get(i,{current:e,total:t})}n.then((e=>{this.findResultsCount.textContent=e,this.#se()}))}open(){this.opened||(this.opened=!0,(0,s.toggleExpandedBtn)(this.toggleButton,!0,this.bar)),this.findField.select(),this.findField.focus(),this.#se()}close(){this.opened&&(this.opened=!1,(0,s.toggleExpandedBtn)(this.toggleButton,!1,this.bar),this.eventBus.dispatch("findbarclose",{source:this}))}toggle(){this.opened?this.close():this.open()}#se(){if(!this.opened)return;this.bar.classList.remove("wrapContainers");const e=this.bar.clientHeight,t=this.bar.firstElementChild.clientHeight;e>t&&this.bar.classList.add("wrapContainers")}}t.PDFFindBar=o},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFindController=t.FindState=void 0,i(98),i(109),i(111),i(114),i(116),i(118),i(120),i(89);var n=i(97),s=i(137),r=i(122);const o={FOUND:0,NOT_FOUND:1,WRAPPED:2,PENDING:3};t.FindState=o;const a=250,l=-50,d=-400,h={"‐":"-","‘":"'","’":"'","‚":"'","‛":"'","“":'"',"”":'"',"„":'"',"‟":'"',"¼":"1/4","½":"1/2","¾":"3/4"},c=new Set([12441,12442,2381,2509,2637,2765,2893,3021,3149,3277,3387,3388,3405,3530,3642,3770,3972,4153,4154,5908,5940,6098,6752,6980,7082,7083,7154,7155,11647,43014,43052,43204,43347,43456,43766,44013,3158,3953,3954,3962,3963,3964,3965,3968,3956]);let u;const p=/\p{M}+/gu,g=/([.*+?^${}()|[\]\\])|(\p{P})|(\s+)|(\p{M})|(\p{L})/gu,f=/([^\p{M}])\p{M}*$/u,m=/^\p{M}*([^\p{M}])/u,v=/[\uAC00-\uD7AF\uFA6C\uFACF-\uFAD1\uFAD5-\uFAD7]+/g,w=new Map,b="[\\u1100-\\u1112\\ud7a4-\\ud7af\\ud84a\\ud84c\\ud850\\ud854\\ud857\\ud85f]",_=new Map;let y=null,P=null;function E(e){const t=[];let i,n;while(null!==(i=v.exec(e))){let{index:e}=i;for(const n of i[0]){let i=w.get(n);i||(i=n.normalize("NFD").length,w.set(n,i)),t.push([i,e++])}}if(0===t.length&&y)n=y;else if(t.length>0&&P)n=P;else{const e=Object.keys(h).join(""),i=(0,s.getNormalizeWithNFKC)(),r="(?:\\p{Ideographic}|[぀-ヿ])",o="(?:゙|゚)",a=`([${e}])|([${i}])|(${o}\\n)|(\\p{M}+(?:-\\n)?)|(\\S-\\n)|(${r}\\n)|(\\n)`;n=0===t.length?y=new RegExp(a+"|(\\u0000)","gum"):P=new RegExp(a+`|(${b})`,"gum")}const r=[];while(null!==(i=p.exec(e)))r.push([i[0].length,i.index]);let o=e.normalize("NFD");const a=[[0,0]];let l=0,d=0,c=0,u=0,g=0,f=!1;return o=o.replace(n,((e,i,n,s,o,p,m,v,w,b)=>{if(b-=u,i){const e=h[i],t=e.length;for(let i=1;i<t;i++)a.push([b-c+i,c-i]);return c-=t-1,e}if(n){let e=_.get(n);e||(e=n.normalize("NFKC"),_.set(n,e));const t=e.length;for(let i=1;i<t;i++)a.push([b-c+i,c-i]);return c-=t-1,e}if(s)return f=!0,b+g===r[l]?.[1]?++l:(a.push([b-1-c+1,c-1]),c-=1,u+=1),a.push([b-c+1,c]),u+=1,g+=1,s.charAt(0);if(o){const e=o.endsWith("\n"),t=e?o.length-2:o.length;f=!0;let i=t;b+g===r[l]?.[1]&&(i-=r[l][0],++l);for(let n=1;n<=i;n++)a.push([b-1-c+n,c-n]);return c-=i,u+=i,e?(b+=t-1,a.push([b-c+1,1+c]),c+=1,u+=1,g+=1,o.slice(0,t)):o}if(p){const e=p.length-2;return a.push([b-c+e,1+c]),c+=1,u+=1,g+=1,p.slice(0,-2)}if(m){const e=m.length-1;return a.push([b-c+e,c]),u+=1,g+=1,m.slice(0,-1)}if(v)return a.push([b-c+1,c-1]),c-=1,u+=1,g+=1," ";if(b+g===t[d]?.[1]){const e=t[d][0]-1;++d;for(let t=1;t<=e;t++)a.push([b-(c-t),c-t]);c-=e,u+=e}return w})),a.push([o.length,c]),[o,a,f]}function S(e,t,i){if(!e)return[t,i];const s=t,r=t+i-1;let o=(0,n.binarySearchFirstItem)(e,(e=>e[0]>=s));e[o][0]>s&&--o;let a=(0,n.binarySearchFirstItem)(e,(e=>e[0]>=r),o);e[a][0]>r&&--a;const l=s+e[o][1],d=r+e[a][1],h=d+1-l;return[l,h]}class C{#re=null;#oe=!0;#ae=0;constructor(e){let{linkService:t,eventBus:i,updateMatchesCountOnProgress:n=!0}=e;this._linkService=t,this._eventBus=i,this.#oe=n,this.onIsPageVisible=null,this.#Y(),i._on("find",this.#le.bind(this)),i._on("findbarclose",this.#de.bind(this))}get highlightMatches(){return this._highlightMatches}get pageMatches(){return this._pageMatches}get pageMatchesLength(){return this._pageMatchesLength}get selected(){return this._selected}get state(){return this.#re}setDocument(e){this._pdfDocument&&this.#Y(),e&&(this._pdfDocument=e,this._firstPageCapability.resolve())}#le(e){if(!e)return;!1===e.phraseSearch&&(console.error("The `phraseSearch`-parameter was removed, please provide an Array of strings in the `query`-parameter instead."),"string"===typeof e.query&&(e.query=e.query.match(/\S+/g)));const t=this._pdfDocument,{type:i}=e;(null===this.#re||this.#he(e))&&(this._dirtyMatch=!0),this.#re=e,"highlightallchange"!==i&&this.#h(o.PENDING),this._firstPageCapability.promise.then((()=>{if(!this._pdfDocument||t&&this._pdfDocument!==t)return;this.#ce();const e=!this._highlightMatches,n=!!this._findTimeout;this._findTimeout&&(clearTimeout(this._findTimeout),this._findTimeout=null),i?this._dirtyMatch?this.#ue():"again"===i?(this.#ue(),e&&this.#re.highlightAll&&this.#pe()):"highlightallchange"===i?(n?this.#ue():this._highlightMatches=!0,this.#pe()):this.#ue():this._findTimeout=setTimeout((()=>{this.#ue(),this._findTimeout=null}),a)}))}scrollMatchIntoView(e){let{element:t=null,selectedLeft:i=0,pageIndex:s=-1,matchIndex:r=-1}=e;if(!this._scrollMatches||!t)return;if(-1===r||r!==this._selected.matchIdx)return;if(-1===s||s!==this._selected.pageIdx)return;this._scrollMatches=!1;const o={top:l,left:i+d};(0,n.scrollIntoView)(t,o,!0)}#Y(){this._highlightMatches=!1,this._scrollMatches=!1,this._pdfDocument=null,this._pageMatches=[],this._pageMatchesLength=[],this.#ae=0,this.#re=null,this._selected={pageIdx:-1,matchIdx:-1},this._offset={pageIdx:null,matchIdx:null,wrapped:!1},this._extractTextPromises=[],this._pageContents=[],this._pageDiffs=[],this._hasDiacritics=[],this._matchesCountTotal=0,this._pagesToSearch=null,this._pendingFindMatches=new Set,this._resumePageIdx=null,this._dirtyMatch=!1,clearTimeout(this._findTimeout),this._findTimeout=null,this._firstPageCapability=new r.PromiseCapability}get#ge(){const{query:e}=this.#re;return"string"===typeof e?(e!==this._rawQuery&&(this._rawQuery=e,[this._normalizedQuery]=E(e)),this._normalizedQuery):(e||[]).filter((e=>!!e)).map((e=>E(e)[0]))}#he(e){const t=e.query,i=this.#re.query,n=typeof t,s=typeof i;if(n!==s)return!0;if("string"===n){if(t!==i)return!0}else if(JSON.stringify(t)!==JSON.stringify(i))return!0;switch(e.type){case"again":const e=this._selected.pageIdx+1,t=this._linkService;return e>=1&&e<=t.pagesCount&&e!==t.page&&!(this.onIsPageVisible?.(e)??1);case"highlightallchange":return!1}return!0}#fe(e,t,i){let n=e.slice(0,t).match(f);if(n){const i=e.charCodeAt(t),r=n[1].charCodeAt(0);if((0,s.getCharacterType)(i)===(0,s.getCharacterType)(r))return!1}if(n=e.slice(t+i).match(m),n){const r=e.charCodeAt(t+i-1),o=n[1].charCodeAt(0);if((0,s.getCharacterType)(r)===(0,s.getCharacterType)(o))return!1}return!0}#me(e,t,i,n){const s=this._pageMatches[i]=[],r=this._pageMatchesLength[i]=[];if(!e)return;const o=this._pageDiffs[i];let a;while(null!==(a=e.exec(n))){if(t&&!this.#fe(n,a.index,a[0].length))continue;const[e,i]=S(o,a.index,a[0].length);i&&(s.push(e),r.push(i))}}#ve(e,t){const{matchDiacritics:i}=this.#re;let n=!1;e=e.replaceAll(g,((e,s,r,o,a,l)=>s?`[ ]*\\${s}[ ]*`:r?`[ ]*${r}[ ]*`:o?"[ ]+":i?a||l:a?c.has(a.charCodeAt(0))?a:"":t?(n=!0,`${l}\\p{M}*`):l));const s="[ ]*";return e.endsWith(s)&&(e=e.slice(0,e.length-s.length)),i&&t&&(u||=String.fromCharCode(...c),n=!0,e=`${e}(?=[${u}]|[^\\p{M}]|$)`),[n,e]}#we(e){let t=this.#ge;if(0===t.length)return;const{caseSensitive:i,entireWord:n}=this.#re,s=this._pageContents[e],r=this._hasDiacritics[e];let o=!1;"string"===typeof t?[o,t]=this.#ve(t,r):t=t.sort().reverse().map((e=>{const[t,i]=this.#ve(e,r);return o||=t,`(${i})`})).join("|");const a=`g${o?"u":""}${i?"":"i"}`;t=t?new RegExp(t,a):null,this.#me(t,n,e,s),this.#re.highlightAll&&this.#be(e),this._resumePageIdx===e&&(this._resumePageIdx=null,this.#_e());const l=this._pageMatches[e].length;this._matchesCountTotal+=l,this.#oe?l>0&&this.#ye():++this.#ae===this._linkService.pagesCount&&this.#ye()}#ce(){if(this._extractTextPromises.length>0)return;let e=Promise.resolve();const t={disableNormalization:!0};for(let i=0,n=this._linkService.pagesCount;i<n;i++){const n=new r.PromiseCapability;this._extractTextPromises[i]=n.promise,e=e.then((()=>this._pdfDocument.getPage(i+1).then((e=>e.getTextContent(t))).then((e=>{const t=[];for(const i of e.items)t.push(i.str),i.hasEOL&&t.push("\n");[this._pageContents[i],this._pageDiffs[i],this._hasDiacritics[i]]=E(t.join("")),n.resolve()}),(e=>{console.error(`Unable to get text content for page ${i+1}`,e),this._pageContents[i]="",this._pageDiffs[i]=null,this._hasDiacritics[i]=!1,n.resolve()}))))}}#be(e){this._scrollMatches&&this._selected.pageIdx===e&&(this._linkService.page=e+1),this._eventBus.dispatch("updatetextlayermatches",{source:this,pageIndex:e})}#pe(){this._eventBus.dispatch("updatetextlayermatches",{source:this,pageIndex:-1})}#ue(){const e=this.#re.findPrevious,t=this._linkService.page-1,i=this._linkService.pagesCount;if(this._highlightMatches=!0,this._dirtyMatch){this._dirtyMatch=!1,this._selected.pageIdx=this._selected.matchIdx=-1,this._offset.pageIdx=t,this._offset.matchIdx=null,this._offset.wrapped=!1,this._resumePageIdx=null,this._pageMatches.length=0,this._pageMatchesLength.length=0,this.#ae=0,this._matchesCountTotal=0,this.#pe();for(let e=0;e<i;e++)this._pendingFindMatches.has(e)||(this._pendingFindMatches.add(e),this._extractTextPromises[e].then((()=>{this._pendingFindMatches.delete(e),this.#we(e)})))}const n=this.#ge;if(0===n.length)return void this.#h(o.FOUND);if(this._resumePageIdx)return;const s=this._offset;if(this._pagesToSearch=i,null!==s.matchIdx){const t=this._pageMatches[s.pageIdx].length;if(!e&&s.matchIdx+1<t||e&&s.matchIdx>0)return s.matchIdx=e?s.matchIdx-1:s.matchIdx+1,void this.#Pe(!0);this.#Ee(e)}this.#_e()}#Se(e){const t=this._offset,i=e.length,n=this.#re.findPrevious;return i?(t.matchIdx=n?i-1:0,this.#Pe(!0),!0):(this.#Ee(n),!!(t.wrapped&&(t.matchIdx=null,this._pagesToSearch<0))&&(this.#Pe(!1),!0))}#_e(){null!==this._resumePageIdx&&console.error("There can only be one pending page.");let e=null;do{const t=this._offset.pageIdx;if(e=this._pageMatches[t],!e){this._resumePageIdx=t;break}}while(!this.#Se(e))}#Ee(e){const t=this._offset,i=this._linkService.pagesCount;t.pageIdx=e?t.pageIdx-1:t.pageIdx+1,t.matchIdx=null,this._pagesToSearch--,(t.pageIdx>=i||t.pageIdx<0)&&(t.pageIdx=e?i-1:0,t.wrapped=!0)}#Pe(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=o.NOT_FOUND;const i=this._offset.wrapped;if(this._offset.wrapped=!1,e){const e=this._selected.pageIdx;this._selected.pageIdx=this._offset.pageIdx,this._selected.matchIdx=this._offset.matchIdx,t=i?o.WRAPPED:o.FOUND,-1!==e&&e!==this._selected.pageIdx&&this.#be(e)}this.#h(t,this.#re.findPrevious),-1!==this._selected.pageIdx&&(this._scrollMatches=!0,this.#be(this._selected.pageIdx))}#de(e){const t=this._pdfDocument;this._firstPageCapability.promise.then((()=>{!this._pdfDocument||t&&this._pdfDocument!==t||(this._findTimeout&&(clearTimeout(this._findTimeout),this._findTimeout=null),this._resumePageIdx&&(this._resumePageIdx=null,this._dirtyMatch=!0),this.#h(o.FOUND),this._highlightMatches=!1,this.#pe())}))}#Ce(){const{pageIdx:e,matchIdx:t}=this._selected;let i=0,n=this._matchesCountTotal;if(-1!==t){for(let t=0;t<e;t++)i+=this._pageMatches[t]?.length||0;i+=t+1}return(i<1||i>n)&&(i=n=0),{current:i,total:n}}#ye(){this._eventBus.dispatch("updatefindmatchescount",{source:this,matchesCount:this.#Ce()})}#h(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];(this.#oe||this.#ae===this._linkService.pagesCount&&e!==o.PENDING)&&this._eventBus.dispatch("updatefindcontrolstate",{source:this,state:e,previous:t,matchesCount:this.#Ce(),rawQuery:this.#re?.query??null})}}t.PDFFindController=C},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CharacterType=void 0,t.getCharacterType=p,t.getNormalizeWithNFKC=f;const i={SPACE:0,ALPHA_LETTER:1,PUNCT:2,HAN_LETTER:3,KATAKANA_LETTER:4,HIRAGANA_LETTER:5,HALFWIDTH_KATAKANA_LETTER:6,THAI_LETTER:7};function n(e){return e<11904}function s(e){return 0===(65408&e)}function r(e){return e>=97&&e<=122||e>=65&&e<=90}function o(e){return e>=48&&e<=57}function a(e){return 32===e||9===e||13===e||10===e}function l(e){return e>=13312&&e<=40959||e>=63744&&e<=64255}function d(e){return e>=12448&&e<=12543}function h(e){return e>=12352&&e<=12447}function c(e){return e>=65376&&e<=65439}function u(e){return 3584===(65408&e)}function p(e){return n(e)?s(e)?a(e)?i.SPACE:r(e)||o(e)||95===e?i.ALPHA_LETTER:i.PUNCT:u(e)?i.THAI_LETTER:160===e?i.SPACE:i.ALPHA_LETTER:l(e)?i.HAN_LETTER:d(e)?i.KATAKANA_LETTER:h(e)?i.HIRAGANA_LETTER:c(e)?i.HALFWIDTH_KATAKANA_LETTER:i.ALPHA_LETTER}let g;function f(){return g||=" ¨ª¯²-µ¸-º¼-¾Ĳ-ĳĿ-ŀŉſǄ-ǌǱ-ǳʰ-ʸ˘-˝ˠ-ˤʹͺ;΄-΅·ϐ-ϖϰ-ϲϴ-ϵϹևٵ-ٸक़-य़ড়-ঢ়য়ਲ਼ਸ਼ਖ਼-ਜ਼ਫ਼ଡ଼-ଢ଼ำຳໜ-ໝ༌གྷཌྷདྷབྷཛྷཀྵჼᴬ-ᴮᴰ-ᴺᴼ-ᵍᵏ-ᵪᵸᶛ-ᶿẚ-ẛάέήίόύώΆ᾽-῁ΈΉ῍-῏ΐΊ῝-῟ΰΎ῭-`ΌΏ´-῾ - ‑‗․-… ″-‴‶-‷‼‾⁇-⁉⁗ ⁰-ⁱ⁴-₎ₐ-ₜ₨℀-℃℅-ℇ℉-ℓℕ-№ℙ-ℝ℠-™ℤΩℨK-ℭℯ-ℱℳ-ℹ℻-⅀ⅅ-ⅉ⅐-ⅿ↉∬-∭∯-∰〈-〉①-⓪⨌⩴-⩶⫝̸ⱼ-ⱽⵯ⺟⻳⼀-⿕　〶〸-〺゛-゜ゟヿㄱ-ㆎ㆒-㆟㈀-㈞㈠-㉇㉐-㉾㊀-㏿ꚜ-ꚝꝰꟲ-ꟴꟸ-ꟹꭜ-ꭟꭩ豈-嗀塚晴凞-羽蘒諸逸-都飯-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-זּטּ-לּמּנּ-סּףּ-פּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-﷼︐-︙︰-﹄﹇-﹒﹔-﹦﹨-﹫ﹰ-ﹲﹴﹶ-ﻼ！-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ￠-￦",g}t.CharacterType=i},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFHistory=void 0,t.isDestArraysEqual=c,t.isDestHashesEqual=h;var n=i(97),s=i(124);const r=1e3,o=50,a=1e3;function l(){return document.location.hash}class d{constructor(e){let{linkService:t,eventBus:i}=e;this.linkService=t,this.eventBus=i,this._initialized=!1,this._fingerprint="",this.reset(),this._boundEvents=null,this.eventBus._on("pagesinit",(()=>{this._isPagesLoaded=!1,this.eventBus._on("pagesloaded",(e=>{this._isPagesLoaded=!!e.pagesCount}),{once:!0})}))}initialize(e){let{fingerprint:t,resetHistory:i=!1,updateUrl:n=!1}=e;if(!t||"string"!==typeof t)return void console.error('PDFHistory.initialize: The "fingerprint" must be a non-empty string.');this._initialized&&this.reset();const s=""!==this._fingerprint&&this._fingerprint!==t;this._fingerprint=t,this._updateUrl=!0===n,this._initialized=!0,this._bindEvents();const r=window.history.state;if(this._popStateInProgress=!1,this._blockHashChange=0,this._currentHash=l(),this._numPositionUpdates=0,this._uid=this._maxUid=0,this._destination=null,this._position=null,!this._isValidState(r,!0)||i){const{hash:e,page:t,rotation:n}=this._parseCurrentHash(!0);return!e||s||i?void this._pushOrReplaceState(null,!0):void this._pushOrReplaceState({hash:e,page:t,rotation:n},!0)}const o=r.destination;this._updateInternalState(o,r.uid,!0),void 0!==o.rotation&&(this._initialRotation=o.rotation),o.dest?(this._initialBookmark=JSON.stringify(o.dest),this._destination.page=null):o.hash?this._initialBookmark=o.hash:o.page&&(this._initialBookmark=`page=${o.page}`)}reset(){this._initialized&&(this._pageHide(),this._initialized=!1,this._unbindEvents()),this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),this._initialBookmark=null,this._initialRotation=null}push(e){let{namedDest:t=null,explicitDest:i,pageNumber:n}=e;if(!this._initialized)return;if(t&&"string"!==typeof t)return void console.error(`PDFHistory.push: "${t}" is not a valid namedDest parameter.`);if(!Array.isArray(i))return void console.error(`PDFHistory.push: "${i}" is not a valid explicitDest parameter.`);if(!this._isValidPage(n)&&(null!==n||this._destination))return void console.error(`PDFHistory.push: "${n}" is not a valid pageNumber parameter.`);const s=t||JSON.stringify(i);if(!s)return;let r=!1;if(this._destination&&(h(this._destination.hash,s)||c(this._destination.dest,i))){if(this._destination.page)return;r=!0}this._popStateInProgress&&!r||(this._pushOrReplaceState({dest:i,hash:s,page:n,rotation:this.linkService.rotation},r),this._popStateInProgress||(this._popStateInProgress=!0,Promise.resolve().then((()=>{this._popStateInProgress=!1}))))}pushPage(e){this._initialized&&(this._isValidPage(e)?this._destination?.page!==e&&(this._popStateInProgress||(this._pushOrReplaceState({dest:null,hash:`page=${e}`,page:e,rotation:this.linkService.rotation}),this._popStateInProgress||(this._popStateInProgress=!0,Promise.resolve().then((()=>{this._popStateInProgress=!1}))))):console.error(`PDFHistory.pushPage: "${e}" is not a valid page number.`))}pushCurrentPosition(){this._initialized&&!this._popStateInProgress&&this._tryPushCurrentPosition()}back(){if(!this._initialized||this._popStateInProgress)return;const e=window.history.state;this._isValidState(e)&&e.uid>0&&window.history.back()}forward(){if(!this._initialized||this._popStateInProgress)return;const e=window.history.state;this._isValidState(e)&&e.uid<this._maxUid&&window.history.forward()}get popStateInProgress(){return this._initialized&&(this._popStateInProgress||this._blockHashChange>0)}get initialBookmark(){return this._initialized?this._initialBookmark:null}get initialRotation(){return this._initialized?this._initialRotation:null}_pushOrReplaceState(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=t||!this._destination,n={fingerprint:this._fingerprint,uid:i?this._uid:this._uid+1,destination:e};let s;if(this._updateInternalState(e,n.uid),this._updateUrl&&e?.hash){const t=document.location.href.split("#")[0];t.startsWith("file://")||(s=`${t}#${e.hash}`)}i?window.history.replaceState(n,"",s):window.history.pushState(n,"",s)}_tryPushCurrentPosition(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this._position)return;let t=this._position;if(e&&(t=Object.assign(Object.create(null),this._position),t.temporary=!0),!this._destination)return void this._pushOrReplaceState(t);if(this._destination.temporary)return void this._pushOrReplaceState(t,!0);if(this._destination.hash===t.hash)return;if(!this._destination.page&&(o<=0||this._numPositionUpdates<=o))return;let i=!1;if(this._destination.page>=t.first&&this._destination.page<=t.page){if(void 0!==this._destination.dest||!this._destination.first)return;i=!0}this._pushOrReplaceState(t,i)}_isValidPage(e){return Number.isInteger(e)&&e>0&&e<=this.linkService.pagesCount}_isValidState(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(!e)return!1;if(e.fingerprint!==this._fingerprint){if(!t)return!1;{if("string"!==typeof e.fingerprint||e.fingerprint.length!==this._fingerprint.length)return!1;const[t]=performance.getEntriesByType("navigation");if("reload"!==t?.type)return!1}}return!(!Number.isInteger(e.uid)||e.uid<0)&&(null!==e.destination&&"object"===typeof e.destination)}_updateInternalState(e,t){let i=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),i&&e?.temporary&&delete e.temporary,this._destination=e,this._uid=t,this._maxUid=Math.max(this._maxUid,t),this._numPositionUpdates=0}_parseCurrentHash(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const t=unescape(l()).substring(1),i=(0,n.parseQueryString)(t),s=i.get("nameddest")||"";let r=0|i.get("page");return(!this._isValidPage(r)||e&&s.length>0)&&(r=null),{hash:t,page:r,rotation:this.linkService.rotation}}_updateViewarea(e){let{location:t}=e;this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),this._position={hash:t.pdfOpenParams.substring(1),page:this.linkService.page,first:t.pageNumber,rotation:t.rotation},this._popStateInProgress||(o>0&&this._isPagesLoaded&&this._destination&&!this._destination.page&&this._numPositionUpdates++,a>0&&(this._updateViewareaTimeout=setTimeout((()=>{this._popStateInProgress||this._tryPushCurrentPosition(!0),this._updateViewareaTimeout=null}),a)))}_popState(e){let{state:t}=e;const i=l(),o=this._currentHash!==i;if(this._currentHash=i,!t){this._uid++;const{hash:e,page:t,rotation:i}=this._parseCurrentHash();return void this._pushOrReplaceState({hash:e,page:t,rotation:i},!0)}if(!this._isValidState(t))return;this._popStateInProgress=!0,o&&(this._blockHashChange++,(0,s.waitOnEventOrTimeout)({target:window,name:"hashchange",delay:r}).then((()=>{this._blockHashChange--})));const a=t.destination;this._updateInternalState(a,t.uid,!0),(0,n.isValidRotation)(a.rotation)&&(this.linkService.rotation=a.rotation),a.dest?this.linkService.goToDestination(a.dest):a.hash?this.linkService.setHash(a.hash):a.page&&(this.linkService.page=a.page),Promise.resolve().then((()=>{this._popStateInProgress=!1}))}_pageHide(){this._destination&&!this._destination.temporary||this._tryPushCurrentPosition()}_bindEvents(){this._boundEvents||(this._boundEvents={updateViewarea:this._updateViewarea.bind(this),popState:this._popState.bind(this),pageHide:this._pageHide.bind(this)},this.eventBus._on("updateviewarea",this._boundEvents.updateViewarea),window.addEventListener("popstate",this._boundEvents.popState),window.addEventListener("pagehide",this._boundEvents.pageHide))}_unbindEvents(){this._boundEvents&&(this.eventBus._off("updateviewarea",this._boundEvents.updateViewarea),window.removeEventListener("popstate",this._boundEvents.popState),window.removeEventListener("pagehide",this._boundEvents.pageHide),this._boundEvents=null)}}function h(e,t){if("string"!==typeof e||"string"!==typeof t)return!1;if(e===t)return!0;const i=(0,n.parseQueryString)(e).get("nameddest");return i===t}function c(e,t){function i(e,t){if(typeof e!==typeof t)return!1;if(Array.isArray(e)||Array.isArray(t))return!1;if(null!==e&&"object"===typeof e&&null!==t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(!i(e[n],t[n]))return!1;return!0}return e===t||Number.isNaN(e)&&Number.isNaN(t)}if(!Array.isArray(e)||!Array.isArray(t))return!1;if(e.length!==t.length)return!1;for(let n=0,s=e.length;n<s;n++)if(!i(e[n],t[n]))return!1;return!0}t.PDFHistory=d},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFLayerViewer=void 0,i(89);var n=i(131);class s extends n.BaseTreeViewer{constructor(e){super(e),this.l10n=e.l10n,this.eventBus._on("optionalcontentconfigchanged",(e=>{this.#Le(e.promise)})),this.eventBus._on("resetlayers",(()=>{this.#Le()})),this.eventBus._on("togglelayerstree",this._toggleAllTreeItems.bind(this))}reset(){super.reset(),this._optionalContentConfig=null,this._optionalContentHash=null}_dispatchEvent(e){this.eventBus.dispatch("layersloaded",{source:this,layersCount:e})}_bindLink(e,t){let{groupId:i,input:n}=t;const s=()=>{this._optionalContentConfig.setVisibility(i,n.checked),this._optionalContentHash=this._optionalContentConfig.getHash(),this.eventBus.dispatch("optionalcontentconfig",{source:this,promise:Promise.resolve(this._optionalContentConfig)})};e.onclick=t=>t.target===n?(s(),!0):t.target!==e||(n.checked=!n.checked,s(),!1)}async _setNestedName(e,t){let{name:i=null}=t;"string"!==typeof i?(e.textContent=await this.l10n.get("additional_layers"),e.style.fontStyle="italic"):e.textContent=this._normalizeTextContent(i)}_addToggleButton(e,t){let{name:i=null}=t;super._addToggleButton(e,null===i)}_toggleAllTreeItems(){this._optionalContentConfig&&super._toggleAllTreeItems()}render(e){let{optionalContentConfig:t,pdfDocument:i}=e;this._optionalContentConfig&&this.reset(),this._optionalContentConfig=t||null,this._pdfDocument=i||null;const n=t?.getOrder();if(!n)return void this._dispatchEvent(0);this._optionalContentHash=t.getHash();const s=document.createDocumentFragment(),r=[{parent:s,groups:n}];let o=0,a=!1;while(r.length>0){const e=r.shift();for(const i of e.groups){const n=document.createElement("div");n.className="treeItem";const s=document.createElement("a");if(n.append(s),"object"===typeof i){a=!0,this._addToggleButton(n,i),this._setNestedName(s,i);const e=document.createElement("div");e.className="treeItems",n.append(e),r.push({parent:e,groups:i.order})}else{const e=t.getGroup(i),n=document.createElement("input");this._bindLink(s,{groupId:i,input:n}),n.type="checkbox",n.checked=e.visible;const r=document.createElement("label");r.textContent=this._normalizeTextContent(e.name),r.append(n),s.append(r),o++}e.parent.append(n)}}this._finishRendering(s,o,a)}async#Le(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(!this._optionalContentConfig)return;const t=this._pdfDocument,i=await(e||t.getOptionalContentConfig());if(t===this._pdfDocument){if(e){if(i.getHash()===this._optionalContentHash)return}else this.eventBus.dispatch("optionalcontentconfig",{source:this,promise:Promise.resolve(i)});this.render({optionalContentConfig:i,pdfDocument:this._pdfDocument})}}}t.PDFLayerViewer=s},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFOutlineViewer=void 0,i(89),i(76);var n=i(131),s=i(122),r=i(97);class o extends n.BaseTreeViewer{constructor(e){super(e),this.linkService=e.linkService,this.downloadManager=e.downloadManager,this.eventBus._on("toggleoutlinetree",this._toggleAllTreeItems.bind(this)),this.eventBus._on("currentoutlineitem",this._currentOutlineItem.bind(this)),this.eventBus._on("pagechanging",(e=>{this._currentPageNumber=e.pageNumber})),this.eventBus._on("pagesloaded",(e=>{this._isPagesLoaded=!!e.pagesCount,this._currentOutlineItemCapability&&!this._currentOutlineItemCapability.settled&&this._currentOutlineItemCapability.resolve(this._isPagesLoaded)})),this.eventBus._on("sidebarviewchanged",(e=>{this._sidebarView=e.view}))}reset(){super.reset(),this._outline=null,this._pageNumberToDestHashCapability=null,this._currentPageNumber=1,this._isPagesLoaded=null,this._currentOutlineItemCapability&&!this._currentOutlineItemCapability.settled&&this._currentOutlineItemCapability.resolve(!1),this._currentOutlineItemCapability=null}_dispatchEvent(e){this._currentOutlineItemCapability=new s.PromiseCapability,0===e||this._pdfDocument?.loadingParams.disableAutoFetch?this._currentOutlineItemCapability.resolve(!1):null!==this._isPagesLoaded&&this._currentOutlineItemCapability.resolve(this._isPagesLoaded),this.eventBus.dispatch("outlineloaded",{source:this,outlineCount:e,currentOutlineItemPromise:this._currentOutlineItemCapability.promise})}_bindLink(e,t){let{url:i,newWindow:n,action:s,attachment:r,dest:o,setOCGState:a}=t;const{linkService:l}=this;if(i)l.addLinkAttributes(e,i,n);else{if(s)return e.href=l.getAnchorUrl(""),void(e.onclick=()=>(l.executeNamedAction(s),!1));if(r)return e.href=l.getAnchorUrl(""),void(e.onclick=()=>(this.downloadManager.openOrDownloadData(e,r.content,r.filename),!1));if(a)return e.href=l.getAnchorUrl(""),void(e.onclick=()=>(l.executeSetOCGState(a),!1));e.href=l.getDestinationHash(o),e.onclick=e=>(this._updateCurrentTreeItem(e.target.parentNode),o&&l.goToDestination(o),!1)}}_setStyles(e,t){let{bold:i,italic:n}=t;i&&(e.style.fontWeight="bold"),n&&(e.style.fontStyle="italic")}_addToggleButton(e,t){let{count:i,items:n}=t,s=!1;if(i<0){let e=n.length;if(e>0){const t=[...n];while(t.length>0){const{count:i,items:n}=t.shift();i>0&&n.length>0&&(e+=n.length,t.push(...n))}}Math.abs(i)===e&&(s=!0)}super._addToggleButton(e,s)}_toggleAllTreeItems(){this._outline&&super._toggleAllTreeItems()}render(e){let{outline:t,pdfDocument:i}=e;if(this._outline&&this.reset(),this._outline=t||null,this._pdfDocument=i||null,!t)return void this._dispatchEvent(0);const n=document.createDocumentFragment(),s=[{parent:n,items:t}];let r=0,o=!1;while(s.length>0){const e=s.shift();for(const t of e.items){const i=document.createElement("div");i.className="treeItem";const n=document.createElement("a");if(this._bindLink(n,t),this._setStyles(n,t),n.textContent=this._normalizeTextContent(t.title),i.append(n),t.items.length>0){o=!0,this._addToggleButton(i,t);const e=document.createElement("div");e.className="treeItems",i.append(e),s.push({parent:e,items:t.items})}e.parent.append(i),r++}}this._finishRendering(n,r,o)}async _currentOutlineItem(){if(!this._isPagesLoaded)throw new Error("_currentOutlineItem: All pages have not been loaded.");if(!this._outline||!this._pdfDocument)return;const e=await this._getPageNumberToDestHash(this._pdfDocument);if(e&&(this._updateCurrentTreeItem(null),this._sidebarView===r.SidebarView.OUTLINE))for(let t=this._currentPageNumber;t>0;t--){const i=e.get(t);if(!i)continue;const n=this.container.querySelector(`a[href="${i}"]`);if(n){this._scrollToCurrentTreeItem(n.parentNode);break}}}async _getPageNumberToDestHash(e){if(this._pageNumberToDestHashCapability)return this._pageNumberToDestHashCapability.promise;this._pageNumberToDestHashCapability=new s.PromiseCapability;const t=new Map,i=new Map,n=[{nesting:0,items:this._outline}];while(n.length>0){const s=n.shift(),r=s.nesting;for(const{dest:o,items:a}of s.items){let s,l;if("string"===typeof o){if(s=await e.getDestination(o),e!==this._pdfDocument)return null}else s=o;if(Array.isArray(s)){const[n]=s;if("object"===typeof n&&null!==n){if(l=this.linkService._cachedPageNumber(n),!l)try{if(l=await e.getPageIndex(n)+1,e!==this._pdfDocument)return null;this.linkService.cachePageRef(l,n)}catch{}}else Number.isInteger(n)&&(l=n+1);if(Number.isInteger(l)&&(!t.has(l)||r>i.get(l))){const e=this.linkService.getDestinationHash(o);t.set(l,e),i.set(l,r)}}a.length>0&&n.push({nesting:r+1,items:a})}}return this._pageNumberToDestHashCapability.resolve(t.size>0?t:null),this._pageNumberToDestHashCapability.promise}}t.PDFOutlineViewer=o},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPresentationMode=void 0;var n=i(97),s=i(122);const r=3e3,o="pdfPresentationMode",a="pdfPresentationModeControls",l=50,d=.1,h=50,c=Math.PI/6;class u{#re=n.PresentationModeState.UNKNOWN;#Te=null;constructor(e){let{container:t,pdfViewer:i,eventBus:n}=e;this.container=t,this.pdfViewer=i,this.eventBus=n,this.contextMenuOpen=!1,this.mouseScrollTimeStamp=0,this.mouseScrollDelta=0,this.touchSwipeState=null}async request(){const{container:e,pdfViewer:t}=this;if(this.active||!t.pagesCount||!e.requestFullscreen)return!1;this.#Me(),this.#Ie(n.PresentationModeState.CHANGING);const i=e.requestFullscreen();this.#Te={pageNumber:t.currentPageNumber,scaleValue:t.currentScaleValue,scrollMode:t.scrollMode,spreadMode:null,annotationEditorMode:null},t.spreadMode===n.SpreadMode.NONE||t.pageViewsReady&&t.hasEqualPageSizes||(console.warn("Ignoring Spread modes when entering PresentationMode, since the document may contain varying page sizes."),this.#Te.spreadMode=t.spreadMode),t.annotationEditorMode!==s.AnnotationEditorType.DISABLE&&(this.#Te.annotationEditorMode=t.annotationEditorMode);try{return await i,t.focus(),!0}catch{this.#xe(),this.#Ie(n.PresentationModeState.NORMAL)}return!1}get active(){return this.#re===n.PresentationModeState.CHANGING||this.#re===n.PresentationModeState.FULLSCREEN}#Ae(e){if(!this.active)return;e.preventDefault();const t=(0,n.normalizeWheelEventDelta)(e),i=Date.now(),s=this.mouseScrollTimeStamp;if(!(i>s&&i-s<l)&&((this.mouseScrollDelta>0&&t<0||this.mouseScrollDelta<0&&t>0)&&this.#De(),this.mouseScrollDelta+=t,Math.abs(this.mouseScrollDelta)>=d)){const e=this.mouseScrollDelta;this.#De();const t=e>0?this.pdfViewer.previousPage():this.pdfViewer.nextPage();t&&(this.mouseScrollTimeStamp=i)}}#Ie(e){this.#re=e,this.eventBus.dispatch("presentationmodechanged",{source:this,state:e})}#Ne(){this.#Ie(n.PresentationModeState.FULLSCREEN),this.container.classList.add(o),setTimeout((()=>{this.pdfViewer.scrollMode=n.ScrollMode.PAGE,null!==this.#Te.spreadMode&&(this.pdfViewer.spreadMode=n.SpreadMode.NONE),this.pdfViewer.currentPageNumber=this.#Te.pageNumber,this.pdfViewer.currentScaleValue="page-fit",null!==this.#Te.annotationEditorMode&&(this.pdfViewer.annotationEditorMode={mode:s.AnnotationEditorType.NONE})}),0),this.#ke(),this.#Be(),this.contextMenuOpen=!1,window.getSelection().removeAllRanges()}#Oe(){const e=this.pdfViewer.currentPageNumber;this.container.classList.remove(o),setTimeout((()=>{this.#xe(),this.#Ie(n.PresentationModeState.NORMAL),this.pdfViewer.scrollMode=this.#Te.scrollMode,null!==this.#Te.spreadMode&&(this.pdfViewer.spreadMode=this.#Te.spreadMode),this.pdfViewer.currentScaleValue=this.#Te.scaleValue,this.pdfViewer.currentPageNumber=e,null!==this.#Te.annotationEditorMode&&(this.pdfViewer.annotationEditorMode={mode:this.#Te.annotationEditorMode}),this.#Te=null}),0),this.#Ve(),this.#Re(),this.#De(),this.contextMenuOpen=!1}#Fe(e){if(this.contextMenuOpen)return this.contextMenuOpen=!1,void e.preventDefault();0===e.button&&(e.target.href&&e.target.parentNode?.hasAttribute("data-internal-link")||(e.preventDefault(),e.shiftKey?this.pdfViewer.previousPage():this.pdfViewer.nextPage()))}#Ue(){this.contextMenuOpen=!0}#Be(){this.controlsTimeout?clearTimeout(this.controlsTimeout):this.container.classList.add(a),this.controlsTimeout=setTimeout((()=>{this.container.classList.remove(a),delete this.controlsTimeout}),r)}#Re(){this.controlsTimeout&&(clearTimeout(this.controlsTimeout),this.container.classList.remove(a),delete this.controlsTimeout)}#De(){this.mouseScrollTimeStamp=0,this.mouseScrollDelta=0}#ze(e){if(this.active)if(e.touches.length>1)this.touchSwipeState=null;else switch(e.type){case"touchstart":this.touchSwipeState={startX:e.touches[0].pageX,startY:e.touches[0].pageY,endX:e.touches[0].pageX,endY:e.touches[0].pageY};break;case"touchmove":if(null===this.touchSwipeState)return;this.touchSwipeState.endX=e.touches[0].pageX,this.touchSwipeState.endY=e.touches[0].pageY,e.preventDefault();break;case"touchend":if(null===this.touchSwipeState)return;let t=0;const i=this.touchSwipeState.endX-this.touchSwipeState.startX,n=this.touchSwipeState.endY-this.touchSwipeState.startY,s=Math.abs(Math.atan2(n,i));Math.abs(i)>h&&(s<=c||s>=Math.PI-c)?t=i:Math.abs(n)>h&&Math.abs(s-Math.PI/2)<=c&&(t=n),t>0?this.pdfViewer.previousPage():t<0&&this.pdfViewer.nextPage();break}}#ke(){this.showControlsBind=this.#Be.bind(this),this.mouseDownBind=this.#Fe.bind(this),this.mouseWheelBind=this.#Ae.bind(this),this.resetMouseScrollStateBind=this.#De.bind(this),this.contextMenuBind=this.#Ue.bind(this),this.touchSwipeBind=this.#ze.bind(this),window.addEventListener("mousemove",this.showControlsBind),window.addEventListener("mousedown",this.mouseDownBind),window.addEventListener("wheel",this.mouseWheelBind,{passive:!1}),window.addEventListener("keydown",this.resetMouseScrollStateBind),window.addEventListener("contextmenu",this.contextMenuBind),window.addEventListener("touchstart",this.touchSwipeBind),window.addEventListener("touchmove",this.touchSwipeBind),window.addEventListener("touchend",this.touchSwipeBind)}#Ve(){window.removeEventListener("mousemove",this.showControlsBind),window.removeEventListener("mousedown",this.mouseDownBind),window.removeEventListener("wheel",this.mouseWheelBind,{passive:!1}),window.removeEventListener("keydown",this.resetMouseScrollStateBind),window.removeEventListener("contextmenu",this.contextMenuBind),window.removeEventListener("touchstart",this.touchSwipeBind),window.removeEventListener("touchmove",this.touchSwipeBind),window.removeEventListener("touchend",this.touchSwipeBind),delete this.showControlsBind,delete this.mouseDownBind,delete this.mouseWheelBind,delete this.resetMouseScrollStateBind,delete this.contextMenuBind,delete this.touchSwipeBind}#He(){document.fullscreenElement?this.#Ne():this.#Oe()}#Me(){this.fullscreenChangeBind=this.#He.bind(this),window.addEventListener("fullscreenchange",this.fullscreenChangeBind)}#xe(){window.removeEventListener("fullscreenchange",this.fullscreenChangeBind),delete this.fullscreenChangeBind}}t.PDFPresentationMode=u},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFRenderingQueue=void 0;var n=i(122),s=i(97);const r=3e4;class o{constructor(){this.pdfViewer=null,this.pdfThumbnailViewer=null,this.onIdle=null,this.highestPriorityPage=null,this.idleTimeout=null,this.printing=!1,this.isThumbnailViewEnabled=!1,Object.defineProperty(this,"hasViewer",{value:()=>!!this.pdfViewer})}setViewer(e){this.pdfViewer=e}setThumbnailViewer(e){this.pdfThumbnailViewer=e}isHighestPriority(e){return this.highestPriorityPage===e.renderingId}renderHighestPriority(e){this.idleTimeout&&(clearTimeout(this.idleTimeout),this.idleTimeout=null),this.pdfViewer.forceRendering(e)||this.isThumbnailViewEnabled&&this.pdfThumbnailViewer?.forceRendering()||this.printing||this.onIdle&&(this.idleTimeout=setTimeout(this.onIdle.bind(this),r))}getHighestPriority(e,t,i){let n=arguments.length>3&&void 0!==arguments[3]&&arguments[3];const s=e.views,r=s.length;if(0===r)return null;for(let h=0;h<r;h++){const e=s[h].view;if(!this.isViewFinished(e))return e}const o=e.first.id,a=e.last.id;if(a-o+1>r){const n=e.ids;for(let e=1,s=a-o;e<s;e++){const s=i?o+e:a-e;if(n.has(s))continue;const r=t[s-1];if(!this.isViewFinished(r))return r}}let l=i?a:o-2,d=t[l];return d&&!this.isViewFinished(d)||n&&(l+=i?1:-1,d=t[l],d&&!this.isViewFinished(d))?d:null}isViewFinished(e){return e.renderingState===s.RenderingStates.FINISHED}renderView(e){switch(e.renderingState){case s.RenderingStates.FINISHED:return!1;case s.RenderingStates.PAUSED:this.highestPriorityPage=e.renderingId,e.resume();break;case s.RenderingStates.RUNNING:this.highestPriorityPage=e.renderingId;break;case s.RenderingStates.INITIAL:this.highestPriorityPage=e.renderingId,e.draw().finally((()=>{this.renderHighestPriority()})).catch((e=>{e instanceof n.RenderingCancelledException||console.error(`renderView: "${e}"`)}));break}return!0}}t.PDFRenderingQueue=o},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFScriptingManager=void 0,i(98),i(109),i(111),i(114),i(116),i(118),i(120),i(76);var n=i(97),s=i(122);class r{#We=null;#je=null;#$e=null;#w=null;#Ge=null;#Xe=null;#Ke=null;#Ze=!1;#Qe=null;#qe=null;#Ye=null;constructor(e){let{eventBus:t,sandboxBundleSrc:i=null,externalServices:n=null,docProperties:s=null}=e;this.#w=t,this.#Qe=i,this.#Ge=n,this.#$e=s}setViewer(e){this.#Ke=e}async setDocument(e){if(this.#Xe&&await this.#Je(),this.#Xe=e,!e)return;const[t,i,n]=await Promise.all([e.getFieldObjects(),e.getCalculationOrderIds(),e.getJSActions()]);if(t||n){if(e===this.#Xe){try{this.#qe=this.#et()}catch(s){return console.error(`setDocument: "${s.message}".`),void await this.#Je()}this._internalEvents.set("updatefromsandbox",(e=>{e?.source===window&&this.#tt(e.detail)})),this._internalEvents.set("dispatcheventinsandbox",(e=>{this.#qe?.dispatchEventInSandbox(e.detail)})),this._internalEvents.set("pagechanging",(e=>{let{pageNumber:t,previous:i}=e;t!==i&&(this.#it(i),this.#nt(t))})),this._internalEvents.set("pagerendered",(e=>{let{pageNumber:t}=e;this._pageOpenPending.has(t)&&t===this.#Ke.currentPageNumber&&this.#nt(t)})),this._internalEvents.set("pagesdestroy",(async()=>{await this.#it(this.#Ke.currentPageNumber),await(this.#qe?.dispatchEventInSandbox({id:"doc",name:"WillClose"})),this.#We?.resolve()}));for(const[e,t]of this._internalEvents)this.#w._on(e,t);try{const s=await this.#$e(e);if(e!==this.#Xe)return;await this.#qe.createSandbox({objects:t,calculationOrder:i,appInfo:{platform:navigator.platform,language:navigator.language},docInfo:{...s,actions:n}}),this.#w.dispatch("sandboxcreated",{source:this})}catch(s){return console.error(`setDocument: "${s.message}".`),void await this.#Je()}await(this.#qe?.dispatchEventInSandbox({id:"doc",name:"Open"})),await this.#nt(this.#Ke.currentPageNumber,!0),Promise.resolve().then((()=>{e===this.#Xe&&(this.#Ze=!0)}))}}else await this.#Je()}async dispatchWillSave(){return this.#qe?.dispatchEventInSandbox({id:"doc",name:"WillSave"})}async dispatchDidSave(){return this.#qe?.dispatchEventInSandbox({id:"doc",name:"DidSave"})}async dispatchWillPrint(){if(this.#qe){await(this.#Ye?.promise),this.#Ye=new s.PromiseCapability;try{await this.#qe.dispatchEventInSandbox({id:"doc",name:"WillPrint"})}catch(e){throw this.#Ye.resolve(),this.#Ye=null,e}await this.#Ye.promise}}async dispatchDidPrint(){return this.#qe?.dispatchEventInSandbox({id:"doc",name:"DidPrint"})}get destroyPromise(){return this.#je?.promise||null}get ready(){return this.#Ze}get _internalEvents(){return(0,s.shadow)(this,"_internalEvents",new Map)}get _pageOpenPending(){return(0,s.shadow)(this,"_pageOpenPending",new Set)}get _visitedPages(){return(0,s.shadow)(this,"_visitedPages",new Map)}async#tt(e){const t=this.#Ke,i=t.isInPresentationMode||t.isChangingPresentationMode,{id:s,siblings:r,command:o,value:a}=e;if(!s){switch(o){case"clear":console.clear();break;case"error":console.error(a);break;case"layout":if(!i){const e=(0,n.apiPageLayoutToViewerModes)(a);t.spreadMode=e.spreadMode}break;case"page-num":t.currentPageNumber=a+1;break;case"print":await t.pagesPromise,this.#w.dispatch("print",{source:this});break;case"println":console.log(a);break;case"zoom":i||(t.currentScaleValue=a);break;case"SaveAs":this.#w.dispatch("download",{source:this});break;case"FirstPage":t.currentPageNumber=1;break;case"LastPage":t.currentPageNumber=t.pagesCount;break;case"NextPage":t.nextPage();break;case"PrevPage":t.previousPage();break;case"ZoomViewIn":i||t.increaseScale();break;case"ZoomViewOut":i||t.decreaseScale();break;case"WillPrintFinished":this.#Ye?.resolve(),this.#Ye=null;break}return}if(i&&e.focus)return;delete e.id,delete e.siblings;const l=r?[s,...r]:[s];for(const n of l){const t=document.querySelector(`[data-element-id="${n}"]`);t?t.dispatchEvent(new CustomEvent("updatefromsandbox",{detail:e})):this.#Xe?.annotationStorage.setValue(n,e)}}async#nt(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=this.#Xe,r=this._visitedPages;if(t&&(this.#We=new s.PromiseCapability),!this.#We)return;const o=this.#Ke.getPageView(e-1);if(o?.renderingState!==n.RenderingStates.FINISHED)return void this._pageOpenPending.add(e);this._pageOpenPending.delete(e);const a=(async()=>{const t=await(r.has(e)?null:o.pdfPage?.getJSActions());i===this.#Xe&&await(this.#qe?.dispatchEventInSandbox({id:"page",name:"PageOpen",pageNumber:e,actions:t}))})();r.set(e,a)}async#it(e){const t=this.#Xe,i=this._visitedPages;if(!this.#We)return;if(this._pageOpenPending.has(e))return;const n=i.get(e);n&&(i.set(e,null),await n,t===this.#Xe&&await(this.#qe?.dispatchEventInSandbox({id:"page",name:"PageClose",pageNumber:e})))}#et(){if(this.#je=new s.PromiseCapability,this.#qe)throw new Error("#initScripting: Scripting already exists.");return this.#Ge.createScripting({sandboxBundleSrc:this.#Qe})}async#Je(){if(!this.#qe)return this.#Xe=null,void this.#je?.resolve();this.#We&&(await Promise.race([this.#We.promise,new Promise((e=>{setTimeout(e,1e3)}))]).catch((()=>{})),this.#We=null),this.#Xe=null;try{await this.#qe.destroySandbox()}catch{}this.#Ye?.reject(new Error("Scripting destroyed.")),this.#Ye=null;for(const[e,t]of this._internalEvents)this.#w._off(e,t);this._internalEvents.clear(),this._pageOpenPending.clear(),this._visitedPages.clear(),this.#qe=null,this.#Ze=!1,this.#je?.resolve()}}t.PDFScriptingManager=r},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFSidebar=void 0;var n=i(97);const s="--sidebar-width",r=200,o="sidebarResizing",a="pdfSidebarNotification";class l{#st=!1;#rt=this.#ot.bind(this);#at=this.#lt.bind(this);#dt=null;#ht=null;constructor(e){let{elements:t,eventBus:i,l10n:s}=e;this.isOpen=!1,this.active=n.SidebarView.THUMBS,this.isInitialViewSet=!1,this.isInitialEventDispatched=!1,this.onToggled=null,this.onUpdateThumbnails=null,this.outerContainer=t.outerContainer,this.sidebarContainer=t.sidebarContainer,this.toggleButton=t.toggleButton,this.resizer=t.resizer,this.thumbnailButton=t.thumbnailButton,this.outlineButton=t.outlineButton,this.attachmentsButton=t.attachmentsButton,this.layersButton=t.layersButton,this.thumbnailView=t.thumbnailView,this.outlineView=t.outlineView,this.attachmentsView=t.attachmentsView,this.layersView=t.layersView,this._outlineOptionsContainer=t.outlineOptionsContainer,this._currentOutlineItemButton=t.currentOutlineItemButton,this.eventBus=i,this.l10n=s,s.getDirection().then((e=>{this.#st="rtl"===e})),this.#X()}reset(){this.isInitialViewSet=!1,this.isInitialEventDispatched=!1,this.#ct(!0),this.switchView(n.SidebarView.THUMBS),this.outlineButton.disabled=!1,this.attachmentsButton.disabled=!1,this.layersButton.disabled=!1,this._currentOutlineItemButton.disabled=!0}get visibleView(){return this.isOpen?this.active:n.SidebarView.NONE}setInitialView(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:n.SidebarView.NONE;this.isInitialViewSet||(this.isInitialViewSet=!0,e!==n.SidebarView.NONE&&e!==n.SidebarView.UNKNOWN?(this.switchView(e,!0),this.isInitialEventDispatched||this.#ut()):this.#ut())}switchView(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];const i=e!==this.active;let s=!1;switch(e){case n.SidebarView.NONE:return void(this.isOpen&&this.close());case n.SidebarView.THUMBS:this.isOpen&&i&&(s=!0);break;case n.SidebarView.OUTLINE:if(this.outlineButton.disabled)return;break;case n.SidebarView.ATTACHMENTS:if(this.attachmentsButton.disabled)return;break;case n.SidebarView.LAYERS:if(this.layersButton.disabled)return;break;default:return void console.error(`PDFSidebar.switchView: "${e}" is not a valid view.`)}this.active=e,(0,n.toggleCheckedBtn)(this.thumbnailButton,e===n.SidebarView.THUMBS,this.thumbnailView),(0,n.toggleCheckedBtn)(this.outlineButton,e===n.SidebarView.OUTLINE,this.outlineView),(0,n.toggleCheckedBtn)(this.attachmentsButton,e===n.SidebarView.ATTACHMENTS,this.attachmentsView),(0,n.toggleCheckedBtn)(this.layersButton,e===n.SidebarView.LAYERS,this.layersView),this._outlineOptionsContainer.classList.toggle("hidden",e!==n.SidebarView.OUTLINE),!t||this.isOpen?(s&&(this.onUpdateThumbnails(),this.onToggled()),i&&this.#ut()):this.open()}open(){this.isOpen||(this.isOpen=!0,(0,n.toggleExpandedBtn)(this.toggleButton,!0),this.outerContainer.classList.add("sidebarMoving","sidebarOpen"),this.active===n.SidebarView.THUMBS&&this.onUpdateThumbnails(),this.onToggled(),this.#ut(),this.#ct())}close(){this.isOpen&&(this.isOpen=!1,(0,n.toggleExpandedBtn)(this.toggleButton,!1),this.outerContainer.classList.add("sidebarMoving"),this.outerContainer.classList.remove("sidebarOpen"),this.onToggled(),this.#ut())}toggle(){this.isOpen?this.close():this.open()}#ut(){this.isInitialViewSet&&(this.isInitialEventDispatched||=!0),this.eventBus.dispatch("sidebarviewchanged",{source:this,view:this.visibleView})}#pt(){this.toggleButton.setAttribute("data-l10n-id","toggle_sidebar_notification2"),this.l10n.translate(this.toggleButton),this.isOpen||this.toggleButton.classList.add(a)}#ct(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];(this.isOpen||e)&&this.toggleButton.classList.remove(a),e&&(this.toggleButton.setAttribute("data-l10n-id","toggle_sidebar"),this.l10n.translate(this.toggleButton))}#X(){this.sidebarContainer.addEventListener("transitionend",(e=>{e.target===this.sidebarContainer&&this.outerContainer.classList.remove("sidebarMoving")})),this.toggleButton.addEventListener("click",(()=>{this.toggle()})),this.thumbnailButton.addEventListener("click",(()=>{this.switchView(n.SidebarView.THUMBS)})),this.outlineButton.addEventListener("click",(()=>{this.switchView(n.SidebarView.OUTLINE)})),this.outlineButton.addEventListener("dblclick",(()=>{this.eventBus.dispatch("toggleoutlinetree",{source:this})})),this.attachmentsButton.addEventListener("click",(()=>{this.switchView(n.SidebarView.ATTACHMENTS)})),this.layersButton.addEventListener("click",(()=>{this.switchView(n.SidebarView.LAYERS)})),this.layersButton.addEventListener("dblclick",(()=>{this.eventBus.dispatch("resetlayers",{source:this})})),this._currentOutlineItemButton.addEventListener("click",(()=>{this.eventBus.dispatch("currentoutlineitem",{source:this})}));const e=(e,t,i)=>{t.disabled=!e,e?this.#pt():this.active===i&&this.switchView(n.SidebarView.THUMBS)};this.eventBus._on("outlineloaded",(t=>{e(t.outlineCount,this.outlineButton,n.SidebarView.OUTLINE),t.currentOutlineItemPromise.then((e=>{this.isInitialViewSet&&(this._currentOutlineItemButton.disabled=!e)}))})),this.eventBus._on("attachmentsloaded",(t=>{e(t.attachmentsCount,this.attachmentsButton,n.SidebarView.ATTACHMENTS)})),this.eventBus._on("layersloaded",(t=>{e(t.layersCount,this.layersButton,n.SidebarView.LAYERS)})),this.eventBus._on("presentationmodechanged",(e=>{e.state===n.PresentationModeState.NORMAL&&this.visibleView===n.SidebarView.THUMBS&&this.onUpdateThumbnails()})),this.resizer.addEventListener("mousedown",(e=>{0===e.button&&(this.outerContainer.classList.add(o),window.addEventListener("mousemove",this.#rt),window.addEventListener("mouseup",this.#at))})),this.eventBus._on("resize",(e=>{if(e.source!==window)return;if(this.#dt=null,!this.#ht)return;if(!this.isOpen)return void this.#gt(this.#ht);this.outerContainer.classList.add(o);const t=this.#gt(this.#ht);Promise.resolve().then((()=>{this.outerContainer.classList.remove(o),t&&this.eventBus.dispatch("resize",{source:this})}))}))}get outerContainerWidth(){return this.#dt||=this.outerContainer.clientWidth}#gt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;const t=Math.floor(this.outerContainerWidth/2);return e>t&&(e=t),e<r&&(e=r),e!==this.#ht&&(this.#ht=e,n.docStyle.setProperty(s,`${e}px`),!0)}#ot(e){let t=e.clientX;this.#st&&(t=this.outerContainerWidth-t),this.#gt(t)}#lt(e){this.outerContainer.classList.remove(o),this.eventBus.dispatch("resize",{source:this}),window.removeEventListener("mousemove",this.#rt),window.removeEventListener("mouseup",this.#at)}}t.PDFSidebar=l},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFThumbnailViewer=void 0,i(76),i(89);var n=i(97),s=i(146);const r=-19,o="selected";class a{constructor(e){let{container:t,eventBus:i,linkService:s,renderingQueue:r,l10n:o,pageColors:a}=e;this.container=t,this.eventBus=i,this.linkService=s,this.renderingQueue=r,this.l10n=o,this.pageColors=a||null,this.scroll=(0,n.watchScroll)(this.container,this._scrollUpdated.bind(this)),this._resetView()}_scrollUpdated(){this.renderingQueue.renderHighestPriority()}getThumbnail(e){return this._thumbnails[e]}_getVisibleThumbs(){return(0,n.getVisibleElements)({scrollEl:this.container,views:this._thumbnails})}scrollThumbnailIntoView(e){if(!this.pdfDocument)return;const t=this._thumbnails[e-1];if(!t)return void console.error('scrollThumbnailIntoView: Invalid "pageNumber" parameter.');if(e!==this._currentPageNumber){const e=this._thumbnails[this._currentPageNumber-1];e.div.classList.remove(o),t.div.classList.add(o)}const{first:i,last:s,views:a}=this._getVisibleThumbs();if(a.length>0){let o=!1;if(e<=i.id||e>=s.id)o=!0;else for(const{id:t,percent:i}of a)if(t===e){o=i<100;break}o&&(0,n.scrollIntoView)(t.div,{top:r})}this._currentPageNumber=e}get pagesRotation(){return this._pagesRotation}set pagesRotation(e){if(!(0,n.isValidRotation)(e))throw new Error("Invalid thumbnails rotation angle.");if(!this.pdfDocument)return;if(this._pagesRotation===e)return;this._pagesRotation=e;const t={rotation:e};for(const i of this._thumbnails)i.update(t)}cleanup(){for(const e of this._thumbnails)e.renderingState!==n.RenderingStates.FINISHED&&e.reset();s.TempImageFactory.destroyCanvas()}_resetView(){this._thumbnails=[],this._currentPageNumber=1,this._pageLabels=null,this._pagesRotation=0,this.container.textContent=""}setDocument(e){if(this.pdfDocument&&(this._cancelRendering(),this._resetView()),this.pdfDocument=e,!e)return;const t=e.getPage(1),i=e.getOptionalContentConfig();t.then((t=>{const n=e.numPages,r=t.getViewport({scale:1});for(let e=1;e<=n;++e){const t=new s.PDFThumbnailView({container:this.container,eventBus:this.eventBus,id:e,defaultViewport:r.clone(),optionalContentConfigPromise:i,linkService:this.linkService,renderingQueue:this.renderingQueue,l10n:this.l10n,pageColors:this.pageColors});this._thumbnails.push(t)}this._thumbnails[0]?.setPdfPage(t);const a=this._thumbnails[this._currentPageNumber-1];a.div.classList.add(o)})).catch((e=>{console.error("Unable to initialize thumbnail viewer",e)}))}_cancelRendering(){for(const e of this._thumbnails)e.cancelRendering()}setPageLabels(e){if(this.pdfDocument){e?Array.isArray(e)&&this.pdfDocument.numPages===e.length?this._pageLabels=e:(this._pageLabels=null,console.error("PDFThumbnailViewer_setPageLabels: Invalid page labels.")):this._pageLabels=null;for(let e=0,t=this._thumbnails.length;e<t;e++)this._thumbnails[e].setPageLabel(this._pageLabels?.[e]??null)}}async#ft(e){if(e.pdfPage)return e.pdfPage;try{const t=await this.pdfDocument.getPage(e.id);return e.pdfPage||e.setPdfPage(t),t}catch(t){return console.error("Unable to get page for thumb view",t),null}}#mt(e){return 1===e.first?.id||e.last?.id!==this._thumbnails.length&&this.scroll.down}forceRendering(){const e=this._getVisibleThumbs(),t=this.#mt(e),i=this.renderingQueue.getHighestPriority(e,this._thumbnails,t);return!!i&&(this.#ft(i).then((()=>{this.renderingQueue.renderView(i)})),!0)}}t.PDFThumbnailViewer=a},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TempImageFactory=t.PDFThumbnailView=void 0,i(76);var n=i(97),s=i(122);const r=2,o=3,a=98;class l{static#vt=null;static getCanvas(e,t){const i=this.#vt||=document.createElement("canvas");i.width=e,i.height=t;const n=i.getContext("2d",{alpha:!1});return n.save(),n.fillStyle="rgb(255, 255, 255)",n.fillRect(0,0,e,t),n.restore(),[i,i.getContext("2d")]}static destroyCanvas(){const e=this.#vt;e&&(e.width=0,e.height=0),this.#vt=null}}t.TempImageFactory=l;class d{constructor(e){let{container:t,eventBus:i,id:s,defaultViewport:r,optionalContentConfigPromise:o,linkService:a,renderingQueue:l,l10n:d,pageColors:h}=e;this.id=s,this.renderingId="thumbnail"+s,this.pageLabel=null,this.pdfPage=null,this.rotation=0,this.viewport=r,this.pdfPageRotate=r.rotation,this._optionalContentConfigPromise=o||null,this.pageColors=h||null,this.eventBus=i,this.linkService=a,this.renderingQueue=l,this.renderTask=null,this.renderingState=n.RenderingStates.INITIAL,this.resume=null,this.l10n=d;const c=document.createElement("a");c.href=a.getAnchorUrl("#page="+s),this._thumbPageTitle.then((e=>{c.title=e})),c.onclick=function(){return a.goToPage(s),!1},this.anchor=c;const u=document.createElement("div");u.className="thumbnail",u.setAttribute("data-page-number",this.id),this.div=u,this.#wt();const p=document.createElement("div");p.className="thumbnailImage",this._placeholderImg=p,u.append(p),c.append(u),t.append(c)}#wt(){const{width:e,height:t}=this.viewport,i=e/t;this.canvasWidth=a,this.canvasHeight=this.canvasWidth/i|0,this.scale=this.canvasWidth/e;const{style:n}=this.div;n.setProperty("--thumbnail-width",`${this.canvasWidth}px`),n.setProperty("--thumbnail-height",`${this.canvasHeight}px`)}setPdfPage(e){this.pdfPage=e,this.pdfPageRotate=e.rotate;const t=(this.rotation+this.pdfPageRotate)%360;this.viewport=e.getViewport({scale:1,rotation:t}),this.reset()}reset(){this.cancelRendering(),this.renderingState=n.RenderingStates.INITIAL,this.div.removeAttribute("data-loaded"),this.image?.replaceWith(this._placeholderImg),this.#wt(),this.image&&(this.image.removeAttribute("src"),delete this.image)}update(e){let{rotation:t=null}=e;"number"===typeof t&&(this.rotation=t);const i=(this.rotation+this.pdfPageRotate)%360;this.viewport=this.viewport.clone({scale:1,rotation:i}),this.reset()}cancelRendering(){this.renderTask&&(this.renderTask.cancel(),this.renderTask=null),this.resume=null}_getPageDrawContext(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;const t=document.createElement("canvas"),i=t.getContext("2d",{alpha:!1}),s=new n.OutputScale;t.width=e*this.canvasWidth*s.sx|0,t.height=e*this.canvasHeight*s.sy|0;const r=s.scaled?[s.sx,0,0,s.sy,0,0]:null;return{ctx:i,canvas:t,transform:r}}_convertCanvasToImage(e){if(this.renderingState!==n.RenderingStates.FINISHED)throw new Error("_convertCanvasToImage: Rendering has not finished.");const t=this._reduceImage(e),i=document.createElement("img");i.className="thumbnailImage",this._thumbPageCanvas.then((e=>{i.setAttribute("aria-label",e)})),i.src=t.toDataURL(),this.image=i,this.div.setAttribute("data-loaded",!0),this._placeholderImg.replaceWith(i),t.width=0,t.height=0}async#bt(e,t){let i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null;if(e===this.renderTask&&(this.renderTask=null),!(i instanceof s.RenderingCancelledException)&&(this.renderingState=n.RenderingStates.FINISHED,this._convertCanvasToImage(t),i))throw i}async draw(){if(this.renderingState!==n.RenderingStates.INITIAL)return void console.error("Must be in new state before drawing");const{pdfPage:e}=this;if(!e)throw this.renderingState=n.RenderingStates.FINISHED,new Error("pdfPage is not loaded");this.renderingState=n.RenderingStates.RUNNING;const{ctx:t,canvas:i,transform:s}=this._getPageDrawContext(r),o=this.viewport.clone({scale:r*this.scale}),a=e=>{if(!this.renderingQueue.isHighestPriority(this))return this.renderingState=n.RenderingStates.PAUSED,void(this.resume=()=>{this.renderingState=n.RenderingStates.RUNNING,e()});e()},l={canvasContext:t,transform:s,viewport:o,optionalContentConfigPromise:this._optionalContentConfigPromise,pageColors:this.pageColors},d=this.renderTask=e.render(l);d.onContinue=a;const h=d.promise.then((()=>this.#bt(d,i)),(e=>this.#bt(d,i,e)));return h.finally((()=>{i.width=0,i.height=0,this.eventBus.dispatch("thumbnailrendered",{source:this,pageNumber:this.id,pdfPage:this.pdfPage})})),h}setImage(e){if(this.renderingState!==n.RenderingStates.INITIAL)return;const{thumbnailCanvas:t,pdfPage:i,scale:s}=e;t&&(this.pdfPage||this.setPdfPage(i),s<this.scale||(this.renderingState=n.RenderingStates.FINISHED,this._convertCanvasToImage(t)))}_reduceImage(e){const{ctx:t,canvas:i}=this._getPageDrawContext();if(e.width<=2*i.width)return t.drawImage(e,0,0,e.width,e.height,0,0,i.width,i.height),i;let n=i.width<<o,s=i.height<<o;const[r,a]=l.getCanvas(n,s);while(n>e.width||s>e.height)n>>=1,s>>=1;a.drawImage(e,0,0,e.width,e.height,0,0,n,s);while(n>2*i.width)a.drawImage(r,0,0,n,s,0,0,n>>1,s>>1),n>>=1,s>>=1;return t.drawImage(r,0,0,n,s,0,0,i.width,i.height),i}get _thumbPageTitle(){return this.l10n.get("thumb_page_title",{page:this.pageLabel??this.id})}get _thumbPageCanvas(){return this.l10n.get("thumb_page_canvas",{page:this.pageLabel??this.id})}setPageLabel(e){this.pageLabel="string"===typeof e?e:null,this._thumbPageTitle.then((e=>{this.anchor.title=e})),this.renderingState===n.RenderingStates.FINISHED&&this._thumbPageCanvas.then((e=>{this.image?.setAttribute("aria-label",e)}))}}t.PDFThumbnailView=d},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PagesCountLimit=t.PDFViewer=t.PDFPageViewBuffer=void 0,i(98),i(109),i(111),i(114),i(116),i(118),i(120),i(76),i(89);var n=i(122),s=i(97),r=i(148),o=i(149),a=i(142),l=i(125);const d=10,h={FORCE_SCROLL_MODE_PAGE:15e3,FORCE_LAZY_PAGE_INIT:7500,PAUSE_EAGER_PAGE_INIT:250};function c(e){return Object.values(n.AnnotationEditorType).includes(e)&&e!==n.AnnotationEditorType.DISABLE}t.PagesCountLimit=h;class u{#_t=new Set;#yt=0;constructor(e){this.#yt=e}push(e){const t=this.#_t;t.has(e)&&t.delete(e),t.add(e),t.size>this.#yt&&this.#Pt()}resize(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;this.#yt=e;const i=this.#_t;if(t){const e=i.size;let n=1;for(const s of i)if(t.has(s.id)&&(i.delete(s),i.add(s)),++n>e)break}while(i.size>this.#yt)this.#Pt()}has(e){return this.#_t.has(e)}[Symbol.iterator](){return this.#_t.keys()}#Pt(){const e=this.#_t.keys().next().value;e?.destroy(),this.#_t.delete(e)}}t.PDFPageViewBuffer=u;class p{#Et=null;#St=null;#Ct=n.AnnotationEditorType.NONE;#Lt=null;#Tt=n.AnnotationMode.ENABLE_FORMS;#Mt=null;#It=null;#xt=!1;#At=!1;#Dt=null;#Nt=!1;#kt=0;#Bt=new ResizeObserver(this.#Ot.bind(this));#Vt=null;#Rt=null;#Ft=null;#Ut=s.TextLayerMode.ENABLE;constructor(e){const t="3.11.174";if(n.version!==t)throw new Error(`The API version "${n.version}" does not match the Viewer version "${t}".`);if(this.container=e.container,this.viewer=e.viewer||e.container.firstElementChild,"DIV"!==this.container?.tagName||"DIV"!==this.viewer?.tagName)throw new Error("Invalid `container` and/or `viewer` option.");if(this.container.offsetParent&&"absolute"!==getComputedStyle(this.container).position)throw new Error("The `container` must be absolutely positioned.");this.#Bt.observe(this.container),this.eventBus=e.eventBus,this.linkService=e.linkService||new l.SimpleLinkService,this.downloadManager=e.downloadManager||null,this.findController=e.findController||null,this.#St=e.altTextManager||null,this.findController&&(this.findController.onIsPageVisible=e=>this._getVisiblePages().ids.has(e)),this._scriptingManager=e.scriptingManager||null,this.#Ut=e.textLayerMode??s.TextLayerMode.ENABLE,this.#Tt=e.annotationMode??n.AnnotationMode.ENABLE_FORMS,this.#Ct=e.annotationEditorMode??n.AnnotationEditorType.NONE,this.imageResourcesPath=e.imageResourcesPath||"",this.enablePrintAutoRotate=e.enablePrintAutoRotate||!1,this.removePageBorders=e.removePageBorders||!1,e.useOnlyCssZoom&&(console.error("useOnlyCssZoom was removed, please use `maxCanvasPixels = 0` instead."),e.maxCanvasPixels=0),this.isOffscreenCanvasSupported=e.isOffscreenCanvasSupported??!0,this.maxCanvasPixels=e.maxCanvasPixels,this.l10n=e.l10n||r.NullL10n,this.#xt=e.enablePermissions||!1,this.pageColors=e.pageColors||null,this.defaultRenderingQueue=!e.renderingQueue,this.defaultRenderingQueue?(this.renderingQueue=new a.PDFRenderingQueue,this.renderingQueue.setViewer(this)):this.renderingQueue=e.renderingQueue,this.scroll=(0,s.watchScroll)(this.container,this._scrollUpdate.bind(this)),this.presentationModeState=s.PresentationModeState.UNKNOWN,this._onBeforeDraw=this._onAfterDraw=null,this._resetView(),this.removePageBorders&&this.viewer.classList.add("removePageBorders"),this.#zt(),this.eventBus._on("thumbnailrendered",(e=>{let{pageNumber:t,pdfPage:i}=e;const n=this._pages[t-1];this.#Et.has(n)||i?.cleanup()}))}get pagesCount(){return this._pages.length}getPageView(e){return this._pages[e]}getCachedPageViews(){return new Set(this.#Et)}get pageViewsReady(){return this._pagesCapability.settled&&this._pages.every((e=>e?.pdfPage))}get renderForms(){return this.#Tt===n.AnnotationMode.ENABLE_FORMS}get enableScripting(){return!!this._scriptingManager}get currentPageNumber(){return this._currentPageNumber}set currentPageNumber(e){if(!Number.isInteger(e))throw new Error("Invalid page number.");this.pdfDocument&&(this._setCurrentPageNumber(e,!0)||console.error(`currentPageNumber: "${e}" is not a valid page.`))}_setCurrentPageNumber(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(this._currentPageNumber===e)return t&&this.#Ht(),!0;if(!(0<e&&e<=this.pagesCount))return!1;const i=this._currentPageNumber;return this._currentPageNumber=e,this.eventBus.dispatch("pagechanging",{source:this,pageNumber:e,pageLabel:this._pageLabels?.[e-1]??null,previous:i}),t&&this.#Ht(),!0}get currentPageLabel(){return this._pageLabels?.[this._currentPageNumber-1]??null}set currentPageLabel(e){if(!this.pdfDocument)return;let t=0|e;if(this._pageLabels){const i=this._pageLabels.indexOf(e);i>=0&&(t=i+1)}this._setCurrentPageNumber(t,!0)||console.error(`currentPageLabel: "${e}" is not a valid page.`)}get currentScale(){return this._currentScale!==s.UNKNOWN_SCALE?this._currentScale:s.DEFAULT_SCALE}set currentScale(e){if(isNaN(e))throw new Error("Invalid numeric scale.");this.pdfDocument&&this.#Wt(e,{noScroll:!1})}get currentScaleValue(){return this._currentScaleValue}set currentScaleValue(e){this.pdfDocument&&this.#Wt(e,{noScroll:!1})}get pagesRotation(){return this._pagesRotation}set pagesRotation(e){if(!(0,s.isValidRotation)(e))throw new Error("Invalid pages rotation angle.");if(!this.pdfDocument)return;if(e%=360,e<0&&(e+=360),this._pagesRotation===e)return;this._pagesRotation=e;const t=this._currentPageNumber;this.refresh(!0,{rotation:e}),this._currentScaleValue&&this.#Wt(this._currentScaleValue,{noScroll:!0}),this.eventBus.dispatch("rotationchanging",{source:this,pagesRotation:e,pageNumber:t}),this.defaultRenderingQueue&&this.update()}get firstPagePromise(){return this.pdfDocument?this._firstPageCapability.promise:null}get onePageRendered(){return this.pdfDocument?this._onePageRenderedCapability.promise:null}get pagesPromise(){return this.pdfDocument?this._pagesCapability.promise:null}#jt(){const e=this;return{get annotationEditorUIManager(){return e.#Lt},get annotationStorage(){return e.pdfDocument?.annotationStorage},get downloadManager(){return e.downloadManager},get enableScripting(){return!!e._scriptingManager},get fieldObjectsPromise(){return e.pdfDocument?.getFieldObjects()},get findController(){return e.findController},get hasJSActionsPromise(){return e.pdfDocument?.hasJSActions()},get linkService(){return e.linkService}}}#$t(e){const t={annotationEditorMode:this.#Ct,annotationMode:this.#Tt,textLayerMode:this.#Ut};return e?(e.includes(n.PermissionFlag.COPY)||this.#Ut!==s.TextLayerMode.ENABLE||(t.textLayerMode=s.TextLayerMode.ENABLE_PERMISSIONS),e.includes(n.PermissionFlag.MODIFY_CONTENTS)||(t.annotationEditorMode=n.AnnotationEditorType.DISABLE),e.includes(n.PermissionFlag.MODIFY_ANNOTATIONS)||e.includes(n.PermissionFlag.FILL_INTERACTIVE_FORMS)||this.#Tt!==n.AnnotationMode.ENABLE_FORMS||(t.annotationMode=n.AnnotationMode.ENABLE),t):t}#Gt(){if("hidden"===document.visibilityState||!this.container.offsetParent||0===this._getVisiblePages().views.length)return Promise.resolve();const e=new Promise((e=>{this.#Rt=()=>{"hidden"===document.visibilityState&&(e(),document.removeEventListener("visibilitychange",this.#Rt),this.#Rt=null)},document.addEventListener("visibilitychange",this.#Rt)}));return Promise.race([this._onePageRenderedCapability.promise,e])}async getAllText(){const e=[],t=[];for(let i=1,n=this.pdfDocument.numPages;i<=n;++i){if(this.#Nt)return null;t.length=0;const n=await this.pdfDocument.getPage(i),{items:r}=await n.getTextContent();for(const e of r)e.str&&t.push(e.str),e.hasEOL&&t.push("\n");e.push((0,s.removeNullCharacters)(t.join("")))}return e.join("\n")}#Xt(e,t){const i=document.getSelection(),{focusNode:n,anchorNode:r}=i;if(r&&n&&i.containsNode(this.#Dt)){if(this.#At||e===s.TextLayerMode.ENABLE_PERMISSIONS)return t.preventDefault(),void t.stopPropagation();this.#At=!0;const i=this.container.style.cursor;this.container.style.cursor="wait";const n=e=>this.#Nt="Escape"===e.key;window.addEventListener("keydown",n),this.getAllText().then((async e=>{null!==e&&await navigator.clipboard.writeText(e)})).catch((e=>{console.warn(`Something goes wrong when extracting the text: ${e.message}`)})).finally((()=>{this.#At=!1,this.#Nt=!1,window.removeEventListener("keydown",n),this.container.style.cursor=i})),t.preventDefault(),t.stopPropagation()}}setDocument(e){if(this.pdfDocument&&(this.eventBus.dispatch("pagesdestroy",{source:this}),this._cancelRendering(),this._resetView(),this.findController?.setDocument(null),this._scriptingManager?.setDocument(null),this.#Lt&&(this.#Lt.destroy(),this.#Lt=null)),this.pdfDocument=e,!e)return;const t=e.numPages,i=e.getPage(1),r=e.getOptionalContentConfig(),a=this.#xt?e.getPermissions():Promise.resolve();if(t>h.FORCE_SCROLL_MODE_PAGE){console.warn("Forcing PAGE-scrolling for performance reasons, given the length of the document.");const e=this._scrollMode=s.ScrollMode.PAGE;this.eventBus.dispatch("scrollmodechanged",{source:this,mode:e})}this._pagesCapability.promise.then((()=>{this.eventBus.dispatch("pagesloaded",{source:this,pagesCount:t})}),(()=>{})),this._onBeforeDraw=e=>{const t=this._pages[e.pageNumber-1];t&&this.#Et.push(t)},this.eventBus._on("pagerender",this._onBeforeDraw),this._onAfterDraw=e=>{e.cssTransform||this._onePageRenderedCapability.settled||(this._onePageRenderedCapability.resolve({timestamp:e.timestamp}),this.eventBus._off("pagerendered",this._onAfterDraw),this._onAfterDraw=null,this.#Rt&&(document.removeEventListener("visibilitychange",this.#Rt),this.#Rt=null))},this.eventBus._on("pagerendered",this._onAfterDraw),Promise.all([i,a]).then((i=>{let[a,l]=i;if(e!==this.pdfDocument)return;this._firstPageCapability.resolve(a),this._optionalContentConfigPromise=r;const{annotationEditorMode:d,annotationMode:u,textLayerMode:p}=this.#$t(l);if(p!==s.TextLayerMode.DISABLE){const e=this.#Dt=document.createElement("div");e.id="hiddenCopyElement",this.viewer.before(e)}if(d!==n.AnnotationEditorType.DISABLE){const t=d;e.isPureXfa?console.warn("Warning: XFA-editing is not implemented."):c(t)?(this.#Lt=new n.AnnotationEditorUIManager(this.container,this.viewer,this.#St,this.eventBus,e,this.pageColors),t!==n.AnnotationEditorType.NONE&&this.#Lt.updateMode(t)):console.error(`Invalid AnnotationEditor mode: ${t}`)}const g=this.#jt.bind(this),f=this._scrollMode===s.ScrollMode.PAGE?null:this.viewer,m=this.currentScale,v=a.getViewport({scale:m*n.PixelsPerInch.PDF_TO_CSS_UNITS});this.viewer.style.setProperty("--scale-factor",v.scale),"CanvasText"!==this.pageColors?.foreground&&"Canvas"!==this.pageColors?.background||this.viewer.style.setProperty("--hcm-highligh-filter",e.filterFactory.addHighlightHCMFilter("CanvasText","Canvas","HighlightText","Highlight"));for(let e=1;e<=t;++e){const t=new o.PDFPageView({container:f,eventBus:this.eventBus,id:e,scale:m,defaultViewport:v.clone(),optionalContentConfigPromise:r,renderingQueue:this.renderingQueue,textLayerMode:p,annotationMode:u,imageResourcesPath:this.imageResourcesPath,isOffscreenCanvasSupported:this.isOffscreenCanvasSupported,maxCanvasPixels:this.maxCanvasPixels,pageColors:this.pageColors,l10n:this.l10n,layerProperties:g});this._pages.push(t)}const w=this._pages[0];w&&(w.setPdfPage(a),this.linkService.cachePageRef(1,a.ref)),this._scrollMode===s.ScrollMode.PAGE?this.#Kt():this._spreadMode!==s.SpreadMode.NONE&&this._updateSpreadMode(),this.#Gt().then((async()=>{if(this.findController?.setDocument(e),this._scriptingManager?.setDocument(e),this.#Dt&&(this.#It=this.#Xt.bind(this,p),document.addEventListener("copy",this.#It)),this.#Lt&&this.eventBus.dispatch("annotationeditormodechanged",{source:this,mode:this.#Ct}),e.loadingParams.disableAutoFetch||t>h.FORCE_LAZY_PAGE_INIT)return void this._pagesCapability.resolve();let i=t-1;if(i<=0)this._pagesCapability.resolve();else for(let n=2;n<=t;++n){const t=e.getPage(n).then((e=>{const t=this._pages[n-1];t.pdfPage||t.setPdfPage(e),this.linkService.cachePageRef(n,e.ref),0===--i&&this._pagesCapability.resolve()}),(e=>{console.error(`Unable to get page ${n} to initialize viewer`,e),0===--i&&this._pagesCapability.resolve()}));n%h.PAUSE_EAGER_PAGE_INIT===0&&await t}})),this.eventBus.dispatch("pagesinit",{source:this}),e.getMetadata().then((t=>{let{info:i}=t;e===this.pdfDocument&&i.Language&&(this.viewer.lang=i.Language)})),this.defaultRenderingQueue&&this.update()})).catch((e=>{console.error("Unable to initialize viewer",e),this._pagesCapability.reject(e)}))}setPageLabels(e){if(this.pdfDocument){e?Array.isArray(e)&&this.pdfDocument.numPages===e.length?this._pageLabels=e:(this._pageLabels=null,console.error("setPageLabels: Invalid page labels.")):this._pageLabels=null;for(let e=0,t=this._pages.length;e<t;e++)this._pages[e].setPageLabel(this._pageLabels?.[e]??null)}}_resetView(){this._pages=[],this._currentPageNumber=1,this._currentScale=s.UNKNOWN_SCALE,this._currentScaleValue=null,this._pageLabels=null,this.#Et=new u(d),this._location=null,this._pagesRotation=0,this._optionalContentConfigPromise=null,this._firstPageCapability=new n.PromiseCapability,this._onePageRenderedCapability=new n.PromiseCapability,this._pagesCapability=new n.PromiseCapability,this._scrollMode=s.ScrollMode.VERTICAL,this._previousScrollMode=s.ScrollMode.UNKNOWN,this._spreadMode=s.SpreadMode.NONE,this.#Vt={previousPageNumber:1,scrollDown:!0,pages:[]},this._onBeforeDraw&&(this.eventBus._off("pagerender",this._onBeforeDraw),this._onBeforeDraw=null),this._onAfterDraw&&(this.eventBus._off("pagerendered",this._onAfterDraw),this._onAfterDraw=null),this.#Rt&&(document.removeEventListener("visibilitychange",this.#Rt),this.#Rt=null),this.viewer.textContent="",this._updateScrollMode(),this.viewer.removeAttribute("lang"),this.#Dt&&(document.removeEventListener("copy",this.#It),this.#It=null,this.#Dt.remove(),this.#Dt=null)}#Kt(){if(this._scrollMode!==s.ScrollMode.PAGE)throw new Error("#ensurePageViewVisible: Invalid scrollMode value.");const e=this._currentPageNumber,t=this.#Vt,i=this.viewer;if(i.textContent="",t.pages.length=0,this._spreadMode!==s.SpreadMode.NONE||this.isInPresentationMode){const n=new Set,s=this._spreadMode-1;-1===s?n.add(e-1):e%2!==s?(n.add(e-1),n.add(e)):(n.add(e-2),n.add(e-1));const r=document.createElement("div");if(r.className="spread",this.isInPresentationMode){const e=document.createElement("div");e.className="dummyPage",r.append(e)}for(const e of n){const i=this._pages[e];i&&(r.append(i.div),t.pages.push(i))}i.append(r)}else{const n=this._pages[e-1];i.append(n.div),t.pages.push(n)}t.scrollDown=e>=t.previousPageNumber,t.previousPageNumber=e}_scrollUpdate(){0!==this.pagesCount&&this.update()}#Zt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;const{div:i,id:n}=e;if(this._currentPageNumber!==n&&this._setCurrentPageNumber(n),this._scrollMode===s.ScrollMode.PAGE&&(this.#Kt(),this.update()),!t&&!this.isInPresentationMode){const e=i.offsetLeft+i.clientLeft,n=e+i.clientWidth,{scrollLeft:r,clientWidth:o}=this.container;(this._scrollMode===s.ScrollMode.HORIZONTAL||e<r||n>r+o)&&(t={left:0,top:0})}(0,s.scrollIntoView)(i,t),!this._currentScaleValue&&this._location&&(this._location=null)}#Qt(e){return e===this._currentScale||Math.abs(e-this._currentScale)<1e-15}#qt(e,t,i){let{noScroll:s=!1,preset:r=!1,drawingDelay:o=-1}=i;if(this._currentScaleValue=t.toString(),this.#Qt(e))return void(r&&this.eventBus.dispatch("scalechanging",{source:this,scale:e,presetValue:t}));this.viewer.style.setProperty("--scale-factor",e*n.PixelsPerInch.PDF_TO_CSS_UNITS);const a=o>=0&&o<1e3;if(this.refresh(!0,{scale:e,drawingDelay:a?o:-1}),a&&(this.#Ft=setTimeout((()=>{this.#Ft=null,this.refresh()}),o)),this._currentScale=e,!s){let e,t=this._currentPageNumber;!this._location||this.isInPresentationMode||this.isChangingPresentationMode||(t=this._location.pageNumber,e=[null,{name:"XYZ"},this._location.left,this._location.top,null]),this.scrollPageIntoView({pageNumber:t,destArray:e,allowNegativeOffset:!0})}this.eventBus.dispatch("scalechanging",{source:this,scale:e,presetValue:r?t:void 0}),this.defaultRenderingQueue&&this.update()}get#Yt(){return this._spreadMode!==s.SpreadMode.NONE&&this._scrollMode!==s.ScrollMode.HORIZONTAL?2:1}#Wt(e,t){let i=parseFloat(e);if(i>0)t.preset=!1,this.#qt(i,e,t);else{const n=this._pages[this._currentPageNumber-1];if(!n)return;let r=s.SCROLLBAR_PADDING,o=s.VERTICAL_PADDING;this.isInPresentationMode?(r=o=4,this._spreadMode!==s.SpreadMode.NONE&&(r*=2)):this.removePageBorders?r=o=0:this._scrollMode===s.ScrollMode.HORIZONTAL&&([r,o]=[o,r]);const a=(this.container.clientWidth-r)/n.width*n.scale/this.#Yt,l=(this.container.clientHeight-o)/n.height*n.scale;switch(e){case"page-actual":i=1;break;case"page-width":i=a;break;case"page-height":i=l;break;case"page-fit":i=Math.min(a,l);break;case"auto":const t=(0,s.isPortraitOrientation)(n)?a:Math.min(l,a);i=Math.min(s.MAX_AUTO_SCALE,t);break;default:return void console.error(`#setScale: "${e}" is an unknown zoom value.`)}t.preset=!0,this.#qt(i,e,t)}}#Ht(){const e=this._pages[this._currentPageNumber-1];this.isInPresentationMode&&this.#Wt(this._currentScaleValue,{noScroll:!0}),this.#Zt(e)}pageLabelToPageNumber(e){if(!this._pageLabels)return null;const t=this._pageLabels.indexOf(e);return t<0?null:t+1}scrollPageIntoView(e){let{pageNumber:t,destArray:i=null,allowNegativeOffset:r=!1,ignoreDestinationZoom:o=!1}=e;if(!this.pdfDocument)return;const a=Number.isInteger(t)&&this._pages[t-1];if(!a)return void console.error(`scrollPageIntoView: "${t}" is not a valid pageNumber parameter.`);if(this.isInPresentationMode||!i)return void this._setCurrentPageNumber(t,!0);let l,d,h=0,c=0,u=0,p=0;const g=a.rotation%180!==0,f=(g?a.height:a.width)/a.scale/n.PixelsPerInch.PDF_TO_CSS_UNITS,m=(g?a.width:a.height)/a.scale/n.PixelsPerInch.PDF_TO_CSS_UNITS;let v=0;switch(i[1].name){case"XYZ":h=i[2],c=i[3],v=i[4],h=null!==h?h:0,c=null!==c?c:m;break;case"Fit":case"FitB":v="page-fit";break;case"FitH":case"FitBH":c=i[2],v="page-width",null===c&&this._location?(h=this._location.left,c=this._location.top):("number"!==typeof c||c<0)&&(c=m);break;case"FitV":case"FitBV":h=i[2],u=f,p=m,v="page-height";break;case"FitR":h=i[2],c=i[3],u=i[4]-h,p=i[5]-c;let e=s.SCROLLBAR_PADDING,t=s.VERTICAL_PADDING;this.removePageBorders&&(e=t=0),l=(this.container.clientWidth-e)/u/n.PixelsPerInch.PDF_TO_CSS_UNITS,d=(this.container.clientHeight-t)/p/n.PixelsPerInch.PDF_TO_CSS_UNITS,v=Math.min(Math.abs(l),Math.abs(d));break;default:return void console.error(`scrollPageIntoView: "${i[1].name}" is not a valid destination type.`)}if(o||(v&&v!==this._currentScale?this.currentScaleValue=v:this._currentScale===s.UNKNOWN_SCALE&&(this.currentScaleValue=s.DEFAULT_SCALE_VALUE)),"page-fit"===v&&!i[4])return void this.#Zt(a);const w=[a.viewport.convertToViewportPoint(h,c),a.viewport.convertToViewportPoint(h+u,c+p)];let b=Math.min(w[0][0],w[1][0]),_=Math.min(w[0][1],w[1][1]);r||(b=Math.max(b,0),_=Math.max(_,0)),this.#Zt(a,{left:b,top:_})}_updateLocation(e){const t=this._currentScale,i=this._currentScaleValue,n=parseFloat(i)===t?Math.round(1e4*t)/100:i,s=e.id,r=this._pages[s-1],o=this.container,a=r.getPagePoint(o.scrollLeft-e.x,o.scrollTop-e.y),l=Math.round(a[0]),d=Math.round(a[1]);let h=`#page=${s}`;this.isInPresentationMode||(h+=`&zoom=${n},${l},${d}`),this._location={pageNumber:s,scale:n,top:d,left:l,rotation:this._pagesRotation,pdfOpenParams:h}}update(){const e=this._getVisiblePages(),t=e.views,i=t.length;if(0===i)return;const n=Math.max(d,2*i+1);this.#Et.resize(n,e.ids),this.renderingQueue.renderHighestPriority(e);const r=this._spreadMode===s.SpreadMode.NONE&&(this._scrollMode===s.ScrollMode.PAGE||this._scrollMode===s.ScrollMode.VERTICAL),o=this._currentPageNumber;let a=!1;for(const s of t){if(s.percent<100)break;if(s.id===o&&r){a=!0;break}}this._setCurrentPageNumber(a?o:t[0].id),this._updateLocation(e.first),this.eventBus.dispatch("updateviewarea",{source:this,location:this._location})}containsElement(e){return this.container.contains(e)}focus(){this.container.focus()}get _isContainerRtl(){return"rtl"===getComputedStyle(this.container).direction}get isInPresentationMode(){return this.presentationModeState===s.PresentationModeState.FULLSCREEN}get isChangingPresentationMode(){return this.presentationModeState===s.PresentationModeState.CHANGING}get isHorizontalScrollbarEnabled(){return!this.isInPresentationMode&&this.container.scrollWidth>this.container.clientWidth}get isVerticalScrollbarEnabled(){return!this.isInPresentationMode&&this.container.scrollHeight>this.container.clientHeight}_getVisiblePages(){const e=this._scrollMode===s.ScrollMode.PAGE?this.#Vt.pages:this._pages,t=this._scrollMode===s.ScrollMode.HORIZONTAL,i=t&&this._isContainerRtl;return(0,s.getVisibleElements)({scrollEl:this.container,views:e,sortByVisibility:!0,horizontal:t,rtl:i})}cleanup(){for(const e of this._pages)e.renderingState!==s.RenderingStates.FINISHED&&e.reset()}_cancelRendering(){for(const e of this._pages)e.cancelRendering()}async#ft(e){if(e.pdfPage)return e.pdfPage;try{const t=await this.pdfDocument.getPage(e.id);return e.pdfPage||e.setPdfPage(t),this.linkService._cachedPageNumber?.(t.ref)||this.linkService.cachePageRef(e.id,t.ref),t}catch(t){return console.error("Unable to get page for page view",t),null}}#mt(e){if(1===e.first?.id)return!0;if(e.last?.id===this.pagesCount)return!1;switch(this._scrollMode){case s.ScrollMode.PAGE:return this.#Vt.scrollDown;case s.ScrollMode.HORIZONTAL:return this.scroll.right}return this.scroll.down}forceRendering(e){const t=e||this._getVisiblePages(),i=this.#mt(t),n=this._spreadMode!==s.SpreadMode.NONE&&this._scrollMode!==s.ScrollMode.HORIZONTAL,r=this.renderingQueue.getHighestPriority(t,this._pages,i,n);return!!r&&(this.#ft(r).then((()=>{this.renderingQueue.renderView(r)})),!0)}get hasEqualPageSizes(){const e=this._pages[0];for(let t=1,i=this._pages.length;t<i;++t){const i=this._pages[t];if(i.width!==e.width||i.height!==e.height)return!1}return!0}getPagesOverview(){let e;return this._pages.map((t=>{const i=t.pdfPage.getViewport({scale:1}),n=(0,s.isPortraitOrientation)(i);if(void 0===e)e=n;else if(this.enablePrintAutoRotate&&n!==e)return{width:i.height,height:i.width,rotation:(i.rotation-90)%360};return{width:i.width,height:i.height,rotation:i.rotation}}))}get optionalContentConfigPromise(){return this.pdfDocument?this._optionalContentConfigPromise?this._optionalContentConfigPromise:(console.error("optionalContentConfigPromise: Not initialized yet."),this.pdfDocument.getOptionalContentConfig()):Promise.resolve(null)}set optionalContentConfigPromise(e){if(!(e instanceof Promise))throw new Error(`Invalid optionalContentConfigPromise: ${e}`);this.pdfDocument&&this._optionalContentConfigPromise&&(this._optionalContentConfigPromise=e,this.refresh(!1,{optionalContentConfigPromise:e}),this.eventBus.dispatch("optionalcontentconfigchanged",{source:this,promise:e}))}get scrollMode(){return this._scrollMode}set scrollMode(e){if(this._scrollMode!==e){if(!(0,s.isValidScrollMode)(e))throw new Error(`Invalid scroll mode: ${e}`);this.pagesCount>h.FORCE_SCROLL_MODE_PAGE||(this._previousScrollMode=this._scrollMode,this._scrollMode=e,this.eventBus.dispatch("scrollmodechanged",{source:this,mode:e}),this._updateScrollMode(this._currentPageNumber))}}_updateScrollMode(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;const t=this._scrollMode,i=this.viewer;i.classList.toggle("scrollHorizontal",t===s.ScrollMode.HORIZONTAL),i.classList.toggle("scrollWrapped",t===s.ScrollMode.WRAPPED),this.pdfDocument&&e&&(t===s.ScrollMode.PAGE?this.#Kt():this._previousScrollMode===s.ScrollMode.PAGE&&this._updateSpreadMode(),this._currentScaleValue&&isNaN(this._currentScaleValue)&&this.#Wt(this._currentScaleValue,{noScroll:!0}),this._setCurrentPageNumber(e,!0),this.update())}get spreadMode(){return this._spreadMode}set spreadMode(e){if(this._spreadMode!==e){if(!(0,s.isValidSpreadMode)(e))throw new Error(`Invalid spread mode: ${e}`);this._spreadMode=e,this.eventBus.dispatch("spreadmodechanged",{source:this,mode:e}),this._updateSpreadMode(this._currentPageNumber)}}_updateSpreadMode(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(!this.pdfDocument)return;const t=this.viewer,i=this._pages;if(this._scrollMode===s.ScrollMode.PAGE)this.#Kt();else if(t.textContent="",this._spreadMode===s.SpreadMode.NONE)for(const n of this._pages)t.append(n.div);else{const e=this._spreadMode-1;let n=null;for(let s=0,r=i.length;s<r;++s)null===n?(n=document.createElement("div"),n.className="spread",t.append(n)):s%2===e&&(n=n.cloneNode(!1),t.append(n)),n.append(i[s].div)}e&&(this._currentScaleValue&&isNaN(this._currentScaleValue)&&this.#Wt(this._currentScaleValue,{noScroll:!0}),this._setCurrentPageNumber(e,!0),this.update())}_getPageAdvance(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];switch(this._scrollMode){case s.ScrollMode.WRAPPED:{const{views:i}=this._getVisiblePages(),n=new Map;for(const{id:e,y:t,percent:s,widthPercent:r}of i){if(0===s||r<100)continue;let i=n.get(t);i||n.set(t,i||=[]),i.push(e)}for(const s of n.values()){const i=s.indexOf(e);if(-1===i)continue;const n=s.length;if(1===n)break;if(t)for(let t=i-1,r=0;t>=r;t--){const i=s[t],n=s[t+1]-1;if(i<n)return e-n}else for(let t=i+1,r=n;t<r;t++){const i=s[t],n=s[t-1]+1;if(i>n)return n-e}if(t){const t=s[0];if(t<e)return e-t+1}else{const t=s[n-1];if(t>e)return t-e+1}break}break}case s.ScrollMode.HORIZONTAL:break;case s.ScrollMode.PAGE:case s.ScrollMode.VERTICAL:{if(this._spreadMode===s.SpreadMode.NONE)break;const i=this._spreadMode-1;if(t&&e%2!==i)break;if(!t&&e%2===i)break;const{views:n}=this._getVisiblePages(),r=t?e-1:e+1;for(const{id:e,percent:t,widthPercent:s}of n)if(e===r){if(t>0&&100===s)return 2;break}break}}return 1}nextPage(){const e=this._currentPageNumber,t=this.pagesCount;if(e>=t)return!1;const i=this._getPageAdvance(e,!1)||1;return this.currentPageNumber=Math.min(e+i,t),!0}previousPage(){const e=this._currentPageNumber;if(e<=1)return!1;const t=this._getPageAdvance(e,!0)||1;return this.currentPageNumber=Math.max(e-t,1),!0}increaseScale(){let{drawingDelay:e,scaleFactor:t,steps:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.pdfDocument)return;let n=this._currentScale;if(t>1)n=Math.round(n*t*100)/100;else{i??=1;do{n=Math.ceil(10*(n*s.DEFAULT_SCALE_DELTA).toFixed(2))/10}while(--i>0&&n<s.MAX_SCALE)}this.#Wt(Math.min(s.MAX_SCALE,n),{noScroll:!1,drawingDelay:e})}decreaseScale(){let{drawingDelay:e,scaleFactor:t,steps:i}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!this.pdfDocument)return;let n=this._currentScale;if(t>0&&t<1)n=Math.round(n*t*100)/100;else{i??=1;do{n=Math.floor(10*(n/s.DEFAULT_SCALE_DELTA).toFixed(2))/10}while(--i>0&&n>s.MIN_SCALE)}this.#Wt(Math.max(s.MIN_SCALE,n),{noScroll:!1,drawingDelay:e})}#zt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.container.clientHeight;e!==this.#kt&&(this.#kt=e,s.docStyle.setProperty("--viewer-container-height",`${e}px`))}#Ot(e){for(const t of e)if(t.target===this.container){this.#zt(Math.floor(t.borderBoxSize[0].blockSize)),this.#Mt=null;break}}get containerTopLeft(){return this.#Mt||=[this.container.offsetTop,this.container.offsetLeft]}get annotationEditorMode(){return this.#Lt?this.#Ct:n.AnnotationEditorType.DISABLE}set annotationEditorMode(e){let{mode:t,editId:i=null}=e;if(!this.#Lt)throw new Error("The AnnotationEditor is not enabled.");if(this.#Ct!==t){if(!c(t))throw new Error(`Invalid AnnotationEditor mode: ${t}`);this.pdfDocument&&(this.#Ct=t,this.eventBus.dispatch("annotationeditormodechanged",{source:this,mode:t}),this.#Lt.updateMode(t,i))}}set annotationEditorParams(e){let{type:t,value:i}=e;if(!this.#Lt)throw new Error("The AnnotationEditor is not enabled.");this.#Lt.updateParams(t,i)}refresh(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Object.create(null);if(this.pdfDocument){for(const e of this._pages)e.update(t);null!==this.#Ft&&(clearTimeout(this.#Ft),this.#Ft=null),e||this.update()}}}t.PDFViewer=p},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NullL10n=void 0,t.getL10nFallback=n;const i={of_pages:"of {{pagesCount}}",page_of_pages:"({{pageNumber}} of {{pagesCount}})",document_properties_kb:"{{size_kb}} KB ({{size_b}} bytes)",document_properties_mb:"{{size_mb}} MB ({{size_b}} bytes)",document_properties_date_string:"{{date}}, {{time}}",document_properties_page_size_unit_inches:"in",document_properties_page_size_unit_millimeters:"mm",document_properties_page_size_orientation_portrait:"portrait",document_properties_page_size_orientation_landscape:"landscape",document_properties_page_size_name_a3:"A3",document_properties_page_size_name_a4:"A4",document_properties_page_size_name_letter:"Letter",document_properties_page_size_name_legal:"Legal",document_properties_page_size_dimension_string:"{{width}} × {{height}} {{unit}} ({{orientation}})",document_properties_page_size_dimension_name_string:"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})",document_properties_linearized_yes:"Yes",document_properties_linearized_no:"No",additional_layers:"Additional Layers",page_landmark:"Page {{page}}",thumb_page_title:"Page {{page}}",thumb_page_canvas:"Thumbnail of Page {{page}}",find_reached_top:"Reached top of document, continued from bottom",find_reached_bottom:"Reached end of document, continued from top","find_match_count[one]":"{{current}} of {{total}} match","find_match_count[other]":"{{current}} of {{total}} matches","find_match_count_limit[one]":"More than {{limit}} match","find_match_count_limit[other]":"More than {{limit}} matches",find_not_found:"Phrase not found",page_scale_width:"Page Width",page_scale_fit:"Page Fit",page_scale_auto:"Automatic Zoom",page_scale_actual:"Actual Size",page_scale_percent:"{{scale}}%",loading_error:"An error occurred while loading the PDF.",invalid_file_error:"Invalid or corrupted PDF file.",missing_file_error:"Missing PDF file.",unexpected_response_error:"Unexpected server response.",rendering_error:"An error occurred while rendering the page.",annotation_date_string:"{{date}}, {{time}}",printing_not_supported:"Warning: Printing is not fully supported by this browser.",printing_not_ready:"Warning: The PDF is not fully loaded for printing.",web_fonts_disabled:"Web fonts are disabled: unable to use embedded PDF fonts.",free_text2_default_content:"Start typing…",editor_free_text2_aria_label:"Text Editor",editor_ink2_aria_label:"Draw Editor",editor_ink_canvas_aria_label:"User-created image",editor_alt_text_button_label:"Alt text",editor_alt_text_edit_button_label:"Edit alt text",editor_alt_text_decorative_tooltip:"Marked as decorative"};function n(e,t){switch(e){case"find_match_count":e=`find_match_count[${1===t.total?"one":"other"}]`;break;case"find_match_count_limit":e=`find_match_count_limit[${1===t.limit?"one":"other"}]`;break}return i[e]||""}function s(e,t){return t?e.replaceAll(/\{\{\s*(\w+)\s*\}\}/g,((e,i)=>i in t?t[i]:"{{"+i+"}}")):e}i.print_progress_percent="{{progress}}%";const r={async getLanguage(){return"en-us"},async getDirection(){return"ltr"},async get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:n(e,t);return s(i,t)},async translate(e){}};t.NullL10n=r},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPageView=void 0,i(89),i(76);var n=i(122),s=i(97),r=i(150),o=i(151),a=i(123),l=i(148),d=(i(125),i(152)),h=i(153),c=i(154),u=i(155),p=i(156);const g=a.compatibilityParams.maxCanvasPixels||16777216,f=()=>null;class m{#Tt=n.AnnotationMode.ENABLE_FORMS;#Jt=!1;#jt=null;#ei=null;#ti=null;#ii=null;#ni=s.RenderingStates.INITIAL;#Ut=s.TextLayerMode.ENABLE;#si={directDrawing:!0,initialOptionalContent:!0,regularAnnotations:!0};#ri=new WeakMap;constructor(e){const t=e.container,i=e.defaultViewport;this.id=e.id,this.renderingId="page"+this.id,this.#jt=e.layerProperties||f,this.pdfPage=null,this.pageLabel=null,this.rotation=0,this.scale=e.scale||s.DEFAULT_SCALE,this.viewport=i,this.pdfPageRotate=i.rotation,this._optionalContentConfigPromise=e.optionalContentConfigPromise||null,this.#Ut=e.textLayerMode??s.TextLayerMode.ENABLE,this.#Tt=e.annotationMode??n.AnnotationMode.ENABLE_FORMS,this.imageResourcesPath=e.imageResourcesPath||"",this.isOffscreenCanvasSupported=e.isOffscreenCanvasSupported??!0,this.maxCanvasPixels=e.maxCanvasPixels??g,this.pageColors=e.pageColors||null,this.eventBus=e.eventBus,this.renderingQueue=e.renderingQueue,this.l10n=e.l10n||l.NullL10n,this.renderTask=null,this.resume=null,this._isStandalone=!this.renderingQueue?.hasViewer(),this._container=t,e.useOnlyCssZoom&&(console.error("useOnlyCssZoom was removed, please use `maxCanvasPixels = 0` instead."),this.maxCanvasPixels=0),this._annotationCanvasMap=null,this.annotationLayer=null,this.annotationEditorLayer=null,this.textLayer=null,this.zoomLayer=null,this.xfaLayer=null,this.structTreeLayer=null;const r=document.createElement("div");if(r.className="page",r.setAttribute("data-page-number",this.id),r.setAttribute("role","region"),this.l10n.get("page_landmark",{page:this.id}).then((e=>{r.setAttribute("aria-label",e)})),this.div=r,this.#oi(),t?.append(r),this._isStandalone){t?.style.setProperty("--scale-factor",this.scale*n.PixelsPerInch.PDF_TO_CSS_UNITS);const{optionalContentConfigPromise:i}=e;i&&i.then((e=>{i===this._optionalContentConfigPromise&&(this.#si.initialOptionalContent=e.hasInitialVisibility)}))}}get renderingState(){return this.#ni}set renderingState(e){if(e!==this.#ni)switch(this.#ni=e,this.#ei&&(clearTimeout(this.#ei),this.#ei=null),e){case s.RenderingStates.PAUSED:this.div.classList.remove("loading");break;case s.RenderingStates.RUNNING:this.div.classList.add("loadingIcon"),this.#ei=setTimeout((()=>{this.div.classList.add("loading"),this.#ei=null}),0);break;case s.RenderingStates.INITIAL:case s.RenderingStates.FINISHED:this.div.classList.remove("loadingIcon","loading");break}}#oi(){const{viewport:e}=this;if(this.pdfPage){if(this.#ti===e.rotation)return;this.#ti=e.rotation}(0,n.setLayerDimensions)(this.div,e,!0,!1)}setPdfPage(e){!this._isStandalone||"CanvasText"!==this.pageColors?.foreground&&"Canvas"!==this.pageColors?.background||this._container?.style.setProperty("--hcm-highligh-filter",e.filterFactory.addHighlightHCMFilter("CanvasText","Canvas","HighlightText","Highlight")),this.pdfPage=e,this.pdfPageRotate=e.rotate;const t=(this.rotation+this.pdfPageRotate)%360;this.viewport=e.getViewport({scale:this.scale*n.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:t}),this.#oi(),this.reset()}destroy(){this.reset(),this.pdfPage?.cleanup()}get _textHighlighter(){return(0,n.shadow)(this,"_textHighlighter",new c.TextHighlighter({pageIndex:this.id-1,eventBus:this.eventBus,findController:this.#jt().findController}))}async#ai(){let e=null;try{await this.annotationLayer.render(this.viewport,"display")}catch(t){console.error(`#renderAnnotationLayer: "${t}".`),e=t}finally{this.eventBus.dispatch("annotationlayerrendered",{source:this,pageNumber:this.id,error:e})}}async#li(){let e=null;try{await this.annotationEditorLayer.render(this.viewport,"display")}catch(t){console.error(`#renderAnnotationEditorLayer: "${t}".`),e=t}finally{this.eventBus.dispatch("annotationeditorlayerrendered",{source:this,pageNumber:this.id,error:e})}}async#di(){let e=null;try{const e=await this.xfaLayer.render(this.viewport,"display");e?.textDivs&&this._textHighlighter&&this.#hi(e.textDivs)}catch(t){console.error(`#renderXfaLayer: "${t}".`),e=t}finally{this.eventBus.dispatch("xfalayerrendered",{source:this,pageNumber:this.id,error:e})}}async#ci(){const{pdfPage:e,textLayer:t,viewport:i}=this;if(!t)return;let s=null;try{if(!t.renderingDone){const i=e.streamTextContent({includeMarkedContent:!0,disableNormalization:!0});t.setTextContentSource(i)}await t.render(i)}catch(r){if(r instanceof n.AbortException)return;console.error(`#renderTextLayer: "${r}".`),s=r}this.eventBus.dispatch("textlayerrendered",{source:this,pageNumber:this.id,numTextDivs:t.numTextDivs,error:s}),this.#ui()}async#ui(){if(!this.textLayer)return;this.structTreeLayer||=new d.StructTreeLayerBuilder;const e=await(this.structTreeLayer.renderingDone?null:this.pdfPage.getStructTree()),t=this.structTreeLayer?.render(e);t&&this.canvas?.append(t),this.structTreeLayer?.show()}async#hi(e){const t=await this.pdfPage.getTextContent(),i=[];for(const n of t.items)i.push(n.str);this._textHighlighter.setTextMapping(e,i),this._textHighlighter.enable()}_resetZoomLayer(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.zoomLayer)return;const t=this.zoomLayer.firstChild;this.#ri.delete(t),t.width=0,t.height=0,e&&this.zoomLayer.remove(),this.zoomLayer=null}reset(){let{keepZoomLayer:e=!1,keepAnnotationLayer:t=!1,keepAnnotationEditorLayer:i=!1,keepXfaLayer:n=!1,keepTextLayer:r=!1}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.cancelRendering({keepAnnotationLayer:t,keepAnnotationEditorLayer:i,keepXfaLayer:n,keepTextLayer:r}),this.renderingState=s.RenderingStates.INITIAL;const o=this.div,a=o.childNodes,l=e&&this.zoomLayer||null,d=t&&this.annotationLayer?.div||null,h=i&&this.annotationEditorLayer?.div||null,c=n&&this.xfaLayer?.div||null,u=r&&this.textLayer?.div||null;for(let s=a.length-1;s>=0;s--){const e=a[s];switch(e){case l:case d:case h:case c:case u:continue}e.remove()}o.removeAttribute("data-loaded"),d&&this.annotationLayer.hide(),h&&this.annotationEditorLayer.hide(),c&&this.xfaLayer.hide(),u&&this.textLayer.hide(),this.structTreeLayer?.hide(),l||(this.canvas&&(this.#ri.delete(this.canvas),this.canvas.width=0,this.canvas.height=0,delete this.canvas),this._resetZoomLayer())}update(e){let{scale:t=0,rotation:i=null,optionalContentConfigPromise:r=null,drawingDelay:o=-1}=e;this.scale=t||this.scale,"number"===typeof i&&(this.rotation=i),r instanceof Promise&&(this._optionalContentConfigPromise=r,r.then((e=>{r===this._optionalContentConfigPromise&&(this.#si.initialOptionalContent=e.hasInitialVisibility)}))),this.#si.directDrawing=!0;const a=(this.rotation+this.pdfPageRotate)%360;if(this.viewport=this.viewport.clone({scale:this.scale*n.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:a}),this.#oi(),this._isStandalone&&this._container?.style.setProperty("--scale-factor",this.viewport.scale),this.canvas){let e=!1;if(this.#Jt)if(0===this.maxCanvasPixels)e=!0;else if(this.maxCanvasPixels>0){const{width:t,height:i}=this.viewport,{sx:n,sy:s}=this.outputScale;e=(Math.floor(t)*n|0)*(Math.floor(i)*s|0)>this.maxCanvasPixels}const t=!e&&o>=0&&o<1e3;if(t||e){if(t&&this.renderingState!==s.RenderingStates.FINISHED&&(this.cancelRendering({keepZoomLayer:!0,keepAnnotationLayer:!0,keepAnnotationEditorLayer:!0,keepXfaLayer:!0,keepTextLayer:!0,cancelExtraDelay:o}),this.renderingState=s.RenderingStates.FINISHED,this.#si.directDrawing=!1),this.cssTransform({target:this.canvas,redrawAnnotationLayer:!0,redrawAnnotationEditorLayer:!0,redrawXfaLayer:!0,redrawTextLayer:!t,hideTextLayer:t}),t)return;return void this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!0,timestamp:performance.now(),error:this.#ii})}this.zoomLayer||this.canvas.hidden||(this.zoomLayer=this.canvas.parentNode,this.zoomLayer.style.position="absolute")}this.zoomLayer&&this.cssTransform({target:this.zoomLayer.firstChild}),this.reset({keepZoomLayer:!0,keepAnnotationLayer:!0,keepAnnotationEditorLayer:!0,keepXfaLayer:!0,keepTextLayer:!0})}cancelRendering(){let{keepAnnotationLayer:e=!1,keepAnnotationEditorLayer:t=!1,keepXfaLayer:i=!1,keepTextLayer:n=!1,cancelExtraDelay:s=0}=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.renderTask&&(this.renderTask.cancel(s),this.renderTask=null),this.resume=null,!this.textLayer||n&&this.textLayer.div||(this.textLayer.cancel(),this.textLayer=null),this.structTreeLayer&&!this.textLayer&&(this.structTreeLayer=null),!this.annotationLayer||e&&this.annotationLayer.div||(this.annotationLayer.cancel(),this.annotationLayer=null,this._annotationCanvasMap=null),!this.annotationEditorLayer||t&&this.annotationEditorLayer.div||(this.annotationEditorLayer.cancel(),this.annotationEditorLayer=null),!this.xfaLayer||i&&this.xfaLayer.div||(this.xfaLayer.cancel(),this.xfaLayer=null,this._textHighlighter?.disable())}cssTransform(e){let{target:t,redrawAnnotationLayer:i=!1,redrawAnnotationEditorLayer:n=!1,redrawXfaLayer:s=!1,redrawTextLayer:r=!1,hideTextLayer:o=!1}=e;if(!t.hasAttribute("zooming")){t.setAttribute("zooming",!0);const{style:e}=t;e.width=e.height=""}const a=this.#ri.get(t);if(this.viewport!==a){const e=this.viewport.rotation-a.rotation,i=Math.abs(e);let n=1,s=1;if(90===i||270===i){const{width:e,height:t}=this.viewport;n=t/e,s=e/t}t.style.transform=`rotate(${e}deg) scale(${n}, ${s})`}i&&this.annotationLayer&&this.#ai(),n&&this.annotationEditorLayer&&this.#li(),s&&this.xfaLayer&&this.#di(),this.textLayer&&(o?(this.textLayer.hide(),this.structTreeLayer?.hide()):r&&this.#ci())}get width(){return this.viewport.width}get height(){return this.viewport.height}getPagePoint(e,t){return this.viewport.convertToPdfPoint(e,t)}async#bt(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(e===this.renderTask&&(this.renderTask=null),t instanceof n.RenderingCancelledException)this.#ii=null;else if(this.#ii=t,this.renderingState=s.RenderingStates.FINISHED,this._resetZoomLayer(!0),this.#si.regularAnnotations=!e.separateAnnots,this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!1,timestamp:performance.now(),error:this.#ii}),t)throw t}async draw(){this.renderingState!==s.RenderingStates.INITIAL&&(console.error("Must be in new state before drawing"),this.reset());const{div:e,l10n:t,pageColors:i,pdfPage:a,viewport:l}=this;if(!a)throw this.renderingState=s.RenderingStates.FINISHED,new Error("pdfPage is not loaded");this.renderingState=s.RenderingStates.RUNNING;const d=document.createElement("div");if(d.classList.add("canvasWrapper"),e.append(d),this.textLayer||this.#Ut===s.TextLayerMode.DISABLE||a.isPureXfa||(this._accessibilityManager||=new h.TextAccessibilityManager,this.textLayer=new u.TextLayerBuilder({highlighter:this._textHighlighter,accessibilityManager:this._accessibilityManager,isOffscreenCanvasSupported:this.isOffscreenCanvasSupported,enablePermissions:this.#Ut===s.TextLayerMode.ENABLE_PERMISSIONS}),e.append(this.textLayer.div)),!this.annotationLayer&&this.#Tt!==n.AnnotationMode.DISABLE){const{annotationStorage:i,downloadManager:s,enableScripting:r,fieldObjectsPromise:l,hasJSActionsPromise:d,linkService:h}=this.#jt();this._annotationCanvasMap||=new Map,this.annotationLayer=new o.AnnotationLayerBuilder({pageDiv:e,pdfPage:a,annotationStorage:i,imageResourcesPath:this.imageResourcesPath,renderForms:this.#Tt===n.AnnotationMode.ENABLE_FORMS,linkService:h,downloadManager:s,l10n:t,enableScripting:r,hasJSActionsPromise:d,fieldObjectsPromise:l,annotationCanvasMap:this._annotationCanvasMap,accessibilityManager:this._accessibilityManager})}const c=e=>{if(w?.(!1),this.renderingQueue&&!this.renderingQueue.isHighestPriority(this))return this.renderingState=s.RenderingStates.PAUSED,void(this.resume=()=>{this.renderingState=s.RenderingStates.RUNNING,e()});e()},{width:g,height:f}=l,m=document.createElement("canvas");m.setAttribute("role","presentation"),m.hidden=!0;const v=!(!i?.background||!i?.foreground);let w=e=>{v&&!e||(m.hidden=!1,w=null)};d.append(m),this.canvas=m;const b=m.getContext("2d",{alpha:!1}),_=this.outputScale=new s.OutputScale;if(0===this.maxCanvasPixels){const e=1/this.scale;_.sx*=e,_.sy*=e,this.#Jt=!0}else if(this.maxCanvasPixels>0){const e=g*f,t=Math.sqrt(this.maxCanvasPixels/e);_.sx>t||_.sy>t?(_.sx=t,_.sy=t,this.#Jt=!0):this.#Jt=!1}const y=(0,s.approximateFraction)(_.sx),P=(0,s.approximateFraction)(_.sy);m.width=(0,s.roundToDivide)(g*_.sx,y[0]),m.height=(0,s.roundToDivide)(f*_.sy,P[0]);const{style:E}=m;E.width=(0,s.roundToDivide)(g,y[1])+"px",E.height=(0,s.roundToDivide)(f,P[1])+"px",this.#ri.set(m,l);const S=_.scaled?[_.sx,0,0,_.sy,0,0]:null,C={canvasContext:b,transform:S,viewport:l,annotationMode:this.#Tt,optionalContentConfigPromise:this._optionalContentConfigPromise,annotationCanvasMap:this._annotationCanvasMap,pageColors:i},L=this.renderTask=this.pdfPage.render(C);L.onContinue=c;const T=L.promise.then((async()=>{if(w?.(!0),await this.#bt(L),this.#ci(),this.annotationLayer&&await this.#ai(),!this.annotationEditorLayer){const{annotationEditorUIManager:i}=this.#jt();if(!i)return;this.annotationEditorLayer=new r.AnnotationEditorLayerBuilder({uiManager:i,pageDiv:e,pdfPage:a,l10n:t,accessibilityManager:this._accessibilityManager,annotationLayer:this.annotationLayer?.annotationLayer})}this.#li()}),(e=>(e instanceof n.RenderingCancelledException||w?.(!0),this.#bt(L,e))));if(a.isPureXfa){if(this.xfaLayer)this.xfaLayer.div&&e.append(this.xfaLayer.div);else{const{annotationStorage:t,linkService:i}=this.#jt();this.xfaLayer=new p.XfaLayerBuilder({pageDiv:e,pdfPage:a,annotationStorage:t,linkService:i})}this.#di()}return e.setAttribute("data-loaded",!0),this.eventBus.dispatch("pagerender",{source:this,pageNumber:this.id}),T}setPageLabel(e){this.pageLabel="string"===typeof e?e:null,null!==this.pageLabel?this.div.setAttribute("data-page-label",this.pageLabel):this.div.removeAttribute("data-page-label")}get thumbnailCanvas(){const{directDrawing:e,initialOptionalContent:t,regularAnnotations:i}=this.#si;return e&&t&&i?this.canvas:null}}t.PDFPageView=m},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationEditorLayerBuilder=void 0;var n=i(122),s=i(148);class r{#pi=null;#C;constructor(e){this.pageDiv=e.pageDiv,this.pdfPage=e.pdfPage,this.accessibilityManager=e.accessibilityManager,this.l10n=e.l10n||s.NullL10n,this.annotationEditorLayer=null,this.div=null,this._cancelled=!1,this.#C=e.uiManager,this.#pi=e.annotationLayer||null}async render(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"display";if("display"!==t)return;if(this._cancelled)return;const i=e.clone({dontFlip:!0});if(this.div)return this.annotationEditorLayer.update({viewport:i}),void this.show();const s=this.div=document.createElement("div");s.className="annotationEditorLayer",s.tabIndex=0,s.hidden=!0,s.dir=this.#C.direction,this.pageDiv.append(s),this.annotationEditorLayer=new n.AnnotationEditorLayer({uiManager:this.#C,div:s,accessibilityManager:this.accessibilityManager,pageIndex:this.pdfPage.pageNumber-1,l10n:this.l10n,viewport:i,annotationLayer:this.#pi});const r={viewport:i,div:s,annotations:null,intent:t};this.annotationEditorLayer.render(r),this.show()}cancel(){this._cancelled=!0,this.div&&(this.pageDiv=null,this.annotationEditorLayer.destroy(),this.div.remove())}hide(){this.div&&(this.div.hidden=!0)}show(){this.div&&!this.annotationEditorLayer.isEmpty&&(this.div.hidden=!1)}}t.AnnotationEditorLayerBuilder=r},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationLayerBuilder=void 0;var n=i(122),s=i(148),r=i(97);class o{#gi=null;constructor(e){let{pageDiv:t,pdfPage:i,linkService:n,downloadManager:r,annotationStorage:o=null,imageResourcesPath:a="",renderForms:l=!0,l10n:d=s.NullL10n,enableScripting:h=!1,hasJSActionsPromise:c=null,fieldObjectsPromise:u=null,annotationCanvasMap:p=null,accessibilityManager:g=null}=e;this.pageDiv=t,this.pdfPage=i,this.linkService=n,this.downloadManager=r,this.imageResourcesPath=a,this.renderForms=l,this.l10n=d,this.annotationStorage=o,this.enableScripting=h,this._hasJSActionsPromise=c||Promise.resolve(!1),this._fieldObjectsPromise=u||Promise.resolve(null),this._annotationCanvasMap=p,this._accessibilityManager=g,this.annotationLayer=null,this.div=null,this._cancelled=!1,this._eventBus=n.eventBus}async render(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"display";if(this.div){if(this._cancelled||!this.annotationLayer)return;return void this.annotationLayer.update({viewport:e.clone({dontFlip:!0})})}const[i,s,o]=await Promise.all([this.pdfPage.getAnnotations({intent:t}),this._hasJSActionsPromise,this._fieldObjectsPromise]);if(this._cancelled)return;const a=this.div=document.createElement("div");a.className="annotationLayer",this.pageDiv.append(a),0!==i.length?(this.annotationLayer=new n.AnnotationLayer({div:a,accessibilityManager:this._accessibilityManager,annotationCanvasMap:this._annotationCanvasMap,l10n:this.l10n,page:this.pdfPage,viewport:e.clone({dontFlip:!0})}),await this.annotationLayer.render({annotations:i,imageResourcesPath:this.imageResourcesPath,renderForms:this.renderForms,linkService:this.linkService,downloadManager:this.downloadManager,annotationStorage:this.annotationStorage,enableScripting:this.enableScripting,hasJSActions:s,fieldObjects:o}),this.linkService.isInPresentationMode&&this.#fi(r.PresentationModeState.FULLSCREEN),this.#gi||(this.#gi=e=>{this.#fi(e.state)},this._eventBus?._on("presentationmodechanged",this.#gi))):this.hide()}cancel(){this._cancelled=!0,this.#gi&&(this._eventBus?._off("presentationmodechanged",this.#gi),this.#gi=null)}hide(){this.div&&(this.div.hidden=!0)}#fi(e){if(!this.div)return;let t=!1;switch(e){case r.PresentationModeState.FULLSCREEN:t=!0;break;case r.PresentationModeState.NORMAL:break;default:return}for(const i of this.div.childNodes)i.hasAttribute("data-internal-link")||(i.inert=t)}}t.AnnotationLayerBuilder=o},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StructTreeLayerBuilder=void 0;var n=i(97);const s={Document:null,DocumentFragment:null,Part:"group",Sect:"group",Div:"group",Aside:"note",NonStruct:"none",P:null,H:"heading",Title:null,FENote:"note",Sub:"group",Lbl:null,Span:null,Em:null,Strong:null,Link:"link",Annot:"note",Form:"form",Ruby:null,RB:null,RT:null,RP:null,Warichu:null,WT:null,WP:null,L:"list",LI:"listitem",LBody:null,Table:"table",TR:"row",TH:"columnheader",TD:"cell",THead:"columnheader",TBody:null,TFoot:null,Caption:null,Figure:"figure",Formula:null,Artifact:null},r=/^H(\d+)$/;class o{#mi=void 0;get renderingDone(){return void 0!==this.#mi}render(e){if(void 0!==this.#mi)return this.#mi;const t=this.#vi(e);return t?.classList.add("structTree"),this.#mi=t}hide(){this.#mi&&!this.#mi.hidden&&(this.#mi.hidden=!0)}show(){this.#mi?.hidden&&(this.#mi.hidden=!1)}#wi(e,t){const{alt:i,id:s,lang:r}=e;void 0!==i&&t.setAttribute("aria-label",(0,n.removeNullCharacters)(i)),void 0!==s&&t.setAttribute("aria-owns",s),void 0!==r&&t.setAttribute("lang",(0,n.removeNullCharacters)(r,!0))}#vi(e){if(!e)return null;const t=document.createElement("span");if("role"in e){const{role:i}=e,n=i.match(r);n?(t.setAttribute("role","heading"),t.setAttribute("aria-level",n[1])):s[i]&&t.setAttribute("role",s[i])}if(this.#wi(e,t),e.children)if(1===e.children.length&&"id"in e.children[0])this.#wi(e.children[0],t);else for(const i of e.children)t.append(this.#vi(i));return t}}t.StructTreeLayerBuilder=o},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TextAccessibilityManager=void 0,i(76);var n=i(97);class s{#bi=!1;#_i=null;#yi=new Map;#Pi=new Map;setTextMapping(e){this.#_i=e}static#Ei(e,t){const i=e.getBoundingClientRect(),n=t.getBoundingClientRect();if(0===i.width&&0===i.height)return 1;if(0===n.width&&0===n.height)return-1;const s=i.y,r=i.y+i.height,o=i.y+i.height/2,a=n.y,l=n.y+n.height,d=n.y+n.height/2;if(o<=a&&d>=r)return-1;if(d<=s&&o>=l)return 1;const h=i.x+i.width/2,c=n.x+n.width/2;return h-c}enable(){if(this.#bi)throw new Error("TextAccessibilityManager is already enabled.");if(!this.#_i)throw new Error("Text divs and strings have not been set.");if(this.#bi=!0,this.#_i=this.#_i.slice(),this.#_i.sort(s.#Ei),this.#yi.size>0){const e=this.#_i;for(const[t,i]of this.#yi){const n=document.getElementById(t);n?this.#Si(t,e[i]):this.#yi.delete(t)}}for(const[e,t]of this.#Pi)this.addPointerInTextLayer(e,t);this.#Pi.clear()}disable(){this.#bi&&(this.#Pi.clear(),this.#_i=null,this.#bi=!1)}removePointerInTextLayer(e){if(!this.#bi)return void this.#Pi.delete(e);const t=this.#_i;if(!t||0===t.length)return;const{id:i}=e,n=this.#yi.get(i);if(void 0===n)return;const s=t[n];this.#yi.delete(i);let r=s.getAttribute("aria-owns");r?.includes(i)&&(r=r.split(" ").filter((e=>e!==i)).join(" "),r?s.setAttribute("aria-owns",r):(s.removeAttribute("aria-owns"),s.setAttribute("role","presentation")))}#Si(e,t){const i=t.getAttribute("aria-owns");i?.includes(e)||t.setAttribute("aria-owns",i?`${i} ${e}`:e),t.removeAttribute("role")}addPointerInTextLayer(e,t){const{id:i}=e;if(!i)return null;if(!this.#bi)return this.#Pi.set(e,t),null;t&&this.removePointerInTextLayer(e);const r=this.#_i;if(!r||0===r.length)return null;const o=(0,n.binarySearchFirstItem)(r,(t=>s.#Ei(e,t)<0)),a=Math.max(0,o-1),l=r[a];this.#Si(i,l),this.#yi.set(i,a);const d=l.parentNode;return d?.classList.contains("markedContent")?d.id:null}moveElementInDOM(e,t,i,r){const o=this.addPointerInTextLayer(i,r);if(!e.hasChildNodes())return e.append(t),o;const a=Array.from(e.childNodes).filter((e=>e!==t));if(0===a.length)return o;const l=i||t,d=(0,n.binarySearchFirstItem)(a,(e=>s.#Ei(l,e)<0));return 0===d?a[0].before(t):a[d-1].after(t),o}}t.TextAccessibilityManager=s},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TextHighlighter=void 0,i(76),i(89);class n{constructor(e){let{findController:t,eventBus:i,pageIndex:n}=e;this.findController=t,this.matches=[],this.eventBus=i,this.pageIdx=n,this._onUpdateTextLayerMatches=null,this.textDivs=null,this.textContentItemsStr=null,this.enabled=!1}setTextMapping(e,t){this.textDivs=e,this.textContentItemsStr=t}enable(){if(!this.textDivs||!this.textContentItemsStr)throw new Error("Text divs and strings have not been set.");if(this.enabled)throw new Error("TextHighlighter is already enabled.");this.enabled=!0,this._onUpdateTextLayerMatches||(this._onUpdateTextLayerMatches=e=>{e.pageIndex!==this.pageIdx&&-1!==e.pageIndex||this._updateMatches()},this.eventBus._on("updatetextlayermatches",this._onUpdateTextLayerMatches)),this._updateMatches()}disable(){this.enabled&&(this.enabled=!1,this._onUpdateTextLayerMatches&&(this.eventBus._off("updatetextlayermatches",this._onUpdateTextLayerMatches),this._onUpdateTextLayerMatches=null),this._updateMatches(!0))}_convertMatches(e,t){if(!e)return[];const{textContentItemsStr:i}=this;let n=0,s=0;const r=i.length-1,o=[];for(let a=0,l=e.length;a<l;a++){let l=e[a];while(n!==r&&l>=s+i[n].length)s+=i[n].length,n++;n===i.length&&console.error("Could not find a matching mapping");const d={begin:{divIdx:n,offset:l-s}};l+=t[a];while(n!==r&&l>s+i[n].length)s+=i[n].length,n++;d.end={divIdx:n,offset:l-s},o.push(d)}return o}_renderMatches(e){if(0===e.length)return;const{findController:t,pageIdx:i}=this,{textContentItemsStr:n,textDivs:s}=this,r=i===t.selected.pageIdx,o=t.selected.matchIdx,a=t.state.highlightAll;let l=null;const d={divIdx:-1,offset:void 0};function h(e,t){const i=e.divIdx;return s[i].textContent="",c(i,0,e.offset,t)}function c(e,t,i,r){let o=s[e];if(o.nodeType===Node.TEXT_NODE){const t=document.createElement("span");o.before(t),t.append(o),s[e]=t,o=t}const a=n[e].substring(t,i),l=document.createTextNode(a);if(r){const e=document.createElement("span");return e.className=`${r} appended`,e.append(l),o.append(e),r.includes("selected")?e.offsetLeft:0}return o.append(l),0}let u=o,p=u+1;if(a)u=0,p=e.length;else if(!r)return;let g=-1,f=-1;for(let m=u;m<p;m++){const n=e[m],a=n.begin;if(a.divIdx===g&&a.offset===f)continue;g=a.divIdx,f=a.offset;const u=n.end,p=r&&m===o,v=p?" selected":"";let w=0;if(l&&a.divIdx===l.divIdx?c(l.divIdx,l.offset,a.offset):(null!==l&&c(l.divIdx,l.offset,d.offset),h(a)),a.divIdx===u.divIdx)w=c(a.divIdx,a.offset,u.offset,"highlight"+v);else{w=c(a.divIdx,a.offset,d.offset,"highlight begin"+v);for(let e=a.divIdx+1,t=u.divIdx;e<t;e++)s[e].className="highlight middle"+v;h(u,"highlight end"+v)}l=u,p&&t.scrollMatchIntoView({element:s[a.divIdx],selectedLeft:w,pageIndex:i,matchIndex:o})}l&&c(l.divIdx,l.offset,d.offset)}_updateMatches(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.enabled&&!e)return;const{findController:t,matches:i,pageIdx:n}=this,{textContentItemsStr:s,textDivs:r}=this;let o=-1;for(const d of i){const e=Math.max(o,d.begin.divIdx);for(let t=e,i=d.end.divIdx;t<=i;t++){const e=r[t];e.textContent=s[t],e.className=""}o=d.end.divIdx+1}if(!t?.highlightMatches||e)return;const a=t.pageMatches[n]||null,l=t.pageMatchesLength[n]||null;this.matches=this._convertMatches(a,l),this._renderMatches(this.matches)}}t.TextHighlighter=n},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TextLayerBuilder=void 0,i(76);var n=i(122),s=i(97);class r{#xt=!1;#Ci=0;#Li=0;#Ti=null;constructor(e){let{highlighter:t=null,accessibilityManager:i=null,isOffscreenCanvasSupported:n=!0,enablePermissions:s=!1}=e;this.textContentItemsStr=[],this.renderingDone=!1,this.textDivs=[],this.textDivProperties=new WeakMap,this.textLayerRenderTask=null,this.highlighter=t,this.accessibilityManager=i,this.isOffscreenCanvasSupported=n,this.#xt=!0===s,this.div=document.createElement("div"),this.div.className="textLayer",this.hide()}#Mi(){this.renderingDone=!0;const e=document.createElement("div");e.className="endOfContent",this.div.append(e),this.#Ii()}get numTextDivs(){return this.textDivs.length}async render(e){if(!this.#Ti)throw new Error('No "textContentSource" parameter specified.');const t=e.scale*(globalThis.devicePixelRatio||1),{rotation:i}=e;if(this.renderingDone){const s=i!==this.#Ci,r=t!==this.#Li;return(s||r)&&(this.hide(),(0,n.updateTextLayer)({container:this.div,viewport:e,textDivs:this.textDivs,textDivProperties:this.textDivProperties,isOffscreenCanvasSupported:this.isOffscreenCanvasSupported,mustRescale:r,mustRotate:s}),this.#Li=t,this.#Ci=i),void this.show()}this.cancel(),this.highlighter?.setTextMapping(this.textDivs,this.textContentItemsStr),this.accessibilityManager?.setTextMapping(this.textDivs),this.textLayerRenderTask=(0,n.renderTextLayer)({textContentSource:this.#Ti,container:this.div,viewport:e,textDivs:this.textDivs,textDivProperties:this.textDivProperties,textContentItemsStr:this.textContentItemsStr,isOffscreenCanvasSupported:this.isOffscreenCanvasSupported}),await this.textLayerRenderTask.promise,this.#Mi(),this.#Li=t,this.#Ci=i,this.show(),this.accessibilityManager?.enable()}hide(){this.div.hidden||(this.highlighter?.disable(),this.div.hidden=!0)}show(){this.div.hidden&&this.renderingDone&&(this.div.hidden=!1,this.highlighter?.enable())}cancel(){this.textLayerRenderTask&&(this.textLayerRenderTask.cancel(),this.textLayerRenderTask=null),this.highlighter?.disable(),this.accessibilityManager?.disable(),this.textContentItemsStr.length=0,this.textDivs.length=0,this.textDivProperties=new WeakMap}setTextContentSource(e){this.cancel(),this.#Ti=e}#Ii(){const{div:e}=this;e.addEventListener("mousedown",(t=>{const i=e.querySelector(".endOfContent");if(!i)return;let n=t.target!==e;if(n&&="none"!==getComputedStyle(i).getPropertyValue("-moz-user-select"),n){const n=e.getBoundingClientRect(),s=Math.max(0,(t.pageY-n.top)/n.height);i.style.top=(100*s).toFixed(2)+"%"}i.classList.add("active")})),e.addEventListener("mouseup",(()=>{const t=e.querySelector(".endOfContent");t&&(t.style.top="",t.classList.remove("active"))})),e.addEventListener("copy",(e=>{if(!this.#xt){const t=document.getSelection();e.clipboardData.setData("text/plain",(0,s.removeNullCharacters)((0,n.normalizeUnicode)(t.toString())))}e.preventDefault(),e.stopPropagation()}))}}t.TextLayerBuilder=r},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.XfaLayerBuilder=void 0;var n=i(122);class s{constructor(e){let{pageDiv:t,pdfPage:i,annotationStorage:n=null,linkService:s,xfaHtml:r=null}=e;this.pageDiv=t,this.pdfPage=i,this.annotationStorage=n,this.linkService=s,this.xfaHtml=r,this.div=null,this._cancelled=!1}async render(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"display";if("print"===t){const i={viewport:e.clone({dontFlip:!0}),div:this.div,xfaHtml:this.xfaHtml,annotationStorage:this.annotationStorage,linkService:this.linkService,intent:t},s=document.createElement("div");return this.pageDiv.append(s),i.div=s,n.XfaLayer.render(i)}const i=await this.pdfPage.getXfa();if(this._cancelled||!i)return{textDivs:[]};const s={viewport:e.clone({dontFlip:!0}),div:this.div,xfaHtml:i,annotationStorage:this.annotationStorage,linkService:this.linkService,intent:t};return this.div?n.XfaLayer.update(s):(this.div=document.createElement("div"),this.pageDiv.append(this.div),s.div=this.div,n.XfaLayer.render(s))}cancel(){this._cancelled=!0}hide(){this.div&&(this.div.hidden=!0)}}t.XfaLayerBuilder=s},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SecondaryToolbar=void 0,i(89);var n=i(97),s=i(147);class r{constructor(e,t){this.toolbar=e.toolbar,this.toggleButton=e.toggleButton,this.buttons=[{element:e.presentationModeButton,eventName:"presentationmode",close:!0},{element:e.printButton,eventName:"print",close:!0},{element:e.downloadButton,eventName:"download",close:!0},{element:e.viewBookmarkButton,eventName:null,close:!0},{element:e.firstPageButton,eventName:"firstpage",close:!0},{element:e.lastPageButton,eventName:"lastpage",close:!0},{element:e.pageRotateCwButton,eventName:"rotatecw",close:!1},{element:e.pageRotateCcwButton,eventName:"rotateccw",close:!1},{element:e.cursorSelectToolButton,eventName:"switchcursortool",eventDetails:{tool:n.CursorTool.SELECT},close:!0},{element:e.cursorHandToolButton,eventName:"switchcursortool",eventDetails:{tool:n.CursorTool.HAND},close:!0},{element:e.scrollPageButton,eventName:"switchscrollmode",eventDetails:{mode:n.ScrollMode.PAGE},close:!0},{element:e.scrollVerticalButton,eventName:"switchscrollmode",eventDetails:{mode:n.ScrollMode.VERTICAL},close:!0},{element:e.scrollHorizontalButton,eventName:"switchscrollmode",eventDetails:{mode:n.ScrollMode.HORIZONTAL},close:!0},{element:e.scrollWrappedButton,eventName:"switchscrollmode",eventDetails:{mode:n.ScrollMode.WRAPPED},close:!0},{element:e.spreadNoneButton,eventName:"switchspreadmode",eventDetails:{mode:n.SpreadMode.NONE},close:!0},{element:e.spreadOddButton,eventName:"switchspreadmode",eventDetails:{mode:n.SpreadMode.ODD},close:!0},{element:e.spreadEvenButton,eventName:"switchspreadmode",eventDetails:{mode:n.SpreadMode.EVEN},close:!0},{element:e.documentPropertiesButton,eventName:"documentproperties",close:!0}],this.buttons.push({element:e.openFileButton,eventName:"openfile",close:!0}),this.items={firstPage:e.firstPageButton,lastPage:e.lastPageButton,pageRotateCw:e.pageRotateCwButton,pageRotateCcw:e.pageRotateCcwButton},this.eventBus=t,this.opened=!1,this.#xi(),this.#Ai(e),this.#Di(e),this.#Ni(e),this.reset()}get isOpen(){return this.opened}setPageNumber(e){this.pageNumber=e,this.#h()}setPagesCount(e){this.pagesCount=e,this.#h()}reset(){this.pageNumber=0,this.pagesCount=0,this.#h(),this.eventBus.dispatch("secondarytoolbarreset",{source:this})}#h(){this.items.firstPage.disabled=this.pageNumber<=1,this.items.lastPage.disabled=this.pageNumber>=this.pagesCount,this.items.pageRotateCw.disabled=0===this.pagesCount,this.items.pageRotateCcw.disabled=0===this.pagesCount}#xi(){this.toggleButton.addEventListener("click",this.toggle.bind(this));for(const{element:e,eventName:t,close:i,eventDetails:n}of this.buttons)e.addEventListener("click",(s=>{null!==t&&this.eventBus.dispatch(t,{source:this,...n}),i&&this.close(),this.eventBus.dispatch("reporttelemetry",{source:this,details:{type:"buttons",data:{id:e.id}}})}))}#Ai(e){let{cursorSelectToolButton:t,cursorHandToolButton:i}=e;this.eventBus._on("cursortoolchanged",(e=>{let{tool:s}=e;(0,n.toggleCheckedBtn)(t,s===n.CursorTool.SELECT),(0,n.toggleCheckedBtn)(i,s===n.CursorTool.HAND)}))}#Di(e){let{scrollPageButton:t,scrollVerticalButton:i,scrollHorizontalButton:r,scrollWrappedButton:o,spreadNoneButton:a,spreadOddButton:l,spreadEvenButton:d}=e;const h=e=>{let{mode:h}=e;(0,n.toggleCheckedBtn)(t,h===n.ScrollMode.PAGE),(0,n.toggleCheckedBtn)(i,h===n.ScrollMode.VERTICAL),(0,n.toggleCheckedBtn)(r,h===n.ScrollMode.HORIZONTAL),(0,n.toggleCheckedBtn)(o,h===n.ScrollMode.WRAPPED);const c=this.pagesCount>s.PagesCountLimit.FORCE_SCROLL_MODE_PAGE;t.disabled=c,i.disabled=c,r.disabled=c,o.disabled=c;const u=h===n.ScrollMode.HORIZONTAL;a.disabled=u,l.disabled=u,d.disabled=u};this.eventBus._on("scrollmodechanged",h),this.eventBus._on("secondarytoolbarreset",(e=>{e.source===this&&h({mode:n.ScrollMode.VERTICAL})}))}#Ni(e){let{spreadNoneButton:t,spreadOddButton:i,spreadEvenButton:s}=e;const r=e=>{let{mode:r}=e;(0,n.toggleCheckedBtn)(t,r===n.SpreadMode.NONE),(0,n.toggleCheckedBtn)(i,r===n.SpreadMode.ODD),(0,n.toggleCheckedBtn)(s,r===n.SpreadMode.EVEN)};this.eventBus._on("spreadmodechanged",r),this.eventBus._on("secondarytoolbarreset",(e=>{e.source===this&&r({mode:n.SpreadMode.NONE})}))}open(){this.opened||(this.opened=!0,(0,n.toggleExpandedBtn)(this.toggleButton,!0,this.toolbar))}close(){this.opened&&(this.opened=!1,(0,n.toggleExpandedBtn)(this.toggleButton,!1,this.toolbar))}toggle(){this.opened?this.close():this.open()}}t.SecondaryToolbar=r},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Toolbar=void 0,i(89);var n=i(97),s=i(122);const r="visiblePageIsLoading";class o{#ki=!1;constructor(e,t,i){this.toolbar=e.container,this.eventBus=t,this.l10n=i,this.buttons=[{element:e.previous,eventName:"previouspage"},{element:e.next,eventName:"nextpage"},{element:e.zoomIn,eventName:"zoomin"},{element:e.zoomOut,eventName:"zoomout"},{element:e.print,eventName:"print"},{element:e.download,eventName:"download"},{element:e.editorFreeTextButton,eventName:"switchannotationeditormode",eventDetails:{get mode(){const{classList:t}=e.editorFreeTextButton;return t.contains("toggled")?s.AnnotationEditorType.NONE:s.AnnotationEditorType.FREETEXT}}},{element:e.editorInkButton,eventName:"switchannotationeditormode",eventDetails:{get mode(){const{classList:t}=e.editorInkButton;return t.contains("toggled")?s.AnnotationEditorType.NONE:s.AnnotationEditorType.INK}}},{element:e.editorStampButton,eventName:"switchannotationeditormode",eventDetails:{get mode(){const{classList:t}=e.editorStampButton;return t.contains("toggled")?s.AnnotationEditorType.NONE:s.AnnotationEditorType.STAMP}}}],this.buttons.push({element:e.openFile,eventName:"openfile"}),this.items={numPages:e.numPages,pageNumber:e.pageNumber,scaleSelect:e.scaleSelect,customScaleOption:e.customScaleOption,previous:e.previous,next:e.next,zoomIn:e.zoomIn,zoomOut:e.zoomOut},this.#O(e),this.reset()}setPageNumber(e,t){this.pageNumber=e,this.pageLabel=t,this.#h(!1)}setPagesCount(e,t){this.pagesCount=e,this.hasPageLabels=t,this.#h(!0)}setPageScale(e,t){this.pageScaleValue=(e||t).toString(),this.pageScale=t,this.#h(!1)}reset(){this.pageNumber=0,this.pageLabel=null,this.hasPageLabels=!1,this.pagesCount=0,this.pageScaleValue=n.DEFAULT_SCALE_VALUE,this.pageScale=n.DEFAULT_SCALE,this.#h(!0),this.updateLoadingIndicatorState(),this.eventBus.dispatch("toolbarreset",{source:this})}#O(e){const{pageNumber:t,scaleSelect:i}=this.items,n=this;for(const{element:s,eventName:r,eventDetails:o}of this.buttons)s.addEventListener("click",(e=>{null!==r&&this.eventBus.dispatch(r,{source:this,...o})}));t.addEventListener("click",(function(){this.select()})),t.addEventListener("change",(function(){n.eventBus.dispatch("pagenumberchanged",{source:n,value:this.value})})),i.addEventListener("change",(function(){"custom"!==this.value&&n.eventBus.dispatch("scalechanged",{source:n,value:this.value})})),i.addEventListener("click",(function(e){const t=e.target;this.value===n.pageScaleValue&&"OPTION"===t.tagName.toUpperCase()&&this.blur()})),i.oncontextmenu=s.noContextMenu,this.eventBus._on("localized",(()=>{this.#ki=!0,this.#Bi(),this.#h(!0)})),this.#Oi(e)}#Oi(e){let{editorFreeTextButton:t,editorFreeTextParamsToolbar:i,editorInkButton:r,editorInkParamsToolbar:o,editorStampButton:a,editorStampParamsToolbar:l}=e;const d=e=>{let{mode:d}=e;(0,n.toggleCheckedBtn)(t,d===s.AnnotationEditorType.FREETEXT,i),(0,n.toggleCheckedBtn)(r,d===s.AnnotationEditorType.INK,o),(0,n.toggleCheckedBtn)(a,d===s.AnnotationEditorType.STAMP,l);const h=d===s.AnnotationEditorType.DISABLE;t.disabled=h,r.disabled=h,a.disabled=h};this.eventBus._on("annotationeditormodechanged",d),this.eventBus._on("toolbarreset",(e=>{e.source===this&&d({mode:s.AnnotationEditorType.DISABLE})}))}#h(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(!this.#ki)return;const{pageNumber:t,pagesCount:i,pageScaleValue:s,pageScale:r,items:o}=this;e&&(this.hasPageLabels?o.pageNumber.type="text":(o.pageNumber.type="number",this.l10n.get("of_pages",{pagesCount:i}).then((e=>{o.numPages.textContent=e}))),o.pageNumber.max=i),this.hasPageLabels?(o.pageNumber.value=this.pageLabel,this.l10n.get("page_of_pages",{pageNumber:t,pagesCount:i}).then((e=>{o.numPages.textContent=e}))):o.pageNumber.value=t,o.previous.disabled=t<=1,o.next.disabled=t>=i,o.zoomOut.disabled=r<=n.MIN_SCALE,o.zoomIn.disabled=r>=n.MAX_SCALE,this.l10n.get("page_scale_percent",{scale:Math.round(1e4*r)/100}).then((e=>{let t=!1;for(const i of o.scaleSelect.options)i.value===s?(i.selected=!0,t=!0):i.selected=!1;t||(o.customScaleOption.textContent=e,o.customScaleOption.selected=!0)}))}updateLoadingIndicatorState(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];const{pageNumber:t}=this.items;t.classList.toggle(r,e)}async#Bi(){const{items:e,l10n:t}=this,i=Promise.all([t.get("page_scale_auto"),t.get("page_scale_actual"),t.get("page_scale_fit"),t.get("page_scale_width")]);await n.animationStarted;const s=getComputedStyle(e.scaleSelect),r=parseFloat(s.getPropertyValue("--scale-select-width")),o=document.createElement("canvas"),a=o.getContext("2d",{alpha:!1});a.font=`${s.fontSize} ${s.fontFamily}`;let l=0;for(const n of await i){const{width:e}=a.measureText(n);e>l&&(l=e)}if(l+=.3*r,l>r){const t=e.scaleSelect.parentNode;t.style.setProperty("--scale-select-width",`${l}px`)}o.width=0,o.height=0}}t.Toolbar=o},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ViewHistory=void 0,i(2),i(89);const n=20;class s{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:n;this.fingerprint=e,this.cacheSize=t,this._initializedPromise=this._readFromStorage().then((e=>{const t=JSON.parse(e||"{}");let i=-1;if(Array.isArray(t.files)){while(t.files.length>=this.cacheSize)t.files.shift();for(let e=0,n=t.files.length;e<n;e++){const n=t.files[e];if(n.fingerprint===this.fingerprint){i=e;break}}}else t.files=[];-1===i&&(i=t.files.push({fingerprint:this.fingerprint})-1),this.file=t.files[i],this.database=t}))}async _writeToStorage(){const e=JSON.stringify(this.database);localStorage.setItem("pdfjs.history",e)}async _readFromStorage(){return localStorage.getItem("pdfjs.history")}async set(e,t){return await this._initializedPromise,this.file[e]=t,this._writeToStorage()}async setMultiple(e){await this._initializedPromise;for(const t in e)this.file[t]=e[t];return this._writeToStorage()}async get(e,t){await this._initializedPromise;const i=this.file[e];return void 0!==i?i:t}async getMultiple(e){await this._initializedPromise;const t=Object.create(null);for(const i in e){const n=this.file[i];t[i]=void 0!==n?n:e[i]}return t}}t.ViewHistory=s},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.BasePreferences=void 0,i(76);i(123);class n{#Vi=Object.freeze({annotationEditorMode:0,annotationMode:2,cursorToolOnLoad:0,defaultZoomDelay:400,defaultZoomValue:"",disablePageLabels:!1,enablePermissions:!1,enablePrintAutoRotate:!0,enableScripting:!0,enableStampEditor:!0,externalLinkTarget:0,historyUpdateUrl:!1,ignoreDestinationZoom:!1,forcePageColors:!1,pageColorsBackground:"Canvas",pageColorsForeground:"CanvasText",pdfBugEnabled:!1,sidebarViewOnLoad:-1,scrollModeOnLoad:-1,spreadModeOnLoad:-1,textLayerMode:1,viewerCssTheme:0,viewOnLoad:0,disableAutoFetch:!1,disableFontFace:!1,disableRange:!1,disableStream:!1,enableXfa:!0});#Ri=Object.create(null);#Fi=null;constructor(){if(this.constructor===n)throw new Error("Cannot initialize BasePreferences.");this.#Fi=this._readFromStorage(this.#Vi).then((e=>{for(const t in this.#Vi){const i=e?.[t];typeof i===typeof this.#Vi[t]&&(this.#Ri[t]=i)}}))}async _writeToStorage(e){throw new Error("Not implemented: _writeToStorage")}async _readFromStorage(e){throw new Error("Not implemented: _readFromStorage")}async reset(){await this.#Fi;const e=this.#Ri;return this.#Ri=Object.create(null),this._writeToStorage(this.#Vi).catch((t=>{throw this.#Ri=e,t}))}async set(e,t){await this.#Fi;const i=this.#Vi[e],n=this.#Ri;if(void 0===i)throw new Error(`Set preference: "${e}" is undefined.`);if(void 0===t)throw new Error("Set preference: no value is specified.");const s=typeof t,r=typeof i;if(s!==r){if("number"!==s||"string"!==r)throw new Error(`Set preference: "${t}" is a ${s}, expected a ${r}.`);t=t.toString()}else if("number"===s&&!Number.isInteger(t))throw new Error(`Set preference: "${t}" must be an integer.`);return this.#Ri[e]=t,this._writeToStorage(this.#Ri).catch((e=>{throw this.#Ri=n,e}))}async get(e){await this.#Fi;const t=this.#Vi[e];if(void 0===t)throw new Error(`Get preference: "${e}" is undefined.`);return this.#Ri[e]??t}async getAll(){await this.#Fi;const e=Object.create(null);for(const t in this.#Vi)e[t]=this.#Ri[t]??this.#Vi[t];return e}}t.BasePreferences=n},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DownloadManager=void 0,i(76),i(92),i(94),i(95);var n=i(122);function s(e,t){const i=document.createElement("a");if(!i.click)throw new Error('DownloadManager: "a.click()" is not supported.');i.href=e,i.target="_parent","download"in i&&(i.download=t),(document.body||document.documentElement).append(i),i.click(),i.remove()}class r{#Ui=new WeakMap;downloadUrl(e,t,i){(0,n.createValidAbsoluteUrl)(e,"http://example.com")?s(e+"#pdfjs.action=download",t):console.error(`downloadUrl - not a valid URL: ${e}`)}downloadData(e,t,i){const n=URL.createObjectURL(new Blob([e],{type:i}));s(n,t)}openOrDownloadData(e,t,i){const s=(0,n.isPdfFile)(i),r=s?"application/pdf":"";if(s){let n,s=this.#Ui.get(e);s||(s=URL.createObjectURL(new Blob([t],{type:r})),this.#Ui.set(e,s)),n="?file="+encodeURIComponent(s+"#"+i);try{return window.open(n),!0}catch(o){console.error(`openOrDownloadData: ${o}`),URL.revokeObjectURL(s),this.#Ui.delete(e)}}return this.downloadData(t,i,r),!1}download(e,t,i,n){const r=URL.createObjectURL(e);s(r,i)}}t.DownloadManager=r},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GenericL10n=void 0,i(163);var n=i(148);const s={en:"en-US",es:"es-ES",fy:"fy-NL",ga:"ga-IE",gu:"gu-IN",hi:"hi-IN",hy:"hy-AM",nb:"nb-NO",ne:"ne-NP",nn:"nn-NO",pa:"pa-IN",pt:"pt-PT",sv:"sv-SE",zh:"zh-CN"};function r(e){return s[e?.toLowerCase()]||e}class o{constructor(e){const{webL10n:t}=document;this._lang=e,this._ready=new Promise(((i,n)=>{t.setLanguage(r(e),(()=>{i(t)}))}))}async getLanguage(){const e=await this._ready;return e.getLanguage()}async getDirection(){const e=await this._ready;return e.getDirection()}async get(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:(0,n.getL10nFallback)(e,t);const s=await this._ready;return s.get(e,t,i)}async translate(e){const t=await this._ready;return t.translate(e)}}t.GenericL10n=o},(e,t,i)=>{i(2),document.webL10n=function(e,t){var i={},n="",s="textContent",r="",o={},a="loading",l=!0;function d(){return t.querySelectorAll('link[type="application/l10n"]')}function h(){var e=t.querySelector('script[type="application/l10n"]');return e?JSON.parse(e.innerHTML):null}function c(e){return e?e.querySelectorAll("*[data-l10n-id]"):[]}function u(e){if(!e)return{};var t=e.getAttribute("data-l10n-id"),i=e.getAttribute("data-l10n-args"),n={};if(i)try{n=JSON.parse(i)}catch(s){console.warn("could not parse arguments for #"+t)}return{id:t,args:n}}function p(e,t,i){t=t||function(e){},i=i||function(){};var n=new XMLHttpRequest;n.open("GET",e,l),n.overrideMimeType&&n.overrideMimeType("text/plain; charset=utf-8"),n.onreadystatechange=function(){4==n.readyState&&(200==n.status||0===n.status?t(n.responseText):i())},n.onerror=i,n.ontimeout=i;try{n.send(null)}catch(s){i()}}function g(e,t,r,o){var a=e.replace(/[^\/]*$/,"")||"./";function l(e){return e.lastIndexOf("\\")<0?e:e.replace(/\\\\/g,"\\").replace(/\\n/g,"\n").replace(/\\r/g,"\r").replace(/\\t/g,"\t").replace(/\\b/g,"\b").replace(/\\f/g,"\f").replace(/\\{/g,"{").replace(/\\}/g,"}").replace(/\\"/g,'"').replace(/\\'/g,"'")}function d(e,i){var n={},s=/^\s*|\s*$/,r=/^\s*#|^\s*$/,o=/^\s*\[(.*)\]\s*$/,d=/^\s*@import\s+url\((.*)\)\s*$/i,h=/^([^=\s]*)\s*=\s*(.+)$/;function c(e,i,c){var p=e.replace(s,"").split(/[\r\n]+/),g="*",f=t.split("-",1)[0],m=!1,v="";function w(){while(1){if(!p.length)return void c();var e=p.shift();if(!r.test(e)){if(i){if(v=o.exec(e),v){g=v[1].toLowerCase(),m="*"!==g&&g!==t&&g!==f;continue}if(m)continue;if(v=d.exec(e),v)return void u(a+v[1],w)}var s=e.match(h);s&&3==s.length&&(n[s[1]]=l(s[2]))}}}w()}function u(e,t){p(e,(function(e){c(e,!1,t)}),(function(){console.warn(e+" not found."),t()}))}c(e,!0,(function(){i(n)}))}p(e,(function(e){n+=e,d(e,(function(e){for(var t in e){var n,o,a=t.lastIndexOf(".");a>0?(n=t.substring(0,a),o=t.substring(a+1)):(n=t,o=s),i[n]||(i[n]={}),i[n][o]=e[t]}r&&r()}))}),o)}function f(e,t){e&&(e=e.toLowerCase()),t=t||function(){},m(),r=e;var n=d(),s=n.length;if(0!==s){var o=null,l=0;o=function(){l++,l>=s&&(t(),a="complete")};for(var c=0;c<s;c++){var u=new w(n[c]);u.load(e,o)}}else{var p=h();if(p&&p.locales&&p.default_locale){if(console.log("using the embedded JSON directory, early way out"),i=p.locales[e],!i){var f=p.default_locale.toLowerCase();for(var v in p.locales){if(v=v.toLowerCase(),v===e){i=p.locales[e];break}v===f&&(i=p.locales[f])}}t()}else console.log("no resource to load, early way out");a="complete"}function w(e){var t=e.href;this.load=function(e,i){g(t,e,i,(function(){console.warn(t+" not found."),console.warn('"'+e+'" resource not found'),r="",i()}))}}}function m(){i={},n="",r=""}function v(e){var t={af:3,ak:4,am:4,ar:1,asa:3,az:0,be:11,bem:3,bez:3,bg:3,bh:4,bm:0,bn:3,bo:0,br:20,brx:3,bs:11,ca:3,cgg:3,chr:3,cs:12,cy:17,da:3,de:3,dv:3,dz:0,ee:3,el:3,en:3,eo:3,es:3,et:3,eu:3,fa:0,ff:5,fi:3,fil:4,fo:3,fr:5,fur:3,fy:3,ga:8,gd:24,gl:3,gsw:3,gu:3,guw:4,gv:23,ha:3,haw:3,he:2,hi:4,hr:11,hu:0,id:0,ig:0,ii:0,is:3,it:3,iu:7,ja:0,jmc:3,jv:0,ka:0,kab:5,kaj:3,kcg:3,kde:0,kea:0,kk:3,kl:3,km:0,kn:0,ko:0,ksb:3,ksh:21,ku:3,kw:7,lag:18,lb:3,lg:3,ln:4,lo:0,lt:10,lv:6,mas:3,mg:4,mk:16,ml:3,mn:3,mo:9,mr:3,ms:0,mt:15,my:0,nah:3,naq:7,nb:3,nd:3,ne:3,nl:3,nn:3,no:3,nr:3,nso:4,ny:3,nyn:3,om:3,or:3,pa:3,pap:3,pl:13,ps:3,pt:3,rm:3,ro:9,rof:3,ru:11,rwk:3,sah:0,saq:3,se:7,seh:3,ses:0,sg:0,sh:11,shi:19,sk:12,sl:14,sma:7,smi:7,smj:7,smn:7,sms:7,sn:3,so:3,sq:3,sr:11,ss:3,ssy:3,st:3,sv:3,sw:3,syr:3,ta:3,te:3,teo:3,th:0,ti:4,tig:3,tk:3,tl:4,tn:3,to:0,tr:0,ts:3,tzm:22,uk:11,ur:3,ve:3,vi:0,vun:3,wa:4,wae:3,wo:0,xh:3,xog:3,yo:0,zh:0,zu:3};function i(e,t){return-1!==t.indexOf(e)}function n(e,t,i){return t<=e&&e<=i}var s={0:function(e){return"other"},1:function(e){return n(e%100,3,10)?"few":0===e?"zero":n(e%100,11,99)?"many":2==e?"two":1==e?"one":"other"},2:function(e){return 0!==e&&e%10===0?"many":2==e?"two":1==e?"one":"other"},3:function(e){return 1==e?"one":"other"},4:function(e){return n(e,0,1)?"one":"other"},5:function(e){return n(e,0,2)&&2!=e?"one":"other"},6:function(e){return 0===e?"zero":e%10==1&&e%100!=11?"one":"other"},7:function(e){return 2==e?"two":1==e?"one":"other"},8:function(e){return n(e,3,6)?"few":n(e,7,10)?"many":2==e?"two":1==e?"one":"other"},9:function(e){return 0===e||1!=e&&n(e%100,1,19)?"few":1==e?"one":"other"},10:function(e){return n(e%10,2,9)&&!n(e%100,11,19)?"few":e%10!=1||n(e%100,11,19)?"other":"one"},11:function(e){return n(e%10,2,4)&&!n(e%100,12,14)?"few":e%10===0||n(e%10,5,9)||n(e%100,11,14)?"many":e%10==1&&e%100!=11?"one":"other"},12:function(e){return n(e,2,4)?"few":1==e?"one":"other"},13:function(e){return n(e%10,2,4)&&!n(e%100,12,14)?"few":1!=e&&n(e%10,0,1)||n(e%10,5,9)||n(e%100,12,14)?"many":1==e?"one":"other"},14:function(e){return n(e%100,3,4)?"few":e%100==2?"two":e%100==1?"one":"other"},15:function(e){return 0===e||n(e%100,2,10)?"few":n(e%100,11,19)?"many":1==e?"one":"other"},16:function(e){return e%10==1&&11!=e?"one":"other"},17:function(e){return 3==e?"few":0===e?"zero":6==e?"many":2==e?"two":1==e?"one":"other"},18:function(e){return 0===e?"zero":n(e,0,2)&&0!==e&&2!=e?"one":"other"},19:function(e){return n(e,2,10)?"few":n(e,0,1)?"one":"other"},20:function(e){return!n(e%10,3,4)&&e%10!=9||n(e%100,10,19)||n(e%100,70,79)||n(e%100,90,99)?e%1e6===0&&0!==e?"many":e%10!=2||i(e%100,[12,72,92])?e%10!=1||i(e%100,[11,71,91])?"other":"one":"two":"few"},21:function(e){return 0===e?"zero":1==e?"one":"other"},22:function(e){return n(e,0,1)||n(e,11,99)?"one":"other"},23:function(e){return n(e%10,1,2)||e%20===0?"one":"other"},24:function(e){return n(e,3,10)||n(e,13,19)?"few":i(e,[2,12])?"two":i(e,[1,11])?"one":"other"}},r=t[e.replace(/-.*$/,"")];return r in s?s[r]:(console.warn("plural form unknown for ["+e+"]"),function(){return"other"})}function w(e,t,n){var s=i[e];if(!s){if(console.warn("#"+e+" is undefined."),!n)return null;s=n}var r={};for(var o in s){var a=s[o];a=b(a,t,e,o),a=_(a,t,e),r[o]=a}return r}function b(e,t,n,s){var r=/\{\[\s*([a-zA-Z]+)\(([a-zA-Z]+)\)\s*\]\}/,a=r.exec(e);if(!a||!a.length)return e;var l,d=a[1],h=a[2];if(t&&h in t?l=t[h]:h in i&&(l=i[h]),d in o){var c=o[d];e=c(e,l,n,s)}return e}function _(e,t,n){var s=/\{\{\s*(.+?)\s*\}\}/g;return e.replace(s,(function(e,s){return t&&s in t?t[s]:s in i?i[s]:(console.log("argument {{"+s+"}} for #"+n+" is undefined."),e)}))}function y(e){var i=u(e);if(i.id){var n=w(i.id,i.args);if(n){if(n[s]){if(0===P(e))e[s]=n[s];else{for(var r=e.childNodes,o=!1,a=0,l=r.length;a<l;a++)3===r[a].nodeType&&/\S/.test(r[a].nodeValue)&&(o?r[a].nodeValue="":(r[a].nodeValue=n[s],o=!0));if(!o){var d=t.createTextNode(n[s]);e.prepend(d)}}delete n[s]}for(var h in n)e[h]=n[h]}else console.warn("#"+i.id+" is undefined.")}}function P(e){if(e.children)return e.children.length;if("undefined"!==typeof e.childElementCount)return e.childElementCount;for(var t=0,i=0;i<e.childNodes.length;i++)t+=1===e.nodeType?1:0;return t}function E(e){e=e||t.documentElement;for(var i=c(e),n=i.length,s=0;s<n;s++)y(i[s]);y(e)}return o.plural=function(e,t,n,a){var l=parseFloat(t);if(isNaN(l))return e;if(a!=s)return e;o._pluralRules||(o._pluralRules=v(r));var d="["+o._pluralRules(l)+"]";return 0===l&&n+"[zero]"in i?e=i[n+"[zero]"][a]:1==l&&n+"[one]"in i?e=i[n+"[one]"][a]:2==l&&n+"[two]"in i?e=i[n+"[two]"][a]:n+d in i?e=i[n+d][a]:n+"[other]"in i&&(e=i[n+"[other]"][a]),e},{get:function(e,t,i){var n,r=e.lastIndexOf("."),o=s;r>0&&(o=e.substring(r+1),e=e.substring(0,r)),i&&(n={},n[o]=i);var a=w(e,t,n);return a&&o in a?a[o]:"{{"+e+"}}"},getData:function(){return i},getText:function(){return n},getLanguage:function(){return r},setLanguage:function(e,t){f(e,(function(){t&&t()}))},getDirection:function(){var e=["ar","he","fa","ps","ur"],t=r.split("-",1)[0];return e.indexOf(t)>=0?"rtl":"ltr"},translate:E,getReadyState:function(){return a},ready:function(i){i&&("complete"==a||"interactive"==a?e.setTimeout((function(){i()})):t.addEventListener&&t.addEventListener("localized",(function e(){t.removeEventListener("localized",e),i()})))}}}(window,document)},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GenericScripting=void 0,t.docProperties=s;var n=i(122);async function s(e){const t="",i=t.split("#")[0];let{info:s,metadata:r,contentDispositionFilename:o,contentLength:a}=await e.getMetadata();if(!a){const{length:t}=await e.getDownloadInfo();a=t}return{...s,baseURL:i,filesize:a,filename:o||(0,n.getPdfFilenameFromUrl)(t),metadata:r?.getRaw(),authors:r?.get("dc:creator"),numPages:e.numPages,URL:t}}class r{constructor(e){this._ready=(0,n.loadScript)(e,!0).then((()=>window.pdfjsSandbox.QuickJSSandbox()))}async createSandbox(e){const t=await this._ready;t.create(e)}async dispatchEventInSandbox(e){const t=await this._ready;setTimeout((()=>t.dispatchEvent(e)),0)}async destroySandbox(){const e=await this._ready;e.nukeSandbox()}}t.GenericScripting=r},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPrintService=void 0,i(92),i(94),i(95),i(76);var n=i(122),s=i(75),r=i(166);let o=null,a=null,l=null;function d(e,t,i,s,r,a,l){const d=o.scratchCanvas,h=r/n.PixelsPerInch.PDF;d.width=Math.floor(s.width*h),d.height=Math.floor(s.height*h);const c=d.getContext("2d");return c.save(),c.fillStyle="rgb(255, 255, 255)",c.fillRect(0,0,d.width,d.height),c.restore(),Promise.all([t.getPage(i),l]).then((function(e){let[t,i]=e;const r={canvasContext:c,transform:[h,0,0,h,0,0],viewport:t.getViewport({scale:1,rotation:s.rotation}),intent:"print",annotationMode:n.AnnotationMode.ENABLE_STORAGE,optionalContentConfigPromise:a,printAnnotationStorage:i};return t.render(r).promise}))}class h{constructor(e,t,i,n){let s=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,r=arguments.length>5&&void 0!==arguments[5]?arguments[5]:null,o=arguments.length>6?arguments[6]:void 0;this.pdfDocument=e,this.pagesOverview=t,this.printContainer=i,this._printResolution=n||150,this._optionalContentConfigPromise=s||e.getOptionalContentConfig(),this._printAnnotationStoragePromise=r||Promise.resolve(),this.l10n=o,this.currentPage=-1,this.scratchCanvas=document.createElement("canvas")}layout(){this.throwIfInactive();const e=document.querySelector("body");e.setAttribute("data-pdfjsprinting",!0);const{width:t,height:i}=this.pagesOverview[0],n=this.pagesOverview.every((e=>e.width===t&&e.height===i));n||console.warn("Not all pages have the same size. The printed result may be incorrect!"),this.pageStyleSheet=document.createElement("style"),this.pageStyleSheet.textContent=`@page { size: ${t}pt ${i}pt;}`,e.append(this.pageStyleSheet)}destroy(){if(o!==this)return;this.printContainer.textContent="";const e=document.querySelector("body");e.removeAttribute("data-pdfjsprinting"),this.pageStyleSheet&&(this.pageStyleSheet.remove(),this.pageStyleSheet=null),this.scratchCanvas.width=this.scratchCanvas.height=0,this.scratchCanvas=null,o=null,m().then((function(){l.active===a&&l.close(a)}))}renderPages(){if(this.pdfDocument.isPureXfa)return(0,r.getXfaHtmlForPrinting)(this.printContainer,this.pdfDocument),Promise.resolve();const e=this.pagesOverview.length,t=(i,n)=>{if(this.throwIfInactive(),++this.currentPage>=e)return g(e,e,this.l10n),void i();const s=this.currentPage;g(s,e,this.l10n),d(this,this.pdfDocument,s+1,this.pagesOverview[s],this._printResolution,this._optionalContentConfigPromise,this._printAnnotationStoragePromise).then(this.useRenderedPage.bind(this)).then((function(){t(i,n)}),n)};return new Promise(t)}useRenderedPage(){this.throwIfInactive();const e=document.createElement("img"),t=this.scratchCanvas;"toBlob"in t?t.toBlob((function(t){e.src=URL.createObjectURL(t)})):e.src=t.toDataURL();const i=document.createElement("div");return i.className="printedPage",i.append(e),this.printContainer.append(i),new Promise((function(t,i){e.onload=t,e.onerror=i}))}performPrint(){return this.throwIfInactive(),new Promise((e=>{setTimeout((()=>{this.active?(c.call(window),setTimeout(e,20)):e()}),0)}))}get active(){return this===o}throwIfInactive(){if(!this.active)throw new Error("This print request was cancelled or completed.")}}t.PDFPrintService=h;const c=window.print;function u(e){const t=new CustomEvent(e,{bubbles:!1,cancelable:!1,detail:"custom"});window.dispatchEvent(t)}function p(){o&&(o.destroy(),u("afterprint"))}function g(e,t,i){a||=document.getElementById("printServiceDialog");const n=Math.round(100*e/t),s=a.querySelector("progress"),r=a.querySelector(".relative-progress");s.value=n,i.get("print_progress_percent",{progress:n}).then((e=>{r.textContent=e}))}if(window.print=function(){if(o)console.warn("Ignored window.print() because of a pending print job.");else{m().then((function(){o&&l.open(a)}));try{u("beforeprint")}finally{if(!o)return console.error("Expected print service to be initialized."),void m().then((function(){l.active===a&&l.close(a)}));const e=o;o.renderPages().then((function(){return e.performPrint()})).catch((function(){})).then((function(){e.active&&p()}))}}},window.addEventListener("keydown",(function(e){80!==e.keyCode||!e.ctrlKey&&!e.metaKey||e.altKey||e.shiftKey&&!window.chrome&&!window.opera||(window.print(),e.preventDefault(),e.stopImmediatePropagation())}),!0),"onbeforeprint"in window){const e=function(e){"custom"!==e.detail&&e.stopImmediatePropagation()};window.addEventListener("beforeprint",e),window.addEventListener("afterprint",e)}let f;function m(){if(!f){if(l=s.PDFViewerApplication.overlayManager,!l)throw new Error("The overlay manager has not yet been initialized.");a||=document.getElementById("printServiceDialog"),f=l.register(a,!0),document.getElementById("printCancel").onclick=p,a.addEventListener("close",p)}return f}s.PDFPrintServiceFactory.instance={supportsPrinting:!0,createPrintService(e,t,i,n,s,r,a){if(o)throw new Error("The print service is created and active.");return o=new h(e,t,i,n,s,r,a),o}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.getXfaHtmlForPrinting=o;var n=i(122),s=i(125),r=i(156);function o(e,t){const i=t.allXfaHtml,o=new s.SimpleLinkService,a=Math.round(100*n.PixelsPerInch.PDF_TO_CSS_UNITS)/100;for(const s of i.children){const i=document.createElement("div");i.className="xfaPrintedPage",e.append(i);const l=new r.XfaLayerBuilder({pageDiv:i,pdfPage:null,annotationStorage:t.annotationStorage,linkService:o,xfaHtml:s}),d=(0,n.getXfaPageViewport)(s,{scale:a});l.render(d,"print")}}}],t={};function i(n){var s=t[n];if(void 0!==s)return s.exports;var r=t[n]={exports:{}};return e[n].call(r.exports,r,r.exports,i),r.exports}var n={};(()=>{var e=n;Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"PDFViewerApplication",{enumerable:!0,get:function(){return o.PDFViewerApplication}}),e.PDFViewerApplicationConstants=void 0,Object.defineProperty(e,"PDFViewerApplicationOptions",{enumerable:!0,get:function(){return s.AppOptions}}),i(1),i(165);var t=i(97),s=i(123),r=i(125),o=i(75);const a={LinkTarget:r.LinkTarget,RenderingStates:t.RenderingStates,ScrollMode:t.ScrollMode,SpreadMode:t.SpreadMode};function l(){return{appContainer:document.body,mainContainer:document.getElementById("viewerContainer"),viewerContainer:document.getElementById("viewer"),toolbar:{container:document.getElementById("toolbarViewer"),numPages:document.getElementById("numPages"),pageNumber:document.getElementById("pageNumber"),scaleSelect:document.getElementById("scaleSelect"),customScaleOption:document.getElementById("customScaleOption"),previous:document.getElementById("previous"),next:document.getElementById("next"),zoomIn:document.getElementById("zoomIn"),zoomOut:document.getElementById("zoomOut"),viewFind:document.getElementById("viewFind"),openFile:document.getElementById("openFile"),print:document.getElementById("print"),editorFreeTextButton:document.getElementById("editorFreeText"),editorFreeTextParamsToolbar:document.getElementById("editorFreeTextParamsToolbar"),editorInkButton:document.getElementById("editorInk"),editorInkParamsToolbar:document.getElementById("editorInkParamsToolbar"),editorStampButton:document.getElementById("editorStamp"),editorStampParamsToolbar:document.getElementById("editorStampParamsToolbar"),download:document.getElementById("download")},secondaryToolbar:{toolbar:document.getElementById("secondaryToolbar"),toggleButton:document.getElementById("secondaryToolbarToggle"),presentationModeButton:document.getElementById("presentationMode"),openFileButton:document.getElementById("secondaryOpenFile"),printButton:document.getElementById("secondaryPrint"),downloadButton:document.getElementById("secondaryDownload"),viewBookmarkButton:document.getElementById("viewBookmark"),firstPageButton:document.getElementById("firstPage"),lastPageButton:document.getElementById("lastPage"),pageRotateCwButton:document.getElementById("pageRotateCw"),pageRotateCcwButton:document.getElementById("pageRotateCcw"),cursorSelectToolButton:document.getElementById("cursorSelectTool"),cursorHandToolButton:document.getElementById("cursorHandTool"),scrollPageButton:document.getElementById("scrollPage"),scrollVerticalButton:document.getElementById("scrollVertical"),scrollHorizontalButton:document.getElementById("scrollHorizontal"),scrollWrappedButton:document.getElementById("scrollWrapped"),spreadNoneButton:document.getElementById("spreadNone"),spreadOddButton:document.getElementById("spreadOdd"),spreadEvenButton:document.getElementById("spreadEven"),documentPropertiesButton:document.getElementById("documentProperties")},sidebar:{outerContainer:document.getElementById("outerContainer"),sidebarContainer:document.getElementById("sidebarContainer"),toggleButton:document.getElementById("sidebarToggle"),resizer:document.getElementById("sidebarResizer"),thumbnailButton:document.getElementById("viewThumbnail"),outlineButton:document.getElementById("viewOutline"),attachmentsButton:document.getElementById("viewAttachments"),layersButton:document.getElementById("viewLayers"),thumbnailView:document.getElementById("thumbnailView"),outlineView:document.getElementById("outlineView"),attachmentsView:document.getElementById("attachmentsView"),layersView:document.getElementById("layersView"),outlineOptionsContainer:document.getElementById("outlineOptionsContainer"),currentOutlineItemButton:document.getElementById("currentOutlineItem")},findBar:{bar:document.getElementById("findbar"),toggleButton:document.getElementById("viewFind"),findField:document.getElementById("findInput"),highlightAllCheckbox:document.getElementById("findHighlightAll"),caseSensitiveCheckbox:document.getElementById("findMatchCase"),matchDiacriticsCheckbox:document.getElementById("findMatchDiacritics"),entireWordCheckbox:document.getElementById("findEntireWord"),findMsg:document.getElementById("findMsg"),findResultsCount:document.getElementById("findResultsCount"),findPreviousButton:document.getElementById("findPrevious"),findNextButton:document.getElementById("findNext")},passwordOverlay:{dialog:document.getElementById("passwordDialog"),label:document.getElementById("passwordText"),input:document.getElementById("password"),submitButton:document.getElementById("passwordSubmit"),cancelButton:document.getElementById("passwordCancel")},documentProperties:{dialog:document.getElementById("documentPropertiesDialog"),closeButton:document.getElementById("documentPropertiesClose"),fields:{fileName:document.getElementById("fileNameField"),fileSize:document.getElementById("fileSizeField"),title:document.getElementById("titleField"),author:document.getElementById("authorField"),subject:document.getElementById("subjectField"),keywords:document.getElementById("keywordsField"),creationDate:document.getElementById("creationDateField"),modificationDate:document.getElementById("modificationDateField"),creator:document.getElementById("creatorField"),producer:document.getElementById("producerField"),version:document.getElementById("versionField"),pageCount:document.getElementById("pageCountField"),pageSize:document.getElementById("pageSizeField"),linearized:document.getElementById("linearizedField")}},altTextDialog:{dialog:document.getElementById("altTextDialog"),optionDescription:document.getElementById("descriptionButton"),optionDecorative:document.getElementById("decorativeButton"),textarea:document.getElementById("descriptionTextarea"),cancelButton:document.getElementById("altTextCancel"),saveButton:document.getElementById("altTextSave")},annotationEditorParams:{editorFreeTextFontSize:document.getElementById("editorFreeTextFontSize"),editorFreeTextColor:document.getElementById("editorFreeTextColor"),editorInkColor:document.getElementById("editorInkColor"),editorInkThickness:document.getElementById("editorInkThickness"),editorInkOpacity:document.getElementById("editorInkOpacity"),editorStampAddImage:document.getElementById("editorStampAddImage")},printContainer:document.getElementById("printContainer"),openFileInput:document.getElementById("fileInput"),debuggerScriptPath:"./debugger.js"}}function d(){const e=l(),t=new CustomEvent("webviewerloaded",{bubbles:!0,cancelable:!0,detail:{source:window}});try{parent.document.dispatchEvent(t)}catch(i){console.error(`webviewerloaded: ${i}`),document.dispatchEvent(t)}o.PDFViewerApplication.run(e)}e.PDFViewerApplicationConstants=a,window.PDFViewerApplication=o.PDFViewerApplication,window.PDFViewerApplicationConstants=a,window.PDFViewerApplicationOptions=s.AppOptions,document.blockUnblockOnload?.(!0),"interactive"===document.readyState||"complete"===document.readyState?d():document.addEventListener("DOMContentLoaded",d,!0)})()})();