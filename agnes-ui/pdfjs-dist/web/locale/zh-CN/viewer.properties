# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ä¸ä¸é¡µ
previous_label=ä¸ä¸é¡µ
next.title=ä¸ä¸é¡µ
next_label=ä¸ä¸é¡µ

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=é¡µé¢
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=/ {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} / {{pagesCount}})

zoom_out.title=ç¼©å°
zoom_out_label=ç¼©å°
zoom_in.title=æ¾å¤§
zoom_in_label=æ¾å¤§
zoom.title=ç¼©æ¾
presentation_mode.title=åæ¢å°æ¼ç¤ºæ¨¡å¼
presentation_mode_label=æ¼ç¤ºæ¨¡å¼
open_file.title=æå¼æä»¶
open_file_label=æå¼
print.title=æå°
print_label=æå°
save.title=ä¿å­
save_label=ä¿å­
# LOCALIZATION NOTE (download_button.title): used in Firefox for Android as a tooltip for the download button (âdownloadâ is a verb).
download_button.title=ä¸è½½
# LOCALIZATION NOTE (download_button_label): used in Firefox for Android as a label for the download button (âdownloadâ is a verb).
# Length of the translation matters since we are in a mobile context, with limited screen estate.
download_button_label=ä¸è½½
bookmark1.title=å½åé¡µé¢ï¼å¨å½åé¡µé¢æ¥ç URLï¼
bookmark1_label=å½åé¡µé¢
# LOCALIZATION NOTE (open_in_app.title): This string is used in Firefox for Android.
open_in_app.title=å¨åºç¨ä¸­æå¼
# LOCALIZATION NOTE (open_in_app_label): This string is used in Firefox for Android. Length of the translation matters since we are in a mobile context, with limited screen estate.
open_in_app_label=å¨åºç¨ä¸­æå¼

# Secondary toolbar and context menu
tools.title=å·¥å·
tools_label=å·¥å·
first_page.title=è½¬å°ç¬¬ä¸é¡µ
first_page_label=è½¬å°ç¬¬ä¸é¡µ
last_page.title=è½¬å°æåä¸é¡µ
last_page_label=è½¬å°æåä¸é¡µ
page_rotate_cw.title=é¡ºæ¶éæè½¬
page_rotate_cw_label=é¡ºæ¶éæè½¬
page_rotate_ccw.title=éæ¶éæè½¬
page_rotate_ccw_label=éæ¶éæè½¬

cursor_text_select_tool.title=å¯ç¨ææ¬éæ©å·¥å·
cursor_text_select_tool_label=ææ¬éæ©å·¥å·
cursor_hand_tool.title=å¯ç¨æå½¢å·¥å·
cursor_hand_tool_label=æå½¢å·¥å·

scroll_page.title=ä½¿ç¨é¡µé¢æ»å¨
scroll_page_label=é¡µé¢æ»å¨
scroll_vertical.title=ä½¿ç¨åç´æ»å¨
scroll_vertical_label=åç´æ»å¨
scroll_horizontal.title=ä½¿ç¨æ°´å¹³æ»å¨
scroll_horizontal_label=æ°´å¹³æ»å¨
scroll_wrapped.title=ä½¿ç¨å¹³éºæ»å¨
scroll_wrapped_label=å¹³éºæ»å¨

spread_none.title=ä¸å å¥è¡æ¥é¡µ
spread_none_label=åé¡µè§å¾
spread_odd.title=å å¥è¡æ¥é¡µä½¿å¥æ°é¡µä½ä¸ºèµ·å§é¡µ
spread_odd_label=åé¡µè§å¾
spread_even.title=å å¥è¡æ¥é¡µä½¿å¶æ°é¡µä½ä¸ºèµ·å§é¡µ
spread_even_label=ä¹¦ç±è§å¾

# Document properties dialog box
document_properties.title=ææ¡£å±æ§â¦
document_properties_label=ææ¡£å±æ§â¦
document_properties_file_name=æä»¶å:
document_properties_file_size=æä»¶å¤§å°:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KB ({{size_b}} å­è)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MB ({{size_b}} å­è)
document_properties_title=æ é¢:
document_properties_author=ä½è:
document_properties_subject=ä¸»é¢:
document_properties_keywords=å³é®è¯:
document_properties_creation_date=åå»ºæ¥æ:
document_properties_modification_date=ä¿®æ¹æ¥æ:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=åå»ºè:
document_properties_producer=PDF çæå¨ï¼
document_properties_version=PDF çæ¬:
document_properties_page_count=é¡µæ°:
document_properties_page_size=é¡µé¢å¤§å°ï¼
document_properties_page_size_unit_inches=è±å¯¸
document_properties_page_size_unit_millimeters=æ¯«ç±³
document_properties_page_size_orientation_portrait=çºµå
document_properties_page_size_orientation_landscape=æ¨ªå
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ææ¬
document_properties_page_size_name_legal=æ³å¾
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}}ï¼{{orientation}}ï¼
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}}ï¼{{name}}ï¼{{orientation}}ï¼
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=å¿«é Web è§å¾ï¼
document_properties_linearized_yes=æ¯
document_properties_linearized_no=å¦
document_properties_close=å³é­

print_progress_message=æ­£å¨åå¤æå°ææ¡£â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=åæ¶

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=åæ¢ä¾§æ 
toggle_sidebar_notification2.title=åæ¢ä¾§æ ï¼ææ¡£æå«çå¤§çº²/éä»¶/å¾å±ï¼
toggle_sidebar_label=åæ¢ä¾§æ 
document_outline.title=æ¾ç¤ºææ¡£å¤§çº²ï¼åå»å±å¼/æå ææé¡¹ï¼
document_outline_label=ææ¡£å¤§çº²
attachments.title=æ¾ç¤ºéä»¶
attachments_label=éä»¶
layers.title=æ¾ç¤ºå¾å±ï¼åå»å³å¯å°ææå¾å±éç½®ä¸ºé»è®¤ç¶æï¼
layers_label=å¾å±
thumbs.title=æ¾ç¤ºç¼©ç¥å¾
thumbs_label=ç¼©ç¥å¾
current_outline_item.title=æ¥æ¾å½åå¤§çº²é¡¹ç®
current_outline_item_label=å½åå¤§çº²é¡¹ç®
findbar.title=å¨ææ¡£ä¸­æ¥æ¾
findbar_label=æ¥æ¾

additional_layers=å¶ä»å¾å±
# LOCALIZATION NOTE (page_landmark): "{{page}}" will be replaced by the page number.
page_landmark=ç¬¬ {{page}} é¡µ
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=ç¬¬ {{page}} é¡µ
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=é¡µé¢ {{page}} çç¼©ç¥å¾

# Find panel button title and messages
find_input.title=æ¥æ¾
find_input.placeholder=å¨ææ¡£ä¸­æ¥æ¾â¦
find_previous.title=æ¥æ¾è¯è¯­ä¸ä¸æ¬¡åºç°çä½ç½®
find_previous_label=ä¸ä¸é¡µ
find_next.title=æ¥æ¾è¯è¯­åä¸æ¬¡åºç°çä½ç½®
find_next_label=ä¸ä¸é¡µ
find_highlight=å¨é¨é«äº®æ¾ç¤º
find_match_case_label=åºåå¤§å°å
find_match_diacritics_label=å¹éåé³ç¬¦å·
find_entire_word_label=å¨è¯å¹é
find_reached_top=å°è¾¾ææ¡£å¼å¤´ï¼ä»æ«å°¾ç»§ç»­
find_reached_bottom=å°è¾¾ææ¡£æ«å°¾ï¼ä»å¼å¤´ç»§ç»­
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]=ç¬¬ {{current}} é¡¹ï¼å±å¹é {{total}} é¡¹
find_match_count[two]=ç¬¬ {{current}} é¡¹ï¼å±å¹é {{total}} é¡¹
find_match_count[few]=ç¬¬ {{current}} é¡¹ï¼å±å¹é {{total}} é¡¹
find_match_count[many]=ç¬¬ {{current}} é¡¹ï¼å±å¹é {{total}} é¡¹
find_match_count[other]=ç¬¬ {{current}} é¡¹ï¼å±å¹é {{total}} é¡¹
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=è¶è¿ {{limit}} é¡¹å¹é
find_match_count_limit[one]=è¶è¿ {{limit}} é¡¹å¹é
find_match_count_limit[two]=è¶è¿ {{limit}} é¡¹å¹é
find_match_count_limit[few]=è¶è¿ {{limit}} é¡¹å¹é
find_match_count_limit[many]=è¶è¿ {{limit}} é¡¹å¹é
find_match_count_limit[other]=è¶è¿ {{limit}} é¡¹å¹é
find_not_found=æ¾ä¸å°æå®è¯è¯­

# Predefined zoom values
page_scale_width=éåé¡µå®½
page_scale_fit=éåé¡µé¢
page_scale_auto=èªå¨ç¼©æ¾
page_scale_actual=å®éå¤§å°
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error=å è½½ PDF æ¶åçéè¯¯ã
invalid_file_error=æ æææåç PDF æä»¶ã
missing_file_error=ç¼ºå° PDF æä»¶ã
unexpected_response_error=æå¤çæå¡å¨ååºã
rendering_error=æ¸²æé¡µé¢æ¶åçéè¯¯ã

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}ï¼{{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} æ³¨é]
password_label=è¾å¥å¯ç ä»¥æå¼æ­¤ PDF æä»¶ã
password_invalid=å¯ç æ æãè¯·éè¯ã
password_ok=ç¡®å®
password_cancel=åæ¶

printing_not_supported=è­¦åï¼æ­¤æµè§å¨å°æªå®æ´æ¯ææå°åè½ã
printing_not_ready=è­¦åï¼æ­¤ PDF æªå®æå è½½ï¼æ æ³æå°ã
web_fonts_disabled=Web å­ä½å·²è¢«ç¦ç¨ï¼æ æ³ä½¿ç¨åµå¥ç PDF å­ä½ã

# Editor
editor_free_text2.title=ææ¬
editor_free_text2_label=ææ¬
editor_ink2.title=ç»å¾
editor_ink2_label=ç»å¾

editor_stamp1.title=æ·»å æç¼è¾å¾å
editor_stamp1_label=æ·»å æç¼è¾å¾å

free_text2_default_content=å¼å§è¾å¥â¦

# Editor Parameters
editor_free_text_color=é¢è²
editor_free_text_size=å­å·
editor_ink_color=é¢è²
editor_ink_thickness=ç²ç»
editor_ink_opacity=ä¸éæåº¦

editor_stamp_add_image_label=æ·»å å¾å
editor_stamp_add_image.title=æ·»å å¾å

# Editor aria
editor_free_text2_aria_label=ææ¬ç¼è¾å¨
editor_ink2_aria_label=ç»å¾ç¼è¾å¨
editor_ink_canvas_aria_label=ç¨æ·åå»ºå¾å

# Alt-text dialog
# LOCALIZATION NOTE (editor_alt_text_button_label): Alternative text (alt text) helps
# when people can't see the image.
editor_alt_text_button_label=æ¿æ¢æå­
editor_alt_text_edit_button_label=ç¼è¾æ¿æ¢æå­
editor_alt_text_dialog_label=éæ©ä¸ä¸ªéé¡¹
editor_alt_text_dialog_description=æ¿æ¢æå­å¯å¨ç¨æ·æ æ³çå°æå è½½å¾åæ¶ï¼æè¿°å¶åå®¹ã
editor_alt_text_add_description_label=æ·»å æè¿°
editor_alt_text_add_description_description=æè¿°ä¸»é¢ãèæ¯æå¨ä½ï¼é¿åº¦å°½éæ§å¶å¨ä¸¤å¥è¯åã
editor_alt_text_mark_decorative_label=æ è®°ä¸ºè£é¥°
editor_alt_text_mark_decorative_description=ç¨äºè£é¥°æ§å¾åï¼ä¾å¦è¾¹æ¡åæ°´å°ã
editor_alt_text_cancel_button=åæ¶
editor_alt_text_save_button=ä¿å­
editor_alt_text_decorative_tooltip=å·²æ è®°ä¸ºè£é¥°
# This is a placeholder for the alt text input area
editor_alt_text_textarea.placeholder=ä¾å¦ï¼ä¸ä¸ªå°å¹´åå°æ¡åï¼åå¤åé¥­
