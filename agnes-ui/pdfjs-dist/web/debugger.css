:root{--panel-width:300px}#PDFBug,#PDFBug :is(input,button,select){font:message-box}#PDFBug{background-color:#fff;border:1px solid #666;position:fixed;top:32px;right:0;bottom:0;font-size:10px;padding:0;width:var(--panel-width)}#PDFBug .controls{background:#eee;border-bottom:1px solid #666;padding:3px}#PDFBug .panels{inset:27px 0 0;overflow:auto;position:absolute}#PDFBug .panels>div{padding:5px}#PDFBug button.active{font-weight:700}.debuggerHideText:hover,.debuggerShowText{background-color:#ff0}#PDFBug .stats{font-family:courier;font-size:10px;white-space:pre}#PDFBug .stats .title{font-weight:700}#PDFBug table{font-size:10px;white-space:pre}#PDFBug table.showText{border-collapse:collapse;text-align:center}#PDFBug table.showText,#PDFBug table.showText :is(tr,td){border:1px solid #000;padding:1px}#PDFBug table.showText td.advance{color:grey}#viewer.textLayer-visible .textLayer{opacity:1}#viewer.textLayer-visible .canvasWrapper{background-color:#80ff80}#viewer.textLayer-visible .canvasWrapper canvas{mix-blend-mode:screen}#viewer.textLayer-visible .textLayer span{background-color:rgba(255,255,0,.1);color:#000;border:1px solid rgba(255,0,0,.5);box-sizing:border-box}#viewer.textLayer-visible .textLayer span[aria-owns]{background-color:rgba(255,0,0,.3)}#viewer.textLayer-hover .textLayer span:hover{background-color:#fff;color:#000}#viewer.textLayer-shadow .textLayer span{background-color:hsla(0,0%,100%,.6);color:#000}