"use strict";(self["webpackChunk_hexop_agnes_ui"]=self["webpackChunk_hexop_agnes_ui"]||[]).push([[28251],{7539:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAE8AAABPCAYAAACqNJiGAAAABHNCSVQICAgIfAhkiAAABHdJREFUeF7tnF1TWjEQht+AfMiXUEUR6+j0ov//vv0L/Qtt1QpSvgQVQYR0FocZhoJwkuyeI5PceMFJDnnybnazG1Q3naGGb4EJaI3vysMLzG3WwcMz4+bhWXDz8Dw8WwIW/f2e5+FZELDo6pXn4VkQsOjqlefhWRCw6OqV5+FZELDo6pXn4VkQsOgaeeWNxkAqYTFDxq6RgTfRwOBZY/gCPD0D0xXp2b04kN0H0kmFbJqRypZDhw6PoPUfNXqPq4GtmweBLOWBfEZtOVX3j4UK72EAtHt6pcq2nWoyAZSLKhTTDg1e9wHoPrgpncQUUPmkkE5ti9zNc6HAa94DDwM34BYxkALzGTdgthlFHB7tbe2+e3DzyZ6V5UxYFN5gCNx1+MARQDLh84pCXMCPiMK7bgCv5F6ZG3ngcpH5JZKlR25zXUZ1fqKQiPMCFFPe77pdSBIUg4T6RODREeu2yW+ui4Bp77s85d34ROCRdyWzlW7VQ97YTwRerQUMX2SVRwtVyqvZEY6reXgWZEXg/azJq46YZNJvxzauttPwKHVVPeJCJ3TF7E9T42XMN4l1I/s9z4L5TsDjyqJs4sqdZRHZ8yQSAqtAXpzyJghE4FEu4Kou63G5nQUtlgg8epG06XKbrCi88QS4acioT0J1ovDoZS7rFu85C+4z7fzdYmY7fyH3OZc7PFlcNHF45DzqLZ6gWSKHFyo8ejkHQGlw4nve4qoRwE7PTQnysKBwkNsUMrv/XNxsl6dAtwbIkZgUhsirHh6EdxEodHhzmATxaahBp5H3GqXXs/sKebrwI3xDYPl7RQbe4hcbjt4u/VDtY972k4CKhaeyVQsaSXjudyeeET08C66RgDfValYgIjN9HmmMX4HJ5P9Z0XWyPTLdlAKZcTppMXMHXUODR8AeBxr9gV3AnEkBhZwC/ZVu4vBeJ0Cnr/H47HaqSgHFvMJBViGmZBIQYvBIaZ3+FP0nt9CWRyOIFDQXsrzvETthDEYKjc6UkodijWLA46IC3V3mauzKa/U0u9rWwSEVUlI0t8+Djw0emeldZwoKeMNuZMJHB+6L3yzwCFytNQ2lVrtuoUh9xyW3AJ3DiyK4OVDXAJ3Dq7V1JEx1nQJLOaBUcKNAp/D+dt3Hbxz7ZeUwhkzK3vU7g0fhyF17yjFX52OSF76oxKyDaSfwaJ+7upON42yJUhxIVTab5gTeRzHXZVC25msNbzRWuG1+DHNdhhePAxcn5uqzhhd177rJLCn2Mz2BWMGjDMm10BWKTRBMP7dRnxW8j7rXLYM2VZ8VvF91LZopMVXXpn6USK0YeF5jeB8prtsEjz6/PA0e9xnD2xWTnYM1CVuM4V019MoizTarHMVnTNJWRvB2wcsuLyBV5j6Xg8V8RvDof5/UWvYH66gp8EtVAF63r9EN4VeM3LDPyjGkEtuLwkh5YdYlOAFWj1SgQroRPDJZMt1da0GDZQ9vQQFBs8wzeNft4bcgKmre668vY8X4M+Ag38bds/kM6oUsatuOqBR+/AMoRWZqI9xVMwAAAABJRU5ErkJggg=="},12142:function(t,e,s){s.d(e,{De:function(){return d},mf:function(){return u},PS:function(){return m},Iw:function(){return g}});s(16961),s(89370),s(21484),s(32807),s(8200),s(36886),s(56831),s(4118),s(5981),s(63074),s(39724);function r(t,e,s=!1){let r;return function(...n){const a=()=>{r=null,s||t.apply(this,n)},i=s&&!r;clearTimeout(r),r=setTimeout(a,e),i&&t.apply(this,n)}}function n(t,e){let s;return function(...r){s||(t.apply(this,r),s=!0,setTimeout((()=>s=!1),e))}}class a{constructor(){this.timers=new Set}create(t,e,s=!1){const r=s?setInterval(t,e):setTimeout((()=>{t(),this.timers.delete(r)}),e);return this.timers.add(r),r}clear(t){this.timers.has(t)&&(clearTimeout(t),clearInterval(t),this.timers.delete(t))}clearAll(){this.timers.forEach((t=>{clearTimeout(t),clearInterval(t)})),this.timers.clear()}}class i{constructor(t=50,e=3e4){this.cache=new Map,this.maxSize=t,this.ttl=e}generateKey(t){return JSON.stringify(t)}get(t){const e=this.cache.get(t);return e?Date.now()-e.timestamp>this.ttl?(this.cache.delete(t),null):e.data:null}set(t,e){if(this.cache.size>=this.maxSize){const t=this.cache.keys().next().value;this.cache.delete(t)}this.cache.set(t,{data:e,timestamp:Date.now()})}clear(){this.cache.clear()}}class o{constructor(){this.listeners=new Map}add(t,e,s,a={}){const{debounce:i=0,throttle:o=0}=a;let c=s;i>0?c=r(s,i):o>0&&(c=n(s,o)),t.addEventListener(e,c,a);const h=`${t.constructor.name}_${e}_${Date.now()}`;return this.listeners.set(h,{target:t,event:e,handler:c,originalHandler:s}),h}remove(t){const e=this.listeners.get(t);e&&(e.target.removeEventListener(e.event,e.handler),this.listeners.delete(t))}removeAll(){this.listeners.forEach(((t,e)=>{try{t.target.removeEventListener(t.event,t.handler)}catch(s){console.warn("清理事件监听器失败:",s)}})),this.listeners.clear()}}class c{constructor(){this.marks=new Map,this.stats={components:new Map,apis:new Map,memory:[]}}mark(t){this.marks.set(t,performance.now())}measure(t){const e=this.marks.get(t);if(!e)return console.warn(`Performance mark "${t}" not found`),0;const s=performance.now()-e;return this.marks.delete(t),s>100&&console.warn(`Performance warning: "${t}" took ${s.toFixed(2)}ms`),s}recordComponentStat(t,e){this.stats.components.has(t)||this.stats.components.set(t,[]);const s=this.stats.components.get(t);s.push({...e,timestamp:Date.now()}),s.length>50&&s.splice(0,s.length-50)}recordApiStat(t,e){this.stats.apis.has(t)||this.stats.apis.set(t,[]);const s=this.stats.apis.get(t);s.push({...e,timestamp:Date.now()}),s.length>50&&s.splice(0,s.length-50)}getStats(){return{components:Object.fromEntries(this.stats.components),apis:Object.fromEntries(this.stats.apis),memory:this.stats.memory.slice(-10)}}}class h{constructor(t,e,s){this.container=t,this.itemHeight=e,this.visibleCount=s,this.scrollTop=0,this.data=[]}getVisibleRange(){const t=Math.floor(this.scrollTop/this.itemHeight),e=Math.min(t+this.visibleCount,this.data.length);return{start:t,end:e}}getVisibleData(){const{start:t,end:e}=this.getVisibleRange();return this.data.slice(t,e).map(((e,s)=>({...e,index:t+s})))}updateScrollTop(t){this.scrollTop=t}setData(t){this.data=t}}const l=new a;new i,new o,new c;function p(t,e=50,s){if(!Array.isArray(t)||0===t.length)return;const r=[];for(let i=0;i<t.length;i+=e)r.push(t.slice(i,i+e));let n=0;const a=()=>{n<r.length&&requestAnimationFrame((()=>{s(r[n]),n++,n<r.length&&l.create(a,0)}))};a()}const d={data(){return{_timerManager:new a,_requestCache:new i,_eventListenerManager:new o,_performanceMonitor:new c,_debouncedFunctions:new Map,_throttledFunctions:new Map,_isDestroyed:!1}},created(){this._performanceMonitor.mark("component_create")},mounted(){const t=this._performanceMonitor.measure("component_create");this._performanceMonitor.recordComponentStat(this.$options.name||"Anonymous",{type:"mount",duration:t})},beforeDestroy(){this._isDestroyed=!0,this.cleanupPerformanceResources()},methods:{getDebounced(t,e,s=300){return this._debouncedFunctions.has(t)||this._debouncedFunctions.set(t,r(e.bind(this),s)),this._debouncedFunctions.get(t)},getThrottled(t,e,s=100){return this._throttledFunctions.has(t)||this._throttledFunctions.set(t,n(e.bind(this),s)),this._throttledFunctions.get(t)},safeSetTimer(t,e,s=!1){return this._timerManager.create(t,e,s)},safeAddEventListener(t,e,s,r={}){return this._eventListenerManager.add(t,e,s,r)},async optimizedApiCall(t,e={},s={}){const{useCache:r=!0,cacheTime:n=3e4}=s;if(this._isDestroyed)return null;const a=this._requestCache.generateKey({apiPath:t,params:e});if(r){const t=this._requestCache.get(a);if(t)return t}try{this._performanceMonitor.mark(`api_${t}`);const s=t.split(".");let n=this.$api;for(const t of s)n=n[t];const i=await n(e),o=this._performanceMonitor.measure(`api_${t}`);return this._performanceMonitor.recordApiStat(t,{duration:o,success:!0}),r&&i&&i.success&&this._requestCache.set(a,i),i}catch(i){const e=this._performanceMonitor.measure(`api_${t}`);throw this._performanceMonitor.recordApiStat(t,{duration:e,success:!1,error:i.message}),i}},batchRenderData(t,e=50,s){p(t,e,s)},cleanupPerformanceResources(){this._timerManager.clearAll(),this._eventListenerManager.removeAll(),this._requestCache.clear(),this._debouncedFunctions.clear(),this._throttledFunctions.clear()},getPerformanceStats(){return this._performanceMonitor.getStats()}}},u={mixins:[d],data(){return{_chartInstances:new Map}},beforeDestroy(){this.cleanupCharts()},methods:{createOptimizedChart(t,e={}){const s=this.$refs[t]||document.getElementById(t);if(!s)return console.warn(`ECharts容器未找到: ${t}`),null;if(this._chartInstances.has(t)){const e=this._chartInstances.get(t);e.dispose()}const r=echarts.init(s,null,{renderer:"canvas",useDirtyRect:!0,...e});return this._chartInstances.set(t,r),r},getOptimizedChartOptions(t){return{...t,animation:!1,progressive:400,progressiveThreshold:3e3,useUTC:!0,lazyUpdate:!0}},handleChartResize(){this._chartInstances.forEach((t=>{t&&"function"===typeof t.resize&&t.resize()}))},cleanupCharts(){this._chartInstances.forEach((t=>{t&&"function"===typeof t.dispose&&t.dispose()})),this._chartInstances.clear()}}},m={mixins:[d],data(){return{_virtualScrollManager:null,_optimizedScrollOptions:{step:.5,limitMoveNum:3,hoverStop:!0,direction:1,openWatch:!0,singleHeight:0,singleWidth:0,waitTime:1e3}}},methods:{getOptimizedScrollOptions(t={}){return{...this._optimizedScrollOptions,...t}},optimizeScrollData(t,e=100){return Array.isArray(t)?t.slice(0,e):[]},enableVirtualScroll(t,e,s){return this._virtualScrollManager=new h(t,e,s),this._virtualScrollManager}}},g={mixins:[d],data(){return{_treeLoadingStates:new Map,_nodeCache:new i(20,1e4)}},methods:{async optimizedLoadNode(t,e){var s;const r=(null===(s=t.data)||void 0===s?void 0:s.pkId)||t.level;if(this._treeLoadingStates.get(r))return;const n=this._nodeCache.generateKey({nodeId:r,level:t.level,...this.getNodeRequestParams(t)}),a=this._nodeCache.get(n);if(a)e(a);else{this._treeLoadingStates.set(r,!0);try{const s=await this.loadNodeData(t);this._nodeCache.set(n,s),e(s)}catch(i){console.error("加载节点数据失败:",i),e([])}finally{this._treeLoadingStates.delete(r)}}},getNodeRequestParams(t){return{}},async loadNodeData(t){throw new Error("loadNodeData method must be implemented by subclass")},batchUpdateNodes(t){this.$nextTick((()=>{t.forEach((t=>{this.updateSingleNode(t)}))}))},updateSingleNode(t){const{nodeId:e,data:s}=t,r=this.findNodeById(e);r&&Object.assign(r,s)},findNodeById(t){const e=s=>{for(const r of s){if(r.pkId===t)return r;if(r.children){const t=e(r.children);if(t)return t}}return null};return e(this.cardData||[])}}}},28251:function(t,e,s){s.r(e),s.d(e,{default:function(){return l}});var r=function(){var t=this,e=t._self._c;return e("gf-workbench-panel",{staticClass:"workbench-schedule-comp",attrs:{slotQuery:"workbench-schedule-comp"},scopedSlots:t._u([{key:"icon",fn:function({icon:t}){return[e("i",{class:[t||"el-icon-date"]})]}},{key:"title",fn:function({title:s}){return[e("p",[t._v(t._s(s||"日历"))]),e("i",{staticClass:"refresh-btn el-icon-refresh-left",attrs:{"aria-hidden":"true"},on:{click:function(e){return t.refreshData()}}}),e("span",{staticClass:"compEntrance",on:{click:function(e){return t.entranceMenu()}}},[t._v(" 查看更多 "),e("em",{staticClass:"fa fa-angle-right ma-l-4"})])]}},{key:"body",fn:function(){return[e("div",{staticClass:"roster-container"},[t.rosterList.length?t._e():e("svg-icon",{staticStyle:{"margin-top":"50px"},attrs:{name:"empty-img",height:"150px"}}),e("vue-seamless-scroll",{staticClass:"seamless-warp",attrs:{data:t.optimizedRosterList,"class-option":t.optimizedScrollOptions}},t._l(t.rosterList,(function(r,n){return e("div",{key:n,staticClass:"roster-item"},[e("div",{staticClass:"icon"},[e("img",{staticStyle:{"border-radius":"6px"},attrs:{src:s(7539),alt:"avatar",width:"40px",height:"40px"}})]),e("div",{staticClass:"info"},[e("p",[e("svg-icon",{attrs:{name:"personal"===t.pageType?"calendar":"user",height:"12px",color:"#666"}}),e("span",[t._v(t._s("personal"===t.pageType?r.week:r.userName))])],1),e("p",[e("svg-icon",{attrs:{name:"personal"===t.pageType?"clock":"phone",height:"12px",color:"#666"}}),e("span",[t._v(t._s("personal"===t.pageType?r.rosterDate:r.oTel&&r.oTel.substr(r.oTel.length-4,r.oTel.length-1)))]),"department"===t.pageType?[e("svg-icon",{attrs:{name:"mobile",height:"12px",color:"#666"}}),e("span",{staticClass:"telSpan"},[t._v(t._s(r.mobileNo))])]:t._e()],2)]),e("div",{staticClass:"type"},[e("div",{on:{click:function(e){return t.showRoster(r)}}},[t._v(t._s(t.getRosterType(r.rosterType)))])])])})),0)],1)]},proxy:!0}])})},n=[],a=(s(16961),s(7354),s(12142)),i={mixins:[a.PS],props:{pageType:{type:String,default:"department"},quartzTime:String},data(){return{todayDate:(new Date).toLocaleDateString().replace(/\//g,"-"),rosterList:[],rosterType:this.$app.dict.getDictItems("AGNES_ROSTER_TYPE"),rosterStatus:this.$app.dict.getDictItems("AGNES_RELEASE_STATUS"),isDestroyed:!1}},mounted(){this.initDate(),this.startInterval()},watch:{$route(t,e){("personal"===this.pageType&&e.path.includes("datav.client.view")||"department"===this.pageType&&e.path.includes("datav.dep.view"))&&this.clearInterval(),("personal"===this.pageType&&t.path.includes("datav.client.view")||"department"===this.pageType&&t.path.includes("datav.dep.view"))&&(this.initDate(),this.startInterval())}},computed:{intervalMin(){const t=this.quartzTime?this.quartzTime:"5";return 60*parseInt(t)*1e3},optimizedScrollOptions(){return this.getOptimizedScrollOptions({step:.5,limitMoveNum:3,hoverStop:!0,direction:1,openWatch:!0,singleHeight:0,singleWidth:0,waitTime:1e3})},optimizedRosterList(){return this.optimizeScrollData(this.rosterList,50)}},methods:{initDate(){this.$api.changeDataApi.getChangeData().then((t=>{const e=t.data,s=e?e.bizDate:this.todayDate;this.$api.HomePageApi.selectRosterDetailOfWeek({rosterDate:s,pageType:this.pageType}).then((t=>{this.rosterList=t.data}))}))},refreshData(){this.rosterList=[],this.initDate()},showRoster(t){let e="agnes.dop.roster.ru",s=this.$app.views.getView(e),r=Object.assign({args:{rosterType:t.rosterType},id:e},s);this.$nav.showView(r)},getRosterType(t){if(t&&this.$lodash.find(this.rosterType,{dictId:t}))return this.$lodash.find(this.rosterType,{dictId:t}).dictName},startInterval(){this.freshInterval=setInterval((()=>{"personal"===this.pageType&&this.$route.path.includes("datav.client.view")||"department"===this.pageType&&this.$route.path.includes("datav.dep.view")?this.initDate():this.clearInterval()}),this.intervalMin)},clearInterval(){clearInterval(this.freshInterval)},entranceMenu(){this.$nextTick((()=>{let t="agnes.biz.schedule.calendar";if(t){let e=this.$app.views.getView(t),s=Object.assign({id:t},e);this.$nav.showView(s)}}))}}},o=i,c=s(18579),h=(0,c.A)(o,r,n,!1,null,"a80763a6",null),l=h.exports}}]);