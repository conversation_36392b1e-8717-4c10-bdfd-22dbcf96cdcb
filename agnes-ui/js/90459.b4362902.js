"use strict";(self["webpackChunk_hexop_agnes_ui"]=self["webpackChunk_hexop_agnes_ui"]||[]).push([[90459],{12142:function(e,t,a){a.d(t,{De:function(){return m},mf:function(){return p},PS:function(){return u},Iw:function(){return f}});a(16961),a(89370),a(21484),a(32807),a(8200),a(36886),a(56831),a(4118),a(5981),a(63074),a(39724);function s(e,t,a=!1){let s;return function(...i){const r=()=>{s=null,a||e.apply(this,i)},n=a&&!s;clearTimeout(s),s=setTimeout(r,t),n&&e.apply(this,i)}}function i(e,t){let a;return function(...s){a||(e.apply(this,s),a=!0,setTimeout((()=>a=!1),t))}}class r{constructor(){this.timers=new Set}create(e,t,a=!1){const s=a?setInterval(e,t):setTimeout((()=>{e(),this.timers.delete(s)}),t);return this.timers.add(s),s}clear(e){this.timers.has(e)&&(clearTimeout(e),clearInterval(e),this.timers.delete(e))}clearAll(){this.timers.forEach((e=>{clearTimeout(e),clearInterval(e)})),this.timers.clear()}}class n{constructor(e=50,t=3e4){this.cache=new Map,this.maxSize=e,this.ttl=t}generateKey(e){return JSON.stringify(e)}get(e){const t=this.cache.get(e);return t?Date.now()-t.timestamp>this.ttl?(this.cache.delete(e),null):t.data:null}set(e,t){if(this.cache.size>=this.maxSize){const e=this.cache.keys().next().value;this.cache.delete(e)}this.cache.set(e,{data:t,timestamp:Date.now()})}clear(){this.cache.clear()}}class o{constructor(){this.listeners=new Map}add(e,t,a,r={}){const{debounce:n=0,throttle:o=0}=r;let l=a;n>0?l=s(a,n):o>0&&(l=i(a,o)),e.addEventListener(t,l,r);const c=`${e.constructor.name}_${t}_${Date.now()}`;return this.listeners.set(c,{target:e,event:t,handler:l,originalHandler:a}),c}remove(e){const t=this.listeners.get(e);t&&(t.target.removeEventListener(t.event,t.handler),this.listeners.delete(e))}removeAll(){this.listeners.forEach(((e,t)=>{try{e.target.removeEventListener(e.event,e.handler)}catch(a){console.warn("清理事件监听器失败:",a)}})),this.listeners.clear()}}class l{constructor(){this.marks=new Map,this.stats={components:new Map,apis:new Map,memory:[]}}mark(e){this.marks.set(e,performance.now())}measure(e){const t=this.marks.get(e);if(!t)return console.warn(`Performance mark "${e}" not found`),0;const a=performance.now()-t;return this.marks.delete(e),a>100&&console.warn(`Performance warning: "${e}" took ${a.toFixed(2)}ms`),a}recordComponentStat(e,t){this.stats.components.has(e)||this.stats.components.set(e,[]);const a=this.stats.components.get(e);a.push({...t,timestamp:Date.now()}),a.length>50&&a.splice(0,a.length-50)}recordApiStat(e,t){this.stats.apis.has(e)||this.stats.apis.set(e,[]);const a=this.stats.apis.get(e);a.push({...t,timestamp:Date.now()}),a.length>50&&a.splice(0,a.length-50)}getStats(){return{components:Object.fromEntries(this.stats.components),apis:Object.fromEntries(this.stats.apis),memory:this.stats.memory.slice(-10)}}}class c{constructor(e,t,a){this.container=e,this.itemHeight=t,this.visibleCount=a,this.scrollTop=0,this.data=[]}getVisibleRange(){const e=Math.floor(this.scrollTop/this.itemHeight),t=Math.min(e+this.visibleCount,this.data.length);return{start:e,end:t}}getVisibleData(){const{start:e,end:t}=this.getVisibleRange();return this.data.slice(e,t).map(((t,a)=>({...t,index:e+a})))}updateScrollTop(e){this.scrollTop=e}setData(e){this.data=e}}const d=new r;new n,new o,new l;function h(e,t=50,a){if(!Array.isArray(e)||0===e.length)return;const s=[];for(let n=0;n<e.length;n+=t)s.push(e.slice(n,n+t));let i=0;const r=()=>{i<s.length&&requestAnimationFrame((()=>{a(s[i]),i++,i<s.length&&d.create(r,0)}))};r()}const m={data(){return{_timerManager:new r,_requestCache:new n,_eventListenerManager:new o,_performanceMonitor:new l,_debouncedFunctions:new Map,_throttledFunctions:new Map,_isDestroyed:!1}},created(){this._performanceMonitor.mark("component_create")},mounted(){const e=this._performanceMonitor.measure("component_create");this._performanceMonitor.recordComponentStat(this.$options.name||"Anonymous",{type:"mount",duration:e})},beforeDestroy(){this._isDestroyed=!0,this.cleanupPerformanceResources()},methods:{getDebounced(e,t,a=300){return this._debouncedFunctions.has(e)||this._debouncedFunctions.set(e,s(t.bind(this),a)),this._debouncedFunctions.get(e)},getThrottled(e,t,a=100){return this._throttledFunctions.has(e)||this._throttledFunctions.set(e,i(t.bind(this),a)),this._throttledFunctions.get(e)},safeSetTimer(e,t,a=!1){return this._timerManager.create(e,t,a)},safeAddEventListener(e,t,a,s={}){return this._eventListenerManager.add(e,t,a,s)},async optimizedApiCall(e,t={},a={}){const{useCache:s=!0,cacheTime:i=3e4}=a;if(this._isDestroyed)return null;const r=this._requestCache.generateKey({apiPath:e,params:t});if(s){const e=this._requestCache.get(r);if(e)return e}try{this._performanceMonitor.mark(`api_${e}`);const a=e.split(".");let i=this.$api;for(const e of a)i=i[e];const n=await i(t),o=this._performanceMonitor.measure(`api_${e}`);return this._performanceMonitor.recordApiStat(e,{duration:o,success:!0}),s&&n&&n.success&&this._requestCache.set(r,n),n}catch(n){const t=this._performanceMonitor.measure(`api_${e}`);throw this._performanceMonitor.recordApiStat(e,{duration:t,success:!1,error:n.message}),n}},batchRenderData(e,t=50,a){h(e,t,a)},cleanupPerformanceResources(){this._timerManager.clearAll(),this._eventListenerManager.removeAll(),this._requestCache.clear(),this._debouncedFunctions.clear(),this._throttledFunctions.clear()},getPerformanceStats(){return this._performanceMonitor.getStats()}}},p={mixins:[m],data(){return{_chartInstances:new Map}},beforeDestroy(){this.cleanupCharts()},methods:{createOptimizedChart(e,t={}){const a=this.$refs[e]||document.getElementById(e);if(!a)return console.warn(`ECharts容器未找到: ${e}`),null;if(this._chartInstances.has(e)){const t=this._chartInstances.get(e);t.dispose()}const s=echarts.init(a,null,{renderer:"canvas",useDirtyRect:!0,...t});return this._chartInstances.set(e,s),s},getOptimizedChartOptions(e){return{...e,animation:!1,progressive:400,progressiveThreshold:3e3,useUTC:!0,lazyUpdate:!0}},handleChartResize(){this._chartInstances.forEach((e=>{e&&"function"===typeof e.resize&&e.resize()}))},cleanupCharts(){this._chartInstances.forEach((e=>{e&&"function"===typeof e.dispose&&e.dispose()})),this._chartInstances.clear()}}},u={mixins:[m],data(){return{_virtualScrollManager:null,_optimizedScrollOptions:{step:.5,limitMoveNum:3,hoverStop:!0,direction:1,openWatch:!0,singleHeight:0,singleWidth:0,waitTime:1e3}}},methods:{getOptimizedScrollOptions(e={}){return{...this._optimizedScrollOptions,...e}},optimizeScrollData(e,t=100){return Array.isArray(e)?e.slice(0,t):[]},enableVirtualScroll(e,t,a){return this._virtualScrollManager=new c(e,t,a),this._virtualScrollManager}}},f={mixins:[m],data(){return{_treeLoadingStates:new Map,_nodeCache:new n(20,1e4)}},methods:{async optimizedLoadNode(e,t){var a;const s=(null===(a=e.data)||void 0===a?void 0:a.pkId)||e.level;if(this._treeLoadingStates.get(s))return;const i=this._nodeCache.generateKey({nodeId:s,level:e.level,...this.getNodeRequestParams(e)}),r=this._nodeCache.get(i);if(r)t(r);else{this._treeLoadingStates.set(s,!0);try{const a=await this.loadNodeData(e);this._nodeCache.set(i,a),t(a)}catch(n){console.error("加载节点数据失败:",n),t([])}finally{this._treeLoadingStates.delete(s)}}},getNodeRequestParams(e){return{}},async loadNodeData(e){throw new Error("loadNodeData method must be implemented by subclass")},batchUpdateNodes(e){this.$nextTick((()=>{e.forEach((e=>{this.updateSingleNode(e)}))}))},updateSingleNode(e){const{nodeId:t,data:a}=e,s=this.findNodeById(t);s&&Object.assign(s,a)},findNodeById(e){const t=a=>{for(const s of a){if(s.pkId===e)return s;if(s.children){const e=t(s.children);if(e)return e}}return null};return t(this.cardData||[])}}}},23554:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAcdJREFUOE+lkz9oU1EUh79zX0snUbBVUamiSB3EQawiLm5KXjB2MCaxFPqCuEhHLcXhrVLR4iCKJCFQ80IXkb7EwUEnJ0VwcQjaqoh0EsQi0uYd+1IaQ3yxFe947+989/zOH+E/j3SKf1B6vL1brd0BfP1Yeznvum4Qpf0DkPeqF4CroEfWAgS+KNztWuq6PTJyerEV1AS4rmv2DAzmFIYFiqpSQoN5jPSJck5Fr4C8DSzrjFmun8fw2UnZfhNQ8KoTil4XJTGasZ+2p5t/6O/DyDNFa4IcVmEym7InG4D8TLWPus4BE07avtMefL8029st5jXwAzjQeA90v3Mx/n4VUK6Mokx93yzbxmKxn+2A0F7/wWOXUR0CTiG8WUn/aKhbBXiVW8BxJ22fXK+rhcKjLcs9pudSJrHwG1D2b6JyYiOA9g8aGeQ8PyuEFkxvlIW/ZdVaxE+qOp7NxKfWsxE5B7lyZVyUsI1DUW0seE/2KvWdTjr+ouMg9Q8MFoGUwLRgpoOgPieWtVU1OAuMgTx30rFEJGDtMl/yhxG5BhxqES4o3NtkLd5IJpPhLDRPx2Uqzszu0iXZoei3D7VX7za8TP9SwFD7C660pxEMs8JfAAAAAElFTkSuQmCC"},26059:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAECAYAAABGM/VAAAAAAXNSR0IArs4c6QAAADxJREFUGFddyCERgEAUBcD9mggkwZGAFBeCGmfPkACHoN1jcAwrt/wkmet7SRbclWRDx46B880JF1YcaA+UXxGlETd4JAAAAABJRU5ErkJggg=="},78413:function(e){e.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAAAAXNSR0IArs4c6QAAAWxJREFUOE+l0zFLw0AUB/D3rplq6yR00E0FoeAnEAdxSqKgLk108VrRVRHqZkd3cRBpS0QTqFOxrWNncVQEhw6KIGoHkaqgXp6keKVgWiK97XIvP9797w6hx4E9/g8tIOdUdhAo5gsiviwn1HW/tXagCEAT5MJueyEySBLQTdLQp7sCB3YxFkLlEog2uKkfecX549I8McwLgfGVJfW+K+AtZp3SLCJaLgsN98PrR0P03ZKLm8lF9bBTVn9CzDnlZ0Smfrn4rqC44IYW3isUImERHZDIJ303Vs2Zujf3A54QmfYLnHNDi3gBA1C61QXBNTe1eGCg21EH6iDvlNIEuCYhAqrJUwkEWIXTQVcooxIQKOqphH7VcQsElBKk1BQUzQyydnmBIeitDgjvuKlu+wJZp5xBgC0AOAGAuWaIdmWKiCYlgAgP3NT2fQHvo2WfjQvmWkA0wg09+q8QZXGmWlWGHt/G5F4DX6T/vs4f8l+oESgVZ7cAAAAASUVORK5CYII="},90459:function(e,t,a){a.r(t),a.d(t,{default:function(){return A}});var s=function(){var e=this,t=e._self._c;return t("div",{staticClass:"slide-bar-view elec-process"},[t("div",{staticClass:"filter-view"},[t("div",{staticClass:"filter-radio"},[t("el-radio-group",{model:{value:e.tempForm.taskCls,callback:function(t){e.$set(e.tempForm,"taskCls",t)},expression:"tempForm.taskCls"}},e._l(e.options,(function(s){return t("el-radio",{key:s.value,attrs:{label:s.value},nativeOn:{click:function(t){return t.preventDefault(),e.taskClsClickHandler.apply(null,arguments)}}},[t("div",{class:{active:e.tempForm.taskCls==s.value,taskSelectBtn:!0},attrs:{tabindex:"1"}},[t("span",[e._v(e._s(s.label))]),t("img",{staticClass:"select-icon",attrs:{src:a(26059),alt:""}})])])})),1),t("el-dropdown",{on:{command:e.changeFilter}},[t("img",{staticClass:"filter-icon",attrs:{src:a(78413),alt:""}}),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.filters,(function(a){return t("el-dropdown-item",{key:a.command,attrs:{command:a.command}},[e._v(e._s(a.label))])})),1)],1)],1),t("div",{staticClass:"query-area"},[t("el-date-picker",{staticClass:"date-picker",attrs:{type:"daterange",align:"right","unlink-panels":"",clearable:!1,"range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd","picker-options":e.pickerOptions},on:{change:e.queryChange},model:{value:e.taskDateRange,callback:function(t){e.taskDateRange=t},expression:"taskDateRange"}}),t("img",{staticClass:"refresh-icon",attrs:{src:a(23554),alt:""},on:{click:e.refreshCardTask}}),t("div",{staticClass:"interval-ctrl"},[t("span",{directives:[{name:"show",rawName:"v-show",value:e.ifIntervalStart,expression:"ifIntervalStart"}],attrs:{title:"定时刷新已开启"},domProps:{innerHTML:e._s(e.svgImg.startInterval)},on:{contextmenu:function(t){t.preventDefault(),e.intervalListShow=!0},click:function(t){return e.clearFreshInterval(!0)}}}),t("span",{directives:[{name:"show",rawName:"v-show",value:!e.ifIntervalStart,expression:"!ifIntervalStart"}],attrs:{title:"定时刷新已暂停"},domProps:{innerHTML:e._s(e.svgImg.stopInterval)},on:{contextmenu:function(t){t.preventDefault(),e.intervalListShow=!0},click:function(t){return e.startFreshInterval(!0)}}}),t("ul",{directives:[{name:"show",rawName:"v-show",value:e.intervalListShow,expression:"intervalListShow"},{name:"clickoutside",rawName:"v-clickoutside",value:e.outsideClick,expression:"outsideClick"}],staticClass:"interval-list"},e._l(e.intervalList,(function(a){return t("li",{key:a.value,class:e.intervalMin===a.value?"is-select":"",on:{click:function(t){return e.chooseIntervalMin(a.value)}}},[e._v(" "+e._s(a.label)+" ")])})),0)])],1),t("div",{staticClass:"add-query"},[t("el-dropdown",{on:{command:e.selectTaskType}},[t("el-button",{staticClass:"add-btn",attrs:{type:"primary",plain:"",icon:"el-icon-plus"},nativeOn:{dblclick:function(t){return e.quickAadTask.apply(null,arguments)}}},[e._v("新增任务")]),t("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.addTaskTypes,(function(a){return t("el-dropdown-item",{key:a.command,attrs:{command:a.command}},[e._v(e._s(a.label))])})),1)],1),t("el-input",{attrs:{placeholder:"请输入","prefix-icon":"el-icon-search"},model:{value:e.filterText,callback:function(t){e.filterText=t},expression:"filterText"}})],1)]),t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"list-view"},[t("el-tree",{ref:"tree",attrs:{indent:"0",lazy:"","node-key":"pkId",load:e.loadNode,data:e.cardData,props:e.defaultProps,"filter-node-method":e.filterNode,"default-expanded-keys":e.defaultExpandedKeys,"default-checked-keys":e.defaultCheckedKeys},on:{"node-click":e.nodeClick,"node-expand":e.nodeClick},scopedSlots:e._u([{key:"default",fn:function({node:a,data:s}){return t("div",{class:["card-tree-node",1===a.level?"first-level":"","last-level"]},[t("div",{staticClass:"node-label"},[t("span",[e._v(e._s(s.themeName||s.taskName))]),s.timeOutCount>0?t("span",{staticClass:"err-count"},[e._v("超时: "+e._s(s.timeOutCount))]):s.errCount>0?t("span",{staticClass:"err-count"},[e._v("异常: "+e._s(s.errCount))]):e._e()]),s.isInstant?t("div",{staticClass:"card-footer"},[t("div",{staticClass:"card-status"},[t("span",{staticClass:"status-icon",style:{backgroundColor:e.getCardStatus(s.taskStatus).color}}),e._v(" "+e._s(e.getCardStatus(s.taskStatus).label)+" ")]),s.startDate?t("div",{staticClass:"card-date"},[t("i",{staticClass:"el-icon-date"}),e._v(" "+e._s(e.$dateUtils.formatDate(s.startDate,"yyyy-MM-dd"))+"至"+e._s(e.$dateUtils.formatDate(s.endDate,"yyyy-MM-dd"))+" ")]):e._e()]):t("div",{staticClass:"card-footer"},["00"==s.type||"01"==s.type&&s.countFun||"02"==s.type||"05"==s.type?[t("div",{staticClass:"type-tit"},[e._v(" 总数: "),t("span",{staticClass:"num all"},[e._v(e._s(s.totalCount))])]),t("div",{staticClass:"type-tit"},[e._v(" 已完成: "),t("span",{staticClass:"num done"},[e._v(e._s(s.completeCount))])]),t("div",{staticClass:"type-tit"},[e._v(" 未完成: "),t("span",{staticClass:"num todo"},[e._v(e._s(s.noCompleteCount))])])]:e._e(),"04"==s.type||"03"==s.type?[t("div",{staticClass:"type-tit1"},[e._v(" 总数: "),t("span",{staticClass:"num all"},[e._v(e._s(s.totalCount))])]),t("div",{staticClass:"type-tit1"},[e._v(" 异常: "),t("span",{staticClass:"num error"},[e._v(e._s(s.errCount))])]),t("div",{staticClass:"type-tit1"},[e._v(" 干预通过: "),t("span",{staticClass:"num confirm"},[e._v(e._s(s.gytgCount))])]),t("div",{staticClass:"type-tit1"},[e._v(" 正常: "),t("span",{staticClass:"num common"},[e._v(e._s(s.completeCount))])])]:e._e()],2)])}}])})],1)])},i=[];a(21484),a(16961),a(54615),a(89370),a(32807);const r=[{label:"未开始",value:"01",color:"#9EA7B5"},{label:"执行中",value:"02",color:"#3387F3"},{label:"异常",value:"03",color:"#F76964"},{label:"超时",value:"04",color:"#F76964"},{label:"已作废",value:"05",color:"#FAA125"},{label:"已完成",value:"06",color:"#20C781"},{label:"已完成",value:"07",color:"#20C781"}];var n=a(70090),o=function(){var e=this,t=e._self._c;return t("div",[t("el-form",{ref:"form",staticClass:"fit-box",staticStyle:{padding:"10px"},attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"任务名称",prop:"taskName"}},[t("el-input",{attrs:{placeholder:"请输入任务名称"},model:{value:e.form.taskName,callback:function(t){e.$set(e.form,"taskName",t)},expression:"form.taskName"}})],1),t("el-form-item",{attrs:{label:"业务标签",prop:"bizTag"}},[t("gf-dict",{attrs:{"dict-type":"AGNES_BIZ_TAG"},on:{change:e.bizTagChange},model:{value:e.form.bizTag,callback:function(t){e.$set(e.form,"bizTag",t)},expression:"form.bizTag"}})],1),t("el-form-item",{attrs:{label:"责任人",prop:"zrUserInfo"}},[t("gf-person-center-chosen",{ref:"ownerGrouprRef",attrs:{memberRefList:this.ownerGroupList,chosenType:"user, group"},on:{getMemberList:e.getOwnerGroupList}})],1),t("el-form-item",{attrs:{label:"办理方式",prop:"crtType"}},[t("el-radio-group",{model:{value:e.form.crtType,callback:function(t){e.$set(e.form,"crtType",t)},expression:"form.crtType"}},[t("el-radio",{attrs:{label:"1"}},[e._v("单点办理")]),t("el-radio",{attrs:{label:"2"}},[e._v("多点办理")])],1)],1),t("div",{staticClass:"line"},[t("el-form-item",{attrs:{label:"开始时间",prop:"planStartTs"}},[t("el-date-picker",{key:"planStartTs",attrs:{type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},model:{value:e.form.planStartTs,callback:function(t){e.$set(e.form,"planStartTs",t)},expression:"form.planStartTs"}})],1),t("el-form-item",{attrs:{label:"截止时间",prop:"planEndTs"}},[t("el-date-picker",{key:"planEndTs",attrs:{type:"datetime",align:"right","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},model:{value:e.form.planEndTs,callback:function(t){e.$set(e.form,"planEndTs",t)},expression:"form.planEndTs"}})],1)],1),t("el-form-item",{attrs:{label:"任务说明",prop:"taskDesc"}},[t("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入任务说明",maxLength:50},model:{value:e.form.taskDesc,callback:function(t){e.$set(e.form,"taskDesc",t)},expression:"form.taskDesc"}})],1)],1),t("dialog-footer",{attrs:{"on-cancel":e.onCancel,"on-save":e.onSave}})],1)},l=[],c={name:"add-temp-task",props:{actionOk:Function},data(){const e=(e,t,a)=>{""===t||"[]"===t?a(new Error("请选择责任人")):a()};return{form:{taskName:"",bizTag:"",bizTagName:"",crtType:"1",zrUserInfo:"",planStartTs:"",planEndTs:"",taskDesc:""},ownerGroupList:[],rules:{taskName:[{required:!0,message:"请输入任务名称",trigger:"blur"}],zrUserInfo:[{required:!0,validator:e,trigger:"blur"}],crtType:[{required:!0,message:"请选择办理方式",trigger:"blur"}],planStartTs:[{required:!0,message:"请选择开始时间",trigger:"blur"}],planEndTs:[{required:!0,message:"请选择截止时间",trigger:"blur"}]}}},mounted(){this.form.planStartTs=window.bizDate+" 00:00:00",this.form.planEndTs=window.bizDate+" 23:59:00",this.ownerGroupList.push({refType:"1",memberId:this.$app.session.data.user.userId,memberDesc:this.$app.session.data.user.userName})},methods:{bizTagChange(e){var t;this.form.bizTagName=null===(t=this.$app.dict.getDictItem("AGNES_BIZ_TAG",e))||void 0===t?void 0:t.dictName},onCancel(){this.$dialog.close(this,"cancel")},getOwnerGroupList(e){this.ownerGroupList=e,this.form.zrUserInfo=JSON.stringify(e)},async onSave(e){e.preventDefault();const t=await this.$refs.form.validate();if(t){const e=new Date(this.form.planStartTs),t=new Date(this.form.planEndTs);if(t<e)return this.$message.error("开始时间不能大于截至时间！");await this.actionOk(this.form),this.onCancel()}}}},d=c,h=a(18579),m=(0,h.A)(d,o,l,!1,null,null,null),p=m.exports,u=function(){var e=this,t=e._self._c;return t("div",[t("el-form",{ref:"form",staticClass:"fit-box",staticStyle:{padding:"10px"},attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[t("el-form-item",{attrs:{label:"任务名称",prop:"taskName"}},[t("el-input",{attrs:{placeholder:"请输入任务名称"},model:{value:e.form.taskName,callback:function(t){e.$set(e.form,"taskName",t)},expression:"form.taskName"}})],1),t("div",{staticClass:"line"},[t("el-form-item",{attrs:{label:"任务主题",prop:"themeName"}},[t("el-select",{ref:"themeRef",attrs:{remote:"","remote-method":e.handleThemeFilter,filterable:""},on:{change:e.themeChange},model:{value:e.form.themeName,callback:function(t){e.$set(e.form,"themeName",t)},expression:"form.themeName"}},[e._l(e.themeCoverList,(function(e){return t("el-option",{key:e.pkId,staticStyle:{display:"none"},attrs:{value:e.pkId,label:e.themeName}})})),t("el-tree",{ref:"taskDefThemeTreeRef",attrs:{data:e.themeList,props:e.props,"node-key":"id","default-expand-all":"","expand-on-click-node":!1,"filter-node-method":e.filterThemeNode},on:{"node-click":e.handleNodeClick}})],2)],1),t("el-form-item",{attrs:{label:"启动场景:",prop:"caseKey"}},[t("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},on:{change:e.caseChange},model:{value:e.form.caseKey,callback:function(t){e.$set(e.form,"caseKey",t)},expression:"form.caseKey"}},e._l(e.caseList,(function(e,a){return t("el-option",{key:a,attrs:{label:e.taskName,value:e.taskId}})})),1)],1)],1),t("el-form-item",{directives:[{name:"show",rawName:"v-show",value:e.showPrdt,expression:"showPrdt"}],attrs:{label:"产品信息",prop:"prdtCode"}},[t("el-select",{attrs:{clearable:"",filterable:"",placeholder:"请选择","filter-method":e.match},on:{change:e.productChange},model:{value:e.form.prdtCode,callback:function(t){e.$set(e.form,"prdtCode",t)},expression:"form.prdtCode"}},e._l(e.filterList,(function(e){return t("gf-filter-option",{key:e.productId,attrs:{label:e.productName,value:e.productId}})})),1)],1),t("div",{staticClass:"line"},[t("el-form-item",{attrs:{label:"开始时间",prop:"startDate"}},[t("el-date-picker",{key:"startDate",attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.form.startDate,callback:function(t){e.$set(e.form,"startDate",t)},expression:"form.startDate"}})],1),t("el-form-item",{attrs:{label:"截止时间",prop:"endDate"}},[t("el-date-picker",{key:"endDate",attrs:{type:"date",align:"right","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.form.endDate,callback:function(t){e.$set(e.form,"endDate",t)},expression:"form.endDate"}})],1)],1),t("el-form-item",{attrs:{label:"责任人",prop:"zrUserInfo"}},[t("gf-person-center-chosen",{ref:"ownerGrouprRef",attrs:{memberRefList:this.ownerGroupList,chosenType:"user, group"},on:{getMemberList:e.getOwnerGroupList}})],1),t("el-form-item",{attrs:{label:"任务说明",prop:"taskDesc"}},[t("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入任务说明","max-byte-len":50},model:{value:e.form.taskDesc,callback:function(t){e.$set(e.form,"taskDesc",t)},expression:"form.taskDesc"}})],1),e.hasSelfDefinedParam?e._l(e.fieldOption,(function(a,s){return t("div",{key:s,staticClass:"line"},[t("el-form-item",{key:s,attrs:{label:a.paramNameFormat,prop:a.paramName}},["string"===a.paramType?t("el-input",{model:{value:e.form.varMap[a.paramName],callback:function(t){e.$set(e.form.varMap,a.paramName,t)},expression:"form.varMap[item.paramName]"}}):"option"===a.paramType?t("el-select",{attrs:{filterable:"",clearable:"",placeholder:"请选择"},model:{value:e.form.varMap[a.paramName],callback:function(t){e.$set(e.form.varMap,a.paramName,t)},expression:"form.varMap[item.paramName]"}},e._l(a.options,(function(e){return t("el-option",{key:e.pkId,attrs:{label:e.name,value:e.pkId}})})),1):"date"===a.paramType?t("el-date-picker",{attrs:{type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.varMap[a.paramName],callback:function(t){e.$set(e.form.varMap,a.paramName,t)},expression:"form.varMap[item.paramName]"}}):e._e()],1)],1)})):e._e()],2),t("dialog-footer",{attrs:{"on-cancel":e.onCancel,"on-save":e.onSave}})],1)},f=[],g=(a(7354),{name:"add-scene-task",props:{actionOk:Function},data(){const e=(e,t,a)=>{""===t||"[]"===t?a(new Error("请选择责任人")):a()};return{form:{taskName:"",zrUserInfo:"",startDate:(new Date).format("yyyy-MM-dd"),endDate:(new Date).format("yyyy-MM-dd"),themeName:"",taskId:"",caseKey:"",prdtCode:"",prdtName:"",varMap:{}},hasSelfDefinedParam:!1,showPrdt:!1,fieldOption:[],ownerGroupList:[],rules:{taskName:[{required:!0,message:"请输入任务名称",trigger:"blur"}],zrUserInfo:[{required:!0,validator:e,trigger:"blur"}],taskId:[{required:!0,message:"请选择任务主题",trigger:"blur"}],caseKey:[{required:!0,message:"请选择启动场景",trigger:"blur"}],startDate:[{required:!0,message:"请选择开始时间",trigger:"blur"}],endDate:[{required:!0,message:"请选择截止时间",trigger:"blur"}]},caseList:[],themeCoverList:[],themeList:[],dictParentId:"********",props:{label:"themeName",children:"children",value:"pkId"},productOptions:[],filterList:[]}},mounted(){this.initThemeList(),this.initProductData()},methods:{handleThemeFilter(e){var t;console.log("qury",e),null===(t=this.$refs.taskDefThemeTreeRef)||void 0===t||t.filter(e)},filterThemeNode(e,t){return console.log("value",e,"data",t),!e||-1!==t.themeName.indexOf(e)},match(e){if(e){let t=/^[a-zA-Z0-9]+$/;t.test(e)?this.filterList=this.productOptions.filter((t=>-1!==t.productCode.toLowerCase().indexOf(e.toLowerCase()))):this.filterList=this.productOptions.filter((t=>-1!==t.productName.toLowerCase().indexOf(e.toLowerCase())))}else this.filterList=this.productOptions},async initProductData(){const e=await this.$api.PrdtTempInfoApi.query({});"00000000"===e.code?(this.productOptions=e.data,this.filterList=this.productOptions):(this.productOptions=[],this.filterList=[])},productChange(e){var t;this.form.prdtName=null===(t=this.productOptions.find((t=>t.productId===e)))||void 0===t?void 0:t.productName},themeChange(e){var t;0===e.length&&(null===(t=this.$refs.taskDefThemeTreeRef)||void 0===t||t.filter(""));this.$refs.themeRef.blur()},async initThemeList(){const e=await this.$api.MccTaskConfigApi.getQuery({userId:window.$gfui.$app.session.data.user.userId});e.success?(this.themeCoverList=e.data||[],this.themeList=this.formatTree(this.themeCoverList)):this.themeList=[]},formatTree(e){let t=JSON.parse(JSON.stringify(e));return this.transformTree(t,this.dictParentId)},transformTree(e,t){let a=[];e.forEach((s=>{if(s.parentThemeId==t){const t=this.transformTree(e,s.pkId);t.length&&(s.children=t),a.push(s)}}));const s=a.findIndex((e=>"99999999"===e.pkId));if(-1!==s){const e=a.splice(s,1);a.push(e[0])}return a},handleNodeClick(e){this.showPrdt=!1,this.form.themeName=e.themeName,this.initCase(e.pkId),this.$refs.themeRef.blur()},bizTagChange(e){var t;this.form.bizTagName=null===(t=this.$app.dict.getDictItem("AGNES_BIZ_TAG",e))||void 0===t?void 0:t.dictName},async initCase(e){this.form.caseKey="";const t=await this.$api.MccTaskCenterApi.getCaseSceneByType({themeId:e});t&&t.data&&(this.caseList=t.data,console.log("this.caseList",this.caseList))},handleParamType(e){for(let t of e)"日期"===t.paramType?(t.paramType="date",this.$set(this.form.varMap,t.paramName,window.bizDate||(new Date).format("yyyy-MM-dd")),console.log("this.form.varMap",this.form.varMap)):t.paramType="string"},async caseChange(e){let t=this.caseList.find((t=>t.taskId===e));if(this.form.taskId=t.taskId,t&&"prdt"===t.taskType&&"1"===t.execMode&&(this.showPrdt=!0),t&&t.actionParam){let e=JSON.parse(t.actionParam);("1"===t.isInvolvedPrdt&&e.length>5||"0"===t.isInvolvedPrdt&&e.length>4)&&(this.handleParamType(e),"2"===t.execMode&&(this.hasSelfDefinedParam=!0),this.$forceUpdate(),this.fieldOption=e)}},onCancel(){this.$dialog.close(this,"cancel")},getOwnerGroupList(e){this.ownerGroupList=e,this.form.zrUserInfo=JSON.stringify(e)},async onSave(e){e.preventDefault();const t=await this.$refs.form.validate();t&&(await this.actionOk(this.form),this.onCancel())}}}),v=g,k=(0,h.A)(v,u,f,!1,null,null,null),y=k.exports,C=a(12142),T={components:{},mixins:[C.Iw],props:{showType:{type:String,default:""}},data(){return{taskDateRange:[window.bizDate,window.bizDate],tempForm:{basePath:"********",startDate:window.bizDate,endDate:window.bizDate,taskCls:"01"},tempTaskCls:"01",options:[{label:"仅待办",value:"01"},{label:"仅转交",value:"02"},{label:"仅协作",value:"03"}],filters:[{label:"一级标题",command:"first"},{label:"二级标题",command:"second"}],addTaskTypes:[{label:"临时任务",command:"tempTask"},{label:"场景任务",command:"sceneTask"}],pickerOptions:{shortcuts:[{text:"最近三天",onClick(e){const t=new Date(window.bizDate),a=new Date(window.bizDate);a.setTime(a.getTime()-2592e5),e.$emit("pick",[a,t])}},{text:"最近一周",onClick(e){const t=new Date(window.bizDate),a=new Date(window.bizDate);a.setTime(a.getTime()-6048e5),e.$emit("pick",[a,t])}},{text:"最近一个月",onClick(e){const t=new Date(window.bizDate),a=new Date(window.bizDate);a.setTime(a.getTime()-2592e6),e.$emit("pick",[a,t])}},{text:"最近三个月",onClick(e){const t=new Date(window.bizDate),a=new Date(window.bizDate);a.setTime(a.getTime()-7776e6),e.$emit("pick",[a,t])}}]},cardData:[],tempChildren:[],defaultProps:{children:"childList",isLeaf:"isLeaf",label:"themeName",taskCls:"taskCls"},filterText:"",intervalList:[{label:"1分钟",value:1},{label:"3分钟",value:3},{label:"5分钟",value:5}],intervalMin:1,svgImg:this.$lcImg,freshInterval:null,intervalListShow:!1,loading:!1,ifIntervalStart:sessionStorage.getItem("ifIntervalStart")||!1,currentClikNode:{},firstLoad:!0,node:{},resolve:null,timeId:null,reFreshFlag:!1,clickNodeList:[],defaultExpandedKeys:[],defaultCheckedKeys:[],tempNode:{},getCardListTimer:null,cardListCache:null,timeout:null}},watch:{filterText(e){this.$refs.tree.filter(e)},$route(e,t){this.path===t.path&&this.clearFreshInterval(),this.path===e.path&&(this.ifIntervalStart=sessionStorage.getItem("ifIntervalStart")||!1,this.ifIntervalStart&&this.startFreshInterval())}},async mounted(){this.$nextTick((()=>{requestAnimationFrame((()=>{this.getCardList(!1)}))}))},beforeDestroy(){this.clearFreshInterval(),this.timeId&&(clearTimeout(this.timeId),this.timeId=null),this.timeout&&(clearTimeout(this.timeout),this.timeout=null),this.getCardListTimer&&(clearTimeout(this.getCardListTimer),this.getCardListTimer=null),n.l.$off("updateCard"),n.l.$off("nodeTaskMonitor"),n.l.$off("nodeTaskInfo"),this.cardListCache=null},methods:{filterNode(e,t){return!e||-1!==t.themeName.indexOf(e)},async loadeChildNode(e,t){if(this.tempForm.basePath=e.data.basePath,!e.data.leaf)return await this.getCardList(!0),void t(this.tempChildren);let a=this.tempForm;"其他任务"===e.data.themeName?a.cardType="01":"临时任务"===e.data.themeName?a.cardType="02":a.cardType="";try{let e=await this.$api.MccTaskCenterApi.todoTaskQuery({...a,showType:this.showType});if(e.success){let a=e.data.filter((e=>"2"!==e.taskType));a.map((e=>{e.isInstant=!0,e.isLeaf=!0})),t(a)}else this.$msg.error(e.message||"获取实例失败")}catch(s){this.$msg.error(s)}},async loadNode(e,t){this.node=e,this.resolve=t,0===e.level?t(this.cardData):this.loadeChildNode(e,t)},async getCardList(e,t){this.getCardListTimer&&clearTimeout(this.getCardListTimer),this.getCardListTimer=setTimeout((async()=>{this.loading=!0;try{this.tempForm.basePath="1"===t?"********":this.tempForm.basePath??"********";const a=JSON.stringify({...this.tempForm,showType:this.showType,childFlag:e});if(this.cardListCache&&this.cardListCache.key===a&&Date.now()-this.cardListCache.timestamp<5e3)return this.getSuccess(this.cardListCache.data,e),void(this.loading=!1);let s=await this.$api.MccTaskCenterApi.queryForTask({...this.tempForm,showType:this.showType});s.success?(this.cardListCache={key:a,data:s,timestamp:Date.now()},this.getSuccess(s,e)):this.$msg.error(s.message||"分组卡片查询失败")}catch(a){this.$msg.error(a)}finally{this.loading=!1}}),200)},getSuccess(e,t){this.loading=!1,e.data.forEach((e=>{0===e.totalCount||"04"===e.type||"01"===e.type&&e.countFun?e.isLeaf=!0:e.isLeaf=!1,e.isInstant=!1})),t?this.tempChildren=e.data:this.cardData=e.data},refreshClickNode(e){this.clickNodeList.length>0?(1===this.clickNodeList.length&&(this.tempNode=this.clickNodeList[0]),this.timeId=setTimeout((()=>{var t,a;this.nodeClick(this.clickNodeList[0],e),"01"!==(null===(t=this.clickNodeList[0])||void 0===t?void 0:t.cardType)&&"02"!==(null===(a=this.clickNodeList[0])||void 0===a?void 0:a.cardType)&&(this.$refs.tree.setCurrentNode(this.clickNodeList[0]),this.defaultExpandedKeys.push(this.clickNodeList[0].pkId),this.defaultCheckedKeys[0]=this.clickNodeList[0].pkId),this.clickNodeList.splice(0,1),this.refreshClickNode(e)}),800)):(this.reFreshFlag=!1,this.timeId=setTimeout((()=>{var t,a;this.nodeClick(this.tempNode,e),"01"!==(null===(t=this.tempNode)||void 0===t?void 0:t.cardType)&&"02"!==(null===(a=this.tempNode)||void 0===a?void 0:a.cardType)&&(this.$refs.tree.setCurrentNode(this.tempNode),this.defaultExpandedKeys.push(this.tempNode.pkId),this.defaultCheckedKeys[0]=this.tempNode.pkId)}),800))},queryChange(){this.resetTempForm(),this.getCardList(!1)},taskClsClickHandler(e){let t=this.options.filter((t=>t.label==e.target.innerText));this.tempForm.taskCls=this.tempTaskCls===t[0].value?"":t[0].value,this.tempTaskCls=this.tempForm.taskCls,this.queryChange()},async changeFilter(e){console.log(e)},async refreshCardTask(){this.reFreshFlag=!0,this.resetTempForm(),this.getCardList(!1),n.l.$emit("updateCard"),"03"===this.currentClikNode.type||"04"===this.currentClikNode.type?n.l.$emit("nodeTaskMonitor",JSON.stringify(this.currentClikNode),!0):n.l.$emit("nodeTaskInfo",JSON.stringify({...this.currentClikNode,updateCardNotState:!0}),!0),this.$nextTick((()=>{this.refreshClickNode(!0)}))},resetTempForm(){this.tempForm={...this.tempForm,basePath:"********",startDate:this.taskDateRange[0],endDate:this.taskDateRange[1]}},getCardStatus(e){let t=r.filter((t=>t.value===e));return t.length>0?t[0]:{}},nodeClick(e,t){this.reFreshFlag||(this.reFreshClick(e),["01","02"].indexOf(null===e||void 0===e?void 0:e.cardType)>=0&&(this.clickNodeList=[],this.clickNodeList.push(e)));const a=document.querySelectorAll(".last-level");a.forEach((e=>{const t=e.parentElement;t&&(t.classList.add("is-current"),t.classList.add("click-node"))}));let s={...e,taskCls:this.tempForm.taskCls,startDate:this.taskDateRange[0],endDate:this.taskDateRange[1]};if(this.currentClikNode=s,"03"===s.type||"04"===s.type)n.l.$emit("nodeTaskMonitor",JSON.stringify(s));else{const e="boolean"==typeof t;if(this.checkoutTempData(s)&&e)return;this.nodeClicknodeTaskInfoParams={...s},s.updateCardNotState=!!e&&t;const a=JSON.stringify(this.nodeClicknodeTaskInfoParams);n.l.$emit("nodeTaskInfo",a,t)}},reFreshClick(e){if(this.clickNodeList.length>0){var t,a,s,i,r,n;let o=this.clickNodeList.length-1;(null===e||void 0===e||null===(t=e.basePath)||void 0===t?void 0:t.split("/").length)>(null===(a=this.clickNodeList[o])||void 0===a||null===(s=a.basePath)||void 0===s?void 0:s.split("/").length)?this.clickNodeList.push(e):(null===e||void 0===e||null===(i=e.basePath)||void 0===i?void 0:i.split("/").length)==(null===(r=this.clickNodeList[o])||void 0===r||null===(n=r.basePath)||void 0===n?void 0:n.split("/").length)&&(this.clickNodeList=[],this.clickNodeList.push(e))}else this.clickNodeList=[],this.clickNodeList.push(e)},checkoutTempData(e){this.nodeClicknodeTaskInfoParams=this.nodeClicknodeTaskInfoParams||{};let t=Object.keys(this.nodeClicknodeTaskInfoParams),a=Object.keys(e);return t.length==a.length&&0==t.filter((t=>this.nodeClicknodeTaskInfoParams[t]!=e[t]))},selectTaskType(e){"tempTask"===e?this.$dialog.create({title:"新增临时任务",width:"700px",component:p,closeOnClickModal:!1,args:{actionOk:this.addTempTaskFun.bind(this)}}):this.$dialog.create({title:"新增场景任务",width:"700px",component:y,closeOnClickModal:!1,args:{actionOk:this.addSceneTaskFun.bind(this)}})},addTempTaskFun(e){this.addData("tempTaskAdd",e)},quickAadTask(){this.$dialog.create({title:"新增临时任务",width:"700px",component:p,closeOnClickModal:!1,args:{actionOk:this.addTempTaskFun.bind(this)}})},addSceneTaskFun(e){this.addData("sceneTaskAdd",e)},async addData(e,t){try{let a=await this.$api.MccTaskCenterApi[e](t);"00000000"===a.code?(this.getCardList(!1,"1"),this.$msg.success("保存成功")):this.$msg.error(a.message||"保存失败")}catch(a){this.$msg.error(a)}},async clearFreshInterval(e){if(e){const e=await this.$msg.ask("是否关闭定时刷新？");if(!e)return}this.ifIntervalStart=!1,e&&sessionStorage.setItem("ifIntervalStart",this.ifIntervalStart),this.$forceUpdate(),clearInterval(this.freshInterval)},async startFreshInterval(e){if(e){const e=await this.$msg.ask("是否开启定时刷新？");if(!e)return}this.ifIntervalStart=!0,sessionStorage.setItem("ifIntervalStart",this.ifIntervalStart),this.$forceUpdate();const t=60*this.intervalMin*1e3;this.freshInterval=setInterval((()=>{this.loading||this.refreshCardTask()}),t)},outsideClick(){this.intervalListShow=!1,this.$forceUpdate()},async chooseIntervalMin(e){const t=await this.$msg.ask(`是否切换刷新频率为${e}分钟？`);t&&(this.clearFreshInterval(),this.intervalMin=e,this.ifIntervalStart&&this.startFreshInterval(),this.$msg.success("刷新频率设置成功！"))}}},w=T,b=(0,h.A)(w,s,i,!1,null,"159b1146",null),A=b.exports}}]);