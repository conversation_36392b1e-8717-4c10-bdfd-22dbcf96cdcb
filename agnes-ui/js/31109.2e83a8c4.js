"use strict";(self["webpackChunk_hexop_agnes_ui"]=self["webpackChunk_hexop_agnes_ui"]||[]).push([[31109],{12142:function(t,e,s){s.d(e,{De:function(){return d},mf:function(){return p},PS:function(){return m},Iw:function(){return f}});s(16961),s(89370),s(21484),s(32807),s(8200),s(36886),s(56831),s(4118),s(5981),s(63074),s(39724);function a(t,e,s=!1){let a;return function(...r){const n=()=>{a=null,s||t.apply(this,r)},i=s&&!a;clearTimeout(a),a=setTimeout(n,e),i&&t.apply(this,r)}}function r(t,e){let s;return function(...a){s||(t.apply(this,a),s=!0,setTimeout((()=>s=!1),e))}}class n{constructor(){this.timers=new Set}create(t,e,s=!1){const a=s?setInterval(t,e):setTimeout((()=>{t(),this.timers.delete(a)}),e);return this.timers.add(a),a}clear(t){this.timers.has(t)&&(clearTimeout(t),clearInterval(t),this.timers.delete(t))}clearAll(){this.timers.forEach((t=>{clearTimeout(t),clearInterval(t)})),this.timers.clear()}}class i{constructor(t=50,e=3e4){this.cache=new Map,this.maxSize=t,this.ttl=e}generateKey(t){return JSON.stringify(t)}get(t){const e=this.cache.get(t);return e?Date.now()-e.timestamp>this.ttl?(this.cache.delete(t),null):e.data:null}set(t,e){if(this.cache.size>=this.maxSize){const t=this.cache.keys().next().value;this.cache.delete(t)}this.cache.set(t,{data:e,timestamp:Date.now()})}clear(){this.cache.clear()}}class o{constructor(){this.listeners=new Map}add(t,e,s,n={}){const{debounce:i=0,throttle:o=0}=n;let c=s;i>0?c=a(s,i):o>0&&(c=r(s,o)),t.addEventListener(e,c,n);const l=`${t.constructor.name}_${e}_${Date.now()}`;return this.listeners.set(l,{target:t,event:e,handler:c,originalHandler:s}),l}remove(t){const e=this.listeners.get(t);e&&(e.target.removeEventListener(e.event,e.handler),this.listeners.delete(t))}removeAll(){this.listeners.forEach(((t,e)=>{try{t.target.removeEventListener(t.event,t.handler)}catch(s){console.warn("清理事件监听器失败:",s)}})),this.listeners.clear()}}class c{constructor(){this.marks=new Map,this.stats={components:new Map,apis:new Map,memory:[]}}mark(t){this.marks.set(t,performance.now())}measure(t){const e=this.marks.get(t);if(!e)return console.warn(`Performance mark "${t}" not found`),0;const s=performance.now()-e;return this.marks.delete(t),s>100&&console.warn(`Performance warning: "${t}" took ${s.toFixed(2)}ms`),s}recordComponentStat(t,e){this.stats.components.has(t)||this.stats.components.set(t,[]);const s=this.stats.components.get(t);s.push({...e,timestamp:Date.now()}),s.length>50&&s.splice(0,s.length-50)}recordApiStat(t,e){this.stats.apis.has(t)||this.stats.apis.set(t,[]);const s=this.stats.apis.get(t);s.push({...e,timestamp:Date.now()}),s.length>50&&s.splice(0,s.length-50)}getStats(){return{components:Object.fromEntries(this.stats.components),apis:Object.fromEntries(this.stats.apis),memory:this.stats.memory.slice(-10)}}}class l{constructor(t,e,s){this.container=t,this.itemHeight=e,this.visibleCount=s,this.scrollTop=0,this.data=[]}getVisibleRange(){const t=Math.floor(this.scrollTop/this.itemHeight),e=Math.min(t+this.visibleCount,this.data.length);return{start:t,end:e}}getVisibleData(){const{start:t,end:e}=this.getVisibleRange();return this.data.slice(t,e).map(((e,s)=>({...e,index:t+s})))}updateScrollTop(t){this.scrollTop=t}setData(t){this.data=t}}const h=new n;new i,new o,new c;function u(t,e=50,s){if(!Array.isArray(t)||0===t.length)return;const a=[];for(let i=0;i<t.length;i+=e)a.push(t.slice(i,i+e));let r=0;const n=()=>{r<a.length&&requestAnimationFrame((()=>{s(a[r]),r++,r<a.length&&h.create(n,0)}))};n()}const d={data(){return{_timerManager:new n,_requestCache:new i,_eventListenerManager:new o,_performanceMonitor:new c,_debouncedFunctions:new Map,_throttledFunctions:new Map,_isDestroyed:!1}},created(){this._performanceMonitor.mark("component_create")},mounted(){const t=this._performanceMonitor.measure("component_create");this._performanceMonitor.recordComponentStat(this.$options.name||"Anonymous",{type:"mount",duration:t})},beforeDestroy(){this._isDestroyed=!0,this.cleanupPerformanceResources()},methods:{getDebounced(t,e,s=300){return this._debouncedFunctions.has(t)||this._debouncedFunctions.set(t,a(e.bind(this),s)),this._debouncedFunctions.get(t)},getThrottled(t,e,s=100){return this._throttledFunctions.has(t)||this._throttledFunctions.set(t,r(e.bind(this),s)),this._throttledFunctions.get(t)},safeSetTimer(t,e,s=!1){return this._timerManager.create(t,e,s)},safeAddEventListener(t,e,s,a={}){return this._eventListenerManager.add(t,e,s,a)},async optimizedApiCall(t,e={},s={}){const{useCache:a=!0,cacheTime:r=3e4}=s;if(this._isDestroyed)return null;const n=this._requestCache.generateKey({apiPath:t,params:e});if(a){const t=this._requestCache.get(n);if(t)return t}try{this._performanceMonitor.mark(`api_${t}`);const s=t.split(".");let r=this.$api;for(const t of s)r=r[t];const i=await r(e),o=this._performanceMonitor.measure(`api_${t}`);return this._performanceMonitor.recordApiStat(t,{duration:o,success:!0}),a&&i&&i.success&&this._requestCache.set(n,i),i}catch(i){const e=this._performanceMonitor.measure(`api_${t}`);throw this._performanceMonitor.recordApiStat(t,{duration:e,success:!1,error:i.message}),i}},batchRenderData(t,e=50,s){u(t,e,s)},cleanupPerformanceResources(){this._timerManager.clearAll(),this._eventListenerManager.removeAll(),this._requestCache.clear(),this._debouncedFunctions.clear(),this._throttledFunctions.clear()},getPerformanceStats(){return this._performanceMonitor.getStats()}}},p={mixins:[d],data(){return{_chartInstances:new Map}},beforeDestroy(){this.cleanupCharts()},methods:{createOptimizedChart(t,e={}){const s=this.$refs[t]||document.getElementById(t);if(!s)return console.warn(`ECharts容器未找到: ${t}`),null;if(this._chartInstances.has(t)){const e=this._chartInstances.get(t);e.dispose()}const a=echarts.init(s,null,{renderer:"canvas",useDirtyRect:!0,...e});return this._chartInstances.set(t,a),a},getOptimizedChartOptions(t){return{...t,animation:!1,progressive:400,progressiveThreshold:3e3,useUTC:!0,lazyUpdate:!0}},handleChartResize(){this._chartInstances.forEach((t=>{t&&"function"===typeof t.resize&&t.resize()}))},cleanupCharts(){this._chartInstances.forEach((t=>{t&&"function"===typeof t.dispose&&t.dispose()})),this._chartInstances.clear()}}},m={mixins:[d],data(){return{_virtualScrollManager:null,_optimizedScrollOptions:{step:.5,limitMoveNum:3,hoverStop:!0,direction:1,openWatch:!0,singleHeight:0,singleWidth:0,waitTime:1e3}}},methods:{getOptimizedScrollOptions(t={}){return{...this._optimizedScrollOptions,...t}},optimizeScrollData(t,e=100){return Array.isArray(t)?t.slice(0,e):[]},enableVirtualScroll(t,e,s){return this._virtualScrollManager=new l(t,e,s),this._virtualScrollManager}}},f={mixins:[d],data(){return{_treeLoadingStates:new Map,_nodeCache:new i(20,1e4)}},methods:{async optimizedLoadNode(t,e){var s;const a=(null===(s=t.data)||void 0===s?void 0:s.pkId)||t.level;if(this._treeLoadingStates.get(a))return;const r=this._nodeCache.generateKey({nodeId:a,level:t.level,...this.getNodeRequestParams(t)}),n=this._nodeCache.get(r);if(n)e(n);else{this._treeLoadingStates.set(a,!0);try{const s=await this.loadNodeData(t);this._nodeCache.set(r,s),e(s)}catch(i){console.error("加载节点数据失败:",i),e([])}finally{this._treeLoadingStates.delete(a)}}},getNodeRequestParams(t){return{}},async loadNodeData(t){throw new Error("loadNodeData method must be implemented by subclass")},batchUpdateNodes(t){this.$nextTick((()=>{t.forEach((t=>{this.updateSingleNode(t)}))}))},updateSingleNode(t){const{nodeId:e,data:s}=t,a=this.findNodeById(e);a&&Object.assign(a,s)},findNodeById(t){const e=s=>{for(const a of s){if(a.pkId===t)return a;if(a.children){const t=e(a.children);if(t)return t}}return null};return e(this.cardData||[])}}}},31109:function(t,e,s){s.r(e),s.d(e,{default:function(){return h}});var a=function(){var t=this,e=t._self._c;return e("gf-workbench-panel",{attrs:{slotQuery:"workbench-table-pie"},scopedSlots:t._u([{key:"icon",fn:function({icon:t}){return[e("i",{class:[t||"el-icon-setting"]})]}},{key:"title",fn:function({title:s}){return[e("p",[t._v(t._s(s||"今日流程总体完成情况"))]),e("i",{staticClass:"refresh-btn el-icon-refresh-left",attrs:{"aria-hidden":"true"},on:{click:function(e){return t.refreshData()}}})]}},{key:"body",fn:function(){return[e("div",{staticClass:"table-pie"},[e("div",{staticClass:"table-container"},[e("el-table",{staticClass:"el-table-grid task-table",attrs:{data:t.taskArr,"max-height":"330"}},t._l(t.tableHeader,(function(s){return e("el-table-column",{key:s.attrValue,attrs:{prop:s.attrValue,label:s.label,width:s.width?s.width:null},scopedSlots:t._u([{key:"default",fn:function(a){return["taskCategory"===s.key?e("span",[t._v(" "+t._s(t.getDictName(a.row.taskCategory))+" ")]):e("span",{style:{color:s.color}},[t._v(t._s(a.row[s.key]))])]}}],null,!0)})})),1)],1),e("div",{staticClass:"pie-chart"},[e("div",{ref:"pieCharts",staticStyle:{width:"100%",height:"100%"}}),e("div",{staticClass:"legend"},[e("p",{staticClass:"rec-legend"},[t._l(t.legendRec,(function(s,a){return e("span",{key:a},[e("em",{staticClass:"rect-block",style:{background:s.color}}),t._v(" "+t._s(s.name.substr(0,2))+" ")])})),t._l(t.legendCir,(function(s,a){return e("span",{key:a},[e("em",{staticClass:"fa fa-circle",style:{color:s.color}}),t._v(" "+t._s(s.label)+" ")])}))],2)])])])]},proxy:!0}])})},r=[],n=(s(21484),s(16961),s(89370),s(12142));s(73751);var i={mixins:[n.mf],props:{pageType:String,quartzTime:String},data(){return{statusColor:["#FCA06A","#56DF9A","#56BFDF"],tableHeader:[{label:"任务类型",attrValue:"taskCategory"},{label:"已完成",attrValue:"doneNum",width:70,color:"#1DBE28"},{label:"未完成",attrValue:"undoneNum",width:70,color:"#7A86CD"},{label:"目标数",attrValue:"targetNum",width:70,color:"#0F5EFF"}],taskArr:[],legendRec:[],legendCir:[{label:"已完成",color:"#4C6CFF"},{label:"未完成",color:"#C9CDE3"}],pieChart:{},isDestroyed:!1,freshInterval:null}},mounted(){this.getData(),this.startInterval()},watch:{$route(t,e){("personal"===this.pageType&&e.path.includes("datav.client.view")||"department"===this.pageType&&e.path.includes("datav.dep.view"))&&this.clearInterval(),("personal"===this.pageType&&t.path.includes("datav.client.view")||"department"===this.pageType&&t.path.includes("datav.dep.view"))&&this.startInterval()}},computed:{intervalMin(){const t=this.quartzTime?this.quartzTime:"5";return 60*parseInt(t)*1e3}},methods:{async getData(){this.exeTime=window.bizDate||(new Date).format("yyyy-MM-dd");let t=await this.$api.HomePageApi.selectCaseStepOfToday(this.exeTime),e=[],s=[];t&&(t.data.forEach(((t,a)=>{const r=this.getDictName(t.taskCategory);if(r){this.taskArr.push(t),e.push({value:100,name:r,color:this.statusColor[a]});const n=parseInt(t.doneNum/t.targetNum*100),i=100-n;s.push({value:n,name:"已完成"},{value:i,name:"未完成"})}})),this.legendRec=e);const a={tooltip:{show:!1},animationDuration:function(t){return 3e4*t},series:[{name:"任务类型",type:"pie",radius:[0,"35%"],center:["50%","50%"],hoverAnimation:!1,label:{show:!1},emphasis:{label:{show:!1}},itemStyle:{normal:{color:t=>t.data.color}},data:e},{name:"完成情况",type:"pie",radius:["50%","70%"],center:["50%","50%"],hoverAnimation:!1,label:{show:!0,padding:[0,-25],formatter:function(t){return t.dataIndex%2===0?"已完成 "+t.value+"%":""},fontSize:12,lineHeight:18,color:"#0F5EFF",distanceToLabelLine:-10},labelLine:{show:!1},itemStyle:{normal:{color:function(t){return["#4C6CFF","#C9CDE3"][t.dataIndex%2]}}},data:s}]};if(this.pieChart=this.createOptimizedChart("pieCharts",{renderer:"canvas",useDirtyRect:!0}),this.pieChart){const t=this.getOptimizedChartOptions(a);this.pieChart.setOption(t),this.$nextTick((()=>{!this.isDestroyed&&this.pieChart&&this.pieChart.resize()}))}},refreshData(){this.legendRec=[],this.getData()},getDictName(t){const e=this.$app.dict.getDictItem("AGNES_TASK_TYPE",t);if(e)return e.dictName},startInterval(){this.clearInterval(),this.freshInterval=this.safeSetTimer((()=>{!this.isDestroyed&&this.$route.path.includes("datav.client.view")?this.getData():this.clearInterval()}),this.intervalMin,!0)},clearInterval(){this.freshInterval&&(clearInterval(this.freshInterval),this.freshInterval=null)}},beforeDestroy(){this.isDestroyed=!0,this.clearInterval(),this.cleanupGlobalResources()}},o=i,c=s(18579),l=(0,c.A)(o,a,r,!1,null,"61eb3316",null),h=l.exports}}]);