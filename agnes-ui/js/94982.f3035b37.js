"use strict";(self["webpackChunk_hexop_agnes_ui"]=self["webpackChunk_hexop_agnes_ui"]||[]).push([[94982],{12142:function(t,e,s){s.d(e,{De:function(){return u},mf:function(){return m},PS:function(){return p},Iw:function(){return g}});s(16961),s(89370),s(21484),s(32807),s(8200),s(36886),s(56831),s(4118),s(5981),s(63074),s(39724);function a(t,e,s=!1){let a;return function(...n){const i=()=>{a=null,s||t.apply(this,n)},r=s&&!a;clearTimeout(a),a=setTimeout(i,e),r&&t.apply(this,n)}}function n(t,e){let s;return function(...a){s||(t.apply(this,a),s=!0,setTimeout((()=>s=!1),e))}}class i{constructor(){this.timers=new Set}create(t,e,s=!1){const a=s?setInterval(t,e):setTimeout((()=>{t(),this.timers.delete(a)}),e);return this.timers.add(a),a}clear(t){this.timers.has(t)&&(clearTimeout(t),clearInterval(t),this.timers.delete(t))}clearAll(){this.timers.forEach((t=>{clearTimeout(t),clearInterval(t)})),this.timers.clear()}}class r{constructor(t=50,e=3e4){this.cache=new Map,this.maxSize=t,this.ttl=e}generateKey(t){return JSON.stringify(t)}get(t){const e=this.cache.get(t);return e?Date.now()-e.timestamp>this.ttl?(this.cache.delete(t),null):e.data:null}set(t,e){if(this.cache.size>=this.maxSize){const t=this.cache.keys().next().value;this.cache.delete(t)}this.cache.set(t,{data:e,timestamp:Date.now()})}clear(){this.cache.clear()}}class o{constructor(){this.listeners=new Map}add(t,e,s,i={}){const{debounce:r=0,throttle:o=0}=i;let c=s;r>0?c=a(s,r):o>0&&(c=n(s,o)),t.addEventListener(e,c,i);const h=`${t.constructor.name}_${e}_${Date.now()}`;return this.listeners.set(h,{target:t,event:e,handler:c,originalHandler:s}),h}remove(t){const e=this.listeners.get(t);e&&(e.target.removeEventListener(e.event,e.handler),this.listeners.delete(t))}removeAll(){this.listeners.forEach(((t,e)=>{try{t.target.removeEventListener(t.event,t.handler)}catch(s){console.warn("清理事件监听器失败:",s)}})),this.listeners.clear()}}class c{constructor(){this.marks=new Map,this.stats={components:new Map,apis:new Map,memory:[]}}mark(t){this.marks.set(t,performance.now())}measure(t){const e=this.marks.get(t);if(!e)return console.warn(`Performance mark "${t}" not found`),0;const s=performance.now()-e;return this.marks.delete(t),s>100&&console.warn(`Performance warning: "${t}" took ${s.toFixed(2)}ms`),s}recordComponentStat(t,e){this.stats.components.has(t)||this.stats.components.set(t,[]);const s=this.stats.components.get(t);s.push({...e,timestamp:Date.now()}),s.length>50&&s.splice(0,s.length-50)}recordApiStat(t,e){this.stats.apis.has(t)||this.stats.apis.set(t,[]);const s=this.stats.apis.get(t);s.push({...e,timestamp:Date.now()}),s.length>50&&s.splice(0,s.length-50)}getStats(){return{components:Object.fromEntries(this.stats.components),apis:Object.fromEntries(this.stats.apis),memory:this.stats.memory.slice(-10)}}}class h{constructor(t,e,s){this.container=t,this.itemHeight=e,this.visibleCount=s,this.scrollTop=0,this.data=[]}getVisibleRange(){const t=Math.floor(this.scrollTop/this.itemHeight),e=Math.min(t+this.visibleCount,this.data.length);return{start:t,end:e}}getVisibleData(){const{start:t,end:e}=this.getVisibleRange();return this.data.slice(t,e).map(((e,s)=>({...e,index:t+s})))}updateScrollTop(t){this.scrollTop=t}setData(t){this.data=t}}const l=new i;new r,new o,new c;function d(t,e=50,s){if(!Array.isArray(t)||0===t.length)return;const a=[];for(let r=0;r<t.length;r+=e)a.push(t.slice(r,r+e));let n=0;const i=()=>{n<a.length&&requestAnimationFrame((()=>{s(a[n]),n++,n<a.length&&l.create(i,0)}))};i()}const u={data(){return{_timerManager:new i,_requestCache:new r,_eventListenerManager:new o,_performanceMonitor:new c,_debouncedFunctions:new Map,_throttledFunctions:new Map,_isDestroyed:!1}},created(){this._performanceMonitor.mark("component_create")},mounted(){const t=this._performanceMonitor.measure("component_create");this._performanceMonitor.recordComponentStat(this.$options.name||"Anonymous",{type:"mount",duration:t})},beforeDestroy(){this._isDestroyed=!0,this.cleanupPerformanceResources()},methods:{getDebounced(t,e,s=300){return this._debouncedFunctions.has(t)||this._debouncedFunctions.set(t,a(e.bind(this),s)),this._debouncedFunctions.get(t)},getThrottled(t,e,s=100){return this._throttledFunctions.has(t)||this._throttledFunctions.set(t,n(e.bind(this),s)),this._throttledFunctions.get(t)},safeSetTimer(t,e,s=!1){return this._timerManager.create(t,e,s)},safeAddEventListener(t,e,s,a={}){return this._eventListenerManager.add(t,e,s,a)},async optimizedApiCall(t,e={},s={}){const{useCache:a=!0,cacheTime:n=3e4}=s;if(this._isDestroyed)return null;const i=this._requestCache.generateKey({apiPath:t,params:e});if(a){const t=this._requestCache.get(i);if(t)return t}try{this._performanceMonitor.mark(`api_${t}`);const s=t.split(".");let n=this.$api;for(const t of s)n=n[t];const r=await n(e),o=this._performanceMonitor.measure(`api_${t}`);return this._performanceMonitor.recordApiStat(t,{duration:o,success:!0}),a&&r&&r.success&&this._requestCache.set(i,r),r}catch(r){const e=this._performanceMonitor.measure(`api_${t}`);throw this._performanceMonitor.recordApiStat(t,{duration:e,success:!1,error:r.message}),r}},batchRenderData(t,e=50,s){d(t,e,s)},cleanupPerformanceResources(){this._timerManager.clearAll(),this._eventListenerManager.removeAll(),this._requestCache.clear(),this._debouncedFunctions.clear(),this._throttledFunctions.clear()},getPerformanceStats(){return this._performanceMonitor.getStats()}}},m={mixins:[u],data(){return{_chartInstances:new Map}},beforeDestroy(){this.cleanupCharts()},methods:{createOptimizedChart(t,e={}){const s=this.$refs[t]||document.getElementById(t);if(!s)return console.warn(`ECharts容器未找到: ${t}`),null;if(this._chartInstances.has(t)){const e=this._chartInstances.get(t);e.dispose()}const a=echarts.init(s,null,{renderer:"canvas",useDirtyRect:!0,...e});return this._chartInstances.set(t,a),a},getOptimizedChartOptions(t){return{...t,animation:!1,progressive:400,progressiveThreshold:3e3,useUTC:!0,lazyUpdate:!0}},handleChartResize(){this._chartInstances.forEach((t=>{t&&"function"===typeof t.resize&&t.resize()}))},cleanupCharts(){this._chartInstances.forEach((t=>{t&&"function"===typeof t.dispose&&t.dispose()})),this._chartInstances.clear()}}},p={mixins:[u],data(){return{_virtualScrollManager:null,_optimizedScrollOptions:{step:.5,limitMoveNum:3,hoverStop:!0,direction:1,openWatch:!0,singleHeight:0,singleWidth:0,waitTime:1e3}}},methods:{getOptimizedScrollOptions(t={}){return{...this._optimizedScrollOptions,...t}},optimizeScrollData(t,e=100){return Array.isArray(t)?t.slice(0,e):[]},enableVirtualScroll(t,e,s){return this._virtualScrollManager=new h(t,e,s),this._virtualScrollManager}}},g={mixins:[u],data(){return{_treeLoadingStates:new Map,_nodeCache:new r(20,1e4)}},methods:{async optimizedLoadNode(t,e){var s;const a=(null===(s=t.data)||void 0===s?void 0:s.pkId)||t.level;if(this._treeLoadingStates.get(a))return;const n=this._nodeCache.generateKey({nodeId:a,level:t.level,...this.getNodeRequestParams(t)}),i=this._nodeCache.get(n);if(i)e(i);else{this._treeLoadingStates.set(a,!0);try{const s=await this.loadNodeData(t);this._nodeCache.set(n,s),e(s)}catch(r){console.error("加载节点数据失败:",r),e([])}finally{this._treeLoadingStates.delete(a)}}},getNodeRequestParams(t){return{}},async loadNodeData(t){throw new Error("loadNodeData method must be implemented by subclass")},batchUpdateNodes(t){this.$nextTick((()=>{t.forEach((t=>{this.updateSingleNode(t)}))}))},updateSingleNode(t){const{nodeId:e,data:s}=t,a=this.findNodeById(e);a&&Object.assign(a,s)},findNodeById(t){const e=s=>{for(const a of s){if(a.pkId===t)return a;if(a.children){const t=e(a.children);if(t)return t}}return null};return e(this.cardData||[])}}}},57654:function(t,e,s){s.d(e,{s:function(){return a}});const a=()=>document.getElementsByTagName("body")[0].style.zoom},94982:function(t,e,s){s.r(e),s.d(e,{default:function(){return u}});var a=function(){var t=this,e=t._self._c;return e("div",{directives:[{name:"resize",rawName:"v-resize",value:t.chartResize,expression:"chartResize"}],staticClass:"agnes-workbench-panel"},[t._m(0),e("div",{staticClass:"content",staticStyle:{flex:"1"}},[e("div",{staticStyle:{width:"100%",height:"100%"},style:`zoom: ${1/t.zoom};`,attrs:{id:t.chartId+"manager"}})])])},n=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header"},[e("p",{staticClass:"title"},[t._v("历任基金经理")])])}],i=(s(21484),s(16961),s(89370),s(57654)),r=s(12142);const o=s(73751);var c={mixins:[r.De],data(){return{managerData:{QB:[],ZR:[],TYPE:[],URL:[]},zoom:"1",chartId:""}},created(){let t=this.$app.views.app.nav.tabBar.tabView.id;-1===t.indexOf("default")&&(this.chartId="default")},mounted(){this.queryCustList(),this.setZoom(),this.safeAddEventListener(window,"resize",(()=>{this.setZoom(),this.chartResize()}),{throttle:200})},beforeDestroy(){this.cleanupGlobalResources()},methods:{async queryCustList(){let t=this.$app.dict.getDictItem("AGNES_HOME_PARM","jjjl").dictName||"";const e=await this.$api.workbenchManagerApi.queryCustList(t);if(e.data&&e.data.rows&&e.data.rows.length){let t=[],s=[],a=[],n=[];e.data.rows.forEach((e=>{t.push(e.QB),s.push(e.ZR),a.push(e.TYPE),n.push(e.URL)})),this.managerData.QB=t||[],this.managerData.ZR=s||[],this.managerData.TYPE=a||[],this.managerData.URL=n||[],this.drawLine()}},drawLine(){var t=document.getElementById(this.chartId+"manager"),e=o.init(t);let s=null,a=this;s={tooltip:{trigger:"axis"},color:["#3387F3","#FAA125"],legend:{data:["在职","全部"],right:20,selectedMode:!1,itemWidth:8,itemHeight:8},grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0},xAxis:[{type:"category",data:this.managerData.TYPE,nameTextStyle:{color:"#5E5E5E",fontSize:"16px"},axisLine:{show:!0,lineStyle:{color:"#E4E7EE",width:2,type:"solid"}},axisTick:{show:!1},axisLabel:{show:!0,color:"#5E5E5E",fontSize:14}}],yAxis:[{type:"value",name:"数量/个",axisLine:{show:!0,lineStyle:{color:"#E4E7EE",width:2,type:"solid"}},splitLine:{show:!0,lineStyle:{color:"#E4E7EE",width:1,type:"solid"}},nameTextStyle:{color:"#5E5E5E",fontSize:14},axisTick:{show:!1},axisLabel:{show:!0,color:"#5E5E5E",fontSize:14}}],series:[{name:"在职",type:"bar",data:this.managerData.ZR,urls:this.managerData.URL,barMaxWidth:11,barGap:0},{name:"全部",type:"bar",data:this.managerData.QB,urls:this.managerData.URL,barMaxWidth:11}]},s&&"object"===typeof s&&e.setOption(s,!0),e.getZr().on("click",(function(){a.showDetail(a.managerData.URL[0])}))},showDetail(t){const e=t,s={defaultCheckedKey:e},a="agnes.fund.treasure.chest";this.$agnesUtils.closeTab(a);let n=this.$app.views.getView(a),i=Object.assign(n,{args:s});this.$nextTick((()=>{this.$nav.showView(i)}))},chartResize(){let t=o.getInstanceByDom(document.getElementById(this.chartId+"manager"));null==t&&(t=o.init(document.getElementById(this.chartId+"manager"))),t.resize()},setZoom(){this.zoom=(0,i.s)()}}},h=c,l=s(18579),d=(0,l.A)(h,a,n,!1,null,null,null),u=d.exports}}]);