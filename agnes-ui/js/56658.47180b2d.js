"use strict";(self["webpackChunk_hexop_agnes_ui"]=self["webpackChunk_hexop_agnes_ui"]||[]).push([[56658],{36565:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAABBZJREFUSEuVVUtMXFUY/v5z77yYGSkUkOFRbusQcLAkhkSjJqaoG12YbmDRFa660HTTRBcuOiZuNK5NiGnUjYllZTQmmjRFGzcmxGjtiEJgeA44UBiY12XmnqPn3LmXe4FK/Rdzz/kf3/f/37n3DOE/TNyCBgxFgEIQ1ZBepqDWVK8IaBpH2KyjWjMR3qzSOKyHwdBJAQVcMuIAwnY86kkruWu5ioZIwBQmotn9k4iOEYhPjXBR0x4jk5OEVSANjlID26FTsSgg/dEQE7CsPXojW/U27SMQk11NiERiTkLRkymdzl4l1EkUdeHWO/FYpVKkq+tlp9RNUJ1H9XiszgSqnPbDTMT3JWQMCHBCmAlVVOVuTTGmoigW7af6kfVbfJ+uzZvSpZKV5pXBM6o4XBIbhQjzjRkQJGokSqYgpbmaxR3UTe2UMkmTzURmd+WZ2AQ3B+L7ejEIxCFPNieJZPdxoDUUfZcL3N09KP1oI9liNOtNzwcYvbR1QO/L8xIhJmKNKSUGIppJ4/eLJNJg6D/X7LRRkOU7Fgsbr3bEEk9OsNnP30Tc+Et0v/wLwAHYw9Ha7aext5gUgxOTe8u/fmKu397ubtG4rHfB5pYLJLU/fB1tmtWdOusYfecbIu05zH0BtF4Ezl70v9Hb94AH94D+K+Dcupuf/uByT4vOC0FOzQcNqYAqiVtDMexUNLQA2AHkc3mrzhJPjS8DFPN27WdQ4jrHWM79/mXPuQgTiDKhcIKcoFVqJPVHyD4Lx7L5OutuDawBaDoOeqKnvPag1m2061LDQzM1QfmbA/E2AFsNt1wv5GusN6Gt/x+ClZzVdaE9wCWOxHDMluiIzW+YrK+ZckcJ2BOXASHAF746WlJeKohEsjPkn0AKaH+9bQRsukV/5ix2obNlw0ugvfAhWO8oIDj46g+wfnrbS1Je2NjpHEhoPB9uo/aqZn8PlS1B4ttkCCtlDV0ApCgAMvUa64+dkYzuGbDeV6CNfqzi1vRb4Mvf+wjmFncfTyUC3MWReFqTReIOdOR7At7smQyn4fPRvI/AeA3U/aJKE6t3wJe+8xH88fdex3BfwC9Re7JGQoDwmRHyESwdsOHz0VkAvY/4Fq38tlgaHOkL+gkmsqZ9VUwiADPJkLThpn+u07OG/rpO9BGAs6eQbNe5uL65VP/aeEa3tZ8HEGrmdHWmZhPIKaaGXJmmMyX7PsgCMDzwci9N+py1szAMXEplGxMMAWP3a0Q4vM/VnZQY0fzdzgAYOWUAmeO1ESA3Y1FaXVz2de2YIkk5vjEgM0VIqfvAb5lLdl1qWiADO+fwKRzwYwRKLkma9hO76KkxQmZKwHk6AWd/A0LK4u3mxD99l8ib+l6acCPt8aT/rT3cHwV2SB5KcIrwjxz+B0Qdnk9KTevwAAAAAElFTkSuQmCC"},56658:function(t,s,a){a.r(s),a.d(s,{default:function(){return f}});var e=function(){var t=this,s=t._self._c;return s("div",{staticClass:"main-view",attrs:{id:""}},[s("CardView",{attrs:{showType:t.showType}}),s("div",{staticClass:"main-content"},[s("card-task",{directives:[{name:"show",rawName:"v-show",value:"card-task"===t.taskActive,expression:"taskActive === 'card-task'"}],attrs:{showType:t.showType}}),s("process-scene-task",{directives:[{name:"show",rawName:"v-show",value:"process-scene-task"===t.taskActive,expression:"taskActive === 'process-scene-task'"}],ref:"selfDefinedTask",attrs:{showType:t.showType}}),s("self-defined-task",{directives:[{name:"show",rawName:"v-show",value:"self-defined-task"===t.taskActive,expression:"taskActive === 'self-defined-task'"}],attrs:{linkPageId:t.linkPageId,pageAssignment:t.pageAssignment}}),s("monitor-prdt",{directives:[{name:"show",rawName:"v-show",value:"monitor-prdt"===t.taskActive,expression:"taskActive === 'monitor-prdt'"}],attrs:{showType:t.showType}})],1)],1)},i=[],n=(a(16961),a(89370),function(){var t=this,s=t._self._c;return s("div",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],staticClass:"card-view",class:{cjStyleCss:"true"==t.$app.dict.getDictName("AGNES_CJHX_BUTTON","cjhxCss")}},[s("div",{class:["card-item","today-todo-task"===t.activeCard?"active-card today-todo-task-active":""],on:{click:function(s){return t.selectCard("today-todo-task")}}},[s("img",{staticClass:"card-icon",attrs:{src:a(36565),alt:""}}),s("div",{staticClass:"task-name"},[t._v(" 今日必办 "),s("span",{staticClass:"card-num today-todo-task"},[t._v(t._s(t.countObj.TODAYMUSTDO))]),s("span",{staticClass:"units"},[t._v("个")])]),s("div",{staticClass:"task-detail-tit"},[s("div",[t._v(" 任务总数 "),s("span",{staticClass:"task-detail-num"},[t._v(t._s(t.countObj.TOTALNUM))])]),s("div",[t._v(" 未开始"),s("span",{staticClass:"task-detail-num"},[t._v(t._s(t.countObj.NOTDO))])])])]),s("div",{class:["card-item","timeout-task"===t.activeCard||"history-timeout-task"===t.activeCard?"active-card timeout-task-active":""],on:{click:function(s){return t.selectCard("timeout-task")}}},[s("img",{staticClass:"card-icon",attrs:{src:a(63774),alt:""}}),s("div",{staticClass:"task-name"},[t._v(" 今日超时 "),s("span",{staticClass:"card-num timeout-task"},[t._v(t._s(t.countObj.TODATOVERTIMETASK))]),s("span",{staticClass:"units"},[t._v("个")])]),s("div",{staticClass:"task-detail-tit"},[s("div",[t._v(" 超时任务 "),s("span",{staticClass:"task-detail-num"},[t._v(t._s(t.countObj.OVERTIMETASK))])]),s("div",[t._v(" 历史超时"),s("span",{staticClass:"task-detail-num timeout",on:{click:function(s){return s.stopPropagation(),t.selectCard("history-timeout-task")}}},[t._v(t._s(t.countObj.HISTORYOVERTIMETASK))])])])]),s("div",{class:["card-item","error-task"===t.activeCard?"active-card error-task-active":""],on:{click:function(s){return t.selectCard("error-task")}}},[s("img",{staticClass:"card-icon",attrs:{src:a(60483),alt:""}}),s("div",{staticClass:"task-name"},[t._v(" 今日异常 "),s("span",{staticClass:"card-num error-task"},[t._v(t._s(t.countObj.TODAYEXCEPTION))]),s("span",{staticClass:"units"},[t._v("个")])]),s("div",{staticClass:"task-detail-tit"},[s("div",[t._v(" 普通 "),s("span",{staticClass:"task-detail-num"},[t._v(t._s(t.countObj.YBEXCEPTION))])]),s("div",[t._v(" 重要"),s("span",{staticClass:"task-detail-num"},[t._v(t._s(t.countObj.ZYEXCEPTION))])]),s("div",[t._v(" 紧急"),s("span",{staticClass:"task-detail-num"},[t._v(t._s(t.countObj.JJEXCEPTION))])])])]),s("div",{class:["card-item","today-can-do-task"===t.activeCard?"active-card today-can-do-task-active":""],on:{click:function(s){return t.selectCard("today-can-do-task")}}},[s("img",{staticClass:"card-icon",attrs:{src:a(70641),alt:""}}),s("div",{staticClass:"task-name"},[t._v(" 可提前办 "),s("span",{staticClass:"card-num today-can-do-task"},[t._v(t._s(t.countObj.TODAYCANDO))]),s("span",{staticClass:"units"},[t._v("个")])]),s("div",{staticClass:"task-detail-tit"},[s("div",[t._v(" 两日内 "),s("span",{staticClass:"task-detail-num"},[t._v(t._s(t.countObj.TWODAYCANDO))])]),s("div",[t._v(" 三日内"),s("span",{staticClass:"task-detail-num"},[t._v(t._s(t.countObj.THREEDAYCANDO))])])])]),s("div",{class:["card-item","my-publish-task"===t.activeCard?"active-card my-publish-task-active":""],on:{click:function(s){return t.selectCard("my-publish-task")}}},[s("img",{staticClass:"card-icon",attrs:{src:a(91657),alt:""}}),s("div",{staticClass:"task-name"},[t._v(" 我的发布 "),s("span",{staticClass:"card-num my-publish-task"},[t._v(t._s(t.countObj.PUBTASK))]),s("span",{staticClass:"units"},[t._v("个")])]),s("div",{staticClass:"task-detail-tit"},[s("div",[t._v(" 自己办理 "),s("span",{staticClass:"task-detail-num"},[t._v(t._s(t.countObj.MYPUBTASK))])]),s("div",[t._v(" 别人办理"),s("span",{staticClass:"task-detail-num"},[t._v(t._s(t.countObj.OTHERPUBTASK))])])])])])}),o=[],c=(a(54615),a(70090)),d={props:{showType:{type:String,default:""}},data(){return{activeCard:"today-todo-task",countObj:{},cardList:[{key:"today-todo-task",value:"1"},{key:"timeout-task",value:"2"},{key:"error-task",value:"3"},{key:"today-can-do-task",value:"4"},{key:"my-publish-task",value:"5"},{key:"history-timeout-task",value:"6"}],loading:!1}},mounted(){c.l.$on("nodeTaskInfo",this.handleSelectNodeInfo),c.l.$on("nodeTaskMonitor",this.handleSelectNodeInfo),c.l.$on("updateCard",this.getTodoTaskCount)},beforeDestroy(){c.l.$off("nodeTaskInfo",this.handleSelectNodeInfo),c.l.$off("nodeTaskMonitor",this.handleSelectNodeInfo),c.l.$off("updateCard",this.getTodoTaskCount)},methods:{handleSelectNodeInfo(){this.activeCard=""},selectCard(t){this.activeCard=t;let s=this.cardList.filter((s=>s.key===t));s.length>0&&c.l.$emit("selectCard",s[0].value)},getTodoTaskCount(){this.loading=!0,this.timeout&&clearTimeout(this.timeout),this.timeout=setTimeout((async()=>{try{var t;let s;"00"===this.showType?s=await this.$api.MccTaskCenterApi.todoTaskCount({...this.form,showType:this.showType,bizDate:window.bizDate}):"01"===this.showType&&(s=await this.$api.MccTaskCenterApi.todoStepCount({...this.form,showType:this.showType,bizDate:window.bizDate})),null!==(t=s)&&void 0!==t&&t.success?this.countObj=s.data:this.$msg.error("获取任务统计失败")}catch(s){this.$msg.error(s)}this.loading=!1}),500)}}},A=d,l=a(18579),r=(0,l.A)(A,n,o,!1,null,"52d5ea2e",null),u=r.exports,k=a(76731),v=a(84924),m=a(31314),C=a(99159),h={components:{CardView:u,CardTask:k["default"],processSceneTask:v["default"],selfDefinedTask:m["default"],monitorPrdt:C["default"]},props:{showType:{type:String,default:""}},data(){return{taskActive:"card-task",linkPageId:"",pageAssignment:{}}},mounted(){this.debouncedHandleSelectCard=this.debounce(this.handleSelectCard,100),this.debouncedHandleSelectNodeInfo=this.debounce(this.handleSelectNodeInfo,100),c.l.$on("selectCard",this.debouncedHandleSelectCard),c.l.$on("nodeTaskInfo",this.debouncedHandleSelectNodeInfo),c.l.$on("nodeTaskMonitor",this.debouncedHandleSelectNodeInfo)},methods:{debounce(t,s){let a;return function(...e){const i=()=>{clearTimeout(a),t.apply(this,e)};clearTimeout(a),a=setTimeout(i,s)}},handleSelectCard(){this.taskActive="card-task"},handleSelectNodeInfo(t){var s;let a=JSON.parse(t),e=null===t||void 0===t||null===(s=t.pageAssignment)||void 0===s?void 0:s.split(",");null===e||void 0===e||e.forEach((t=>{const[s,a]=t.split(":");this.pageAssignment[s]=a})),a.linkPageId?(this.linkPageId=a.linkPageId,this.taskActive="self-defined-task"):"03"===a.type||"04"===a.type?this.taskActive="monitor-prdt":"0"===a.taskType||"1"===a.taskType?(this.taskActive="process-scene-task",this.$refs.selfDefinedTask.handleShowTaskInfo(t)):this.taskActive="card-task"}},beforeDestroy(){c.l.$off("nodeTaskInfo",this.debouncedHandleSelectNodeInfo),c.l.$off("selectCard",this.debouncedHandleSelectCard),c.l.$off("nodeTaskMonitor",this.debouncedHandleSelectNodeInfo),this.debouncedHandleSelectCard&&this.debouncedHandleSelectCard.timeout&&clearTimeout(this.debouncedHandleSelectCard.timeout),this.debouncedHandleSelectNodeInfo&&this.debouncedHandleSelectNodeInfo.timeout&&clearTimeout(this.debouncedHandleSelectNodeInfo.timeout)}},p=h,T=(0,l.A)(p,e,i,!1,null,"6cf48396",null),f=T.exports},60483:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAABC5JREFUSEuNVUtvG1UU/s6dGY/fjyZjt4kJaUlRhVskCA+xQUEBATukqllHXXgRqav+gVm1EttKFUqLlGUkrxC0ErtKrEAUESo3alVQ2jghjuPE9tjGkxnPhTt+xGMnEWdzfe455/vO4/oM4RTh165JJU0LaKbpg6LITceRuCxz7jhO2LJsJA5NvGy1KJdrnwRDxxkEcGUyFFEO4e/Yg0Nuze5dE1BkbpFtxrcaxnFEIwR8cdFf9/ujpNodW6OLHer+FuewNAAumzzckmq0stIaNHsIeDYbrDMW7qD2EIfRugymydFLou8bQthx6rS8LEp0pU/gZh6hSNhUeN2yiCsK7zupFol7V1ctN6Ze71rDQgHCYfeA66eqBt25Y/YJRM+RTMZhWQSlxXeaEvOUadskhtuwbQrJcp94pLZeUorCI7u7FTETNxt+/XrECNi+TkAEogLDMMRPTLHgLRPOarPa+kPoQgwAExSalxhmK+3G12T53YrDXQLXjXOT7t6tE9d1Vi0WY4PZ1BsNFvvwnWRw+s3Fw4ff3ZTOz/wiXX57fdDH/vXnD/j+Xlr59Mv7++vr98y1tXI4FHKETx8slaqS6D3i8e5z7EAUDINNLiz8QJL0kfnjA8iZK5DSU56O2M/X4ezswPfxJ+C8/dP66v2v3opMOlXTpJj6DwfiQKXSot2lpbBm29JBNzwB4FW9zia/+HybGPkhOn7svwWAwwFGALFm4cHD9FTSxw+aPndGFAxSXC5bJPqPUMgDsWEY7GxA7nEOz3JEJ9Xf/LtSn5yORNwW9SXQ4FQSBD0ZB7AH1AyDTYzHKsNILP0aaGwM7bXfPSZBsNUsTkTjHYLxvY5ZHMSXlsRLdqUEQAPwolpl6WigOkwgz82DLl6Ede+bEYLN0sG5mVjMW4FLkM0GEY0SUESxCKRSKTzbf85eV5O1YQLp3ffA3piBlVsdIdiovTobPxNzWDNCWtDgRaTg1Gqc+I0bKkxTGox4enDALozHxHP3AmlJkKbBeZofIfhzq5hKJBLOuUGLqraJ63NyIa8p6bSwpFFAAcUXLcqkU7011w+R5z8DTZ+H9e3yCMGzcjV5ptVykBYoXYllLOLiES4uqoMRj8tldjmdKnBAvNq+UCIBisbgvNzwlubzbeb/2rw0OzbmncHKitlZFdmsAnWzu39m8OjJE3r/0oWrErHbAMaGW+XRmVTmqv9m8be176evBDgw0zGbpkPLy53NyDknLCwovcBHpZJn2Y0QTAMYKkL4zGnaUQW5nADnR+ta1xm2tz3DPjVzj/ExgNmjm4mJNum6S+b94AiSfP7oLlMi5LXR9ZzJdHzyeY5SiaBpHD3fTI6Tjn4lo59MQaoLYn20AEGeyXA3CXH2ROi5nNBFSzwJnbTGhJfXpuv/EetHwUP6MHCP+0SC/9//0z3/BSb+r+njNAGCAAAAAElFTkSuQmCC"},63774:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAABIpJREFUSEuVVUtsE0cY/v5Zr3eTrGPjOA52CDIKkUojRCrKocCBh8TjUpUD95xyoHBAFbn0skg5cKi4gJBKDyCuHIuEEOKhRtyKCkhu2iqIpCQmsZPgx9rZh71TzW42WZOA6C+tZmf+f77vf80M4RNy9iyXentLHYqiRCWJRZpNJskyF+K2Wm5Tkrqt2VmYd+9S62MwtJVCAPf3l2O2LatC39XVblWv+3OxbtvgRI41P5+obUW0iWB0lKuplNHtOLQl+cc8FZFpmlbVdTLDNm0gus47DcPQPpW2QGdZ4IqCTU5ommboOjUCu3UDXeeqZRkx2+bctomiUc4Do2iUyLY1b27bBmkaYBiAGIWE/4WdbaN27RpZQucRiJyn00ioao1ME5yxOhObhAgQx6mTZXVxRfHHcIRhsmjU1wmMYjFWFjXxCMbHS7F6XYkGGx3HIKDmTTs7sz8CzclGo/hbGLijI3OQMXasXp+fcBxGstzFZdmPUgjnpnXjRtogXefMNCvx8OZazWAjI2p6cFAdffiQvu/r4/+MjOCPsM2LF/TVwgLtPnnS/TmfL/8yNVVbjsUybthGVeMVQSBaUS2XgUQCECPRHDtzJnmPMXzz5AmQywG7drWX/s0bQHzHjglv+eTt27PfDQx86ZpmhUwz7kWSSMAkXS9qq6u9EvB+HaFarbHjx1P/ihKIUn+sYUO6xqNHSzsUpZsrit8clkUUiVgOjY/z2OoqKJkEVlZ8DkmaYYyl50UJAtbBQQn79kne9OXLFl6/bju8Ddct9sfjOS9FKx5QEh0d4CQK3B68SFOVadr2QkBw9KiMQ4cimJxsgjHg8OEInj1r4vFjJ9jaMIyFbCLRvVaDFIAlT0fnzvH1g5VOl1As9kKSppksZ98JgliMMD6u4upVEzt3Mm/T7KyLS5c6cOXKKmo1LyONxcVCJh6Pu+l0L4pFsVSC+KexsUJnd3eGgMU1b/qwsvI3SyQGFgTB7t0STp2Scf26iYMHIzhwIIJ79xycPi3j/n0nSFWjXH67PZlMuJrGyDB6ucCrVl1OFy5wpafnnQRkALxDoZBBJPInU9WcYOxMpQjnz6uYmFiFJAHpNEO1ynHxouqRLi35EZjmTF+zuc3NZjMoFIBsFlheRku0aaRSmZOBHWsRzGF6epGGhvaUghqMjipe7h88cCB65MQJGa4L3Lnj3QYeQak0nU6lkms18LHicTgEcNJ1KDMzfr+LcXn5ORsa2vMXgAFhKMs+6N69Ea9lX71qemTN5np7vDWMqS9se3/bQbt1C5Z3VYyNcVlR4FcQQK32lLZt+/pbIvYTgJ4Pu+yD+XKr5f5Qqfz+ayx2ZP2qsCy4N2+SiMA7iXT5MmTxn88DpdLTdTKxFkTmA88gl8t5kYZF2NTrR7wIhocBXYcDEA9f16xQgH+SPlueA9i/yTqbRUvXySP78MFh+fzG2vAwKJ9H2/W85qG3T+gCm9DIA/BNBL4rXtG3fC4F+fAweDAGrgdzXRfOUJtDn3h3N9IngASpAAguuGC+kZ924GD9fz3sn12akOF//aX8j1SJbSUAAAAASUVORK5CYII="},70641:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAA/1JREFUSEuVVUtvW0UU/s5c29d5OM6redh9OFWiVLYEigJFsCHqBhYIsTEbNiwQQiAhVRWrLriR2CDBAhaVKOIH0EhsqIAVCi0rRIQKcpRUoThp4zQPN3b8yvV9HDLXvo5v7NQwC4/nnDnfd+Y7Z+YSnjZusZJYRlceCKiAL+CHUjHACmAHAVMpQv9zHYdYIOs0GGrruMVKbBkhAEHp7zmxqdS0Jj+YDejpOAp4s5WohSCmcVAB+mw/2pM3gUtil0wYYAs4SGt02JyPBySicXcX0PtU2epOCoC52ppEBShmNCq7GA0CmbkvgJCogm0dJFRwETU2OwCSdhkk/zcSKACOkE3DiR9EYe1D0qW5tjnJyqUZ9NtVkAiAzUJRABK+NtggIj+zO7c/YS+E2uskITFWJpGTNXEIpj/lUDGHQKiejZ3dIhd+bCh03bLMu5Vc5Y57IjmP9ve+RD66UtwrfGIHiITawyIQcgjkUKrQUxoVCRqL8yrCzVlZB5virefDIy9MdL/9xc+VD6ZGfPdfe9b/h5OdlAnA7XvGzP0dc/Lqla6vfl0tff3tvYOs0heVrsbY0JEnqb3bjq7HzD8SN9+J3hZEL974pYLnLvhwOeb3KPNb2sDv6ybef7kLps133/tm8w1f+KxtqyCh1+oF4JASGvdWglCao83chrj2SnRD1thmQJzSsMwASR+h/PlPxbMiGGYRrDfDIajSA4Ok/lbZ224mpcWQOrwJoLt9QVus5ay+F/VxzCOR0g12CE5uN548EAP9IxlJcCZEmJvyteWRJ/ghZaJc5fJ+bifiH7zoIZBBjkQno/XsmghHxrYkweWYguuvBtvKZNiMj747xN+7djmfeTyuDk22EsjbO9wD2pYs28DoKJDZXRUXBqKP/49E6/ubY8qZaXu4BFJ6anXYK4Fp8ktWy0+8RR7YWRbBc+clZ1uJVrZt/LXpeUDLGw83Rv0j8eMTjAPdOiya09i31gdPD9orSzQ+Mb17mkQ/pgzcuFNtVrb8KPtgxB95xiPR5AEMAjPF5qE2765mlsT4xPQKgHP/sYsebv2zeikQmfUQpD+G7nT47Lvsz6vOJXWGWVikwamZ10konwEY6kCStSzrWm5t/3tfKNZ4KsI67KWbZNSuEDMl5o9lKqUXG2SdTpAGEKtvSsfmbKSARAJIaTAA4uM7qrGYzUDBLIClWoSc5LL2c2xvJq1vbZic8AgsaOTI5X0ENBZI1WzJJLCwDEIcjIUmyCQwJ+0AFlNHvgQI7iz3Lh+1aB28laCmF0E75XMZB0mAZAq0kGg8aEjGQQsOsLRRow6nELjZMh1DANo8SJMAMvwof+0oCXcthaYTwC5Kxw97pyJ38v8LweiPS6BRpnMAAAAASUVORK5CYII="},91657:function(t){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAABFhJREFUSEuVlctPG0ccx7/j2Sdgg0vNS6ENqAbhRIAUGqkSUpO2hwo1Bw7AscmJU3puT5hT+we0Bw6InqLKnBAqkcjBVJVKWlFooDgEkEqb8AgOYOPH7np3Pe2sH5hgaLAsj+e3M5/v7zU7BBd8QqEQBaAewyNJOhEIYdSSbUZ1IZtRmCXpxFCUuD44OGifhyHlHnBwKiW6USkpFznAn1mWxQQ1a1SaZqKc0BmBiYmwotMjjyDQsuLnCVqWzRTbe3zv3m29dM0pyNjYdIWqSlX/57XjuWIzQT/rhKZlksPDd9IFRlFgIhxWWFRzm5bNxEt6DyQB5PxSLJu9qqGJL/r6DD53BHjONe2tGl3UnLmha8QwdCKbCjNE/UyqZFlhnFl45swByIrqjIqpMlU9jPGaOJvHx6fcmmBL7nxcUT0HValQo1ZWhi3LHDpOp9e5ja9JAPB6PN+AkbiViH9tGAKRZZUpiukIAB6oKjUGB28nSTAYdNX5e6qr83BNTBN9XyN9n33UD+D+3Pxy5/vd7Zt1tTWcCyDHeLz4rJW6COvpbvt5c2v7qz+ePtlzCbVZh8N/4sDGxkKc8K7hUZUWtr3d3eBva/nTsm268GQdnR2tqFDlU7Xf3NqBQCmuNtfDtKwHPzz46b63xZfV0imiZirzkUAn34VCVQK8/EABOIKUEklDc12H2131y5t0kxMTY49WFleHVLWJZSotVuBQWmGS8akpt5wQi4U85LmHFqBUmX9TAZLFbCyzP+QT67Olewy3yQgvcKkxJYtEzmY6RFF8fBmBl8nUoE+QcwJvA3iV201CoVAV4EMU0SJP1sSAS5J+PU9AVWR0BVpQ6/XAsmxED2KLq8ubn8TEtOkrdCI41QcyNj1doUoSwcsTnO4yAgKlv5UToNSFWx90FYueSKbBBQnw7ezs0pcJJU7cerVTZM2bYWRmZkZ+btv5IufDirNr5wk01dfiRqc/1xLxJP7Z3kdb6xWoiqSvPd1uisUyxg520YRG/EX3bRIOh4VoNCrm0FcAvEA8LQaoLC2Ui6D1nUZca38XseMkeAv3dLWhxpN7TewcHTY/W1o6yHEAn88wCWOMfD83J2PrBJdmcZ+qihv89L8u4q2uQu/N61hd/xs8Gj53WhVY/3F6uat0/d27twynPcfGFkTD2HfhPfAvHq2tua7X+T8l1PU54yKscG6cvsDNbn9VQ11tbwksqWuZOysrS/ObeaP8vC47PNxjOgI8isnJSSdNkf/eDwd7e+XvAh4TAL8f6O8f+FiUaS/gOo7GjybDDx++OGxoYIE8Y2RgwCSEsCKIv5MaGxspcAPA73k/Sv+f17Rn7bu7u3YwGHTOxClPuUggECjaIhEfCQSipflxaNyOD4FANMoiPh9xxvzaSCTCCvAzArlqMRIcHS2bIi7OAblxgAGjzpaCfWRkhPG0lMZ0wb3LSGltR0dHCQeUbOZ7i/PXwYV1l7rY37wKJyv/BTXHzie/v1oxAAAAAElFTkSuQmCC"}}]);