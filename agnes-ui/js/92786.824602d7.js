"use strict";(self["webpackChunk_hexop_agnes_ui"]=self["webpackChunk_hexop_agnes_ui"]||[]).push([[57882,92786],{29953:function(e,t,a){a.d(t,{A:function(){return d}});var i=function(){var e=this,t=e._self._c;return t("div",[t("el-form",{ref:"form",staticStyle:{padding:"0",overflow:"hidden"},attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"产品名称：",prop:"prdtCode"}},[t("el-select",{attrs:{type:"text",placeholder:"请选择产品名称",filterable:"",clearable:"",searchable:""},model:{value:e.form.prdtCode,callback:function(t){e.$set(e.form,"prdtCode",t)},expression:"form.prdtCode"}},e._l(e.prdtCode,(function(e){return t("el-option",{key:e.productName,attrs:{label:`${e.productName}-${e.productCode}`,value:e.productCode}})})),1)],1),t("el-form-item",{attrs:{label:"主岗人：",prop:"zhugangren"}},[t("el-select",{attrs:{type:"text",placeholder:"请选择主岗人",filterable:"",clearable:"",searchable:""},model:{value:e.form.zhugangren,callback:function(t){e.$set(e.form,"zhugangren",t)},expression:"form.zhugangren"}},e._l(e.attUser,(function(e){return t("el-option",{key:e.userId,attrs:{label:`${e.userName}[${e.userId}]`,value:e.userId}})})),1)],1),t("el-form-item",{attrs:{label:"备岗人1：",prop:"beigangone"}},[t("el-select",{attrs:{type:"text",placeholder:"请选择备岗人1",filterable:"",clearable:"",searchable:""},model:{value:e.form.beigangone,callback:function(t){e.$set(e.form,"beigangone",t)},expression:"form.beigangone"}},e._l(e.attUser,(function(e){return t("el-option",{key:e.userId,attrs:{label:`${e.userName}[${e.userId}]`,value:e.userId}})})),1)],1),t("el-form-item",{attrs:{label:"备岗人2：",prop:"beigangtwo"}},[t("el-select",{attrs:{type:"text",placeholder:"请选择备岗人2",filterable:"",clearable:"",searchable:""},model:{value:e.form.beigangtwo,callback:function(t){e.$set(e.form,"beigangtwo",t)},expression:"form.beigangtwo"}},e._l(e.attUser,(function(e){return t("el-option",{key:e.userId,attrs:{label:`${e.userName}[${e.userId}]`,value:e.userId}})})),1)],1),t("el-form-item",{attrs:{label:"备岗人3：",prop:"beigangthr"}},[t("el-select",{attrs:{type:"text",placeholder:"请选择备岗人3",filterable:"",clearable:"",searchable:""},model:{value:e.form.beigangthr,callback:function(t){e.$set(e.form,"beigangthr",t)},expression:"form.beigangthr"}},e._l(e.attUser,(function(e){return t("el-option",{key:e.userId,attrs:{label:`${e.userName}[${e.userId}]`,value:e.userId}})})),1)],1),t("el-form-item",{attrs:{label:"开始日期：",prop:"startDate"}},[t("el-date-picker",{staticClass:"date-picker",attrs:{type:"date",align:"right","value-format":"yyyy-MM-dd",placeholder:"请选择开始日期","picker-options":{disabledDate(e){return e.getTime()<Date.now()-864e5}}},model:{value:e.form.startDate,callback:function(t){e.$set(e.form,"startDate",t)},expression:"form.startDate"}})],1),t("el-form-item",{attrs:{label:"结束日期：",prop:"endDate"}},[t("el-date-picker",{staticClass:"date-picker",attrs:{type:"date",align:"right","value-format":"yyyy-MM-dd",placeholder:"请选择结束日期","picker-options":e.endDatePickerOptions},model:{value:e.form.endDate,callback:function(t){e.$set(e.form,"endDate",t)},expression:"form.endDate"}})],1)],1),t("span",{staticClass:"dialog-footer",staticStyle:{"text-align":"center"},attrs:{slot:"footer"},slot:"footer"},[t("el-button",{on:{click:e.dialogVisibletest}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v("保 存")])],1)],1)},s=[],r={name:"divideAddDlg",props:{selectObjId:{type:String,default:""},type:{type:String,default:""},dialogVisible:{type:Boolean,default:!1},currentSelectObj:{type:Object,default:()=>({})},prdtCode:{type:Array,required:!0},attUser:{type:Array,required:!0},bakProductCode:{type:String,default:"",required:!0}},computed:{endDatePickerOptions(){return{disabledDate:e=>{const t=this.form.startDate?new Date(this.form.startDate):null;if(!t)return!0;const a=new Date(t);return a.setDate(a.getDate()-1),e.getTime()<a.getTime()}}}},data(){return{fetchCode:"",Comptype:"add",prdtUpdateCode:"",form:{startDate:this.getDefaultTodayRange(),endDate:this.getDefaultForeverRange(),prdtCode:"",zhugangren:"",beigangone:"",beigangtwo:"",beigangthr:""},rules:{prdtCode:[{required:!0,message:"请输入产品名称",trigger:"blur"}],zhugangren:[{required:!0,message:"请输入主岗人",trigger:"blur"}],beigangone:[{required:!0,message:"请输入备岗人1",trigger:"blur"}],startDate:[{required:!0,message:"请输入开始日期",trigger:"blur"}],endDate:[{required:!0,message:"请输入结束日期",trigger:"blur"}]}}},watch:{dialogVisible:function(e){e&&("add"===this.type&&(this.form.id="",this.form.beigangone="",this.form.beigangtwo="",this.form.beigangthr="",this.form.zhugangren="",this.form.prdtCode=""),null!==this.currentSelectObj&&"undefined"!==typeof this.currentSelectObj&&"edit"===this.type?(console.log("可以装配了"),this.handleEditCurrentValue()):null!==this.currentSelectObj&&"undefined"!==typeof this.currentSelectObj||"edit"!==this.type||this.handleEditNoCurrentValue())}},methods:{handleEditCurrentValue(){this.form.prdtCode=this.currentSelectObj.productName+"-"+this.currentSelectObj.productCode,this.form.zhugangren=this.currentSelectObj.zhugangren,this.form.beigangone=this.currentSelectObj.beigangone,this.form.beigangtwo=this.currentSelectObj.beigangtwo,null!==this.currentSelectObj.beigangthr&&"undefined"!==typeof this.currentSelectObj.beigangthr?this.form.beigangthr=this.currentSelectObj.beigangthr:this.form.beigangthr="",this.form.endDate=this.currentSelectObj.endDate,this.form.startDate=this.currentSelectObj.startDate},handleEditNoCurrentValue(){this.$api.DivideWorkApi.getByPrdtCode(this.bakProductCode).then((e=>{e.success&&(this.form.prdtCode=e.data.productName+"-"+e.data.productCode,this.form.zhugangren=e.data.zhugangren,this.form.beigangone=e.data.beigangone,this.form.beigangtwo=e.data.beigangtwo,null!==e.data.beigangthr&&"undefined"!==typeof e.data.beigangthr?this.form.beigangthr=e.data.beigangthr:this.form.beigangthr="",this.form.endDate=this.currentSelectObj.endDate,this.form.startDate=this.currentSelectObj.startDate)})).catch((e=>this.$msg.error(e)))},getDefaultTodayRange(){const e=new Date;return e.setHours(0,0,0,0),this.formatDate(e)},getDefaultForeverRange(){const e=new Date("9998-12-31");return this.formatDate(e)},formatDate(e){const t=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${a}-${i}`},async dialogVisibletest(){this.$refs.form.resetFields(),this.form.beigangone=this.form.beigangtwo=this.form.beigangthr=this.form.zhugangren=this.form.prdtCode="",this.$emit("cancel-test")},addSave(){console.log(this.form),this.$api.DivideWorkApi.addDivide(this.form).then((e=>{if(e.success){if(console.log(e),"500"===e.data.status)return this.$msg.error("保存失败,原因为"+e.data.message),void this.dialogVisibletest();"200"===e.data.status&&(this.$msg.success("保存成功"),this.dialogVisibletest())}else this.$msg.error(e.message||"保存失败")})).catch((e=>{this.$msg.error(e)}))},async handleSubmit(){const e=await this.$refs.form.validate();e&&("add"===this.type?this.addSave():"edit"===this.type&&this.handleEdit())},handleEdit(){this.form.selectObjId=this.selectObjId,this.$api.DivideWorkApi.updateDivide(this.form).then((e=>{if(console.log(e),e.success){if("500"===e.data.status)return this.$msg.error("编辑失败,原因为"+e.data.message),void this.dialogVisibletest();"200"===e.data.status&&(this.$msg.success("编辑成功"),this.dialogVisibletest())}else this.$msg.error(e.message||"编辑失败")})).catch((e=>{this.$msg.error(e)}))}},mounted(){null!==this.currentSelectObj&&"undefined"!==typeof this.currentSelectObj&&"edit"===this.type&&(console.log("初始化装配"),this.bakProductCode=this.currentSelectObj.productCode,this.form=this.$utils.deepClone(this.currentSelectObj),this.form.prdtCode=this.currentSelectObj.productName+"-"+this.currentSelectObj.productCode,this.form.zhugangren=this.currentSelectObj.zhugangren,this.form.beigangone=this.currentSelectObj.beigangone,this.form.beigangtwo=this.currentSelectObj.beigangtwo,null!==this.currentSelectObj.beigangthr&&"undefined"!==typeof this.currentSelectObj.beigangthr?this.form.beigangthr=this.currentSelectObj.beigangthr:this.form.beigangthr="")}},l=r,o=a(18579),n=(0,o.A)(l,i,s,!1,null,null,null),d=n.exports},57882:function(e,t,a){a.r(t),a.d(t,{default:function(){return d}});var i=function(){var e=this,t=e._self._c;return t("el-select",e._g(e._b({attrs:{multiple:"",remote:"",filterable:"","remote-method":e.remoteMethod,loading:e.loading},on:{change:e.handleSelect},model:{value:e.checkedList,callback:function(t){e.checkedList=t},expression:"checkedList"}},"el-select",e.$attrs,!1),e.$listeners),[t("div",{staticClass:"flex"},[t("div",{staticStyle:{padding:"0 20px"}},[t("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.handleCheckAllChange},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")])],1),t("div",[e._v("共"+e._s(e.checkedList.length)+"个")])]),t("el-checkbox-group",{model:{value:e.checkedList,callback:function(t){e.checkedList=t},expression:"checkedList"}},e._l(e.filteredOptions,(function(a){return t("el-option",{key:a.value,attrs:{label:a.label,value:a.value}},[t("el-checkbox",{staticStyle:{"pointer-events":"none"},attrs:{label:a.value}},[e._v(e._s(a.label))])],1)})),1)],1)},s=[],r=(a(16961),a(54615),a(32807),a(24929),a(8200),a(36886),a(56831),a(4118),a(5981),a(63074),a(39724),{model:{prop:"value",event:"updateValue"},props:{value:{type:[Array,String],default(){return[]}},options:{type:Array,default(){return[]}},isString:{type:Boolean,default:!1}},data(){return{checkAll:!1,isIndeterminate:!1,list:[],checkedList:[],filteredOptions:[],loading:!1}},watch:{options:{handler(e){this.list=e.length&&e?e:[],this.filteredOptions=this.list,this.initState()},deep:!0,immediate:!0},value:{handler(e){this.checkedList="string"===typeof e?e?e.split(","):[]:e,this.initState()},deep:!0,immediate:!0}},mounted(){},methods:{initState(){const e=this.checkedList.filter((e=>this.filteredOptions.some((t=>t.value===e)))).length;this.isIndeterminate=e>0&&e<this.filteredOptions.length,this.checkAll=e===this.filteredOptions.length},handleSelect(e){this.checkedList=e||[];let t=this.checkedList;this.isString&&(t=t.join(",")),this.$emit("updateValue",t)},handleCheckAllChange(e){this.checkedList=e?Array.from(new Set([...this.checkedList,...this.filteredOptions.map((e=>e.value))])):this.checkedList.filter((e=>!this.filteredOptions.some((t=>t.value===e))));let t=this.checkedList;this.isString&&(t=t.join(",")),this.$emit("updateValue",t),this.initState()},remoteMethod(e){""!==e?(this.loading=!0,setTimeout((()=>{this.loading=!1,this.filteredOptions=this.list.filter((t=>t.label.toLowerCase().includes(e.toLowerCase())))}),200)):this.filteredOptions=this.list}}}),l=r,o=a(18579),n=(0,o.A)(l,i,s,!1,null,null,null),d=n.exports},92786:function(e,t,a){a.r(t),a.d(t,{default:function(){return S}});a(16961),a(32807);var i=function(){var e=this,t=e._self._c;return t("div",{staticClass:"flex-column"},[t("div",{staticClass:"task-query"},[t("el-form",{ref:"form",attrs:{"label-position":"right",model:e.queryArgs,"label-width":"100px",size:"mini"}},[t("el-row",{attrs:{gutter:24}},[t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"产品名称:"}},[t("virtual-select",{attrs:{placeholder:"请选择产品名称","collapse-tags":"",clearable:"",searchable:"",filterable:"",options:e.prdtCode.map((e=>({value:e.productCode,label:e.productName+"-"+e.productCode})))},model:{value:e.queryArgs.productName,callback:function(t){e.$set(e.queryArgs,"productName",t)},expression:"queryArgs.productName"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"主岗人:"}},[t("select-picker",{attrs:{placeholder:"请选择主岗人","collapse-tags":"",clearable:"",searchable:"",filterable:"",options:e.attUser.map((e=>({value:e.userId,label:e.userName})))},model:{value:e.queryArgs.zhugangren,callback:function(t){e.$set(e.queryArgs,"zhugangren",t)},expression:"queryArgs.zhugangren"}})],1)],1),t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"备岗人:"}},[t("select-picker",{attrs:{placeholder:"请选择备岗人","collapse-tags":"",clearable:"",searchable:"",filterable:"",options:e.attUser.map((e=>({value:e.userId,label:e.userName})))},model:{value:e.queryArgs.beigangren,callback:function(t){e.$set(e.queryArgs,"beigangren",t)},expression:"queryArgs.beigangren"}})],1)],1),e.collapsed?e._e():t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"分工方案:"}},[t("select-picker",{attrs:{placeholder:"请选择分工方案","collapse-tags":"",clearable:"",searchable:"",filterable:"",options:e.DivideDate},model:{value:e.queryArgs.divideWork,callback:function(t){e.$set(e.queryArgs,"divideWork",t)},expression:"queryArgs.divideWork"}})],1)],1)],1),t("el-row",{attrs:{gutter:24}},[e.collapsed?e._e():t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"产品状态:"}},[t("select-picker",{attrs:{placeholder:"请选择产品状态","collapse-tags":"",clearable:"",searchable:"",filterable:"",options:e.dictProductStatus.map((e=>({value:e.dictId,label:e.dictName})))},model:{value:e.queryArgs.productStatus,callback:function(t){e.$set(e.queryArgs,"productStatus",t)},expression:"queryArgs.productStatus"}})],1)],1),e.collapsed?e._e():t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:"产品种类:"}},[t("select-picker",{attrs:{placeholder:"请选择产品种类","collapse-tags":"",clearable:"",searchable:"",filterable:"",options:e.assetTypeDict.map((e=>({value:e.dictId,label:e.dictName})))},model:{value:e.queryArgs.assetTypeList,callback:function(t){e.$set(e.queryArgs,"assetTypeList",t)},expression:"queryArgs.assetTypeList"}})],1)],1),e.collapsed?e._e():t("el-col",{attrs:{span:6}},[t("el-form-item",{attrs:{label:" "}},[t("el-checkbox",{model:{value:e.queryArgs.allParam,callback:function(t){e.$set(e.queryArgs,"allParam",t)},expression:"queryArgs.allParam"}},[e._v("全部")])],1)],1),t("el-col",{staticClass:"button-col",attrs:{span:e.collapsed?6:5}},[t("div",{class:["button-container",e.collapsed?"button-fold":"button-not-fold"]},[t("gf-button",{staticClass:"option-btn",attrs:{type:"primary"},on:{click:function(t){return e.getQueryData()}}},[e._v("查询")]),t("gf-button",{staticClass:"option-btn",on:{click:function(t){return e.reSetSearch()}}},[e._v("重置")]),t("gf-button",{staticClass:"option-btn",on:{click:function(t){return e.hideQueryParam()}}},[e._v(e._s(e.collapsed?"展开条件":"收起条件")+" "),t("i",{class:[e.collapsed?"el-icon-arrow-down":"el-icon-arrow-up"]})])],1)])],1)],1)],1),t("div",{staticClass:"bottom",staticStyle:{"margin-top":"30px"}},[t("gf-grid",{ref:"taskGrid",attrs:{filterRemote:!1,"grid-no":"agnes-biz-product-divideWork",toolbar:"find,refresh,more","query-args":e.queryArgs,height:"100%"},on:{"selected-changed":e.selectedChanged}},[t("template",{slot:"left"},[e.$hasPermission("agnes.biz.product.divide.work.divideAdd")?t("el-button",{staticClass:"action-btn",on:{click:function(t){return e.genDivideAddDlg("add")}}},[e._v(" 新增 ")]):e._e(),e.$hasPermission("agnes.biz.product.divide.work.divideEdit")?t("el-button",{staticClass:"action-btn",attrs:{disabled:1!==e.selectNum},on:{click:()=>e.genDivideAddDlg("edit")}},[e._v(" 修改 ")]):e._e(),e.$hasPermission("agnes.biz.product.divide.work.divideDel")?t("el-button",{staticClass:"action-btn",on:{click:()=>e.deleteByPrdtCode()}},[e._v(" 删除 ")]):e._e(),e.$hasPermission("agnes.biz.product.divide.work.import")?t("el-button",{staticClass:"action-btn",on:{click:()=>e.uploadFile()}},[e._v(" 导入 ")]):e._e(),e.$hasPermission("agnes.biz.product.divide.work.export")?t("el-button",{staticClass:"action-btn",on:{click:()=>e.exportDivide()}},[e._v(" 导出 ")]):e._e(),e.$hasPermission("agnes.biz.product.divide.work.divideTemp")?t("el-button",{staticClass:"action-btn",on:{click:function(t){return e.downloadFile()}}},[e._v(" 模板 ")]):e._e(),e.$hasPermission("agnes.biz.divide.work.viewProduct")?t("el-button",{staticClass:"action-btn",on:{click:function(t){return e.viewProduct()}}},[e._v(" 未有方案产品 ")]):e._e(),t("el-checkbox",{staticStyle:{"margin-right":"10px"},attrs:{label:"全部","true-label":!0,"false-label":!1},on:{change:e.reloadData},model:{value:e.queryArgs.allParam,callback:function(t){e.$set(e.queryArgs,"allParam",t)},expression:"queryArgs.allParam"}})],1)],2)],1),t("el-dialog",{attrs:{title:"产品分工方案",visible:e.dialogVisible,width:"470px"},on:{"update:visible":function(t){e.dialogVisible=t}}},[t("DivideAddDlg",{attrs:{type:e.type,currentSelectObj:e.currentSelectObj,selectObjId:e.selectObjId,prdtCode:e.prdtCode,attUser:e.attUser,bakProductCode:e.bakProductCode,dialogVisible:e.dialogVisible},on:{"cancel-test":e.onCancelTest}})],1),t("el-dialog",{attrs:{title:"产品分工方案导入",visible:e.fileVisible,width:"350px"},on:{"update:visible":function(t){e.fileVisible=t}}},[e.fileVisible?t("fileUploadDlg",{on:{"file-upload-cancel":e.onCancelFile}}):e._e()],1)],1)},s=[],r=(a(21484),a(89370),a(29953)),l=a(70499),o=function(){var e=this,t=e._self._c;return t("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticStyle:{"max-height":"600px","overflow-y":"auto"}},[t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("div",{staticStyle:{display:"flex","flex-direction":"column","align-items":"flex-start"}},[t("div",{staticStyle:{display:"flex","align-items":"center","margin-bottom":"10px"}},[t("el-upload",{ref:"uploadFile",staticClass:"upload-demo",attrs:{action:e.uploadUrl,"auto-upload":!1,data:e.uploadData,"on-change":e.UploadChange,"before-upload":e.beforeFileUpload,accept:e.acceptType,"on-success":e.handleSuccess,"on-remove":e.handleRemove,"before-remove":e.beforeRemove,"on-exceed":e.handleExceed,"file-list":e.fileList}},[t("el-button",{staticStyle:{"vertical-align":"middle"},attrs:{size:"small",type:"primary"}},[e._v("点击上传")])],1)],1),t("div",{staticStyle:{display:"flex","align-items":"center"}},[t("span",[e._v("生效日期 :")]),t("el-date-picker",{staticStyle:{"margin-left":"7px"},attrs:{type:"date","value-format":"yyyy-MM-dd",placeholder:"选择日期"},model:{value:e.selectedDate,callback:function(t){e.selectedDate=t},expression:"selectedDate"}})],1)])]),t("div",{staticClass:"dialog-footer",staticStyle:{display:"flex","justify-content":"flex-end","margin-top":"20px"}},[t("el-button",{on:{click:e.dialogVisibletest}},[e._v("取 消")]),t("el-button",{attrs:{type:"primary"},on:{click:e.save}},[e._v("保 存")])],1)])},n=[],d={props:{row:Object,actionOk:Function,mode:String},data(){return{loading:!1,fileList:[],selectedDate:this.getDefaultTodayRange(),uploadData:{name:"",aliases:"",docId:"",fileIdList:[],selectedDate:""},requestData:{docId:""},fileMap:{fileIdList:[],name:""},fileMsgList:[],uploadUrl:"./api/agnes-app/v1/dop/prdt-divide-work/importDivide",acceptType:".xls,.xlsx"}},beforeMount(){const{objectId:e,aliases:t,name:a}=this.row;this.requestData.docId=e,this.uploadData.docId=e,this.uploadData.aliases=t,this.uploadData.name=a},methods:{getDefaultTodayRange(){const e=new Date;return e.setHours(0,0,0,0),this.formatDate(e)},formatDate(e){const t=e.getFullYear(),a=String(e.getMonth()+1).padStart(2,"0"),i=String(e.getDate()).padStart(2,"0");return`${t}-${a}-${i}`},async save(){this.uploadData.selectedDate=this.selectedDate,this.loading=!0,await this.$refs.uploadFile.submit()},handleSuccess(e,t,a){console.log(e,t,a,"success"),console.log("文件上传成功一次"),this.loading=!1,e.data&&"200"!==e.data.status?(this.$message.error(e.data.message),t.status="ready"):(this.$refs.uploadFile.clearFiles(),this.$message.success(e.data.message),this.dialogVisibletest())},async upload(){console.log("文件upload一次");try{const e=this.$api.DocManagerApi.uploadMul(this.fileMsgList),t=await this.$app.blockingView(e);t&&t.status?(this.$msg.success("上传成功"),this.$dialog.close(this),this.actionOk&&this.actionOk()):this.$msg.error(t.message)}catch(e){this.$msg.error(e)}},beforeFileUpload(e,t){console.log(e,t)},UploadChange(e,t){const a=e.size/1024/1024<100;if(!a)return setTimeout((()=>{this.$message.error(e.name+"文件超过500KB，请编辑后上传")}),10),void this.$refs.uploadFile.uploadFiles.pop();this.fileList=t,console.log(e,t)},handleRemove(e,t){console.log(e,t)},handleExceed(e,t){this.$message.warning(`当前限制选择 1 个文件，本次选择了 ${e.length} 个文件，共选择了 ${e.length+t.length} 个文件`)},beforeRemove(e,t){return console.log(t),this.$confirm(`确定移除 ${e.name}？`)},dialogVisibletest(){this.fileList=[],this.selectedDate=this.getDefaultTodayRange(),this.$emit("file-upload-cancel")}}},c=d,u=a(18579),h=(0,u.A)(c,o,n,!1,null,"62d0434d",null),g=h.exports,p=a(57882),m=function(){var e=this,t=e._self._c;return t("div",{staticStyle:{width:"100%"}},[t("div",{staticStyle:{height:"550px"}},[t("gf-grid",{ref:"grid",attrs:{filterRemote:!1,"grid-no":"biz-divideWork-product-info",height:"100%"}})],1),t("dialog-footer",{attrs:{okButtonVisible:!1}})],1)},b=[],f={},v=(0,u.A)(f,m,b,!1,null,null,null),y=v.exports,k={components:{DivideAddDlg:r.A,fileUploadDlg:g,SelectPicker:p["default"]},watch:{type:function(e){"edit"===e?console.log(e):"add"===e&&(this.currentSelectObj=null)}},data(){return{dictProductStatus:[],assetTypeDict:this.$app.dict.getDictItems("AGNES_PRODUCT_CLASS"),selected:null,bakProductCode:"",selectNum:0,prdtCode:[],selectObj:[],attUser:[],divideWork:[],dialogVisible:!1,fileVisible:!1,type:"",currentSelectObj:{},DivideDate:[],selectObjId:"",queryArgs:{allParam:!1,productName:[],zhugangren:[],beigangren:[],divideWork:[],productStatus:[],custodianBank:"",assetTypeList:[]},collapsed:!0}},mounted(){this.dictProductStatus=this.$app.dict.getDictItems("AGNES_PRODUCT_STAGE"),this.getAttUser(),this.getPrdt(),this.getDivideWork()},methods:{viewProduct(){this.$nav.showDialog(y,{width:"50%",title:this.$dialog.formatTitle("产品信息","view"),closeOnClickModal:!1})},hideQueryParam(){this.collapsed=!this.collapsed},uploadFile(){this.fileVisible=!0},async exportDivide(){let e=this.$refs.taskGrid.getSelectedRows();if(0==e.length)return void this.$msg.error("请选择需要导出的数据！");const t=await this.$msg.ask("确定要导出这些产品分工信息吗?, 是否继续?");if(!t)return;const a=[];e.forEach((e=>{a.push(e.id)})),(0,l.A)({method:"post",url:"/api/agnes-app/v1/dop/prdt-divide-work/exportDivide",data:{pkIds:a},responseType:"blob"}).then((e=>{const t=document.createElement("a");let a=new Blob([e.data],{type:"multipary/form-data"});t.style.display="none",t.href=URL.createObjectURL(a),t.setAttribute("download",decodeURI(e.headers["content-disposition"].split("=")[1])),document.body.appendChild(t),t.click(),document.body.removeChild(t)})).catch((e=>{this.$Notice.error({title:"错误",desc:"系统数据错误"}),console.log(e)}))},async deleteByPrdtCode(){const e=await this.$msg.ask("确定要删除这些产品分工信息吗?");if(!e)return;let t=this.$refs.taskGrid.getSelectedRows(),a=t.map((e=>e.id));console.log(a);try{let e=await this.$api.DivideWorkApi.deleteDivide(a);e.success?(this.$msg.success("删除成功"),this.getDivideWork()):this.$msg.error(e.message||"删除失败")}catch(i){this.$msg.error(i)}finally{this.$refs.taskGrid.reloadData()}},async selectedChanged(){this.selectNum=this.$refs.taskGrid.getSelectedRows().length,this.selectObj=this.$refs.taskGrid.getSelectedRows(),this.currentSelectObj=this.selectObj[0],this.selectObjId=this.currentSelectObj&&this.currentSelectObj.id||"",this.bakProductCode=this.currentSelectObj&&this.currentSelectObj.productCode||""},async getDivideWork(){try{let e=await this.$api.DivideWorkApi.getDivideWork();e.success?this.DivideDate=e.data:this.$msg.error(e.message||"获取分工方案列表失败")}catch(e){this.$msg.error(e)}},async getPrdt(){try{let e=await this.$api.DivideWorkApi.getPrdt();e.success?this.prdtCode=e.data:this.$msg.error(e.message||"获取产品代码失败")}catch(e){this.$msg.error(e)}},async getAttUser(){try{let e=await this.$api.DivideWorkApi.getAttUser();e.success?this.attUser=e.data:this.$msg.error(e.message||"获取责任人列表失败")}catch(e){this.$msg.error(e)}},async reSetSearch(){this.queryArgs={allParam:!1,productName:[],zhugangren:[],beigangren:[],divideWork:[],productStatus:[],productType:[],custodianBank:""},this.$nextTick((()=>{this.reloadData()}))},getQueryData(){this.reloadData()},reloadData(){this.$refs.taskGrid.reloadData(!0)},async downloadFile(){(0,l.A)({method:"post",url:"/api/agnes-app/v1/dop/prdt-divide-work/dowmloadTemplate",responseType:"blob"}).then((e=>{const t=document.createElement("a");let a=new Blob([e.data],{type:"multipary/form-data"});t.style.display="none",t.href=URL.createObjectURL(a),t.setAttribute("download",decodeURI(e.headers["content-disposition"].split("=")[1])),document.body.appendChild(t),t.click(),document.body.removeChild(t)})).catch((e=>{this.$Notice.error({title:"错误",desc:"模板下载错误"}),console.log(e)}))},genDivideAddDlg(e){"edit"===e&&(this.currentSelectObj=this.$refs.taskGrid.getSelectedRows()[0]),this.dialogVisible=!0,this.type=e},onCancelTest(){this.dialogVisible=!1,this.type="",this.$refs.taskGrid.reloadData(),this.getDivideWork()},async onCancelFile(){this.fileVisible=!1,this.getDivideWork(),this.$refs.taskGrid.reloadData(),this.getDivideWork()}}},D=k,$=(0,u.A)(D,i,s,!1,null,"62a9eb1a",null),S=$.exports}}]);