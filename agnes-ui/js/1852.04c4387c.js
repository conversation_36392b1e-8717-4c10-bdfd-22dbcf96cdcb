"use strict";(self["webpackChunk_hexop_agnes_ui"]=self["webpackChunk_hexop_agnes_ui"]||[]).push([[1852],{1852:function(t,e,s){s.r(e),s.d(e,{default:function(){return l}});var n=function(){var t=this,e=t._self._c;return e("div",{staticClass:"agnes-workbench-panel",attrs:{resize:"getShowHeight"}},[t._m(0),e("div",{staticClass:"content partner",staticStyle:{flex:"1"},attrs:{id:"partner"}},[e("div",{staticClass:"total",attrs:{id:"partner-total"}},t._l(t.acountList,(function(s,n){return e("div",{key:n,staticClass:"total-item",class:`total-item-bg${n}`,on:{click:function(e){return t.showDetail(s.URL)}}},[e("div",{staticClass:"total-item-text"},[e("p",{staticStyle:{"font-weight":"500","font-size":"16px",color:"#333333"}},[t._v(t._s(s.TYPE))]),e("div",{staticStyle:{"font-size":"28px","font-weight":"600","margin-top":"5px"}},[t._v(t._s(s.SL)),e("span",{staticStyle:{"font-weight":"500","font-size":"16px",color:"#333333"}},[t._v("个")])])])])})),0),e("div",{staticClass:"list",attrs:{id:"partner-list"}},[t._l(t.filterOragnList,(function(s,n){return e("div",{key:n,staticClass:"partner-item",on:{click:function(e){return t.showDetail(s.URL)}}},[e("svg-icon",{attrs:{name:"person-icon",height:"16px",width:"16px"}}),e("span",{staticClass:"ellipsis",staticStyle:{"margin-left":"5px",flex:"1"},attrs:{title:s.NAME}},[t._v(t._s(s.NAME))])],1)})),t.filterOragnList.length<t.organList.length?e("div",{staticClass:"partner-item",staticStyle:{"justify-content":"center"},on:{click:function(e){return t.showDetail("hzhb")}}},[t._v("···")]):t._e()],2)])])},i=[function(){var t=this,e=t._self._c;return e("div",{staticClass:"header"},[e("p",{staticClass:"title"},[t._v("合作伙伴")])])}],r=(s(16961),s(54615),s(12142));(function(){let t=0})();var a={mixins:[r.De],name:"workbench-partner",data(){return{acountList:[],organList:[],showIndex:2}},computed:{filterOragnList(){return this.organList.filter(((t,e)=>e<3*this.showIndex-1))}},mounted(){this.queryCustList1(),this.queryCustList2(),this.$nextTick((()=>{this.getShowHeight()})),this.safeAddEventListener(window,"resize",this.getShowHeight,{throttle:250})},beforeDestroy(){this.cleanupGlobalResources()},methods:{async queryCustList1(){let t=this.$app.dict.getDictItem("AGNES_HOME_PARM","hzhbsltj").dictName||"";const e=await this.$api.workbenchManagerApi.queryCustList(t);e.data&&e.data.rows&&e.data.rows.length&&(this.acountList=e.data.rows||[])},async queryCustList2(){let t=this.$app.dict.getDictItem("AGNES_HOME_PARM","hzhbjgmc").dictName||"";const e=await this.$api.workbenchManagerApi.queryCustList(t);e.data&&e.data.rows&&e.data.rows.length&&(this.organList=e.data.rows||[])},showDetail(t){const e=t,s={defaultCheckedKey:e},n="agnes.fund.treasure.chest";this.$agnesUtils.closeTab(n);let i=this.$app.views.getView(n),r=Object.assign(i,{args:s});this.$nextTick((()=>{this.$nav.showView(r)}))},getShowHeight(){let t=document.getElementById("partner").offsetHeight,e=document.getElementById("partner-total").offsetHeight||100;this.showIndex=t&&e&&Math.floor((t-e)/50)||2}}},o=a,c=s(18579),h=(0,c.A)(o,n,i,!1,null,"45977712",null),l=h.exports},12142:function(t,e,s){s.d(e,{De:function(){return d},mf:function(){return p},PS:function(){return m},Iw:function(){return g}});s(16961),s(89370),s(21484),s(32807),s(8200),s(36886),s(56831),s(4118),s(5981),s(63074),s(39724);function n(t,e,s=!1){let n;return function(...i){const r=()=>{n=null,s||t.apply(this,i)},a=s&&!n;clearTimeout(n),n=setTimeout(r,e),a&&t.apply(this,i)}}function i(t,e){let s;return function(...n){s||(t.apply(this,n),s=!0,setTimeout((()=>s=!1),e))}}class r{constructor(){this.timers=new Set}create(t,e,s=!1){const n=s?setInterval(t,e):setTimeout((()=>{t(),this.timers.delete(n)}),e);return this.timers.add(n),n}clear(t){this.timers.has(t)&&(clearTimeout(t),clearInterval(t),this.timers.delete(t))}clearAll(){this.timers.forEach((t=>{clearTimeout(t),clearInterval(t)})),this.timers.clear()}}class a{constructor(t=50,e=3e4){this.cache=new Map,this.maxSize=t,this.ttl=e}generateKey(t){return JSON.stringify(t)}get(t){const e=this.cache.get(t);return e?Date.now()-e.timestamp>this.ttl?(this.cache.delete(t),null):e.data:null}set(t,e){if(this.cache.size>=this.maxSize){const t=this.cache.keys().next().value;this.cache.delete(t)}this.cache.set(t,{data:e,timestamp:Date.now()})}clear(){this.cache.clear()}}class o{constructor(){this.listeners=new Map}add(t,e,s,r={}){const{debounce:a=0,throttle:o=0}=r;let c=s;a>0?c=n(s,a):o>0&&(c=i(s,o)),t.addEventListener(e,c,r);const h=`${t.constructor.name}_${e}_${Date.now()}`;return this.listeners.set(h,{target:t,event:e,handler:c,originalHandler:s}),h}remove(t){const e=this.listeners.get(t);e&&(e.target.removeEventListener(e.event,e.handler),this.listeners.delete(t))}removeAll(){this.listeners.forEach(((t,e)=>{try{t.target.removeEventListener(t.event,t.handler)}catch(s){console.warn("清理事件监听器失败:",s)}})),this.listeners.clear()}}class c{constructor(){this.marks=new Map,this.stats={components:new Map,apis:new Map,memory:[]}}mark(t){this.marks.set(t,performance.now())}measure(t){const e=this.marks.get(t);if(!e)return console.warn(`Performance mark "${t}" not found`),0;const s=performance.now()-e;return this.marks.delete(t),s>100&&console.warn(`Performance warning: "${t}" took ${s.toFixed(2)}ms`),s}recordComponentStat(t,e){this.stats.components.has(t)||this.stats.components.set(t,[]);const s=this.stats.components.get(t);s.push({...e,timestamp:Date.now()}),s.length>50&&s.splice(0,s.length-50)}recordApiStat(t,e){this.stats.apis.has(t)||this.stats.apis.set(t,[]);const s=this.stats.apis.get(t);s.push({...e,timestamp:Date.now()}),s.length>50&&s.splice(0,s.length-50)}getStats(){return{components:Object.fromEntries(this.stats.components),apis:Object.fromEntries(this.stats.apis),memory:this.stats.memory.slice(-10)}}}class h{constructor(t,e,s){this.container=t,this.itemHeight=e,this.visibleCount=s,this.scrollTop=0,this.data=[]}getVisibleRange(){const t=Math.floor(this.scrollTop/this.itemHeight),e=Math.min(t+this.visibleCount,this.data.length);return{start:t,end:e}}getVisibleData(){const{start:t,end:e}=this.getVisibleRange();return this.data.slice(t,e).map(((e,s)=>({...e,index:t+s})))}updateScrollTop(t){this.scrollTop=t}setData(t){this.data=t}}const l=new r;new a,new o,new c;function u(t,e=50,s){if(!Array.isArray(t)||0===t.length)return;const n=[];for(let a=0;a<t.length;a+=e)n.push(t.slice(a,a+e));let i=0;const r=()=>{i<n.length&&requestAnimationFrame((()=>{s(n[i]),i++,i<n.length&&l.create(r,0)}))};r()}const d={data(){return{_timerManager:new r,_requestCache:new a,_eventListenerManager:new o,_performanceMonitor:new c,_debouncedFunctions:new Map,_throttledFunctions:new Map,_isDestroyed:!1}},created(){this._performanceMonitor.mark("component_create")},mounted(){const t=this._performanceMonitor.measure("component_create");this._performanceMonitor.recordComponentStat(this.$options.name||"Anonymous",{type:"mount",duration:t})},beforeDestroy(){this._isDestroyed=!0,this.cleanupPerformanceResources()},methods:{getDebounced(t,e,s=300){return this._debouncedFunctions.has(t)||this._debouncedFunctions.set(t,n(e.bind(this),s)),this._debouncedFunctions.get(t)},getThrottled(t,e,s=100){return this._throttledFunctions.has(t)||this._throttledFunctions.set(t,i(e.bind(this),s)),this._throttledFunctions.get(t)},safeSetTimer(t,e,s=!1){return this._timerManager.create(t,e,s)},safeAddEventListener(t,e,s,n={}){return this._eventListenerManager.add(t,e,s,n)},async optimizedApiCall(t,e={},s={}){const{useCache:n=!0,cacheTime:i=3e4}=s;if(this._isDestroyed)return null;const r=this._requestCache.generateKey({apiPath:t,params:e});if(n){const t=this._requestCache.get(r);if(t)return t}try{this._performanceMonitor.mark(`api_${t}`);const s=t.split(".");let i=this.$api;for(const t of s)i=i[t];const a=await i(e),o=this._performanceMonitor.measure(`api_${t}`);return this._performanceMonitor.recordApiStat(t,{duration:o,success:!0}),n&&a&&a.success&&this._requestCache.set(r,a),a}catch(a){const e=this._performanceMonitor.measure(`api_${t}`);throw this._performanceMonitor.recordApiStat(t,{duration:e,success:!1,error:a.message}),a}},batchRenderData(t,e=50,s){u(t,e,s)},cleanupPerformanceResources(){this._timerManager.clearAll(),this._eventListenerManager.removeAll(),this._requestCache.clear(),this._debouncedFunctions.clear(),this._throttledFunctions.clear()},getPerformanceStats(){return this._performanceMonitor.getStats()}}},p={mixins:[d],data(){return{_chartInstances:new Map}},beforeDestroy(){this.cleanupCharts()},methods:{createOptimizedChart(t,e={}){const s=this.$refs[t]||document.getElementById(t);if(!s)return console.warn(`ECharts容器未找到: ${t}`),null;if(this._chartInstances.has(t)){const e=this._chartInstances.get(t);e.dispose()}const n=echarts.init(s,null,{renderer:"canvas",useDirtyRect:!0,...e});return this._chartInstances.set(t,n),n},getOptimizedChartOptions(t){return{...t,animation:!1,progressive:400,progressiveThreshold:3e3,useUTC:!0,lazyUpdate:!0}},handleChartResize(){this._chartInstances.forEach((t=>{t&&"function"===typeof t.resize&&t.resize()}))},cleanupCharts(){this._chartInstances.forEach((t=>{t&&"function"===typeof t.dispose&&t.dispose()})),this._chartInstances.clear()}}},m={mixins:[d],data(){return{_virtualScrollManager:null,_optimizedScrollOptions:{step:.5,limitMoveNum:3,hoverStop:!0,direction:1,openWatch:!0,singleHeight:0,singleWidth:0,waitTime:1e3}}},methods:{getOptimizedScrollOptions(t={}){return{...this._optimizedScrollOptions,...t}},optimizeScrollData(t,e=100){return Array.isArray(t)?t.slice(0,e):[]},enableVirtualScroll(t,e,s){return this._virtualScrollManager=new h(t,e,s),this._virtualScrollManager}}},g={mixins:[d],data(){return{_treeLoadingStates:new Map,_nodeCache:new a(20,1e4)}},methods:{async optimizedLoadNode(t,e){var s;const n=(null===(s=t.data)||void 0===s?void 0:s.pkId)||t.level;if(this._treeLoadingStates.get(n))return;const i=this._nodeCache.generateKey({nodeId:n,level:t.level,...this.getNodeRequestParams(t)}),r=this._nodeCache.get(i);if(r)e(r);else{this._treeLoadingStates.set(n,!0);try{const s=await this.loadNodeData(t);this._nodeCache.set(i,s),e(s)}catch(a){console.error("加载节点数据失败:",a),e([])}finally{this._treeLoadingStates.delete(n)}}},getNodeRequestParams(t){return{}},async loadNodeData(t){throw new Error("loadNodeData method must be implemented by subclass")},batchUpdateNodes(t){this.$nextTick((()=>{t.forEach((t=>{this.updateSingleNode(t)}))}))},updateSingleNode(t){const{nodeId:e,data:s}=t,n=this.findNodeById(e);n&&Object.assign(n,s)},findNodeById(t){const e=s=>{for(const n of s){if(n.pkId===t)return n;if(n.children){const t=e(n.children);if(t)return t}}return null};return e(this.cardData||[])}}}}}]);