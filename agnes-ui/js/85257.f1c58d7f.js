"use strict";(self["webpackChunk_hexop_agnes_ui"]=self["webpackChunk_hexop_agnes_ui"]||[]).push([[85257],{85257:function(e,t,r){r.r(t),r.d(t,{default:function(){return n}});var o=function(){var e=this,t=e._self._c;return t("div",[e.isNewCombination?e._e():t("el-form",{ref:"queryForm",staticStyle:{padding:"10px 30px 10px 10px"},attrs:{"label-width":"110px",model:e.query,rules:e.rules}},[t("div",{staticClass:"line"},[t("el-form-item",{attrs:{label:"结构代码:",prop:"treeCode"}},[t("el-input",{attrs:{disabled:"edit"===e.mode,maxlength:10},model:{value:e.query.treeCode,callback:function(t){e.$set(e.query,"treeCode",t)},expression:"query.treeCode"}})],1),t("el-form-item",{attrs:{label:"结构名称:",prop:"treeName"}},[t("el-input",{attrs:{maxlength:"20"},model:{value:e.query.treeName,callback:function(t){e.$set(e.query,"treeName",t)},expression:"query.treeName"}})],1)],1),t("div",{staticClass:"line"},[t("el-form-item",{attrs:{label:"上级结构:",prop:"parentNodeCode"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{disabled:"edit"===e.mode,placeholder:"请选择",filterable:"",clearable:""},model:{value:e.query.parentNodeCode,callback:function(t){e.$set(e.query,"parentNodeCode",t)},expression:"query.parentNodeCode"}},e._l(e.parentOption,(function(e){return t("gf-filter-option",{key:e.parentId,attrs:{label:e.label,value:e.parentId}})})),1)],1),t("el-form-item",{attrs:{label:"分类规则:"}},[t("gf-dict",{attrs:{filterable:"",clearable:"",placeholder:"请选择","dict-type":"AGNES_PROD_TREE_TYPE"},model:{value:e.query.sortRule,callback:function(t){e.$set(e.query,"sortRule",t)},expression:"query.sortRule"}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.addNextNode,expression:"addNextNode"}],staticClass:"line"},[t("el-form-item",{attrs:{label:"用户:"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.query.userId,callback:function(t){e.$set(e.query,"userId",t)},expression:"query.userId"}},e._l(e.usersOption,(function(e){return t("gf-filter-option",{key:e.userId,attrs:{label:e.userName,value:e.userId}})})),1)],1),t("el-form-item",{attrs:{label:"显示未分配:"}},[t("gf-dict",{attrs:{filterable:"",clearable:"","dict-type":"OPDS_YES_NO"},model:{value:e.query.isShowUndis,callback:function(t){e.$set(e.query,"isShowUndis",t)},expression:"query.isShowUndis"}})],1)],1),t("div",{directives:[{name:"show",rawName:"v-show",value:e.addNextNode,expression:"addNextNode"}],staticStyle:{width:"50%"}},[t("el-form-item",{attrs:{label:"产品权限过滤:"}},[t("gf-dict",{attrs:{filterable:"",clearable:"","dict-type":"OPDS_YES_NO"},model:{value:e.query.productPermission,callback:function(t){e.$set(e.query,"productPermission",t)},expression:"query.productPermission"}})],1)],1)]),e.isNewCombination?t("el-form",{staticStyle:{padding:"10px 30px 10px 10px"},attrs:{"label-width":"110px"}},[t("div",{staticClass:"line"},[t("el-form-item",{attrs:{label:"树形结构代码:"}},[t("el-select",{staticStyle:{width:"50%"},attrs:{placeholder:"请选择",filterable:"",clearable:""},model:{value:e.query.treeCode,callback:function(t){e.$set(e.query,"treeCode",t)},expression:"query.treeCode"}},e._l(e.treeCodeOption,(function(e){return t("gf-filter-option",{key:e.id,attrs:{label:e.label,value:e.id}})})),1)],1)],1),t("el-main",{staticClass:"el-border",staticStyle:{"padding-top":"0px","padding-bottom":"0px"},attrs:{height:"500px"}},[t("gf-grid",{ref:"grid",attrs:{filterRemote:!1,"grid-no":"agnes-product-tree-field",height:"450px"}})],1)],1):e._e(),t("dialog-footer",{attrs:{"on-save":e.onSave,"on-cancel":e.onCancel,"ok-button-title":"确认"}})],1)},i=[],a=(r(21484),r(16961),r(89370),{props:{addNextNode:Boolean,rootNodeCode:String,treeCode:String,command:String,isNewCombination:{type:Boolean,default:!1},parentNodes:{type:Array,default:()=>[]},mode:{type:String},row:Object,actionOk:Function},data(){let e=(e,t,r)=>{let o=this;if(t){const e=o.$api.productTreeApi.getTreeByCode(this.query.treeCode);e.then((function(e){e.data&&"add"===o.mode?r(new Error("该结构代码已存在！")):r()}))}else r()};return{parentOption:[{parentId:"[root]",label:"[root]"}],roleOption:[{roleId:"1",label:"roleName"}],usersOption:[],showOption:[{id:1,label:"是"},{id:2,label:"否"}],treeCodeOption:[{id:"[root]",label:"[root]"}],query:{treeCode:"",treeName:"",parentNodeCode:"[root]",sortRule:"",rootNodeCode:"",isShowUndis:"",userId:"",productPermission:"0"},rules:{treeCode:[{required:!0,message:"请输入结构代码",trigger:"blur"},{validator:e,trigger:"change"}],treeName:[{required:!0,message:"请输入结构名称",trigger:"blur"}],parentNodeCode:[{required:!0,message:"请选择上级结构",trigger:"change"}]}}},mounted(){this.parentNodes.length>0&&(this.parentOption=this.parentNodes,this.query.parentNodeCode=this.parentOption[0].parentId),this.row&&(this.query=this.row),this.getUserInfo()},methods:{onCancel(){this.$dialog.close(this,"cancel")},async getUserInfo(){const e=await this.$api.productTreeApi.getUserInfo();this.usersOption=e.data},async submitForm(){try{if(this.query.rootNodeCode||(this.rootNodeCode?this.query.rootNodeCode=this.rootNodeCode:this.query.rootNodeCode=this.query.treeCode),"03"!==this.command){const e=this.$api.productTreeApi.saveTreeStructure(this.query);await this.$app.blockingApp(e)}else{let e=this.$refs.grid.getSelectedRows(),t=[];e.forEach((e=>{let r={};r.treeCode=this.treeCode,r.rootNodeCode=this.query.rootNodeCode,r.prodCode=e.productCode,t.push(r)}));const r=this.$api.productTreeApi.saveTreeProd(t);await this.$app.blockingApp(r)}this.actionOk(this.query.rootNodeCode),this.$dialog.close(this),this.$msg.success("保存成功")}catch(e){this.$msg.error(e)}},onSave(){this.isNewCombination?this.submitForm():this.$refs.queryForm.validate((e=>{if(!e)return!1;this.submitForm()}))}}}),s=a,l=r(18579),d=(0,l.A)(s,o,i,!1,null,null,null),n=d.exports}}]);