.ql-cell-selected-after,
.ql-cell-selected::after,
.ql-cell-focused::after {
    content: "";
    pointer-events: none;
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(158, 207, 250, .3)
}

.ql-table-border-shadow,
.ql-table-select-container,
.ql-table-dropdown-list,
.ql-table-dropdown-properties-list,
.ql-table-menus-container {
    border-radius: 2px;
    border: 1px solid #ccced1;
    background: #fff;
    box-shadow: 0 1px 2px 1px rgba(0, 0, 0, .1490196078)
}

.ql-table-triangle-common,
.ql-table-tooltip-error::before,
.label-field-view-status::before,
.ql-table-tooltip::before,
.ql-table-triangle-down:not(.ql-table-triangle-none)::after,
.ql-table-triangle-down:not(.ql-table-triangle-none)::before,
.ql-table-triangle-up:not(.ql-table-triangle-none)::after,
.ql-table-triangle-up:not(.ql-table-triangle-none)::before {
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
    content: "";
    border: 10px solid rgba(0, 0, 0, 0)
}

.ql-table-input-focus,
.ql-table-color-container .color-picker .color-picker-select>.erase-container,
.ql-table-selected,
.ql-table-properties-form .ql-table-dropdown-selected,
.ql-table-properties-form .ql-table-color-selected,
.ql-table-input:focus,
.ql-table-color-container .label-field-view-color .property-input:focus,
.ql-table-properties-form .property-input:focus {
    border: 1px solid #3779eb;
    box-shadow: 0 0 0 3px #cae1fc
}

.ql-table-input,
.ql-table-color-container .label-field-view-color .property-input,
.ql-table-properties-form .property-input {
    width: 80px;
    height: 30px;
    border: 1px solid #ccced1;
    outline: none;
    padding-left: 6px;
    background: inherit
}

.ql-table-input:focus::placeholder,
.ql-table-color-container .label-field-view-color .property-input:focus::placeholder,
.ql-table-properties-form .property-input:focus::placeholder {
    color: rgba(0, 0, 0, 0)
}

.ql-table-input:focus+label,
.ql-table-color-container .label-field-view-color .property-input:focus+label,
.ql-table-properties-form .property-input:focus+label {
    display: block
}

.ql-table-input:not(:placeholder-shown)+label,
.ql-table-color-container .label-field-view-color .property-input:not(:placeholder-shown)+label,
.ql-table-properties-form .property-input:not(:placeholder-shown)+label {
    display: block
}

.ql-table-temporary {
    display: none
}

.ql-table-center,
.ql-table-select-container .ql-table-select-list,
.ql-table-select-container,
.ql-table-color-container .color-picker .color-picker-palette .color-picker-wrap .iro-container,
.ql-table-color-container .color-picker,
.ql-table-properties-form .properties-form-action-row>button,
.ql-operate-line-container {
    display: flex;
    justify-content: center;
    align-items: center
}

.ql-table-selected,
.ql-table-properties-form .ql-table-dropdown-selected,
.ql-table-properties-form .ql-table-color-selected {
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAMAAAAoLQ9TAAAAM1BMVEUAAAAyMjIyMjIiIiIyMjIyMjIyMjIyMjIxMTEyMjIyMjIyMjIwMDAzMzMxMTEwMDAzMzOYbpUsAAAAEXRSTlMA/XgF8HRua2fm2rI3rzk1Lf2cC1oAAAA8SURBVBjTY6AUsHKh8RnZ8PKZoHwBZhifHcLg5GVG4TPwsXAzQ/gwwAMUYWLkALIQIlxgPgLwc3JQ4h0Ae0ABBy2kuXoAAAAASUVORK5CYII=);
    background-repeat: no-repeat;
    background-size: 16px;
    box-sizing: border-box
}

.ql-operate-line-container {
    position: absolute;
    z-index: 10
}

.ql-operate-line-container .ql-operate-line {
    background-color: #0589f3
}

.ql-operate-block {
    position: absolute;
    z-index: 10;
    border: 1px solid #979797;
    cursor: nwse-resize
}

.ql-operate-block-move {
    cursor: crosshair;
    border: none
}

.ql-operate-drag-table {
    border: 1px dashed #000;
    position: absolute
}

.ql-cell-focused {
    position: relative
}

.ql-cell-focused::after {
    border: 1px solid #3779eb
}

.ql-cell-selected {
    position: relative
}

.ql-table-menus-container {
    position: absolute;
    display: flex;
    align-items: center;
    height: 40px;
    width: fit-content;
    padding: 4px;
    box-sizing: border-box;
    z-index: 1
}

.ql-table-triangle-up:not(.ql-table-triangle-none)::before {
    bottom: -20px;
    border-top-color: rgba(0, 0, 0, .1490196078) !important
}

.ql-table-triangle-up:not(.ql-table-triangle-none)::after {
    bottom: -19px;
    border-top-color: #fff !important
}

.ql-table-triangle-down:not(.ql-table-triangle-none)::before {
    top: -20px;
    border-bottom-color: rgba(0, 0, 0, .1490196078) !important
}

.ql-table-triangle-down:not(.ql-table-triangle-none)::after {
    top: -19px;
    border-bottom-color: #fff !important
}

.label-field-view {
    position: relative
}

.label-field-view-input-wrapper {
    position: relative;
    height: 100%
}

.label-field-view-input-wrapper>label {
    position: absolute;
    left: 0;
    top: -50%;
    transform: translateY(50%) scale(0.75);
    color: #999;
    background: #fff;
    display: none
}

.label-field-view-status {
    max-width: 160px;
    width: max-content
}

.label-field-view-error>input {
    border-color: #db3700 !important;
    animation: ql-table-input-shake .3s ease both
}

.label-field-view-error>input:focus {
    box-shadow: 0 0 0 3px rgba(255, 64, 31, .3019607843) !important
}

.label-field-view-error>label {
    color: #db3700
}

.ql-table-dropdown,
.ql-table-dropdown-properties {
    display: flex;
    height: 100%;
    align-items: center;
    position: relative;
    padding: 0 4px
}

.ql-table-dropdown:hover,
.ql-table-dropdown-properties:hover {
    background: #f0f0f0
}

.ql-table-dropdown-text,
.ql-table-dropdown-properties-text {
    flex: 1;
    height: 100%;
    margin-right: 7px;
    display: flex;
    align-items: center
}

.ql-table-dropdown-list,
.ql-table-dropdown-properties-list {
    position: absolute;
    left: 0;
    bottom: 0;
    transform: translateY(100%);
    margin: 0;
    padding: 0;
    width: 170px;
    z-index: 10
}

.ql-table-dropdown-list li,
.ql-table-dropdown-properties-list li {
    list-style: none;
    line-height: 30px;
    padding-left: 10px
}

.ql-table-dropdown-list li:hover,
.ql-table-dropdown-properties-list li:hover {
    background-color: #f0f0f0
}

.ql-table-dropdown-label,
.ql-table-dropdown-properties-label {
    width: 100%;
    min-width: 100%;
    line-height: 24px;
    font-weight: bold;
    margin-bottom: 6px;
    display: block
}

.ql-table-tooltip-hover {
    display: flex;
    position: relative
}

.ql-table-tooltip-hover:hover .ql-table-tooltip {
    display: block
}

.ql-table-tooltip-hover:hover+.ql-table-tooltip {
    display: block
}

.ql-table-tooltip {
    font-size: 12px;
    min-width: 32px;
    line-height: 20px;
    padding: 6px;
    white-space: nowrap;
    color: #fff;
    text-align: center;
    word-wrap: break-word;
    background: rgba(0, 0, 0, .8509803922);
    border-radius: 6px;
    position: absolute;
    z-index: 11;
    left: 50%;
    bottom: -10px;
    transform: translate(-50%, 100%)
}

.ql-table-tooltip::before {
    border-bottom-color: rgba(0, 0, 0, .8509803922) !important;
    top: -20px
}

.ql-table-tooltip:hover {
    display: block
}

.ql-table-tooltip-hidden {
    display: none !important
}

.ql-table-tooltip-error,
.label-field-view-status {
    font-size: 12px;
    min-width: 32px;
    line-height: 20px;
    padding: 6px;
    white-space: nowrap;
    color: #fff;
    text-align: center;
    word-wrap: break-word;
    background: #db3700;
    border-radius: 6px;
    position: absolute;
    z-index: 11;
    left: 50%;
    bottom: -10px;
    transform: translate(-50%, 100%);
    white-space: pre-wrap;
    z-index: 9
}

.ql-table-tooltip-error::before,
.label-field-view-status::before {
    border-bottom-color: #db3700 !important;
    top: -20px
}

.ql-table-tooltip-error:hover,
.label-field-view-status:hover {
    display: block
}

.ql-table-tooltip-error-hidden {
    display: none !important
}

.ql-table-dropdown-properties {
    width: 80px;
    height: 30px;
    border: 1px solid #ccced1;
    box-sizing: border-box
}

.ql-table-dropdown-properties:hover {
    background: none
}

.ql-table-properties-form {
    width: 320px;
    position: absolute;
    left: 50%;
    padding-bottom: 8px;
    background: #fff;
    z-index: 1;
    box-shadow: 0 1px 2px 1px #ccced1
}

.ql-table-properties-form .properties-form-header {
    height: 40px;
    line-height: 40px;
    padding: 0 12px;
    border-bottom: 1px solid #ccced1;
    margin: 0;
    box-sizing: border-box;
    color: #333;
    font-size: 14px
}

.ql-table-properties-form .properties-form-row {
    display: flex;
    flex-wrap: wrap;
    padding: 8px 12px;
    justify-content: space-between
}

.ql-table-properties-form .properties-form-row .ql-table-check-container {
    display: flex;
    border: 1px solid #ccced1;
    align-items: center
}

.ql-table-properties-form .properties-form-row .ql-table-check-container .ql-table-tooltip-hover {
    padding: 6px 10px;
    cursor: pointer
}

.ql-table-properties-form .properties-form-row .ql-table-check-container .ql-table-tooltip-hover:hover {
    background: #f0f0f0
}

.ql-table-properties-form .properties-form-row .ql-table-check-container .ql-table-btns-checked {
    background: #f0f7ff
}

.ql-table-properties-form .properties-form-row .ql-table-check-container .ql-table-btns-checked>svg path {
    stroke: #2977ff
}

.ql-table-properties-form .properties-form-row-full .ql-table-color-container {
    width: 100%
}

.ql-table-properties-form .properties-form-row-full .ql-table-color-container .property-input {
    width: 100%
}

.ql-table-properties-form .properties-form-action-row {
    display: flex;
    justify-content: space-around;
    padding: 0 12px
}

.ql-table-properties-form .properties-form-action-row>button {
    background: #fff;
    outline: none;
    border: none;
    height: 30px;
    cursor: pointer;
    flex: 1
}

.ql-table-properties-form .properties-form-action-row>button>span {
    margin: 0 2px;
    display: flex
}

.ql-table-properties-form .properties-form-action-row>button:hover {
    background: #f0f0f0
}

.ql-table-properties-form .properties-form-action-row>button[disabled] {
    background-color: rgba(0, 0, 0, 0)
}

.ql-table-properties-form .ql-table-color-selected {
    background-position: center
}

.ql-table-properties-form .ql-table-dropdown-selected {
    background-position: calc(100% - 10px) center
}

.ql-table-color-container {
    border: 1px solid #ccced1;
    height: 30px;
    box-sizing: border-box;
    display: flex
}

.ql-table-color-container .label-field-view-color {
    flex: 1
}

.ql-table-color-container .label-field-view-color .property-input {
    border: 1px solid rgba(0, 0, 0, 0);
    height: 100%
}

.ql-table-color-container .color-picker {
    width: 30px;
    border-left: 1px solid #ccced1;
    box-sizing: border-box;
    position: relative
}

.ql-table-color-container .color-picker .color-button {
    width: 20px;
    height: 20px;
    border: 1px solid #ccced1;
    box-sizing: border-box;
    cursor: pointer;
    position: relative
}

.ql-table-color-container .color-picker .color-unselected {
    position: relative
}

.ql-table-color-container .color-picker .color-unselected::after {
    content: "";
    position: absolute;
    width: 1px;
    height: 26px;
    background: red;
    transform-origin: 50%;
    transform: rotate(45deg);
    left: 50%;
    top: -4px
}

.ql-table-color-container .color-picker .color-picker-select {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 156px;
    transform: translateY(100%);
    background: #fff;
    z-index: 10;
    box-shadow: 0 1px 2px 1px #ccced1
}

.ql-table-color-container .color-picker .color-picker-select .erase-container {
    display: flex;
    height: 30px;
    align-items: center;
    padding: 0 12px;
    cursor: pointer
}

.ql-table-color-container .color-picker .color-picker-select .erase-container:hover {
    background: #f0f0f0
}

.ql-table-color-container .color-picker .color-picker-select .erase-container>button {
    border: none;
    outline: none;
    background: inherit;
    height: 100%;
    cursor: pointer
}

.ql-table-color-container .color-picker .color-picker-select>.erase-container {
    margin-bottom: 4px
}

.ql-table-color-container .color-picker .color-picker-select .color-list {
    display: flex;
    flex-wrap: wrap;
    padding: 0 12px;
    margin: 0;
    justify-content: space-between
}

.ql-table-color-container .color-picker .color-picker-select .color-list>li {
    list-style: none;
    width: 24px;
    height: 24px;
    margin: 2px 0;
    position: relative;
    cursor: pointer
}

.ql-table-color-container .color-picker .color-picker-select .color-list>li[data-color="#ffffff"] {
    border: 1px solid #ccced1;
    box-sizing: border-box
}

.ql-table-color-container .color-picker .color-picker-palette {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    background: #fff
}

.ql-table-color-container .color-picker .color-picker-palette .color-picker-wrap {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column
}

.ql-table-color-container .color-picker .color-picker-palette .color-picker-wrap .iro-container {
    flex: 1
}

.ql-table-disabled {
    background: #f2f2f2;
    pointer-events: none
}

.ql-table-button-disabled {
    background: #f2f2f2 !important;
    pointer-events: none
}

.ql-table-button-disabled svg .ql-fill {
    fill: #999 !important
}

.ql-table-button-disabled svg .ql-stroke {
    stroke: #999 !important
}

button.ql-table-better {
    position: relative
}

.ql-table-select-container {
    flex-direction: column;
    width: 190px;
    padding: 2px;
    position: absolute;
    top: 24px;
    z-index: 10;
    box-sizing: border-box
}

.ql-table-select-container .ql-table-select-list {
    flex-wrap: wrap
}

.ql-table-select-container .ql-table-select-label {
    width: 100%;
    line-height: 16px;
    text-align: center;
    color: rgba(34, 47, 62, .7019607843);
    margin-top: 2px
}

.ql-table-select-container span {
    width: 16px;
    height: 16px;
    border: 1px solid #000;
    box-sizing: border-box;
    margin: 1px
}

ol.table-list-container {
    counter-reset: list-0
}

@keyframes ql-table-input-shake {
    20% {
        transform: translateX(-2px)
    }

    40% {
        transform: translateX(2px)
    }

    60% {
        transform: translateX(-1px)
    }

    80% {
        transform: translateX(1px)
    }
}