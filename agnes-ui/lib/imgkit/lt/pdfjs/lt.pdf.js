/* Copyright 2017 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
!function(e, t) { "object"==typeof exports&&"object"==typeof module? module.exports=t():"function"==typeof define&&define.amd? define("pdfjs-dist/build/pdf", [], t):"object"==typeof exports? exports["pdfjs-dist/build/pdf"]=t():e["pdfjs-dist/build/pdf"]=e.pdfjsDistBuildPdf=t() }(this, function() {
  return function(e) { function t(n) { if(r[n]) return r[n].exports; var i=r[n]={ i: n, l: !1, exports: {} }; return e[n].call(i.exports, i, i.exports, t), i.l=!0, i.exports } var r={}; return t.m=e, t.c=r, t.i=function(e) { return e }, t.d=function(e, r, n) { t.o(e, r)||Object.defineProperty(e, r, { configurable: !1, enumerable: !0, get: n }) }, t.n=function(e) { var r=e&&e.__esModule? function() { return e.default }:function() { return e }; return t.d(r, "a", r), r }, t.o=function(e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, t.p="", t(t.s=14) }([function(e, t, r) { "use strict"; (function(e) { function n(e) { ae=e } function i() { return ae } function a(e) { ae>=re.infos&&console.log("Info: "+e) } function o(e) { ae>=re.warnings&&console.log("Warning: "+e) } function s(e) { console.log("Deprecated API usage: "+e) } function l(e) { throw ae>=re.errors&&(console.log("Error: "+e), console.log(c())), new Error(e) } function c() { try { throw new Error } catch(e) { return e.stack? e.stack.split("\n").slice(2).join("\n"):"" } } function u(e, t) { e||l(t) } function d(e, t) { try { var r=new URL(e); if(!r.origin||"null"===r.origin) return !1 } catch(e) { return !1 } var n=new URL(t, r); return r.origin===n.origin } function h(e) { if(!e) return !1; switch(e.protocol) { case "http:": case "https:": case "ftp:": case "mailto:": case "tel:": return !0; default: return !1 } } function f(e, t) { if(!e) return null; try { var r=t? new URL(e, t):new URL(e); if(h(r)) return r } catch(e) { } return null } function p(e, t, r) { return Object.defineProperty(e, t, { value: r, enumerable: !0, configurable: !0, writable: !1 }), r } function m(e) { var t; return function() { return e&&(t=Object.create(null), e(t), e=null), t } } function g(e) { return "string"!=typeof e? (o("The argument for removeNullCharacters must be a string."), e):e.replace(ge, "") } function v(e) { u(null!==e&&"object"===(void 0===e? "undefined":z(e))&&void 0!==e.length, "Invalid argument for bytesToString"); var t=e.length; if(t<8192) return String.fromCharCode.apply(null, e); for(var r=[], n=0; n<t; n+=8192) { var i=Math.min(n+8192, t), a=e.subarray(n, i); r.push(String.fromCharCode.apply(null, a)) } return r.join("") } function b(e) { u("string"==typeof e, "Invalid argument for stringToBytes"); for(var t=e.length, r=new Uint8Array(t), n=0; n<t; ++n)r[n]=255&e.charCodeAt(n); return r } function y(e) { return void 0!==e.length? e.length:(u(void 0!==e.byteLength), e.byteLength) } function A(e) { if(1===e.length&&e[0] instanceof Uint8Array) return e[0]; var t, r, n, i=0, a=e.length; for(t=0; t<a; t++)r=e[t], n=y(r), i+=n; var o=0, s=new Uint8Array(i); for(t=0; t<a; t++)r=e[t], r instanceof Uint8Array||(r="string"==typeof r? b(r):new Uint8Array(r)), n=r.byteLength, s.set(r, o), o+=n; return s } function _(e) { return String.fromCharCode(e>>24&255, e>>16&255, e>>8&255, 255&e) } function S(e) { for(var t=1, r=0; e>t;)t<<=1, r++; return r } function w(e, t) { return e[t]<<24>>24 } function P(e, t) { return e[t]<<8|e[t+1] } function x(e, t) { return (e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3])>>>0 } function T() { var e=new Uint8Array(4); return e[0]=1, 1===new Uint32Array(e.buffer, 0, 1)[0] } function k() { try { return new Function(""), !0 } catch(e) { return !1 } } function C(e) { var t, r=e.length, n=[]; if("þ"===e[0]&&"ÿ"===e[1]) for(t=2; t<r; t+=2)n.push(String.fromCharCode(e.charCodeAt(t)<<8|e.charCodeAt(t+1))); else for(t=0; t<r; ++t) { var i=Ae[e.charCodeAt(t)]; n.push(i? String.fromCharCode(i):e.charAt(t)) } return n.join("") } function R(e) { return decodeURIComponent(escape(e)) } function E(e) { return unescape(encodeURIComponent(e)) } function L(e) { for(var t in e) return !1; return !0 } function O(e) { return "boolean"==typeof e } function I(e) { return "number"==typeof e&&(0|e)===e } function j(e) { return "number"==typeof e } function D(e) { return "string"==typeof e } function F(e) { return e instanceof Array } function N(e) { return "object"===(void 0===e? "undefined":z(e))&&null!==e&&void 0!==e.byteLength } function M(e) { return 32===e||9===e||13===e||10===e } function W() { return "undefined"==typeof __pdfjsdev_webpack__&&("object"===("undefined"==typeof process? "undefined":z(process))&&process+""=="[object process]") } function U() { var e={}; return e.promise=new Promise(function(t, r) { e.resolve=t, e.reject=r }), e } function q(e, t, r) { var n=this; this.sourceName=e, this.targetName=t, this.comObj=r, this.callbackIndex=1, this.postMessageTransfers=!0; var i=this.callbacksCapabilities=Object.create(null), a=this.actionHandler=Object.create(null); this._onComObjOnMessage=function(e) { var t=e.data; if(t.targetName===n.sourceName) if(t.isReply) { var o=t.callbackId; if(t.callbackId in i) { var s=i[o]; delete i[o], "error" in t? s.reject(t.error):s.resolve(t.data) } else l("Cannot resolve callback "+o) } else if(t.action in a) { var c=a[t.action]; if(t.callbackId) { var u=n.sourceName, d=t.sourceName; Promise.resolve().then(function() { return c[0].call(c[1], t.data) }).then(function(e) { r.postMessage({ sourceName: u, targetName: d, isReply: !0, callbackId: t.callbackId, data: e }) }, function(e) { e instanceof Error&&(e+=""), r.postMessage({ sourceName: u, targetName: d, isReply: !0, callbackId: t.callbackId, error: e }) }) } else c[0].call(c[1], t.data) } else l("Unknown action from worker: "+t.action) }, r.addEventListener("message", this._onComObjOnMessage) } function B(e, t, r) { var n=new Image; n.onload=function() { r.resolve(e, n) }, n.onerror=function() { r.resolve(e, null), o("Error during JPEG image loading") }, n.src=t } Object.defineProperty(t, "__esModule", { value: !0 }), t.warn=t.utf8StringToString=t.stringToUTF8String=t.stringToPDFString=t.stringToBytes=t.string32=t.shadow=t.setVerbosityLevel=t.ReadableStream=t.removeNullCharacters=t.readUint32=t.readUint16=t.readInt8=t.log2=t.loadJpegStream=t.isEvalSupported=t.isLittleEndian=t.createValidAbsoluteUrl=t.isSameOrigin=t.isNodeJS=t.isSpace=t.isString=t.isNum=t.isInt=t.isEmptyObj=t.isBool=t.isArrayBuffer=t.isArray=t.info=t.globalScope=t.getVerbosityLevel=t.getLookupTableFactory=t.error=t.deprecated=t.createObjectURL=t.createPromiseCapability=t.createBlob=t.bytesToString=t.assert=t.arraysToBytes=t.arrayByteLength=t.XRefParseException=t.Util=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.StreamType=t.StatTimer=t.PasswordResponses=t.PasswordException=t.PageViewport=t.NotImplementedException=t.NativeImageDecoding=t.MissingPDFException=t.MissingDataException=t.MessageHandler=t.InvalidPDFException=t.CMapCompressionType=t.ImageKind=t.FontType=t.AnnotationType=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationBorderStyleType=t.UNSUPPORTED_FEATURES=t.VERBOSITY_LEVELS=t.OPS=t.IDENTITY_MATRIX=t.FONT_IDENTITY_MATRIX=void 0; var z="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator? function(e) { return typeof e }:function(e) { return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype? "symbol":typeof e }; r(15); var G=r(10), X="undefined"!=typeof window? window:void 0!==e? e:"undefined"!=typeof self? self:void 0, H=[.001, 0, 0, .001, 0, 0], Y={ NONE: "none", DECODE: "decode", DISPLAY: "display" }, V={ FILL: 0, STROKE: 1, FILL_STROKE: 2, INVISIBLE: 3, FILL_ADD_TO_PATH: 4, STROKE_ADD_TO_PATH: 5, FILL_STROKE_ADD_TO_PATH: 6, ADD_TO_PATH: 7, FILL_STROKE_MASK: 3, ADD_TO_PATH_FLAG: 4 }, J={ GRAYSCALE_1BPP: 1, RGB_24BPP: 2, RGBA_32BPP: 3 }, Q={ TEXT: 1, LINK: 2, FREETEXT: 3, LINE: 4, SQUARE: 5, CIRCLE: 6, POLYGON: 7, POLYLINE: 8, HIGHLIGHT: 9, UNDERLINE: 10, SQUIGGLY: 11, STRIKEOUT: 12, STAMP: 13, CARET: 14, INK: 15, POPUP: 16, FILEATTACHMENT: 17, SOUND: 18, MOVIE: 19, WIDGET: 20, SCREEN: 21, PRINTERMARK: 22, TRAPNET: 23, WATERMARK: 24, THREED: 25, REDACT: 26 }, K={ INVISIBLE: 1, HIDDEN: 2, PRINT: 4, NOZOOM: 8, NOROTATE: 16, NOVIEW: 32, READONLY: 64, LOCKED: 128, TOGGLENOVIEW: 256, LOCKEDCONTENTS: 512 }, Z={ READONLY: 1, REQUIRED: 2, NOEXPORT: 4, MULTILINE: 4096, PASSWORD: 8192, NOTOGGLETOOFF: 16384, RADIO: 32768, PUSHBUTTON: 65536, COMBO: 131072, EDIT: 262144, SORT: 524288, FILESELECT: 1048576, MULTISELECT: 2097152, DONOTSPELLCHECK: 4194304, DONOTSCROLL: 8388608, COMB: 16777216, RICHTEXT: 33554432, RADIOSINUNISON: 33554432, COMMITONSELCHANGE: 67108864 }, $={ SOLID: 1, DASHED: 2, BEVELED: 3, INSET: 4, UNDERLINE: 5 }, ee={ UNKNOWN: 0, FLATE: 1, LZW: 2, DCT: 3, JPX: 4, JBIG: 5, A85: 6, AHX: 7, CCF: 8, RL: 9 }, te={ UNKNOWN: 0, TYPE1: 1, TYPE1C: 2, CIDFONTTYPE0: 3, CIDFONTTYPE0C: 4, TRUETYPE: 5, CIDFONTTYPE2: 6, TYPE3: 7, OPENTYPE: 8, TYPE0: 9, MMTYPE1: 10 }, re={ errors: 0, warnings: 1, infos: 5 }, ne={ NONE: 0, BINARY: 1, STREAM: 2 }, ie={ dependency: 1, setLineWidth: 2, setLineCap: 3, setLineJoin: 4, setMiterLimit: 5, setDash: 6, setRenderingIntent: 7, setFlatness: 8, setGState: 9, save: 10, restore: 11, transform: 12, moveTo: 13, lineTo: 14, curveTo: 15, curveTo2: 16, curveTo3: 17, closePath: 18, rectangle: 19, stroke: 20, closeStroke: 21, fill: 22, eoFill: 23, fillStroke: 24, eoFillStroke: 25, closeFillStroke: 26, closeEOFillStroke: 27, endPath: 28, clip: 29, eoClip: 30, beginText: 31, endText: 32, setCharSpacing: 33, setWordSpacing: 34, setHScale: 35, setLeading: 36, setFont: 37, setTextRenderingMode: 38, setTextRise: 39, moveText: 40, setLeadingMoveText: 41, setTextMatrix: 42, nextLine: 43, showText: 44, showSpacedText: 45, nextLineShowText: 46, nextLineSetSpacingShowText: 47, setCharWidth: 48, setCharWidthAndBounds: 49, setStrokeColorSpace: 50, setFillColorSpace: 51, setStrokeColor: 52, setStrokeColorN: 53, setFillColor: 54, setFillColorN: 55, setStrokeGray: 56, setFillGray: 57, setStrokeRGBColor: 58, setFillRGBColor: 59, setStrokeCMYKColor: 60, setFillCMYKColor: 61, shadingFill: 62, beginInlineImage: 63, beginImageData: 64, endInlineImage: 65, paintXObject: 66, markPoint: 67, markPointProps: 68, beginMarkedContent: 69, beginMarkedContentProps: 70, endMarkedContent: 71, beginCompat: 72, endCompat: 73, paintFormXObjectBegin: 74, paintFormXObjectEnd: 75, beginGroup: 76, endGroup: 77, beginAnnotations: 78, endAnnotations: 79, beginAnnotation: 80, endAnnotation: 81, paintJpegXObject: 82, paintImageMaskXObject: 83, paintImageMaskXObjectGroup: 84, paintImageXObject: 85, paintInlineImageXObject: 86, paintInlineImageXObjectGroup: 87, paintImageXObjectRepeat: 88, paintImageMaskXObjectRepeat: 89, paintSolidColorImageMask: 90, constructPath: 91 }, ae=re.warnings, oe={ unknown: "unknown", forms: "forms", javaScript: "javaScript", smask: "smask", shadingPattern: "shadingPattern", font: "font" }, se={ NEED_PASSWORD: 1, INCORRECT_PASSWORD: 2 }, le=function() { function e(e, t) { this.name="PasswordException", this.message=e, this.code=t } return e.prototype=new Error, e.constructor=e, e }(), ce=function() { function e(e, t) { this.name="UnknownErrorException", this.message=e, this.details=t } return e.prototype=new Error, e.constructor=e, e }(), ue=function() { function e(e) { this.name="InvalidPDFException", this.message=e } return e.prototype=new Error, e.constructor=e, e }(), de=function() { function e(e) { this.name="MissingPDFException", this.message=e } return e.prototype=new Error, e.constructor=e, e }(), he=function() { function e(e, t) { this.name="UnexpectedResponseException", this.message=e, this.status=t } return e.prototype=new Error, e.constructor=e, e }(), fe=function() { function e(e) { this.message=e } return e.prototype=new Error, e.prototype.name="NotImplementedException", e.constructor=e, e }(), pe=function() { function e(e, t) { this.begin=e, this.end=t, this.message="Missing data ["+e+", "+t+")" } return e.prototype=new Error, e.prototype.name="MissingDataException", e.constructor=e, e }(), me=function() { function e(e) { this.message=e } return e.prototype=new Error, e.prototype.name="XRefParseException", e.constructor=e, e }(), ge=/\x00/g, ve=[1, 0, 0, 1, 0, 0], be=function() { function e() { } var t=["rgb(", 0, ",", 0, ",", 0, ")"]; e.makeCssRgb=function(e, r, n) { return t[1]=e, t[3]=r, t[5]=n, t.join("") }, e.transform=function(e, t) { return [e[0]*t[0]+e[2]*t[1], e[1]*t[0]+e[3]*t[1], e[0]*t[2]+e[2]*t[3], e[1]*t[2]+e[3]*t[3], e[0]*t[4]+e[2]*t[5]+e[4], e[1]*t[4]+e[3]*t[5]+e[5]] }, e.applyTransform=function(e, t) { return [e[0]*t[0]+e[1]*t[2]+t[4], e[0]*t[1]+e[1]*t[3]+t[5]] }, e.applyInverseTransform=function(e, t) { var r=t[0]*t[3]-t[1]*t[2]; return [(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/r, (-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/r] }, e.getAxialAlignedBoundingBox=function(t, r) { var n=e.applyTransform(t, r), i=e.applyTransform(t.slice(2, 4), r), a=e.applyTransform([t[0], t[3]], r), o=e.applyTransform([t[2], t[1]], r); return [Math.min(n[0], i[0], a[0], o[0]), Math.min(n[1], i[1], a[1], o[1]), Math.max(n[0], i[0], a[0], o[0]), Math.max(n[1], i[1], a[1], o[1])] }, e.inverseTransform=function(e) { var t=e[0]*e[3]-e[1]*e[2]; return [e[3]/t, -e[1]/t, -e[2]/t, e[0]/t, (e[2]*e[5]-e[4]*e[3])/t, (e[4]*e[1]-e[5]*e[0])/t] }, e.apply3dTransform=function(e, t) { return [e[0]*t[0]+e[1]*t[1]+e[2]*t[2], e[3]*t[0]+e[4]*t[1]+e[5]*t[2], e[6]*t[0]+e[7]*t[1]+e[8]*t[2]] }, e.singularValueDecompose2dScale=function(e) { var t=[e[0], e[2], e[1], e[3]], r=e[0]*t[0]+e[1]*t[2], n=e[0]*t[1]+e[1]*t[3], i=e[2]*t[0]+e[3]*t[2], a=e[2]*t[1]+e[3]*t[3], o=(r+a)/2, s=Math.sqrt((r+a)*(r+a)-4*(r*a-i*n))/2, l=o+s||1, c=o-s||1; return [Math.sqrt(l), Math.sqrt(c)] }, e.normalizeRect=function(e) { var t=e.slice(0); return e[0]>e[2]&&(t[0]=e[2], t[2]=e[0]), e[1]>e[3]&&(t[1]=e[3], t[3]=e[1]), t }, e.intersect=function(t, r) { function n(e, t) { return e-t } var i=[t[0], t[2], r[0], r[2]].sort(n), a=[t[1], t[3], r[1], r[3]].sort(n), o=[]; return t=e.normalizeRect(t), r=e.normalizeRect(r), (i[0]===t[0]&&i[1]===r[0]||i[0]===r[0]&&i[1]===t[0])&&(o[0]=i[1], o[2]=i[2], (a[0]===t[1]&&a[1]===r[1]||a[0]===r[1]&&a[1]===t[1])&&(o[1]=a[1], o[3]=a[2], o)) }, e.sign=function(e) { return e<0? -1:1 }; var r=["", "C", "CC", "CCC", "CD", "D", "DC", "DCC", "DCCC", "CM", "", "X", "XX", "XXX", "XL", "L", "LX", "LXX", "LXXX", "XC", "", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX"]; return e.toRoman=function(e, t) { u(I(e)&&e>0, "The number should be a positive integer."); for(var n, i=[]; e>=1e3;)e-=1e3, i.push("M"); n=e/100|0, e%=100, i.push(r[n]), n=e/10|0, e%=10, i.push(r[10+n]), i.push(r[20+e]); var a=i.join(""); return t? a.toLowerCase():a }, e.appendToArray=function(e, t) { Array.prototype.push.apply(e, t) }, e.prependToArray=function(e, t) { Array.prototype.unshift.apply(e, t) }, e.extendObj=function(e, t) { for(var r in t) e[r]=t[r] }, e.getInheritableProperty=function(e, t, r) { for(; e&&!e.has(t);)e=e.get("Parent"); return e? r? e.getArray(t):e.get(t):null }, e.inherit=function(e, t, r) { e.prototype=Object.create(t.prototype), e.prototype.constructor=e; for(var n in r) e.prototype[n]=r[n] }, e.loadScript=function(e, t) { var r=document.createElement("script"), n=!1; r.setAttribute("src", e), t&&(r.onload=function() { n||t(), n=!0 }), document.getElementsByTagName("head")[0].appendChild(r) }, e }(), ye=function() { function e(e, t, r, n, i, a) { this.viewBox=e, this.scale=t, this.rotation=r, this.offsetX=n, this.offsetY=i; var o, s, l, c, u=(e[2]+e[0])/2, d=(e[3]+e[1])/2; switch(r%=360, r=r<0? r+360:r) { case 180: o=-1, s=0, l=0, c=1; break; case 90: o=0, s=1, l=1, c=0; break; case 270: o=0, s=-1, l=-1, c=0; break; default: o=1, s=0, l=0, c=-1 }a&&(l=-l, c=-c); var h, f, p, m; 0===o? (h=Math.abs(d-e[1])*t+n, f=Math.abs(u-e[0])*t+i, p=Math.abs(e[3]-e[1])*t, m=Math.abs(e[2]-e[0])*t):(h=Math.abs(u-e[0])*t+n, f=Math.abs(d-e[1])*t+i, p=Math.abs(e[2]-e[0])*t, m=Math.abs(e[3]-e[1])*t), this.transform=[o*t, s*t, l*t, c*t, h-o*t*u-l*t*d, f-s*t*u-c*t*d], this.width=p, this.height=m, this.fontScale=t } return e.prototype={ clone: function(t) { t=t||{}; var r="scale" in t? t.scale:this.scale, n="rotation" in t? t.rotation:this.rotation; return new e(this.viewBox.slice(), r, n, this.offsetX, this.offsetY, t.dontFlip) }, convertToViewportPoint: function(e, t) { return be.applyTransform([e, t], this.transform) }, convertToViewportRectangle: function(e) { var t=be.applyTransform([e[0], e[1]], this.transform), r=be.applyTransform([e[2], e[3]], this.transform); return [t[0], t[1], r[0], r[1]] }, convertToPdfPoint: function(e, t) { return be.applyInverseTransform([e, t], this.transform) } }, e }(), Ae=[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 728, 711, 710, 729, 733, 731, 730, 732, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 8226, 8224, 8225, 8230, 8212, 8211, 402, 8260, 8249, 8250, 8722, 8240, 8222, 8220, 8221, 8216, 8217, 8218, 8482, 64257, 64258, 321, 338, 352, 376, 381, 305, 322, 339, 353, 382, 0, 8364], _e=function() { function e(e, t, r) { for(; e.length<r;)e+=t; return e } function t() { this.started=Object.create(null), this.times=[], this.enabled=!0 } return t.prototype={ time: function(e) { this.enabled&&(e in this.started&&o("Timer is already running for "+e), this.started[e]=Date.now()) }, timeEnd: function(e) { this.enabled&&(e in this.started||o("Timer has not been started for "+e), this.times.push({ name: e, start: this.started[e], end: Date.now() }), delete this.started[e]) }, toString: function() { var t, r, n=this.times, i="", a=0; for(t=0, r=n.length; t<r; ++t) { var o=n[t].name; o.length>a&&(a=o.length) } for(t=0, r=n.length; t<r; ++t) { var s=n[t], l=s.end-s.start; i+=e(s.name, " ", a)+" "+l+"ms\n" } return i } }, t }(), Se=function(e, t) { if("undefined"!=typeof Blob) return new Blob([e], { type: t }); throw new Error('The "Blob" constructor is not supported.') }, we=function() { var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="; return function(t, r) { if(!(arguments.length>2&&void 0!==arguments[2]&&arguments[2])&&URL.createObjectURL) { var n=Se(t, r); return URL.createObjectURL(n) } for(var i="data:"+r+";base64,", a=0, o=t.length; a<o; a+=3) { var s=255&t[a], l=255&t[a+1], c=255&t[a+2]; i+=e[s>>2]+e[(3&s)<<4|l>>4]+e[a+1<o? (15&l)<<2|c>>6:64]+e[a+2<o? 63&c:64] } return i } }(); q.prototype={ on: function(e, t, r) { var n=this.actionHandler; n[e]&&l('There is already an actionName called "'+e+'"'), n[e]=[t, r] }, send: function(e, t, r) { var n={ sourceName: this.sourceName, targetName: this.targetName, action: e, data: t }; this.postMessage(n, r) }, sendWithPromise: function(e, t, r) { var n=this.callbackIndex++, i={ sourceName: this.sourceName, targetName: this.targetName, action: e, data: t, callbackId: n }, a=U(); this.callbacksCapabilities[n]=a; try { this.postMessage(i, r) } catch(e) { a.reject(e) } return a.promise }, postMessage: function(e, t) { t&&this.postMessageTransfers? this.comObj.postMessage(e, t):this.comObj.postMessage(e) }, destroy: function() { this.comObj.removeEventListener("message", this._onComObjOnMessage) } }, t.FONT_IDENTITY_MATRIX=H, t.IDENTITY_MATRIX=ve, t.OPS=ie, t.VERBOSITY_LEVELS=re, t.UNSUPPORTED_FEATURES=oe, t.AnnotationBorderStyleType=$, t.AnnotationFieldFlag=Z, t.AnnotationFlag=K, t.AnnotationType=Q, t.FontType=te, t.ImageKind=J, t.CMapCompressionType=ne, t.InvalidPDFException=ue, t.MessageHandler=q, t.MissingDataException=pe, t.MissingPDFException=de, t.NativeImageDecoding=Y, t.NotImplementedException=fe, t.PageViewport=ye, t.PasswordException=le, t.PasswordResponses=se, t.StatTimer=_e, t.StreamType=ee, t.TextRenderingMode=V, t.UnexpectedResponseException=he, t.UnknownErrorException=ce, t.Util=be, t.XRefParseException=me, t.arrayByteLength=y, t.arraysToBytes=A, t.assert=u, t.bytesToString=v, t.createBlob=Se, t.createPromiseCapability=U, t.createObjectURL=we, t.deprecated=s, t.error=l, t.getLookupTableFactory=m, t.getVerbosityLevel=i, t.globalScope=X, t.info=a, t.isArray=F, t.isArrayBuffer=N, t.isBool=O, t.isEmptyObj=L, t.isInt=I, t.isNum=j, t.isString=D, t.isSpace=M, t.isNodeJS=W, t.isSameOrigin=d, t.createValidAbsoluteUrl=f, t.isLittleEndian=T, t.isEvalSupported=k, t.loadJpegStream=B, t.log2=S, t.readInt8=w, t.readUint16=P, t.readUint32=x, t.removeNullCharacters=g, t.ReadableStream=G.ReadableStream, t.setVerbosityLevel=n, t.shadow=p, t.string32=_, t.stringToBytes=b, t.stringToPDFString=C, t.stringToUTF8String=R, t.utf8StringToString=E, t.warn=o }).call(t, r(6)) }, function(e, t, r) { "use strict"; function n(e, t) { if(!(e instanceof t)) throw new TypeError("Cannot call a class as a function") } function i(e, t) { var r=t&&t.url; if(e.href=e.title=r? (0, u.removeNullCharacters)(r):"", r) { var n=t.target; void 0===n&&(n=o("externalLinkTarget")), e.target=v[n]; var i=t.rel; void 0===i&&(i=o("externalLinkRel")), e.rel=i } } function a(e) { var t=e.indexOf("#"), r=e.indexOf("?"), n=Math.min(t>0? t:e.length, r>0? r:e.length); return e.substring(e.lastIndexOf("/", n)+1, n) } function o(e) { var t=u.globalScope.PDFJS; switch(e) { case "pdfBug": return !!t&&t.pdfBug; case "disableAutoFetch": return !!t&&t.disableAutoFetch; case "disableStream": return !!t&&t.disableStream; case "disableRange": return !!t&&t.disableRange; case "disableFontFace": return !!t&&t.disableFontFace; case "disableCreateObjectURL": return !!t&&t.disableCreateObjectURL; case "disableWebGL": return !t||t.disableWebGL; case "cMapUrl": return t? t.cMapUrl:null; case "cMapPacked": return !!t&&t.cMapPacked; case "postMessageTransfers": return !t||t.postMessageTransfers; case "workerPort": return t? t.workerPort:null; case "workerSrc": return t? t.workerSrc:null; case "disableWorker": return !!t&&t.disableWorker; case "maxImageSize": return t? t.maxImageSize:-1; case "imageResourcesPath": return t? t.imageResourcesPath:""; case "isEvalSupported": return !t||t.isEvalSupported; case "externalLinkTarget": if(!t) return g.NONE; switch(t.externalLinkTarget) { case g.NONE: case g.SELF: case g.BLANK: case g.PARENT: case g.TOP: return t.externalLinkTarget }return (0, u.warn)("PDFJS.externalLinkTarget is invalid: "+t.externalLinkTarget), t.externalLinkTarget=g.NONE, g.NONE; case "externalLinkRel": return t? t.externalLinkRel:d; case "enableStats": return !(!t||!t.enableStats); case "pdfjsNext": return !(!t||!t.pdfjsNext); default: throw new Error("Unknown default setting: "+e) } } function s() { switch(o("externalLinkTarget")) { case g.NONE: return !1; case g.SELF: case g.BLANK: case g.PARENT: case g.TOP: return !0 } } function l(e, t) { (0, u.deprecated)("isValidUrl(), please use createValidAbsoluteUrl() instead."); var r=t? "http://example.com":null; return null!==(0, u.createValidAbsoluteUrl)(e, r) } Object.defineProperty(t, "__esModule", { value: !0 }), t.DOMCMapReaderFactory=t.DOMCanvasFactory=t.DEFAULT_LINK_REL=t.getDefaultSetting=t.LinkTarget=t.getFilenameFromUrl=t.isValidUrl=t.isExternalLinkTargetSet=t.addLinkAttributes=t.RenderingCancelledException=t.CustomStyle=void 0; var c=function() { function e(e, t) { for(var r=0; r<t.length; r++) { var n=t[r]; n.enumerable=n.enumerable||!1, n.configurable=!0, "value" in n&&(n.writable=!0), Object.defineProperty(e, n.key, n) } } return function(t, r, n) { return r&&e(t.prototype, r), n&&e(t, n), t } }(), u=r(0), d="noopener noreferrer nofollow", h=function() { function e() { n(this, e) } return c(e, [{ key: "create", value: function(e, t) { (0, u.assert)(e>0&&t>0, "invalid canvas size"); var r=document.createElement("canvas"), n=r.getContext("2d"); return r.width=e, r.height=t, { canvas: r, context: n } } }, { key: "reset", value: function(e, t, r) { (0, u.assert)(e.canvas, "canvas is not specified"), (0, u.assert)(t>0&&r>0, "invalid canvas size"), e.canvas.width=t, e.canvas.height=r } }, { key: "destroy", value: function(e) { (0, u.assert)(e.canvas, "canvas is not specified"), e.canvas.width=0, e.canvas.height=0, e.canvas=null, e.context=null } }]), e }(), f=function() { function e(t) { var r=t.baseUrl, i=void 0===r? null:r, a=t.isCompressed, o=void 0!==a&&a; n(this, e), this.baseUrl=i, this.isCompressed=o } return c(e, [{ key: "fetch", value: function(e) { var t=this, r=e.name; return r? new Promise(function(e, n) { var i=t.baseUrl+r+(t.isCompressed? ".bcmap":""), a=new XMLHttpRequest; a.open("GET", i, !0), t.isCompressed&&(a.responseType="arraybuffer"), a.onreadystatechange=function() { if(a.readyState===XMLHttpRequest.DONE) { if(200===a.status||0===a.status) { var r=void 0; if(t.isCompressed&&a.response? r=new Uint8Array(a.response):!t.isCompressed&&a.responseText&&(r=(0, u.stringToBytes)(a.responseText)), r) return void e({ cMapData: r, compressionType: t.isCompressed? u.CMapCompressionType.BINARY:u.CMapCompressionType.NONE }) } n(new Error("Unable to load "+(t.isCompressed? "binary ":"")+"CMap at: "+i)) } }, a.send(null) }):Promise.reject(new Error("CMap name must be specified.")) } }]), e }(), p=function() { function e() { } var t=["ms", "Moz", "Webkit", "O"], r=Object.create(null); return e.getProp=function(e, n) { if(1===arguments.length&&"string"==typeof r[e]) return r[e]; n=n||document.documentElement; var i, a, o=n.style; if("string"==typeof o[e]) return r[e]=e; a=e.charAt(0).toUpperCase()+e.slice(1); for(var s=0, l=t.length; s<l; s++)if(i=t[s]+a, "string"==typeof o[i]) return r[e]=i; return r[e]="undefined" }, e.setProp=function(e, t, r) { var n=this.getProp(e); "undefined"!==n&&(t.style[n]=r) }, e }(), m=function() { function e(e, t) { this.message=e, this.type=t } return e.prototype=new Error, e.prototype.name="RenderingCancelledException", e.constructor=e, e }(), g={ NONE: 0, SELF: 1, BLANK: 2, PARENT: 3, TOP: 4 }, v=["", "_self", "_blank", "_parent", "_top"]; t.CustomStyle=p, t.RenderingCancelledException=m, t.addLinkAttributes=i, t.isExternalLinkTargetSet=s, t.isValidUrl=l, t.getFilenameFromUrl=a, t.LinkTarget=g, t.getDefaultSetting=o, t.DEFAULT_LINK_REL=d, t.DOMCanvasFactory=h, t.DOMCMapReaderFactory=f }, function(e, t, r) {
    "use strict"; function n() { } Object.defineProperty(t, "__esModule", { value: !0 }), t.AnnotationLayer=void 0; var i=r(1), a=r(0); n.prototype={ create: function(e) { switch(e.data.annotationType) { case a.AnnotationType.LINK: return new s(e); case a.AnnotationType.TEXT: return new l(e); case a.AnnotationType.WIDGET: switch(e.data.fieldType) { case "Tx": return new u(e); case "Btn": if(e.data.radioButton) return new h(e); if(e.data.checkBox) return new d(e); (0, a.warn)("Unimplemented button widget annotation: pushbutton"); break; case "Ch": return new f(e) }return new c(e); case a.AnnotationType.POPUP: return new p(e); case a.AnnotationType.LINE: return new g(e); case a.AnnotationType.HIGHLIGHT: return new v(e); case a.AnnotationType.UNDERLINE: return new b(e); case a.AnnotationType.SQUIGGLY: return new y(e); case a.AnnotationType.STRIKEOUT: return new A(e); case a.AnnotationType.FILEATTACHMENT: return new _(e); default: return new o(e) } } }; var o=function() { function e(e, t, r) { this.isRenderable=t||!1, this.data=e.data, this.layer=e.layer, this.page=e.page, this.viewport=e.viewport, this.linkService=e.linkService, this.downloadManager=e.downloadManager, this.imageResourcesPath=e.imageResourcesPath, this.renderInteractiveForms=e.renderInteractiveForms, t&&(this.container=this._createContainer(r)) } return e.prototype={ _createContainer: function(e) { var t=this.data, r=this.page, n=this.viewport, o=document.createElement("section"), s=t.rect[2]-t.rect[0], l=t.rect[3]-t.rect[1]; o.setAttribute("data-annotation-id", t.id); var c=a.Util.normalizeRect([t.rect[0], r.view[3]-t.rect[1]+r.view[1], t.rect[2], r.view[3]-t.rect[3]+r.view[1]]); if(i.CustomStyle.setProp("transform", o, "matrix("+n.transform.join(",")+")"), i.CustomStyle.setProp("transformOrigin", o, -c[0]+"px "+-c[1]+"px"), !e&&t.borderStyle.width>0) { o.style.borderWidth=t.borderStyle.width+"px", t.borderStyle.style!==a.AnnotationBorderStyleType.UNDERLINE&&(s-=2*t.borderStyle.width, l-=2*t.borderStyle.width); var u=t.borderStyle.horizontalCornerRadius, d=t.borderStyle.verticalCornerRadius; if(u>0||d>0) { var h=u+"px / "+d+"px"; i.CustomStyle.setProp("borderRadius", o, h) } switch(t.borderStyle.style) { case a.AnnotationBorderStyleType.SOLID: o.style.borderStyle="solid"; break; case a.AnnotationBorderStyleType.DASHED: o.style.borderStyle="dashed"; break; case a.AnnotationBorderStyleType.BEVELED: (0, a.warn)("Unimplemented border style: beveled"); break; case a.AnnotationBorderStyleType.INSET: (0, a.warn)("Unimplemented border style: inset"); break; case a.AnnotationBorderStyleType.UNDERLINE: o.style.borderBottomStyle="solid" }t.color? o.style.borderColor=a.Util.makeCssRgb(0|t.color[0], 0|t.color[1], 0|t.color[2]):o.style.borderWidth=0 } return o.style.left=c[0]+"px", o.style.top=c[1]+"px", o.style.width=s+"px", o.style.height=l+"px", o }, _createPopup: function(e, t, r) { t||(t=document.createElement("div"), t.style.height=e.style.height, t.style.width=e.style.width, e.appendChild(t)); var n=new m({ container: e, trigger: t, color: r.color, title: r.title, contents: r.contents, hideWrapper: !0 }), i=n.render(); i.style.left=e.style.width, e.appendChild(i) }, render: function() { throw new Error("Abstract method AnnotationElement.render called") } }, e }(), s=function() { function e(e) { o.call(this, e, !0) } return a.Util.inherit(e, o, { render: function() { this.container.className="linkAnnotation"; var e=document.createElement("a"); return (0, i.addLinkAttributes)(e, { url: this.data.url, target: this.data.newWindow? i.LinkTarget.BLANK:void 0 }), this.data.url||(this.data.action? this._bindNamedAction(e, this.data.action):this._bindLink(e, this.data.dest)), this.container.appendChild(e), this.container }, _bindLink: function(e, t) { var r=this; e.href=this.linkService.getDestinationHash(t), e.onclick=function() { return t&&r.linkService.navigateTo(t), !1 }, t&&(e.className="internalLink") }, _bindNamedAction: function(e, t) { var r=this; e.href=this.linkService.getAnchorUrl(""), e.onclick=function() { return r.linkService.executeNamedAction(t), !1 }, e.className="internalLink" } }), e }(), l=function() { function e(e) { var t=!!(e.data.hasPopup||e.data.title||e.data.contents); o.call(this, e, t) } return a.Util.inherit(e, o, { render: function() { this.container.className="textAnnotation"; var e=document.createElement("img"); return e.style.height=this.container.style.height, e.style.width=this.container.style.width, e.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg", e.alt="[{{type}} Annotation]", e.dataset.l10nId="text_annotation_type", e.dataset.l10nArgs=JSON.stringify({ type: this.data.name }), this.data.hasPopup||this._createPopup(this.container, e, this.data), this.container.appendChild(e), this.container } }), e }(), c=function() { function e(e, t) { o.call(this, e, t) } return a.Util.inherit(e, o, { render: function() { return this.container } }), e }(), u=function() { function e(e) { var t=e.renderInteractiveForms||!e.data.hasAppearance&&!!e.data.fieldValue; c.call(this, e, t) } var t=["left", "center", "right"]; return a.Util.inherit(e, c, { render: function() { this.container.className="textWidgetAnnotation"; var e=null; if(this.renderInteractiveForms) { if(this.data.multiLine? (e=document.createElement("textarea"), e.textContent=this.data.fieldValue):(e=document.createElement("input"), e.type="text", e.setAttribute("value", this.data.fieldValue)), e.disabled=this.data.readOnly, null!==this.data.maxLen&&(e.maxLength=this.data.maxLen), this.data.comb) { var r=this.data.rect[2]-this.data.rect[0], n=r/this.data.maxLen; e.classList.add("comb"), e.style.letterSpacing="calc("+n+"px - 1ch)" } } else { e=document.createElement("div"), e.textContent=this.data.fieldValue, e.style.verticalAlign="middle", e.style.display="table-cell"; var i=null; this.data.fontRefName&&(i=this.page.commonObjs.getData(this.data.fontRefName)), this._setTextStyle(e, i) } return null!==this.data.textAlignment&&(e.style.textAlign=t[this.data.textAlignment]), this.container.appendChild(e), this.container }, _setTextStyle: function(e, t) { var r=e.style; if(r.fontSize=this.data.fontSize+"px", r.direction=this.data.fontDirection<0? "rtl":"ltr", t) { r.fontWeight=t.black? t.bold? "900":"bold":t.bold? "bold":"normal", r.fontStyle=t.italic? "italic":"normal"; var n=t.loadedName? '"'+t.loadedName+'", ':"", i=t.fallbackName||"Helvetica, sans-serif"; r.fontFamily=n+i } } }), e }(), d=function() { function e(e) { c.call(this, e, e.renderInteractiveForms) } return a.Util.inherit(e, c, { render: function() { this.container.className="buttonWidgetAnnotation checkBox"; var e=document.createElement("input"); return e.disabled=this.data.readOnly, e.type="checkbox", this.data.fieldValue&&"Off"!==this.data.fieldValue&&e.setAttribute("checked", !0), this.container.appendChild(e), this.container } }), e }(), h=function() { function e(e) { c.call(this, e, e.renderInteractiveForms) } return a.Util.inherit(e, c, { render: function() { this.container.className="buttonWidgetAnnotation radioButton"; var e=document.createElement("input"); return e.disabled=this.data.readOnly, e.type="radio", e.name=this.data.fieldName, this.data.fieldValue===this.data.buttonValue&&e.setAttribute("checked", !0), this.container.appendChild(e), this.container } }), e }(), f=function() { function e(e) { c.call(this, e, e.renderInteractiveForms) } return a.Util.inherit(e, c, { render: function() { this.container.className="choiceWidgetAnnotation"; var e=document.createElement("select"); e.disabled=this.data.readOnly, this.data.combo||(e.size=this.data.options.length, this.data.multiSelect&&(e.multiple=!0)); for(var t=0, r=this.data.options.length; t<r; t++) { var n=this.data.options[t], i=document.createElement("option"); i.textContent=n.displayValue, i.value=n.exportValue, this.data.fieldValue.indexOf(n.displayValue)>=0&&i.setAttribute("selected", !0), e.appendChild(i) } return this.container.appendChild(e), this.container } }), e }(), p=function() {
      function e(e) { var t=!(!e.data.title&&!e.data.contents); o.call(this, e, t) } var t=["Line"]; return a.Util.inherit(e, o, {
        render: function() {
          if(this.container.className="popupAnnotation", t.indexOf(this.data.parentType)>=0) return this.container; var e='[data-annotation-id="'+this.data.parentId+'"]', r=this.layer.querySelector(e); if(!r) return this.container; var n=new m({
            container: this.container, trigger: r, color: this.data.color, title: this.data.title, contents: this.data.contents
          }), a=parseFloat(r.style.left), o=parseFloat(r.style.width); return i.CustomStyle.setProp("transformOrigin", this.container, -(a+o)+"px -"+r.style.top), this.container.style.left=a+o+"px", this.container.appendChild(n.render()), this.container
        }
      }), e
    }(), m=function() { function e(e) { this.container=e.container, this.trigger=e.trigger, this.color=e.color, this.title=e.title, this.contents=e.contents, this.hideWrapper=e.hideWrapper||!1, this.pinned=!1 } return e.prototype={ render: function() { var e=document.createElement("div"); e.className="popupWrapper", this.hideElement=this.hideWrapper? e:this.container, this.hideElement.setAttribute("hidden", !0); var t=document.createElement("div"); t.className="popup"; var r=this.color; if(r) { var n=.7*(255-r[0])+r[0], i=.7*(255-r[1])+r[1], o=.7*(255-r[2])+r[2]; t.style.backgroundColor=a.Util.makeCssRgb(0|n, 0|i, 0|o) } var s=this._formatContents(this.contents), l=document.createElement("h1"); return l.textContent=this.title, this.trigger.addEventListener("click", this._toggle.bind(this)), this.trigger.addEventListener("mouseover", this._show.bind(this, !1)), this.trigger.addEventListener("mouseout", this._hide.bind(this, !1)), t.addEventListener("click", this._hide.bind(this, !0)), t.appendChild(l), t.appendChild(s), e.appendChild(t), e }, _formatContents: function(e) { for(var t=document.createElement("p"), r=e.split(/(?:\r\n?|\n)/), n=0, i=r.length; n<i; ++n) { var a=r[n]; t.appendChild(document.createTextNode(a)), n<i-1&&t.appendChild(document.createElement("br")) } return t }, _toggle: function() { this.pinned? this._hide(!0):this._show(!0) }, _show: function(e) { e&&(this.pinned=!0), this.hideElement.hasAttribute("hidden")&&(this.hideElement.removeAttribute("hidden"), this.container.style.zIndex+=1) }, _hide: function(e) { e&&(this.pinned=!1), this.hideElement.hasAttribute("hidden")||this.pinned||(this.hideElement.setAttribute("hidden", !0), this.container.style.zIndex-=1) } }, e }(), g=function() { function e(e) { var t=!!(e.data.hasPopup||e.data.title||e.data.contents); o.call(this, e, t, !0) } var t="http://www.w3.org/2000/svg"; return a.Util.inherit(e, o, { render: function() { this.container.className="lineAnnotation"; var e=this.data, r=e.rect[2]-e.rect[0], n=e.rect[3]-e.rect[1], i=document.createElementNS(t, "svg:svg"); i.setAttributeNS(null, "version", "1.1"), i.setAttributeNS(null, "width", r+"px"), i.setAttributeNS(null, "height", n+"px"), i.setAttributeNS(null, "preserveAspectRatio", "none"), i.setAttributeNS(null, "viewBox", "0 0 "+r+" "+n); var a=document.createElementNS(t, "svg:line"); return a.setAttributeNS(null, "x1", e.rect[2]-e.lineCoordinates[0]), a.setAttributeNS(null, "y1", e.rect[3]-e.lineCoordinates[1]), a.setAttributeNS(null, "x2", e.rect[2]-e.lineCoordinates[2]), a.setAttributeNS(null, "y2", e.rect[3]-e.lineCoordinates[3]), a.setAttributeNS(null, "stroke-width", e.borderStyle.width), a.setAttributeNS(null, "stroke", "transparent"), i.appendChild(a), this.container.append(i), this._createPopup(this.container, a, this.data), this.container } }), e }(), v=function() { function e(e) { var t=!!(e.data.hasPopup||e.data.title||e.data.contents); o.call(this, e, t, !0) } return a.Util.inherit(e, o, { render: function() { return this.container.className="highlightAnnotation", this.data.hasPopup||this._createPopup(this.container, null, this.data), this.container } }), e }(), b=function() { function e(e) { var t=!!(e.data.hasPopup||e.data.title||e.data.contents); o.call(this, e, t, !0) } return a.Util.inherit(e, o, { render: function() { return this.container.className="underlineAnnotation", this.data.hasPopup||this._createPopup(this.container, null, this.data), this.container } }), e }(), y=function() { function e(e) { var t=!!(e.data.hasPopup||e.data.title||e.data.contents); o.call(this, e, t, !0) } return a.Util.inherit(e, o, { render: function() { return this.container.className="squigglyAnnotation", this.data.hasPopup||this._createPopup(this.container, null, this.data), this.container } }), e }(), A=function() { function e(e) { var t=!!(e.data.hasPopup||e.data.title||e.data.contents); o.call(this, e, t, !0) } return a.Util.inherit(e, o, { render: function() { return this.container.className="strikeoutAnnotation", this.data.hasPopup||this._createPopup(this.container, null, this.data), this.container } }), e }(), _=function() { function e(e) { o.call(this, e, !0); var t=this.data.file; this.filename=(0, i.getFilenameFromUrl)(t.filename), this.content=t.content, this.linkService.onFileAttachmentAnnotation({ id: (0, a.stringToPDFString)(t.filename), filename: t.filename, content: t.content }) } return a.Util.inherit(e, o, { render: function() { this.container.className="fileAttachmentAnnotation"; var e=document.createElement("div"); return e.style.height=this.container.style.height, e.style.width=this.container.style.width, e.addEventListener("dblclick", this._download.bind(this)), this.data.hasPopup||!this.data.title&&!this.data.contents||this._createPopup(this.container, e, this.data), this.container.appendChild(e), this.container }, _download: function() { if(!this.downloadManager) return void (0, a.warn)("Download cannot be started due to unavailable download manager"); this.downloadManager.downloadData(this.content, this.filename, "") } }), e }(), S=function() { return { render: function(e) { for(var t=new n, r=0, a=e.annotations.length; r<a; r++) { var o=e.annotations[r]; if(o) { var s=t.create({ data: o, layer: e.div, page: e.page, viewport: e.viewport, linkService: e.linkService, downloadManager: e.downloadManager, imageResourcesPath: e.imageResourcesPath||(0, i.getDefaultSetting)("imageResourcesPath"), renderInteractiveForms: e.renderInteractiveForms||!1 }); s.isRenderable&&e.div.appendChild(s.render()) } } }, update: function(e) { for(var t=0, r=e.annotations.length; t<r; t++) { var n=e.annotations[t], a=e.div.querySelector('[data-annotation-id="'+n.id+'"]'); a&&i.CustomStyle.setProp("transform", a, "matrix("+e.viewport.transform.join(",")+")") } e.div.removeAttribute("hidden") } } }(); t.AnnotationLayer=S
  }, function(e, t, r) {
    "use strict"; function n(e, t) { if(!(e instanceof t)) throw new TypeError("Cannot call a class as a function") } function i(e, t, r, n) { var i=new w; arguments.length>1&&(0, c.deprecated)("getDocument is called with pdfDataRangeTransport, passwordCallback or progressCallback argument"), t&&(t instanceof P||(t=Object.create(t), t.length=e.length, t.initialData=e.initialData, t.abort||(t.abort=function() { })), e=Object.create(e), e.range=t), i.onPassword=r||null, i.onProgress=n||null; var o; "string"==typeof e? o={ url: e }:(0, c.isArrayBuffer)(e)? o={ data: e }:e instanceof P? o={ range: e }:("object"!==(void 0===e? "undefined":l(e))&&(0, c.error)("Invalid parameter in getDocument, need either Uint8Array, string or a parameter object"), e.url||e.data||e.range||(0, c.error)("Invalid parameter object: need either .data, .range or .url"), o=e); var s={}, d=null, h=null; for(var f in o) if("url"!==f||"undefined"==typeof window) if("range"!==f) if("worker"!==f) if("data"!==f||o[f] instanceof Uint8Array) s[f]=o[f]; else { var m=o[f]; "string"==typeof m? s[f]=(0, c.stringToBytes)(m):"object"!==(void 0===m? "undefined":l(m))||null===m||isNaN(m.length)? (0, c.isArrayBuffer)(m)? s[f]=new Uint8Array(m):(0, c.error)("Invalid PDF binary data: either typed array, string or array-like object is expected in the data property."):s[f]=new Uint8Array(m) } else h=o[f]; else d=o[f]; else s[f]=new URL(o[f], window.location).href; s.rangeChunkSize=s.rangeChunkSize||p, s.ignoreErrors=!0!==s.stopAtErrors; var g=s.CMapReaderFactory||u.DOMCMapReaderFactory; if(void 0!==s.disableNativeImageDecoder&&(0, c.deprecated)("parameter disableNativeImageDecoder, use nativeImageDecoderSupport instead"), s.nativeImageDecoderSupport=s.nativeImageDecoderSupport||(!0===s.disableNativeImageDecoder? c.NativeImageDecoding.NONE:c.NativeImageDecoding.DECODE), s.nativeImageDecoderSupport!==c.NativeImageDecoding.DECODE&&s.nativeImageDecoderSupport!==c.NativeImageDecoding.NONE&&s.nativeImageDecoderSupport!==c.NativeImageDecoding.DISPLAY&&((0, c.warn)("Invalid parameter nativeImageDecoderSupport: need a state of enum {NativeImageDecoding}"), s.nativeImageDecoderSupport=c.NativeImageDecoding.DECODE), !h) { var v=(0, u.getDefaultSetting)("workerPort"); h=v? new C(null, v):new C, i._worker=h } var b=i.docId; return h.promise.then(function() { if(i.destroyed) throw new Error("Loading aborted"); return a(h, s, d, b).then(function(e) { if(i.destroyed) throw new Error("Loading aborted"); var t=new c.MessageHandler(b, e, h.port), r=new R(t, i, d, g); i._transport=r, t.send("Ready", null) }) }).catch(i._capability.reject), i } function a(e, t, r, n) { return e.destroyed? Promise.reject(new Error("Worker was destroyed")):(t.disableAutoFetch=(0, u.getDefaultSetting)("disableAutoFetch"), t.disableStream=(0, u.getDefaultSetting)("disableStream"), t.chunkedViewerLoading=!!r, r&&(t.length=r.length, t.initialData=r.initialData), e.messageHandler.sendWithPromise("GetDocRequest", { docId: n, source: t, disableRange: (0, u.getDefaultSetting)("disableRange"), maxImageSize: (0, u.getDefaultSetting)("maxImageSize"), disableFontFace: (0, u.getDefaultSetting)("disableFontFace"), disableCreateObjectURL: (0, u.getDefaultSetting)("disableCreateObjectURL"), postMessageTransfers: (0, u.getDefaultSetting)("postMessageTransfers")&&!g, docBaseUrl: t.docBaseUrl, nativeImageDecoderSupport: t.nativeImageDecoderSupport, ignoreErrors: t.ignoreErrors }).then(function(t) { if(e.destroyed) throw new Error("Worker was destroyed"); return t })) } Object.defineProperty(t, "__esModule", { value: !0 }), t.build=t.version=t._UnsupportedManager=t.PDFPageProxy=t.PDFDocumentProxy=t.PDFWorker=t.PDFDataRangeTransport=t.LoopbackPort=t.getDocument=void 0; var o, s=function() { function e(e, t) { for(var r=0; r<t.length; r++) { var n=t[r]; n.enumerable=n.enumerable||!1, n.configurable=!0, "value" in n&&(n.writable=!0), Object.defineProperty(e, n.key, n) } } return function(t, r, n) { return r&&e(t.prototype, r), n&&e(t, n), t } }(), l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator? function(e) { return typeof e }:function(e) { return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype? "symbol":typeof e }, c=r(0), u=r(1), d=r(12), h=r(11), f=r(7), p=65536, m=!1, g=!1, v="undefined"!=typeof document&&document.currentScript? document.currentScript.src:null, b=null, y=!1; if("undefined"==typeof __pdfjsdev_webpack__) { "undefined"==typeof window? (m=!0, void 0===require.ensure&&(require.ensure=require("node-ensure")), y=!0):"undefined"!=typeof require&&"function"==typeof require.ensure&&(y=!0), "undefined"!=typeof requirejs&&requirejs.toUrl&&(o=requirejs.toUrl("pdfjs-dist/build/pdf.worker.js")); var A="undefined"!=typeof requirejs&&requirejs.load; b=y? function(e) { require.ensure([], function() { var t; t=require("./pdf.worker.js"), e(t.WorkerMessageHandler) }) }:A? function(e) { requirejs(["pdfjs-dist/build/pdf.worker"], function(t) { e(t.WorkerMessageHandler) }) }:null } var _, S, w=function() { function e() { this._capability=(0, c.createPromiseCapability)(), this._transport=null, this._worker=null, this.docId="d"+t++, this.destroyed=!1, this.onPassword=null, this.onProgress=null, this.onUnsupportedFeature=null } var t=0; return e.prototype={ get promise() { return this._capability.promise }, destroy: function() { var e=this; return this.destroyed=!0, (this._transport? this._transport.destroy():Promise.resolve()).then(function() { e._transport=null, e._worker&&(e._worker.destroy(), e._worker=null) }) }, then: function(e, t) { return this.promise.then.apply(this.promise, arguments) } }, e }(), P=function() { function e(e, t) { this.length=e, this.initialData=t, this._rangeListeners=[], this._progressListeners=[], this._progressiveReadListeners=[], this._readyCapability=(0, c.createPromiseCapability)() } return e.prototype={ addRangeListener: function(e) { this._rangeListeners.push(e) }, addProgressListener: function(e) { this._progressListeners.push(e) }, addProgressiveReadListener: function(e) { this._progressiveReadListeners.push(e) }, onDataRange: function(e, t) { for(var r=this._rangeListeners, n=0, i=r.length; n<i; ++n)r[n](e, t) }, onDataProgress: function(e) { var t=this; this._readyCapability.promise.then(function() { for(var r=t._progressListeners, n=0, i=r.length; n<i; ++n)r[n](e) }) }, onDataProgressiveRead: function(e) { var t=this; this._readyCapability.promise.then(function() { for(var r=t._progressiveReadListeners, n=0, i=r.length; n<i; ++n)r[n](e) }) }, transportReady: function() { this._readyCapability.resolve() }, requestDataRange: function(e, t) { throw new Error("Abstract method PDFDataRangeTransport.requestDataRange") }, abort: function() { } }, e }(), x=function() { function e(e, t, r) { this.pdfInfo=e, this.transport=t, this.loadingTask=r } return e.prototype={ get numPages() { return this.pdfInfo.numPages }, get fingerprint() { return this.pdfInfo.fingerprint }, getPage: function(e) { return this.transport.getPage(e) }, getPageIndex: function(e) { return this.transport.getPageIndex(e) }, getDestinations: function() { return this.transport.getDestinations() }, getDestination: function(e) { return this.transport.getDestination(e) }, getPageLabels: function() { return this.transport.getPageLabels() }, getAttachments: function() { return this.transport.getAttachments() }, getJavaScript: function() { return this.transport.getJavaScript() }, getOutline: function() { return this.transport.getOutline() }, getMetadata: function() { return this.transport.getMetadata() }, getData: function() { return this.transport.getData() }, getDownloadInfo: function() { return this.transport.downloadInfoCapability.promise }, getStats: function() { return this.transport.getStats() }, cleanup: function() { this.transport.startCleanup() }, destroy: function() { return this.loadingTask.destroy() } }, e }(), T=function() { function e(e, t, r) { this.pageIndex=e, this.pageInfo=t, this.transport=r, this.stats=new c.StatTimer, this.stats.enabled=(0, u.getDefaultSetting)("enableStats"), this.commonObjs=r.commonObjs, this.objs=new E, this.cleanupAfterRender=!1, this.pendingCleanup=!1, this.intentStates=Object.create(null), this.destroyed=!1 } return e.prototype={ get pageNumber() { return this.pageIndex+1 }, get rotate() { return this.pageInfo.rotate }, get ref() { return this.pageInfo.ref }, get userUnit() { return this.pageInfo.userUnit }, get view() { return this.pageInfo.view }, getViewport: function(e, t) { return arguments.length<2&&(t=this.rotate), new c.PageViewport(this.view, e, t, 0, 0) }, getAnnotations: function(e) { var t=e&&e.intent||null; return this.annotationsPromise&&this.annotationsIntent===t||(this.annotationsPromise=this.transport.getAnnotations(this.pageIndex, t), this.annotationsIntent=t), this.annotationsPromise }, render: function(e) { var t=this, r=this.stats; r.time("Overall"), this.pendingCleanup=!1; var n="print"===e.intent? "print":"display", i=e.canvasFactory||new u.DOMCanvasFactory; this.intentStates[n]||(this.intentStates[n]=Object.create(null)); var a=this.intentStates[n]; a.displayReadyCapability||(a.receivingOperatorList=!0, a.displayReadyCapability=(0, c.createPromiseCapability)(), a.operatorList={ fnArray: [], argsArray: [], lastChunk: !1 }, this.stats.time("Page Request"), this.transport.messageHandler.send("RenderPageRequest", { pageIndex: this.pageNumber-1, intent: n, renderInteractiveForms: !0===e.renderInteractiveForms })); var o=function(e) { var n=a.renderTasks.indexOf(s); n>=0&&a.renderTasks.splice(n, 1), t.cleanupAfterRender&&(t.pendingCleanup=!0), t._tryCleanup(), e? s.capability.reject(e):s.capability.resolve(), r.timeEnd("Rendering"), r.timeEnd("Overall") }, s=new O(o, e, this.objs, this.commonObjs, a.operatorList, this.pageNumber, i); s.useRequestAnimationFrame="print"!==n, a.renderTasks||(a.renderTasks=[]), a.renderTasks.push(s); var l=s.task; return e.continueCallback&&((0, c.deprecated)("render is used with continueCallback parameter"), l.onContinue=e.continueCallback), a.displayReadyCapability.promise.then(function(e) { if(t.pendingCleanup) return void o(); r.time("Rendering"), s.initializeGraphics(e), s.operatorListChanged() }, o), l }, getOperatorList: function() { function e() { if(r.operatorList.lastChunk) { r.opListReadCapability.resolve(r.operatorList); var e=r.renderTasks.indexOf(t); e>=0&&r.renderTasks.splice(e, 1) } } this.intentStates.oplist||(this.intentStates.oplist=Object.create(null)); var t, r=this.intentStates.oplist; return r.opListReadCapability||(t={}, t.operatorListChanged=e, r.receivingOperatorList=!0, r.opListReadCapability=(0, c.createPromiseCapability)(), r.renderTasks=[], r.renderTasks.push(t), r.operatorList={ fnArray: [], argsArray: [], lastChunk: !1 }, this.transport.messageHandler.send("RenderPageRequest", { pageIndex: this.pageIndex, intent: "oplist" })), r.opListReadCapability.promise }, getTextContent: function(e) { return e=e||{}, this.transport.messageHandler.sendWithPromise("GetTextContent", { pageIndex: this.pageNumber-1, normalizeWhitespace: !0===e.normalizeWhitespace, combineTextItems: !0!==e.disableCombineTextItems }) }, _destroy: function() { this.destroyed=!0, this.transport.pageCache[this.pageIndex]=null; var e=[]; return Object.keys(this.intentStates).forEach(function(t) { if("oplist"!==t) { this.intentStates[t].renderTasks.forEach(function(t) { var r=t.capability.promise.catch(function() { }); e.push(r), t.cancel() }) } }, this), this.objs.clear(), this.annotationsPromise=null, this.pendingCleanup=!1, Promise.all(e) }, destroy: function() { (0, c.deprecated)("page destroy method, use cleanup() instead"), this.cleanup() }, cleanup: function() { this.pendingCleanup=!0, this._tryCleanup() }, _tryCleanup: function() { this.pendingCleanup&&!Object.keys(this.intentStates).some(function(e) { var t=this.intentStates[e]; return 0!==t.renderTasks.length||t.receivingOperatorList }, this)&&(Object.keys(this.intentStates).forEach(function(e) { delete this.intentStates[e] }, this), this.objs.clear(), this.annotationsPromise=null, this.pendingCleanup=!1) }, _startRenderPage: function(e, t) { var r=this.intentStates[t]; r.displayReadyCapability&&r.displayReadyCapability.resolve(e) }, _renderPageChunk: function(e, t) { var r, n, i=this.intentStates[t]; for(r=0, n=e.length; r<n; r++)i.operatorList.fnArray.push(e.fnArray[r]), i.operatorList.argsArray.push(e.argsArray[r]); for(i.operatorList.lastChunk=e.lastChunk, r=0; r<i.renderTasks.length; r++)i.renderTasks[r].operatorListChanged(); e.lastChunk&&(i.receivingOperatorList=!1, this._tryCleanup()) } }, e }(), k=function() { function e(t) { n(this, e), this._listeners=[], this._defer=t, this._deferred=Promise.resolve(void 0) } return s(e, [{ key: "postMessage", value: function(e, t) { function r(e) { if("object"!==(void 0===e? "undefined":l(e))||null===e) return e; if(i.has(e)) return i.get(e); var n, a; if((a=e.buffer)&&(0, c.isArrayBuffer)(a)) { var o=t&&t.indexOf(a)>=0; return n=e===a? e:o? new e.constructor(a, e.byteOffset, e.byteLength):new e.constructor(e), i.set(e, n), n } n=(0, c.isArray)(e)? []:{}, i.set(e, n); for(var s in e) { for(var u, d=e; !(u=Object.getOwnPropertyDescriptor(d, s));)d=Object.getPrototypeOf(d); void 0!==u.value&&"function"!=typeof u.value&&(n[s]=r(u.value)) } return n } var n=this; if(!this._defer) return void this._listeners.forEach(function(t) { t.call(this, { data: e }) }, this); var i=new WeakMap, a={ data: r(e) }; this._deferred.then(function() { n._listeners.forEach(function(e) { e.call(this, a) }, n) }) } }, { key: "addEventListener", value: function(e, t) { this._listeners.push(t) } }, { key: "removeEventListener", value: function(e, t) { var r=this._listeners.indexOf(t); this._listeners.splice(r, 1) } }, { key: "terminate", value: function() { this._listeners=[] } }]), e }(), C=function() { function e() { return void 0!==o? o:(0, u.getDefaultSetting)("workerSrc")? (0, u.getDefaultSetting)("workerSrc"):v? v.replace(/(\.(?:min\.)?js)(\?.*)?$/i, ".worker$1$2"):void (0, c.error)("No PDFJS.workerSrc specified") } function t() { return i? i.promise:(i=(0, c.createPromiseCapability)(), (b||function(t) { c.Util.loadScript(e(), function() { t(window.pdfjsDistBuildPdfWorker.WorkerMessageHandler) }) })(i.resolve), i.promise) } function r(e) { var t="importScripts('"+e+"');"; return URL.createObjectURL(new Blob([t])) } function n(e, t) { if(this.name=e, this.destroyed=!1, this._readyCapability=(0, c.createPromiseCapability)(), this._port=null, this._webWorker=null, this._messageHandler=null, t) return void this._initializeFromPort(t); this._initialize() } var i, a=0; return n.prototype={ get promise() { return this._readyCapability.promise }, get port() { return this._port }, get messageHandler() { return this._messageHandler }, _initializeFromPort: function(e) { this._port=e, this._messageHandler=new c.MessageHandler("main", "worker", e), this._messageHandler.on("ready", function() { }), this._readyCapability.resolve() }, _initialize: function() { var t=this; if(!m&&!(0, u.getDefaultSetting)("disableWorker")&&"undefined"!=typeof Worker) { var n=e(); try { (0, c.isSameOrigin)(window.location.href, n)||(n=r(new URL(n, window.location).href)); var i=new Worker(n), a=new c.MessageHandler("main", "worker", i), o=function() { i.removeEventListener("error", s), a.destroy(), i.terminate(), t.destroyed? t._readyCapability.reject(new Error("Worker was destroyed")):t._setupFakeWorker() }, s=function() { t._webWorker||o() }; i.addEventListener("error", s), a.on("test", function(e) { if(i.removeEventListener("error", s), t.destroyed) return void o(); e&&e.supportTypedArray? (t._messageHandler=a, t._port=i, t._webWorker=i, e.supportTransfers||(g=!0), t._readyCapability.resolve(), a.send("configure", { verbosity: (0, c.getVerbosityLevel)() })):(t._setupFakeWorker(), a.destroy(), i.terminate()) }), a.on("console_log", function(e) { console.log.apply(console, e) }), a.on("console_error", function(e) { console.error.apply(console, e) }), a.on("ready", function(e) { if(i.removeEventListener("error", s), t.destroyed) return void o(); try { l() } catch(e) { t._setupFakeWorker() } }); var l=function() { var e=(0, u.getDefaultSetting)("postMessageTransfers")&&!g, t=new Uint8Array([e? 255:0]); try { a.send("test", t, [t.buffer]) } catch(e) { (0, c.info)("Cannot use postMessage transfers"), t[0]=0, a.send("test", t) } }; return void l() } catch(e) { (0, c.info)("The worker has been disabled.") } } this._setupFakeWorker() }, _setupFakeWorker: function() { var e=this; m||(0, u.getDefaultSetting)("disableWorker")||((0, c.warn)("Setting up fake worker."), m=!0), t().then(function(t) { if(e.destroyed) return void e._readyCapability.reject(new Error("Worker was destroyed")); var r=Uint8Array!==Float32Array, n=new k(r); e._port=n; var i="fake"+a++, o=new c.MessageHandler(i+"_worker", i, n); t.setup(o, n); var s=new c.MessageHandler(i, i+"_worker", n); e._messageHandler=s, e._readyCapability.resolve() }) }, destroy: function() { this.destroyed=!0, this._webWorker&&(this._webWorker.terminate(), this._webWorker=null), this._port=null, this._messageHandler&&(this._messageHandler.destroy(), this._messageHandler=null) } }, n }(), R=function() { function e(e, t, r, n) { this.messageHandler=e, this.loadingTask=t, this.pdfDataRangeTransport=r, this.commonObjs=new E, this.fontLoader=new d.FontLoader(t.docId), this.CMapReaderFactory=new n({ baseUrl: (0, u.getDefaultSetting)("cMapUrl"), isCompressed: (0, u.getDefaultSetting)("cMapPacked") }), this.destroyed=!1, this.destroyCapability=null, this._passwordCapability=null, this.pageCache=[], this.pagePromises=[], this.downloadInfoCapability=(0, c.createPromiseCapability)(), this.setupMessageHandler() } return e.prototype={ destroy: function() { var e=this; if(this.destroyCapability) return this.destroyCapability.promise; this.destroyed=!0, this.destroyCapability=(0, c.createPromiseCapability)(), this._passwordCapability&&this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback")); var t=[]; this.pageCache.forEach(function(e) { e&&t.push(e._destroy()) }), this.pageCache=[], this.pagePromises=[]; var r=this.messageHandler.sendWithPromise("Terminate", null); return t.push(r), Promise.all(t).then(function() { e.fontLoader.clear(), e.pdfDataRangeTransport&&(e.pdfDataRangeTransport.abort(), e.pdfDataRangeTransport=null), e.messageHandler&&(e.messageHandler.destroy(), e.messageHandler=null), e.destroyCapability.resolve() }, this.destroyCapability.reject), this.destroyCapability.promise }, setupMessageHandler: function() { var e=this.messageHandler, t=this.loadingTask, r=this.pdfDataRangeTransport; r&&(r.addRangeListener(function(t, r) { e.send("OnDataRange", { begin: t, chunk: r }) }), r.addProgressListener(function(t) { e.send("OnDataProgress", { loaded: t }) }), r.addProgressiveReadListener(function(t) { e.send("OnDataRange", { chunk: t }) }), e.on("RequestDataRange", function(e) { r.requestDataRange(e.begin, e.end) }, this)), e.on("GetDoc", function(e) { var t=e.pdfInfo; this.numPages=e.pdfInfo.numPages; var r=this.loadingTask, n=new x(t, this, r); this.pdfDocument=n, r._capability.resolve(n) }, this), e.on("PasswordRequest", function(e) { var r=this; if(this._passwordCapability=(0, c.createPromiseCapability)(), t.onPassword) { var n=function(e) { r._passwordCapability.resolve({ password: e }) }; t.onPassword(n, e.code) } else this._passwordCapability.reject(new c.PasswordException(e.message, e.code)); return this._passwordCapability.promise }, this), e.on("PasswordException", function(e) { t._capability.reject(new c.PasswordException(e.message, e.code)) }, this), e.on("InvalidPDF", function(e) { this.loadingTask._capability.reject(new c.InvalidPDFException(e.message)) }, this), e.on("MissingPDF", function(e) { this.loadingTask._capability.reject(new c.MissingPDFException(e.message)) }, this), e.on("UnexpectedResponse", function(e) { this.loadingTask._capability.reject(new c.UnexpectedResponseException(e.message, e.status)) }, this), e.on("UnknownError", function(e) { this.loadingTask._capability.reject(new c.UnknownErrorException(e.message, e.details)) }, this), e.on("DataLoaded", function(e) { this.downloadInfoCapability.resolve(e) }, this), e.on("PDFManagerReady", function(e) { this.pdfDataRangeTransport&&this.pdfDataRangeTransport.transportReady() }, this), e.on("StartRenderPage", function(e) { if(!this.destroyed) { var t=this.pageCache[e.pageIndex]; t.stats.timeEnd("Page Request"), t._startRenderPage(e.transparency, e.intent) } }, this), e.on("RenderPageChunk", function(e) { if(!this.destroyed) { this.pageCache[e.pageIndex]._renderPageChunk(e.operatorList, e.intent) } }, this), e.on("commonobj", function(e) { var t=this; if(!this.destroyed) { var r=e[0], n=e[1]; if(!this.commonObjs.hasData(r)) switch(n) { case "Font": var i=e[2]; if("error" in i) { var a=i.error; (0, c.warn)("Error during font loading: "+a), this.commonObjs.resolve(r, a); break } var o=null; (0, u.getDefaultSetting)("pdfBug")&&c.globalScope.FontInspector&&c.globalScope.FontInspector.enabled&&(o={ registerFont: function(e, t) { c.globalScope.FontInspector.fontAdded(e, t) } }); var s=new d.FontFaceObject(i, { isEvalSuported: (0, u.getDefaultSetting)("isEvalSupported"), disableFontFace: (0, u.getDefaultSetting)("disableFontFace"), fontRegistry: o }), l=function(e) { t.commonObjs.resolve(r, s) }; this.fontLoader.bind([s], l); break; case "FontPath": this.commonObjs.resolve(r, e[2]); break; default: (0, c.error)("Got unknown common object type "+n) } } }, this), e.on("obj", function(e) { if(!this.destroyed) { var t, r=e[0], n=e[1], i=e[2], a=this.pageCache[n]; if(!a.objs.hasData(r)) switch(i) { case "JpegStream": t=e[3], (0, c.loadJpegStream)(r, t, a.objs); break; case "Image": t=e[3], a.objs.resolve(r, t); t&&"data" in t&&t.data.length>8e6&&(a.cleanupAfterRender=!0); break; default: (0, c.error)("Got unknown object type "+i) } } }, this), e.on("DocProgress", function(e) { if(!this.destroyed) { var t=this.loadingTask; t.onProgress&&t.onProgress({ loaded: e.loaded, total: e.total }) } }, this), e.on("PageError", function(e) { if(!this.destroyed) { var t=this.pageCache[e.pageNum-1], r=t.intentStates[e.intent]; if(r.displayReadyCapability? r.displayReadyCapability.reject(e.error):(0, c.error)(e.error), r.operatorList) { r.operatorList.lastChunk=!0; for(var n=0; n<r.renderTasks.length; n++)r.renderTasks[n].operatorListChanged() } } }, this), e.on("UnsupportedFeature", function(e) { if(!this.destroyed) { var t=e.featureId, r=this.loadingTask; r.onUnsupportedFeature&&r.onUnsupportedFeature(t), I.notify(t) } }, this), e.on("JpegDecode", function(e) { if(this.destroyed) return Promise.reject(new Error("Worker was destroyed")); if("undefined"==typeof document) return Promise.reject(new Error('"document" is not defined.')); var t=e[0], r=e[1]; return 3!==r&&1!==r? Promise.reject(new Error("Only 3 components or 1 component can be returned")):new Promise(function(e, n) { var i=new Image; i.onload=function() { var t=i.width, n=i.height, a=t*n, o=4*a, s=new Uint8Array(a*r), l=document.createElement("canvas"); l.width=t, l.height=n; var c=l.getContext("2d"); c.drawImage(i, 0, 0); var u, d, h=c.getImageData(0, 0, t, n).data; if(3===r) for(u=0, d=0; u<o; u+=4, d+=3)s[d]=h[u], s[d+1]=h[u+1], s[d+2]=h[u+2]; else if(1===r) for(u=0, d=0; u<o; u+=4, d++)s[d]=h[u]; e({ data: s, width: t, height: n }) }, i.onerror=function() { n(new Error("JpegDecode failed to load image")) }, i.src=t }) }, this), e.on("FetchBuiltInCMap", function(e) { return this.destroyed? Promise.reject(new Error("Worker was destroyed")):this.CMapReaderFactory.fetch({ name: e.name }) }, this) }, getData: function() { return this.messageHandler.sendWithPromise("GetData", null) }, getPage: function(e, t) { var r=this; if(!(0, c.isInt)(e)||e<=0||e>this.numPages) return Promise.reject(new Error("Invalid page request")); var n=e-1; if(n in this.pagePromises) return this.pagePromises[n]; var i=this.messageHandler.sendWithPromise("GetPage", { pageIndex: n }).then(function(e) { if(r.destroyed) throw new Error("Transport destroyed"); var t=new T(n, e, r); return r.pageCache[n]=t, t }); return this.pagePromises[n]=i, i }, getPageIndex: function(e) { return this.messageHandler.sendWithPromise("GetPageIndex", { ref: e }).catch(function(e) { return Promise.reject(new Error(e)) }) }, getAnnotations: function(e, t) { return this.messageHandler.sendWithPromise("GetAnnotations", { pageIndex: e, intent: t }) }, getDestinations: function() { return this.messageHandler.sendWithPromise("GetDestinations", null) }, getDestination: function(e) { return this.messageHandler.sendWithPromise("GetDestination", { id: e }) }, getPageLabels: function() { return this.messageHandler.sendWithPromise("GetPageLabels", null) }, getAttachments: function() { return this.messageHandler.sendWithPromise("GetAttachments", null) }, getJavaScript: function() { return this.messageHandler.sendWithPromise("GetJavaScript", null) }, getOutline: function() { return this.messageHandler.sendWithPromise("GetOutline", null) }, getMetadata: function() { return this.messageHandler.sendWithPromise("GetMetadata", null).then(function(e) { return { info: e[0], metadata: e[1]? new f.Metadata(e[1]):null } }) }, getStats: function() { return this.messageHandler.sendWithPromise("GetStats", null) }, startCleanup: function() { var e=this; this.messageHandler.sendWithPromise("Cleanup", null).then(function() { for(var t=0, r=e.pageCache.length; t<r; t++) { var n=e.pageCache[t]; n&&n.cleanup() } e.commonObjs.clear(), e.fontLoader.clear() }) } }, e }(), E=function() { function e() { this.objs=Object.create(null) } return e.prototype={ ensureObj: function(e) { if(this.objs[e]) return this.objs[e]; var t={ capability: (0, c.createPromiseCapability)(), data: null, resolved: !1 }; return this.objs[e]=t, t }, get: function(e, t) { if(t) return this.ensureObj(e).capability.promise.then(t), null; var r=this.objs[e]; return r&&r.resolved||(0, c.error)("Requesting object that isn't resolved yet "+e), r.data }, resolve: function(e, t) { var r=this.ensureObj(e); r.resolved=!0, r.data=t, r.capability.resolve(t) }, isResolved: function(e) { var t=this.objs; return !!t[e]&&t[e].resolved }, hasData: function(e) { return this.isResolved(e) }, getData: function(e) { var t=this.objs; return t[e]&&t[e].resolved? t[e].data:null }, clear: function() { this.objs=Object.create(null) } }, e }(), L=function() { function e(e) { this._internalRenderTask=e, this.onContinue=null } return e.prototype={ get promise() { return this._internalRenderTask.capability.promise }, cancel: function() { this._internalRenderTask.cancel() }, then: function(e, t) { return this.promise.then.apply(this.promise, arguments) } }, e }(), O=function() {
      function e(e, t, r, n, i, a, o) { this.callback=e, this.params=t, this.objs=r, this.commonObjs=n, this.operatorListIdx=null, this.operatorList=i, this.pageNumber=a, this.canvasFactory=o, this.running=!1, this.graphicsReadyCallback=null, this.graphicsReady=!1, this.useRequestAnimationFrame=!1, this.cancelled=!1, this.capability=(0, c.createPromiseCapability)(), this.task=new L(this), this._continueBound=this._continue.bind(this), this._scheduleNextBound=this._scheduleNext.bind(this), this._nextBound=this._next.bind(this) } return e.prototype={
        initializeGraphics: function(e) { if(!this.cancelled) { (0, u.getDefaultSetting)("pdfBug")&&c.globalScope.StepperManager&&c.globalScope.StepperManager.enabled&&(this.stepper=c.globalScope.StepperManager.create(this.pageNumber-1), this.stepper.init(this.operatorList), this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()); var t=this.params; this.gfx=new h.CanvasGraphics(t.canvasContext, this.commonObjs, this.objs, this.canvasFactory, t.imageLayer), this.gfx.beginDrawing({ transform: t.transform, viewport: t.viewport, transparency: e, background: t.background }), this.operatorListIdx=0, this.graphicsReady=!0, this.graphicsReadyCallback&&this.graphicsReadyCallback() } }, cancel: function() {
          this.running=!1, this.cancelled=!0, (0,
            u.getDefaultSetting)("pdfjsNext")? this.callback(new u.RenderingCancelledException("Rendering cancelled, page "+this.pageNumber, "canvas")):this.callback("cancelled")
        }, operatorListChanged: function() { if(!this.graphicsReady) return void (this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)); this.stepper&&this.stepper.updateOperatorList(this.operatorList), this.running||this._continue() }, _continue: function() { this.running=!0, this.cancelled||(this.task.onContinue? this.task.onContinue(this._scheduleNextBound):this._scheduleNext()) }, _scheduleNext: function() { this.useRequestAnimationFrame&&"undefined"!=typeof window? window.requestAnimationFrame(this._nextBound):Promise.resolve(void 0).then(this._nextBound) }, _next: function() { this.cancelled||(this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList, this.operatorListIdx, this._continueBound, this.stepper), this.operatorListIdx===this.operatorList.argsArray.length&&(this.running=!1, this.operatorList.lastChunk&&(this.gfx.endDrawing(), this.callback()))) }
      }, e
    }(), I=function() { var e=[]; return { listen: function(t) { (0, c.deprecated)("Global UnsupportedManager.listen is used:  use PDFDocumentLoadingTask.onUnsupportedFeature instead"), e.push(t) }, notify: function(t) { for(var r=0, n=e.length; r<n; r++)e[r](t) } } }(); t.version=_="1.8.0", t.build=S="", t.getDocument=i, t.LoopbackPort=k, t.PDFDataRangeTransport=P, t.PDFWorker=C, t.PDFDocumentProxy=x, t.PDFPageProxy=T, t._UnsupportedManager=I, t.version=_, t.build=S
  }, function(e, t, r) { "use strict"; Object.defineProperty(t, "__esModule", { value: !0 }), t.SVGGraphics=void 0; var n=r(0), i=function() { throw new Error("Not implemented: SVGGraphics") }, a={ fontStyle: "normal", fontWeight: "normal", fillColor: "#000000" }, o=function() { function e(e, t, r) { for(var n=-1, i=t; i<r; i++) { var a=255&(n^e[i]); n=n>>>8^s[a] } return -1^n } function t(t, r, n, i) { var a=i, o=r.length; n[a]=o>>24&255, n[a+1]=o>>16&255, n[a+2]=o>>8&255, n[a+3]=255&o, a+=4, n[a]=255&t.charCodeAt(0), n[a+1]=255&t.charCodeAt(1), n[a+2]=255&t.charCodeAt(2), n[a+3]=255&t.charCodeAt(3), a+=4, n.set(r, a), a+=r.length; var s=e(n, i+4, a); n[a]=s>>24&255, n[a+1]=s>>16&255, n[a+2]=s>>8&255, n[a+3]=255&s } function r(e, t, r) { for(var n=1, i=0, a=t; a<r; ++a)n=(n+(255&e[a]))%65521, i=(i+n)%65521; return i<<16|n } function i(e, i, s) { var l, c, u, d=e.width, h=e.height, f=e.data; switch(i) { case n.ImageKind.GRAYSCALE_1BPP: c=0, l=1, u=d+7>>3; break; case n.ImageKind.RGB_24BPP: c=2, l=8, u=3*d; break; case n.ImageKind.RGBA_32BPP: c=6, l=8, u=4*d; break; default: throw new Error("invalid format") }var p, m, g=new Uint8Array((1+u)*h), v=0, b=0; for(p=0; p<h; ++p)g[v++]=0, g.set(f.subarray(b, b+u), v), b+=u, v+=u; if(i===n.ImageKind.GRAYSCALE_1BPP) for(v=0, p=0; p<h; p++)for(v++, m=0; m<u; m++)g[v++]^=255; var y=new Uint8Array([d>>24&255, d>>16&255, d>>8&255, 255&d, h>>24&255, h>>16&255, h>>8&255, 255&h, l, c, 0, 0, 0]), A=g.length, _=Math.ceil(A/65535), S=new Uint8Array(2+A+5*_+4), w=0; S[w++]=120, S[w++]=156; for(var P=0; A>65535;)S[w++]=0, S[w++]=255, S[w++]=255, S[w++]=0, S[w++]=0, S.set(g.subarray(P, P+65535), w), w+=65535, P+=65535, A-=65535; S[w++]=1, S[w++]=255&A, S[w++]=A>>8&255, S[w++]=255&~A, S[w++]=(65535&~A)>>8&255, S.set(g.subarray(P), w), w+=g.length-P; var x=r(g, 0, g.length); S[w++]=x>>24&255, S[w++]=x>>16&255, S[w++]=x>>8&255, S[w++]=255&x; var T=a.length+3*o+y.length+S.length, k=new Uint8Array(T), C=0; return k.set(a, C), C+=a.length, t("IHDR", y, k, C), C+=o+y.length, t("IDATA", S, k, C), C+=o+S.length, t("IEND", new Uint8Array(0), k, C), (0, n.createObjectURL)(k, "image/png", s) } for(var a=new Uint8Array([137, 80, 78, 71, 13, 10, 26, 10]), o=12, s=new Int32Array(256), l=0; l<256; l++) { for(var c=l, u=0; u<8; u++)c=1&c? 3988292384^c>>1&2147483647:c>>1&2147483647; s[l]=c } return function(e, t) { return i(e, void 0===e.kind? n.ImageKind.GRAYSCALE_1BPP:e.kind, t) } }(), s=function() { function e() { this.fontSizeScale=1, this.fontWeight=a.fontWeight, this.fontSize=0, this.textMatrix=n.IDENTITY_MATRIX, this.fontMatrix=n.FONT_IDENTITY_MATRIX, this.leading=0, this.x=0, this.y=0, this.lineX=0, this.lineY=0, this.charSpacing=0, this.wordSpacing=0, this.textHScale=1, this.textRise=0, this.fillColor=a.fillColor, this.strokeColor="#000000", this.fillAlpha=1, this.strokeAlpha=1, this.lineWidth=1, this.lineJoin="", this.lineCap="", this.miterLimit=0, this.dashArray=[], this.dashPhase=0, this.dependencies=[], this.activeClipUrl=null, this.clipGroup=null, this.maskId="" } return e.prototype={ clone: function() { return Object.create(this) }, setCurrentPoint: function(e, t) { this.x=e, this.y=t } }, e }(); t.SVGGraphics=i=function() { function e(e) { for(var t=[], r=[], n=e.length, i=0; i<n; i++)"save"!==e[i].fn? "restore"===e[i].fn? t=r.pop():t.push(e[i]):(t.push({ fnId: 92, fn: "group", items: [] }), r.push(t), t=t[t.length-1].items); return t } function t(e) { if(e===(0|e)) return e.toString(); var t=e.toFixed(10), r=t.length-1; if("0"!==t[r]) return t; do { r-- } while("0"===t[r]); return t.substr(0, "."===t[r]? r:r+1) } function r(e) { if(0===e[4]&&0===e[5]) { if(0===e[1]&&0===e[2]) return 1===e[0]&&1===e[3]? "":"scale("+t(e[0])+" "+t(e[3])+")"; if(e[0]===e[3]&&e[1]===-e[2]) { return "rotate("+t(180*Math.acos(e[0])/Math.PI)+")" } } else if(1===e[0]&&0===e[1]&&0===e[2]&&1===e[3]) return "translate("+t(e[4])+" "+t(e[5])+")"; return "matrix("+t(e[0])+" "+t(e[1])+" "+t(e[2])+" "+t(e[3])+" "+t(e[4])+" "+t(e[5])+")" } function i(e, t, r) { this.current=new s, this.transformMatrix=n.IDENTITY_MATRIX, this.transformStack=[], this.extraStack=[], this.commonObjs=e, this.objs=t, this.pendingEOFill=!1, this.embedFonts=!1, this.embeddedFonts=Object.create(null), this.cssStyle=null, this.forceDataSchema=!!r } var l="http://www.w3.org/2000/svg", c="http://www.w3.org/1999/xlink", u=["butt", "round", "square"], d=["miter", "round", "bevel"], h=0, f=0; return i.prototype={ save: function() { this.transformStack.push(this.transformMatrix); var e=this.current; this.extraStack.push(e), this.current=e.clone() }, restore: function() { this.transformMatrix=this.transformStack.pop(), this.current=this.extraStack.pop(), this.tgrp=null }, group: function(e) { this.save(), this.executeOpTree(e), this.restore() }, loadDependencies: function(e) { for(var t=this, r=e.fnArray, i=r.length, a=e.argsArray, o=0; o<i; o++)if(n.OPS.dependency===r[o]) for(var s=a[o], l=0, c=s.length; l<c; l++) { var u, d=s[l], h="g_"===d.substring(0, 2); u=h? new Promise(function(e) { t.commonObjs.get(d, e) }):new Promise(function(e) { t.objs.get(d, e) }), this.current.dependencies.push(u) } return Promise.all(this.current.dependencies) }, transform: function(e, t, r, i, a, o) { var s=[e, t, r, i, a, o]; this.transformMatrix=n.Util.transform(this.transformMatrix, s), this.tgrp=null }, getSVG: function(e, t) { var r=this; this.viewport=t; var i=this._initialize(t); return this.loadDependencies(e).then(function() { r.transformMatrix=n.IDENTITY_MATRIX; var t=r.convertOpList(e); return r.executeOpTree(t), i }) }, convertOpList: function(t) { var r=t.argsArray, i=t.fnArray, a=i.length, o=[], s=[]; for(var l in n.OPS) o[n.OPS[l]]=l; for(var c=0; c<a; c++) { var u=i[c]; s.push({ fnId: u, fn: o[u], args: r[c] }) } return e(s) }, executeOpTree: function(e) { for(var t=e.length, r=0; r<t; r++) { var i=e[r].fn, a=e[r].fnId, o=e[r].args; switch(0|a) { case n.OPS.beginText: this.beginText(); break; case n.OPS.setLeading: this.setLeading(o); break; case n.OPS.setLeadingMoveText: this.setLeadingMoveText(o[0], o[1]); break; case n.OPS.setFont: this.setFont(o); break; case n.OPS.showText: case n.OPS.showSpacedText: this.showText(o[0]); break; case n.OPS.endText: this.endText(); break; case n.OPS.moveText: this.moveText(o[0], o[1]); break; case n.OPS.setCharSpacing: this.setCharSpacing(o[0]); break; case n.OPS.setWordSpacing: this.setWordSpacing(o[0]); break; case n.OPS.setHScale: this.setHScale(o[0]); break; case n.OPS.setTextMatrix: this.setTextMatrix(o[0], o[1], o[2], o[3], o[4], o[5]); break; case n.OPS.setLineWidth: this.setLineWidth(o[0]); break; case n.OPS.setLineJoin: this.setLineJoin(o[0]); break; case n.OPS.setLineCap: this.setLineCap(o[0]); break; case n.OPS.setMiterLimit: this.setMiterLimit(o[0]); break; case n.OPS.setFillRGBColor: this.setFillRGBColor(o[0], o[1], o[2]); break; case n.OPS.setStrokeRGBColor: this.setStrokeRGBColor(o[0], o[1], o[2]); break; case n.OPS.setDash: this.setDash(o[0], o[1]); break; case n.OPS.setGState: this.setGState(o[0]); break; case n.OPS.fill: this.fill(); break; case n.OPS.eoFill: this.eoFill(); break; case n.OPS.stroke: this.stroke(); break; case n.OPS.fillStroke: this.fillStroke(); break; case n.OPS.eoFillStroke: this.eoFillStroke(); break; case n.OPS.clip: this.clip("nonzero"); break; case n.OPS.eoClip: this.clip("evenodd"); break; case n.OPS.paintSolidColorImageMask: this.paintSolidColorImageMask(); break; case n.OPS.paintJpegXObject: this.paintJpegXObject(o[0], o[1], o[2]); break; case n.OPS.paintImageXObject: this.paintImageXObject(o[0]); break; case n.OPS.paintInlineImageXObject: this.paintInlineImageXObject(o[0]); break; case n.OPS.paintImageMaskXObject: this.paintImageMaskXObject(o[0]); break; case n.OPS.paintFormXObjectBegin: this.paintFormXObjectBegin(o[0], o[1]); break; case n.OPS.paintFormXObjectEnd: this.paintFormXObjectEnd(); break; case n.OPS.closePath: this.closePath(); break; case n.OPS.closeStroke: this.closeStroke(); break; case n.OPS.closeFillStroke: this.closeFillStroke(); break; case n.OPS.nextLine: this.nextLine(); break; case n.OPS.transform: this.transform(o[0], o[1], o[2], o[3], o[4], o[5]); break; case n.OPS.constructPath: this.constructPath(o[0], o[1]); break; case n.OPS.endPath: this.endPath(); break; case 92: this.group(e[r].items); break; default: (0, n.warn)("Unimplemented operator "+i) } } }, setWordSpacing: function(e) { this.current.wordSpacing=e }, setCharSpacing: function(e) { this.current.charSpacing=e }, nextLine: function() { this.moveText(0, this.current.leading) }, setTextMatrix: function(e, r, n, i, a, o) { var s=this.current; this.current.textMatrix=this.current.lineMatrix=[e, r, n, i, a, o], this.current.x=this.current.lineX=0, this.current.y=this.current.lineY=0, s.xcoords=[], s.tspan=document.createElementNS(l, "svg:tspan"), s.tspan.setAttributeNS(null, "font-family", s.fontFamily), s.tspan.setAttributeNS(null, "font-size", t(s.fontSize)+"px"), s.tspan.setAttributeNS(null, "y", t(-s.y)), s.txtElement=document.createElementNS(l, "svg:text"), s.txtElement.appendChild(s.tspan) }, beginText: function() { this.current.x=this.current.lineX=0, this.current.y=this.current.lineY=0, this.current.textMatrix=n.IDENTITY_MATRIX, this.current.lineMatrix=n.IDENTITY_MATRIX, this.current.tspan=document.createElementNS(l, "svg:tspan"), this.current.txtElement=document.createElementNS(l, "svg:text"), this.current.txtgrp=document.createElementNS(l, "svg:g"), this.current.xcoords=[] }, moveText: function(e, r) { var n=this.current; this.current.x=this.current.lineX+=e, this.current.y=this.current.lineY+=r, n.xcoords=[], n.tspan=document.createElementNS(l, "svg:tspan"), n.tspan.setAttributeNS(null, "font-family", n.fontFamily), n.tspan.setAttributeNS(null, "font-size", t(n.fontSize)+"px"), n.tspan.setAttributeNS(null, "y", t(-n.y)) }, showText: function(e) { var i=this.current, o=i.font, s=i.fontSize; if(0!==s) { var l, c=i.charSpacing, u=i.wordSpacing, d=i.fontDirection, h=i.textHScale*d, f=e.length, p=o.vertical, m=s*i.fontMatrix[0], g=0; for(l=0; l<f; ++l) { var v=e[l]; if(null!==v) if((0, n.isNum)(v)) g+=-v*s*.001; else { i.xcoords.push(i.x+g*h); var b=v.width, y=v.fontChar, A=(v.isSpace? u:0)+c, _=b*m+A*d; g+=_, i.tspan.textContent+=y } else g+=d*u } p? i.y-=g*h:i.x+=g*h, i.tspan.setAttributeNS(null, "x", i.xcoords.map(t).join(" ")), i.tspan.setAttributeNS(null, "y", t(-i.y)), i.tspan.setAttributeNS(null, "font-family", i.fontFamily), i.tspan.setAttributeNS(null, "font-size", t(i.fontSize)+"px"), i.fontStyle!==a.fontStyle&&i.tspan.setAttributeNS(null, "font-style", i.fontStyle), i.fontWeight!==a.fontWeight&&i.tspan.setAttributeNS(null, "font-weight", i.fontWeight), i.fillColor!==a.fillColor&&i.tspan.setAttributeNS(null, "fill", i.fillColor), i.txtElement.setAttributeNS(null, "transform", r(i.textMatrix)+" scale(1, -1)"), i.txtElement.setAttributeNS("http://www.w3.org/XML/1998/namespace", "xml:space", "preserve"), i.txtElement.appendChild(i.tspan), i.txtgrp.appendChild(i.txtElement), this._ensureTransformGroup().appendChild(i.txtElement) } }, setLeadingMoveText: function(e, t) { this.setLeading(-t), this.moveText(e, t) }, addFontStyle: function(e) { this.cssStyle||(this.cssStyle=document.createElementNS(l, "svg:style"), this.cssStyle.setAttributeNS(null, "type", "text/css"), this.defs.appendChild(this.cssStyle)); var t=(0, n.createObjectURL)(e.data, e.mimetype, this.forceDataSchema); this.cssStyle.textContent+='@font-face { font-family: "'+e.loadedName+'"; src: url('+t+"); }\n" }, setFont: function(e) { var r=this.current, i=this.commonObjs.get(e[0]), a=e[1]; this.current.font=i, this.embedFonts&&i.data&&!this.embeddedFonts[i.loadedName]&&(this.addFontStyle(i), this.embeddedFonts[i.loadedName]=i), r.fontMatrix=i.fontMatrix? i.fontMatrix:n.FONT_IDENTITY_MATRIX; var o=i.black? i.bold? "bolder":"bold":i.bold? "bold":"normal", s=i.italic? "italic":"normal"; a<0? (a=-a, r.fontDirection=-1):r.fontDirection=1, r.fontSize=a, r.fontFamily=i.loadedName, r.fontWeight=o, r.fontStyle=s, r.tspan=document.createElementNS(l, "svg:tspan"), r.tspan.setAttributeNS(null, "y", t(-r.y)), r.xcoords=[] }, endText: function() { }, setLineWidth: function(e) { this.current.lineWidth=e }, setLineCap: function(e) { this.current.lineCap=u[e] }, setLineJoin: function(e) { this.current.lineJoin=d[e] }, setMiterLimit: function(e) { this.current.miterLimit=e }, setStrokeRGBColor: function(e, t, r) { var i=n.Util.makeCssRgb(e, t, r); this.current.strokeColor=i }, setFillRGBColor: function(e, t, r) { var i=n.Util.makeCssRgb(e, t, r); this.current.fillColor=i, this.current.tspan=document.createElementNS(l, "svg:tspan"), this.current.xcoords=[] }, setDash: function(e, t) { this.current.dashArray=e, this.current.dashPhase=t }, constructPath: function(e, r) { var i=this.current, a=i.x, o=i.y; i.path=document.createElementNS(l, "svg:path"); for(var s=[], c=e.length, u=0, d=0; u<c; u++)switch(0|e[u]) { case n.OPS.rectangle: a=r[d++], o=r[d++]; var h=r[d++], f=r[d++], p=a+h, m=o+f; s.push("M", t(a), t(o), "L", t(p), t(o), "L", t(p), t(m), "L", t(a), t(m), "Z"); break; case n.OPS.moveTo: a=r[d++], o=r[d++], s.push("M", t(a), t(o)); break; case n.OPS.lineTo: a=r[d++], o=r[d++], s.push("L", t(a), t(o)); break; case n.OPS.curveTo: a=r[d+4], o=r[d+5], s.push("C", t(r[d]), t(r[d+1]), t(r[d+2]), t(r[d+3]), t(a), t(o)), d+=6; break; case n.OPS.curveTo2: a=r[d+2], o=r[d+3], s.push("C", t(a), t(o), t(r[d]), t(r[d+1]), t(r[d+2]), t(r[d+3])), d+=4; break; case n.OPS.curveTo3: a=r[d+2], o=r[d+3], s.push("C", t(r[d]), t(r[d+1]), t(a), t(o), t(a), t(o)), d+=4; break; case n.OPS.closePath: s.push("Z") }i.path.setAttributeNS(null, "d", s.join(" ")), i.path.setAttributeNS(null, "stroke-miterlimit", t(i.miterLimit)), i.path.setAttributeNS(null, "stroke-linecap", i.lineCap), i.path.setAttributeNS(null, "stroke-linejoin", i.lineJoin), i.path.setAttributeNS(null, "stroke-width", t(i.lineWidth)+"px"), i.path.setAttributeNS(null, "stroke-dasharray", i.dashArray.map(t).join(" ")), i.path.setAttributeNS(null, "stroke-dashoffset", t(i.dashPhase)+"px"), i.path.setAttributeNS(null, "fill", "none"), this._ensureTransformGroup().appendChild(i.path), i.element=i.path, i.setCurrentPoint(a, o) }, endPath: function() { }, clip: function(e) { var t=this.current, n="clippath"+h; h++; var i=document.createElementNS(l, "svg:clipPath"); i.setAttributeNS(null, "id", n), i.setAttributeNS(null, "transform", r(this.transformMatrix)); var a=t.element.cloneNode(); "evenodd"===e? a.setAttributeNS(null, "clip-rule", "evenodd"):a.setAttributeNS(null, "clip-rule", "nonzero"), i.appendChild(a), this.defs.appendChild(i), t.activeClipUrl&&(t.clipGroup=null, this.extraStack.forEach(function(e) { e.clipGroup=null })), t.activeClipUrl="url(#"+n+")", this.tgrp=null }, closePath: function() { var e=this.current, t=e.path.getAttributeNS(null, "d"); t+="Z", e.path.setAttributeNS(null, "d", t) }, setLeading: function(e) { this.current.leading=-e }, setTextRise: function(e) { this.current.textRise=e }, setHScale: function(e) { this.current.textHScale=e/100 }, setGState: function(e) { for(var t=0, r=e.length; t<r; t++) { var i=e[t], a=i[0], o=i[1]; switch(a) { case "LW": this.setLineWidth(o); break; case "LC": this.setLineCap(o); break; case "LJ": this.setLineJoin(o); break; case "ML": this.setMiterLimit(o); break; case "D": this.setDash(o[0], o[1]); break; case "Font": this.setFont(o); break; default: (0, n.warn)("Unimplemented graphic state "+a) } } }, fill: function() { var e=this.current; e.element.setAttributeNS(null, "fill", e.fillColor) }, stroke: function() { var e=this.current; e.element.setAttributeNS(null, "stroke", e.strokeColor), e.element.setAttributeNS(null, "fill", "none") }, eoFill: function() { var e=this.current; e.element.setAttributeNS(null, "fill", e.fillColor), e.element.setAttributeNS(null, "fill-rule", "evenodd") }, fillStroke: function() { this.stroke(), this.fill() }, eoFillStroke: function() { this.current.element.setAttributeNS(null, "fill-rule", "evenodd"), this.fillStroke() }, closeStroke: function() { this.closePath(), this.stroke() }, closeFillStroke: function() { this.closePath(), this.fillStroke() }, paintSolidColorImageMask: function() { var e=this.current, t=document.createElementNS(l, "svg:rect"); t.setAttributeNS(null, "x", "0"), t.setAttributeNS(null, "y", "0"), t.setAttributeNS(null, "width", "1px"), t.setAttributeNS(null, "height", "1px"), t.setAttributeNS(null, "fill", e.fillColor), this._ensureTransformGroup().appendChild(t) }, paintJpegXObject: function(e, r, n) { var i=this.objs.get(e), a=document.createElementNS(l, "svg:image"); a.setAttributeNS(c, "xlink:href", i.src), a.setAttributeNS(null, "width", t(r)), a.setAttributeNS(null, "height", t(n)), a.setAttributeNS(null, "x", "0"), a.setAttributeNS(null, "y", t(-n)), a.setAttributeNS(null, "transform", "scale("+t(1/r)+" "+t(-1/n)+")"), this._ensureTransformGroup().appendChild(a) }, paintImageXObject: function(e) { var t=this.objs.get(e); if(!t) return void (0, n.warn)("Dependent image isn't ready yet"); this.paintInlineImageXObject(t) }, paintInlineImageXObject: function(e, r) { var n=e.width, i=e.height, a=o(e, this.forceDataSchema), s=document.createElementNS(l, "svg:rect"); s.setAttributeNS(null, "x", "0"), s.setAttributeNS(null, "y", "0"), s.setAttributeNS(null, "width", t(n)), s.setAttributeNS(null, "height", t(i)), this.current.element=s, this.clip("nonzero"); var u=document.createElementNS(l, "svg:image"); u.setAttributeNS(c, "xlink:href", a), u.setAttributeNS(null, "x", "0"), u.setAttributeNS(null, "y", t(-i)), u.setAttributeNS(null, "width", t(n)+"px"), u.setAttributeNS(null, "height", t(i)+"px"), u.setAttributeNS(null, "transform", "scale("+t(1/n)+" "+t(-1/i)+")"), r? r.appendChild(u):this._ensureTransformGroup().appendChild(u) }, paintImageMaskXObject: function(e) { var r=this.current, n=e.width, i=e.height, a=r.fillColor; r.maskId="mask"+f++; var o=document.createElementNS(l, "svg:mask"); o.setAttributeNS(null, "id", r.maskId); var s=document.createElementNS(l, "svg:rect"); s.setAttributeNS(null, "x", "0"), s.setAttributeNS(null, "y", "0"), s.setAttributeNS(null, "width", t(n)), s.setAttributeNS(null, "height", t(i)), s.setAttributeNS(null, "fill", a), s.setAttributeNS(null, "mask", "url(#"+r.maskId+")"), this.defs.appendChild(o), this._ensureTransformGroup().appendChild(s), this.paintInlineImageXObject(e, o) }, paintFormXObjectBegin: function(e, r) { if((0, n.isArray)(e)&&6===e.length&&this.transform(e[0], e[1], e[2], e[3], e[4], e[5]), (0, n.isArray)(r)&&4===r.length) { var i=r[2]-r[0], a=r[3]-r[1], o=document.createElementNS(l, "svg:rect"); o.setAttributeNS(null, "x", r[0]), o.setAttributeNS(null, "y", r[1]), o.setAttributeNS(null, "width", t(i)), o.setAttributeNS(null, "height", t(a)), this.current.element=o, this.clip("nonzero"), this.endPath() } }, paintFormXObjectEnd: function() { }, _initialize: function(e) { var t=document.createElementNS(l, "svg:svg"); t.setAttributeNS(null, "version", "1.1"), t.setAttributeNS(null, "width", e.width+"px"), t.setAttributeNS(null, "height", e.height+"px"), t.setAttributeNS(null, "preserveAspectRatio", "none"), t.setAttributeNS(null, "viewBox", "0 0 "+e.width+" "+e.height); var n=document.createElementNS(l, "svg:defs"); t.appendChild(n), this.defs=n; var i=document.createElementNS(l, "svg:g"); return i.setAttributeNS(null, "transform", r(e.transform)), t.appendChild(i), this.svg=i, t }, _ensureClipGroup: function() { if(!this.current.clipGroup) { var e=document.createElementNS(l, "svg:g"); e.setAttributeNS(null, "clip-path", this.current.activeClipUrl), this.svg.appendChild(e), this.current.clipGroup=e } return this.current.clipGroup }, _ensureTransformGroup: function() { return this.tgrp||(this.tgrp=document.createElementNS(l, "svg:g"), this.tgrp.setAttributeNS(null, "transform", r(this.transformMatrix)), this.current.activeClipUrl? this._ensureClipGroup().appendChild(this.tgrp):this.svg.appendChild(this.tgrp)), this.tgrp } }, i }(), t.SVGGraphics=i }, function(e, t, r) { "use strict"; Object.defineProperty(t, "__esModule", { value: !0 }), t.renderTextLayer=void 0; var n=r(0), i=r(1), a=function() { function e(e) { return !d.test(e) } function t(t, r, a) { var o=document.createElement("div"), s={ style: null, angle: 0, canvasWidth: 0, isWhitespace: !1, originalTransform: null, paddingBottom: 0, paddingLeft: 0, paddingRight: 0, paddingTop: 0, scale: 1 }; if(t._textDivs.push(o), e(r.str)) return s.isWhitespace=!0, void t._textDivProperties.set(o, s); var l=n.Util.transform(t._viewport.transform, r.transform), c=Math.atan2(l[1], l[0]), u=a[r.fontName]; u.vertical&&(c+=Math.PI/2); var d=Math.sqrt(l[2]*l[2]+l[3]*l[3]), f=d; u.ascent? f=u.ascent*f:u.descent&&(f=(1+u.descent)*f); var p, m; if(0===c? (p=l[4], m=l[5]-f):(p=l[4]+f*Math.sin(c), m=l[5]-f*Math.cos(c)), h[1]=p, h[3]=m, h[5]=d, h[7]=u.fontFamily, s.style=h.join(""), o.setAttribute("style", s.style), o.textContent=r.str, (0, i.getDefaultSetting)("pdfBug")&&(o.dataset.fontName=r.fontName), 0!==c&&(s.angle=c*(180/Math.PI)), r.str.length>1&&(u.vertical? s.canvasWidth=r.height*t._viewport.scale:s.canvasWidth=r.width*t._viewport.scale), t._textDivProperties.set(o, s), t._enhanceTextSelection) { var g=1, v=0; 0!==c&&(g=Math.cos(c), v=Math.sin(c)); var b, y, A=(u.vertical? r.height:r.width)*t._viewport.scale, _=d; 0!==c? (b=[g, v, -v, g, p, m], y=n.Util.getAxialAlignedBoundingBox([0, 0, A, _], b)):y=[p, m, p+A, m+_], t._bounds.push({ left: y[0], top: y[1], right: y[2], bottom: y[3], div: o, size: [A, _], m: b }) } } function r(e) { if(!e._canceled) { var t=e._container, r=e._textDivs, n=e._capability, a=r.length; if(a>u) return e._renderingDone=!0, void n.resolve(); var o=document.createElement("canvas"); o.mozOpaque=!0; for(var s, l, c=o.getContext("2d", { alpha: !1 }), d=0; d<a; d++) { var h=r[d], f=e._textDivProperties.get(h); if(!f.isWhitespace) { var p=h.style.fontSize, m=h.style.fontFamily; p===s&&m===l||(c.font=p+" "+m, s=p, l=m); var g=c.measureText(h.textContent).width; t.appendChild(h); var v=""; 0!==f.canvasWidth&&g>0&&(f.scale=f.canvasWidth/g, v="scaleX("+f.scale+")"), 0!==f.angle&&(v="rotate("+f.angle+"deg) "+v), ""!==v&&(f.originalTransform=v, i.CustomStyle.setProp("transform", h, v)), e._textDivProperties.set(h, f) } } e._renderingDone=!0, n.resolve() } } function a(e) { for(var t=e._bounds, r=e._viewport, i=o(r.width, r.height, t), a=0; a<i.length; a++) { var s=t[a].div, l=e._textDivProperties.get(s); if(0!==l.angle) { var c=i[a], u=t[a], d=u.m, h=d[0], f=d[1], p=[[0, 0], [0, u.size[1]], [u.size[0], 0], u.size], m=new Float64Array(64); p.forEach(function(e, t) { var r=n.Util.applyTransform(e, d); m[t+0]=h&&(c.left-r[0])/h, m[t+4]=f&&(c.top-r[1])/f, m[t+8]=h&&(c.right-r[0])/h, m[t+12]=f&&(c.bottom-r[1])/f, m[t+16]=f&&(c.left-r[0])/-f, m[t+20]=h&&(c.top-r[1])/h, m[t+24]=f&&(c.right-r[0])/-f, m[t+28]=h&&(c.bottom-r[1])/h, m[t+32]=h&&(c.left-r[0])/-h, m[t+36]=f&&(c.top-r[1])/-f, m[t+40]=h&&(c.right-r[0])/-h, m[t+44]=f&&(c.bottom-r[1])/-f, m[t+48]=f&&(c.left-r[0])/f, m[t+52]=h&&(c.top-r[1])/-h, m[t+56]=f&&(c.right-r[0])/f, m[t+60]=h&&(c.bottom-r[1])/-h }); var g=function(e, t, r) { for(var n=0, i=0; i<r; i++) { var a=e[t++]; a>0&&(n=n? Math.min(a, n):a) } return n }, v=1+Math.min(Math.abs(h), Math.abs(f)); l.paddingLeft=g(m, 32, 16)/v, l.paddingTop=g(m, 48, 16)/v, l.paddingRight=g(m, 0, 16)/v, l.paddingBottom=g(m, 16, 16)/v, e._textDivProperties.set(s, l) } else l.paddingLeft=t[a].left-i[a].left, l.paddingTop=t[a].top-i[a].top, l.paddingRight=i[a].right-t[a].right, l.paddingBottom=i[a].bottom-t[a].bottom, e._textDivProperties.set(s, l) } } function o(e, t, r) { var n=r.map(function(e, t) { return { x1: e.left, y1: e.top, x2: e.right, y2: e.bottom, index: t, x1New: void 0, x2New: void 0 } }); s(e, n); var i=new Array(r.length); return n.forEach(function(e) { var t=e.index; i[t]={ left: e.x1New, top: 0, right: e.x2New, bottom: 0 } }), r.map(function(t, r) { var a=i[r], o=n[r]; o.x1=t.top, o.y1=e-a.right, o.x2=t.bottom, o.y2=e-a.left, o.index=r, o.x1New=void 0, o.x2New=void 0 }), s(t, n), n.forEach(function(e) { var t=e.index; i[t].top=e.x1New, i[t].bottom=e.x2New }), i } function s(e, t) { t.sort(function(e, t) { return e.x1-t.x1||e.index-t.index }); var r={ x1: -1/0, y1: -1/0, x2: 0, y2: 1/0, index: -1, x1New: 0, x2New: 0 }, n=[{ start: -1/0, end: 1/0, boundary: r }]; t.forEach(function(e) { for(var t=0; t<n.length&&n[t].end<=e.y1;)t++; for(var r=n.length-1; r>=0&&n[r].start>=e.y2;)r--; var i, a, o, s, l=-1/0; for(o=t; o<=r; o++) { i=n[o], a=i.boundary; var c; c=a.x2>e.x1? a.index>e.index? a.x1New:e.x1:void 0===a.x2New? (a.x2+e.x1)/2:a.x2New, c>l&&(l=c) } for(e.x1New=l, o=t; o<=r; o++)i=n[o], a=i.boundary, void 0===a.x2New? a.x2>e.x1? a.index>e.index&&(a.x2New=a.x2):a.x2New=l:a.x2New>l&&(a.x2New=Math.max(l, a.x2)); var u=[], d=null; for(o=t; o<=r; o++) { i=n[o], a=i.boundary; var h=a.x2>e.x2? a:e; d===h? u[u.length-1].end=i.end:(u.push({ start: i.start, end: i.end, boundary: h }), d=h) } for(n[t].start<e.y1&&(u[0].start=e.y1, u.unshift({ start: n[t].start, end: e.y1, boundary: n[t].boundary })), e.y2<n[r].end&&(u[u.length-1].end=e.y2, u.push({ start: e.y2, end: n[r].end, boundary: n[r].boundary })), o=t; o<=r; o++)if(i=n[o], a=i.boundary, void 0===a.x2New) { var f=!1; for(s=t-1; !f&&s>=0&&n[s].start>=a.y1; s--)f=n[s].boundary===a; for(s=r+1; !f&&s<n.length&&n[s].end<=a.y2; s++)f=n[s].boundary===a; for(s=0; !f&&s<u.length; s++)f=u[s].boundary===a; f||(a.x2New=l) } Array.prototype.splice.apply(n, [t, r-t+1].concat(u)) }), n.forEach(function(t) { var r=t.boundary; void 0===r.x2New&&(r.x2New=Math.max(e, r.x2)) }) } function l(e, t, r, i, a) { this._textContent=e, this._container=t, this._viewport=r, this._textDivs=i||[], this._textDivProperties=new WeakMap, this._renderingDone=!1, this._canceled=!1, this._capability=(0, n.createPromiseCapability)(), this._renderTimer=null, this._bounds=[], this._enhanceTextSelection=!!a } function c(e) { var t=new l(e.textContent, e.container, e.viewport, e.textDivs, e.enhanceTextSelection); return t._render(e.timeout), t } var u=1e5, d=/\S/, h=["left: ", 0, "px; top: ", 0, "px; font-size: ", 0, "px; font-family: ", "", ";"]; return l.prototype={ get promise() { return this._capability.promise }, cancel: function() { this._canceled=!0, null!==this._renderTimer&&(clearTimeout(this._renderTimer), this._renderTimer=null), this._capability.reject("canceled") }, _render: function(e) { for(var n=this, i=this._textContent.items, a=this._textContent.styles, o=0, s=i.length; o<s; o++)t(this, i[o], a); e? this._renderTimer=setTimeout(function() { r(n), n._renderTimer=null }, e):r(this) }, expandTextDivs: function(e) { if(this._enhanceTextSelection&&this._renderingDone) { null!==this._bounds&&(a(this), this._bounds=null); for(var t=0, r=this._textDivs.length; t<r; t++) { var n=this._textDivs[t], o=this._textDivProperties.get(n); if(!o.isWhitespace) if(e) { var s="", l=""; 1!==o.scale&&(s="scaleX("+o.scale+")"), 0!==o.angle&&(s="rotate("+o.angle+"deg) "+s), 0!==o.paddingLeft&&(l+=" padding-left: "+o.paddingLeft/o.scale+"px;", s+=" translateX("+-o.paddingLeft/o.scale+"px)"), 0!==o.paddingTop&&(l+=" padding-top: "+o.paddingTop+"px;", s+=" translateY("+-o.paddingTop+"px)"), 0!==o.paddingRight&&(l+=" padding-right: "+o.paddingRight/o.scale+"px;"), 0!==o.paddingBottom&&(l+=" padding-bottom: "+o.paddingBottom+"px;"), ""!==l&&n.setAttribute("style", o.style+l), ""!==s&&i.CustomStyle.setProp("transform", n, s) } else n.style.padding=0, i.CustomStyle.setProp("transform", n, o.originalTransform||"") } } } }, c }(); t.renderTextLayer=a }, function(e, t, r) { "use strict"; var n, i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator? function(e) { return typeof e }:function(e) { return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype? "symbol":typeof e }; n=function() { return this }(); try { n=n||Function("return this")()||(0, eval)("this") } catch(e) { "object"===("undefined"==typeof window? "undefined":i(window))&&(n=window) } e.exports=n }, function(e, t, r) { "use strict"; function n(e) { return e.replace(/>\\376\\377([^<]+)/g, function(e, t) { for(var r=t.replace(/\\([0-3])([0-7])([0-7])/g, function(e, t, r, n) { return String.fromCharCode(64*t+8*r+1*n) }), n="", i=0; i<r.length; i+=2) { var a=256*r.charCodeAt(i)+r.charCodeAt(i+1); n+=a>=32&&a<127&&60!==a&&62!==a&&38!==a? String.fromCharCode(a):"&#x"+(65536+a).toString(16).substring(1)+";" } return ">"+n }) } function i(e) { if("string"==typeof e) { e=n(e); e=(new DOMParser).parseFromString(e, "application/xml") } else e instanceof Document||(0, a.error)("Metadata: Invalid metadata object"); this.metaDocument=e, this.metadata=Object.create(null), this.parse() } Object.defineProperty(t, "__esModule", { value: !0 }), t.Metadata=void 0; var a=r(0); i.prototype={ parse: function() { var e=this.metaDocument, t=e.documentElement; if("rdf:rdf"!==t.nodeName.toLowerCase()) for(t=t.firstChild; t&&"rdf:rdf"!==t.nodeName.toLowerCase();)t=t.nextSibling; var r=t? t.nodeName.toLowerCase():null; if(t&&"rdf:rdf"===r&&t.hasChildNodes()) { var n, i, a, o, s, l, c, u=t.childNodes; for(o=0, l=u.length; o<l; o++)if(n=u[o], "rdf:description"===n.nodeName.toLowerCase()) for(s=0, c=n.childNodes.length; s<c; s++)"#text"!==n.childNodes[s].nodeName.toLowerCase()&&(i=n.childNodes[s], a=i.nodeName.toLowerCase(), this.metadata[a]=i.textContent.trim()) } }, get: function(e) { return this.metadata[e]||null }, has: function(e) { return void 0!==this.metadata[e] } }, t.Metadata=i }, function(e, t, r) {
    "use strict"; Object.defineProperty(t, "__esModule", { value: !0 }), t.WebGLUtils=void 0; var n=r(1), i=r(0), a=function() {
      function e(e, t, r) { var n=e.createShader(r); if(e.shaderSource(n, t), e.compileShader(n), !e.getShaderParameter(n, e.COMPILE_STATUS)) { var i=e.getShaderInfoLog(n); throw new Error("Error during shader compilation: "+i) } return n } function t(t, r) { return e(t, r, t.VERTEX_SHADER) } function r(t, r) { return e(t, r, t.FRAGMENT_SHADER) } function a(e, t) { for(var r=e.createProgram(), n=0, i=t.length; n<i; ++n)e.attachShader(r, t[n]); if(e.linkProgram(r), !e.getProgramParameter(r, e.LINK_STATUS)) { var a=e.getProgramInfoLog(r); throw new Error("Error during program linking: "+a) } return r } function o(e, t, r) { e.activeTexture(r); var n=e.createTexture(); return e.bindTexture(e.TEXTURE_2D, n), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_S, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_WRAP_T, e.CLAMP_TO_EDGE), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MIN_FILTER, e.NEAREST), e.texParameteri(e.TEXTURE_2D, e.TEXTURE_MAG_FILTER, e.NEAREST), e.texImage2D(e.TEXTURE_2D, 0, e.RGBA, e.RGBA, e.UNSIGNED_BYTE, t), n } function s() { f||(p=document.createElement("canvas"), f=p.getContext("webgl", { premultipliedalpha: !1 })) } function l() { var e, n; s(), e=p, p=null, n=f, f=null; var i=t(n, m), o=r(n, g), l=a(n, [i, o]); n.useProgram(l); var c={}; c.gl=n, c.canvas=e, c.resolutionLocation=n.getUniformLocation(l, "u_resolution"), c.positionLocation=n.getAttribLocation(l, "a_position"), c.backdropLocation=n.getUniformLocation(l, "u_backdrop"), c.subtypeLocation=n.getUniformLocation(l, "u_subtype"); var u=n.getAttribLocation(l, "a_texCoord"), d=n.getUniformLocation(l, "u_image"), h=n.getUniformLocation(l, "u_mask"), b=n.createBuffer(); n.bindBuffer(n.ARRAY_BUFFER, b), n.bufferData(n.ARRAY_BUFFER, new Float32Array([0, 0, 1, 0, 0, 1, 0, 1, 1, 0, 1, 1]), n.STATIC_DRAW), n.enableVertexAttribArray(u), n.vertexAttribPointer(u, 2, n.FLOAT, !1, 0, 0), n.uniform1i(d, 0), n.uniform1i(h, 1), v=c } function c(e, t, r) { var n=e.width, i=e.height; v||l(); var a=v, s=a.canvas, c=a.gl; s.width=n, s.height=i, c.viewport(0, 0, c.drawingBufferWidth, c.drawingBufferHeight), c.uniform2f(a.resolutionLocation, n, i), r.backdrop? c.uniform4f(a.resolutionLocation, r.backdrop[0], r.backdrop[1], r.backdrop[2], 1):c.uniform4f(a.resolutionLocation, 0, 0, 0, 0), c.uniform1i(a.subtypeLocation, "Luminosity"===r.subtype? 1:0); var u=o(c, e, c.TEXTURE0), d=o(c, t, c.TEXTURE1), h=c.createBuffer(); return c.bindBuffer(c.ARRAY_BUFFER, h), c.bufferData(c.ARRAY_BUFFER, new Float32Array([0, 0, n, 0, 0, i, 0, i, n, 0, n, i]), c.STATIC_DRAW), c.enableVertexAttribArray(a.positionLocation), c.vertexAttribPointer(a.positionLocation, 2, c.FLOAT, !1, 0, 0), c.clearColor(0, 0, 0, 0), c.enable(c.BLEND), c.blendFunc(c.ONE, c.ONE_MINUS_SRC_ALPHA), c.clear(c.COLOR_BUFFER_BIT), c.drawArrays(c.TRIANGLES, 0, 6), c.flush(), c.deleteTexture(u), c.deleteTexture(d), c.deleteBuffer(h), s } function u() {
        var e, n; s(), e=p, p=null, n=f, f=null; var i=t(n, b), o=r(n, y), l=a(n, [i, o]); n.useProgram(l); var c={}; c.gl=n, c.canvas=e, c.resolutionLocation=n.getUniformLocation(l, "u_resolution"), c.scaleLocation=n.getUniformLocation(l, "u_scale"), c.offsetLocation=n.getUniformLocation(l, "u_offset"), c.positionLocation=n.getAttribLocation(l, "a_position"),
          c.colorLocation=n.getAttribLocation(l, "a_color"), A=c
      } function d(e, t, r, n, i) { A||u(); var a=A, o=a.canvas, s=a.gl; o.width=e, o.height=t, s.viewport(0, 0, s.drawingBufferWidth, s.drawingBufferHeight), s.uniform2f(a.resolutionLocation, e, t); var l, c, d, h=0; for(l=0, c=n.length; l<c; l++)switch(n[l].type) { case "lattice": d=n[l].coords.length/n[l].verticesPerRow|0, h+=(d-1)*(n[l].verticesPerRow-1)*6; break; case "triangles": h+=n[l].coords.length }var f=new Float32Array(2*h), p=new Uint8Array(3*h), m=i.coords, g=i.colors, v=0, b=0; for(l=0, c=n.length; l<c; l++) { var y=n[l], _=y.coords, S=y.colors; switch(y.type) { case "lattice": var w=y.verticesPerRow; d=_.length/w|0; for(var P=1; P<d; P++)for(var x=P*w+1, T=1; T<w; T++, x++)f[v]=m[_[x-w-1]], f[v+1]=m[_[x-w-1]+1], f[v+2]=m[_[x-w]], f[v+3]=m[_[x-w]+1], f[v+4]=m[_[x-1]], f[v+5]=m[_[x-1]+1], p[b]=g[S[x-w-1]], p[b+1]=g[S[x-w-1]+1], p[b+2]=g[S[x-w-1]+2], p[b+3]=g[S[x-w]], p[b+4]=g[S[x-w]+1], p[b+5]=g[S[x-w]+2], p[b+6]=g[S[x-1]], p[b+7]=g[S[x-1]+1], p[b+8]=g[S[x-1]+2], f[v+6]=f[v+2], f[v+7]=f[v+3], f[v+8]=f[v+4], f[v+9]=f[v+5], f[v+10]=m[_[x]], f[v+11]=m[_[x]+1], p[b+9]=p[b+3], p[b+10]=p[b+4], p[b+11]=p[b+5], p[b+12]=p[b+6], p[b+13]=p[b+7], p[b+14]=p[b+8], p[b+15]=g[S[x]], p[b+16]=g[S[x]+1], p[b+17]=g[S[x]+2], v+=12, b+=18; break; case "triangles": for(var k=0, C=_.length; k<C; k++)f[v]=m[_[k]], f[v+1]=m[_[k]+1], p[b]=g[S[k]], p[b+1]=g[S[k]+1], p[b+2]=g[S[k]+2], v+=2, b+=3 } } r? s.clearColor(r[0]/255, r[1]/255, r[2]/255, 1):s.clearColor(0, 0, 0, 0), s.clear(s.COLOR_BUFFER_BIT); var R=s.createBuffer(); s.bindBuffer(s.ARRAY_BUFFER, R), s.bufferData(s.ARRAY_BUFFER, f, s.STATIC_DRAW), s.enableVertexAttribArray(a.positionLocation), s.vertexAttribPointer(a.positionLocation, 2, s.FLOAT, !1, 0, 0); var E=s.createBuffer(); return s.bindBuffer(s.ARRAY_BUFFER, E), s.bufferData(s.ARRAY_BUFFER, p, s.STATIC_DRAW), s.enableVertexAttribArray(a.colorLocation), s.vertexAttribPointer(a.colorLocation, 3, s.UNSIGNED_BYTE, !1, 0, 0), s.uniform2f(a.scaleLocation, i.scaleX, i.scaleY), s.uniform2f(a.offsetLocation, i.offsetX, i.offsetY), s.drawArrays(s.TRIANGLES, 0, h), s.flush(), s.deleteBuffer(R), s.deleteBuffer(E), o } function h() { v&&v.canvas&&(v.canvas.width=0, v.canvas.height=0), A&&A.canvas&&(A.canvas.width=0, A.canvas.height=0), v=null, A=null } var f, p, m="  attribute vec2 a_position;                                      attribute vec2 a_texCoord;                                                                                                      uniform vec2 u_resolution;                                                                                                      varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec2 clipSpace = (a_position / u_resolution) * 2.0 - 1.0;       gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_texCoord = a_texCoord;                                      }                                                             ", g="  precision mediump float;                                                                                                        uniform vec4 u_backdrop;                                        uniform int u_subtype;                                          uniform sampler2D u_image;                                      uniform sampler2D u_mask;                                                                                                       varying vec2 v_texCoord;                                                                                                        void main() {                                                     vec4 imageColor = texture2D(u_image, v_texCoord);               vec4 maskColor = texture2D(u_mask, v_texCoord);                 if (u_backdrop.a > 0.0) {                                         maskColor.rgb = maskColor.rgb * maskColor.a +                                   u_backdrop.rgb * (1.0 - maskColor.a);         }                                                               float lum;                                                      if (u_subtype == 0) {                                             lum = maskColor.a;                                            } else {                                                          lum = maskColor.r * 0.3 + maskColor.g * 0.59 +                        maskColor.b * 0.11;                                     }                                                               imageColor.a *= lum;                                            imageColor.rgb *= imageColor.a;                                 gl_FragColor = imageColor;                                    }                                                             ", v=null, b="  attribute vec2 a_position;                                      attribute vec3 a_color;                                                                                                         uniform vec2 u_resolution;                                      uniform vec2 u_scale;                                           uniform vec2 u_offset;                                                                                                          varying vec4 v_color;                                                                                                           void main() {                                                     vec2 position = (a_position + u_offset) * u_scale;              vec2 clipSpace = (position / u_resolution) * 2.0 - 1.0;         gl_Position = vec4(clipSpace * vec2(1, -1), 0, 1);                                                                              v_color = vec4(a_color / 255.0, 1.0);                         }                                                             ", y="  precision mediump float;                                                                                                        varying vec4 v_color;                                                                                                           void main() {                                                     gl_FragColor = v_color;                                       }                                                             ", A=null; return { get isEnabled() { if((0, n.getDefaultSetting)("disableWebGL")) return !1; var e=!1; try { s(), e=!!f } catch(e) { } return (0, i.shadow)(this, "isEnabled", e) }, composeSMask: c, drawFigures: d, clear: h }
    }(); t.WebGLUtils=a
  }, function(e, t, r) { "use strict"; Object.defineProperty(t, "__esModule", { value: !0 }), t.PDFJS=t.isWorker=t.globalScope=void 0; var n=r(3), i=r(1), a=r(0), o=r(2), s=r(7), l=r(5), c=r(4), u="undefined"==typeof window; a.globalScope.PDFJS||(a.globalScope.PDFJS={}); var d=a.globalScope.PDFJS; d.version="1.8.0", d.build="", d.pdfBug=!1, void 0!==d.verbosity&&(0, a.setVerbosityLevel)(d.verbosity), delete d.verbosity, Object.defineProperty(d, "verbosity", { get: function() { return (0, a.getVerbosityLevel)() }, set: function(e) { (0, a.setVerbosityLevel)(e) }, enumerable: !0, configurable: !0 }), d.VERBOSITY_LEVELS=a.VERBOSITY_LEVELS, d.OPS=a.OPS, d.UNSUPPORTED_FEATURES=a.UNSUPPORTED_FEATURES, d.isValidUrl=i.isValidUrl, d.shadow=a.shadow, d.createBlob=a.createBlob, d.createObjectURL=function(e, t) { return (0, a.createObjectURL)(e, t, d.disableCreateObjectURL) }, Object.defineProperty(d, "isLittleEndian", { configurable: !0, get: function() { return (0, a.shadow)(d, "isLittleEndian", (0, a.isLittleEndian)()) } }), d.removeNullCharacters=a.removeNullCharacters, d.PasswordResponses=a.PasswordResponses, d.PasswordException=a.PasswordException, d.UnknownErrorException=a.UnknownErrorException, d.InvalidPDFException=a.InvalidPDFException, d.MissingPDFException=a.MissingPDFException, d.UnexpectedResponseException=a.UnexpectedResponseException, d.Util=a.Util, d.PageViewport=a.PageViewport, d.createPromiseCapability=a.createPromiseCapability, d.maxImageSize=void 0===d.maxImageSize? -1:d.maxImageSize, d.cMapUrl=void 0===d.cMapUrl? null:d.cMapUrl, d.cMapPacked=void 0!==d.cMapPacked&&d.cMapPacked, d.disableFontFace=void 0!==d.disableFontFace&&d.disableFontFace, d.imageResourcesPath=void 0===d.imageResourcesPath? "":d.imageResourcesPath, d.disableWorker=void 0!==d.disableWorker&&d.disableWorker, d.workerSrc=void 0===d.workerSrc? null:d.workerSrc, d.workerPort=void 0===d.workerPort? null:d.workerPort, d.disableRange=void 0!==d.disableRange&&d.disableRange, d.disableStream=void 0!==d.disableStream&&d.disableStream, d.disableAutoFetch=void 0!==d.disableAutoFetch&&d.disableAutoFetch, d.pdfBug=void 0!==d.pdfBug&&d.pdfBug, d.postMessageTransfers=void 0===d.postMessageTransfers||d.postMessageTransfers, d.disableCreateObjectURL=void 0!==d.disableCreateObjectURL&&d.disableCreateObjectURL, d.disableWebGL=void 0===d.disableWebGL||d.disableWebGL, d.externalLinkTarget=void 0===d.externalLinkTarget? i.LinkTarget.NONE:d.externalLinkTarget, d.externalLinkRel=void 0===d.externalLinkRel? i.DEFAULT_LINK_REL:d.externalLinkRel, d.isEvalSupported=void 0===d.isEvalSupported||d.isEvalSupported, d.pdfjsNext=void 0!==d.pdfjsNext&&d.pdfjsNext; var h=d.openExternalLinksInNewWindow; delete d.openExternalLinksInNewWindow, Object.defineProperty(d, "openExternalLinksInNewWindow", { get: function() { return d.externalLinkTarget===i.LinkTarget.BLANK }, set: function(e) { if(e&&(0, a.deprecated)('PDFJS.openExternalLinksInNewWindow, please use "PDFJS.externalLinkTarget = PDFJS.LinkTarget.BLANK" instead.'), d.externalLinkTarget!==i.LinkTarget.NONE) return void (0, a.warn)("PDFJS.externalLinkTarget is already initialized"); d.externalLinkTarget=e? i.LinkTarget.BLANK:i.LinkTarget.NONE }, enumerable: !0, configurable: !0 }), h&&(d.openExternalLinksInNewWindow=h), d.getDocument=n.getDocument, d.LoopbackPort=n.LoopbackPort, d.PDFDataRangeTransport=n.PDFDataRangeTransport, d.PDFWorker=n.PDFWorker, d.hasCanvasTypedArrays=!0, d.CustomStyle=i.CustomStyle, d.LinkTarget=i.LinkTarget, d.addLinkAttributes=i.addLinkAttributes, d.getFilenameFromUrl=i.getFilenameFromUrl, d.isExternalLinkTargetSet=i.isExternalLinkTargetSet, d.AnnotationLayer=o.AnnotationLayer, d.renderTextLayer=l.renderTextLayer, d.Metadata=s.Metadata, d.SVGGraphics=c.SVGGraphics, d.UnsupportedManager=n._UnsupportedManager, t.globalScope=a.globalScope, t.isWorker=u, t.PDFJS=d }, function(e, t, r) {
    "use strict"; var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator? function(e) { return typeof e }:function(e) { return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype? "symbol":typeof e }; !function(e, t) { for(var r in t) e[r]=t[r] }(t, function(e) { function t(n) { if(r[n]) return r[n].exports; var i=r[n]={ i: n, l: !1, exports: {} }; return e[n].call(i.exports, i, i.exports, t), i.l=!0, i.exports } var r={}; return t.m=e, t.c=r, t.i=function(e) { return e }, t.d=function(e, r, n) { t.o(e, r)||Object.defineProperty(e, r, { configurable: !1, enumerable: !0, get: n }) }, t.n=function(e) { var r=e&&e.__esModule? function() { return e.default }:function() { return e }; return t.d(r, "a", r), r }, t.o=function(e, t) { return Object.prototype.hasOwnProperty.call(e, t) }, t.p="", t(t.s=7) }([function(e, t, r) { function i(e) { return "string"==typeof e||"symbol"===(void 0===e? "undefined":o(e)) } function a(e, t, r) { if("function"!=typeof e) throw new TypeError("Argument is not a function"); return Function.prototype.apply.call(e, t, r) } var o="function"==typeof Symbol&&"symbol"===n(Symbol.iterator)? function(e) { return void 0===e? "undefined":n(e) }:function(e) { return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype? "symbol":void 0===e? "undefined":n(e) }, s=r(1), l=s.assert; t.typeIsObject=function(e) { return "object"===(void 0===e? "undefined":o(e))&&null!==e||"function"==typeof e }, t.createDataProperty=function(e, r, n) { l(t.typeIsObject(e)), Object.defineProperty(e, r, { value: n, writable: !0, enumerable: !0, configurable: !0 }) }, t.createArrayFromList=function(e) { return e.slice() }, t.ArrayBufferCopy=function(e, t, r, n, i) { new Uint8Array(e).set(new Uint8Array(r, n, i), t) }, t.CreateIterResultObject=function(e, t) { l("boolean"==typeof t); var r={}; return Object.defineProperty(r, "value", { value: e, enumerable: !0, writable: !0, configurable: !0 }), Object.defineProperty(r, "done", { value: t, enumerable: !0, writable: !0, configurable: !0 }), r }, t.IsFiniteNonNegativeNumber=function(e) { return !Number.isNaN(e)&&(e!==1/0&&!(e<0)) }, t.InvokeOrNoop=function(e, t, r) { l(void 0!==e), l(i(t)), l(Array.isArray(r)); var n=e[t]; if(void 0!==n) return a(n, e, r) }, t.PromiseInvokeOrNoop=function(e, r, n) { l(void 0!==e), l(i(r)), l(Array.isArray(n)); try { return Promise.resolve(t.InvokeOrNoop(e, r, n)) } catch(e) { return Promise.reject(e) } }, t.PromiseInvokeOrPerformFallback=function(e, t, r, n, o) { l(void 0!==e), l(i(t)), l(Array.isArray(r)), l(Array.isArray(o)); var s=void 0; try { s=e[t] } catch(e) { return Promise.reject(e) } if(void 0===s) return n.apply(null, o); try { return Promise.resolve(a(s, e, r)) } catch(e) { return Promise.reject(e) } }, t.TransferArrayBuffer=function(e) { return e.slice() }, t.ValidateAndNormalizeHighWaterMark=function(e) { if(e=Number(e), Number.isNaN(e)||e<0) throw new RangeError("highWaterMark property of a queuing strategy must be non-negative and non-NaN"); return e }, t.ValidateAndNormalizeQueuingStrategy=function(e, r) { if(void 0!==e&&"function"!=typeof e) throw new TypeError("size property of a queuing strategy must be a function"); return r=t.ValidateAndNormalizeHighWaterMark(r), { size: e, highWaterMark: r } } }, function(e, t, r) { function n(e) { e&&e.constructor===i&&setTimeout(function() { throw e }, 0) } function i(e) { this.name="AssertionError", this.message=e||"", this.stack=(new Error).stack } function a(e, t) { if(!e) throw new i(t) } i.prototype=Object.create(Error.prototype), i.prototype.constructor=i, e.exports={ rethrowAssertionErrorRejection: n, AssertionError: i, assert: a } }, function(e, t, r) { function n(e, t) { if(!(e instanceof t)) throw new TypeError("Cannot call a class as a function") } function i(e) { return new ye(e) } function a(e) { return !!ce(e)&&!!Object.prototype.hasOwnProperty.call(e, "_writableStreamController") } function o(e) { return de(!0===a(e), "IsWritableStreamLocked should only be used on known writable streams"), void 0!==e._writer } function s(e, t) { var r=e._state; if("closed"===r) return Promise.resolve(void 0); if("errored"===r) return Promise.reject(e._storedError); var n=new TypeError("Requested to abort"); if(void 0!==e._pendingAbortRequest) return Promise.reject(n); de("writable"===r||"erroring"===r, "state must be writable or erroring"); var i=!1; "erroring"===r&&(i=!0, t=void 0); var a=new Promise(function(r, n) { e._pendingAbortRequest={ _resolve: r, _reject: n, _reason: t, _wasAlreadyErroring: i } }); return !1===i&&u(e, n), a } function l(e) { return de(!0===o(e)), de("writable"===e._state), new Promise(function(t, r) { var n={ _resolve: t, _reject: r }; e._writeRequests.push(n) }) } function c(e, t) { var r=e._state; if("writable"===r) return void u(e, t); de("erroring"===r), d(e) } function u(e, t) { de(void 0===e._storedError, "stream._storedError === undefined"), de("writable"===e._state, "state must be writable"); var r=e._writableStreamController; de(void 0!==r, "controller must not be undefined"), e._state="erroring", e._storedError=t; var n=e._writer; void 0!==n&&k(n, t), !1===v(e)&&!0===r._started&&d(e) } function d(e) { de("erroring"===e._state, "stream._state === erroring"), de(!1===v(e), "WritableStreamHasOperationMarkedInFlight(stream) === false"), e._state="errored", e._writableStreamController.__errorSteps(); for(var t=e._storedError, r=0; r<e._writeRequests.length; r++) { e._writeRequests[r]._reject(t) } if(e._writeRequests=[], void 0===e._pendingAbortRequest) return void A(e); var n=e._pendingAbortRequest; if(e._pendingAbortRequest=void 0, !0===n._wasAlreadyErroring) return n._reject(t), void A(e); e._writableStreamController.__abortSteps(n._reason).then(function() { n._resolve(), A(e) }, function(t) { n._reject(t), A(e) }) } function h(e) { de(void 0!==e._inFlightWriteRequest), e._inFlightWriteRequest._resolve(void 0), e._inFlightWriteRequest=void 0 } function f(e, t) { de(void 0!==e._inFlightWriteRequest), e._inFlightWriteRequest._reject(t), e._inFlightWriteRequest=void 0, de("writable"===e._state||"erroring"===e._state), c(e, t) } function p(e) { de(void 0!==e._inFlightCloseRequest), e._inFlightCloseRequest._resolve(void 0), e._inFlightCloseRequest=void 0; var t=e._state; de("writable"===t||"erroring"===t), "erroring"===t&&(e._storedError=void 0, void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(), e._pendingAbortRequest=void 0)), e._state="closed"; var r=e._writer; void 0!==r&&Q(r), de(void 0===e._pendingAbortRequest, "stream._pendingAbortRequest === undefined"), de(void 0===e._storedError, "stream._storedError === undefined") } function m(e, t) { de(void 0!==e._inFlightCloseRequest), e._inFlightCloseRequest._reject(t), e._inFlightCloseRequest=void 0, de("writable"===e._state||"erroring"===e._state), void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._reject(t), e._pendingAbortRequest=void 0), c(e, t) } function g(e) { return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest } function v(e) { return void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest } function b(e) { de(void 0===e._inFlightCloseRequest), de(void 0!==e._closeRequest), e._inFlightCloseRequest=e._closeRequest, e._closeRequest=void 0 } function y(e) { de(void 0===e._inFlightWriteRequest, "there must be no pending write request"), de(0!==e._writeRequests.length, "writeRequests must not be empty"), e._inFlightWriteRequest=e._writeRequests.shift() } function A(e) { de("errored"===e._state, '_stream_.[[state]] is `"errored"`'), void 0!==e._closeRequest&&(de(void 0===e._inFlightCloseRequest), e._closeRequest._reject(e._storedError), e._closeRequest=void 0); var t=e._writer; void 0!==t&&(V(t, e._storedError), t._closedPromise.catch(function() { })) } function _(e, t) { de("writable"===e._state), de(!1===g(e)); var r=e._writer; void 0!==r&&t!==e._backpressure&&(!0===t? te(r):(de(!1===t), ne(r))), e._backpressure=t } function S(e) { return !!ce(e)&&!!Object.prototype.hasOwnProperty.call(e, "_ownerWritableStream") } function w(e, t) { var r=e._ownerWritableStream; return de(void 0!==r), s(r, t) } function P(e) { var t=e._ownerWritableStream; de(void 0!==t); var r=t._state; if("closed"===r||"errored"===r) return Promise.reject(new TypeError("The stream (in "+r+" state) is not in the writable state and cannot be closed")); de("writable"===r||"erroring"===r), de(!1===g(t)); var n=new Promise(function(e, r) { var n={ _resolve: e, _reject: r }; t._closeRequest=n }); return !0===t._backpressure&&"writable"===r&&ne(e), L(t._writableStreamController), n } function x(e) { var t=e._ownerWritableStream; de(void 0!==t); var r=t._state; return !0===g(t)||"closed"===r? Promise.resolve():"errored"===r? Promise.reject(t._storedError):(de("writable"===r||"erroring"===r), P(e)) } function T(e, t) { "pending"===e._closedPromiseState? V(e, t):J(e, t), e._closedPromise.catch(function() { }) } function k(e, t) { "pending"===e._readyPromiseState? ee(e, t):re(e, t), e._readyPromise.catch(function() { }) } function C(e) { var t=e._ownerWritableStream, r=t._state; return "errored"===r||"erroring"===r? null:"closed"===r? 0:I(t._writableStreamController) } function R(e) { var t=e._ownerWritableStream; de(void 0!==t), de(t._writer===e); var r=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness"); k(e, r), T(e, r), t._writer=void 0, e._ownerWritableStream=void 0 } function E(e, t) { var r=e._ownerWritableStream; de(void 0!==r); var n=r._writableStreamController, i=O(n, t); if(r!==e._ownerWritableStream) return Promise.reject(G("write to")); var a=r._state; if("errored"===a) return Promise.reject(r._storedError); if(!0===g(r)||"closed"===a) return Promise.reject(new TypeError("The stream is closing or closed and cannot be written to")); if("erroring"===a) return Promise.reject(r._storedError); de("writable"===a); var o=l(r); return j(n, t, i), o } function L(e) { me(e, "close", 0), F(e) } function O(e, t) { var r=e._strategySize; if(void 0===r) return 1; try { return r(t) } catch(t) { return N(e, t), 1 } } function I(e) { return e._strategyHWM-e._queueTotalSize } function j(e, t, r) { var n={ chunk: t }; try { me(e, n, r) } catch(t) { return void N(e, t) } var i=e._controlledWritableStream; if(!1===g(i)&&"writable"===i._state) { _(i, U(e)) } F(e) } function D(e) { return !!ce(e)&&!!Object.prototype.hasOwnProperty.call(e, "_underlyingSink") } function F(e) { var t=e._controlledWritableStream; if(!1!==e._started&&void 0===t._inFlightWriteRequest) { var r=t._state; if("closed"!==r&&"errored"!==r) { if("erroring"===r) return void d(t); if(0!==e._queue.length) { var n=ge(e); "close"===n? M(e):W(e, n.chunk) } } } } function N(e, t) { "writable"===e._controlledWritableStream._state&&q(e, t) } function M(e) { var t=e._controlledWritableStream; b(t), pe(e), de(0===e._queue.length, "queue must be empty once the final write record is dequeued"), se(e._underlyingSink, "close", []).then(function() { p(t) }, function(e) { m(t, e) }).catch(he) } function W(e, t) { var r=e._controlledWritableStream; y(r), se(e._underlyingSink, "write", [t, e]).then(function() { h(r); var t=r._state; if(de("writable"===t||"erroring"===t), pe(e), !1===g(r)&&"writable"===t) { var n=U(e); _(r, n) } F(e) }, function(e) { f(r, e) }).catch(he) } function U(e) { return I(e)<=0 } function q(e, t) { var r=e._controlledWritableStream; de("writable"===r._state), u(r, t) } function B(e) { return new TypeError("WritableStream.prototype."+e+" can only be used on a WritableStream") } function z(e) { return new TypeError("WritableStreamDefaultWriter.prototype."+e+" can only be used on a WritableStreamDefaultWriter") } function G(e) { return new TypeError("Cannot "+e+" a stream using a released writer") } function X(e) { e._closedPromise=new Promise(function(t, r) { e._closedPromise_resolve=t, e._closedPromise_reject=r, e._closedPromiseState="pending" }) } function H(e, t) { e._closedPromise=Promise.reject(t), e._closedPromise_resolve=void 0, e._closedPromise_reject=void 0, e._closedPromiseState="rejected" } function Y(e) { e._closedPromise=Promise.resolve(void 0), e._closedPromise_resolve=void 0, e._closedPromise_reject=void 0, e._closedPromiseState="resolved" } function V(e, t) { de(void 0!==e._closedPromise_resolve, "writer._closedPromise_resolve !== undefined"), de(void 0!==e._closedPromise_reject, "writer._closedPromise_reject !== undefined"), de("pending"===e._closedPromiseState, "writer._closedPromiseState is pending"), e._closedPromise_reject(t), e._closedPromise_resolve=void 0, e._closedPromise_reject=void 0, e._closedPromiseState="rejected" } function J(e, t) { de(void 0===e._closedPromise_resolve, "writer._closedPromise_resolve === undefined"), de(void 0===e._closedPromise_reject, "writer._closedPromise_reject === undefined"), de("pending"!==e._closedPromiseState, "writer._closedPromiseState is not pending"), e._closedPromise=Promise.reject(t), e._closedPromiseState="rejected" } function Q(e) { de(void 0!==e._closedPromise_resolve, "writer._closedPromise_resolve !== undefined"), de(void 0!==e._closedPromise_reject, "writer._closedPromise_reject !== undefined"), de("pending"===e._closedPromiseState, "writer._closedPromiseState is pending"), e._closedPromise_resolve(void 0), e._closedPromise_resolve=void 0, e._closedPromise_reject=void 0, e._closedPromiseState="resolved" } function K(e) { e._readyPromise=new Promise(function(t, r) { e._readyPromise_resolve=t, e._readyPromise_reject=r }), e._readyPromiseState="pending" } function Z(e, t) { e._readyPromise=Promise.reject(t), e._readyPromise_resolve=void 0, e._readyPromise_reject=void 0, e._readyPromiseState="rejected" } function $(e) { e._readyPromise=Promise.resolve(void 0), e._readyPromise_resolve=void 0, e._readyPromise_reject=void 0, e._readyPromiseState="fulfilled" } function ee(e, t) { de(void 0!==e._readyPromise_resolve, "writer._readyPromise_resolve !== undefined"), de(void 0!==e._readyPromise_reject, "writer._readyPromise_reject !== undefined"), e._readyPromise_reject(t), e._readyPromise_resolve=void 0, e._readyPromise_reject=void 0, e._readyPromiseState="rejected" } function te(e) { de(void 0===e._readyPromise_resolve, "writer._readyPromise_resolve === undefined"), de(void 0===e._readyPromise_reject, "writer._readyPromise_reject === undefined"), e._readyPromise=new Promise(function(t, r) { e._readyPromise_resolve=t, e._readyPromise_reject=r }), e._readyPromiseState="pending" } function re(e, t) { de(void 0===e._readyPromise_resolve, "writer._readyPromise_resolve === undefined"), de(void 0===e._readyPromise_reject, "writer._readyPromise_reject === undefined"), e._readyPromise=Promise.reject(t), e._readyPromiseState="rejected" } function ne(e) { de(void 0!==e._readyPromise_resolve, "writer._readyPromise_resolve !== undefined"), de(void 0!==e._readyPromise_reject, "writer._readyPromise_reject !== undefined"), e._readyPromise_resolve(void 0), e._readyPromise_resolve=void 0, e._readyPromise_reject=void 0, e._readyPromiseState="fulfilled" } var ie=function() { function e(e, t) { for(var r=0; r<t.length; r++) { var n=t[r]; n.enumerable=n.enumerable||!1, n.configurable=!0, "value" in n&&(n.writable=!0), Object.defineProperty(e, n.key, n) } } return function(t, r, n) { return r&&e(t.prototype, r), n&&e(t, n), t } }(), ae=r(0), oe=ae.InvokeOrNoop, se=ae.PromiseInvokeOrNoop, le=ae.ValidateAndNormalizeQueuingStrategy, ce=ae.typeIsObject, ue=r(1), de=ue.assert, he=ue.rethrowAssertionErrorRejection, fe=r(3), pe=fe.DequeueValue, me=fe.EnqueueValueWithSize, ge=fe.PeekQueueValue, ve=fe.ResetQueue, be=function() { function e() { var t=arguments.length>0&&void 0!==arguments[0]? arguments[0]:{}, r=arguments.length>1&&void 0!==arguments[1]? arguments[1]:{}, i=r.size, a=r.highWaterMark, o=void 0===a? 1:a; if(n(this, e), this._state="writable", this._storedError=void 0, this._writer=void 0, this._writableStreamController=void 0, this._writeRequests=[], this._inFlightWriteRequest=void 0, this._closeRequest=void 0, this._inFlightCloseRequest=void 0, this._pendingAbortRequest=void 0, this._backpressure=!1, void 0!==t.type) throw new RangeError("Invalid type is specified"); this._writableStreamController=new Ae(this, t, i, o), this._writableStreamController.__startSteps() } return ie(e, [{ key: "abort", value: function(e) { return !1===a(this)? Promise.reject(B("abort")):!0===o(this)? Promise.reject(new TypeError("Cannot abort a stream that already has a writer")):s(this, e) } }, { key: "getWriter", value: function() { if(!1===a(this)) throw B("getWriter"); return i(this) } }, { key: "locked", get: function() { if(!1===a(this)) throw B("locked"); return o(this) } }]), e }(); e.exports={ AcquireWritableStreamDefaultWriter: i, IsWritableStream: a, IsWritableStreamLocked: o, WritableStream: be, WritableStreamAbort: s, WritableStreamDefaultControllerError: q, WritableStreamDefaultWriterCloseWithErrorPropagation: x, WritableStreamDefaultWriterRelease: R, WritableStreamDefaultWriterWrite: E, WritableStreamCloseQueuedOrInFlight: g }; var ye=function() { function e(t) { if(n(this, e), !1===a(t)) throw new TypeError("WritableStreamDefaultWriter can only be constructed with a WritableStream instance"); if(!0===o(t)) throw new TypeError("This stream has already been locked for exclusive writing by another writer"); this._ownerWritableStream=t, t._writer=this; var r=t._state; if("writable"===r) !1===g(t)&&!0===t._backpressure? K(this):$(this), X(this); else if("erroring"===r) Z(this, t._storedError), this._readyPromise.catch(function() { }), X(this); else if("closed"===r) $(this), Y(this); else { de("errored"===r, "state must be errored"); var i=t._storedError; Z(this, i), this._readyPromise.catch(function() { }), H(this, i), this._closedPromise.catch(function() { }) } } return ie(e, [{ key: "abort", value: function(e) { return !1===S(this)? Promise.reject(z("abort")):void 0===this._ownerWritableStream? Promise.reject(G("abort")):w(this, e) } }, { key: "close", value: function() { if(!1===S(this)) return Promise.reject(z("close")); var e=this._ownerWritableStream; return void 0===e? Promise.reject(G("close")):!0===g(e)? Promise.reject(new TypeError("cannot close an already-closing stream")):P(this) } }, { key: "releaseLock", value: function() { if(!1===S(this)) throw z("releaseLock"); var e=this._ownerWritableStream; void 0!==e&&(de(void 0!==e._writer), R(this)) } }, { key: "write", value: function(e) { return !1===S(this)? Promise.reject(z("write")):void 0===this._ownerWritableStream? Promise.reject(G("write to")):E(this, e) } }, { key: "closed", get: function() { return !1===S(this)? Promise.reject(z("closed")):this._closedPromise } }, { key: "desiredSize", get: function() { if(!1===S(this)) throw z("desiredSize"); if(void 0===this._ownerWritableStream) throw G("desiredSize"); return C(this) } }, { key: "ready", get: function() { return !1===S(this)? Promise.reject(z("ready")):this._readyPromise } }]), e }(), Ae=function() { function e(t, r, i, o) { if(n(this, e), !1===a(t)) throw new TypeError("WritableStreamDefaultController can only be constructed with a WritableStream instance"); if(void 0!==t._writableStreamController) throw new TypeError("WritableStreamDefaultController instances can only be created by the WritableStream constructor"); this._controlledWritableStream=t, this._underlyingSink=r, this._queue=void 0, this._queueTotalSize=void 0, ve(this), this._started=!1; var s=le(i, o); this._strategySize=s.size, this._strategyHWM=s.highWaterMark, _(t, U(this)) } return ie(e, [{ key: "error", value: function(e) { if(!1===D(this)) throw new TypeError("WritableStreamDefaultController.prototype.error can only be used on a WritableStreamDefaultController"); "writable"===this._controlledWritableStream._state&&q(this, e) } }, { key: "__abortSteps", value: function(e) { return se(this._underlyingSink, "abort", [e]) } }, { key: "__errorSteps", value: function() { ve(this) } }, { key: "__startSteps", value: function() { var e=this, t=oe(this._underlyingSink, "start", [this]), r=this._controlledWritableStream; Promise.resolve(t).then(function() { de("writable"===r._state||"erroring"===r._state), e._started=!0, F(e) }, function(t) { de("writable"===r._state||"erroring"===r._state), e._started=!0, c(r, t) }).catch(he) } }]), e }() }, function(e, t, r) { var n=r(0), i=n.IsFiniteNonNegativeNumber, a=r(1), o=a.assert; t.DequeueValue=function(e) { o("_queue" in e&&"_queueTotalSize" in e, "Spec-level failure: DequeueValue should only be used on containers with [[queue]] and [[queueTotalSize]]."), o(e._queue.length>0, "Spec-level failure: should never dequeue from an empty queue."); var t=e._queue.shift(); return e._queueTotalSize-=t.size, e._queueTotalSize<0&&(e._queueTotalSize=0), t.value }, t.EnqueueValueWithSize=function(e, t, r) { if(o("_queue" in e&&"_queueTotalSize" in e, "Spec-level failure: EnqueueValueWithSize should only be used on containers with [[queue]] and [[queueTotalSize]]."), r=Number(r), !i(r)) throw new RangeError("Size must be a finite, non-NaN, non-negative number."); e._queue.push({ value: t, size: r }), e._queueTotalSize+=r }, t.PeekQueueValue=function(e) { return o("_queue" in e&&"_queueTotalSize" in e, "Spec-level failure: PeekQueueValue should only be used on containers with [[queue]] and [[queueTotalSize]]."), o(e._queue.length>0, "Spec-level failure: should never peek at an empty queue."), e._queue[0].value }, t.ResetQueue=function(e) { o("_queue" in e&&"_queueTotalSize" in e, "Spec-level failure: ResetQueue should only be used on containers with [[queue]] and [[queueTotalSize]]."), e._queue=[], e._queueTotalSize=0 } }, function(e, t, r) {
      function n(e, t) { if(!(e instanceof t)) throw new TypeError("Cannot call a class as a function") } function i(e) { return new tt(e) } function a(e) { return new et(e) } function o(e) { return !!Fe(e)&&!!Object.prototype.hasOwnProperty.call(e, "_readableStreamController") } function s(e) { return Me(!0===o(e), "IsReadableStreamDisturbed should only be used on known readable streams"), e._disturbed } function l(e) { return Me(!0===o(e), "IsReadableStreamLocked should only be used on known readable streams"), void 0!==e._reader } function c(e, t) { Me(!0===o(e)), Me("boolean"==typeof t); var r=a(e), n={ closedOrErrored: !1, canceled1: !1, canceled2: !1, reason1: void 0, reason2: void 0 }; n.promise=new Promise(function(e) { n._resolve=e }); var i=u(); i._reader=r, i._teeState=n, i._cloneForBranch2=t; var s=d(); s._stream=e, s._teeState=n; var l=h(); l._stream=e, l._teeState=n; var c=Object.create(Object.prototype); De(c, "pull", i), De(c, "cancel", s); var f=new $e(c), p=Object.create(Object.prototype); De(p, "pull", i), De(p, "cancel", l); var m=new $e(p); return i._branch1=f._readableStreamController, i._branch2=m._readableStreamController, r._closedPromise.catch(function(e) { !0!==n.closedOrErrored&&(F(i._branch1, e), F(i._branch2, e), n.closedOrErrored=!0) }), [f, m] } function u() { function e() { var t=e._reader, r=e._branch1, n=e._branch2, i=e._teeState; return E(t).then(function(e) { Me(Fe(e)); var t=e.value, a=e.done; if(Me("boolean"==typeof a), !0===a&&!1===i.closedOrErrored&&(!1===i.canceled1&&j(r), !1===i.canceled2&&j(n), i.closedOrErrored=!0), !0!==i.closedOrErrored) { var o=t, s=t; !1===i.canceled1&&D(r, o), !1===i.canceled2&&D(n, s) } }) } return e }
      function d() { function e(t) { var r=e._stream, n=e._teeState; if(n.canceled1=!0, n.reason1=t, !0===n.canceled2) { var i=je([n.reason1, n.reason2]), a=m(r, i); n._resolve(a) } return n.promise } return e } function h() { function e(t) { var r=e._stream, n=e._teeState; if(n.canceled2=!0, n.reason2=t, !0===n.canceled1) { var i=je([n.reason1, n.reason2]), a=m(r, i); n._resolve(a) } return n.promise } return e } function f(e) { return Me(!0===P(e._reader)), Me("readable"===e._state||"closed"===e._state), new Promise(function(t, r) { var n={ _resolve: t, _reject: r }; e._reader._readIntoRequests.push(n) }) } function p(e) { return Me(!0===x(e._reader)), Me("readable"===e._state), new Promise(function(t, r) { var n={ _resolve: t, _reject: r }; e._reader._readRequests.push(n) }) } function m(e, t) { return e._disturbed=!0, "closed"===e._state? Promise.resolve(void 0):"errored"===e._state? Promise.reject(e._storedError):(g(e), e._readableStreamController.__cancelSteps(t).then(function() { })) } function g(e) { Me("readable"===e._state), e._state="closed"; var t=e._reader; if(void 0!==t) { if(!0===x(t)) { for(var r=0; r<t._readRequests.length; r++) { (0, t._readRequests[r])(Te(void 0, !0)) } t._readRequests=[] } ve(t) } } function v(e, t) { Me(!0===o(e), "stream must be ReadableStream"), Me("readable"===e._state, "state must be readable"), e._state="errored", e._storedError=t; var r=e._reader; if(void 0!==r) { if(!0===x(r)) { for(var n=0; n<r._readRequests.length; n++) { r._readRequests[n]._reject(t) } r._readRequests=[] } else { Me(P(r), "reader must be ReadableStreamBYOBReader"); for(var i=0; i<r._readIntoRequests.length; i++) { r._readIntoRequests[i]._reject(t) } r._readIntoRequests=[] } me(r, t), r._closedPromise.catch(function() { }) } } function b(e, t, r) { var n=e._reader; Me(n._readIntoRequests.length>0), n._readIntoRequests.shift()._resolve(Te(t, r)) } function y(e, t, r) { var n=e._reader; Me(n._readRequests.length>0), n._readRequests.shift()._resolve(Te(t, r)) } function A(e) { return e._reader._readIntoRequests.length } function _(e) { return e._reader._readRequests.length } function S(e) { var t=e._reader; return void 0!==t&&!1!==P(t) } function w(e) { var t=e._reader; return void 0!==t&&!1!==x(t) } function P(e) { return !!Fe(e)&&!!Object.prototype.hasOwnProperty.call(e, "_readIntoRequests") } function x(e) { return !!Fe(e)&&!!Object.prototype.hasOwnProperty.call(e, "_readRequests") } function T(e, t) { e._ownerReadableStream=t, t._reader=e, "readable"===t._state? he(e):"closed"===t._state? pe(e):(Me("errored"===t._state, "state must be errored"), fe(e, t._storedError), e._closedPromise.catch(function() { })) } function k(e, t) { var r=e._ownerReadableStream; return Me(void 0!==r), m(r, t) } function C(e) { Me(void 0!==e._ownerReadableStream), Me(e._ownerReadableStream._reader===e), "readable"===e._ownerReadableStream._state? me(e, new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):ge(e, new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")), e._closedPromise.catch(function() { }), e._ownerReadableStream._reader=void 0, e._ownerReadableStream=void 0 } function R(e, t) { var r=e._ownerReadableStream; return Me(void 0!==r), r._disturbed=!0, "errored"===r._state? Promise.reject(r._storedError):K(r._readableStreamController, t) } function E(e) { var t=e._ownerReadableStream; return Me(void 0!==t), t._disturbed=!0, "closed"===t._state? Promise.resolve(Te(void 0, !0)):"errored"===t._state? Promise.reject(t._storedError):(Me("readable"===t._state), t._readableStreamController.__pullSteps()) } function L(e) { return !!Fe(e)&&!!Object.prototype.hasOwnProperty.call(e, "_underlyingSource") } function O(e) { if(!1!==I(e)) { if(!0===e._pulling) return void (e._pullAgain=!0); Me(!1===e._pullAgain), e._pulling=!0, Re(e._underlyingSource, "pull", [e]).then(function() { if(e._pulling=!1, !0===e._pullAgain) return e._pullAgain=!1, O(e) }, function(t) { N(e, t) }).catch(We) } } function I(e) { var t=e._controlledReadableStream; return "closed"!==t._state&&"errored"!==t._state&&(!0!==e._closeRequested&&(!1!==e._started&&(!0===l(t)&&_(t)>0||M(e)>0))) } function j(e) { var t=e._controlledReadableStream; Me(!1===e._closeRequested), Me("readable"===t._state), e._closeRequested=!0, 0===e._queue.length&&g(t) } function D(e, t) { var r=e._controlledReadableStream; if(Me(!1===e._closeRequested), Me("readable"===r._state), !0===l(r)&&_(r)>0) y(r, t, !1); else { var n=1; if(void 0!==e._strategySize) { var i=e._strategySize; try { n=i(t) } catch(t) { throw N(e, t), t } } try { Be(e, t, n) } catch(t) { throw N(e, t), t } } O(e) } function F(e, t) { var r=e._controlledReadableStream; Me("readable"===r._state), ze(e), v(r, t) } function N(e, t) { "readable"===e._controlledReadableStream._state&&F(e, t) } function M(e) { var t=e._controlledReadableStream, r=t._state; return "errored"===r? null:"closed"===r? 0:e._strategyHWM-e._queueTotalSize } function W(e) { return !!Fe(e)&&!!Object.prototype.hasOwnProperty.call(e, "_underlyingByteSource") } function U(e) { return !!Fe(e)&&!!Object.prototype.hasOwnProperty.call(e, "_associatedReadableByteStreamController") } function q(e) { if(!1!==re(e)) { if(!0===e._pulling) return void (e._pullAgain=!0); Me(!1===e._pullAgain), e._pulling=!0, Re(e._underlyingByteSource, "pull", [e]).then(function() { e._pulling=!1, !0===e._pullAgain&&(e._pullAgain=!1, q(e)) }, function(t) { "readable"===e._controlledReadableStream._state&&ae(e, t) }).catch(We) } } function B(e) { J(e), e._pendingPullIntos=[] } function z(e, t) { Me("errored"!==e._state, "state must not be errored"); var r=!1; "closed"===e._state&&(Me(0===t.bytesFilled), r=!0); var n=G(t); "default"===t.readerType? y(e, n, r):(Me("byob"===t.readerType), b(e, n, r)) } function G(e) { var t=e.bytesFilled, r=e.elementSize; return Me(t<=e.byteLength), Me(t%r==0), new e.ctor(e.buffer, e.byteOffset, t/r) } function X(e, t, r, n) { e._queue.push({ buffer: t, byteOffset: r, byteLength: n }), e._queueTotalSize+=n } function H(e, t) { var r=t.elementSize, n=t.bytesFilled-t.bytesFilled%r, i=Math.min(e._queueTotalSize, t.byteLength-t.bytesFilled), a=t.bytesFilled+i, o=a-a%r, s=i, l=!1; o>n&&(s=o-t.bytesFilled, l=!0); for(var c=e._queue; s>0;) { var u=c[0], d=Math.min(s, u.byteLength), h=t.byteOffset+t.bytesFilled; xe(t.buffer, h, u.buffer, u.byteOffset, d), u.byteLength===d? c.shift():(u.byteOffset+=d, u.byteLength-=d), e._queueTotalSize-=d, Y(e, d, t), s-=d } return !1===l&&(Me(0===e._queueTotalSize, "queue must be empty"), Me(t.bytesFilled>0), Me(t.bytesFilled<t.elementSize)), l } function Y(e, t, r) { Me(0===e._pendingPullIntos.length||e._pendingPullIntos[0]===r), J(e), r.bytesFilled+=t } function V(e) { Me("readable"===e._controlledReadableStream._state), 0===e._queueTotalSize&&!0===e._closeRequested? g(e._controlledReadableStream):q(e) } function J(e) { void 0!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0, e._byobRequest._view=void 0, e._byobRequest=void 0) } function Q(e) { for(Me(!1===e._closeRequested); e._pendingPullIntos.length>0;) { if(0===e._queueTotalSize) return; var t=e._pendingPullIntos[0]; !0===H(e, t)&&(te(e), z(e._controlledReadableStream, t)) } } function K(e, t) { var r=e._controlledReadableStream, n=1; t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT); var i=t.constructor, a={ buffer: t.buffer, byteOffset: t.byteOffset, byteLength: t.byteLength, bytesFilled: 0, elementSize: n, ctor: i, readerType: "byob" }; if(e._pendingPullIntos.length>0) return a.buffer=Ee(a.buffer), e._pendingPullIntos.push(a), f(r); if("closed"===r._state) { var o=new t.constructor(a.buffer, a.byteOffset, 0); return Promise.resolve(Te(o, !0)) } if(e._queueTotalSize>0) { if(!0===H(e, a)) { var s=G(a); return V(e), Promise.resolve(Te(s, !1)) } if(!0===e._closeRequested) { var l=new TypeError("Insufficient bytes to fill elements in the given buffer"); return ae(e, l), Promise.reject(l) } } a.buffer=Ee(a.buffer), e._pendingPullIntos.push(a); var c=f(r); return q(e), c } function Z(e, t) { t.buffer=Ee(t.buffer), Me(0===t.bytesFilled, "bytesFilled must be 0"); var r=e._controlledReadableStream; if(!0===S(r)) for(; A(r)>0;) { var n=te(e); z(r, n) } } function $(e, t, r) { if(r.bytesFilled+t>r.byteLength) throw new RangeError("bytesWritten out of range"); if(Y(e, t, r), !(r.bytesFilled<r.elementSize)) { te(e); var n=r.bytesFilled%r.elementSize; if(n>0) { var i=r.byteOffset+r.bytesFilled, a=r.buffer.slice(i-n, i); X(e, a, 0, a.byteLength) } r.buffer=Ee(r.buffer), r.bytesFilled-=n, z(e._controlledReadableStream, r), Q(e) } } function ee(e, t) { var r=e._pendingPullIntos[0], n=e._controlledReadableStream; if("closed"===n._state) { if(0!==t) throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream"); Z(e, r) } else Me("readable"===n._state), $(e, t, r) } function te(e) { var t=e._pendingPullIntos.shift(); return J(e), t } function re(e) { var t=e._controlledReadableStream; return "readable"===t._state&&(!0!==e._closeRequested&&(!1!==e._started&&(!0===w(t)&&_(t)>0||(!0===S(t)&&A(t)>0||oe(e)>0)))) } function ne(e) { var t=e._controlledReadableStream; if(Me(!1===e._closeRequested), Me("readable"===t._state), e._queueTotalSize>0) return void (e._closeRequested=!0); if(e._pendingPullIntos.length>0) { if(e._pendingPullIntos[0].bytesFilled>0) { var r=new TypeError("Insufficient bytes to fill elements in the given buffer"); throw ae(e, r), r } } g(t) } function ie(e, t) { var r=e._controlledReadableStream; Me(!1===e._closeRequested), Me("readable"===r._state); var n=t.buffer, i=t.byteOffset, a=t.byteLength, o=Ee(n); if(!0===w(r)) if(0===_(r)) X(e, o, i, a); else { Me(0===e._queue.length); var s=new Uint8Array(o, i, a); y(r, s, !1) } else !0===S(r)? (X(e, o, i, a), Q(e)):(Me(!1===l(r), "stream must not be locked"), X(e, o, i, a)) } function ae(e, t) { var r=e._controlledReadableStream; Me("readable"===r._state), B(e), ze(e), v(r, t) } function oe(e) { var t=e._controlledReadableStream, r=t._state; return "errored"===r? null:"closed"===r? 0:e._strategyHWM-e._queueTotalSize } function se(e, t) { if(t=Number(t), !1===ke(t)) throw new RangeError("bytesWritten must be a finite"); Me(e._pendingPullIntos.length>0), ee(e, t) } function le(e, t) { Me(e._pendingPullIntos.length>0); var r=e._pendingPullIntos[0]; if(r.byteOffset+r.bytesFilled!==t.byteOffset) throw new RangeError("The region specified by view does not match byobRequest"); if(r.byteLength!==t.byteLength) throw new RangeError("The buffer of view has different capacity than byobRequest"); r.buffer=t.buffer, ee(e, t.byteLength) } function ce(e) { return new TypeError("ReadableStream.prototype."+e+" can only be used on a ReadableStream") } function ue(e) { return new TypeError("Cannot "+e+" a stream using a released reader") } function de(e) { return new TypeError("ReadableStreamDefaultReader.prototype."+e+" can only be used on a ReadableStreamDefaultReader") } function he(e) { e._closedPromise=new Promise(function(t, r) { e._closedPromise_resolve=t, e._closedPromise_reject=r }) } function fe(e, t) { e._closedPromise=Promise.reject(t), e._closedPromise_resolve=void 0, e._closedPromise_reject=void 0 } function pe(e) { e._closedPromise=Promise.resolve(void 0), e._closedPromise_resolve=void 0, e._closedPromise_reject=void 0 } function me(e, t) { Me(void 0!==e._closedPromise_resolve), Me(void 0!==e._closedPromise_reject), e._closedPromise_reject(t), e._closedPromise_resolve=void 0, e._closedPromise_reject=void 0 } function ge(e, t) { Me(void 0===e._closedPromise_resolve), Me(void 0===e._closedPromise_reject), e._closedPromise=Promise.reject(t) } function ve(e) { Me(void 0!==e._closedPromise_resolve), Me(void 0!==e._closedPromise_reject), e._closedPromise_resolve(void 0), e._closedPromise_resolve=void 0, e._closedPromise_reject=void 0 } function be(e) { return new TypeError("ReadableStreamBYOBReader.prototype."+e+" can only be used on a ReadableStreamBYOBReader") } function ye(e) { return new TypeError("ReadableStreamDefaultController.prototype."+e+" can only be used on a ReadableStreamDefaultController") } function Ae(e) { return new TypeError("ReadableStreamBYOBRequest.prototype."+e+" can only be used on a ReadableStreamBYOBRequest") } function _e(e) { return new TypeError("ReadableByteStreamController.prototype."+e+" can only be used on a ReadableByteStreamController") } function Se(e) { try { Promise.prototype.then.call(e, void 0, function() { }) } catch(e) { } } var we=function() { function e(e, t) { for(var r=0; r<t.length; r++) { var n=t[r]; n.enumerable=n.enumerable||!1, n.configurable=!0, "value" in n&&(n.writable=!0), Object.defineProperty(e, n.key, n) } } return function(t, r, n) { return r&&e(t.prototype, r), n&&e(t, n), t } }(), Pe=r(0), xe=Pe.ArrayBufferCopy, Te=Pe.CreateIterResultObject, ke=Pe.IsFiniteNonNegativeNumber, Ce=Pe.InvokeOrNoop, Re=Pe.PromiseInvokeOrNoop, Ee=Pe.TransferArrayBuffer, Le=Pe.ValidateAndNormalizeQueuingStrategy, Oe=Pe.ValidateAndNormalizeHighWaterMark, Ie=r(0), je=Ie.createArrayFromList, De=Ie.createDataProperty, Fe=Ie.typeIsObject, Ne=r(1), Me=Ne.assert, We=Ne.rethrowAssertionErrorRejection, Ue=r(3), qe=Ue.DequeueValue, Be=Ue.EnqueueValueWithSize, ze=Ue.ResetQueue, Ge=r(2), Xe=Ge.AcquireWritableStreamDefaultWriter, He=Ge.IsWritableStream, Ye=Ge.IsWritableStreamLocked, Ve=Ge.WritableStreamAbort, Je=Ge.WritableStreamDefaultWriterCloseWithErrorPropagation, Qe=Ge.WritableStreamDefaultWriterRelease, Ke=Ge.WritableStreamDefaultWriterWrite, Ze=Ge.WritableStreamCloseQueuedOrInFlight, $e=function() { function e() { var t=arguments.length>0&&void 0!==arguments[0]? arguments[0]:{}, r=arguments.length>1&&void 0!==arguments[1]? arguments[1]:{}, i=r.size, a=r.highWaterMark; n(this, e), this._state="readable", this._reader=void 0, this._storedError=void 0, this._disturbed=!1, this._readableStreamController=void 0; var o=t.type; if("bytes"===String(o)) void 0===a&&(a=0), this._readableStreamController=new it(this, t, a); else { if(void 0!==o) throw new RangeError("Invalid type is specified"); void 0===a&&(a=1), this._readableStreamController=new rt(this, t, i, a) } } return we(e, [{ key: "cancel", value: function(e) { return !1===o(this)? Promise.reject(ce("cancel")):!0===l(this)? Promise.reject(new TypeError("Cannot cancel a stream that already has a reader")):m(this, e) } }, { key: "getReader", value: function() { var e=arguments.length>0&&void 0!==arguments[0]? arguments[0]:{}, t=e.mode; if(!1===o(this)) throw ce("getReader"); if(void 0===t) return a(this); if("byob"===(t=String(t))) return i(this); throw new RangeError("Invalid mode is specified") } }, { key: "pipeThrough", value: function(e, t) { var r=e.writable, n=e.readable; return Se(this.pipeTo(r, t)), n } }, { key: "pipeTo", value: function(e) { var t=this, r=arguments.length>1&&void 0!==arguments[1]? arguments[1]:{}, n=r.preventClose, i=r.preventAbort, s=r.preventCancel; if(!1===o(this)) return Promise.reject(ce("pipeTo")); if(!1===He(e)) return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream")); if(n=Boolean(n), i=Boolean(i), s=Boolean(s), !0===l(this)) return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")); if(!0===Ye(e)) return Promise.reject(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")); var c=a(this), u=Xe(e), d=!1, h=Promise.resolve(); return new Promise(function(r, a) { function o() { return h=Promise.resolve(), !0===d? Promise.resolve():u._readyPromise.then(function() { return E(c).then(function(e) { var t=e.value; !0!==e.done&&(h=Ke(u, t).catch(function() { })) }) }).then(o) } function l() { var e=h; return h.then(function() { return e!==h? l():void 0 }) } function f(e, t, r) { "errored"===e._state? r(e._storedError):t.catch(r).catch(We) } function p(t, r, n) { function i() { t().then(function() { return v(r, n) }, function(e) { return v(!0, e) }).catch(We) } !0!==d&&(d=!0, "writable"===e._state&&!1===Ze(e)? l().then(i):i()) } function g(t, r) { !0!==d&&(d=!0, "writable"===e._state&&!1===Ze(e)? l().then(function() { return v(t, r) }).catch(We):v(t, r)) } function v(e, t) { Qe(u), C(c), e? a(t):r(void 0) } if(f(t, c._closedPromise, function(t) { !1===i? p(function() { return Ve(e, t) }, !0, t):g(!0, t) }), f(e, u._closedPromise, function(e) { !1===s? p(function() { return m(t, e) }, !0, e):g(!0, e) }), function(e, t, r) { "closed"===e._state? r():t.then(r).catch(We) }(t, c._closedPromise, function() { !1===n? p(function() { return Je(u) }):g() }), !0===Ze(e)||"closed"===e._state) { var b=new TypeError("the destination writable stream closed before all data could be piped to it"); !1===s? p(function() { return m(t, b) }, !0, b):g(!0, b) } o().catch(function(e) { h=Promise.resolve(), We(e) }) }) } }, { key: "tee", value: function() { if(!1===o(this)) throw ce("tee"); var e=c(this, !1); return je(e) } }, { key: "locked", get: function() { if(!1===o(this)) throw ce("locked"); return l(this) } }]), e }(); e.exports={ ReadableStream: $e, IsReadableStreamDisturbed: s, ReadableStreamDefaultControllerClose: j, ReadableStreamDefaultControllerEnqueue: D, ReadableStreamDefaultControllerError: F, ReadableStreamDefaultControllerGetDesiredSize: M }; var et=function() { function e(t) { if(n(this, e), !1===o(t)) throw new TypeError("ReadableStreamDefaultReader can only be constructed with a ReadableStream instance"); if(!0===l(t)) throw new TypeError("This stream has already been locked for exclusive reading by another reader"); T(this, t), this._readRequests=[] } return we(e, [{ key: "cancel", value: function(e) { return !1===x(this)? Promise.reject(de("cancel")):void 0===this._ownerReadableStream? Promise.reject(ue("cancel")):k(this, e) } }, { key: "read", value: function() { return !1===x(this)? Promise.reject(de("read")):void 0===this._ownerReadableStream? Promise.reject(ue("read from")):E(this) } }, { key: "releaseLock", value: function() { if(!1===x(this)) throw de("releaseLock"); if(void 0!==this._ownerReadableStream) { if(this._readRequests.length>0) throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled"); C(this) } } }, { key: "closed", get: function() { return !1===x(this)? Promise.reject(de("closed")):this._closedPromise } }]), e }(), tt=function() { function e(t) { if(n(this, e), !o(t)) throw new TypeError("ReadableStreamBYOBReader can only be constructed with a ReadableStream instance given a byte source"); if(!1===W(t._readableStreamController)) throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source"); if(l(t)) throw new TypeError("This stream has already been locked for exclusive reading by another reader"); T(this, t), this._readIntoRequests=[] } return we(e, [{ key: "cancel", value: function(e) { return P(this)? void 0===this._ownerReadableStream? Promise.reject(ue("cancel")):k(this, e):Promise.reject(be("cancel")) } }, { key: "read", value: function(e) { return P(this)? void 0===this._ownerReadableStream? Promise.reject(ue("read from")):ArrayBuffer.isView(e)? 0===e.byteLength? Promise.reject(new TypeError("view must have non-zero byteLength")):R(this, e):Promise.reject(new TypeError("view must be an array buffer view")):Promise.reject(be("read")) } }, { key: "releaseLock", value: function() { if(!P(this)) throw be("releaseLock"); if(void 0!==this._ownerReadableStream) { if(this._readIntoRequests.length>0) throw new TypeError("Tried to release a reader lock when that reader has pending read() calls un-settled"); C(this) } } }, { key: "closed", get: function() { return P(this)? this._closedPromise:Promise.reject(be("closed")) } }]), e }(), rt=function() { function e(t, r, i, a) { if(n(this, e), !1===o(t)) throw new TypeError("ReadableStreamDefaultController can only be constructed with a ReadableStream instance"); if(void 0!==t._readableStreamController) throw new TypeError("ReadableStreamDefaultController instances can only be created by the ReadableStream constructor"); this._controlledReadableStream=t, this._underlyingSource=r, this._queue=void 0, this._queueTotalSize=void 0, ze(this), this._started=!1, this._closeRequested=!1, this._pullAgain=!1, this._pulling=!1; var s=Le(i, a); this._strategySize=s.size, this._strategyHWM=s.highWaterMark; var l=this, c=Ce(r, "start", [this]); Promise.resolve(c).then(function() { l._started=!0, Me(!1===l._pulling), Me(!1===l._pullAgain), O(l) }, function(e) { N(l, e) }).catch(We) } return we(e, [{ key: "close", value: function() { if(!1===L(this)) throw ye("close"); if(!0===this._closeRequested) throw new TypeError("The stream has already been closed; do not close it again!"); var e=this._controlledReadableStream._state; if("readable"!==e) throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed"); j(this) } }, { key: "enqueue", value: function(e) { if(!1===L(this)) throw ye("enqueue"); if(!0===this._closeRequested) throw new TypeError("stream is closed or draining"); var t=this._controlledReadableStream._state; if("readable"!==t) throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to"); return D(this, e) } }, { key: "error", value: function(e) { if(!1===L(this)) throw ye("error"); var t=this._controlledReadableStream; if("readable"!==t._state) throw new TypeError("The stream is "+t._state+" and so cannot be errored"); F(this, e) } }, { key: "__cancelSteps", value: function(e) { return ze(this), Re(this._underlyingSource, "cancel", [e]) } }, { key: "__pullSteps", value: function() { var e=this._controlledReadableStream; if(this._queue.length>0) { var t=qe(this); return !0===this._closeRequested&&0===this._queue.length? g(e):O(this), Promise.resolve(Te(t, !1)) } var r=p(e); return O(this), r } }, { key: "desiredSize", get: function() { if(!1===L(this)) throw ye("desiredSize"); return M(this) } }]), e }(), nt=function() { function e(t, r) { n(this, e), this._associatedReadableByteStreamController=t, this._view=r } return we(e, [{ key: "respond", value: function(e) { if(!1===U(this)) throw Ae("respond"); if(void 0===this._associatedReadableByteStreamController) throw new TypeError("This BYOB request has been invalidated"); se(this._associatedReadableByteStreamController, e) } }, { key: "respondWithNewView", value: function(e) { if(!1===U(this)) throw Ae("respond"); if(void 0===this._associatedReadableByteStreamController) throw new TypeError("This BYOB request has been invalidated"); if(!ArrayBuffer.isView(e)) throw new TypeError("You can only respond with array buffer views"); le(this._associatedReadableByteStreamController, e) } }, { key: "view", get: function() { return this._view } }]), e }(), it=function() { function e(t, r, i) { if(n(this, e), !1===o(t)) throw new TypeError("ReadableByteStreamController can only be constructed with a ReadableStream instance given a byte source"); if(void 0!==t._readableStreamController) throw new TypeError("ReadableByteStreamController instances can only be created by the ReadableStream constructor given a byte source"); this._controlledReadableStream=t, this._underlyingByteSource=r, this._pullAgain=!1, this._pulling=!1, B(this), this._queue=this._queueTotalSize=void 0, ze(this), this._closeRequested=!1, this._started=!1, this._strategyHWM=Oe(i); var a=r.autoAllocateChunkSize; if(void 0!==a&&(!1===Number.isInteger(a)||a<=0)) throw new RangeError("autoAllocateChunkSize must be a positive integer"); this._autoAllocateChunkSize=a, this._pendingPullIntos=[]; var s=this, l=Ce(r, "start", [this]); Promise.resolve(l).then(function() { s._started=!0, Me(!1===s._pulling), Me(!1===s._pullAgain), q(s) }, function(e) { "readable"===t._state&&ae(s, e) }).catch(We) } return we(e, [{ key: "close", value: function() { if(!1===W(this)) throw _e("close"); if(!0===this._closeRequested) throw new TypeError("The stream has already been closed; do not close it again!"); var e=this._controlledReadableStream._state; if("readable"!==e) throw new TypeError("The stream (in "+e+" state) is not in the readable state and cannot be closed"); ne(this) } }, { key: "enqueue", value: function(e) { if(!1===W(this)) throw _e("enqueue"); if(!0===this._closeRequested) throw new TypeError("stream is closed or draining"); var t=this._controlledReadableStream._state; if("readable"!==t) throw new TypeError("The stream (in "+t+" state) is not in the readable state and cannot be enqueued to"); if(!ArrayBuffer.isView(e)) throw new TypeError("You can only enqueue array buffer views when using a ReadableByteStreamController"); ie(this, e) } }, { key: "error", value: function(e) { if(!1===W(this)) throw _e("error"); var t=this._controlledReadableStream; if("readable"!==t._state) throw new TypeError("The stream is "+t._state+" and so cannot be errored"); ae(this, e) } }, { key: "__cancelSteps", value: function(e) { if(this._pendingPullIntos.length>0) { this._pendingPullIntos[0].bytesFilled=0 } return ze(this), Re(this._underlyingByteSource, "cancel", [e]) } }, { key: "__pullSteps", value: function() { var e=this._controlledReadableStream; if(Me(!0===w(e)), this._queueTotalSize>0) { Me(0===_(e)); var t=this._queue.shift(); this._queueTotalSize-=t.byteLength, V(this); var r=void 0; try { r=new Uint8Array(t.buffer, t.byteOffset, t.byteLength) } catch(e) { return Promise.reject(e) } return Promise.resolve(Te(r, !1)) } var n=this._autoAllocateChunkSize; if(void 0!==n) { var i=void 0; try { i=new ArrayBuffer(n) } catch(e) { return Promise.reject(e) } var a={ buffer: i, byteOffset: 0, byteLength: n, bytesFilled: 0, elementSize: 1, ctor: Uint8Array, readerType: "default" }; this._pendingPullIntos.push(a) } var o=p(e); return q(this), o } }, { key: "byobRequest", get: function() { if(!1===W(this)) throw _e("byobRequest"); if(void 0===this._byobRequest&&this._pendingPullIntos.length>0) { var e=this._pendingPullIntos[0], t=new Uint8Array(e.buffer, e.byteOffset+e.bytesFilled, e.byteLength-e.bytesFilled); this._byobRequest=new nt(this, t) } return this._byobRequest } }, { key: "desiredSize", get: function() { if(!1===W(this)) throw _e("desiredSize"); return oe(this) } }]), e }()
    }, function(e, t, r) { var n=r(6), i=r(4), a=r(2); t.TransformStream=n.TransformStream, t.ReadableStream=i.ReadableStream, t.IsReadableStreamDisturbed=i.IsReadableStreamDisturbed, t.ReadableStreamDefaultControllerClose=i.ReadableStreamDefaultControllerClose, t.ReadableStreamDefaultControllerEnqueue=i.ReadableStreamDefaultControllerEnqueue, t.ReadableStreamDefaultControllerError=i.ReadableStreamDefaultControllerError, t.ReadableStreamDefaultControllerGetDesiredSize=i.ReadableStreamDefaultControllerGetDesiredSize, t.AcquireWritableStreamDefaultWriter=a.AcquireWritableStreamDefaultWriter, t.IsWritableStream=a.IsWritableStream, t.IsWritableStreamLocked=a.IsWritableStreamLocked, t.WritableStream=a.WritableStream, t.WritableStreamAbort=a.WritableStreamAbort, t.WritableStreamDefaultControllerError=a.WritableStreamDefaultControllerError, t.WritableStreamDefaultWriterCloseWithErrorPropagation=a.WritableStreamDefaultWriterCloseWithErrorPropagation, t.WritableStreamDefaultWriterRelease=a.WritableStreamDefaultWriterRelease, t.WritableStreamDefaultWriterWrite=a.WritableStreamDefaultWriterWrite }, function(e, t, r) {
      function n(e, t) { if(!(e instanceof t)) throw new TypeError("Cannot call a class as a function") } function i(e) { if(!0===e._errored) throw new TypeError("TransformStream is already errored"); if(!0===e._readableClosed) throw new TypeError("Readable side is already closed"); s(e) } function a(e, t) { if(!0===e._errored) throw new TypeError("TransformStream is already errored"); if(!0===e._readableClosed) throw new TypeError("Readable side is already closed"); var r=e._readableController; try { R(r, t) } catch(t) { throw e._readableClosed=!0, l(e, t), e._storedError } !0==L(r)<=0&&!1===e._backpressure&&d(e, !0) } function o(e, t) { if(!0===e._errored) throw new TypeError("TransformStream is already errored"); c(e, t) } function s(e) { A(!1===e._errored), A(!1===e._readableClosed); try { C(e._readableController) } catch(e) { A(!1) } e._readableClosed=!0 } function l(e, t) { !1===e._errored&&c(e, t) } function c(e, t) { A(!1===e._errored), e._errored=!0, e._storedError=t, !1===e._writableDone&&j(e._writableController, t), !1===e._readableClosed&&E(e._readableController, t) } function u(e) { return A(void 0!==e._backpressureChangePromise, "_backpressureChangePromise should have been initialized"), !1===e._backpressure? Promise.resolve():(A(!0===e._backpressure, "_backpressure should have been initialized"), e._backpressureChangePromise) } function d(e, t) { A(e._backpressure!==t, "TransformStreamSetBackpressure() should be called only when backpressure is changed"), void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(t), e._backpressureChangePromise=new Promise(function(t) { e._backpressureChangePromise_resolve=t }), e._backpressureChangePromise.then(function(e) { A(e!==t, "_backpressureChangePromise should be fulfilled only when backpressure is changed") }), e._backpressure=t } function h(e, t) { return a(t._controlledTransformStream, e), Promise.resolve() } function f(e, t) { A(!1===e._errored), A(!1===e._transforming), A(!1===e._backpressure), e._transforming=!0; var r=e._transformer, n=e._transformStreamController; return w(r, "transform", [t, n], h, [t, n]).then(function() { return e._transforming=!1, u(e) }, function(t) { return l(e, t), Promise.reject(t) }) } function p(e) { return !!x(e)&&!!Object.prototype.hasOwnProperty.call(e, "_controlledTransformStream") } function m(e) { return !!x(e)&&!!Object.prototype.hasOwnProperty.call(e, "_transformStreamController") } function g(e) { return new TypeError("TransformStreamDefaultController.prototype."+e+" can only be used on a TransformStreamDefaultController") } function v(e) { return new TypeError("TransformStream.prototype."+e+" can only be used on a TransformStream") } var b=function() { function e(e, t) { for(var r=0; r<t.length; r++) { var n=t[r]; n.enumerable=n.enumerable||!1, n.configurable=!0, "value" in n&&(n.writable=!0), Object.defineProperty(e, n.key, n) } } return function(t, r, n) { return r&&e(t.prototype, r), n&&e(t, n), t } }(), y=r(1), A=y.assert, _=r(0), S=_.InvokeOrNoop, w=_.PromiseInvokeOrPerformFallback, P=_.PromiseInvokeOrNoop, x=_.typeIsObject, T=r(4), k=T.ReadableStream, C=T.ReadableStreamDefaultControllerClose, R=T.ReadableStreamDefaultControllerEnqueue, E=T.ReadableStreamDefaultControllerError, L=T.ReadableStreamDefaultControllerGetDesiredSize, O=r(2), I=O.WritableStream, j=O.WritableStreamDefaultControllerError, D=function() { function e(t, r) { n(this, e), this._transformStream=t, this._startPromise=r } return b(e, [{ key: "start", value: function(e) { var t=this._transformStream; return t._writableController=e, this._startPromise.then(function() { return u(t) }) } }, { key: "write", value: function(e) { return f(this._transformStream, e) } }, { key: "abort", value: function() { var e=this._transformStream; e._writableDone=!0, c(e, new TypeError("Writable side aborted")) } }, { key: "close", value: function() { var e=this._transformStream; return A(!1===e._transforming), e._writableDone=!0, P(e._transformer, "flush", [e._transformStreamController]).then(function() { return !0===e._errored? Promise.reject(e._storedError):(!1===e._readableClosed&&s(e), Promise.resolve()) }).catch(function(t) { return l(e, t), Promise.reject(e._storedError) }) } }]), e }(), F=function() { function e(t, r) { n(this, e), this._transformStream=t, this._startPromise=r } return b(e, [{ key: "start", value: function(e) { var t=this._transformStream; return t._readableController=e, this._startPromise.then(function() { return A(void 0!==t._backpressureChangePromise, "_backpressureChangePromise should have been initialized"), !0===t._backpressure? Promise.resolve():(A(!1===t._backpressure, "_backpressure should have been initialized"), t._backpressureChangePromise) }) } }, { key: "pull", value: function() { var e=this._transformStream; return A(!0===e._backpressure, "pull() should be never called while _backpressure is false"), A(void 0!==e._backpressureChangePromise, "_backpressureChangePromise should have been initialized"), d(e, !1), e._backpressureChangePromise } }, { key: "cancel", value: function() { var e=this._transformStream; e._readableClosed=!0, c(e, new TypeError("Readable side canceled")) } }]), e }(), N=function() { function e(t) { if(n(this, e), !1===m(t)) throw new TypeError("TransformStreamDefaultController can only be constructed with a TransformStream instance"); if(void 0!==t._transformStreamController) throw new TypeError("TransformStreamDefaultController instances can only be created by the TransformStream constructor"); this._controlledTransformStream=t } return b(e, [{ key: "enqueue", value: function(e) { if(!1===p(this)) throw g("enqueue"); a(this._controlledTransformStream, e) } }, { key: "close", value: function() { if(!1===p(this)) throw g("close"); i(this._controlledTransformStream) } }, { key: "error", value: function(e) { if(!1===p(this)) throw g("error"); o(this._controlledTransformStream, e) } }, { key: "desiredSize", get: function() { if(!1===p(this)) throw g("desiredSize"); var e=this._controlledTransformStream, t=e._readableController; return L(t) } }]), e }(), M=function() {
        function e() {
          var t=arguments.length>0&&void 0!==arguments[0]? arguments[0]:{}; n(this, e), this._transformer=t; var r=t.readableStrategy, i=t.writableStrategy; this._transforming=!1, this._errored=!1, this._storedError=void 0, this._writableController=void 0, this._readableController=void 0, this._transformStreamController=void 0, this._writableDone=!1, this._readableClosed=!1, this._backpressure=void 0, this._backpressureChangePromise=void 0, this._backpressureChangePromise_resolve=void 0, this._transformStreamController=new N(this); var a=void 0, o=new Promise(function(e) { a=e }), s=new F(this, o); this._readable=new k(s, r); var l=new D(this, o); this._writable=new I(l, i),
            A(void 0!==this._writableController), A(void 0!==this._readableController), d(this, L(this._readableController)<=0); var c=this, u=S(t, "start", [c._transformStreamController]); a(u), o.catch(function(e) { !1===c._errored&&(c._errored=!0, c._storedError=e) })
        } return b(e, [{ key: "readable", get: function() { if(!1===m(this)) throw v("readable"); return this._readable } }, { key: "writable", get: function() { if(!1===m(this)) throw v("writable"); return this._writable } }]), e
      }(); e.exports={ TransformStream: M }
    }, function(e, t, r) { e.exports=r(5) }]))
  }, function(e, t, r) { "use strict"; function n(e) { e.mozCurrentTransform||(e._originalSave=e.save, e._originalRestore=e.restore, e._originalRotate=e.rotate, e._originalScale=e.scale, e._originalTranslate=e.translate, e._originalTransform=e.transform, e._originalSetTransform=e.setTransform, e._transformMatrix=e._transformMatrix||[1, 0, 0, 1, 0, 0], e._transformStack=[], Object.defineProperty(e, "mozCurrentTransform", { get: function() { return this._transformMatrix } }), Object.defineProperty(e, "mozCurrentTransformInverse", { get: function() { var e=this._transformMatrix, t=e[0], r=e[1], n=e[2], i=e[3], a=e[4], o=e[5], s=t*i-r*n, l=r*n-t*i; return [i/s, r/l, n/l, t/s, (i*a-n*o)/l, (r*a-t*o)/s] } }), e.save=function() { var e=this._transformMatrix; this._transformStack.push(e), this._transformMatrix=e.slice(0, 6), this._originalSave() }, e.restore=function() { var e=this._transformStack.pop(); e&&(this._transformMatrix=e, this._originalRestore()) }, e.translate=function(e, t) { var r=this._transformMatrix; r[4]=r[0]*e+r[2]*t+r[4], r[5]=r[1]*e+r[3]*t+r[5], this._originalTranslate(e, t) }, e.scale=function(e, t) { var r=this._transformMatrix; r[0]=r[0]*e, r[1]=r[1]*e, r[2]=r[2]*t, r[3]=r[3]*t, this._originalScale(e, t) }, e.transform=function(t, r, n, i, a, o) { var s=this._transformMatrix; this._transformMatrix=[s[0]*t+s[2]*r, s[1]*t+s[3]*r, s[0]*n+s[2]*i, s[1]*n+s[3]*i, s[0]*a+s[2]*o+s[4], s[1]*a+s[3]*o+s[5]], e._originalTransform(t, r, n, i, a, o) }, e.setTransform=function(t, r, n, i, a, o) { this._transformMatrix=[t, r, n, i, a, o], e._originalSetTransform(t, r, n, i, a, o) }, e.rotate=function(e) { var t=Math.cos(e), r=Math.sin(e), n=this._transformMatrix; this._transformMatrix=[n[0]*t+n[2]*r, n[1]*t+n[3]*r, n[0]*-r+n[2]*t, n[1]*-r+n[3]*t, n[4], n[5]], this._originalRotate(e) }) } function i(e) { var t, r, n, i, a=e.width, o=e.height, s=a+1, l=new Uint8Array(s*(o+1)), c=new Uint8Array([0, 2, 4, 0, 1, 0, 5, 4, 8, 10, 0, 8, 0, 2, 1, 0]), u=a+7&-8, d=e.data, h=new Uint8Array(u*o), f=0; for(t=0, i=d.length; t<i; t++)for(var p=128, m=d[t]; p>0;)h[f++]=m&p? 0:255, p>>=1; var g=0; for(f=0, 0!==h[f]&&(l[0]=1, ++g), r=1; r<a; r++)h[f]!==h[f+1]&&(l[r]=h[f]? 2:1, ++g), f++; for(0!==h[f]&&(l[r]=2, ++g), t=1; t<o; t++) { f=t*u, n=t*s, h[f-u]!==h[f]&&(l[n]=h[f]? 1:8, ++g); var v=(h[f]? 4:0)+(h[f-u]? 8:0); for(r=1; r<a; r++)v=(v>>2)+(h[f+1]? 4:0)+(h[f-u+1]? 8:0), c[v]&&(l[n+r]=c[v], ++g), f++; if(h[f-u]!==h[f]&&(l[n+r]=h[f]? 2:4, ++g), g>1e3) return null } for(f=u*(o-1), n=t*s, 0!==h[f]&&(l[n]=8, ++g), r=1; r<a; r++)h[f]!==h[f+1]&&(l[n+r]=h[f]? 4:8, ++g), f++; if(0!==h[f]&&(l[n+r]=4, ++g), g>1e3) return null; var b=new Int32Array([0, s, -1, 0, -s, 0, 0, 0, 1]), y=[]; for(t=0; g&&t<=o; t++) { for(var A=t*s, _=A+a; A<_&&!l[A];)A++; if(A!==_) { var S, w=[A%s, t], P=l[A], x=A; do { var T=b[P]; do { A+=T } while(!l[A]); S=l[A], 5!==S&&10!==S? (P=S, l[A]=0):(P=S&51*P>>4, l[A]&=P>>2|P<<2), w.push(A%s), w.push(A/s|0), --g } while(x!==A); y.push(w), --t } } return function(e) { e.save(), e.scale(1/a, -1/o), e.translate(0, -o), e.beginPath(); for(var t=0, r=y.length; t<r; t++) { var n=y[t]; e.moveTo(n[0], n[1]); for(var i=2, s=n.length; i<s; i+=2)e.lineTo(n[i], n[i+1]) } e.fill(), e.beginPath(), e.restore() } } Object.defineProperty(t, "__esModule", { value: !0 }), t.CanvasGraphics=void 0; var a=r(0), o=r(13), s=r(8), l=16, c={ get value() { return (0, a.shadow)(c, "value", (0, a.isLittleEndian)()) } }, u=function() { function e(e) { this.canvasFactory=e, this.cache=Object.create(null) } return e.prototype={ getCanvas: function(e, t, r, i) { var a; return void 0!==this.cache[e]? (a=this.cache[e], this.canvasFactory.reset(a, t, r), a.context.setTransform(1, 0, 0, 1, 0, 0)):(a=this.canvasFactory.create(t, r), this.cache[e]=a), i&&n(a.context), a }, clear: function() { for(var e in this.cache) { var t=this.cache[e]; this.canvasFactory.destroy(t), delete this.cache[e] } } }, e }(), d=function() { function e() { this.alphaIsShape=!1, this.fontSize=0, this.fontSizeScale=1, this.textMatrix=a.IDENTITY_MATRIX, this.textMatrixScale=1, this.fontMatrix=a.FONT_IDENTITY_MATRIX, this.leading=0, this.x=0, this.y=0, this.lineX=0, this.lineY=0, this.charSpacing=0, this.wordSpacing=0, this.textHScale=1, this.textRenderingMode=a.TextRenderingMode.FILL, this.textRise=0, this.fillColor="#000000", this.strokeColor="#000000", this.patternFill=!1, this.fillAlpha=1, this.strokeAlpha=1, this.lineWidth=1, this.activeSMask=null, this.resumeSMaskCtx=null } return e.prototype={ clone: function() { return Object.create(this) }, setCurrentPoint: function(e, t) { this.x=e, this.y=t } }, e }(), h=function() { function e(e, t, r, i, a) { this.ctx=e, this.current=new d, this.stateStack=[], this.pendingClip=null, this.pendingEOFill=!1, this.res=null, this.xobjs=null, this.commonObjs=t, this.objs=r, this.canvasFactory=i, this.imageLayer=a, this.groupStack=[], this.processingType3=null, this.baseTransform=null, this.baseTransformStack=[], this.groupLevel=0, this.smaskStack=[], this.smaskCounter=0, this.tempSMask=null, this.cachedCanvases=new u(this.canvasFactory), e&&n(e), this.cachedGetSinglePixelWidth=null } function t(e, t) { if("undefined"!=typeof ImageData&&t instanceof ImageData) return void e.putImageData(t, 0, 0); var r, n, i, o, s, u=t.height, d=t.width, h=u%l, f=(u-h)/l, p=0===h? f:f+1, m=e.createImageData(d, l), g=0, v=t.data, b=m.data; if(t.kind===a.ImageKind.GRAYSCALE_1BPP) { var y=v.byteLength, A=new Uint32Array(b.buffer, 0, b.byteLength>>2), _=A.length, S=d+7>>3, w=4294967295, P=c.value? 4278190080:255; for(n=0; n<p; n++) { for(o=n<f? l:h, r=0, i=0; i<o; i++) { for(var x=y-g, T=0, k=x>S? d:8*x-7, C=-8&k, R=0, E=0; T<C; T+=8)E=v[g++], A[r++]=128&E? w:P, A[r++]=64&E? w:P, A[r++]=32&E? w:P, A[r++]=16&E? w:P, A[r++]=8&E? w:P, A[r++]=4&E? w:P, A[r++]=2&E? w:P, A[r++]=1&E? w:P; for(; T<k; T++)0===R&&(E=v[g++], R=128), A[r++]=E&R? w:P, R>>=1 } for(; r<_;)A[r++]=0; e.putImageData(m, 0, n*l) } } else if(t.kind===a.ImageKind.RGBA_32BPP) { for(i=0, s=d*l*4, n=0; n<f; n++)b.set(v.subarray(g, g+s)), g+=s, e.putImageData(m, 0, i), i+=l; n<p&&(s=d*h*4, b.set(v.subarray(g, g+s)), e.putImageData(m, 0, i)) } else if(t.kind===a.ImageKind.RGB_24BPP) for(o=l, s=d*o, n=0; n<p; n++) { for(n>=f&&(o=h, s=d*o), r=0, i=s; i--;)b[r++]=v[g++], b[r++]=v[g++], b[r++]=v[g++], b[r++]=255; e.putImageData(m, 0, n*l) } else(0, a.error)("bad image kind: "+t.kind) } function r(e, t) { for(var r=t.height, n=t.width, i=r%l, a=(r-i)/l, o=0===i? a:a+1, s=e.createImageData(n, l), c=0, u=t.data, d=s.data, h=0; h<o; h++) { for(var f=h<a? l:i, p=3, m=0; m<f; m++)for(var g=0, v=0; v<n; v++) { if(!g) { var b=u[c++]; g=128 } d[p]=b&g? 0:255, p+=4, g>>=1 } e.putImageData(s, 0, h*l) } } function h(e, t) { for(var r=["strokeStyle", "fillStyle", "fillRule", "globalAlpha", "lineWidth", "lineCap", "lineJoin", "miterLimit", "globalCompositeOperation", "font"], n=0, i=r.length; n<i; n++) { var a=r[n]; void 0!==e[a]&&(t[a]=e[a]) } void 0!==e.setLineDash&&(t.setLineDash(e.getLineDash()), t.lineDashOffset=e.lineDashOffset) } function f(e) { e.strokeStyle="#000000", e.fillStyle="#000000", e.fillRule="nonzero", e.globalAlpha=1, e.lineWidth=1, e.lineCap="butt", e.lineJoin="miter", e.miterLimit=10, e.globalCompositeOperation="source-over", e.font="10px sans-serif", void 0!==e.setLineDash&&(e.setLineDash([]), e.lineDashOffset=0) } function p(e, t, r, n) { for(var i=e.length, a=3; a<i; a+=4) { var o=e[a]; if(0===o) e[a-3]=t, e[a-2]=r, e[a-1]=n; else if(o<255) { var s=255-o; e[a-3]=e[a-3]*o+t*s>>8, e[a-2]=e[a-2]*o+r*s>>8, e[a-1]=e[a-1]*o+n*s>>8 } } } function m(e, t, r) { for(var n=e.length, i=3; i<n; i+=4) { var a=r? r[e[i]]:e[i]; t[i]=t[i]*a*(1/255)|0 } } function g(e, t, r) { for(var n=e.length, i=3; i<n; i+=4) { var a=77*e[i-3]+152*e[i-2]+28*e[i-1]; t[i]=r? t[i]*r[a>>8]>>8:t[i]*a>>16 } } function v(e, t, r, n, i, a, o) { var s, l=!!a, c=l? a[0]:0, u=l? a[1]:0, d=l? a[2]:0; s="Luminosity"===i? g:m; for(var h=Math.min(n, Math.ceil(1048576/r)), f=0; f<n; f+=h) { var v=Math.min(h, n-f), b=e.getImageData(0, f, r, v), y=t.getImageData(0, f, r, v); l&&p(b.data, c, u, d), s(b.data, y.data, o), e.putImageData(y, 0, f) } } function b(e, t, r) { var n=t.canvas, i=t.context; e.setTransform(t.scaleX, 0, 0, t.scaleY, t.offsetX, t.offsetY); var a=t.backdrop||null; if(!t.transferMap&&s.WebGLUtils.isEnabled) { var o=s.WebGLUtils.composeSMask(r.canvas, n, { subtype: t.subtype, backdrop: a }); return e.setTransform(1, 0, 0, 1, 0, 0), void e.drawImage(o, t.offsetX, t.offsetY) } v(i, r, n.width, n.height, t.subtype, a, t.transferMap), e.drawImage(n, 0, 0) } var y=["butt", "round", "square"], A=["miter", "round", "bevel"], _={}, S={}; e.prototype={ beginDrawing: function(e) { var t=e.transform, r=e.viewport, n=e.transparency, i=e.background, a=void 0===i? null:i, o=this.ctx.canvas.width, s=this.ctx.canvas.height; if(this.ctx.save(), this.ctx.fillStyle=a||"rgb(255, 255, 255)", this.ctx.fillRect(0, 0, o, s), this.ctx.restore(), n) { var l=this.cachedCanvases.getCanvas("transparent", o, s, !0); this.compositeCtx=this.ctx, this.transparentCanvas=l.canvas, this.ctx=l.context, this.ctx.save(), this.ctx.transform.apply(this.ctx, this.compositeCtx.mozCurrentTransform) } this.ctx.save(), f(this.ctx), t&&this.ctx.transform.apply(this.ctx, t), this.ctx.transform.apply(this.ctx, r.transform), this.baseTransform=this.ctx.mozCurrentTransform.slice(), this.imageLayer&&this.imageLayer.beginLayout() }, executeOperatorList: function(e, t, r, n) { var i=e.argsArray, o=e.fnArray, s=t||0, l=i.length; if(l===s) return s; for(var c, u=l-s>10&&"function"==typeof r, d=u? Date.now()+15:0, h=0, f=this.commonObjs, p=this.objs; ;) { if(void 0!==n&&s===n.nextBreakPoint) return n.breakIt(s, r), s; if((c=o[s])!==a.OPS.dependency) this[c].apply(this, i[s]); else for(var m=i[s], g=0, v=m.length; g<v; g++) { var b=m[g], y="g"===b[0]&&"_"===b[1], A=y? f:p; if(!A.isResolved(b)) return A.get(b, r), s } if(++s===l) return s; if(u&&++h>10) { if(Date.now()>d) return r(), s; h=0 } } }, endDrawing: function() { null!==this.current.activeSMask&&this.endSMaskGroup(), this.ctx.restore(), this.transparentCanvas&&(this.ctx=this.compositeCtx, this.ctx.save(), this.ctx.setTransform(1, 0, 0, 1, 0, 0), this.ctx.drawImage(this.transparentCanvas, 0, 0), this.ctx.restore(), this.transparentCanvas=null), this.cachedCanvases.clear(), s.WebGLUtils.clear(), this.imageLayer&&this.imageLayer.endLayout() }, setLineWidth: function(e) { this.current.lineWidth=e, this.ctx.lineWidth=e }, setLineCap: function(e) { this.ctx.lineCap=y[e] }, setLineJoin: function(e) { this.ctx.lineJoin=A[e] }, setMiterLimit: function(e) { this.ctx.miterLimit=e }, setDash: function(e, t) { var r=this.ctx; void 0!==r.setLineDash&&(r.setLineDash(e), r.lineDashOffset=t) }, setRenderingIntent: function(e) { }, setFlatness: function(e) { }, setGState: function(e) { for(var t=0, r=e.length; t<r; t++) { var n=e[t], i=n[0], a=n[1]; switch(i) { case "LW": this.setLineWidth(a); break; case "LC": this.setLineCap(a); break; case "LJ": this.setLineJoin(a); break; case "ML": this.setMiterLimit(a); break; case "D": this.setDash(a[0], a[1]); break; case "RI": this.setRenderingIntent(a); break; case "FL": this.setFlatness(a); break; case "Font": this.setFont(a[0], a[1]); break; case "CA": this.current.strokeAlpha=n[1]; break; case "ca": this.current.fillAlpha=n[1], this.ctx.globalAlpha=n[1]; break; case "BM": this.ctx.globalCompositeOperation=a; break; case "SMask": this.current.activeSMask&&(this.stateStack.length>0&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask? this.suspendSMaskGroup():this.endSMaskGroup()), this.current.activeSMask=a? this.tempSMask:null, this.current.activeSMask&&this.beginSMaskGroup(), this.tempSMask=null } } }, beginSMaskGroup: function() { var e=this.current.activeSMask, t=e.canvas.width, r=e.canvas.height, n="smaskGroupAt"+this.groupLevel, i=this.cachedCanvases.getCanvas(n, t, r, !0), a=this.ctx, o=a.mozCurrentTransform; this.ctx.save(); var s=i.context; s.scale(1/e.scaleX, 1/e.scaleY), s.translate(-e.offsetX, -e.offsetY), s.transform.apply(s, o), e.startTransformInverse=s.mozCurrentTransformInverse, h(a, s), this.ctx=s, this.setGState([["BM", "source-over"], ["ca", 1], ["CA", 1]]), this.groupStack.push(a), this.groupLevel++ }, suspendSMaskGroup: function() { var e=this.ctx; this.groupLevel--, this.ctx=this.groupStack.pop(), b(this.ctx, this.current.activeSMask, e), this.ctx.restore(), this.ctx.save(), h(e, this.ctx), this.current.resumeSMaskCtx=e; var t=a.Util.transform(this.current.activeSMask.startTransformInverse, e.mozCurrentTransform); this.ctx.transform.apply(this.ctx, t), e.save(), e.setTransform(1, 0, 0, 1, 0, 0), e.clearRect(0, 0, e.canvas.width, e.canvas.height), e.restore() }, resumeSMaskGroup: function() { var e=this.current.resumeSMaskCtx, t=this.ctx; this.ctx=e, this.groupStack.push(t), this.groupLevel++ }, endSMaskGroup: function() { var e=this.ctx; this.groupLevel--, this.ctx=this.groupStack.pop(), b(this.ctx, this.current.activeSMask, e), this.ctx.restore(), h(e, this.ctx); var t=a.Util.transform(this.current.activeSMask.startTransformInverse, e.mozCurrentTransform); this.ctx.transform.apply(this.ctx, t) }, save: function() { this.ctx.save(); var e=this.current; this.stateStack.push(e), this.current=e.clone(), this.current.resumeSMaskCtx=null }, restore: function() { this.current.resumeSMaskCtx&&this.resumeSMaskGroup(), null===this.current.activeSMask||0!==this.stateStack.length&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask||this.endSMaskGroup(), 0!==this.stateStack.length&&(this.current=this.stateStack.pop(), this.ctx.restore(), this.pendingClip=null, this.cachedGetSinglePixelWidth=null) }, transform: function(e, t, r, n, i, a) { this.ctx.transform(e, t, r, n, i, a), this.cachedGetSinglePixelWidth=null }, constructPath: function(e, t) { for(var r=this.ctx, n=this.current, i=n.x, o=n.y, s=0, l=0, c=e.length; s<c; s++)switch(0|e[s]) { case a.OPS.rectangle: i=t[l++], o=t[l++]; var u=t[l++], d=t[l++]; 0===u&&(u=this.getSinglePixelWidth()), 0===d&&(d=this.getSinglePixelWidth()); var h=i+u, f=o+d; this.ctx.moveTo(i, o), this.ctx.lineTo(h, o), this.ctx.lineTo(h, f), this.ctx.lineTo(i, f), this.ctx.lineTo(i, o), this.ctx.closePath(); break; case a.OPS.moveTo: i=t[l++], o=t[l++], r.moveTo(i, o); break; case a.OPS.lineTo: i=t[l++], o=t[l++], r.lineTo(i, o); break; case a.OPS.curveTo: i=t[l+4], o=t[l+5], r.bezierCurveTo(t[l], t[l+1], t[l+2], t[l+3], i, o), l+=6; break; case a.OPS.curveTo2: r.bezierCurveTo(i, o, t[l], t[l+1], t[l+2], t[l+3]), i=t[l+2], o=t[l+3], l+=4; break; case a.OPS.curveTo3: i=t[l+2], o=t[l+3], r.bezierCurveTo(t[l], t[l+1], i, o, i, o), l+=4; break; case a.OPS.closePath: r.closePath() }n.setCurrentPoint(i, o) }, closePath: function() { this.ctx.closePath() }, stroke: function(e) { e=void 0===e||e; var t=this.ctx, r=this.current.strokeColor; t.lineWidth=Math.max(.65*this.getSinglePixelWidth(), this.current.lineWidth), t.globalAlpha=this.current.strokeAlpha, r&&r.hasOwnProperty("type")&&"Pattern"===r.type? (t.save(), t.strokeStyle=r.getPattern(t, this), t.stroke(), t.restore()):t.stroke(), e&&this.consumePath(), t.globalAlpha=this.current.fillAlpha }, closeStroke: function() { this.closePath(), this.stroke() }, fill: function(e) { e=void 0===e||e; var t=this.ctx, r=this.current.fillColor, n=this.current.patternFill, i=!1; n&&(t.save(), this.baseTransform&&t.setTransform.apply(t, this.baseTransform), t.fillStyle=r.getPattern(t, this), i=!0), this.pendingEOFill? (t.fill("evenodd"), this.pendingEOFill=!1):t.fill(), i&&t.restore(), e&&this.consumePath() }, eoFill: function() { this.pendingEOFill=!0, this.fill() }, fillStroke: function() { this.fill(!1), this.stroke(!1), this.consumePath() }, eoFillStroke: function() { this.pendingEOFill=!0, this.fillStroke() }, closeFillStroke: function() { this.closePath(), this.fillStroke() }, closeEOFillStroke: function() { this.pendingEOFill=!0, this.closePath(), this.fillStroke() }, endPath: function() { this.consumePath() }, clip: function() { this.pendingClip=_ }, eoClip: function() { this.pendingClip=S }, beginText: function() { this.current.textMatrix=a.IDENTITY_MATRIX, this.current.textMatrixScale=1, this.current.x=this.current.lineX=0, this.current.y=this.current.lineY=0 }, endText: function() { var e=this.pendingTextPaths, t=this.ctx; if(void 0===e) return void t.beginPath(); t.save(), t.beginPath(); for(var r=0; r<e.length; r++) { var n=e[r]; t.setTransform.apply(t, n.transform), t.translate(n.x, n.y), n.addToPath(t, n.fontSize) } t.restore(), t.clip(), t.beginPath(), delete this.pendingTextPaths }, setCharSpacing: function(e) { this.current.charSpacing=e }, setWordSpacing: function(e) { this.current.wordSpacing=e }, setHScale: function(e) { this.current.textHScale=e/100 }, setLeading: function(e) { this.current.leading=-e }, setFont: function(e, t) { var r=this.commonObjs.get(e), n=this.current; if(r||(0, a.error)("Can't find font for "+e), n.fontMatrix=r.fontMatrix? r.fontMatrix:a.FONT_IDENTITY_MATRIX, 0!==n.fontMatrix[0]&&0!==n.fontMatrix[3]||(0, a.warn)("Invalid font matrix for font "+e), t<0? (t=-t, n.fontDirection=-1):n.fontDirection=1, this.current.font=r, this.current.fontSize=t, !r.isType3Font) { var i=r.loadedName||"sans-serif", o=r.black? "900":r.bold? "bold":"normal", s=r.italic? "italic":"normal", l='"'+i+'", '+r.fallbackName, c=t<16? 16:t>100? 100:t; this.current.fontSizeScale=t/c; var u=s+" "+o+" "+c+"px "+l; this.ctx.font=u } }, setTextRenderingMode: function(e) { this.current.textRenderingMode=e }, setTextRise: function(e) { this.current.textRise=e }, moveText: function(e, t) { this.current.x=this.current.lineX+=e, this.current.y=this.current.lineY+=t }, setLeadingMoveText: function(e, t) { this.setLeading(-t), this.moveText(e, t) }, setTextMatrix: function(e, t, r, n, i, a) { this.current.textMatrix=[e, t, r, n, i, a], this.current.textMatrixScale=Math.sqrt(e*e+t*t), this.current.x=this.current.lineX=0, this.current.y=this.current.lineY=0 }, nextLine: function() { this.moveText(0, this.current.leading) }, paintChar: function(e, t, r) { var n, i=this.ctx, o=this.current, s=o.font, l=o.textRenderingMode, c=o.fontSize/o.fontSizeScale, u=l&a.TextRenderingMode.FILL_STROKE_MASK, d=!!(l&a.TextRenderingMode.ADD_TO_PATH_FLAG); if((s.disableFontFace||d)&&(n=s.getPathGenerator(this.commonObjs, e)), s.disableFontFace? (i.save(), i.translate(t, r), i.beginPath(), n(i, c), u!==a.TextRenderingMode.FILL&&u!==a.TextRenderingMode.FILL_STROKE||i.fill(), u!==a.TextRenderingMode.STROKE&&u!==a.TextRenderingMode.FILL_STROKE||i.stroke(), i.restore()):(u!==a.TextRenderingMode.FILL&&u!==a.TextRenderingMode.FILL_STROKE||i.fillText.call(i, e, t, r, o), u!==a.TextRenderingMode.STROKE&&u!==a.TextRenderingMode.FILL_STROKE||i.strokeText.call(i, e, t, r, o)), d) { (this.pendingTextPaths||(this.pendingTextPaths=[])).push({ transform: i.mozCurrentTransform, x: t, y: r, fontSize: c, addToPath: n }) } }, get isFontSubpixelAAEnabled() { var e=this.canvasFactory.create(10, 10).context; e.scale(1.5, 1), e.fillText("I", 0, 10); for(var t=e.getImageData(0, 0, 10, 10).data, r=!1, n=3; n<t.length; n+=4)if(t[n]>0&&t[n]<255) { r=!0; break } return (0, a.shadow)(this, "isFontSubpixelAAEnabled", r) }, showText: function(e) { var t=this.current, r=t.font; if(r.isType3Font) return this.showType3Text(e); var n=t.fontSize; if(0!==n) { var i=this.ctx, o=t.fontSizeScale, s=t.charSpacing, l=t.wordSpacing, c=t.fontDirection, u=t.textHScale*c, d=e.length, h=r.vertical, f=h? 1:-1, p=r.defaultVMetrics, m=n*t.fontMatrix[0], g=t.textRenderingMode===a.TextRenderingMode.FILL&&!r.disableFontFace; i.save(), i.transform.apply(i, t.textMatrix), i.translate(t.x, t.y+t.textRise), t.patternFill&&(i.fillStyle=t.fillColor.getPattern(i, this)), c>0? i.scale(u, -1):i.scale(u, 1); var v=t.lineWidth, b=t.textMatrixScale; if(0===b||0===v) { var y=t.textRenderingMode&a.TextRenderingMode.FILL_STROKE_MASK; y!==a.TextRenderingMode.STROKE&&y!==a.TextRenderingMode.FILL_STROKE||(this.cachedGetSinglePixelWidth=null, v=.65*this.getSinglePixelWidth()) } else v/=b; 1!==o&&(i.scale(o, o), v/=o), i.lineWidth=v; var A, _=0; for(A=0; A<d; ++A) { var S=e[A]; if((0, a.isNum)(S)) _+=f*S*n/1e3; else { var w, P, x, T, k=!1, C=(S.isSpace? l:0)+s, R=S.fontChar, E=S.accent, L=S.width; if(h) { var O, I, j; O=S.vmetric||p, I=S.vmetric? O[1]:.5*L, I=-I*m, j=O[2]*m, L=O? -O[0]:L, w=I/o, P=(_+j)/o } else w=_/o, P=0; if(r.remeasure&&L>0) { var D=1e3*i.measureText(R).width/n*o; if(L<D&&this.isFontSubpixelAAEnabled) { var F=L/D; k=!0, i.save(), i.scale(F, 1), w/=F } else L!==D&&(w+=(L-D)/2e3*n/o) } (S.isInFont||r.missingFile)&&(g&&!E? i.fillText.call(i, R, w, P, t):(this.paintChar(R, w, P), E&&(x=w+E.offset.x/o, T=P-E.offset.y/o, this.paintChar(E.fontChar, x, T)))); _+=L*m+C*c, k&&i.restore() } } h? t.y-=_*u:t.x+=_*u, i.restore() } }, showType3Text: function(e) { var t, r, n, i, o=this.ctx, s=this.current, l=s.font, c=s.fontSize, u=s.fontDirection, d=l.vertical? 1:-1, h=s.charSpacing, f=s.wordSpacing, p=s.textHScale*u, m=s.fontMatrix||a.FONT_IDENTITY_MATRIX, g=e.length, v=s.textRenderingMode===a.TextRenderingMode.INVISIBLE; if(!v&&0!==c) { for(this.cachedGetSinglePixelWidth=null, o.save(), o.transform.apply(o, s.textMatrix), o.translate(s.x, s.y), o.scale(p, u), t=0; t<g; ++t)if(r=e[t], (0, a.isNum)(r)) i=d*r*c/1e3, this.ctx.translate(i, 0), s.x+=i*p; else { var b=(r.isSpace? f:0)+h, y=l.charProcOperatorList[r.operatorListId]; if(y) { this.processingType3=r, this.save(), o.scale(c, c), o.transform.apply(o, m), this.executeOperatorList(y), this.restore(); var A=a.Util.applyTransform([r.width, 0], m); n=A[0]*c+b, o.translate(n, 0), s.x+=n*p } else(0, a.warn)('Type3 character "'+r.operatorListId+'" is not available.') } o.restore(), this.processingType3=null } }, setCharWidth: function(e, t) { }, setCharWidthAndBounds: function(e, t, r, n, i, a) { this.ctx.rect(r, n, i-r, a-n), this.clip(), this.endPath() }, getColorN_Pattern: function(t) { var r, n=this; if("TilingPattern"===t[0]) { var i=t[1], a=this.baseTransform||this.ctx.mozCurrentTransform.slice(), s={ createCanvasGraphics: function(t) { return new e(t, n.commonObjs, n.objs, n.canvasFactory) } }; r=new o.TilingPattern(t, i, this.ctx, s, a) } else r=(0, o.getShadingPatternFromIR)(t); return r }, setStrokeColorN: function() { this.current.strokeColor=this.getColorN_Pattern(arguments) }, setFillColorN: function() { this.current.fillColor=this.getColorN_Pattern(arguments), this.current.patternFill=!0 }, setStrokeRGBColor: function(e, t, r) { var n=a.Util.makeCssRgb(e, t, r); this.ctx.strokeStyle=n, this.current.strokeColor=n }, setFillRGBColor: function(e, t, r) { var n=a.Util.makeCssRgb(e, t, r); this.ctx.fillStyle=n, this.current.fillColor=n, this.current.patternFill=!1 }, shadingFill: function(e) { var t=this.ctx; this.save(); var r=(0, o.getShadingPatternFromIR)(e); t.fillStyle=r.getPattern(t, this, !0); var n=t.mozCurrentTransformInverse; if(n) { var i=t.canvas, s=i.width, l=i.height, c=a.Util.applyTransform([0, 0], n), u=a.Util.applyTransform([0, l], n), d=a.Util.applyTransform([s, 0], n), h=a.Util.applyTransform([s, l], n), f=Math.min(c[0], u[0], d[0], h[0]), p=Math.min(c[1], u[1], d[1], h[1]), m=Math.max(c[0], u[0], d[0], h[0]), g=Math.max(c[1], u[1], d[1], h[1]); this.ctx.fillRect(f, p, m-f, g-p) } else this.ctx.fillRect(-1e10, -1e10, 2e10, 2e10); this.restore() }, beginInlineImage: function() { (0, a.error)("Should not call beginInlineImage") }, beginImageData: function() { (0, a.error)("Should not call beginImageData") }, paintFormXObjectBegin: function(e, t) { if(this.save(), this.baseTransformStack.push(this.baseTransform), (0, a.isArray)(e)&&6===e.length&&this.transform.apply(this, e), this.baseTransform=this.ctx.mozCurrentTransform, (0, a.isArray)(t)&&4===t.length) { var r=t[2]-t[0], n=t[3]-t[1]; this.ctx.rect(t[0], t[1], r, n), this.clip(), this.endPath() } }, paintFormXObjectEnd: function() { this.restore(), this.baseTransform=this.baseTransformStack.pop() }, beginGroup: function(e) { this.save(); var t=this.ctx; e.isolated||(0, a.info)("TODO: Support non-isolated groups."), e.knockout&&(0, a.warn)("Knockout groups not supported."); var r=t.mozCurrentTransform; e.matrix&&t.transform.apply(t, e.matrix), (0, a.assert)(e.bbox, "Bounding box is required."); var n=a.Util.getAxialAlignedBoundingBox(e.bbox, t.mozCurrentTransform), i=[0, 0, t.canvas.width, t.canvas.height]; n=a.Util.intersect(n, i)||[0, 0, 0, 0]; var o=Math.floor(n[0]), s=Math.floor(n[1]), l=Math.max(Math.ceil(n[2])-o, 1), c=Math.max(Math.ceil(n[3])-s, 1), u=1, d=1; l>4096&&(u=l/4096, l=4096), c>4096&&(d=c/4096, c=4096); var f="groupAt"+this.groupLevel; e.smask&&(f+="_smask_"+this.smaskCounter++%2); var p=this.cachedCanvases.getCanvas(f, l, c, !0), m=p.context; m.scale(1/u, 1/d), m.translate(-o, -s), m.transform.apply(m, r), e.smask? this.smaskStack.push({ canvas: p.canvas, context: m, offsetX: o, offsetY: s, scaleX: u, scaleY: d, subtype: e.smask.subtype, backdrop: e.smask.backdrop, transferMap: e.smask.transferMap||null, startTransformInverse: null }):(t.setTransform(1, 0, 0, 1, 0, 0), t.translate(o, s), t.scale(u, d)), h(t, m), this.ctx=m, this.setGState([["BM", "source-over"], ["ca", 1], ["CA", 1]]), this.groupStack.push(t), this.groupLevel++, this.current.activeSMask=null }, endGroup: function(e) { this.groupLevel--; var t=this.ctx; this.ctx=this.groupStack.pop(), void 0!==this.ctx.imageSmoothingEnabled? this.ctx.imageSmoothingEnabled=!1:this.ctx.mozImageSmoothingEnabled=!1, e.smask? this.tempSMask=this.smaskStack.pop():this.ctx.drawImage(t.canvas, 0, 0), this.restore() }, beginAnnotations: function() { this.save(), this.baseTransform&&this.ctx.setTransform.apply(this.ctx, this.baseTransform) }, endAnnotations: function() { this.restore() }, beginAnnotation: function(e, t, r) { if(this.save(), f(this.ctx), this.current=new d, (0, a.isArray)(e)&&4===e.length) { var n=e[2]-e[0], i=e[3]-e[1]; this.ctx.rect(e[0], e[1], n, i), this.clip(), this.endPath() } this.transform.apply(this, t), this.transform.apply(this, r) }, endAnnotation: function() { this.restore() }, paintJpegXObject: function(e, t, r) { var n=this.objs.get(e); if(!n) return void (0, a.warn)("Dependent image isn't ready yet"); this.save(); var i=this.ctx; if(i.scale(1/t, -1/r), i.drawImage(n, 0, 0, n.width, n.height, 0, -r, t, r), this.imageLayer) { var o=i.mozCurrentTransformInverse, s=this.getCanvasPosition(0, 0); this.imageLayer.appendImage({ objId: e, left: s[0], top: s[1], width: t/o[0], height: r/o[3] }) } this.restore() }, paintImageMaskXObject: function(e) { var t=this.ctx, n=e.width, a=e.height, o=this.current.fillColor, s=this.current.patternFill, l=this.processingType3; if(l&&void 0===l.compiled&&(l.compiled=n<=1e3&&a<=1e3? i({ data: e.data, width: n, height: a }):null), l&&l.compiled) return void l.compiled(t); var c=this.cachedCanvases.getCanvas("maskCanvas", n, a), u=c.context; u.save(), r(u, e), u.globalCompositeOperation="source-in", u.fillStyle=s? o.getPattern(u, this):o, u.fillRect(0, 0, n, a), u.restore(), this.paintInlineImageXObject(c.canvas) }, paintImageMaskXObjectRepeat: function(e, t, n, i) { var a=e.width, o=e.height, s=this.current.fillColor, l=this.current.patternFill, c=this.cachedCanvases.getCanvas("maskCanvas", a, o), u=c.context; u.save(), r(u, e), u.globalCompositeOperation="source-in", u.fillStyle=l? s.getPattern(u, this):s, u.fillRect(0, 0, a, o), u.restore(); for(var d=this.ctx, h=0, f=i.length; h<f; h+=2)d.save(), d.transform(t, 0, 0, n, i[h], i[h+1]), d.scale(1, -1), d.drawImage(c.canvas, 0, 0, a, o, 0, -1, 1, 1), d.restore() }, paintImageMaskXObjectGroup: function(e) { for(var t=this.ctx, n=this.current.fillColor, i=this.current.patternFill, a=0, o=e.length; a<o; a++) { var s=e[a], l=s.width, c=s.height, u=this.cachedCanvases.getCanvas("maskCanvas", l, c), d=u.context; d.save(), r(d, s), d.globalCompositeOperation="source-in", d.fillStyle=i? n.getPattern(d, this):n, d.fillRect(0, 0, l, c), d.restore(), t.save(), t.transform.apply(t, s.transform), t.scale(1, -1), t.drawImage(u.canvas, 0, 0, l, c, 0, -1, 1, 1), t.restore() } }, paintImageXObject: function(e) { var t=this.objs.get(e); if(!t) return void (0, a.warn)("Dependent image isn't ready yet"); this.paintInlineImageXObject(t) }, paintImageXObjectRepeat: function(e, t, r, n) { var i=this.objs.get(e); if(!i) return void (0, a.warn)("Dependent image isn't ready yet"); for(var o=i.width, s=i.height, l=[], c=0, u=n.length; c<u; c+=2)l.push({ transform: [t, 0, 0, r, n[c], n[c+1]], x: 0, y: 0, w: o, h: s }); this.paintInlineImageXObjectGroup(i, l) }, paintInlineImageXObject: function(e) { var r=e.width, n=e.height, i=this.ctx; this.save(), i.scale(1/r, -1/n); var a, o, s=i.mozCurrentTransformInverse, l=s[0], c=s[1], u=Math.max(Math.sqrt(l*l+c*c), 1), d=s[2], h=s[3], f=Math.max(Math.sqrt(d*d+h*h), 1); if(e instanceof HTMLElement||!e.data) a=e; else { o=this.cachedCanvases.getCanvas("inlineImage", r, n); var p=o.context; t(p, e), a=o.canvas } for(var m=r, g=n, v="prescale1"; u>2&&m>1||f>2&&g>1;) { var b=m, y=g; u>2&&m>1&&(b=Math.ceil(m/2), u/=m/b), f>2&&g>1&&(y=Math.ceil(g/2), f/=g/y), o=this.cachedCanvases.getCanvas(v, b, y), p=o.context, p.clearRect(0, 0, b, y), p.drawImage(a, 0, 0, m, g, 0, 0, b, y), a=o.canvas, m=b, g=y, v="prescale1"===v? "prescale2":"prescale1" } if(i.drawImage(a, 0, 0, m, g, 0, -n, r, n), this.imageLayer) { var A=this.getCanvasPosition(0, -n); this.imageLayer.appendImage({ imgData: e, left: A[0], top: A[1], width: r/s[0], height: n/s[3] }) } this.restore() }, paintInlineImageXObjectGroup: function(e, r) { var n=this.ctx, i=e.width, a=e.height, o=this.cachedCanvases.getCanvas("inlineImage", i, a); t(o.context, e); for(var s=0, l=r.length; s<l; s++) { var c=r[s]; if(n.save(), n.transform.apply(n, c.transform), n.scale(1, -1), n.drawImage(o.canvas, c.x, c.y, c.w, c.h, 0, -1, 1, 1), this.imageLayer) { var u=this.getCanvasPosition(c.x, c.y); this.imageLayer.appendImage({ imgData: e, left: u[0], top: u[1], width: i, height: a }) } n.restore() } }, paintSolidColorImageMask: function() { this.ctx.fillRect(0, 0, 1, 1) }, paintXObject: function() { (0, a.warn)("Unsupported 'paintXObject' command.") }, markPoint: function(e) { }, markPointProps: function(e, t) { }, beginMarkedContent: function(e) { }, beginMarkedContentProps: function(e, t) { }, endMarkedContent: function() { }, beginCompat: function() { }, endCompat: function() { }, consumePath: function() { var e=this.ctx; this.pendingClip&&(this.pendingClip===S? e.clip("evenodd"):e.clip(), this.pendingClip=null), e.beginPath() }, getSinglePixelWidth: function(e) { if(null===this.cachedGetSinglePixelWidth) { this.ctx.save(); var t=this.ctx.mozCurrentTransformInverse; this.ctx.restore(), this.cachedGetSinglePixelWidth=Math.sqrt(Math.max(t[0]*t[0]+t[1]*t[1], t[2]*t[2]+t[3]*t[3])) } return this.cachedGetSinglePixelWidth }, getCanvasPosition: function(e, t) { var r=this.ctx.mozCurrentTransform; return [r[0]*e+r[2]*t+r[4], r[1]*e+r[3]*t+r[5]] } }; for(var w in a.OPS) e.prototype[a.OPS[w]]=e.prototype[w]; return e }(); t.CanvasGraphics=h }, function(e, t, r) {
    "use strict"; function n(e) { this.docId=e, this.styleElement=null, this.nativeFontFaces=[], this.loadTestFontId=0, this.loadingContext={ requests: [], nextRequestId: 0 } } Object.defineProperty(t, "__esModule", { value: !0 }), t.FontLoader=t.FontFaceObject=void 0; var i=r(0); n.prototype={ insertRule: function(e) { var t=this.styleElement; t||(t=this.styleElement=document.createElement("style"), t.id="PDFJS_FONT_STYLE_TAG_"+this.docId, document.documentElement.getElementsByTagName("head")[0].appendChild(t)); var r=t.sheet; r.insertRule(e, r.cssRules.length) }, clear: function() { this.styleElement&&(this.styleElement.remove(), this.styleElement=null), this.nativeFontFaces.forEach(function(e) { document.fonts.delete(e) }), this.nativeFontFaces.length=0 } }; var a=function() { return atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA==") }; Object.defineProperty(n.prototype, "loadTestFont", { get: function() { return (0, i.shadow)(this, "loadTestFont", a()) }, configurable: !0 }), n.prototype.addNativeFontFace=function(e) { this.nativeFontFaces.push(e), document.fonts.add(e) }, n.prototype.bind=function(e, t) {
      for(var r=[], a=[], o=[], s=n.isFontLoadingAPISupported&&!n.isSyncFontLoadingSupported, l=0, c=e.length; l<c; l++) {
        var u=e[l]; if(!u.attached&&!1!==u.loading) if(u.attached=!0, s) { var d=u.createNativeFontFace(); d&&(this.addNativeFontFace(d), o.push(function(e) { return e.loaded.catch(function(t) { (0, i.warn)('Failed to load font "'+e.family+'": '+t) }) }(d))) } else {
          var h=u.createFontFaceRule(); h&&(this.insertRule(h), r.push(h), a.push(u))
        }
      } var f=this.queueLoadingCallback(t); s? Promise.all(o).then(function() { f.complete() }):r.length>0&&!n.isSyncFontLoadingSupported? this.prepareFontLoadEvent(r, a, f):f.complete()
    }, n.prototype.queueLoadingCallback=function(e) { function t() { for((0, i.assert)(!a.end, "completeRequest() cannot be called twice"), a.end=Date.now(); r.requests.length>0&&r.requests[0].end;) { var e=r.requests.shift(); setTimeout(e.callback, 0) } } var r=this.loadingContext, n="pdfjs-font-loading-"+r.nextRequestId++, a={ id: n, complete: t, callback: e, started: Date.now() }; return r.requests.push(a), a }, n.prototype.prepareFontLoadEvent=function(e, t, r) { function n(e, t) { return e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|255&e.charCodeAt(t+3) } function a(e, t, r, n) { return e.substr(0, t)+n+e.substr(t+r) } function o(e, t) { return ++d>30? ((0, i.warn)("Load test font never loaded."), void t()):(u.font="30px "+e, u.fillText(".", 0, 20), u.getImageData(0, 0, 1, 1).data[3]>0? void t():void setTimeout(o.bind(null, e, t))) } var s, l, c=document.createElement("canvas"); c.width=1, c.height=1; var u=c.getContext("2d"), d=0, h="lt"+Date.now()+this.loadTestFontId++, f=this.loadTestFont; f=a(f, 976, h.length, h); var p=n(f, 16); for(s=0, l=h.length-3; s<l; s+=4)p=p-1482184792+n(h, s)|0; s<h.length&&(p=p-1482184792+n(h+"XXX", s)|0), f=a(f, 16, 4, (0, i.string32)(p)); var m="url(data:font/opentype;base64,"+btoa(f)+");", g='@font-face { font-family:"'+h+'";src:'+m+"}"; this.insertRule(g); var v=[]; for(s=0, l=t.length; s<l; s++)v.push(t[s].loadedName); v.push(h); var b=document.createElement("div"); for(b.setAttribute("style", "visibility: hidden;width: 10px; height: 10px;position: absolute; top: 0px; left: 0px;"), s=0, l=v.length; s<l; ++s) { var y=document.createElement("span"); y.textContent="Hi", y.style.fontFamily=v[s], b.appendChild(y) } document.body.appendChild(b), o(h, function() { document.body.removeChild(b), r.complete() }) }, n.isFontLoadingAPISupported="undefined"!=typeof document&&!!document.fonts; var o=function() { if("undefined"==typeof navigator) return !0; var e=!1, t=/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent); return t&&t[1]>=14&&(e=!0), e }; Object.defineProperty(n, "isSyncFontLoadingSupported", { get: function() { return (0, i.shadow)(n, "isSyncFontLoadingSupported", o()) }, enumerable: !0, configurable: !0 }); var s={ get value() { return (0, i.shadow)(this, "value", (0, i.isEvalSupported)()) } }, l=function() { function e(e, t) { this.compiledGlyphs=Object.create(null); for(var r in e) this[r]=e[r]; this.options=t } return e.prototype={ createNativeFontFace: function() { if(!this.data) return null; if(this.options.disableFontFace) return this.disableFontFace=!0, null; var e=new FontFace(this.loadedName, this.data, {}); return this.options.fontRegistry&&this.options.fontRegistry.registerFont(this), e }, createFontFaceRule: function() { if(!this.data) return null; if(this.options.disableFontFace) return this.disableFontFace=!0, null; var e=(0, i.bytesToString)(new Uint8Array(this.data)), t=this.loadedName, r="url(data:"+this.mimetype+";base64,"+btoa(e)+");", n='@font-face { font-family:"'+t+'";src:'+r+"}"; return this.options.fontRegistry&&this.options.fontRegistry.registerFont(this, r), n }, getPathGenerator: function(e, t) { if(!(t in this.compiledGlyphs)) { var r, n, i, a=e.get(this.loadedName+"_path_"+t); if(this.options.isEvalSupported&&s.value) { var o, l=""; for(n=0, i=a.length; n<i; n++)r=a[n], o=void 0!==r.args? r.args.join(","):"", l+="c."+r.cmd+"("+o+");\n"; this.compiledGlyphs[t]=new Function("c", "size", l) } else this.compiledGlyphs[t]=function(e, t) { for(n=0, i=a.length; n<i; n++)r=a[n], "scale"===r.cmd&&(r.args=[t, -t]), e[r.cmd].apply(e, r.args) } } return this.compiledGlyphs[t] } }, e }(); t.FontFaceObject=l, t.FontLoader=n
  }, function(e, t, r) { "use strict"; function n(e) { var t=o[e[0]]; return t||(0, i.error)("Unknown IR type: "+e[0]), t.fromIR(e) } Object.defineProperty(t, "__esModule", { value: !0 }), t.TilingPattern=t.getShadingPatternFromIR=void 0; var i=r(0), a=r(8), o={}; o.RadialAxial={ fromIR: function(e) { var t=e[1], r=e[2], n=e[3], i=e[4], a=e[5], o=e[6]; return { type: "Pattern", getPattern: function(e) { var s; "axial"===t? s=e.createLinearGradient(n[0], n[1], i[0], i[1]):"radial"===t&&(s=e.createRadialGradient(n[0], n[1], a, i[0], i[1], o)); for(var l=0, c=r.length; l<c; ++l) { var u=r[l]; s.addColorStop(u[0], u[1]) } return s } } } }; var s=function() { function e(e, t, r, n, i, a, o, s) { var l, c=t.coords, u=t.colors, d=e.data, h=4*e.width; c[r+1]>c[n+1]&&(l=r, r=n, n=l, l=a, a=o, o=l), c[n+1]>c[i+1]&&(l=n, n=i, i=l, l=o, o=s, s=l), c[r+1]>c[n+1]&&(l=r, r=n, n=l, l=a, a=o, o=l); var f=(c[r]+t.offsetX)*t.scaleX, p=(c[r+1]+t.offsetY)*t.scaleY, m=(c[n]+t.offsetX)*t.scaleX, g=(c[n+1]+t.offsetY)*t.scaleY, v=(c[i]+t.offsetX)*t.scaleX, b=(c[i+1]+t.offsetY)*t.scaleY; if(!(p>=b)) for(var y, A, _, S, w, P, x, T, k, C=u[a], R=u[a+1], E=u[a+2], L=u[o], O=u[o+1], I=u[o+2], j=u[s], D=u[s+1], F=u[s+2], N=Math.round(p), M=Math.round(b), W=N; W<=M; W++) { W<g? (k=W<p? 0:p===g? 1:(p-W)/(p-g), y=f-(f-m)*k, A=C-(C-L)*k, _=R-(R-O)*k, S=E-(E-I)*k):(k=W>b? 1:g===b? 0:(g-W)/(g-b), y=m-(m-v)*k, A=L-(L-j)*k, _=O-(O-D)*k, S=I-(I-F)*k), k=W<p? 0:W>b? 1:(p-W)/(p-b), w=f-(f-v)*k, P=C-(C-j)*k, x=R-(R-D)*k, T=E-(E-F)*k; for(var U=Math.round(Math.min(y, w)), q=Math.round(Math.max(y, w)), B=h*W+4*U, z=U; z<=q; z++)k=(y-z)/(y-w), k=k<0? 0:k>1? 1:k, d[B++]=A-(A-P)*k|0, d[B++]=_-(_-x)*k|0, d[B++]=S-(S-T)*k|0, d[B++]=255 } } function t(t, r, n) { var a, o, s=r.coords, l=r.colors; switch(r.type) { case "lattice": var c=r.verticesPerRow, u=Math.floor(s.length/c)-1, d=c-1; for(a=0; a<u; a++)for(var h=a*c, f=0; f<d; f++, h++)e(t, n, s[h], s[h+1], s[h+c], l[h], l[h+1], l[h+c]), e(t, n, s[h+c+1], s[h+1], s[h+c], l[h+c+1], l[h+1], l[h+c]); break; case "triangles": for(a=0, o=s.length; a<o; a+=3)e(t, n, s[a], s[a+1], s[a+2], l[a], l[a+1], l[a+2]); break; default: (0, i.error)("illigal figure") } } function r(e, r, n, i, o, s, l) { var c, u, d, h, f=Math.floor(e[0]), p=Math.floor(e[1]), m=Math.ceil(e[2])-f, g=Math.ceil(e[3])-p, v=Math.min(Math.ceil(Math.abs(m*r[0]*1.1)), 3e3), b=Math.min(Math.ceil(Math.abs(g*r[1]*1.1)), 3e3), y=m/v, A=g/b, _={ coords: n, colors: i, offsetX: -f, offsetY: -p, scaleX: 1/y, scaleY: 1/A }, S=v+4, w=b+4; if(a.WebGLUtils.isEnabled) c=a.WebGLUtils.drawFigures(v, b, s, o, _), u=l.getCanvas("mesh", S, w, !1), u.context.drawImage(c, 2, 2), c=u.canvas; else { u=l.getCanvas("mesh", S, w, !1); var P=u.context, x=P.createImageData(v, b); if(s) { var T=x.data; for(d=0, h=T.length; d<h; d+=4)T[d]=s[0], T[d+1]=s[1], T[d+2]=s[2], T[d+3]=255 } for(d=0; d<o.length; d++)t(x, o[d], _); P.putImageData(x, 2, 2), c=u.canvas } return { canvas: c, offsetX: f-2*y, offsetY: p-2*A, scaleX: y, scaleY: A } } return r }(); o.Mesh={ fromIR: function(e) { var t=e[2], r=e[3], n=e[4], a=e[5], o=e[6], l=e[8]; return { type: "Pattern", getPattern: function(e, c, u) { var d; if(u) d=i.Util.singularValueDecompose2dScale(e.mozCurrentTransform); else if(d=i.Util.singularValueDecompose2dScale(c.baseTransform), o) { var h=i.Util.singularValueDecompose2dScale(o); d=[d[0]*h[0], d[1]*h[1]] } var f=s(a, d, t, r, n, u? null:l, c.cachedCanvases); return u||(e.setTransform.apply(e, c.baseTransform), o&&e.transform.apply(e, o)), e.translate(f.offsetX, f.offsetY), e.scale(f.scaleX, f.scaleY), e.createPattern(f.canvas, "no-repeat") } } } }, o.Dummy={ fromIR: function() { return { type: "Pattern", getPattern: function() { return "hotpink" } } } }; var l=function() { function e(e, t, r, n, a) { this.operatorList=e[2], this.matrix=e[3]||[1, 0, 0, 1, 0, 0], this.bbox=i.Util.normalizeRect(e[4]), this.xstep=e[5], this.ystep=e[6], this.paintType=e[7], this.tilingType=e[8], this.color=t, this.canvasGraphicsFactory=n, this.baseTransform=a, this.type="Pattern", this.ctx=r } var t={ COLORED: 1, UNCOLORED: 2 }; return e.prototype={ createPatternCanvas: function(e) { var t=this.operatorList, r=this.bbox, n=this.xstep, a=this.ystep, o=this.paintType, s=this.tilingType, l=this.color, c=this.canvasGraphicsFactory; (0, i.info)("TilingType: "+s); var u=r[0], d=r[1], h=r[2], f=r[3], p=[u, d], m=[u+n, d+a], g=m[0]-p[0], v=m[1]-p[1], b=i.Util.singularValueDecompose2dScale(this.matrix), y=i.Util.singularValueDecompose2dScale(this.baseTransform), A=[b[0]*y[0], b[1]*y[1]]; g=Math.min(Math.ceil(Math.abs(g*A[0])), 3e3), v=Math.min(Math.ceil(Math.abs(v*A[1])), 3e3); var _=e.cachedCanvases.getCanvas("pattern", g, v, !0), S=_.context, w=c.createCanvasGraphics(S); w.groupLevel=e.groupLevel, this.setFillAndStrokeStyleToContext(S, o, l), this.setScale(g, v, n, a), this.transformToScale(w); var P=[1, 0, 0, 1, -p[0], -p[1]]; return w.transform.apply(w, P), this.clipBbox(w, r, u, d, h, f), w.executeOperatorList(t), _.canvas }, setScale: function(e, t, r, n) { this.scale=[e/r, t/n] }, transformToScale: function(e) { var t=this.scale, r=[t[0], 0, 0, t[1], 0, 0]; e.transform.apply(e, r) }, scaleToContext: function() { var e=this.scale; this.ctx.scale(1/e[0], 1/e[1]) }, clipBbox: function(e, t, r, n, a, o) { if((0, i.isArray)(t)&&4===t.length) { var s=a-r, l=o-n; e.ctx.rect(r, n, s, l), e.clip(), e.endPath() } }, setFillAndStrokeStyleToContext: function(e, r, n) { switch(r) { case t.COLORED: var a=this.ctx; e.fillStyle=a.fillStyle, e.strokeStyle=a.strokeStyle; break; case t.UNCOLORED: var o=i.Util.makeCssRgb(n[0], n[1], n[2]); e.fillStyle=o, e.strokeStyle=o; break; default: (0, i.error)("Unsupported paint type: "+r) } }, getPattern: function(e, t) { var r=this.createPatternCanvas(t); return e=this.ctx, e.setTransform.apply(e, this.baseTransform), e.transform.apply(e, this.matrix), this.scaleToContext(), e.createPattern(r, "repeat") } }, e }(); t.getShadingPatternFromIR=n, t.TilingPattern=l }, function(e, t, r) { "use strict"; var n=r(0), i=r(9), a=r(3), o=r(5), s=r(2), l=r(1), c=r(4); t.PDFJS=i.PDFJS, t.build=a.build, t.version=a.version, t.getDocument=a.getDocument, t.LoopbackPort=a.LoopbackPort, t.PDFDataRangeTransport=a.PDFDataRangeTransport, t.PDFWorker=a.PDFWorker, t.renderTextLayer=o.renderTextLayer, t.AnnotationLayer=s.AnnotationLayer, t.CustomStyle=l.CustomStyle, t.createPromiseCapability=n.createPromiseCapability, t.PasswordResponses=n.PasswordResponses, t.InvalidPDFException=n.InvalidPDFException, t.MissingPDFException=n.MissingPDFException, t.SVGGraphics=c.SVGGraphics, t.NativeImageDecoding=n.NativeImageDecoding, t.UnexpectedResponseException=n.UnexpectedResponseException, t.OPS=n.OPS, t.UNSUPPORTED_FEATURES=n.UNSUPPORTED_FEATURES, t.isValidUrl=l.isValidUrl, t.createValidAbsoluteUrl=n.createValidAbsoluteUrl, t.createObjectURL=n.createObjectURL, t.removeNullCharacters=n.removeNullCharacters, t.shadow=n.shadow, t.createBlob=n.createBlob, t.RenderingCancelledException=l.RenderingCancelledException, t.getFilenameFromUrl=l.getFilenameFromUrl, t.addLinkAttributes=l.addLinkAttributes, t.StatTimer=n.StatTimer }, function(e, t, r) { "use strict"; (function(e) { var t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator? function(e) { return typeof e }:function(e) { return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype? "symbol":typeof e }; if("undefined"==typeof PDFJS||!PDFJS.compatibilityChecked) { var r="undefined"!=typeof window? window:void 0!==e? e:"undefined"!=typeof self? self:void 0, n="undefined"!=typeof navigator&&navigator.userAgent||"", i=/Android/.test(n), a=/Android\s[0-2][^\d]/.test(n), o=/Android\s[0-4][^\d]/.test(n), s=n.indexOf("Chrom")>=0, l=/Chrome\/(39|40)\./.test(n), c=n.indexOf("CriOS")>=0, u=n.indexOf("Trident")>=0, d=/\b(iPad|iPhone|iPod)(?=;)/.test(n), h=n.indexOf("Opera")>=0, f=/Safari\//.test(n)&&!/(Chrome\/|Android\s)/.test(n), p="object"===("undefined"==typeof window? "undefined":t(window))&&"object"===("undefined"==typeof document? "undefined":t(document)); "undefined"==typeof PDFJS&&(r.PDFJS={}), PDFJS.compatibilityChecked=!0, function() { function e(e, t) { return new s(this.slice(e, t)) } function n(e, t) { arguments.length<2&&(t=0); for(var r=0, n=e.length; r<n; ++r, ++t)this[t]=255&e[r] } function i(e, t) { this.buffer=e, this.byteLength=e.length, this.length=t, o(this.length) } function a(e) { return { get: function() { var t=this.buffer, r=e<<2; return (t[r]|t[r+1]<<8|t[r+2]<<16|t[r+3]<<24)>>>0 }, set: function(t) { var r=this.buffer, n=e<<2; r[n]=255&t, r[n+1]=t>>8&255, r[n+2]=t>>16&255, r[n+3]=t>>>24&255 } } } function o(e) { for(; l<e;)Object.defineProperty(i.prototype, l, a(l)), l++ } function s(r) { var i, a, o; if("number"==typeof r) for(i=[], a=0; a<r; ++a)i[a]=0; else if("slice" in r) i=r.slice(0); else for(i=[], a=0, o=r.length; a<o; ++a)i[a]=r[a]; return i.subarray=e, i.buffer=i, i.byteLength=i.length, i.set=n, "object"===(void 0===r? "undefined":t(r))&&r.buffer&&(i.buffer=r.buffer), i } if("undefined"!=typeof Uint8Array) return void 0===Uint8Array.prototype.subarray&&(Uint8Array.prototype.subarray=function(e, t) { return new Uint8Array(this.slice(e, t)) }, Float32Array.prototype.subarray=function(e, t) { return new Float32Array(this.slice(e, t)) }), void ("undefined"==typeof Float64Array&&(r.Float64Array=Float32Array)); i.prototype=Object.create(null); var l=0; r.Uint8Array=s, r.Int8Array=s, r.Int32Array=s, r.Uint16Array=s, r.Float32Array=s, r.Float64Array=s, r.Uint32Array=function() { if(3===arguments.length) { if(0!==arguments[1]) throw new Error("offset !== 0 is not supported"); return new i(arguments[0], arguments[2]) } return s.apply(this, arguments) } }(), function() { if(p&&window.CanvasPixelArray) { var e=window.CanvasPixelArray.prototype; "buffer" in e||(Object.defineProperty(e, "buffer", { get: function() { return this }, enumerable: !1, configurable: !0 }), Object.defineProperty(e, "byteLength", { get: function() { return this.length }, enumerable: !1, configurable: !0 })) } }(), function() { r.URL||(r.URL=r.webkitURL) }(), function() { if(void 0!==Object.defineProperty) { var e=!0; try { p&&Object.defineProperty(new Image, "id", { value: "test" }); var t=function() { }; t.prototype={ get id() { } }, Object.defineProperty(new t, "id", { value: "", configurable: !0, enumerable: !0, writable: !1 }) } catch(t) { e=!1 } if(e) return } Object.defineProperty=function(e, t, r) { delete e[t], "get" in r&&e.__defineGetter__(t, r.get), "set" in r&&e.__defineSetter__(t, r.set), "value" in r&&(e.__defineSetter__(t, function(e) { return this.__defineGetter__(t, function() { return e }), e }), e[t]=r.value) } }(), function() { if("undefined"!=typeof XMLHttpRequest) { var e=XMLHttpRequest.prototype, t=new XMLHttpRequest; if("overrideMimeType" in t||Object.defineProperty(e, "overrideMimeType", { value: function(e) { } }), !("responseType" in t)) { if(Object.defineProperty(e, "responseType", { get: function() { return this._responseType||"text" }, set: function(e) { "text"!==e&&"arraybuffer"!==e||(this._responseType=e, "arraybuffer"===e&&"function"==typeof this.overrideMimeType&&this.overrideMimeType("text/plain; charset=x-user-defined")) } }), "undefined"!=typeof VBArray) return void Object.defineProperty(e, "response", { get: function() { return "arraybuffer"===this.responseType? new Uint8Array(new VBArray(this.responseBody).toArray()):this.responseText } }); Object.defineProperty(e, "response", { get: function() { if("arraybuffer"!==this.responseType) return this.responseText; var e, t=this.responseText, r=t.length, n=new Uint8Array(r); for(e=0; e<r; ++e)n[e]=255&t.charCodeAt(e); return n.buffer } }) } } }(), function() { if(!("btoa" in r)) { var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="; r.btoa=function(t) { var r, n, i=""; for(r=0, n=t.length; r<n; r+=3) { var a=255&t.charCodeAt(r), o=255&t.charCodeAt(r+1), s=255&t.charCodeAt(r+2), l=a>>2, c=(3&a)<<4|o>>4, u=r+1<n? (15&o)<<2|s>>6:64, d=r+2<n? 63&s:64; i+=e.charAt(l)+e.charAt(c)+e.charAt(u)+e.charAt(d) } return i } } }(), function() { if(!("atob" in r)) { r.atob=function(e) { if(e=e.replace(/=+$/, ""), e.length%4==1) throw new Error("bad atob input"); for(var t, r, n=0, i=0, a=""; r=e.charAt(i++); ~r&&(t=n%4? 64*t+r:r, n++%4)? a+=String.fromCharCode(255&t>>(-2*n&6)):0)r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(r); return a } } }(), function() { void 0===Function.prototype.bind&&(Function.prototype.bind=function(e) { var t=this, r=Array.prototype.slice.call(arguments, 1); return function() { var n=r.concat(Array.prototype.slice.call(arguments)); return t.apply(e, n) } }) }(), function() { if(p) { "dataset" in document.createElement("div")||Object.defineProperty(HTMLElement.prototype, "dataset", { get: function() { if(this._dataset) return this._dataset; for(var e={}, t=0, r=this.attributes.length; t<r; t++) { var n=this.attributes[t]; if("data-"===n.name.substring(0, 5)) { e[n.name.substring(5).replace(/\-([a-z])/g, function(e, t) { return t.toUpperCase() })]=n.value } } return Object.defineProperty(this, "_dataset", { value: e, writable: !1, enumerable: !1 }), e }, enumerable: !0 }) } }(), function() { function e(e, t, r, n) { var i=e.className||"", a=i.split(/\s+/g); ""===a[0]&&a.shift(); var o=a.indexOf(t); return o<0&&r&&a.push(t), o>=0&&n&&a.splice(o, 1), e.className=a.join(" "), o>=0 } if(p) { if(!("classList" in document.createElement("div"))) { var t={ add: function(t) { e(this.element, t, !0, !1) }, contains: function(t) { return e(this.element, t, !1, !1) }, remove: function(t) { e(this.element, t, !1, !0) }, toggle: function(t) { e(this.element, t, !0, !0) } }; Object.defineProperty(HTMLElement.prototype, "classList", { get: function() { if(this._classList) return this._classList; var e=Object.create(t, { element: { value: this, writable: !1, enumerable: !0 } }); return Object.defineProperty(this, "_classList", { value: e, writable: !1, enumerable: !1 }), e }, enumerable: !0 }) } } }(), function() { if(!("undefined"==typeof importScripts||"console" in r)) { var e={}, t={ log: function() { var e=Array.prototype.slice.call(arguments); r.postMessage({ targetName: "main", action: "console_log", data: e }) }, error: function() { var e=Array.prototype.slice.call(arguments); r.postMessage({ targetName: "main", action: "console_error", data: e }) }, time: function(t) { e[t]=Date.now() }, timeEnd: function(t) { var r=e[t]; if(!r) throw new Error("Unknown timer name "+t); this.log("Timer:", t, Date.now()-r) } }; r.console=t } }(), function() { if(p) "console" in window? "bind" in console.log||(console.log=function(e) { return function(t) { return e(t) } }(console.log), console.error=function(e) { return function(t) { return e(t) } }(console.error), console.warn=function(e) { return function(t) { return e(t) } }(console.warn)):window.console={ log: function() { }, error: function() { }, warn: function() { } } }(), function() { function e(e) { t(e.target)&&e.stopPropagation() } function t(e) { return e.disabled||e.parentNode&&t(e.parentNode) } h&&document.addEventListener("click", e, !0) }(), function() { (u||c)&&(PDFJS.disableCreateObjectURL=!0) }(), function() { "undefined"!=typeof navigator&&("language" in navigator||(PDFJS.locale=navigator.userLanguage||"en-US")) }(), function() { (f||a||l||d)&&(PDFJS.disableRange=!0, PDFJS.disableStream=!0) }(), function() { p&&(history.pushState&&!a||(PDFJS.disableHistory=!0)) }(), function() { if(p) if(window.CanvasPixelArray) "function"!=typeof window.CanvasPixelArray.prototype.set&&(window.CanvasPixelArray.prototype.set=function(e) { for(var t=0, r=this.length; t<r; t++)this[t]=e[t] }); else { var e, t=!1; if(s? (e=n.match(/Chrom(e|ium)\/([0-9]+)\./), t=e&&parseInt(e[2])<21):i? t=o:f&&(e=n.match(/Version\/([0-9]+)\.([0-9]+)\.([0-9]+) Safari\//), t=e&&parseInt(e[1])<6), t) { var r=window.CanvasRenderingContext2D.prototype, a=r.createImageData; r.createImageData=function(e, t) { var r=a.call(this, e, t); return r.data.set=function(e) { for(var t=0, r=this.length; t<r; t++)this[t]=e[t] }, r }, r=null } } }(), function() { function e() { window.requestAnimationFrame=function(e) { return window.setTimeout(e, 20) }, window.cancelAnimationFrame=function(e) { window.clearTimeout(e) } } if(p) d? e():"requestAnimationFrame" in window||(window.requestAnimationFrame=window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame, window.requestAnimationFrame||e()) }(), function() { (d||i)&&(PDFJS.maxCanvasPixels=5242880) }(), function() { p&&u&&window.parent!==window&&(PDFJS.disableFullscreen=!0) }(), function() { p&&("currentScript" in document||Object.defineProperty(document, "currentScript", { get: function() { var e=document.getElementsByTagName("script"); return e[e.length-1] }, enumerable: !0, configurable: !0 })) }(), function() { if(p) { var e=document.createElement("input"); try { e.type="number" } catch(n) { var t=e.constructor.prototype, r=Object.getOwnPropertyDescriptor(t, "type"); Object.defineProperty(t, "type", { get: function() { return r.get.call(this) }, set: function(e) { r.set.call(this, "number"===e? "text":e) }, enumerable: !0, configurable: !0 }) } } }(), function() { if(p&&document.attachEvent) { var e=document.constructor.prototype, t=Object.getOwnPropertyDescriptor(e, "readyState"); Object.defineProperty(e, "readyState", { get: function() { var e=t.get.call(this); return "interactive"===e? "loading":e }, set: function(e) { t.set.call(this, e) }, enumerable: !0, configurable: !0 }) } }(), function() { p&&void 0===Element.prototype.remove&&(Element.prototype.remove=function() { this.parentNode&&this.parentNode.removeChild(this) }) }(), function() { if(r.Promise) return "function"!=typeof r.Promise.all&&(r.Promise.all=function(e) { var t, n, i=0, a=[], o=new r.Promise(function(e, r) { t=e, n=r }); return e.forEach(function(e, r) { i++, e.then(function(e) { a[r]=e, 0===--i&&t(a) }, n) }), 0===i&&t(a), o }), "function"!=typeof r.Promise.resolve&&(r.Promise.resolve=function(e) { return new r.Promise(function(t) { t(e) }) }), "function"!=typeof r.Promise.reject&&(r.Promise.reject=function(e) { return new r.Promise(function(t, r) { r(e) }) }), void ("function"!=typeof r.Promise.prototype.catch&&(r.Promise.prototype.catch=function(e) { return r.Promise.prototype.then(void 0, e) })); var e=2, t={ handlers: [], running: !1, unhandledRejections: [], pendingRejectionCheck: !1, scheduleHandlers: function(e) { 0!==e._status&&(this.handlers=this.handlers.concat(e._handlers), e._handlers=[], this.running||(this.running=!0, setTimeout(this.runHandlers.bind(this), 0))) }, runHandlers: function() { for(var t=Date.now()+1; this.handlers.length>0;) { var r=this.handlers.shift(), n=r.thisPromise._status, i=r.thisPromise._value; try { 1===n? "function"==typeof r.onResolve&&(i=r.onResolve(i)):"function"==typeof r.onReject&&(i=r.onReject(i), n=1, r.thisPromise._unhandledRejection&&this.removeUnhandeledRejection(r.thisPromise)) } catch(t) { n=e, i=t } if(r.nextPromise._updateStatus(n, i), Date.now()>=t) break } if(this.handlers.length>0) return void setTimeout(this.runHandlers.bind(this), 0); this.running=!1 }, addUnhandledRejection: function(e) { this.unhandledRejections.push({ promise: e, time: Date.now() }), this.scheduleRejectionCheck() }, removeUnhandeledRejection: function(e) { e._unhandledRejection=!1; for(var t=0; t<this.unhandledRejections.length; t++)this.unhandledRejections[t].promise===e&&(this.unhandledRejections.splice(t), t--) }, scheduleRejectionCheck: function() { var e=this; this.pendingRejectionCheck||(this.pendingRejectionCheck=!0, setTimeout(function() { e.pendingRejectionCheck=!1; for(var t=Date.now(), r=0; r<e.unhandledRejections.length; r++)if(t-e.unhandledRejections[r].time>500) { var n=e.unhandledRejections[r].promise._value, i="Unhandled rejection: "+n; n.stack&&(i+="\n"+n.stack); try { throw new Error(i) } catch(e) { console.warn(i) } e.unhandledRejections.splice(r), r-- } e.unhandledRejections.length&&e.scheduleRejectionCheck() }, 500)) } }, n=function(e) { this._status=0, this._handlers=[]; try { e.call(this, this._resolve.bind(this), this._reject.bind(this)) } catch(e) { this._reject(e) } }; n.all=function(t) { function r(t) { o._status!==e&&(l=[], a(t)) } var i, a, o=new n(function(e, t) { i=e, a=t }), s=t.length, l=[]; if(0===s) return i(l), o; for(var c=0, u=t.length; c<u; ++c) { var d=t[c], h=function(t) { return function(r) { o._status!==e&&(l[t]=r, 0===--s&&i(l)) } }(c); n.isPromise(d)? d.then(h, r):h(d) } return o }, n.isPromise=function(e) { return e&&"function"==typeof e.then }, n.resolve=function(e) { return new n(function(t) { t(e) }) }, n.reject=function(e) { return new n(function(t, r) { r(e) }) }, n.prototype={ _status: null, _value: null, _handlers: null, _unhandledRejection: null, _updateStatus: function(r, i) { if(1!==this._status&&this._status!==e) { if(1===r&&n.isPromise(i)) return void i.then(this._updateStatus.bind(this, 1), this._updateStatus.bind(this, e)); this._status=r, this._value=i, r===e&&0===this._handlers.length&&(this._unhandledRejection=!0, t.addUnhandledRejection(this)), t.scheduleHandlers(this) } }, _resolve: function(e) { this._updateStatus(1, e) }, _reject: function(t) { this._updateStatus(e, t) }, then: function(e, r) { var i=new n(function(e, t) { this.resolve=e, this.reject=t }); return this._handlers.push({ thisPromise: this, onResolve: e, onReject: r, nextPromise: i }), t.scheduleHandlers(this), i }, catch: function(e) { return this.then(void 0, e) } }, r.Promise=n }(), function() { function e() { this.id="$weakmap"+t++ } if(!r.WeakMap) { var t=0; e.prototype={ has: function(e) { return !!Object.getOwnPropertyDescriptor(e, this.id) }, get: function(e, t) { return this.has(e)? e[this.id]:t }, set: function(e, t) { Object.defineProperty(e, this.id, { value: t, enumerable: !1, configurable: !0 }) }, delete: function(e) { delete e[this.id] } }, r.WeakMap=e } }(), function() { function e(e) { return void 0!==h[e] } function n() { l.call(this), this._isInvalid=!0 } function i(e) { return ""===e&&n.call(this), e.toLowerCase() } function a(e) { var t=e.charCodeAt(0); return t>32&&t<127&&-1===[34, 35, 60, 62, 63, 96].indexOf(t)? e:encodeURIComponent(e) } function o(e) { var t=e.charCodeAt(0); return t>32&&t<127&&-1===[34, 35, 60, 62, 96].indexOf(t)? e:encodeURIComponent(e) } function s(t, r, s) { function l(e) { y.push(e) } var c=r||"scheme start", u=0, d="", v=!1, b=!1, y=[]; e: for(; (t[u-1]!==p||0===u)&&!this._isInvalid;) { var A=t[u]; switch(c) { case "scheme start": if(!A||!m.test(A)) { if(r) { l("Invalid scheme."); break e } d="", c="no scheme"; continue } d+=A.toLowerCase(), c="scheme"; break; case "scheme": if(A&&g.test(A)) d+=A.toLowerCase(); else { if(":"!==A) { if(r) { if(A===p) break e; l("Code point not allowed in scheme: "+A); break e } d="", u=0, c="no scheme"; continue } if(this._scheme=d, d="", r) break e; e(this._scheme)&&(this._isRelative=!0), c="file"===this._scheme? "relative":this._isRelative&&s&&s._scheme===this._scheme? "relative or authority":this._isRelative? "authority first slash":"scheme data" } break; case "scheme data": "?"===A? (this._query="?", c="query"):"#"===A? (this._fragment="#", c="fragment"):A!==p&&"\t"!==A&&"\n"!==A&&"\r"!==A&&(this._schemeData+=a(A)); break; case "no scheme": if(s&&e(s._scheme)) { c="relative"; continue } l("Missing scheme."), n.call(this); break; case "relative or authority": if("/"!==A||"/"!==t[u+1]) { l("Expected /, got: "+A), c="relative"; continue } c="authority ignore slashes"; break; case "relative": if(this._isRelative=!0, "file"!==this._scheme&&(this._scheme=s._scheme), A===p) { this._host=s._host, this._port=s._port, this._path=s._path.slice(), this._query=s._query, this._username=s._username, this._password=s._password; break e } if("/"===A||"\\"===A) "\\"===A&&l("\\ is an invalid code point."), c="relative slash"; else if("?"===A) this._host=s._host, this._port=s._port, this._path=s._path.slice(), this._query="?", this._username=s._username, this._password=s._password, c="query"; else { if("#"!==A) { var _=t[u+1], S=t[u+2]; ("file"!==this._scheme||!m.test(A)||":"!==_&&"|"!==_||S!==p&&"/"!==S&&"\\"!==S&&"?"!==S&&"#"!==S)&&(this._host=s._host, this._port=s._port, this._username=s._username, this._password=s._password, this._path=s._path.slice(), this._path.pop()), c="relative path"; continue } this._host=s._host, this._port=s._port, this._path=s._path.slice(), this._query=s._query, this._fragment="#", this._username=s._username, this._password=s._password, c="fragment" } break; case "relative slash": if("/"!==A&&"\\"!==A) { "file"!==this._scheme&&(this._host=s._host, this._port=s._port, this._username=s._username, this._password=s._password), c="relative path"; continue } "\\"===A&&l("\\ is an invalid code point."), c="file"===this._scheme? "file host":"authority ignore slashes"; break; case "authority first slash": if("/"!==A) { l("Expected '/', got: "+A), c="authority ignore slashes"; continue } c="authority second slash"; break; case "authority second slash": if(c="authority ignore slashes", "/"!==A) { l("Expected '/', got: "+A); continue } break; case "authority ignore slashes": if("/"!==A&&"\\"!==A) { c="authority"; continue } l("Expected authority, got: "+A); break; case "authority": if("@"===A) { v&&(l("@ already seen."), d+="%40"), v=!0; for(var w=0; w<d.length; w++) { var P=d[w]; if("\t"!==P&&"\n"!==P&&"\r"!==P) if(":"!==P||null!==this._password) { var x=a(P); null!==this._password? this._password+=x:this._username+=x } else this._password=""; else l("Invalid whitespace in authority.") } d="" } else { if(A===p||"/"===A||"\\"===A||"?"===A||"#"===A) { u-=d.length, d="", c="host"; continue } d+=A } break; case "file host": if(A===p||"/"===A||"\\"===A||"?"===A||"#"===A) { 2!==d.length||!m.test(d[0])||":"!==d[1]&&"|"!==d[1]? 0===d.length? c="relative path start":(this._host=i.call(this, d), d="", c="relative path start"):c="relative path"; continue } "\t"===A||"\n"===A||"\r"===A? l("Invalid whitespace in file host."):d+=A; break; case "host": case "hostname": if(":"!==A||b) { if(A===p||"/"===A||"\\"===A||"?"===A||"#"===A) { if(this._host=i.call(this, d), d="", c="relative path start", r) break e; continue } "\t"!==A&&"\n"!==A&&"\r"!==A? ("["===A? b=!0:"]"===A&&(b=!1), d+=A):l("Invalid code point in host/hostname: "+A) } else if(this._host=i.call(this, d), d="", c="port", "hostname"===r) break e; break; case "port": if(/[0-9]/.test(A)) d+=A; else { if(A===p||"/"===A||"\\"===A||"?"===A||"#"===A||r) { if(""!==d) { var T=parseInt(d, 10); T!==h[this._scheme]&&(this._port=T+""), d="" } if(r) break e; c="relative path start"; continue } "\t"===A||"\n"===A||"\r"===A? l("Invalid code point in port: "+A):n.call(this) } break; case "relative path start": if("\\"===A&&l("'\\' not allowed in path."), c="relative path", "/"!==A&&"\\"!==A) continue; break; case "relative path": if(A!==p&&"/"!==A&&"\\"!==A&&(r||"?"!==A&&"#"!==A)) "\t"!==A&&"\n"!==A&&"\r"!==A&&(d+=a(A)); else { "\\"===A&&l("\\ not allowed in relative path."); var k; (k=f[d.toLowerCase()])&&(d=k), ".."===d? (this._path.pop(), "/"!==A&&"\\"!==A&&this._path.push("")):"."===d&&"/"!==A&&"\\"!==A? this._path.push(""):"."!==d&&("file"===this._scheme&&0===this._path.length&&2===d.length&&m.test(d[0])&&"|"===d[1]&&(d=d[0]+":"), this._path.push(d)), d="", "?"===A? (this._query="?", c="query"):"#"===A&&(this._fragment="#", c="fragment") } break; case "query": r||"#"!==A? A!==p&&"\t"!==A&&"\n"!==A&&"\r"!==A&&(this._query+=o(A)):(this._fragment="#", c="fragment"); break; case "fragment": A!==p&&"\t"!==A&&"\n"!==A&&"\r"!==A&&(this._fragment+=A) }u++ } } function l() { this._scheme="", this._schemeData="", this._username="", this._password=window.$pvaluee, this._host="", this._port="", this._path=[], this._query="", this._fragment="", this._isInvalid=!1, this._isRelative=!1 } function c(e, t) { void 0===t||t instanceof c||(t=new c(String(t))), this._url=e, l.call(this); var r=e.replace(/^[ \t\r\n\f]+|[ \t\r\n\f]+$/g, ""); s.call(this, r, null, t) } var u=!1; try { if("function"==typeof URL&&"object"===t(URL.prototype)&&"origin" in URL.prototype) { var d=new URL("b", "http://a"); d.pathname="c%20d", u="http://a/c%20d"===d.href } } catch(e) { } if(!u) { var h=Object.create(null); h.ftp=21, h.file=0, h.gopher=70, h.http=80, h.https=443, h.ws=80, h.wss=443; var f=Object.create(null); f["%2e"]=".", f[".%2e"]="..", f["%2e."]="..", f["%2e%2e"]=".."; var p, m=/[a-zA-Z]/, g=/[a-zA-Z0-9\+\-\.]/; c.prototype={ toString: function() { return this.href }, get href() { if(this._isInvalid) return this._url; var e=""; return ""===this._username&&null===this._password||(e=this._username+(null!==this._password? ":"+this._password:"")+"@"), this.protocol+(this._isRelative? "//"+e+this.host:"")+this.pathname+this._query+this._fragment }, set href(e) { l.call(this), s.call(this, e) }, get protocol() { return this._scheme+":" }, set protocol(e) { this._isInvalid||s.call(this, e+":", "scheme start") }, get host() { return this._isInvalid? "":this._port? this._host+":"+this._port:this._host }, set host(e) { !this._isInvalid&&this._isRelative&&s.call(this, e, "host") }, get hostname() { return this._host }, set hostname(e) { !this._isInvalid&&this._isRelative&&s.call(this, e, "hostname") }, get port() { return this._port }, set port(e) { !this._isInvalid&&this._isRelative&&s.call(this, e, "port") }, get pathname() { return this._isInvalid? "":this._isRelative? "/"+this._path.join("/"):this._schemeData }, set pathname(e) { !this._isInvalid&&this._isRelative&&(this._path=[], s.call(this, e, "relative path start")) }, get search() { return this._isInvalid||!this._query||"?"===this._query? "":this._query }, set search(e) { !this._isInvalid&&this._isRelative&&(this._query="?", "?"===e[0]&&(e=e.slice(1)), s.call(this, e, "query")) }, get hash() { return this._isInvalid||!this._fragment||"#"===this._fragment? "":this._fragment }, set hash(e) { this._isInvalid||(this._fragment="#", "#"===e[0]&&(e=e.slice(1)), s.call(this, e, "fragment")) }, get origin() { var e; if(this._isInvalid||!this._scheme) return ""; switch(this._scheme) { case "data": case "file": case "javascript": case "mailto": return "null" }return e=this.host, e? this._scheme+"://"+e:"" } }; var v=r.URL; v&&(c.createObjectURL=function(e) { return v.createObjectURL.apply(v, arguments) }, c.revokeObjectURL=function(e) { v.revokeObjectURL(e) }), r.URL=c } }() } }).call(t, r(6)) }])
});