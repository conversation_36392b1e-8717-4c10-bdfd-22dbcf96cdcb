/**************************************************
LEADTOOLS (C) 1991-2017 LEAD Technologies, Inc. ALL RIGHTS RESERVED.
This software is protected by United States and International copyright laws.
Any copying, duplication, deployment, redistribution, modification or other
disposition hereof is STRICTLY PROHIBITED without an express written license
granted by LEAD Technologies, Inc.. This notice may not be removed or otherwise
altered under any circumstances.
Portions of this product are licensed under US patent 5,327,254 and foreign
counterparts.
For more information, contact LEAD Technologies, Inc. at 704-332-5532 or visit
https://www.leadtools.com
**************************************************/
// Leadtools.js
// Version:*********
(function(a){function b(a){this.__typeName=a}a.ss={version:"1.0.0.0",isUndefined:function(a){return void 0===a},isNull:function(a){return null===a},isNullOrUndefined:function(a){return null===a||void 0===a},isValue:function(a){return null!==a&&void 0!==a}};Object.__typeName="Object";Object.__baseType=null;Object.clearKeys=function(a){for(var b in a)delete a[b]};Object.keyExists=function(a,b){return void 0!==a[b]};Object.keys?Object.getKeyCount=function(a){return Object.keys(a).length}:(Object.keys=
function(a){var b=[],c;for(c in a)b.push(c);return b},Object.getKeyCount=function(a){var b=0,c;for(c in a)b++;return b});Boolean.__typeName="Boolean";Boolean.parse=function(a){return a.toLowerCase()=="true"};Number.__typeName="Number";Number.parse=function(a){return!a||!a.length?0:a.indexOf(".")>=0||a.indexOf("e")>=0||a.endsWith("f")||a.endsWith("F")?parseFloat(a):parseInt(a,10)};Number.prototype.format=function(a){return ss.isNullOrUndefined(a)||a.length==0||a=="i"?this.toString():this._netFormat(a,
false)};Number.prototype.localeFormat=function(a){return ss.isNullOrUndefined(a)||a.length==0||a=="i"?this.toLocaleString():this._netFormat(a,true)};Number._commaFormat=function(a,b,c,d){var g=null,c=a.indexOf(c);if(c>0){g=a.substr(c);a=a.substr(0,c)}(c=a.startsWith("-"))&&(a=a.substr(1));var j=0,l=b[j];if(a.length<l)return g?a+g:a;for(var i=a.length,o="",p=false;!p;){var n=l,m=i-n;if(m<0){l=l+m;n=n+m;m=0;p=true}if(!n)break;m=a.substr(m,n);o=o.length?m+d+o:m;i=i-n;if(j<b.length-1){j++;l=b[j]}}c&&
(o="-"+o);return g?o+g:o};Number.prototype._netFormat=function(a,b){var c=b?ss.CultureInfo.CurrentCulture.numberFormat:ss.CultureInfo.InvariantCulture.numberFormat,d="",g=-1;a.length>1&&(g=parseInt(a.substr(1)));var j=a.charAt(0);switch(j){case "d":case "D":d=parseInt(Math.abs(this)).toString();g!=-1&&(d=d.padLeft(g,"0"));this<0&&(d="-"+d);break;case "x":case "X":d=parseInt(Math.abs(this)).toString(16);j=="X"&&(d=d.toUpperCase());g!=-1&&(d=d.padLeft(g,"0"));break;case "e":case "E":d=g==-1?this.toExponential():
this.toExponential(g);j=="E"&&(d=d.toUpperCase());break;case "f":case "F":case "n":case "N":if(g==-1)g=c.numberDecimalDigits;d=this.toFixed(g).toString();if(g&&c.numberDecimalSeparator!="."){g=d.indexOf(".");d=d.substr(0,g)+c.numberDecimalSeparator+d.substr(g+1)}if(j=="n"||j=="N")d=Number._commaFormat(d,c.numberGroupSizes,c.numberDecimalSeparator,c.numberGroupSeparator);break;case "c":case "C":if(g==-1)g=c.currencyDecimalDigits;d=Math.abs(this).toFixed(g).toString();if(g&&c.currencyDecimalSeparator!=
"."){g=d.indexOf(".");d=d.substr(0,g)+c.currencyDecimalSeparator+d.substr(g+1)}d=Number._commaFormat(d,c.currencyGroupSizes,c.currencyDecimalSeparator,c.currencyGroupSeparator);d=this<0?String.format(c.currencyNegativePattern,d):String.format(c.currencyPositivePattern,d);break;case "p":case "P":if(g==-1)g=c.percentDecimalDigits;d=(Math.abs(this)*100).toFixed(g).toString();if(g&&c.percentDecimalSeparator!="."){g=d.indexOf(".");d=d.substr(0,g)+c.percentDecimalSeparator+d.substr(g+1)}d=Number._commaFormat(d,
c.percentGroupSizes,c.percentDecimalSeparator,c.percentGroupSeparator);d=this<0?String.format(c.percentNegativePattern,d):String.format(c.percentPositivePattern,d)}return d};String.__typeName="String";String.Empty="";String.compare=function(a,b,c){if(c){a&&(a=a.toUpperCase());b&&(b=b.toUpperCase())}a=a||"";b=b||"";return a==b?0:a<b?-1:1};String.prototype.compareTo=function(a,b){return String.compare(this,a,b)};String.concat=function(){return arguments.length===2?arguments[0]+arguments[1]:Array.prototype.join.call(arguments,
"")};String.prototype.endsWith=function(a){return!a.length?true:a.length>this.length?false:this.substr(this.length-a.length)==a};String.equals=function(a,b,c){return String.compare(a,b,c)==0};String._format=function(a,b,c){if(!String._formatRE)String._formatRE=/(\{[^\}^\{]+\})/g;return a.replace(String._formatRE,function(a,e){var d=parseInt(e.substr(1)),d=b[d+1];if(ss.isNullOrUndefined(d))return"";if(d.format){var l=null,i=e.indexOf(":");i>0&&(l=e.substring(i+1,e.length-1));return c?d.localeFormat(l):
d.format(l)}return c?d.toLocaleString():d.toString()})};String.format=function(a){return String._format(a,arguments,false)};String.fromChar=function(a,b){for(var c=a,d=1;d<b;d++)c=c+a;return c};String.prototype.htmlDecode=function(){var a=document.createElement("div");a.innerHTML=this;return a.textContent||a.innerText};String.prototype.htmlEncode=function(){var a=document.createElement("div");a.appendChild(document.createTextNode(this));return a.innerHTML.replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,
"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")};String.prototype.indexOfAny=function(a,b,c){var d=this.length;if(!d)return-1;b=b||0;c=b+(c||d)-1;for(c>=d&&(c=d-1);b<=c;b++)if(a.indexOf(this.charAt(b))>=0)return b;return-1};String.prototype.insert=function(a,b){if(!b)return this.valueOf();if(!a)return b+this;var c=this.substr(0,a),d=this.substr(a);return c+b+d};String.isNullOrEmpty=function(a){return!a||!a.length};String.isNullOrWhiteSpace=function(a){return String.isNullOrEmpty(a)||a.trim()===
""};String.prototype.lastIndexOfAny=function(a,b,c){var d=this.length;if(!d)return-1;b=b||d-1;c=b-(c||d)+1;for(c<0&&(c=0);b>=c;b--)if(a.indexOf(this.charAt(b))>=0)return b;return-1};String.localeFormat=function(a){return String._format(a,arguments,true)};String.prototype.padLeft=function(a,b){return this.length<a?String.fromChar(b||" ",a-this.length)+this:this.valueOf()};String.prototype.padRight=function(a,b){return this.length<a?this+String.fromChar(b||" ",a-this.length):this.valueOf()};String.prototype.remove=
function(a,b){return!b||a+b>this.length?this.substr(0,a):this.substr(0,a)+this.substr(a+b)};String.prototype.replaceAll=function(a,b){return this.split(a).join(b||"")};String.prototype.startsWith=function(a){return!a.length?true:a.length>this.length?false:this.substr(0,a.length)==a};String.prototype.trim||(String.prototype.trim=function(){return this.trimEnd().trimStart()});String.prototype.trimEnd=function(){return this.replace(/\s*$/,"")};String.prototype.trimStart=function(){return this.replace(/^\s*/,
"")};Array.__typeName="Array";Array.__interfaces=[ss.IEnumerable];Array.prototype.add=function(a){this[this.length]=a};Array.prototype.addRange=function(a){this.push.apply(this,a)};Array.prototype.clear=function(){this.length=0};Array.prototype.clone=function(){return this.length===1?[this[0]]:Array.apply(null,this)};Array.prototype.contains=function(a){return this.indexOf(a)>=0};Array.prototype.dequeue=function(){return this.shift()};Array.prototype.enqueue=function(a){this._queue=true;this.push(a)};
Array.prototype.peek=function(){return this.length?this[this._queue?0:this.length-1]:null};Array.prototype.every||(Array.prototype.every=function(a,b){for(var c=this.length,d=0;d<c;d++)if(d in this&&!a.call(b,this[d],d,this))return false;return true});Array.prototype.extract=function(a,b){return!b?this.slice(a):this.slice(a,a+b)};Array.prototype.filter||(Array.prototype.filter=function(a,b){for(var c=this.length,d=[],g=0;g<c;g++)if(g in this){var j=this[g];a.call(b,j,g,this)&&d.push(j)}return d});
Array.prototype.forEach||(Array.prototype.forEach=function(a,b){for(var c=this.length,d=0;d<c;d++)d in this&&a.call(b,this[d],d,this)});Array.prototype.getEnumerator=function(){return new ss.ArrayEnumerator(this)};Array.prototype.groupBy=function(a,b){for(var c=this.length,d=[],g={},j=0;j<c;j++)if(j in this){var l=a.call(b,this[j],j);if(!String.isNullOrEmpty(l)){var i=g[l];if(!i){i=[];i.key=l;g[l]=i;d.add(i)}i.add(this[j])}}return d};Array.prototype.index=function(a,b){for(var c=this.length,d={},
g=0;g<c;g++)if(g in this){var j=a.call(b,this[g],g);String.isNullOrEmpty(j)||(d[j]=this[g])}return d};Array.prototype.indexOf||(Array.prototype.indexOf=function(a,b){var c=this.length;if(c)for(var d=b||0;d<c;d++)if(this[d]===a)return d;return-1});Array.prototype.insert=function(a,b){this.splice(a,0,b)};Array.prototype.insertRange=function(a,b){if(a===0)this.unshift.apply(this,b);else for(var c=0;c<b.length;c++)this.splice(a+c,0,b[c])};Array.prototype.lastIndexOf||(Array.prototype.lastIndexOf=function(a,
b){for(var c=b=b||this.length-1;c>=0;c--)if(this[c]===a)return c;return-1});Array.prototype.map||(Array.prototype.map=function(a,b){for(var c=this.length,d=Array(c),g=0;g<c;g++)g in this&&(d[g]=a.call(b,this[g],g,this));return d});Array.prototype.reduce||(Array.prototype.reduce=function(a,b){var c=b,d=this.length;if(d){var g=0;if(arguments.length==1){c=this[0];g++}for(;g<d;g++)c=a(c,this[g],g,this)}return c},Array.prototype.reduceRight=function(a,b){var c=b,d=this.length;if(d){d=d-1;if(arguments.length==
1){c=this[d];d--}for(;d>=0;d--)c=a(c,this[d],d,this)}return c});Array.prototype.remove=function(a){a=this.indexOf(a);if(a>=0){this.splice(a,1);return true}return false};Array.prototype.removeAt=function(a){this.splice(a,1)};Array.prototype.removeRange=function(a,b){return this.splice(a,b)};Array.prototype.some||(Array.prototype.some=function(a,b){for(var c=this.length,d=0;d<c;d++)if(d in this&&a.call(b,this[d],d,this))return true;return false});Array.toArray=function(a){return Array.prototype.slice.call(a)};
RegExp.__typeName="RegExp";RegExp.parse=function(a){if(a.startsWith("/")){var b=a.lastIndexOf("/");if(b>1){var c=a.substring(1,b),a=a.substr(b+1);return RegExp(c,a)}}return null};Date.__typeName="Date";Date.empty=null;Date.get_now=function(){return new Date};Date.get_today=function(){var a=new Date;return new Date(a.getFullYear(),a.getMonth(),a.getDate())};Date.isEmpty=function(a){return a===null||a.valueOf()===0};Date.prototype.format=function(a){return ss.isNullOrUndefined(a)||a.length==0||a=="i"?
this.toString():a=="id"?this.toDateString():a=="it"?this.toTimeString():this._netFormat(a,false)};Date.prototype.localeFormat=function(a){return ss.isNullOrUndefined(a)||a.length==0||a=="i"?this.toLocaleString():a=="id"?this.toLocaleDateString():a=="it"?this.toLocaleTimeString():this._netFormat(a,true)};Date.prototype._netFormat=function(a,b){var c=this,d=b?ss.CultureInfo.CurrentCulture.dateFormat:ss.CultureInfo.InvariantCulture.dateFormat;if(a.length==1)switch(a){case "f":a=d.longDatePattern+" "+
d.shortTimePattern;break;case "F":a=d.dateTimePattern;break;case "d":a=d.shortDatePattern;break;case "D":a=d.longDatePattern;break;case "t":a=d.shortTimePattern;break;case "T":a=d.longTimePattern;break;case "g":a=d.shortDatePattern+" "+d.shortTimePattern;break;case "G":a=d.shortDatePattern+" "+d.longTimePattern;break;case "R":case "r":d=ss.CultureInfo.InvariantCulture.dateFormat;a=d.gmtDateTimePattern;break;case "u":a=d.universalDateTimePattern;break;case "U":a=d.dateTimePattern;c=new Date(c.getUTCFullYear(),
c.getUTCMonth(),c.getUTCDate(),c.getUTCHours(),c.getUTCMinutes(),c.getUTCSeconds(),c.getUTCMilliseconds());break;case "s":a=d.sortableDateTimePattern}a.charAt(0)=="%"&&(a=a.substr(1));if(!Date._formatRE)Date._formatRE=/'.*?[^\\]'|dddd|ddd|dd|d|MMMM|MMM|MM|M|yyyy|yy|y|hh|h|HH|H|mm|m|ss|s|tt|t|fff|ff|f|zzz|zz|z/g;var g=Date._formatRE,j=new ss.StringBuilder;for(g.lastIndex=0;;){var l=g.lastIndex,i=g.exec(a);j.append(a.slice(l,i?i.index:a.length));if(!i)break;i=l=i[0];switch(l){case "dddd":i=d.dayNames[c.getDay()];
break;case "ddd":i=d.shortDayNames[c.getDay()];break;case "dd":i=c.getDate().toString().padLeft(2,"0");break;case "d":i=c.getDate();break;case "MMMM":i=d.monthNames[c.getMonth()];break;case "MMM":i=d.shortMonthNames[c.getMonth()];break;case "MM":i=(c.getMonth()+1).toString().padLeft(2,"0");break;case "M":i=c.getMonth()+1;break;case "yyyy":i=c.getFullYear();break;case "yy":i=(c.getFullYear()%100).toString().padLeft(2,"0");break;case "y":i=c.getFullYear()%100;break;case "h":case "hh":(i=c.getHours()%
12)?l=="hh"&&(i=i.toString().padLeft(2,"0")):i="12";break;case "HH":i=c.getHours().toString().padLeft(2,"0");break;case "H":i=c.getHours();break;case "mm":i=c.getMinutes().toString().padLeft(2,"0");break;case "m":i=c.getMinutes();break;case "ss":i=c.getSeconds().toString().padLeft(2,"0");break;case "s":i=c.getSeconds();break;case "t":case "tt":i=c.getHours()<12?d.amDesignator:d.pmDesignator;l=="t"&&(i=i.charAt(0));break;case "fff":i=c.getMilliseconds().toString().padLeft(3,"0");break;case "ff":i=
c.getMilliseconds().toString().padLeft(3).substr(0,2);break;case "f":i=c.getMilliseconds().toString().padLeft(3).charAt(0);break;case "z":i=c.getTimezoneOffset()/60;i=(i>=0?"-":"+")+Math.floor(Math.abs(i));break;case "zz":case "zzz":i=c.getTimezoneOffset()/60;i=(i>=0?"-":"+")+Math.floor(Math.abs(i)).toString().padLeft(2,"0");l=="zzz"&&(i=i+(d.timeSeparator+Math.abs(c.getTimezoneOffset()%60).toString().padLeft(2,"0")));break;default:i.charAt(0)=="'"&&(i=i.substr(1,i.length-2).replace(/\\'/g,"'"))}j.append(i)}return j.toString()};
Date.parseDate=function(a){return new Date(Date.parse(a))};Error.__typeName="Error";Error.prototype.popStackFrame=function(){if(!ss.isNullOrUndefined(this.stack)&&!ss.isNullOrUndefined(this.fileName)&&!ss.isNullOrUndefined(this.lineNumber)){for(var a=this.stack.split("\n"),b=a[0],c=this.fileName+":"+this.lineNumber;!ss.isNullOrUndefined(b)&&b.indexOf(c)===-1;){a.shift();b=a[0]}b=a[1];if(!isNullOrUndefined(b)){b=b.match(/@(.*):(\d+)$/);if(!ss.isNullOrUndefined(b)){a.shift();this.stack=a.join("\n");
this.fileName=b[1];this.lineNumber=parseInt(b[2])}}}};Error.createError=function(a,b,c){a=Error(a);if(b)for(var d in b)a[d]=b[d];if(c)a.innerException=c;a.popStackFrame();return a};a.Type=Function;Type.__typeName="Type";var c={},d=[];b.prototype={__namespace:!0,getName:function(){return this.__typeName}};Type.registerNamespace=function(e){if(!c[e]){for(var f=a,h=e.split("."),k=0;k<h.length;k++){var g=h[k],j=f[g];if(!j){f[g]=j=new b(h.slice(0,k+1).join("."));k==0&&d.add(j)}f=j}c[e]=f}};Type.prototype.registerClass=
function(a,b,c){this.prototype.constructor=this;this.__typeName=a;this.__class=true;this.__baseType=b||Object;if(b)this.__basePrototypePending=true;if(c){this.__interfaces=[];for(var d=2;d<arguments.length;d++){c=arguments[d];this.__interfaces.add(c)}}};Type.prototype.registerInterface=function(a){this.__typeName=a;this.__interface=true};Type.prototype.registerEnum=function(a,b){for(var c in this.prototype)this[c]=this.prototype[c];this.__typeName=a;this.__enum=true;if(b)this.__flags=true};Type.prototype.setupBase=
function(){if(this.__basePrototypePending){var a=this.__baseType;a.__basePrototypePending&&a.setupBase();for(var b in a.prototype){var c=Object.getOwnPropertyDescriptor(a.prototype,b);c&&(c.get||c.set)?Object.getOwnPropertyDescriptor(this.prototype,b)||Object.defineProperty(this.prototype,b,c):this.prototype[b]||(this.prototype[b]=a.prototype[b])}delete this.__basePrototypePending}};Type.prototype.resolveInheritance||(Type.prototype.resolveInheritance=Type.prototype.setupBase);Type.prototype.initializeBase=
function(a,b){this.__basePrototypePending&&this.setupBase();b?this.__baseType.apply(a,b):this.__baseType.apply(a)};Type.prototype.callBaseMethod=function(a,b,c){b=this.__baseType.prototype[b];return c?b.apply(a,c):b.apply(a)};Type.prototype.get_baseType=function(){return this.__baseType||null};Type.prototype.get_fullName=function(){return this.__typeName};Type.prototype.get_name=function(){var a=this.__typeName,b=a.lastIndexOf(".");return b>0?a.substr(b+1):a};Type.prototype.isInstanceOfType=function(a){return ss.isNullOrUndefined(a)?
false:this==Object||a instanceof this?true:this.isAssignableFrom(Type.getInstanceType(a))};Type.prototype.isAssignableFrom=function(a){if(this==Object||this==a)return true;if(this.__class)for(a=a.__baseType;a;){if(this==a)return true;a=a.__baseType}else if(this.__interface){var b=a.__interfaces;if(b&&b.contains(this))return true;for(a=a.__baseType;a;){if((b=a.__interfaces)&&b.contains(this))return true;a=a.__baseType}}return false};Type.isClass=function(a){return a.__class==true};Type.isInterface=
function(a){return a.__interface==true};Type.canCast=function(a,b){return b.isInstanceOfType(a)};Type.safeCast=function(a,b){return b.isInstanceOfType(a)?a:null};Type.getInstanceType=function(a){var b=null;try{b=a.constructor}catch(c){}if(!b||!b.__typeName)b=Object;return b};Type.getType=function(a){if(!a)return null;if(!Type.__typeCache)Type.__typeCache={};var b=Type.__typeCache[a];if(!b){var b=a.split("."),c=window;b.forEach(function(a){c=c[a]});b=c;Type.__typeCache[a]=b}return b};ss.Delegate=function(){};
ss.Delegate.registerClass("Delegate");ss.Delegate.empty=function(){};ss.Delegate._contains=function(a,b,c){for(var d=0;d<a.length;d=d+2)if(a[d]===b&&a[d+1]===c)return true;return false};ss.Delegate._create=function(a){var b=function(){if(a.length==2)return a[1].apply(a[0],arguments);for(var b=a.clone(),c=0;c<b.length;c=c+2)ss.Delegate._contains(a,b[c],b[c+1])&&b[c+1].apply(b[c],arguments);return null};b._targets=a;return b};ss.Delegate.create=function(a,b){return!a?b:ss.Delegate._create([a,b])};ss.Delegate.combine=
function(a,b){return!a?!b._targets?ss.Delegate.create(null,b):b:!b?!a._targets?ss.Delegate.create(null,a):a:ss.Delegate._create((a._targets?a._targets:[null,a]).concat(b._targets?b._targets:[null,b]))};ss.Delegate.remove=function(a,b){if(!a||a===b)return null;if(!b)return a;var c=a._targets,d=null,g;if(b._targets){d=b._targets[0];g=b._targets[1]}else g=b;for(var j=0;j<c.length;j=j+2)if(c[j]===d&&c[j+1]===g){if(c.length==2)return null;c.splice(j,2);return ss.Delegate._create(c)}return a};ss.Delegate.createExport=
function(b,c,d){d=d||"__"+(new Date).valueOf();a[d]=c?b:function(){try{delete a[d]}catch(c){a[d]=void 0}b.apply(null,arguments)};return d};ss.Delegate.deleteExport=function(b){delete a[b]};ss.Delegate.clearExport=function(b){a[b]=ss.Delegate.empty};ss.CultureInfo=function(a,b,c){this.name=a;this.numberFormat=b;this.dateFormat=c};ss.CultureInfo.registerClass("CultureInfo");ss.CultureInfo.InvariantCulture=new ss.CultureInfo("en-US",{naNSymbol:"NaN",negativeSign:"-",positiveSign:"+",negativeInfinityText:"-Infinity",
positiveInfinityText:"Infinity",percentSymbol:"%",percentGroupSizes:[3],percentDecimalDigits:2,percentDecimalSeparator:".",percentGroupSeparator:",",percentPositivePattern:"{0} %",percentNegativePattern:"-{0} %",currencySymbol:"$",currencyGroupSizes:[3],currencyDecimalDigits:2,currencyDecimalSeparator:".",currencyGroupSeparator:",",currencyNegativePattern:"(${0})",currencyPositivePattern:"${0}",numberGroupSizes:[3],numberDecimalDigits:2,numberDecimalSeparator:".",numberGroupSeparator:","},{amDesignator:"AM",
pmDesignator:"PM",dateSeparator:"/",timeSeparator:":",gmtDateTimePattern:"ddd, dd MMM yyyy HH:mm:ss 'GMT'",universalDateTimePattern:"yyyy-MM-dd HH:mm:ssZ",sortableDateTimePattern:"yyyy-MM-ddTHH:mm:ss",dateTimePattern:"dddd, MMMM dd, yyyy h:mm:ss tt",longDatePattern:"dddd, MMMM dd, yyyy",shortDatePattern:"M/d/yyyy",longTimePattern:"h:mm:ss tt",shortTimePattern:"h:mm tt",firstDayOfWeek:0,dayNames:"Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday".split(","),shortDayNames:"Sun,Mon,Tue,Wed,Thu,Fri,Sat".split(","),
minimizedDayNames:"Su,Mo,Tu,We,Th,Fr,Sa".split(","),monthNames:"January,February,March,April,May,June,July,August,September,October,November,December,".split(","),shortMonthNames:"Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec,".split(",")});ss.CultureInfo.CurrentCulture=ss.CultureInfo.InvariantCulture;ss.IEnumerator=function(){};ss.IEnumerator.prototype={get_current:null,moveNext:null,reset:null};ss.IEnumerator.getEnumerator=function(a){return a?a.getEnumerator?a.getEnumerator():new ss.ArrayEnumerator(a):
null};ss.IEnumerator.registerInterface("IEnumerator");ss.IEnumerable=function(){};ss.IEnumerable.prototype={getEnumerator:null};ss.IEnumerable.registerInterface("IEnumerable");ss.ArrayEnumerator=function(a){this._array=a;this._index=-1;this.current=null};ss.ArrayEnumerator.prototype={moveNext:function(){this._index++;this.current=this._array[this._index];return this._index<this._array.length},reset:function(){this._index=-1;this.current=null}};ss.ArrayEnumerator.registerClass("ArrayEnumerator",null,
ss.IEnumerator);ss.IDisposable=function(){};ss.IDisposable.prototype={dispose:null};ss.IDisposable.registerInterface("IDisposable");ss.StringBuilder=function(a){this._parts=ss.isNullOrUndefined(a)||a===""?[]:[a];this.isEmpty=this._parts.length==0};ss.StringBuilder.prototype={append:function(a){if(!ss.isNullOrUndefined(a)&&a!==""){this._parts.add(a);this.isEmpty=false}return this},appendLine:function(a){this.append(a);this.append("\r\n");this.isEmpty=false;return this},clear:function(){this._parts=
[];this.isEmpty=true},toString:function(a){return this._parts.join(a||"")}};ss.StringBuilder.registerClass("StringBuilder");ss.EventHandler=function(){};ss.EventHandler.registerClass("EventHandler");ss.EventHandler.create=function(a,b,c){var d=new ss.EventHandler;d._target=a;d._addFunction=b;d._removeFunction=c;return d};ss.EventHandler.prototype={_target:null,_addFunction:null,_removeFunction:null,add:function(a){this._target?this._addFunction.call(this._target,a):this._addFunction(a);return a},
remove:function(a){this._target?this._removeFunction.call(this._target,a):this._removeFunction(a)}};ss.Tuple=function(a,b,c){this.first=a;this.second=b;if(arguments.length==3)this.third=c};ss.Tuple.registerClass("Tuple");ss.Observable=function(a){this._v=a;this._observers=null};ss.Observable.prototype={getValue:function(){this._observers=ss.Observable._captureObservers(this._observers);return this._v},setValue:function(a){if(this._v!==a){this._v=a;if(a=this._observers){this._observers=null;ss.Observable._invalidateObservers(a)}}}};
ss.Observable._observerStack=[];ss.Observable._observerRegistration={dispose:function(){ss.Observable._observerStack.pop()}};ss.Observable.registerObserver=function(a){ss.Observable._observerStack.push(a);return ss.Observable._observerRegistration};ss.Observable._captureObservers=function(a){var b=ss.Observable._observerStack,c=b.length;if(c){for(var a=a||[],d=0;d<c;d++){var g=b[d];a.contains(g)||a.push(g)}return a}return null};ss.Observable._invalidateObservers=function(a){for(var b=0,c=a.length;b<
c;b++)a[b].invalidateObserver()};ss.Observable.registerClass("Observable");ss.ObservableCollection=function(a){this._items=a||[];this._observers=null};ss.ObservableCollection.prototype={get_item:function(a){this._observers=ss.Observable._captureObservers(this._observers);return this._items[a]},set_item:function(a,b){this._items[a]=b;this._updated()},get_length:function(){this._observers=ss.Observable._captureObservers(this._observers);return this._items.length},add:function(a){this._items.push(a);
this._updated()},clear:function(){this._items.clear();this._updated()},contains:function(a){return this._items.contains(a)},getEnumerator:function(){this._observers=ss.Observable._captureObservers(this._observers);return this._items.getEnumerator()},indexOf:function(a){return this._items.indexOf(a)},insert:function(a,b){this._items.insert(a,b);this._updated()},remove:function(a){if(this._items.remove(a)){this._updated();return true}return false},removeAt:function(a){this._items.removeAt(a);this._updated()},
toArray:function(){return this._items},_updated:function(){var a=this._observers;if(a){this._observers=null;ss.Observable._invalidateObservers(a)}}};ss.ObservableCollection.registerClass("ObservableCollection",null,ss.IEnumerable);ss.Task=function(a){this._continuations=ss.isValue(a)?(this.status="done",null):(this.status="pending",[]);this.result=a;this.error=null};ss.Task.prototype={get_completed:function(){return this.status!="pending"},continueWith:function(a){if(this._continuations)this._continuations.push(a);
else{var b=this;setTimeout(function(){a(b)},0)}return this},done:function(a){return this.continueWith(function(b){b.status=="done"&&a(b.result)})},fail:function(a){return this.continueWith(function(b){b.status=="failed"&&a(b.error)})},then:function(a,b){return this.continueWith(function(c){c.status=="done"?a(c.result):b(c.error)})},_update:function(a,b){if(this.status=="pending"){if(b){this.error=b;this.status="failed"}else{this.result=a;this.status="done"}var c=this._continuations;this._continuations=
null;for(var d=0,g=c.length;d<g;d++)c[d](this)}}};ss.Task._join=function(a,b){function c(a){if(l.status=="pending"){i++;b?l._update(a):i==g&&l._update(true)}}function d(){l.status=="pending"&&(b?l._update(null):l._update(false))}var a=Array.toArray(a),g=a.length,j=0;if(g>1&&typeof a[0]=="number"){j=a[0];a=a.slice(1);g--}var l=new ss.Task,i=0;j!=0&&setTimeout(d,j);for(j=0;j<g;j++)a[j].continueWith(c);return l};ss.Task.all=function(){return ss.Task._join(arguments,false)};ss.Task.any=function(){return ss.Task._join(arguments,
true)};ss.Task.delay=function(a){var b=new ss.Task;setTimeout(function(){b._update(true)},a);return b};ss.Deferred=function(a){this.task=new ss.Task(a)};ss.Deferred.prototype={resolve:function(a){this.task._update(a)},reject:function(a){this.task._update(null,a||Error())}};ss.Deferred.registerClass("Deferred");ss.Task.registerClass("Task");ss.IApplication=function(){};ss.IApplication.registerInterface("IApplication");ss.IContainer=function(){};ss.IContainer.registerInterface("IContainer");ss.IObjectFactory=
function(){};ss.IObjectFactory.registerInterface("IObjectFactory");ss.IEventManager=function(){};ss.IEventManager.registerInterface("IEventManager");ss.IInitializable=function(){};ss.IInitializable.registerInterface("IInitializable");Type.registerNamespace("lt");lt.InvalidOperationException=function(a){this.message=a;this.stack=Error().stack};lt.InvalidOperationException.prototype=Error();lt.ArgumentNullException=function(a){this.message=String.format("'{0}' cannot be null",a);this.stack=Error().stack};
lt.ArgumentNullException.prototype=Error();lt.ArgumentOutOfRangeException=function(a,b,c){this.message=String.format("{0}\n'{1}'\nActual value:{2}",c,a,b);this.stack=Error().stack};lt.ArgumentOutOfRangeException.prototype=Error();lt.ArgumentException=function(a,b){this.message=b?String.format("{0}\n'{1}'",a,b):a;this.stack=Error().stack};lt.ArgumentException.prototype=Error()})(this);
(function(){Type.registerNamespace("lt");window.lt._lT_VersionNumber=function(){};lt.NotifyLeadCollectionChangedAction=function(){};lt.NotifyLeadCollectionChangedAction.prototype={add:0,remove:1,replace:2,move:3,reset:4};lt.NotifyLeadCollectionChangedAction.registerEnum("lt.NotifyLeadCollectionChangedAction",!1);lt.ImageLoaderUrlMode=function(){};lt.ImageLoaderUrlMode.prototype={imageUrl:0,ajaxDataUrl:1,ajaxXml:2};lt.ImageLoaderUrlMode.registerEnum("lt.ImageLoaderUrlMode",!1);lt.LTDevice=function(){};
lt.LTDevice.prototype={unknown:0,desktop:1,mobile:2,tablet:3};lt.LTDevice.registerEnum("lt.LTDevice",!1);lt.LTOS=function(){};lt.LTOS.prototype={unknown:0,windows:1,mac:2,iOS:3,android:4,linux:5,blackberry:6,webOS:7,windows7:8,windows8:9,windows10:10};lt.LTOS.registerEnum("lt.LTOS",!1);lt.LTBrowser=function(){};lt.LTBrowser.prototype={unknown:0,internetExplorer:1,firefox:2,chrome:3,safari:4,opera:5,android:6,edge:7};lt.LTBrowser.registerEnum("lt.LTBrowser",!1);lt.NotifyLeadCollectionChangedEventArgs=
function(){this._newItems$1=[];this._oldItems$1=[];lt.NotifyLeadCollectionChangedEventArgs.initializeBase(this)};lt.NotifyLeadCollectionChangedEventArgs.create=function(a){var b=new lt.NotifyLeadCollectionChangedEventArgs;b._action$1=a;return b};lt.NotifyLeadCollectionChangedEventArgs.createAdd=function(a){var b=new lt.NotifyLeadCollectionChangedEventArgs;b._action$1=0;b._newStartingIndex$1=a;return b};lt.NotifyLeadCollectionChangedEventArgs.createMove=function(a,b){var c=new lt.NotifyLeadCollectionChangedEventArgs;
c._action$1=3;c._oldStartingIndex$1=a;c._newStartingIndex$1=b;return c};lt.NotifyLeadCollectionChangedEventArgs.createRemove=function(a){var b=new lt.NotifyLeadCollectionChangedEventArgs;b._action$1=1;b._oldStartingIndex$1=a;b._newStartingIndex$1=-1;return b};lt.NotifyLeadCollectionChangedEventArgs.createReplace=function(a){var b=new lt.NotifyLeadCollectionChangedEventArgs;b._action$1=2;b._oldStartingIndex$1=a;b._newStartingIndex$1=a;return b};lt.NotifyLeadCollectionChangedEventArgs.prototype={_action$1:0,
get_action:function(){return this._action$1},get_newItems:function(){return this._newItems$1},get_oldItems:function(){return this._oldItems$1},_newStartingIndex$1:0,get_newStartingIndex:function(){return this._newStartingIndex$1},_oldStartingIndex$1:0,get_oldStartingIndex:function(){return this._oldStartingIndex$1}};Object.defineProperty(lt.NotifyLeadCollectionChangedEventArgs.prototype,"action",{get:lt.NotifyLeadCollectionChangedEventArgs.prototype.get_action,enumerable:!0,configurable:!0});Object.defineProperty(lt.NotifyLeadCollectionChangedEventArgs.prototype,
"newItems",{get:lt.NotifyLeadCollectionChangedEventArgs.prototype.get_newItems,enumerable:!0,configurable:!0});Object.defineProperty(lt.NotifyLeadCollectionChangedEventArgs.prototype,"oldItems",{get:lt.NotifyLeadCollectionChangedEventArgs.prototype.get_oldItems,enumerable:!0,configurable:!0});Object.defineProperty(lt.NotifyLeadCollectionChangedEventArgs.prototype,"newStartingIndex",{get:lt.NotifyLeadCollectionChangedEventArgs.prototype.get_newStartingIndex,enumerable:!0,configurable:!0});Object.defineProperty(lt.NotifyLeadCollectionChangedEventArgs.prototype,
"oldStartingIndex",{get:lt.NotifyLeadCollectionChangedEventArgs.prototype.get_oldStartingIndex,enumerable:!0,configurable:!0});lt.LeadCollection=function(){this._elements=[]};lt.LeadCollection.prototype={get_count:function(){return this._elements.length},clear:function(){this.clearItems()},remove:function(a){this.removeItem(this.indexOf(a))},add:function(a){this.insertItem(this.get_count(),a)},contains:function(a){return this._elements.contains(a)},getEnumerator:function(){return this._elements.getEnumerator()},
_copyTo:function(a,b,c){for(var d=0,b=ss.IEnumerator.getEnumerator(b);b.moveNext();)d<c||(a[d]=b.current,d++)},toArray:function(){var a=Array(this._elements.length);this._copyTo(a,this._elements,0);return a},insertItem:function(a,b){var c=lt.NotifyLeadCollectionChangedEventArgs.createAdd(-1!==a?a:this.get_count());c.get_newItems().add(b);this._elements.insert(a,b);this.onCollectionChanged(c)},move:function(a,b){this.moveItem(a,b)},insert:function(a,b){this.insertItem(a,b)},moveItem:function(a,b){var c=
this._elements[a];this.removeItem(a);this.insertItem(b,c);var d=lt.NotifyLeadCollectionChangedEventArgs.createMove(a,b);d.get_newItems().add(c);d.get_oldItems().add(c);this.onCollectionChanged(d)},onCollectionChanged:function(a){null!=this.__collectionChanged&&this.__collectionChanged(this,a)},removeAt:function(a){this.removeItem(a)},removeItem:function(a){var b=lt.NotifyLeadCollectionChangedEventArgs.createRemove(a),c=this._elements[a];b.get_newItems().add(c);this._elements.removeAt(a);this.onCollectionChanged(b)},
setItem:function(a,b){var c=this._elements[a];this._elements[a]=b;var d=lt.NotifyLeadCollectionChangedEventArgs.createReplace(a);d.get_oldItems().add(c);d.get_newItems().add(b);this.onCollectionChanged(d)},clearItems:function(){for(var a=lt.NotifyLeadCollectionChangedEventArgs.create(4),b=ss.IEnumerator.getEnumerator(this._elements);b.moveNext();){var c=b.current;a.get_newItems().add(c)}this._elements.clear();this.onCollectionChanged(a)},indexOf:function(a){return this._elements.indexOf(a)},add_collectionChanged:function(a){this.__collectionChanged=
ss.Delegate.combine(this.__collectionChanged,a)},remove_collectionChanged:function(a){this.__collectionChanged=ss.Delegate.remove(this.__collectionChanged,a)},__collectionChanged_handler_get:function(){null==this.__collectionChanged_handler&&(this.__collectionChanged_handler=ss.EventHandler.create(this,this.add_collectionChanged,this.remove_collectionChanged));return this.__collectionChanged_handler},__collectionChanged:null,__collectionChanged_handler:null,get_item:function(a){return this._elements[a]},
set_item:function(a,b){return this._elements[a]=b},item:function(a,b){return void 0===b?this.get_item(a):this.set_item(a,b)}};Object.defineProperty(lt.LeadCollection.prototype,"count",{get:lt.LeadCollection.prototype.get_count,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadCollection.prototype,"collectionChanged",{get:lt.LeadCollection.prototype.__collectionChanged_handler_get,enumerable:!0,configurable:!0});lt.LeadEventArgs=function(){};lt.LeadEventArgs.addToEvent=function(a,b,c){a["__"+
b]=ss.Delegate.combine(a["__"+b],c)};lt.LeadEventArgs.removeFromEvent=function(a,b,c){a["__"+b]=ss.Delegate.remove(a["__"+b],c)};lt.LeadEvent=function(){};lt.LeadEvent.create=function(a,b){if(String.isNullOrWhiteSpace(b))throw Error("'eventName' cannot be null or empty");if(null==a)throw Error("'target' cannot be null");var c=b+"_leadEventHandler",d=b+"_leadEvent";if(a[c]||a[d])throw Error("Event '"+d+"' already defined");a[c]=null;a[d]=ss.EventHandler.create(a,function(b){a[c]=ss.Delegate.combine(a[c],
b);a[d].handler=a[c]},function(b){a[c]=ss.Delegate.remove(a[c],b);a[d].handler=a[c]});a[d].invoke=function(a,b){null!=this.handler&&this.handler(a,b)};return a[d]};lt.LeadEvent.prototype={invoke:function(a,b){null!=this.handler&&this.handler(a,b)}};lt.ImageProcessingProgressEventArgs=function(){lt.ImageProcessingProgressEventArgs.initializeBase(this)};lt.ImageProcessingProgressEventArgs.create=function(a){var b=new lt.ImageProcessingProgressEventArgs;b._percentage$1=a;b._cancel$1=!1;return b};lt.ImageProcessingProgressEventArgs.prototype=
{_percentage$1:0,get_percentage:function(){return this._percentage$1},_cancel$1:!1,get_cancel:function(){return this._cancel$1},set_cancel:function(a){return this._cancel$1=a}};Object.defineProperty(lt.ImageProcessingProgressEventArgs.prototype,"percentage",{get:lt.ImageProcessingProgressEventArgs.prototype.get_percentage,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageProcessingProgressEventArgs.prototype,"cancel",{get:lt.ImageProcessingProgressEventArgs.prototype.get_cancel,set:lt.ImageProcessingProgressEventArgs.prototype.set_cancel,
enumerable:!0,configurable:!0});lt.ImageProcessingCompletedEventArgs=function(){lt.ImageProcessingCompletedEventArgs.initializeBase(this)};lt.ImageProcessingCompletedEventArgs.create=function(a,b){var c=new lt.ImageProcessingCompletedEventArgs;c._imageData$1=a;c._results$1=b;return c};lt.ImageProcessingCompletedEventArgs.prototype={_imageData$1:null,get_imageData:function(){return this._imageData$1},_results$1:null,get_results:function(){return this._results$1}};Object.defineProperty(lt.ImageProcessingCompletedEventArgs.prototype,
"imageData",{get:lt.ImageProcessingCompletedEventArgs.prototype.get_imageData,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageProcessingCompletedEventArgs.prototype,"results",{get:lt.ImageProcessingCompletedEventArgs.prototype.get_results,enumerable:!0,configurable:!0});lt.ImageProcessingErrorEventArgs=function(){lt.ImageProcessingErrorEventArgs.initializeBase(this)};lt.ImageProcessingErrorEventArgs.create=function(a){var b=new lt.ImageProcessingErrorEventArgs;b._error$1=a;return b};
lt.ImageProcessingErrorEventArgs.prototype={_error$1:null,get_error:function(){return this._error$1}};Object.defineProperty(lt.ImageProcessingErrorEventArgs.prototype,"error",{get:lt.ImageProcessingErrorEventArgs.prototype.get_error,enumerable:!0,configurable:!0});lt.ImageProcessing=function(){this._arguments={}};lt.ImageProcessing._getFileName=function(a){var b=a.lastIndexOf("/");-1!==b&&(a=a.substr(b+1));b=a.lastIndexOf("\\");-1!==b&&(a=a.substr(b+1));return a=a.substr(0,a.length-3)};lt.ImageProcessing.prototype=
{_jsFilePath:null,get_jsFilePath:function(){return this._jsFilePath},set_jsFilePath:function(a){return this._jsFilePath=a},_command:null,get_command:function(){return this._command},set_command:function(a){return this._command=a},_imageData:null,get_imageData:function(){return this._imageData},set_imageData:function(a){return this._imageData=a},_arguments:null,get_arguments:function(){return this._arguments},add_progress:function(a){this.__progress=ss.Delegate.combine(this.__progress,a)},remove_progress:function(a){this.__progress=
ss.Delegate.remove(this.__progress,a)},__progress_handler_get:function(){null==this.__progress_handler&&(this.__progress_handler=ss.EventHandler.create(this,this.add_progress,this.remove_progress));return this.__progress_handler},__progress:null,__progress_handler:null,add_completed:function(a){this.__completed=ss.Delegate.combine(this.__completed,a)},remove_completed:function(a){this.__completed=ss.Delegate.remove(this.__completed,a)},__completed_handler_get:function(){null==this.__completed_handler&&
(this.__completed_handler=ss.EventHandler.create(this,this.add_completed,this.remove_completed));return this.__completed_handler},__completed:null,__completed_handler:null,add_error:function(a){this.__error=ss.Delegate.combine(this.__error,a)},remove_error:function(a){this.__error=ss.Delegate.remove(this.__error,a)},__error_handler_get:function(){null==this.__error_handler&&(this.__error_handler=ss.EventHandler.create(this,this.add_error,this.remove_error));return this.__error_handler},__error:null,
__error_handler:null,_isRunning:!1,get_isRunning:function(){return this._isRunning},_workerOnMessageCallback:null,_workerOnErrorCallback:null,_worker:null,_abort:!1,abort:function(){this._isRunning&&(this._abort=!0,null!=this._worker&&(this._clearWorkerEvents(this._worker),this._worker.terminate(),this._worker=null),this._isRunning=!1)},_clearWorkerEvents:function(a){null!=a&&(null!=this._workerOnMessageCallback&&this._worker.removeEventListener("message",this._workerOnMessageCallback,!1),this._workerOnMessageCallback=
null,null!=this._workerOnErrorCallback&&this._worker.removeEventListener("error",this._workerOnErrorCallback,!1),this._workerOnErrorCallback=null)},_runInMainThread:!1,get_runInMainThread:function(){return this._runInMainThread},set_runInMainThread:function(a){return this._runInMainThread=a},run:function(){if(null==this._command)throw Error("'Command' must be set before calling this method");if(null==this._imageData)throw Error("'ImageData' must be set before calling this method");if(null==this._jsFilePath)throw Error("'JSFilePath' must be set before calling this method");
this._abort=this._isRunning=!1;var a=this._worker=null,b=null,c=null,d=!1,e=null,f=null;if(lt.LTHelper.supportsWebWorker&&!this.get_runInMainThread())b=new Worker(this._jsFilePath),c=b.postMessage;else{var h=lt.ImageProcessing._getFileName(this._jsFilePath).replaceAll(".","_")+"_Main",c=window[h];if(null==c){this._isRunning=!0;lt.LTHelper.loadJS(this._jsFilePath,ss.Delegate.create(this,function(){this._isRunning=!1;c=window[h];d=null!=this.__progress;e=ss.Delegate.create(this,function(a){var a=a.data?
a.data:a,c=a.status;"Progress"===c&&d?(null!=this.__progress&&(c=lt.ImageProcessingProgressEventArgs.create(a.percentage),this.__progress(this,c),c.get_cancel()&&(this._abort=!0)),this._abort&&(null!=b?this.abort():a.abort=!0)):"Completed"===c&&(c=a.imageData,this._clearWorkerEvents(this._worker),null!=this.__completed&&(c=lt.ImageProcessingCompletedEventArgs.create(c,a),this.__completed(this,c)),this._worker=null,this._isRunning=!1)});f={imageData:this._imageData,callback:null==b?e:null,useProgress:d,
command:this._command};a=this._arguments;if(0<Object.getKeyCount(a))for(var g=ss.IEnumerator.getEnumerator(Object.keys(a));g.moveNext();){var i=g.current;f[i]=a[i]}this._isRunning=!0;this._abort=!1;this._worker=b;if(null==c)throw Error("Could not find Image Processing main entry function:"+h);c(f)}));return}}var d=null!=this.__progress,e=ss.Delegate.create(this,function(a){var a=a.data?a.data:a,c=a.status;"Progress"===c&&d?(null!=this.__progress&&(c=lt.ImageProcessingProgressEventArgs.create(a.percentage),
this.__progress(this,c),c.get_cancel()&&(this._abort=!0)),this._abort&&(null!=b?this.abort():a.abort=!0)):"Completed"===c&&(this._clearWorkerEvents(this._worker),this._abort||(c=a.imageData,null!=this.__completed&&(c=lt.ImageProcessingCompletedEventArgs.create(c,a),this.__completed(this,c))),this._worker=null,this._isRunning=!1)}),k=null;null!=b&&(k=ss.Delegate.create(this,function(a){null!=this.__error&&(a=!ss.isNullOrUndefined(a.message)?a.message:"Could not run image processing command. Make sure the library path is set",
this.__error(this,lt.ImageProcessingErrorEventArgs.create(Error(a))));this.abort()}));f={imageData:this._imageData,callback:null==b?e:null,useProgress:d,command:this._command};a=this._arguments;if(0<Object.getKeyCount(a))for(var g=ss.IEnumerator.getEnumerator(Object.keys(a));g.moveNext();){var j=g.current;f[j]=a[j]}4===lt.LTHelper.browser&&(f.width=this._imageData.width,f.height=this._imageData.height);this._isRunning=!0;this._abort=!1;this._worker=b;null!=b?(this._workerOnMessageCallback=e,this._workerOnErrorCallback=
k,b.addEventListener("message",e,!1),b.addEventListener("error",k,!1),b.postMessage(f)):c(f)}};Object.defineProperty(lt.ImageProcessing.prototype,"jsFilePath",{get:lt.ImageProcessing.prototype.get_jsFilePath,set:lt.ImageProcessing.prototype.set_jsFilePath,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageProcessing.prototype,"command",{get:lt.ImageProcessing.prototype.get_command,set:lt.ImageProcessing.prototype.set_command,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageProcessing.prototype,
"imageData",{get:lt.ImageProcessing.prototype.get_imageData,set:lt.ImageProcessing.prototype.set_imageData,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageProcessing.prototype,"arguments",{get:lt.ImageProcessing.prototype.get_arguments,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageProcessing.prototype,"progress",{get:lt.ImageProcessing.prototype.__progress_handler_get,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageProcessing.prototype,"completed",{get:lt.ImageProcessing.prototype.__completed_handler_get,
enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageProcessing.prototype,"error",{get:lt.ImageProcessing.prototype.__error_handler_get,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageProcessing.prototype,"isRunning",{get:lt.ImageProcessing.prototype.get_isRunning,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageProcessing.prototype,"runInMainThread",{get:lt.ImageProcessing.prototype.get_runInMainThread,set:lt.ImageProcessing.prototype.set_runInMainThread,enumerable:!0,
configurable:!0});lt.LeadLengthD=function(){};lt.LeadLengthD.create=function(a){var b=new lt.LeadLengthD;b._value=a;return b};lt.LeadLengthD.equals=function(a,b){return lt.LeadDoubleTools.areClose(a.get_value(),b.get_value())};lt.LeadLengthD.fromJSON=function(a){var b=new lt.LeadLengthD;b._value=a.value;return b};lt.LeadLengthD.prototype={_value:0,clone:function(){return lt.LeadLengthD.create(this._value)},get_value:function(){return this._value},set_value:function(a){return this._value=a},toString:function(){return this._value.toString()},
toJSON:function(){return{value:this._value.toString()}}};Object.defineProperty(lt.LeadLengthD.prototype,"value",{get:lt.LeadLengthD.prototype.get_value,set:lt.LeadLengthD.prototype.set_value,enumerable:!0,configurable:!0});lt.LeadDoubleTools=function(){};lt.LeadDoubleTools.areClose=function(a,b){if(a===b)return!0;var c=2.22044604925031E-16*(Math.abs(a)+Math.abs(b)+10),d=a-b;return-c<d&&c>d};lt.LeadDoubleTools.lessThan=function(a,b){return a<b&&!lt.LeadDoubleTools.areClose(a,b)};lt.LeadDoubleTools.greaterThan=
function(a,b){return a>b&&!lt.LeadDoubleTools.areClose(a,b)};lt.LeadDoubleTools.lessThanOrClose=function(a,b){return a<b||lt.LeadDoubleTools.areClose(a,b)};lt.LeadDoubleTools.greaterThanOrClose=function(a,b){return a>b||lt.LeadDoubleTools.areClose(a,b)};lt.LeadDoubleTools.isOne=function(a){return 2.22044604925031E-15>Math.abs(a-1)};lt.LeadDoubleTools.isZero=function(a){return 2.22044604925031E-15>Math.abs(a)};lt.LeadDoubleTools.areClosePoints=function(a,b){return lt.LeadDoubleTools.areClose(a.get_x(),
b.get_x())&&lt.LeadDoubleTools.areClose(a.get_y(),b.get_y())};lt.LeadDoubleTools.areCloseSizes=function(a,b){return lt.LeadDoubleTools.areClose(a.get_width(),b.get_width())&&lt.LeadDoubleTools.areClose(a.get_height(),b.get_height())};lt.LeadDoubleTools.areCloseRects=function(a,b){return a.get_isEmpty()?b.get_isEmpty():!b.get_isEmpty()&&lt.LeadDoubleTools.areClose(a.get_x(),b.get_x())&&lt.LeadDoubleTools.areClose(a.get_y(),b.get_y())&&lt.LeadDoubleTools.areClose(a.get_height(),b.get_height())&&lt.LeadDoubleTools.areClose(a.get_width(),
b.get_width())};lt.LeadDoubleTools.isBetweenZeroAndOne=function(a){return lt.LeadDoubleTools.greaterThanOrClose(a,0)&&lt.LeadDoubleTools.lessThanOrClose(a,1)};lt.LeadDoubleTools.doubleToInt=function(a){return 0>=a?parseInt(a-0.5):parseInt(a+0.5)};lt.LeadDoubleTools.rectHasNaN=function(a){return lt.LeadDoubleTools.isNaN(a.get_x())||lt.LeadDoubleTools.isNaN(a.get_y())||lt.LeadDoubleTools.isNaN(a.get_height())||lt.LeadDoubleTools.isNaN(a.get_width())};lt.LeadDoubleTools.isNaN=function(a){return isNaN(a)};
lt.LeadDoubleTools.isInfinity=function(a){return a===lt.LeadDoubleTools.positiveInfinity||a===lt.LeadDoubleTools.negativeInfinity};lt.LeadDoubleTools.normalizeAngle=function(a){for(;360<a;)a-=360;for(;0>a;)a+=360;return a};lt.LeadMatrix=function(){};lt.LeadMatrix.create=function(a,b,c,d,e,f){var h=new lt.LeadMatrix;h._m11=a;h._m12=b;h._m21=c;h._m22=d;h._offsetX=e;h._offsetY=f;h._type=4;h._padding=0;h._deriveMatrixType();return h};lt.LeadMatrix.get_identity=function(){return lt.LeadMatrix._createIdentity()};
lt.LeadMatrix.multiply=function(a,b){return lt._leadMatrixUtil._multiplyMatrix(a,b)};lt.LeadMatrix._createRotationRadians=function(a){return lt.LeadMatrix._createRotationAtPointRadians(a,0,0)};lt.LeadMatrix._createRotationAtPointRadians=function(a,b,c){var d=new lt.LeadMatrix,e=Math.sin(a),a=Math.cos(a);d._setMatrix(a,e,-e,a,b*(1-a)+c*e,c*(1-a)-b*e,4);return d};lt.LeadMatrix._createScalingAtPoint=function(a,b,c,d){var e=new lt.LeadMatrix;e._setMatrix(a,0,0,b,c-a*c,d-b*d,3);return e};lt.LeadMatrix._createScaling=
function(a,b){var c=new lt.LeadMatrix;c._setMatrix(a,0,0,b,0,0,2);return c};lt.LeadMatrix._createSkewRadians=function(a,b){var c=new lt.LeadMatrix;c._setMatrix(1,Math.tan(b),Math.tan(a),1,0,0,4);return c};lt.LeadMatrix._createTranslation=function(a,b){var c=new lt.LeadMatrix;c._setMatrix(1,0,0,1,a,b,1);return c};lt.LeadMatrix._createIdentity=function(){var a=new lt.LeadMatrix;a._setMatrix(1,0,0,1,0,0,0);return a};lt.LeadMatrix.equals=function(a,b){return a.get__isDistinguishedIdentity()||b.get__isDistinguishedIdentity()?
a.get_isIdentity()===b.get_isIdentity():a.get_m11()===b.get_m11()&&a.get_m12()===b.get_m12()&&a.get_m21()===b.get_m21()&&a.get_m22()===b.get_m22()&&a.get_offsetX()===b.get_offsetX()&&a.get_offsetY()===b.get_offsetY()};lt.LeadMatrix.fromJSON=function(a){var b=new lt.LeadMatrix;b._m11=a.m11;b._m12=a.m12;b._m21=a.m21;b._m22=a.m22;b._offsetX=a.offsetX;b._offsetY=a.offsetY;return b};lt.LeadMatrix.prototype={_m11:0,_m12:0,_m21:0,_m22:0,_offsetX:0,_offsetY:0,_type:0,_padding:0,clone:function(){var a=new lt.LeadMatrix;
a._m11=this._m11;a._m12=this._m12;a._m21=this._m21;a._m22=this._m22;a._offsetX=this._offsetX;a._offsetY=this._offsetY;a._type=this._type;a._padding=0;return a},_copyFrom:function(a){this._m11=a._m11;this._m12=a._m12;this._m21=a._m21;this._m22=a._m22;this._offsetX=a._offsetX;this._offsetY=a._offsetY;this._type=a._type;this._padding=0},get_isIdentity:function(){return!this._type||1===this._m11&&0===this._m12&&0===this._m21&&1===this._m22&&0===this._offsetX&&0===this._offsetY},get_determinant:function(){switch(this._type){case 0:case 1:return 1;
case 2:case 3:return this._m11*this._m22;default:return this._m11*this._m22-this._m12*this._m21}},get_hasInverse:function(){return!lt.LeadDoubleTools.isZero(this.get_determinant())},get_m11:function(){return!this._type?1:this._m11},set_m11:function(a){if(this._type)return this._m11=a,4!==this._type&&(this._type|=2),a;this._setMatrix(a,0,0,1,0,0,2)},get_m12:function(){return!this._type?0:this._m12},set_m12:function(a){if(this._type)return this._m12=a,this._type=4,a;this._setMatrix(1,a,0,1,0,0,4)},
get_m21:function(){return!this._type?0:this._m21},set_m21:function(a){if(this._type)return this._m21=a,this._type=4,a;this._setMatrix(1,0,a,1,0,0,4)},get_m22:function(){return!this._type?1:this._m22},set_m22:function(a){if(this._type)return this._m22=a,4!==this._type&&(this._type|=2),a;this._setMatrix(1,0,0,a,0,0,2)},get_offsetX:function(){return!this._type?0:this._offsetX},set_offsetX:function(a){if(this._type)return this._offsetX=a,4!==this._type&&(this._type|=1),a;this._setMatrix(1,0,0,1,a,0,1)},
get_offsetY:function(){return!this._type?0:this._offsetY},set_offsetY:function(a){if(this._type)return this._offsetY=a,4!==this._type&&(this._type|=1),a;this._setMatrix(1,0,0,1,0,a,1)},get__isDistinguishedIdentity:function(){return!this._type},setIdentity:function(){this._type=0},append:function(a){this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(this,a))},prepend:function(a){this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(a,this))},rotate:function(a){a=lt.LeadDoubleTools.normalizeAngle(a);this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(this,
lt.LeadMatrix._createRotationRadians(0.0174532925199433*a)))},rotatePrepend:function(a){a=lt.LeadDoubleTools.normalizeAngle(a);this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(lt.LeadMatrix._createRotationRadians(0.0174532925199433*a),this))},rotateAt:function(a,b,c){a=lt.LeadDoubleTools.normalizeAngle(a);this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(this,lt.LeadMatrix._createRotationAtPointRadians(0.0174532925199433*a,b,c)))},rotateAtPrepend:function(a,b,c){a=lt.LeadDoubleTools.normalizeAngle(a);
this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(lt.LeadMatrix._createRotationAtPointRadians(0.0174532925199433*a,b,c),this))},scale:function(a,b){this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(this,lt.LeadMatrix._createScaling(a,b)))},scalePrepend:function(a,b){this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(lt.LeadMatrix._createScaling(a,b),this))},scaleAt:function(a,b,c,d){this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(this,lt.LeadMatrix._createScalingAtPoint(a,b,c,d)))},scaleAtPrepend:function(a,
b,c,d){this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(lt.LeadMatrix._createScalingAtPoint(a,b,c,d),this))},skew:function(a,b){a=lt.LeadDoubleTools.normalizeAngle(a);b=lt.LeadDoubleTools.normalizeAngle(b);this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(this,lt.LeadMatrix._createSkewRadians(0.0174532925199433*a,0.0174532925199433*b)))},skewPrepend:function(a,b){a=lt.LeadDoubleTools.normalizeAngle(a);b=lt.LeadDoubleTools.normalizeAngle(b);this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(lt.LeadMatrix._createSkewRadians(0.0174532925199433*
a,0.0174532925199433*b),this))},translate:function(a,b){this._type?4===this._type?(this._offsetX+=a,this._offsetY+=b):(this._offsetX+=a,this._offsetY+=b,this._type|=1):this._setMatrix(1,0,0,1,a,b,1)},translatePrepend:function(a,b){this._copyFrom(lt._leadMatrixUtil._multiplyMatrix(lt.LeadMatrix._createTranslation(a,b),this))},transformPoint:function(a){return this._multiplyPoint(a,!1)},transformVector:function(a){return this._multiplyPoint(a,!0)},transformRect:function(a){return lt._leadMatrixUtil._transformRect(a,
this)},transformPoints:function(a){if(null!=a)for(var b=0;b<a.length;b++)a[b]=this._multiplyPoint(a[b],!1)},invert:function(){var a=this.get_determinant();if(lt.LeadDoubleTools.isZero(a))throw Error("Transform is not invertible");switch(this._type){case 0:break;case 1:this._offsetX=-this._offsetX;this._offsetY=-this._offsetY;break;case 2:this._m11=1/this._m11;this._m22=1/this._m22;break;case 3:this._m11=1/this._m11;this._m22=1/this._m22;this._offsetX=-this._offsetX*this._m11;this._offsetY=-this._offsetY*
this._m22;break;default:a=1/a,this._setMatrix(this._m22*a,-this._m12*a,-this._m21*a,this._m11*a,(this._m21*this._offsetY-this._offsetX*this._m22)*a,(this._offsetX*this._m12-this._m11*this._offsetY)*a,4)}},_multiplyPoint:function(a,b){var c=a.clone();switch(this._type){case 0:return c;case 1:return b||(c._x+=this._offsetX,c._y+=this._offsetY),c;case 2:return c._x*=this._m11,c._y*=this._m22,c;case 3:return c._x*=this._m11,c._y*=this._m22,b||(c._x+=this._offsetX,c._y+=this._offsetY),c;default:var d=
c._y*this._m21,e=c._x*this._m12;b||(d+=this._offsetX,e+=this._offsetY);c._x*=this._m11;c._x+=d;c._y*=this._m22;c._y+=e;return c}},_setMatrix:function(a,b,c,d,e,f,h){this._m11=a;this._m12=b;this._m21=c;this._m22=d;this._offsetX=e;this._offsetY=f;this._type=h},_deriveMatrixType:function(){this._type=0;if(0!==this._m21||0!==this._m12)this._type=4;else{if(1!==this._m11||1!==this._m22)this._type=2;if(0!==this._offsetX||0!==this._offsetY)this._type|=1;this._type&3||(this._type=0)}},toString:function(){return this.get_isIdentity()?
"Identity":String.format("{0},{1},{2},{3},{4},{5}",this._m11,this._m12,this._m21,this._m22,this._offsetX,this._offsetY)},toJSON:function(){return{m11:this._m11.toString(),m12:this._m12.toString(),m21:this._m21.toString(),m22:this._m22.toString(),offsetX:this._offsetX.toString(),offsetY:this._offsetY.toString()}}};Object.defineProperty(lt.LeadMatrix,"identity",{get:lt.LeadMatrix.get_identity,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadMatrix.prototype,"isIdentity",{get:lt.LeadMatrix.prototype.get_isIdentity,
enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadMatrix.prototype,"determinant",{get:lt.LeadMatrix.prototype.get_determinant,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadMatrix.prototype,"hasInverse",{get:lt.LeadMatrix.prototype.get_hasInverse,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadMatrix.prototype,"m11",{get:lt.LeadMatrix.prototype.get_m11,set:lt.LeadMatrix.prototype.set_m11,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadMatrix.prototype,
"m12",{get:lt.LeadMatrix.prototype.get_m12,set:lt.LeadMatrix.prototype.set_m12,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadMatrix.prototype,"m21",{get:lt.LeadMatrix.prototype.get_m21,set:lt.LeadMatrix.prototype.set_m21,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadMatrix.prototype,"m22",{get:lt.LeadMatrix.prototype.get_m22,set:lt.LeadMatrix.prototype.set_m22,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadMatrix.prototype,"offsetX",{get:lt.LeadMatrix.prototype.get_offsetX,
set:lt.LeadMatrix.prototype.set_offsetX,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadMatrix.prototype,"offsetY",{get:lt.LeadMatrix.prototype.get_offsetY,set:lt.LeadMatrix.prototype.set_offsetY,enumerable:!0,configurable:!0});lt._leadMatrixUtil=function(){};lt._leadMatrixUtil._transformRect=function(a,b){var c=a.clone();if(a.get_isEmpty())return c;var d=b._type;if(!d)return c;if(d&2&&(c._x*=b._m11,c._y*=b._m22,c._width*=b._m11,c._height*=b._m22,0>c._width&&(c._x+=c._width,c._width=
-c._width),0>c._height))c._y+=c._height,c._height=-c._height;d&1&&(c._x+=b._offsetX,c._y+=b._offsetY);if(4===d){var d=b.transformPoint(c.get_topLeft()),e=b.transformPoint(c.get_topRight()),f=b.transformPoint(c.get_bottomRight()),h=b.transformPoint(c.get_bottomLeft());c._x=Math.min(Math.min(d.get_x(),e.get_x()),Math.min(f.get_x(),h.get_x()));c._y=Math.min(Math.min(d.get_y(),e.get_y()),Math.min(f.get_y(),h.get_y()));c._width=Math.max(Math.max(d.get_x(),e.get_x()),Math.max(f.get_x(),h.get_x()))-c._x;
c._height=Math.max(Math.max(d.get_y(),e.get_y()),Math.max(f.get_y(),h.get_y()))-c._y}return c};lt._leadMatrixUtil._multiplyMatrix=function(a,b){var c=a._type,d=b._type,e=a.clone();if(!d)return e;if(!c)return e=b.clone();if(1===d)return e._offsetX+=b._offsetX,e._offsetY+=b._offsetY,4!==c&&(e._type|=1),e;if(1!==c){d|=c<<4;switch(d){case 34:return e._m11*=b._m11,e._m22*=b._m22,e;case 35:return e._m11*=b._m11,e._m22*=b._m22,e._offsetX=b._offsetX,e._offsetY=b._offsetY,e._type=3,e;case 36:break;default:switch(d){case 50:return e._m11*=
b._m11,e._m22*=b._m22,e._offsetX*=b._m11,e._offsetY*=b._m22,e;case 51:return e._m11*=b._m11,e._m22*=b._m22,e._offsetX=b._m11*e._offsetX+b._offsetX,e._offsetY=b._m22*e._offsetY+b._offsetY,e;case 52:break;default:switch(d){case 66:case 67:case 68:break;default:return e}}}return e=lt.LeadMatrix.create(a._m11*b._m11+a._m12*b._m21,a._m11*b._m12+a._m12*b._m22,a._m21*b._m11+a._m22*b._m21,a._m21*b._m12+a._m22*b._m22,a._offsetX*b._m11+a._offsetY*b._m21+b._offsetX,a._offsetX*b._m12+a._offsetY*b._m22+b._offsetY)}var c=
a._offsetX,f=a._offsetY,e=b.clone();e._offsetX=c*b._m11+f*b._m21+b._offsetX;e._offsetY=c*b._m12+f*b._m22+b._offsetY;if(4===d)return e._type=4,e;e._type=3;return e};lt.LeadPointD=function(){};lt.LeadPointD.get_empty=function(){return lt.LeadPointD._empty};lt.LeadPointD.create=function(a,b){var c=new lt.LeadPointD;c._x=a;c._y=b;return c};lt.LeadPointD.equals=function(a,b){return a.get_isEmpty()?b.get_isEmpty():lt.LeadDoubleTools.areClosePoints(a,b)};lt.LeadPointD.fromJSON=function(a){if(isNaN(a.x))return lt.LeadPointD._createEmptyPoint();
var b=new lt.LeadPointD;b._x=a.x;b._y=a.y;return b};lt.LeadPointD._createEmptyPoint=function(){var a=new lt.LeadPointD;a._x=lt.LeadDoubleTools.naN;a._y=lt.LeadDoubleTools.naN;return a};lt.LeadPointD.prototype={_x:0,_y:0,get_isEmpty:function(){return lt.LeadDoubleTools.isNaN(this._x)},clone:function(){return this.get_isEmpty()?lt.LeadPointD._empty:lt.LeadPointD.create(this._x,this._y)},get_x:function(){return this._x},set_x:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty LeadPointD");
return this._x=a},get_y:function(){return this._y},set_y:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty LeadPointD");return this._y=a},toString:function(){return this.get_isEmpty()?"Empty":String.format("{0},{1}",this._x,this._y)},toJSON:function(){return{x:this._x.toString(),y:this._y.toString()}}};Object.defineProperty(lt.LeadPointD,"empty",{get:lt.LeadPointD.get_empty,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadPointD.prototype,"isEmpty",{get:lt.LeadPointD.prototype.get_isEmpty,
enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadPointD.prototype,"x",{get:lt.LeadPointD.prototype.get_x,set:lt.LeadPointD.prototype.set_x,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadPointD.prototype,"y",{get:lt.LeadPointD.prototype.get_y,set:lt.LeadPointD.prototype.set_y,enumerable:!0,configurable:!0});lt.LeadRectD=function(){};lt.LeadRectD.create=function(a,b,c,d){if(0>c||0>d)throw Error("Width or Height of 'LeadRectD' cannot be negative");var e=new lt.LeadRectD;e._x=
a;e._y=b;e._width=c;e._height=d;return e};lt.LeadRectD.fromLTRB=function(a,b,c,d){var e;c>=a?(e=a,a=c-a):(e=c,a-=c);d>=b?(c=b,b=d-b):(c=d,b-=d);return lt.LeadRectD.create(e,c,a,b)};lt.LeadRectD.get_empty=function(){return lt.LeadRectD._empty};lt.LeadRectD.equals=function(a,b){return a.get_isEmpty()?b.get_isEmpty():lt.LeadDoubleTools.areCloseRects(a,b)};lt.LeadRectD.fromJSON=function(a){if(!isFinite(a.width))return lt.LeadRectD._createEmptyRect();var b=new lt.LeadRectD;b._x=a.x;b._y=a.y;b._width=a.width;
b._height=a.height;return b};lt.LeadRectD.intersectRects=function(a,b){var c=a.clone();c.intersect(b);return c};lt.LeadRectD.unionRects=function(a,b){if(a.get_isEmpty())return b.get_isEmpty()?lt.LeadRectD.get_empty():b.clone();if(b.get_isEmpty())return a.get_isEmpty()?lt.LeadRectD.get_empty():a.clone();var c=a.clone();c.union(b);return c};lt.LeadRectD.inflateRect=function(a,b,c){a=a.clone();a.inflate(b,c);return a};lt.LeadRectD._createEmptyRect=function(){var a=new lt.LeadRectD;a._x=lt.LeadDoubleTools.naN;
a._y=lt.LeadDoubleTools.naN;a._width=lt.LeadDoubleTools.naN;a._height=lt.LeadDoubleTools.naN;return a};lt.LeadRectD.prototype={_x:0,_y:0,_width:0,_height:0,clone:function(){var a=new lt.LeadRectD;a._x=this._x;a._y=this._y;a._width=this._width;a._height=this._height;return a},_copyFrom:function(a){this._x=a._x;this._y=a._y;this._width=a._width;this._height=a._height},get_isEmpty:function(){return lt.LeadDoubleTools.isNaN(this._width)},get_location:function(){return lt.LeadPointD.create(this._x,this._y)},
set_location:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty 'LeadRectD'");this._x=a._x;this._y=a._y;return a},get_size:function(){return this.get_isEmpty()?lt.LeadSizeD.get_empty():lt.LeadSizeD.create(this._width,this._height)},set_size:function(a){if(a.get_isEmpty())this._copyFrom(lt.LeadRectD._empty);else{if(this.get_isEmpty())throw Error("Cannot modify empty 'LeadRectD'");this._width=a._width;this._height=a._height;return a}},get_x:function(){return this._x},set_x:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty 'LeadRectD'");
return this._x=a},get_y:function(){return this._y},set_y:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty 'LeadRectD'");return this._y=a},get_width:function(){return this._width},set_width:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty 'LeadRectD'");if(0>a)throw Error("Width or Height of 'LeadRectD' cannot be negative");return this._width=a},get_height:function(){return this._height},set_height:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty 'LeadRectD'");
if(0>a)throw Error("Width or Height of 'LeadRectD' cannot be negative");return this._height=a},get_left:function(){return this._x},get_top:function(){return this._y},get_right:function(){return this.get_isEmpty()?-1:this._x+this._width},get_bottom:function(){return this.get_isEmpty()?-1:this._y+this._height},get_topLeft:function(){return lt.LeadPointD.create(this.get_left(),this.get_top())},get_topRight:function(){return lt.LeadPointD.create(this.get_right(),this.get_top())},get_bottomLeft:function(){return lt.LeadPointD.create(this.get_left(),
this.get_bottom())},get_bottomRight:function(){return lt.LeadPointD.create(this.get_right(),this.get_bottom())},toString:function(){return this.get_isEmpty()?"Empty":String.format("{0},{1},{2},{3}",this._x,this._y,this._width,this._height)},toJSON:function(){return this.get_isEmpty()?{x:"Infinity",y:"Infinity",width:"-Infinity",height:"-Infinity"}:{x:this._x.toString(),y:this._y.toString(),width:this._width.toString(),height:this._height.toString()}},containsPoint:function(a){return this.contains(a._x,
a._y)},contains:function(a,b){return!this.get_isEmpty()&&this._containsInternal(a,b)},containsRect:function(a){return!this.get_isEmpty()&&!a.get_isEmpty()&&this._x<=a._x&&this._y<=a._y&&this._x+this._width>=a._x+a._width&&this._y+this._height>=a._y+a._height},intersectsWith:function(a){return!this.get_isEmpty()&&!a.get_isEmpty()&&a.get_left()<=this.get_right()&&a.get_right()>=this.get_left()&&a.get_top()<=this.get_bottom()&&a.get_bottom()>=this.get_top()},intersect:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty 'LeadRectD'");
if(this.intersectsWith(a)){var b=Math.max(this.get_left(),a.get_left()),c=Math.max(this.get_top(),a.get_top());this._width=Math.max(Math.min(this.get_right(),a.get_right())-b,0);this._height=Math.max(Math.min(this.get_bottom(),a.get_bottom())-c,0);this._x=b;this._y=c}else this._copyFrom(lt.LeadRectD._empty)},union:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty 'LeadRectD'");if(!a.get_isEmpty()){var b=Math.min(this.get_left(),a.get_left()),c=Math.min(this.get_top(),a.get_top());
if(-1===a.get_width()||-1===this.get_width())this._width=-1;else{var d=Math.max(this.get_right(),a.get_right());this._width=Math.max(d-b,0)}-1===a.get_height()||-1===this.get_height()?this._height=-1:(a=Math.max(this.get_bottom(),a.get_bottom()),this._height=Math.max(a-c,0));this._x=b;this._y=c}},offset:function(a,b){if(this.get_isEmpty())throw Error("Cannot modify empty 'LeadRectD'");this._x+=a;this._y+=b},inflate:function(a,b){if(this.get_isEmpty())throw Error("Cannot modify empty 'LeadRectD'");
this._x-=a;this._y-=b;this._width+=a;this._width+=a;this._height+=b;this._height+=b;(0>this._width||0>this._height)&&this._copyFrom(lt.LeadRectD._empty)},scale:function(a,b){if(!this.get_isEmpty()&&(this._x*=a,this._y*=b,this._width*=a,this._height*=b,0>a&&(this._x+=this._width,this._width*=-1),0>b))this._y+=this._height,this._height*=-1},_containsInternal:function(a,b){return a>=this._x&&a-this._width<=this._x&&b>=this._y&&b-this._height<=this._y}};Object.defineProperty(lt.LeadRectD,"empty",{get:lt.LeadRectD.get_empty,
enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"isEmpty",{get:lt.LeadRectD.prototype.get_isEmpty,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"location",{get:lt.LeadRectD.prototype.get_location,set:lt.LeadRectD.prototype.set_location,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"size",{get:lt.LeadRectD.prototype.get_size,set:lt.LeadRectD.prototype.set_size,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,
"x",{get:lt.LeadRectD.prototype.get_x,set:lt.LeadRectD.prototype.set_x,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"y",{get:lt.LeadRectD.prototype.get_y,set:lt.LeadRectD.prototype.set_y,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"width",{get:lt.LeadRectD.prototype.get_width,set:lt.LeadRectD.prototype.set_width,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"height",{get:lt.LeadRectD.prototype.get_height,
set:lt.LeadRectD.prototype.set_height,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"left",{get:lt.LeadRectD.prototype.get_left,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"top",{get:lt.LeadRectD.prototype.get_top,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"right",{get:lt.LeadRectD.prototype.get_right,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"bottom",{get:lt.LeadRectD.prototype.get_bottom,
enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"topLeft",{get:lt.LeadRectD.prototype.get_topLeft,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"topRight",{get:lt.LeadRectD.prototype.get_topRight,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"bottomLeft",{get:lt.LeadRectD.prototype.get_bottomLeft,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadRectD.prototype,"bottomRight",{get:lt.LeadRectD.prototype.get_bottomRight,
enumerable:!0,configurable:!0});lt.LeadSizeD=function(){};lt.LeadSizeD.create=function(a,b){if(0>a||0>b)throw Error("Width or Height of LeadSizeD cannot be negative");var c=new lt.LeadSizeD;c._width=a;c._height=b;return c};lt.LeadSizeD.get_empty=function(){return lt.LeadSizeD._empty};lt.LeadSizeD.equals=function(a,b){return a.get_isEmpty()?b.get_isEmpty():lt.LeadDoubleTools.areCloseSizes(a,b)};lt.LeadSizeD.fromJSON=function(a){if(!isFinite(a.width))return lt.LeadSizeD._createEmptySize();var b=new lt.LeadSizeD;
b._width=a.width;b._height=a.height;return b};lt.LeadSizeD._createEmptySize=function(){var a=new lt.LeadSizeD;a._width=lt.LeadDoubleTools.naN;a._height=lt.LeadDoubleTools.naN;return a};lt.LeadSizeD.prototype={_width:0,_height:0,clone:function(){var a=new lt.LeadSizeD;a._width=this._width;a._height=this._height;return a},get_isEmpty:function(){return lt.LeadDoubleTools.isNaN(this._width)},get_width:function(){return this._width},set_width:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty LeadSizeD");
if(0>a)throw Error("Width or Height of LeadSizeD cannot be negative");return this._width=a},get_height:function(){return this._height},set_height:function(a){if(this.get_isEmpty())throw Error("Cannot modify empty LeadSizeD");if(0>a)throw Error("Width or Height of LeadSizeD cannot be negative");return this._height=a},toString:function(){return this.get_isEmpty()?"Empty":String.format("{0},{1}",this._width,this._height)},toJSON:function(){return this.get_isEmpty()?{width:"-Infinity",height:"-Infinity"}:
{width:this._width.toString(),height:this._height.toString()}}};Object.defineProperty(lt.LeadSizeD,"empty",{get:lt.LeadSizeD.get_empty,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadSizeD.prototype,"isEmpty",{get:lt.LeadSizeD.prototype.get_isEmpty,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadSizeD.prototype,"width",{get:lt.LeadSizeD.prototype.get_width,set:lt.LeadSizeD.prototype.set_width,enumerable:!0,configurable:!0});Object.defineProperty(lt.LeadSizeD.prototype,"height",
{get:lt.LeadSizeD.prototype.get_height,set:lt.LeadSizeD.prototype.set_height,enumerable:!0,configurable:!0});lt.TextFontRuler=function(a){this._gMWidth=this._height=-1;var b=document.createElement("canvas");this._canvas=b;this._context=b.getContext("2d");this.set_font(a)};lt.TextFontRuler.prototype={_font:null,get_font:function(){return this._font},set_font:function(a){if(!(null==this._canvas||null==this._context))return this._font!==a&&(this._font=a,this._gMWidth=this._height=-1),this._context.font=
a},_canvas:null,_context:null,get_context:function(){return this._context},measureWidth:function(a){if(null==this._canvas||null==this._context)return 0;this._context.font=this._font;return this._context.measureText(a).width},measure:function(a){var a=this.measureWidth(a),b=this.get_height();return lt.LeadSizeD.create(a,b)},get_height:function(){-1===this._height&&(this._height=this._calculateHeightFromFont());return this._height},_calculateHeightFromFont:function(){if(null==this._canvas||null==this._context)return 0;
var a=this._canvas,b=this._context;-1===this._gMWidth&&(this._gMWidth=Math.ceil(this.measureWidth("gM")+1));var c=this._gMWidth,d=Math.floor(1.3*(96*c/72));a.width=c;a.height=d;b.font=this._font;b.textBaseline="top";b.fillStyle="#000";b.fillText("gM",0,0);var e=a=0;if(0<c&&0<d){for(var b=b.getImageData(0,0,c,d),f=d,h=0;!e&&0<f;){f--;for(h=0;h<c;h++)if(b.data[4*(f*c+h)+3]){e=f;break}}for(f=0;f<d&&!a;){f++;for(h=0;h<c;h++)if(b.data[4*(f*c+h)+3]){a=f;break}}}return e+a},dispose:function(){this._font=
null;null!=this._canvas&&(this._canvas.width=0,this._canvas.height=0,this._canvas=null);this._context=null;this._height=this._gMWidth=-1}};Object.defineProperty(lt.TextFontRuler.prototype,"font",{get:lt.TextFontRuler.prototype.get_font,set:lt.TextFontRuler.prototype.set_font,enumerable:!0,configurable:!0});Object.defineProperty(lt.TextFontRuler.prototype,"context",{get:lt.TextFontRuler.prototype.get_context,enumerable:!0,configurable:!0});Object.defineProperty(lt.TextFontRuler.prototype,"height",
{get:lt.TextFontRuler.prototype.get_height,enumerable:!0,configurable:!0});lt.ImageLoaderPreRunEventArgs=function(){lt.ImageLoaderPreRunEventArgs.initializeBase(this);this._cancel$1=!1};lt.ImageLoaderPreRunEventArgs.prototype={_cancel$1:!1,get_cancel:function(){return this._cancel$1},set_cancel:function(a){return this._cancel$1=a}};Object.defineProperty(lt.ImageLoaderPreRunEventArgs.prototype,"cancel",{get:lt.ImageLoaderPreRunEventArgs.prototype.get_cancel,set:lt.ImageLoaderPreRunEventArgs.prototype.set_cancel,
enumerable:!0,configurable:!0});lt.ImageLoaderAjaxOptions=function(){this._headers={};this._method="GET";this._postData=null};lt.ImageLoaderAjaxOptions.prototype={_headers:null,get_headers:function(){return this._headers},set_headers:function(a){return this._headers=a},_method:null,get_method:function(){return this._method},set_method:function(a){return this._method=a},_postData:null,get_postData:function(){return this._postData},set_postData:function(a){return this._postData=a}};Object.defineProperty(lt.ImageLoaderAjaxOptions.prototype,
"headers",{get:lt.ImageLoaderAjaxOptions.prototype.get_headers,set:lt.ImageLoaderAjaxOptions.prototype.set_headers,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoaderAjaxOptions.prototype,"method",{get:lt.ImageLoaderAjaxOptions.prototype.get_method,set:lt.ImageLoaderAjaxOptions.prototype.set_method,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoaderAjaxOptions.prototype,"postData",{get:lt.ImageLoaderAjaxOptions.prototype.get_postData,set:lt.ImageLoaderAjaxOptions.prototype.set_postData,
enumerable:!0,configurable:!0});lt.ImageLoader=function(){this._isAborted=this._isHTMLImageElement=!1;this._urlMode=0;this._ajaxOptions=new lt.ImageLoaderAjaxOptions;this._imagesHolder=null;this._isWorking=!1};lt.ImageLoader._getMimeType=function(a,b){if(null==a||4>b)return null;if(137===a[0]&&80===a[1]&&78===a[2]&&71===a[3])return lt.ImageLoader._mimeTypePNG;if(255===a[0]&&216===a[1]&&255===a[2]&&(224===a[3]||225===a[3]||226===a[3]))return lt.ImageLoader._mimeTypeJPEG;if(71===a[0]&&73===a[1]&&70===
a[2]&&56===a[3])return lt.ImageLoader._mimeTypeGIF;for(var c=0,d=Math.max(b-c,200);c<d-4;){if(60===a[c]&&115===a[c+1]&&118===a[c+2]&&103===a[c+3])return lt.ImageLoader._mimeTypeSVG;c++}return null};lt.ImageLoader._arrayToDataUri=function(a){var b=lt.ImageLoader._getMimeType(a,a.length);if(null==b)return null;a=lt._base64.fromByteArray(a);return"data:"+b+";base64,"+a};lt.ImageLoader.prototype={add_preRun:function(a){this.__preRun=ss.Delegate.combine(this.__preRun,a)},remove_preRun:function(a){this.__preRun=
ss.Delegate.remove(this.__preRun,a)},__preRun_handler_get:function(){null==this.__preRun_handler&&(this.__preRun_handler=ss.EventHandler.create(this,this.add_preRun,this.remove_preRun));return this.__preRun_handler},__preRun:null,__preRun_handler:null,add_done:function(a){this.__done=ss.Delegate.combine(this.__done,a)},remove_done:function(a){this.__done=ss.Delegate.remove(this.__done,a)},__done_handler_get:function(){null==this.__done_handler&&(this.__done_handler=ss.EventHandler.create(this,this.add_done,
this.remove_done));return this.__done_handler},__done:null,__done_handler:null,add_fail:function(a){this.__fail=ss.Delegate.combine(this.__fail,a)},remove_fail:function(a){this.__fail=ss.Delegate.remove(this.__fail,a)},__fail_handler_get:function(){null==this.__fail_handler&&(this.__fail_handler=ss.EventHandler.create(this,this.add_fail,this.remove_fail));return this.__fail_handler},__fail:null,__fail_handler:null,add_always:function(a){this.__always=ss.Delegate.combine(this.__always,a)},remove_always:function(a){this.__always=
ss.Delegate.remove(this.__always,a)},__always_handler_get:function(){null==this.__always_handler&&(this.__always_handler=ss.EventHandler.create(this,this.add_always,this.remove_always));return this.__always_handler},__always:null,__always_handler:null,add__aborted:function(a){this.__aborted=ss.Delegate.combine(this.__aborted,a)},remove__aborted:function(a){this.__aborted=ss.Delegate.remove(this.__aborted,a)},__aborted_handler_get:function(){null==this.__aborted_handler&&(this.__aborted_handler=ss.EventHandler.create(this,
this.add__aborted,this.remove__aborted));return this.__aborted_handler},__aborted:null,_urlMode:0,get_urlMode:function(){return this._urlMode},set_urlMode:function(a){return this._urlMode=a},_tag:null,get_tag:function(){return this._tag},set_tag:function(a){return this._tag=a},_workingImageElement:null,_element:null,get_element:function(){return this._element},_isHTMLImageElement:!1,get_isHTMLImageElement:function(){return this._isHTMLImageElement},_width:0,get_width:function(){return this._width},
_height:0,get_height:function(){return this._height},_error:null,get_error:function(){return this._error},_isWorking:!1,get_isWorking:function(){return this._isWorking},_ajaxOptions:null,get_ajaxOptions:function(){return this._ajaxOptions},set_ajaxOptions:function(a){return this._ajaxOptions=a},_url:null,get_url:function(){return this._url},set_url:function(a){return this._url=a},_imagesHolder:null,get_imagesHolder:function(){return this._imagesHolder},set_imagesHolder:function(a){return this._imagesHolder=
a},_isAborted:!1,get_isAborted:function(){return this._isAborted},abort:function(){this._internalAbort(!0)},_internalAbort:function(a){this._isAborted||(this._isAborted=!0,a&&null!=this.__aborted&&this.__aborted(this,lt.LeadEventArgs.Empty))},_clearAll:function(){this._element=this._workingImageElement=null;this._isHTMLImageElement=!1;this._error=null;this._height=this._width=0;this._isWorking=!1},dispose:function(){this._internalAbort(!1);this._clearAll();this._url=this._tag=null},get_canRun:function(){return!this._isWorking&&
!this._isAborted},run:function(){if(!this._isAborted){if(null!=this.__preRun){var a=new lt.ImageLoaderPreRunEventArgs;this.__preRun(this,a);if(this._isAborted)return;if(a.get_cancel()){this._error=Error("Canceled");null!=this.__fail&&this.__fail(this,lt.LeadEventArgs.Empty);return}}this._clearAll();this._isWorking=!0;null!=this._url&&0<this._url.length&&this._url.substring(0,Math.min(5,this._url.length)).toLowerCase().startsWith("data:")?this._runImageSrc():this._urlMode?2===this._urlMode?this._runAjaxXml():
this._runAjaxDataUrl():this._runImageSrc()}},_addRequestCallbacks:function(a,b,c){var d=ss.Delegate.create(this,function(){4===a.readyState&&(200===a.status?this._isAborted||b(a):this._isAborted||c(a),a=a.onreadystatechange=null)});a.onreadystatechange=d},_runAjaxXml:function(){var a=ss.Delegate.create(this,function(){null!=this.__always&&this.__always(this,lt.LeadEventArgs.Empty);this.dispose()}),b=null,b=1===lt.LTHelper.OS&&2===lt.LTHelper.device&&"x-wmapp0:"===window.location.protocol?new ActiveXObject("Msxml2.XMLHTTP"):
new XMLHttpRequest,c=ss.Delegate.create(this,function(){try{if(b&&b.response&&null===b.responseXML)throw new lt.InvalidOperationException("Raster image cannot be loaded from this method");var c=b.responseXML.documentElement,c=document.importNode(c,!0);if(null!=c.childNodes&&1===c.childNodes.length&&null!=c.childNodes[0]&&"image"===c.childNodes[0].nodeName.toLowerCase()){var d=c.childNodes[0].getAttribute("xlink:href"),e=null,f=null,h=null,e=ss.Delegate.create(this,function(b){this._isWorking=!1;this._width=
b.get_width();this._height=b.get_height();this._element=b.get_element();this._isHTMLImageElement=b.get_isHTMLImageElement();null!=this.__done&&this.__done(this,lt.LeadEventArgs.Empty);this._isAborted||a()}),f=ss.Delegate.create(this,function(b){this._isWorking=!1;this._error=b.get_error();null!=this.__fail&&this.__fail(this,lt.LeadEventArgs.Empty);this._isAborted||a()}),h=function(a){a.remove_done(e);a.remove_fail(f);a.remove_always(h)},k=new lt.ImageLoader;k.set_imagesHolder(this._imagesHolder);
k.set_url(d);k.set_urlMode(0);k.add_done(e);k.add_fail(f);k.add_always(h);k.run()}else this._isWorking=!1,this._width=parseInt(c.getAttribute("width"),10),this._height=parseInt(c.getAttribute("height"),10),this._element=c,this._isHTMLImageElement=!1,null!=this.__done&&this.__done(this,lt.LeadEventArgs.Empty),this._isAborted||a()}catch(n){this._isWorking=!1,this._error=n,null!=this.__fail&&this.__fail(this,lt.LeadEventArgs.Empty),this._isAborted||a()}}),d=ss.Delegate.create(this,function(){this._isWorking=
!1;this._error=b.status?Error(b.status+": "+b.statusText):Error("0: Connection not allowed");null!=this.__fail&&this.__fail(this,lt.LeadEventArgs.Empty);this._isAborted||a()});try{this._addRequestCallbacks(b,c,d);b.open(this._ajaxOptions.get_method(),this._url,!0);var e=this._ajaxOptions.get_headers();if(null!=e&&0<Object.getKeyCount(e))for(var f=ss.IEnumerator.getEnumerator(Object.keys(e));f.moveNext();){var h=f.current;b.setRequestHeader(h,e[h])}"GET"===this._ajaxOptions.get_method()?b.send():b.send(this._ajaxOptions.get_postData())}catch(k){this._isWorking=
!1,this._error=k,null!=this.__fail&&this.__fail(this,lt.LeadEventArgs.Empty),this._isAborted||a()}},_runImageSrc:function(){var a=null,b=null,c=-1;this._workingImageElement=document.createElement("img");!(1===lt.LTHelper.browser&&11===lt.LTHelper.version)&&null!=this._url&&!this._url.startsWith("data")&&(this._workingImageElement.crossOrigin="Anonymous");var d=ss.Delegate.create(this,function(){if(this._workingImageElement!=null){this._workingImageElement.removeEventListener("load",a,false);this._workingImageElement.removeEventListener("error",
b,false)}}),e=ss.Delegate.create(this,function(){this.__always!=null&&this.__always(this,lt.LeadEventArgs.Empty);this.dispose()}),f=ss.Delegate.create(this,function(){c!==-1&&clearTimeout(c);d();if(!this._isAborted){this._isWorking=false;this._element=this._workingImageElement;this._isHTMLImageElement=true;this._width=this._workingImageElement.naturalWidth;this._height=this._workingImageElement.naturalHeight;var a=false;if((!this._width||!this._height)&&this._imagesHolder!=null){this._imagesHolder.appendChild(this._element);
this._width=this._element.offsetWidth;this._height=this._element.offsetHeight;a=true}this.__done!=null&&this.__done(this,lt.LeadEventArgs.Empty);if(!this._isAborted){(a||lt.LTHelper.browser===1&&lt.LTHelper.version<11)&&this._element.parentNode!=null&&this._element.parentNode===this._imagesHolder&&this._element.parentNode.removeChild(this._element);e()}}}),a=ss.Delegate.create(this,function(){!this._workingImageElement.naturalWidth||!this._workingImageElement.naturalHeight?c=setTimeout(f,0):f()}),
b=ss.Delegate.create(this,function(){d();if(!this._isAborted){this._isWorking=false;this._element=null;this._height=this._width=0;this._error=Error("Could not load image");this.__fail!=null&&this.__fail(this,lt.LeadEventArgs.Empty);this._isAborted||e()}});this._workingImageElement.addEventListener("load",a,!1);this._workingImageElement.addEventListener("error",b,!1);this._workingImageElement.src=this._url},_runAjaxDataUrl:function(){var a=ss.Delegate.create(this,function(){null!=this.__always&&this.__always(this,
lt.LeadEventArgs.Empty);this.dispose()}),b=null,b=1===lt.LTHelper.OS&&2===lt.LTHelper.device&&"x-wmapp0:"===window.location.protocol?new ActiveXObject("Msxml2.XMLHTTP"):new XMLHttpRequest,c=ss.Delegate.create(this,function(c){null==b&&(b=c);try{c=null;if(lt.LTHelper.supportsFileReader){var d=b.response;if(null!=d){var e=new Uint8Array(d);if(null!=e){var f=lt.ImageLoader._getMimeType(e,e.length);null!=f&&(c=(window.URL||window.webkitURL).createObjectURL(new Blob([d],{type:f})))}}}else e=(new VBArray(b.responseBody)).toArray(),
null!=e&&(c=lt.ImageLoader._arrayToDataUri(e));if(null==c)throw Error("Invalid image type");var h=null,k=null,n=null,h=ss.Delegate.create(this,function(b){this._width=b.get_width();this._height=b.get_height();this._element=b.get_element();this._isHTMLImageElement=b.get_isHTMLImageElement();this._isWorking=!1;null!=this.__done&&this.__done(this,lt.LeadEventArgs.Empty);this._isAborted||a()}),k=ss.Delegate.create(this,function(b){this._isWorking=!1;this._error=b.get_error();null!=this.__fail&&this.__fail(this,
lt.LeadEventArgs.Empty);this._isAborted||a()}),n=function(a){a.remove_done(h);a.remove_fail(k);a.remove_always(n)},m=new lt.ImageLoader;m.set_imagesHolder(this._imagesHolder);m.set_url(c);m.set_urlMode(0);m.add_done(h);m.add_fail(k);m.add_always(n);m.run()}catch(q){this._isWorking=!1,this._error=q,null!=this.__fail&&this.__fail(this,lt.LeadEventArgs.Empty),this._isAborted||a()}}),d=ss.Delegate.create(this,function(c){this._isWorking=!1;null==b&&(b=c);this._error=b.status?Error(b.status+": "+b.statusText):
Error("0: Connection not allowed");null!=this.__fail&&this.__fail(this,lt.LeadEventArgs.Empty);this._isAborted||a()});try{this._addRequestCallbacks(b,c,d);b.open(this._ajaxOptions.get_method(),this._url,!0);b.responseType="arraybuffer";var e=this._ajaxOptions.get_headers();if(null!=e&&0<Object.getKeyCount(e))for(var f=ss.IEnumerator.getEnumerator(Object.keys(e));f.moveNext();){var h=f.current;b.setRequestHeader(h,e[h])}"GET"===this._ajaxOptions.get_method()?b.send():b.send(this._ajaxOptions.get_postData())}catch(k){this._isWorking=
!1,this._error=k,null!=this.__fail&&this.__fail(this,lt.LeadEventArgs.Empty),this._isAborted||a()}}};Object.defineProperty(lt.ImageLoader.prototype,"preRun",{get:lt.ImageLoader.prototype.__preRun_handler_get,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"done",{get:lt.ImageLoader.prototype.__done_handler_get,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"fail",{get:lt.ImageLoader.prototype.__fail_handler_get,enumerable:!0,configurable:!0});
Object.defineProperty(lt.ImageLoader.prototype,"always",{get:lt.ImageLoader.prototype.__always_handler_get,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"urlMode",{get:lt.ImageLoader.prototype.get_urlMode,set:lt.ImageLoader.prototype.set_urlMode,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"tag",{get:lt.ImageLoader.prototype.get_tag,set:lt.ImageLoader.prototype.set_tag,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,
"element",{get:lt.ImageLoader.prototype.get_element,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"isHTMLImageElement",{get:lt.ImageLoader.prototype.get_isHTMLImageElement,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"width",{get:lt.ImageLoader.prototype.get_width,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"height",{get:lt.ImageLoader.prototype.get_height,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,
"error",{get:lt.ImageLoader.prototype.get_error,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"isWorking",{get:lt.ImageLoader.prototype.get_isWorking,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"ajaxOptions",{get:lt.ImageLoader.prototype.get_ajaxOptions,set:lt.ImageLoader.prototype.set_ajaxOptions,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"url",{get:lt.ImageLoader.prototype.get_url,set:lt.ImageLoader.prototype.set_url,
enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"imagesHolder",{get:lt.ImageLoader.prototype.get_imagesHolder,set:lt.ImageLoader.prototype.set_imagesHolder,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"isAborted",{get:lt.ImageLoader.prototype.get_isAborted,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageLoader.prototype,"canRun",{get:lt.ImageLoader.prototype.get_canRun,enumerable:!0,configurable:!0});lt.LTHelper=function(){};
lt.LTHelper._testString=function(a,b){for(var c=0;c<b.length;c++)if(-1!==a.indexOf(b[c]))return c;return-1};lt.LTHelper._getVersion=function(a,b){var c=a.indexOf(b);lt.LTHelper.version=-1===c?0:parseFloat(a.substr(c+b.length+1))};lt.LTHelper._detectBrowser=function(){var a=window.navigator.userAgent.toLocaleLowerCase(),b=window.navigator.appVersion.toLocaleLowerCase(),c=window.navigator.platform.toLocaleLowerCase(),d="msie";lt.LTHelper.device=0;lt.LTHelper.OS=0;lt.LTHelper.browser=0;var e=-1!==lt.LTHelper._testString(a,
["windows phone"]);if(!e){if(!lt.LTHelper.OS){var f=lt.LTHelper._testString(a,["iphone","ipod","ipad"]);-1!==f&&(lt.LTHelper.OS=3,lt.LTHelper.browser=4,lt.LTHelper.device=2===f?3:2)}if(!lt.LTHelper.device&&(f=lt.LTHelper._testString(a,["android"]),-1!==f)){lt.LTHelper.OS=4;f=lt.LTHelper._testString(a,["firefox","opera","opr","chrome"]);switch(f){case 0:lt.LTHelper.browser=2;break;case 1:case 2:lt.LTHelper.browser=5;break;case 3:lt.LTHelper.browser=3;break;default:lt.LTHelper.browser=6}f=lt.LTHelper._testString(a,
["mobile"]);lt.LTHelper.device=-1!==f?2:3}lt.LTHelper.device||(f=lt.LTHelper._testString(a,["hp-tablet"]),-1!==f&&(lt.LTHelper.device=3,lt.LTHelper.browser=0,lt.LTHelper.OS=7));lt.LTHelper.device||(f=lt.LTHelper._testString(b,["playbook"]),-1!==f&&(lt.LTHelper.device=3,lt.LTHelper.browser=0,lt.LTHelper.OS=6));lt.LTHelper.device||(f=lt.LTHelper._testString(a,["blackberry"]),-1!==f&&(lt.LTHelper.device=2,lt.LTHelper.browser=0,lt.LTHelper.OS=6))}lt.LTHelper.device||(f=lt.LTHelper._testString(a,["iemobile"]),
-1!==f&&(lt.LTHelper.device=2,lt.LTHelper.browser=1,lt.LTHelper.OS=1));if(!lt.LTHelper.device){lt.LTHelper.device=e?2:1;f=lt.LTHelper._testString(c,["win","mac","linux"]);switch(f){case 0:lt.LTHelper.OS=1;break;case 1:lt.LTHelper.OS=2;break;case 2:lt.LTHelper.OS=5;break;default:lt.LTHelper.OS=0}f=lt.LTHelper._testString(a,"edge,firefox,opera,opr,chrome,safari,msie,trident".split(","));switch(f){case 0:lt.LTHelper.browser=7;break;case 1:lt.LTHelper.browser=2;break;case 2:case 3:lt.LTHelper.browser=
5;break;case 4:lt.LTHelper.browser=3;break;case 5:lt.LTHelper.browser=4;break;case 6:lt.LTHelper.browser=1;break;case 7:lt.LTHelper.browser=1;d="rv";break;default:lt.LTHelper.browser=0}}1===lt.LTHelper.OS&&((f=lt.LTHelper._testString(a,["windows phone 10.0","windows nt 6.2","windows phone os 8","windows phone os 7"]))?1===f||2===f?lt.LTHelper.OS=9:3===f&&(lt.LTHelper.OS=8):lt.LTHelper.OS=10);switch(lt.LTHelper.browser){case 1:lt.LTHelper._getVersion(a,d);break;case 2:lt.LTHelper._getVersion(a,"firefox");
break;case 3:lt.LTHelper._getVersion(a,"chrome");break;case 4:lt.LTHelper._getVersion(a,"version");break;case 5:-1!==a.indexOf("opr")?lt.LTHelper._getVersion(a,"opr"):lt.LTHelper._getVersion(a,"opera");break;case 6:lt.LTHelper._getVersion(a,"version");break;case 7:lt.LTHelper._getVersion(a,"edge");break;default:lt.LTHelper.version=0}lt.LTHelper.vendor=-1!==b.indexOf("edge")?"ms":-1!==a.indexOf("opera")||-1!==a.indexOf("opr")?"O":-1!==b.indexOf("webkit")?"Webkit":-1!==a.indexOf("firefox")?"Moz":-1!==
a.indexOf("trident")?"ms":""};lt.LTHelper._detectCapabilities=function(){lt.LTHelper.supportsHTMLPointerEvents=!(1===lt.LTHelper.browser&&(9===lt.LTHelper.version||10===lt.LTHelper.version));lt.LTHelper.supportsCSSTransitions="transition"in document.documentElement.style||lt.LTHelper.vendor+"Transition"in document.documentElement.style;lt.LTHelper._supportsClassList="classList"in document.documentElement;lt.LTHelper.supportsFileReader="File"in window&&"FileReader"in window;lt.LTHelper.supportsCanvas=
"HTMLCanvasElement"in window;lt.LTHelper.supportsTypedArray=void 0!=window.Uint32Array;lt.LTHelper.supportsTouch="ontouchstart"in window;lt.LTHelper.supportsTouch&&7===lt.LTHelper.OS&&(lt.LTHelper.supportsTouch=!1);lt.LTHelper.msPointerEnabled="pointerEnabled"in window.navigator&&window.navigator.pointerEnabled&&"maxTouchPoints"in window.navigator&&0<window.navigator.maxTouchPoints;lt.LTHelper.msPointerEnabled||(lt.LTHelper.msPointerEnabled="msPointerEnabled"in window.navigator&&window.navigator.msPointerEnabled&&
"msMaxTouchPoints"in window.navigator&&0<window.navigator.msMaxTouchPoints);lt.LTHelper.msPointerEnabled&&(lt.LTHelper.supportsTouch=!0);lt.LTHelper.supportsMultiTouch=lt.LTHelper.supportsTouch;lt.LTHelper.supportsMouse=!lt.LTHelper.supportsTouch||1===lt.LTHelper.device;lt.LTHelper.supportsScroll=1===lt.LTHelper.device;lt.LTHelper.supportsWebGL="WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix;lt.LTHelper.supportsTransform=lt.LTHelper.vendor+"Transform"in document.documentElement.style;lt.LTHelper.supportsTransitionEnd=
3===lt.LTHelper.OS||6===lt.LTHelper.OS&&3===lt.LTHelper.device;lt.LTHelper.supportsWebWorker="undefined"!==typeof Worker;lt.LTHelper.resizeEvent="onorientationchange"in window?"orientationchange":"resize";lt.LTHelper.dragStartEvent=lt.LTHelper.supportsTouch?"touchstart":"mousedown";lt.LTHelper.dragDeltaEvent=lt.LTHelper.supportsTouch?"touchmove":"mousemove";lt.LTHelper.dragCompletedEvent=lt.LTHelper.supportsTouch?"touchend":"mouseup";lt.LTHelper.dragCancelEvent=lt.LTHelper.supportsTouch?"touchcancel":
"mouseup";lt.LTHelper.mouseWheelEvent="Moz"===lt.LTHelper.vendor?"DOMMouseScroll":"mousewheel"};lt.LTHelper._setupConsole=function(){var a=!!window.console;if(a){lt.LTHelper._consoleWarnFunc=window.console.warn?"warn":lt.LTHelper._consoleLogFunc;lt.LTHelper._consoleErrorFunc=window.console.error?"error":lt.LTHelper._consoleLogFunc;try{window.console.log.apply(window.console,null),lt.LTHelper._isConsoleApplyAvailable=!0}catch(b){}}lt.LTHelper._isConsoleAvailable=a};lt.LTHelper.log=function(a){lt.LTHelper._isConsoleAvailable||
lt.LTHelper._setupConsole();lt.LTHelper._innerLog(lt.LTHelper._consoleLogFunc,arguments)};lt.LTHelper.logWarning=function(a){lt.LTHelper._isConsoleAvailable||lt.LTHelper._setupConsole();lt.LTHelper._innerLog(lt.LTHelper._consoleWarnFunc,arguments)};lt.LTHelper.logError=function(a){lt.LTHelper._isConsoleAvailable||lt.LTHelper._setupConsole();lt.LTHelper._innerLog(lt.LTHelper._consoleErrorFunc,arguments)};lt.LTHelper._innerLog=function(a,b){if(lt.LTHelper._isConsoleAvailable)if(lt.LTHelper._isConsoleApplyAvailable)window.console[a].apply(window.console,
b);else window.console[a](b[0])};lt.LTHelper._setupRequestAnimationFrame=function(){if(window.requestAnimationFrame&&window.cancelAnimationFrame)window.lt_requestAnimationFrame=window.requestAnimationFrame,window.lt_cancelAnimationFrame=window.cancelAnimationFrame,lt.LTHelper.supportsAnimationFrame=!0;else for(var a=["ms","moz","webkit","o"],b=0;b<a.length;b++)if(window.lt_requestAnimationFrame=window[a[b]+"RequestAnimationFrame"],window.lt_cancelAnimationFrame=window[a[b]+"CancelAnimationFrame"]||
window[a[b]+"CancelRequestAnimationFrame"],window.lt_requestAnimationFrame&&window.lt_cancelAnimationFrame){lt.LTHelper.supportsAnimationFrame=!0;break}lt.LTHelper.supportsAnimationFrame||(window.lt_requestAnimationFrame=lt.LTHelper._myRequestAnimationFrame,window.lt_cancelAnimationFrame=lt.LTHelper._myCancelAnimationFrame)};lt.LTHelper._myRequestAnimationFrame=function(a){var b=(new Date).getTime(),c=Math.max(0,16-(b-lt.LTHelper._lastAnimationFrameRequestTime)),d=-1,d=setTimeout(function(){a();-1!==
d&&clearTimeout(d)},c);lt.LTHelper._lastAnimationFrameRequestTime=b+c;return d};lt.LTHelper._myCancelAnimationFrame=function(a){-1!==a&&clearTimeout(a)};lt.LTHelper.requestAnimationFrame=function(a){return window.lt_requestAnimationFrame(a)};lt.LTHelper.cancelAnimationFrame=function(a){window.lt_cancelAnimationFrame(a)};lt.LTHelper.getElementStyle=function(a,b){var c=a.style[b];if(!String.isNullOrEmpty(c))return c;a.currentStyle&&(c=a.currentStyle[b]);if(!String.isNullOrEmpty(c))return c;window.getComputedStyle&&
(c=document.defaultView.getComputedStyle(a,null).getPropertyValue(b));return c};lt.LTHelper.getPosition=function(a,b){if(null==a)return lt.LeadPointD.get_empty();var c=lt.LTHelper._getPositionFromDocument(a);if(null==b)return c;var d=lt.LTHelper._getPositionFromDocument(b);return d.get_isEmpty()?c:lt.LeadPointD.create(c.get_x()-d.get_x(),c.get_y()-d.get_y())};lt.LTHelper._getPositionFromDocument=function(a){if(null==a||1>a.getClientRects().length)return lt.LeadPointD.create(0,0);var a=a.getBoundingClientRect(),
a=lt.LeadRectD.create(a.left,a.top,a.width,a.height),b=lt.LeadPointD.create(a.get_left(),a.get_top());if(0<a.get_width()||0<a.get_height())b=lt.LeadPointD.create(a.get_left()+window.pageXOffset-document.documentElement.clientLeft,a.get_top()+window.pageYOffset-document.documentElement.clientTop);return b};lt.LTHelper._getElementClassNameValue=function(a){return"undefined"!==typeof a.className.baseVal?a.className.baseVal:a.className};lt.LTHelper._setElementClassNameValue=function(a,b){"undefined"!==
typeof a.className.baseVal?a.className.baseVal=b:a.className=b};lt.LTHelper.hasClass=function(a,b){return lt.LTHelper._supportsClassList&&"undefined"!==typeof a.classList?a.classList.contains(b):null!=lt.LTHelper._getElementClassNameValue(a).match(RegExp("(\\s|^)"+b+"(\\s|$)"))};lt.LTHelper.removeClass=function(a,b){if(lt.LTHelper._supportsClassList&&"undefined"!==typeof a.classList)a.classList.remove(b);else if(lt.LTHelper.hasClass(a,b)){var c=RegExp("(\\s|^)"+b+"(\\s|$)"),d=lt.LTHelper._getElementClassNameValue(a),
d=d.replace(c," "),d=d.trim();lt.LTHelper._setElementClassNameValue(a,d)}};lt.LTHelper.addClass=function(a,b){if(lt.LTHelper._supportsClassList&&"undefined"!==typeof a.classList)a.classList.add(b);else if(!lt.LTHelper.hasClass(a,b)){var c=lt.LTHelper._getElementClassNameValue(a);lt.LTHelper._setElementClassNameValue(a,(c+(" "+b)).trim())}};lt.LTHelper.base64Encode=function(a,b){if(null==a)return null;if(!a.length)return"";for(var c="",d,e,f,h,k,g,j=lt.LTHelper._utF8Encode(a,b),l=0;l<j.length;)d=j.charCodeAt(l++),
e=j.charCodeAt(l++),f=j.charCodeAt(l++),h=d>>2,d=(d&3)<<4|e>>4,k=(e&15)<<2|f>>6,g=f&63,isNaN(e)?k=g=64:isNaN(f)&&(g=64),c=c+lt.LTHelper._base64Table.charAt(h)+lt.LTHelper._base64Table.charAt(d)+lt.LTHelper._base64Table.charAt(k)+lt.LTHelper._base64Table.charAt(g);return c};lt.LTHelper.base64Decode=function(a){if(null==a)return null;if(!a.length)return[];for(var b="",c,d,e,f,h,k=0,a=a.replace(/[^A-Za-z0-9\+\/\=]/g,"");k<a.length;)c=lt.LTHelper._base64Table.indexOf(a.charAt(k++)),d=lt.LTHelper._base64Table.indexOf(a.charAt(k++)),
f=lt.LTHelper._base64Table.indexOf(a.charAt(k++)),h=lt.LTHelper._base64Table.indexOf(a.charAt(k++)),c=c<<2|d>>4,d=(d&15)<<4|f>>2,e=(f&3)<<6|h,b+=String.fromCharCode(c),64!==f&&(b+=String.fromCharCode(d)),64!==h&&(b+=String.fromCharCode(e));return lt.LTHelper._utF8Decode(b)};lt.LTHelper.base64DecodeToArrayBuffer=function(a){for(var a=window.atob(a),b=a.length,c=new Uint8Array(b),d=0;d<b;d++)c[d]=a.charCodeAt(d);return c.buffer};lt.LTHelper.base64DecodeToByteArray=function(a){for(var a=window.atob(a),
b=a.length,c=Array(b),d=0;d<b;d++)c[d]=a.charCodeAt(d);return c};lt.LTHelper.base64EncodeFromArrayBuffer=function(a){for(var a=new DataView(a),b="",c,d,e,f,h=!1,k=!1,g=0,j=a.byteLength;g<j;)c=lt.LTHelper._getUint8(a,g++),g<j?d=lt.LTHelper._getUint8(a,g++):(d=0,h=!0),g<j?e=lt.LTHelper._getUint8(a,g++):(e=0,k=!0),f=c>>2,c=(c&3)<<4|d>>4,d=(d&15)<<2|e>>6,e&=63,h?d=e=64:k&&(e=64),b=b+lt.LTHelper._charAt(lt.LTHelper._base64Table,f)+lt.LTHelper._charAt(lt.LTHelper._base64Table,c)+lt.LTHelper._charAt(lt.LTHelper._base64Table,
d)+lt.LTHelper._charAt(lt.LTHelper._base64Table,e);return b};lt.LTHelper._getUint8=function(a,b){return a.getUint8(b)};lt.LTHelper._charAt=function(a,b){return a.charAt(b)};lt.LTHelper._utF8Encode=function(a,b){for(var c="",d=0;d<a.length;d++){var e=a[d];if(!b||!(10===e&&0<d&&13===a[d-1]))128>e?c+=String.fromCharCode(e):(127<e&&2048>e?c+=String.fromCharCode(e>>6|192):(c+=String.fromCharCode(e>>12|224),c+=String.fromCharCode(e>>6&63|128)),c+=String.fromCharCode(e&63|128))}return c};lt.LTHelper._utF8Decode=
function(a){for(var b=[],c=0,d=0,e=0,f=0;c<a.length;)d=a.charCodeAt(c),128>d?(b.push(d),c++):191<d&&224>d?(e=a.charCodeAt(c+1),d=(d&31)<<6|e&63,b.push(d),c+=2):(e=a.charCodeAt(c+1),f=a.charCodeAt(c+2),d=(d&15)<<12|(e&63)<<6|f&63,b.push(d),c+=3);return b};lt.LTHelper.loadJS=function(a,b){for(var c=!1,d=document.getElementsByTagName("script"),e=d.length;0<=e&&!c;e--)null!=d[e]&&null!=d[e].getAttribute("src")&&-1!==d[e].getAttribute("src").indexOf(a)&&(c=!0);c||(c=document.createElement("script"),c.setAttribute("type",
"text/javascript"),c.setAttribute("src",a),c.onload=b,document.getElementsByTagName("head")[0].appendChild(c))};lt.LTHelper.removeJS=function(a){for(var b=document.getElementsByTagName("script"),c=b.length;0<=c;c--)null!=b[c]&&null!=b[c].getAttribute("src")&&-1!==b[c].getAttribute("src").indexOf(a)&&b[c].parentNode.removeChild(b[c])};lt.LTHelper._canDrawOnCanvas=function(a){var b=!1;try{var c=a.getContext("2d");if(null==c)return!1;c.beginPath();var d=a.width,e=a.height;c.rect(d-1,e-1,1,1);c.fillStyle=
"red";c.fill();var f=c.getImageData(d-1,e-1,1,1);null!=f&&255===f.data[0]&&(b=!0);c.clearRect(0,0,d,e)}catch(h){}return b};lt.LTHelper.ensureUsableCanvas=function(a){if(null==a)return 0;var b=a.width,c=a.height;if(!b||!c)return 1;var d=1;if(!lt.LTHelper._canDrawOnCanvas(a)){for(var d=[4,2],e=-1,f=0,h=0;h<d.length&&-1===e;h++)f=1024*d[h],a.width=f,a.height=f,lt.LTHelper._canDrawOnCanvas(a)&&(e=h);-1!==e?(d=f/b,d*c>f&&(d=f/c)):d=0}a.width=parseInt(d*b+0.5);a.height=parseInt(d*c+0.5);return d};lt._base64=
function(){};lt._base64._init=function(){if(!lt._base64._initialized){lt._base64._arr="undefined"!==typeof Uint8Array?Uint8Array:Array;var a;lt._base64._lookup=Array(64);for(a=0;64>a;a++)lt._base64._lookup[a]="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"[a];lt._base64._revLookup=Array(66);for(a=0;64>a;++a)lt._base64._revLookup["ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".charCodeAt(a)]=a;lt._base64._revLookup[45]=62;lt._base64._revLookup[95]=63;lt._base64._initialized=
!0}};lt._base64.toByteArray=function(a){lt._base64._init();var b,c,d,e;b=a.length;var f;if(0<b%4)throw Error("Invalid string. Length must be a multiple of 4");e="="===a[b-2]?2:"="===a[b-1]?1:0;f=new lt._base64._arr(3*b/4-e);c=0<e?b-4:b;var h=0;for(b=0;b<c;b+=4)d=lt._base64._revLookup[a.charCodeAt(b)]<<18|lt._base64._revLookup[a.charCodeAt(b+1)]<<12|lt._base64._revLookup[a.charCodeAt(b+2)]<<6|lt._base64._revLookup[a.charCodeAt(b+3)],f[h++]=(d&16711680)>>16,f[h++]=(d&65280)>>8,f[h++]=d&255;2===e?(d=
lt._base64._revLookup[a.charCodeAt(b)]<<2|lt._base64._revLookup[a.charCodeAt(b+1)]>>>4,f[h++]=d&255):1===e&&(d=lt._base64._revLookup[a.charCodeAt(b)]<<10|lt._base64._revLookup[a.charCodeAt(b+1)]<<4|lt._base64._revLookup[a.charCodeAt(b+2)]>>>2,f[h++]=d>>8&255,f[h++]=d&255);return f};lt._base64._tripletToBase64=function(a){return lt._base64._lookup[a>>18&63]+lt._base64._lookup[a>>12&63]+lt._base64._lookup[a>>6&63]+lt._base64._lookup[a&63]};lt._base64.encodeChunk=function(a,b,c){for(var d=[],e=b;e<c;e+=
3)b=(a[e]<<16)+(a[e+1]<<8)+a[e+2],b=lt._base64._tripletToBase64(b),d.push(b);return d.join("")};lt._base64.fromByteArray=function(a){lt._base64._init();for(var b,c=a.length,d=c%3,e="",f=[],h=0,k=c-d;h<k;h+=16383)b=lt._base64.encodeChunk(a,h,h+16383>k?k:h+16383),f.push(b);1===d?(a=a[c-1],e+=lt._base64._lookup[a>>2],e+=lt._base64._lookup[a<<4&63],e+="=="):2===d&&(a=(a[c-2]<<8)+a[c-1],e+=lt._base64._lookup[a>>10],e+=lt._base64._lookup[a>>4&63],e+=lt._base64._lookup[a<<2&63],e+="=");f.push(e);return f.join("")};
lt._imageGroupLoaderItem=function(){};lt._imageGroupLoaderItem.prototype={loader:null,tag:null,isWorking:!1,isCompleted:!1};lt._loadIterator=function(){this._max=this._min=this._origin=-1;this._reset()};lt._loadIterator.prototype={_reset:function(){this._radius=0;this._didPositive=this._didNegative=this._didAllPositive=this._didAllNegative=!1},_radius:0,_didPositive:!1,_didNegative:!1,_didAllPositive:!1,_didAllNegative:!1,_origin:0,get_origin:function(){return this._origin},set_origin:function(a){this._origin=
a;this._reset();return a},setOriginToMiddle:function(a,b){this._origin=a===b?a:Math.floor((b+a)/2);this._reset()},_min:0,get_min:function(){return this._min},set_min:function(a){a!==this._min&&(this._min=a,this._min>this._origin&&(this._origin=this._min));this._reset();return a},_max:0,get_max:function(){return this._max},set_max:function(a){a!==this._max&&(this._max=a,this._max<this._origin&&(this._origin=this._max));this._reset();return a},get_isDone:function(){return this._didAllNegative&&this._didAllPositive||
-1===this._min||-1===this._origin||-1===this._max},next:function(){if(this._origin<this._min||this._origin>this._max)throw new lt.InvalidOperationException(String.format("Origin ({0}) is outside for bounds {1} to {2}",this._origin,this._min,this._max));if(this.get_isDone())throw new lt.InvalidOperationException(String.format("Iterator is complete for bounds {0} to {1}",this._min,this._max));var a=this._origin;if(this._radius)if(!this._didAllPositive&&!this._didPositive){if(this._didPositive=!0,a+=
this._radius,a>this._max)throw new lt.InvalidOperationException(String.format("Next Iterator value ({0}) is outside of maximum of {1}",a,this._max));}else{if(!this._didAllNegative&&!this._didNegative&&(this._didNegative=!0,a-=this._radius,a<this._min))throw new lt.InvalidOperationException(String.format("Next Iterator value ({0}) is outside of minimum of {1}",a,this._min));}else this._didPositive=this._didNegative=!0;if((this._didNegative||this._didAllNegative)&&(this._didPositive||this._didAllPositive))!this._didAllNegative&&
this._origin-this._radius===this._min&&(this._didAllNegative=!0),!this._didAllPositive&&this._origin+this._radius===this._max&&(this._didAllPositive=!0),this._radius++,this._didPositive=this._didNegative=!1;return a}};lt.ImageGroupLoader=function(a,b){this._imagesDiv=b;0<a?(this._useDoneCount=!0,this._doneCount=a):(this._useDoneCount=!1,this._doneCount=-1);this._maximumLoadersWorking=this._currentLoadersWorking=0;this._loaders=[];this._lazyLoad=!1;this._loadersFinished=0;this._loadersIterator=new lt._loadIterator;
this._minImageTagIndex=this._maxImageTagIndex=-1;this._isDisposed=this._isWorking=this._isAborted=this._aborting=!1};lt.ImageGroupLoader.prototype={add_allDone:function(a){this.__allDone=ss.Delegate.combine(this.__allDone,a)},remove_allDone:function(a){this.__allDone=ss.Delegate.remove(this.__allDone,a)},__allDone_handler_get:function(){null==this.__allDone_handler&&(this.__allDone_handler=ss.EventHandler.create(this,this.add_allDone,this.remove_allDone));return this.__allDone_handler},__allDone:null,
__allDone_handler:null,_currentLoadersWorking:0,_loadersFinished:0,_loaders:null,_aborting:!1,_isDisposed:!1,abort:function(a){this._aborting=!0;this._isWorking=!1;if(a)for(a=0;a<this._loaders.length;a++){var b=this._loaders[a];null!=b&&null!=b.loader&&!b.isCompleted&&b.loader.abort()}},get_isAborting:function(){return this._aborting},_isAborted:!1,get_isAborted:function(){return this._isAborted},_isWorking:!1,get_isWorking:function(){return this._isWorking},_imagesDiv:null,get_imagesDiv:function(){return this._imagesDiv},
_useDoneCount:!1,_doneCount:0,get_doneCount:function(){return this._doneCount},_addAbortedToDoneCount:!1,get_addAbortedToDoneCount:function(){return this._addAbortedToDoneCount},set_addAbortedToDoneCount:function(a){return this._addAbortedToDoneCount=a},_maximumLoadersWorking:0,get_maximumLoadersWorking:function(){return this._maximumLoadersWorking},set_maximumLoadersWorking:function(a){if(this._maximumLoadersWorking!==a&&(this._maximumLoadersWorking=a,0<this._loaders.length))for(var b=this._maximumLoadersWorking-
this._currentLoadersWorking,c=0;c<b;c++)this._checkNextLoader();return a},_lazyLoad:!1,get_lazyLoad:function(){return this._lazyLoad},set_lazyLoad:function(a){if(this._lazyLoad!==a){if(this._loaders.length)if(a)this._loadersIterator.set_min(this._minImageTagIndex),this._loadersIterator.set_max(this._maxImageTagIndex),this._loadersIterator.set_origin(Math.min(Math.max(this._loadersIterator.get_origin(),this._minImageTagIndex),this._maxImageTagIndex));else{this._loadersIterator.set_min(0);this._loadersIterator.set_max(this._loaders.length-
1);for(var b=this._maximumLoadersWorking-this._currentLoadersWorking,c=0;c<b;c++)this._checkNextLoader()}this._lazyLoad=a}return a},_failOnInvalidTags:!1,get_failOnInvalidTags:function(){return this._failOnInvalidTags},set_failOnInvalidTags:function(a){return this._failOnInvalidTags=a},_minImageTagIndex:0,_maxImageTagIndex:0,_getImageTagIndices:function(a,b){for(var c=-1,d=-1,e=0;e<this._loaders.length;e++){var f=this._loaders[e];f.tag===a&&(c=e);f.tag===b&&(d=e);if(-1<c&&-1<d)break}return[c,d]},
setImageTag:function(a){this.setImageTags(a,a)},setImageTags:function(a,b){if(!this._useDoneCount||this._isWorking)if(null==a||null==b){if(this._failOnInvalidTags)throw new lt.InvalidOperationException("ImageGroupLoader tags cannot be null");}else if(!(this._loaders[this._minImageTagIndex]===a&&this._loaders[this._maxImageTagIndex]===b)){var c=this._getImageTagIndices(a,b),d=c[0],c=c[1];if(-1===d||-1===c){if(this._failOnInvalidTags)throw new lt.InvalidOperationException("Tags do not exist in ImageGroupLoader");
}else if(!(-1!==this._minImageTagIndex&&-1!==this._maxImageTagIndex&&d>=this._minImageTagIndex&&c<=this._maxImageTagIndex)){if(this._lazyLoad){this._loadersIterator.set_min(d);this._loadersIterator.set_max(c);this._loadersIterator.setOriginToMiddle(d,c);for(var e=this._maximumLoadersWorking-this._currentLoadersWorking,f=0;f<e;f++)this._checkNextLoader()}else this._loadersIterator.setOriginToMiddle(d,c);this._minImageTagIndex=d;this._maxImageTagIndex=c}}},_loadersIterator:null,_createImageLoaderCallbacks:function(a){var b=
this,c=null,d=null,c=function(){a.loader.remove_always(c);a.loader.remove__aborted(d);b._resolveImageLoaderComplete(a,!1)},d=function(){a.loader.remove_always(c);a.loader.remove__aborted(d);b._resolveImageLoaderComplete(a,!0)};a.loader.add_always(c);a.loader.add__aborted(d)},_resolveImageLoaderComplete:function(a,b){this._isDisposed||-1===this._loaders.indexOf(a)||(a.isCompleted=!0,(!b||this._addAbortedToDoneCount)&&this._loadersFinished++,a.isWorking&&(a.isWorking=!1,this._currentLoadersWorking--),
this._checkNextLoader())},_checkNextLoader:function(){if(!this._isDisposed){if(this._aborting)this._currentLoadersWorking||(this._aborting=!1,this._isAborted=!0,null!=this.__allDone&&this.__allDone(this,lt.LeadEventArgs.Empty));else if(this._isWorking&&this._useDoneCount){if(this._loadersFinished===this._doneCount){this._isWorking=!1;null!=this.__allDone&&this.__allDone(this,lt.LeadEventArgs.Empty);return}if(this._loadersFinished+this._currentLoadersWorking===this._doneCount)return}(!this._maximumLoadersWorking||
this._currentLoadersWorking<this._maximumLoadersWorking)&&this._callNextLoader()}},_callNextLoader:function(){var a=null,a=0;do{if(this._loadersIterator.get_isDone())return;a=this._loadersIterator.next();a=this._loaders[a]}while(a.isCompleted||a.isWorking||null==a.loader||a.loader.get_isWorking()||!a.loader.get_canRun());this._currentLoadersWorking++;a.isWorking=!0;a.loader.run()},load:function(a,b,c){this._isWorking=!0;var d=new lt._imageGroupLoaderItem;d.loader=a;d.tag=b;d.isWorking=!1;d.isCompleted=
!1;null!=a&&(a.set_imagesHolder(this._imagesDiv),this._createImageLoaderCallbacks(d));if(this._lazyLoad&&(-1!==this._minImageTagIndex&&c<=this._minImageTagIndex&&(this._minImageTagIndex=-1),-1!==this._minImageTagIndex&&c>=this._maxImageTagIndex))this._maxImageTagIndex=-1;!this._loaders.length&&!this._lazyLoad&&(this._loadersIterator.set_min(0),this._loadersIterator.set_origin(0),this._loadersIterator.set_max(0));if(0<this._loaders.length){a=this._loadersIterator.get_origin();b=this._loadersIterator.get_max();
if(this.get_lazyLoad()){var e=this._loadersIterator.get_min();c<=e&&e++;c<=a&&a++;c<=b&&b++;this._loadersIterator.set_min(e)}else c<=a&&a++,b++;this._loadersIterator.set_origin(a);this._loadersIterator.set_max(b)}this._loaders.insert(c,d);this._checkNextLoader()},remove:function(a){var b=this._loaders[a];if(null!=b){this._loaders.removeAt(a);if(this._lazyLoad&&(-1!==this._minImageTagIndex&&a>=this._minImageTagIndex&&a<=this._maxImageTagIndex&&(this._minImageTagIndex=-1),-1!==this._maxImageTagIndex&&
a>=this._minImageTagIndex&&a<=this._maxImageTagIndex))this._maxImageTagIndex=-1;if(this._loaders.length){var c=this._loadersIterator.get_min();c&&a<c&&c--;this._loadersIterator.set_min(c);(a=this._loadersIterator.get_max())&&a--;this._loadersIterator.set_max(a);var d=this._loadersIterator.get_origin();this._loadersIterator.set_origin(Math.min(Math.max(d,c),a))}else this._loadersIterator.set_min(-1),this._loadersIterator.set_origin(-1),this._loadersIterator.set_max(-1);null!=b.loader&&b.loader.abort()}},
dispose:function(){this.abort(!0);this._loadersIterator=this._loaders=null;this._aborting=this._isAborted=this._isWorking=!1;this._isDisposed=!0}};Object.defineProperty(lt.ImageGroupLoader.prototype,"allDone",{get:lt.ImageGroupLoader.prototype.__allDone_handler_get,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageGroupLoader.prototype,"isAborting",{get:lt.ImageGroupLoader.prototype.get_isAborting,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageGroupLoader.prototype,"isAborted",
{get:lt.ImageGroupLoader.prototype.get_isAborted,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageGroupLoader.prototype,"isWorking",{get:lt.ImageGroupLoader.prototype.get_isWorking,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageGroupLoader.prototype,"imagesDiv",{get:lt.ImageGroupLoader.prototype.get_imagesDiv,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageGroupLoader.prototype,"doneCount",{get:lt.ImageGroupLoader.prototype.get_doneCount,enumerable:!0,configurable:!0});
Object.defineProperty(lt.ImageGroupLoader.prototype,"addAbortedToDoneCount",{get:lt.ImageGroupLoader.prototype.get_addAbortedToDoneCount,set:lt.ImageGroupLoader.prototype.set_addAbortedToDoneCount,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageGroupLoader.prototype,"maximumLoadersWorking",{get:lt.ImageGroupLoader.prototype.get_maximumLoadersWorking,set:lt.ImageGroupLoader.prototype.set_maximumLoadersWorking,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageGroupLoader.prototype,
"lazyLoad",{get:lt.ImageGroupLoader.prototype.get_lazyLoad,set:lt.ImageGroupLoader.prototype.set_lazyLoad,enumerable:!0,configurable:!0});Object.defineProperty(lt.ImageGroupLoader.prototype,"failOnInvalidTags",{get:lt.ImageGroupLoader.prototype.get_failOnInvalidTags,set:lt.ImageGroupLoader.prototype.set_failOnInvalidTags,enumerable:!0,configurable:!0});lt.CanvasHelper=function(){};lt.CanvasHelper._toInt=function(a){return 0>a?parseInt(a-0.5):parseInt(a+0.5)};lt.CanvasHelper._drawOnCanvas=function(a,
b,c){if(null==a||null==b)return 0;var d=a.width,e=a.height;if(!d||!e)return 1;var f=1;if(!lt.CanvasHelper._canDrawOnCanvas(a,b,c)){for(var f=[2,4,8,16],h=-1,k=0,g=0,j=0;j<f.length&&-1===h;j++)k=lt.CanvasHelper._toInt(d/f[j]),g=lt.CanvasHelper._toInt(e/f[j]),0<k&&0<g&&(a.width=k,a.height=g,lt.CanvasHelper._canDrawOnCanvas(a,b,c)&&(h=j));f=-1!==h&&0<k?k/d:0}return f};lt.CanvasHelper._canDrawOnCanvas=function(a,b,c){if(null==a||null==b)return!1;var d=!1,e=null;try{e=a.getContext("2d");if(null==e)return!1;
e.beginPath();var f=a.width,h=a.height;e.rect(f-1,h-1,1,1);e.fillStyle="red";e.fill();var k=e.getImageData(f-1,h-1,1,1);null!=k&&255===k.data[0]&&(d=!0);e.clearRect(0,0,f,h);d&&(a=1,3===lt.LTHelper.OS&&(a=lt.CanvasHelper._detectVerticalSquash(b,c)),1===a?e.drawImage(b,0,0,f,h):e.drawImage(b,0,0,c.get_width()*a,c.get_height()*a,0,0,f,h))}catch(g){}finally{}return d};lt.CanvasHelper._detectVerticalSquash=function(a,b){parseInt(b.get_width()+0.5);var c=parseInt(b.get_height()+0.5),d=document.createElement("canvas");
d.width=1;d.height=c;d=d.getContext("2d");d.drawImage(a,0,0);for(var d=d.getImageData(0,0,1,c).data,e=0,f=c,h=c;h>e;)d[4*(h-1)+3]?e=h:f=h,h=f+e>>1;c=h/c;return!c?1:c};lt.CanvasHelper.prototype={_size:null,get_size:function(){return this._size.clone()},_scale:0,get_scale:function(){return this._scale},_autoScale:!0,get_autoScale:function(){return this._autoScale},set_autoScale:function(a){return this._autoScale=a},_maximumSize:0,get_maximumSize:function(){return this._maximumSize},set_maximumSize:function(a){if(0>
a)throw new lt.ArgumentOutOfRangeException("MaximumSize",a,"Must be a value greater than or equal to 0");return this._maximumSize=a},create:function(a,b){this._scale=1;this._size=b.clone();if(this._size.get_isEmpty())return null;var c=document.createElement("canvas"),d=lt.CanvasHelper._toInt(this._size.get_width()),e=lt.CanvasHelper._toInt(this._size.get_height());this._scale=1;var f=this.get_autoScale(),h=this.get_maximumSize();if(null!=a&&f&&0<h&&(d>h||e>h))this._scale=d>e?h/d:h/e,d=lt.CanvasHelper._toInt(d*
this._scale),e=lt.CanvasHelper._toInt(e*this._scale);c.width=d;c.height=e;if(f){f=null!=a?lt.CanvasHelper._drawOnCanvas(c,a,b):lt.LTHelper.ensureUsableCanvas(c);if(!f)return null;lt.LeadDoubleTools.areClose(f,1)||(d=lt.CanvasHelper._toInt(d*f),e=lt.CanvasHelper._toInt(e*f));this._scale*=f}lt.LeadDoubleTools.areClose(this._scale,1)||(this._size=lt.LeadSizeD.create(d,e));if(null==a){f=null;try{f=c.getContext("2d"),f.clearRect(0,0,d,e)}catch(k){}finally{}}return c},create2:function(a,b){if(b.get_isEmpty()||
null==a)return null;this._size=b.clone();var c=b.get_width(),d=b.get_height(),e=document.createElement("canvas"),f=this.get_maximumSize();if(0<f&&(c>f||d>f))this._scale=c>d?f/c:f/d,c=lt.CanvasHelper._toInt(c*this._scale),d=lt.CanvasHelper._toInt(d*this._scale);e.width=parseInt(c);e.height=parseInt(d);(this._scale=lt.LTHelper.ensureUsableCanvas(e))?1!==this._scale&&(c*=this._scale,d*=this._scale):this._scale=1;e.getContext("2d").drawImage(a,0,0,c,d);return e}};Object.defineProperty(lt.CanvasHelper.prototype,
"size",{get:lt.CanvasHelper.prototype.get_size,enumerable:!0,configurable:!0});Object.defineProperty(lt.CanvasHelper.prototype,"scale",{get:lt.CanvasHelper.prototype.get_scale,enumerable:!0,configurable:!0});Object.defineProperty(lt.CanvasHelper.prototype,"autoScale",{get:lt.CanvasHelper.prototype.get_autoScale,set:lt.CanvasHelper.prototype.set_autoScale,enumerable:!0,configurable:!0});Object.defineProperty(lt.CanvasHelper.prototype,"maximumSize",{get:lt.CanvasHelper.prototype.get_maximumSize,set:lt.CanvasHelper.prototype.set_maximumSize,
enumerable:!0,configurable:!0});lt._lT_VersionNumber.registerClass("lt._lT_VersionNumber");lt.NotifyLeadCollectionChangedEventArgs.registerClass("lt.NotifyLeadCollectionChangedEventArgs",lt.LeadEventArgs);lt.LeadCollection.registerClass("lt.LeadCollection");lt.LeadEventArgs.registerClass("lt.LeadEventArgs");lt.LeadEvent.registerClass("lt.LeadEvent");lt.ImageProcessingProgressEventArgs.registerClass("lt.ImageProcessingProgressEventArgs",lt.LeadEventArgs);lt.ImageProcessingCompletedEventArgs.registerClass("lt.ImageProcessingCompletedEventArgs",
lt.LeadEventArgs);lt.ImageProcessingErrorEventArgs.registerClass("lt.ImageProcessingErrorEventArgs",lt.LeadEventArgs);lt.ImageProcessing.registerClass("lt.ImageProcessing");lt.LeadLengthD.registerClass("lt.LeadLengthD");lt.LeadDoubleTools.registerClass("lt.LeadDoubleTools");lt.LeadMatrix.registerClass("lt.LeadMatrix");lt._leadMatrixUtil.registerClass("lt._leadMatrixUtil");lt.LeadPointD.registerClass("lt.LeadPointD");lt.LeadRectD.registerClass("lt.LeadRectD");lt.LeadSizeD.registerClass("lt.LeadSizeD");
lt.TextFontRuler.registerClass("lt.TextFontRuler");lt.ImageLoaderPreRunEventArgs.registerClass("lt.ImageLoaderPreRunEventArgs",lt.LeadEventArgs);lt.ImageLoaderAjaxOptions.registerClass("lt.ImageLoaderAjaxOptions");lt.ImageLoader.registerClass("lt.ImageLoader",null,ss.IDisposable);lt.LTHelper.registerClass("lt.LTHelper");lt._base64.registerClass("lt._base64");lt._imageGroupLoaderItem.registerClass("lt._imageGroupLoaderItem");lt._loadIterator.registerClass("lt._loadIterator");lt.ImageGroupLoader.registerClass("lt.ImageGroupLoader",
null,ss.IDisposable);lt.CanvasHelper.registerClass("lt.CanvasHelper");lt._lT_VersionNumber.l_VER_PRODUCT="LEADTOOLS\u00ae for JavaScript";lt._lT_VersionNumber.l_VER_COMPANYNAME_STR="LEAD Technologies, Inc.";lt._lT_VersionNumber.l_VER_LEGALTRADEMARKS_STR="LEADTOOLS\u00ae is a trademark of LEAD Technologies, Inc.";lt._lT_VersionNumber.l_VER_LEGALCOPYRIGHT_STR="\u00a9 1991-2017 LEAD Technologies, Inc.";lt._lT_VersionNumber.l_VER_DLLEXT=".dll";lt._lT_VersionNumber.l_VER_EXEEXT=".exe";lt._lT_VersionNumber.l_VER_PLATFORM=
"";lt._lT_VersionNumber.l_VER_PLATFORM_FOR="";lt._lT_VersionNumber.l_VER_PRODUCTNAME_STR="LEADTOOLS\u00ae for JavaScript";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_XXX="Leadtools.Xxx.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_XXX="LEADTOOLS Xxx";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_KERNEL="lt.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_KERNEL="Leadtools";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CONTROLS="lt.Controls.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CONTROLS=
"Controls";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_DOCUMENTS_UI="lt.Documents.UI.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_DOCUMENTS_UI="Documents User Interface";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CONTROLS_MEDICAL="lt.Controls.Medical.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CONTROLS_MEDICAL="Medical Controls";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_DOCUMENTS="lt.Documents.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_DOCUMENTS="Documents";
lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_CORE="lt.Annotations.Core.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_CORE="Annotations Core";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_AUTOMATION="lt.Annotations.Automation.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_AUTOMATION="Annotations Automation";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_DESIGNERS="lt.Annotations.Designers.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_DESIGNERS=
"Annotations Designers";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_RENDERING="lt.Annotations.Rendering.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_RENDERING="Annotations Rendering";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CCOW="Leadtools.Ccow.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CCOW="Leadtools CCOW Library";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_DOCUMENTS="Leadtools.Annotations.Documents.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_DOCUMENTS=
"Annotations Documents";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_LEGACY="Leadtools.Annotations.Legacy.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_LEGACY="Annotations Legacy";lt._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_JAVASCRIPT="Leadtools.Annotations.JavaScript.dll";lt._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_JAVASCRIPT="JavaScripot Annotations";lt._lT_VersionNumber.l_VER_PRODUCTVERSION_DOT_STR="********";lt._lT_VersionNumber.l_VER_FILEVERSION_DOT_STR=
"*********";lt.LeadEventArgs.Empty=new lt.LeadEventArgs;lt.LeadDoubleTools.naN=0/0;lt.LeadDoubleTools.positiveInfinity=1/0;lt.LeadDoubleTools.negativeInfinity=-1/0;lt.LeadMatrix._identity=lt.LeadMatrix._createIdentity();lt.LeadPointD._empty=lt.LeadPointD._createEmptyPoint();lt.LeadRectD._empty=lt.LeadRectD._createEmptyRect();lt.LeadSizeD._empty=lt.LeadSizeD._createEmptySize();lt.ImageLoader._mimeTypePNG="image/png";lt.ImageLoader._mimeTypeGIF="image/gif";lt.ImageLoader._mimeTypeJPEG="image/jpeg";
lt.ImageLoader._mimeTypeSVG="image/svg+xml";lt.LTHelper.requestCustomHeaders=null;lt.LTHelper.device=0;lt.LTHelper.OS=0;lt.LTHelper.browser=0;lt.LTHelper.version=0;lt.LTHelper.vendor=null;lt.LTHelper._supportsClassList=!1;lt.LTHelper._isConsoleAvailable=!1;lt.LTHelper._isConsoleApplyAvailable=!1;lt.LTHelper._consoleLogFunc="log";lt.LTHelper._consoleWarnFunc=null;lt.LTHelper._consoleErrorFunc=null;lt.LTHelper.supportsHTMLPointerEvents=!1;lt.LTHelper.supportsCSSTransitions=!1;lt.LTHelper.supportsFileReader=
!1;lt.LTHelper.supportsCanvas=!1;lt.LTHelper.supportsTypedArray=!1;lt.LTHelper.supportsTouch=!1;lt.LTHelper.supportsMultiTouch=!1;lt.LTHelper.supportsMouse=!1;lt.LTHelper.supportsScroll=!1;lt.LTHelper.supportsWebGL=!1;lt.LTHelper.supportsTransform=!1;lt.LTHelper.supportsTransitionEnd=!1;lt.LTHelper.supportsAnimationFrame=!1;lt.LTHelper.supportsWebWorker=!1;lt.LTHelper.msPointerEnabled=!1;lt.LTHelper.resizeEvent=null;lt.LTHelper.dragStartEvent=null;lt.LTHelper.dragDeltaEvent=null;lt.LTHelper.dragCompletedEvent=
null;lt.LTHelper.dragCancelEvent=null;lt.LTHelper.mouseWheelEvent=null;lt.LTHelper.licenseDirectory="LEADTOOLS";lt.LTHelper._lastAnimationFrameRequestTime=0;lt.LTHelper._base64Table="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";lt.LTHelper._detectBrowser();lt.LTHelper._detectCapabilities();lt.LTHelper._setupConsole();lt.LTHelper._setupRequestAnimationFrame();lt.LTHelper.requestCustomHeaders={};lt._base64._lookup=null;lt._base64._revLookup=null;lt._base64._arr=null;lt._base64._initialized=
!1})();
