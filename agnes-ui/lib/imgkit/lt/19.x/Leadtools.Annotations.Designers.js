/**************************************************
LEADTOOLS (C) 1991-2017 LEAD Technologies, Inc. ALL RIGHTS RESERVED.
This software is protected by United States and International copyright laws.
Any copying, duplication, deployment, redistribution, modification or other
disposition hereof is STRICTLY PROHIBITED without an express written license
granted by LEAD Technologies, Inc.. This notice may not be removed or otherwise
altered under any circumstances.
Portions of this product are licensed under US patent 5,327,254 and foreign
counterparts.
For more information, contact LEAD Technologies, Inc. at 704-332-5532 or visit
https://www.leadtools.com
**************************************************/
// Leadtools.Annotations.Designers.js
// Version:********
(function(){Type.registerNamespace("lt.Annotations.Designers");window.lt.Annotations.Designers._lT_VersionNumber=function(){};lt.Annotations.Designers._leadRectDExtensions=function(){};lt.Annotations.Designers._leadRectDExtensions.inflate=function(b,c,a){b.inflate(c,a);return b};lt.Annotations.Designers._leadRectDExtensions.intersectRects=function(b,c){return lt.LeadRectD.intersectRects(b,c)};lt.Annotations.Designers._leadRectDExtensions.intersectsWith=function(b,c){var a=!1;return a=b.intersectsWith(c)};
lt.Annotations.Designers._leadRectDExtensions.create=function(b,c,a,d){return lt.LeadRectD.create(b,c,a,d)};lt.Annotations.Designers._leadRectDExtensions.unionRects=function(b,c){return lt.LeadRectD.unionRects(b,c)};lt.Annotations.Designers._leadRectDExtensions.unionPoint=function(b,c){return lt.Annotations.Designers._leadRectDExtensions.unionRects(b,lt.LeadRectD.create(c.get_x(),c.get_y(),0,0))};lt.Annotations.Designers._leadRectDExtensions.clone=function(b){return b.clone()};lt.Annotations.Designers._leadRectDExtensions.isEmpty=
function(b){return b.get_isEmpty()};lt.Annotations.Designers._leadRectDExtensions.fromLTRB=function(b,c,a,d){return lt.LeadRectD.fromLTRB(b,c,a,d)};lt.Annotations.Designers._leadRectDExtensions.get_empty=function(){return lt.LeadRectD.get_empty()};lt.Annotations.Designers._leadRectDExtensions.containsRect=function(b,c){return b.containsRect(c)};lt.Annotations.Designers._leadRectDExtensions.containsPoint=function(b,c){return b.containsPoint(c)};lt.Annotations.Designers._leadRectDExtensions.topLeft=
function(b){return b.get_topLeft()};lt.Annotations.Designers._leadRectDExtensions.topRight=function(b){return b.get_topRight()};lt.Annotations.Designers._leadRectDExtensions.bottomLeft=function(b){return b.get_bottomLeft()};lt.Annotations.Designers._leadRectDExtensions.bottomRight=function(b){return b.get_bottomRight()};lt.Annotations.Designers._leadRectDExtensions.top=function(b){return b.get_top()};lt.Annotations.Designers._leadRectDExtensions.left=function(b){return b.get_left()};lt.Annotations.Designers._leadRectDExtensions.bottom=
function(b){return b.get_bottom()};lt.Annotations.Designers._leadRectDExtensions.right=function(b){return b.get_right()};lt.Annotations.Designers._leadRectDExtensions.equals=function(b,c){return b===c};lt.Annotations.Designers._leadPointDExtensions=function(){};lt.Annotations.Designers._leadPointDExtensions.create=function(b,c){return lt.LeadPointD.create(b,c)};lt.Annotations.Designers._leadPointDExtensions.isEqual=function(b,c){return b===c};lt.Annotations.Designers._leadPointDExtensions.clone=function(b){return b.clone()};
lt.Annotations.Designers._leadPointDExtensions.get_empty=function(){return lt.LeadPointD.get_empty()};lt.Annotations.Designers._leadPointDExtensions.isEmpty=function(b){return b.get_isEmpty()};lt.Annotations.Designers._leadLengthDExtensions=function(){};lt.Annotations.Designers._leadLengthDExtensions.isEqual=function(b,c){return b===c};lt.Annotations.Designers._leadLengthDExtensions.create=function(b){return lt.LeadLengthD.create(b)};lt.Annotations.Designers._leadLengthDExtensions.clone=function(b){return b.clone()};
lt.Annotations.Designers._leadSizeDExtensions=function(){};lt.Annotations.Designers._leadSizeDExtensions.create=function(b,c){return lt.LeadSizeD.create(b,c)};lt.Annotations.Designers._leadSizeDExtensions.clone=function(b){return b.clone()};lt.Annotations.Designers._leadSizeDExtensions.get_empty=function(){return lt.LeadSizeD.get_empty()};lt.Annotations.Designers._leadSizeDExtensions.isEmpty=function(b){return b.get_isEmpty()};lt.Annotations.Designers.AnnDesigner=function(b,c,a){this._snapToGridOptions=
new lt.Annotations.Core.AnnSnapToGridOptions;this._automationControl=b;this._targetObject=a;this._container=c};lt.Annotations.Designers.AnnDesigner.prototype={get__enableSnapToGrid:function(){return null!=this.get_snapToGridOptions()&&this.get_snapToGridOptions().get_enableSnap()},_isMouseLeftButtonDown:!1,get_isMouseLeftButtonDown:function(){return this._isMouseLeftButtonDown},set_isMouseLeftButtonDown:function(b){return this._isMouseLeftButtonDown=b},_targetObject:null,get_targetObject:function(){return this._targetObject},
set_targetObject:function(b){return this._targetObject=b},get_finalTargetObject:function(){return this._targetObject},_container:null,get_container:function(){return this._container},_restrictDesigners:!1,get_restrictDesigners:function(){return this._restrictDesigners},set_restrictDesigners:function(b){return this._restrictDesigners=b},get_clipRectangle:function(){var b=lt.Annotations.Designers._leadRectDExtensions.get_empty();null!=this._container&&(b=lt.Annotations.Designers._leadRectDExtensions.create(0,
0,this._container.get_size().get_width(),this._container.get_size().get_height()));return b},clipPoint:function(b,c){if(lt.Annotations.Designers._leadPointDExtensions.isEmpty(b)||lt.Annotations.Designers._leadRectDExtensions.isEmpty(c)||!this._restrictDesigners)return this._targetObject.get_id()!==lt.Annotations.Core.AnnObject.crossProductObjectId?this.snapPointToGrid(b,!1):b;var a=lt.Annotations.Designers._leadPointDExtensions.clone(b);lt.Annotations.Designers._leadRectDExtensions.containsPoint(c,
a)||(a.set_x(Math.min(lt.Annotations.Designers._leadRectDExtensions.right(c),Math.max(c.get_x(),a.get_x()))),a.set_y(Math.min(lt.Annotations.Designers._leadRectDExtensions.bottom(c),Math.max(c.get_y(),a.get_y()))));return this._targetObject.get_id()!==lt.Annotations.Core.AnnObject.crossProductObjectId?this.snapPointToGrid(a,!1):a},getRenderer:function(){var b=null,c=this._automationControl.get_renderingEngine();null!=c&&null!=c.get_renderers()&&Object.keyExists(c.get_renderers(),this.get_targetObject().get_id())&&
(b=c.get_renderers()[this.get_targetObject().get_id()],b.initialize(c));return b},_automationControl:null,get_automationControl:function(){return this._automationControl},invalidate:function(b){if(null!=this._automationControl){var c=this._automationControl.get_renderingEngine();if(null!=c)if(c.get_stateless())this._automationControl.automationInvalidate(b);else for(var b=this.getRenderer(),a=this._automationControl.get_automationGetContainersCallback()(),a=ss.IEnumerator.getEnumerator(a);a.moveNext();){var d=
a.current;d.get_children().contains(this.get_targetObject())&&(c.attachContainer(d),b.render(d.get_mapper(),this.get_targetObject()))}}},_hasStarted:!1,get_hasStarted:function(){return this._hasStarted},start:function(){this.get_hasStarted()||(this._hasStarted=!0)},end:function(){this.get_hasStarted()&&(this._hasStarted=!1)},onPointerDoubleClick:function(){return!1},onPointerDown:function(b,c){!this._isMouseLeftButtonDown&&c.get_button()===lt.Annotations.Core.AnnMouseButton.left&&(this._isMouseLeftButtonDown=
!0);return!1},onPointerUp:function(b,c){c.get_button()===lt.Annotations.Core.AnnMouseButton.left&&(this._isMouseLeftButtonDown=!1);return!1},onPointerMove:function(){return!1},_getRotationAngle:function(b){return 180*Math.atan2(b.get_m21(),b.get_m11())/Math.PI},snapPointToGrid:function(b,c){if(lt.Annotations.Designers._leadPointDExtensions.isEmpty(b))return b;var a=this.get_finalTargetObject().get_id();if(a===lt.Annotations.Core.AnnObject.freehandObjectId||a===lt.Annotations.Core.AnnObject.freehandHotspotObjectId||
a===lt.Annotations.Core.AnnObject.selectObjectId||a===lt.Annotations.Core.AnnObject.textHiliteObjectId||a===lt.Annotations.Core.AnnObject.textRedactionObjectId||a===lt.Annotations.Core.AnnObject.textStrikeoutObjectId||a===lt.Annotations.Core.AnnObject.textUnderlineObjectId||a===lt.Annotations.Core.AnnObject.rectangleObjectId&&0<this._targetObject.get_points().get_count()&&this._targetObject.get_points().get_item(0).get_y()!==this._targetObject.get_points().get_item(1).get_y())return b;var a=lt.Annotations.Designers._leadPointDExtensions.clone(b),
d=this.get_snapToGridOptions();if(null!=d&&d.get_enableSnap()){d.get_lineSpacing();var d=this._getGridCellSize(),e=this._getRotationAngle(this._container.get_mapper().get_transform()),f=this._container.get_mapper().clone();f.updateTransform(lt.LeadMatrix.get_identity());var g=f.rectFromContainerCoordinates(lt.Annotations.Designers._leadRectDExtensions.create(0,0,this._container.get_size().get_width(),this._container.get_size().get_height()),lt.Annotations.Core.AnnFixedStateOperations.none),f=new lt.LeadMatrix,
h=this._container.get_mapper().get_targetDpiX(),k=this._container.get_mapper().get_targetDpiY();f.rotateAt(-e,g.get_width()/2,g.get_height()/2);var i=f.transformRect(g);f.translate((i.get_width()-g.get_width())/2,(i.get_height()-g.get_height())/2);a.set_x(a.get_x()*h*lt.Annotations.Designers.AnnDesigner._unit);a.set_y(a.get_y()*k*lt.Annotations.Designers.AnnDesigner._unit);a=f.transformPoint(a);a.set_x(a.get_x()/h/lt.Annotations.Designers.AnnDesigner._unit);a.set_y(a.get_y()/k/lt.Annotations.Designers.AnnDesigner._unit);
a.set_x(this._snapValue(a.get_x(),d.get_width()));a.set_y(this._snapValue(a.get_y(),d.get_height()));g=this.get_clipRectangle();90===Math.abs(e)&&(g.set_width(this.get_clipRectangle().get_height()),g.set_height(g.get_width()));if((c||Type.canCast(this,lt.Annotations.Designers.AnnDrawDesigner))&&this._restrictDesigners&&!lt.Annotations.Designers._leadRectDExtensions.containsPoint(g,a))a.get_x()>lt.Annotations.Designers._leadRectDExtensions.right(g)&&a.set_x(a.get_x()-d.get_width()),a.get_y()>lt.Annotations.Designers._leadRectDExtensions.bottom(g)&&
a.set_y(a.get_y()-d.get_height());a.set_x(a.get_x()*h*lt.Annotations.Designers.AnnDesigner._unit);a.set_y(a.get_y()*k*lt.Annotations.Designers.AnnDesigner._unit);f.invert();a=f.transformPoint(a);a.set_x(a.get_x()/h/lt.Annotations.Designers.AnnDesigner._unit);a.set_y(a.get_y()/k/lt.Annotations.Designers.AnnDesigner._unit)}return a},_getGridCellSize:function(){var b=lt.Annotations.Designers._leadSizeDExtensions.create(0,0),c=this.get_snapToGridOptions();null!=c&&c.get_enableSnap()&&(c=c.get_gridLength(),
b.set_width(c/this.get_container().get_mapper().get_targetDpiX()/lt.Annotations.Designers.AnnDesigner._unit),b.set_height(c/this.get_container().get_mapper().get_targetDpiY()/lt.Annotations.Designers.AnnDesigner._unit));return b},_snapValue:function(b,c){return Math.round(b/c)*c},get_snapToGridOptions:function(){return this._snapToGridOptions},set_snapToGridOptions:function(b){return this._snapToGridOptions=b}};Object.defineProperty(lt.Annotations.Designers.AnnDesigner.prototype,"isMouseLeftButtonDown",
{get:lt.Annotations.Designers.AnnDesigner.prototype.get_isMouseLeftButtonDown,set:lt.Annotations.Designers.AnnDesigner.prototype.set_isMouseLeftButtonDown,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDesigner.prototype,"targetObject",{get:lt.Annotations.Designers.AnnDesigner.prototype.get_targetObject,set:lt.Annotations.Designers.AnnDesigner.prototype.set_targetObject,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDesigner.prototype,
"finalTargetObject",{get:lt.Annotations.Designers.AnnDesigner.prototype.get_finalTargetObject,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDesigner.prototype,"container",{get:lt.Annotations.Designers.AnnDesigner.prototype.get_container,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDesigner.prototype,"restrictDesigners",{get:lt.Annotations.Designers.AnnDesigner.prototype.get_restrictDesigners,set:lt.Annotations.Designers.AnnDesigner.prototype.set_restrictDesigners,
enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDesigner.prototype,"clipRectangle",{get:lt.Annotations.Designers.AnnDesigner.prototype.get_clipRectangle,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDesigner.prototype,"automationControl",{get:lt.Annotations.Designers.AnnDesigner.prototype.get_automationControl,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDesigner.prototype,"hasStarted",{get:lt.Annotations.Designers.AnnDesigner.prototype.get_hasStarted,
enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDesigner.prototype,"snapToGridOptions",{get:lt.Annotations.Designers.AnnDesigner.prototype.get_snapToGridOptions,set:lt.Annotations.Designers.AnnDesigner.prototype.set_snapToGridOptions,enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnCrossProductDrawDesigner=function(b,c,a){lt.Annotations.Designers.AnnCrossProductDrawDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnCrossProductDrawDesigner.prototype=
{onPointerDown:function(b,c){var a=lt.Annotations.Designers.AnnCrossProductDrawDesigner.callBaseMethod(this,"onPointerDown",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;null!=this.get_targetObject()&&(a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnCrossProductObject),null!=a&&(a.set_firstStartPoint(a.set_firstEndPoint(this.snapPointToGrid(c.get_location(),!1))),a.updateSecondPoints(),this.startWorking()),a=!0);return a},onPointerMove:function(b,c){var a=
lt.Annotations.Designers.AnnCrossProductDrawDesigner.callBaseMethod(this,"onPointerMove",[b,c]);if(null!=this.get_targetObject()&&this.get_isMouseLeftButtonDown()){var d=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnCrossProductObject);null!=d&&(d.set_firstEndPoint(this.snapPointToGrid(c.get_location(),!1)),d.updateSecondPoints(),d.set_secondStartPoint(this.clipPoint(d.get_secondStartPoint(),this.get_clipRectangle())),d.set_secondEndPoint(this.clipPoint(d.get_secondEndPoint(),this.get_clipRectangle())),
this.working(),a=!0)}return a},onPointerUp:function(b,c){var a=lt.Annotations.Designers.AnnCrossProductDrawDesigner.callBaseMethod(this,"onPointerUp",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;if(null!=this.get_targetObject()){var a=!0,d=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnCrossProductObject);1<Math.abs(d.get_firstStartPoint().get_x()-d.get_firstEndPoint().get_x())||1<Math.abs(d.get_firstStartPoint().get_y()-d.get_firstEndPoint().get_y())?
this.endWorking():(this.cancel(),a=!1)}return a}};lt.Annotations.Designers.AnnDrawDesigner=function(b,c,a){this._invalidateRect$1=lt.Annotations.Designers._leadRectDExtensions.get_empty();lt.Annotations.Designers.AnnDrawDesigner.initializeBase(this,[b,c,a]);this._extendedMode$1=!1;this._extendedModeModifierKey$1=lt.Annotations.Core.AnnKeys.none};lt.Annotations.Designers.AnnDrawDesigner.prototype={_operationStatus$1:0,get_operationStatus:function(){return this._operationStatus$1},add_draw:function(b){this.__draw$1=
ss.Delegate.combine(this.__draw$1,b)},remove_draw:function(b){this.__draw$1=ss.Delegate.remove(this.__draw$1,b)},__draw$1_handler_get:function(){null==this.__draw$1_handler&&(this.__draw$1_handler=ss.EventHandler.create(this,this.add_draw,this.remove_draw));return this.__draw$1_handler},__draw$1:null,__draw$1_handler:null,start:function(){this.get_hasStarted();lt.Annotations.Designers.AnnDrawDesigner.callBaseMethod(this,"start")},end:function(){lt.Annotations.Designers.AnnDrawDesigner.callBaseMethod(this,
"end")},onDraw:function(b){this._operationStatus$1=b.get_operationStatus();null!=this.__draw$1&&this.__draw$1(this,b)},_isTargetObjectAdded$1:!1,get_isTargetObjectAdded:function(){return this._isTargetObjectAdded$1},startWorking:function(){this.get_hasStarted()||lt.Annotations.Core.ExceptionHelper.invalidOperationException("Designer not started. Call Start before calling this method");var b=this.get_targetObject();this._invalidateRect$1=b.getInvalidateRect(this.get_container().get_mapper(),this.getRenderer());
Type.canCast(b,lt.Annotations.Core.AnnSelectionObject)?this._isTargetObjectAdded$1=!0:this.get_container().get_children().contains(b)||(this.get_container().get_children().add(b),this._isTargetObjectAdded$1=!0);var c=lt.Annotations.Core.AnnDrawDesignerEventArgs.create(this.get_targetObject(),lt.Annotations.Core.AnnDesignerOperationStatus.start);this.onDraw(c);c.get_cancel()&&this.cancel();this.invalidate(b.getInvalidateRect(this.get_container().get_mapper(),this.getRenderer()));return!c.get_cancel()},
working:function(){this.get_hasStarted()||lt.Annotations.Core.ExceptionHelper.invalidOperationException("Designer not started. Call Start before calling this method");var b=lt.Annotations.Core.AnnDrawDesignerEventArgs.create(this.get_targetObject(),lt.Annotations.Core.AnnDesignerOperationStatus.working);this.onDraw(b);b.get_cancel()&&this.cancel();var c=this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),this.getRenderer()),a=lt.Annotations.Designers._leadRectDExtensions.clone(c),
a=lt.Annotations.Designers._leadRectDExtensions.unionRects(a,this._invalidateRect$1);this.invalidate(a);this._invalidateRect$1=c;return b.get_cancel()},cancel:function(){if(null!=this.get_targetObject()){this.onDraw(lt.Annotations.Core.AnnDrawDesignerEventArgs.create(this.get_targetObject(),lt.Annotations.Core.AnnDesignerOperationStatus.canceled));this.get_container().get_children().contains(this.get_targetObject())&&(this.get_container().get_children().remove(this.get_targetObject()),this._isTargetObjectAdded$1=
!1);var b=this.getRenderer();null!=b&&this.get_targetObject().get_id()!==lt.Annotations.Core.AnnObject.selectObjectId&&null!=b.get_renderingEngine()&&!b.get_renderingEngine().get_stateless()&&this.getRenderer().removeObject(this.get_targetObject());this.invalidate(this._invalidateRect$1)}},endWorking:function(){this.get_hasStarted()||lt.Annotations.Core.ExceptionHelper.invalidOperationException("Designer not started. Call Start before calling this method");var b=lt.Annotations.Core.AnnDrawDesignerEventArgs.create(this.get_targetObject(),
lt.Annotations.Core.AnnDesignerOperationStatus.end);this.onDraw(b);b.get_cancel()&&this.cancel();var c=this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),this.getRenderer()),c=lt.Annotations.Designers._leadRectDExtensions.clone(c),c=lt.Annotations.Designers._leadRectDExtensions.isEmpty(c)?lt.Annotations.Designers._leadRectDExtensions.unionRects(c,this._invalidateRect$1):this._invalidateRect$1;this.invalidate(c);return b.get_cancel()},_extendedMode$1:!1,get_extendedMode:function(){return this._extendedMode$1},
set_extendedMode:function(b){return this._extendedMode$1=b},_extendedModeModifierKey$1:0,get_extendedModeModifierKey:function(){return this._extendedModeModifierKey$1},set_extendedModeModifierKey:function(b){return this._extendedModeModifierKey$1=b},get_isExtendedMode:function(){return this.get_extendedMode()||lt.Annotations.Core.Utils.checkModifierKey(this.get_extendedModeModifierKey())}};Object.defineProperty(lt.Annotations.Designers.AnnDrawDesigner.prototype,"operationStatus",{get:lt.Annotations.Designers.AnnDrawDesigner.prototype.get_operationStatus,
enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDrawDesigner.prototype,"draw",{get:lt.Annotations.Designers.AnnDrawDesigner.prototype.__draw$1_handler_get,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDrawDesigner.prototype,"isTargetObjectAdded",{get:lt.Annotations.Designers.AnnDrawDesigner.prototype.get_isTargetObjectAdded,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDrawDesigner.prototype,
"extendedMode",{get:lt.Annotations.Designers.AnnDrawDesigner.prototype.get_extendedMode,set:lt.Annotations.Designers.AnnDrawDesigner.prototype.set_extendedMode,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDrawDesigner.prototype,"extendedModeModifierKey",{get:lt.Annotations.Designers.AnnDrawDesigner.prototype.get_extendedModeModifierKey,set:lt.Annotations.Designers.AnnDrawDesigner.prototype.set_extendedModeModifierKey,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnDrawDesigner.prototype,
"isExtendedMode",{get:lt.Annotations.Designers.AnnDrawDesigner.prototype.get_isExtendedMode,enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnFreehandDrawDesigner=function(b,c,a){lt.Annotations.Designers.AnnFreehandDrawDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnFreehandDrawDesigner.prototype={_spacing$2:2,_spaceCount$2:0,get_spacing:function(){return this._spacing$2},set_spacing:function(b){return this._spacing$2=b},onPointerDown:function(b,c){var a=lt.Annotations.Designers.AnnFreehandDrawDesigner.callBaseMethod(this,
"onPointerDown",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;c.get_button()===lt.Annotations.Core.AnnMouseButton.left?(Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPolylineObject).get_points().add(this.clipPoint(c.get_location(),this.get_clipRectangle())),this.startWorking()):this._endMe$2();return!0},onPointerMove:function(b,c){var a=lt.Annotations.Designers.AnnFreehandDrawDesigner.callBaseMethod(this,"onPointerMove",[b,c]);null!=this.get_targetObject()&&
this.get_isMouseLeftButtonDown()&&(this._spaceCount$2++,this._spaceCount$2>=this.get_spacing()&&(Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPolylineObject).get_points().add(this.clipPoint(c.get_location(),this.get_clipRectangle())),this.working(),a=!0,this._spaceCount$2=0));return a},onPointerUp:function(b,c){var a=lt.Annotations.Designers.AnnFreehandDrawDesigner.callBaseMethod(this,"onPointerUp",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;null!=this.get_targetObject()&&
(this._spaceCount$2=0,this._endMe$2(),a=!0);return a},_endMe$2:function(){var b=!0;null!=this.get_targetObject()&&2<Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPolylineObject).get_points().get_count()&&(b=!1,this.endWorking());b&&this.cancel()}};Object.defineProperty(lt.Annotations.Designers.AnnFreehandDrawDesigner.prototype,"spacing",{get:lt.Annotations.Designers.AnnFreehandDrawDesigner.prototype.get_spacing,set:lt.Annotations.Designers.AnnFreehandDrawDesigner.prototype.set_spacing,
enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnLineDrawDesigner=function(b,c,a){lt.Annotations.Designers.AnnLineDrawDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnLineDrawDesigner._setPoints$3=function(b,c,a){b.get_points().set_item(0,lt.Annotations.Designers._leadPointDExtensions.isEmpty(c)?b.get_points().get_item(0):c);b.get_points().set_item(1,lt.Annotations.Designers._leadPointDExtensions.isEmpty(a)?b.get_points().get_item(1):a)};lt.Annotations.Designers.AnnLineDrawDesigner.prototype=
{_begin$3:null,_end$3:null,onPointerDown:function(b,c){var a=lt.Annotations.Designers.AnnLineDrawDesigner.callBaseMethod(this,"onPointerDown",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left||!this.get_hasStarted())return a;c.get_button()===lt.Annotations.Core.AnnMouseButton.left&&(this._end$3=this._begin$3=this.clipPoint(c.get_location(),this.get_clipRectangle()),lt.Annotations.Designers.AnnLineDrawDesigner._setPoints$3(this.get_targetObject(),this._begin$3,this._end$3),this.startWorking());
return a},onPointerMove:function(b,c){var a=lt.Annotations.Designers.AnnLineDrawDesigner.callBaseMethod(this,"onPointerMove",[b,c]);if(!lt.Annotations.Designers.AnnLineDrawDesigner.callBaseMethod(this,"get_hasStarted"))return a;if(null!=this.get_targetObject()&&this.get_isMouseLeftButtonDown()){a=this.clipPoint(c.get_location(),this.get_clipRectangle());if(a.get_x()!==this._end$3.get_x()||a.get_y()!==this._end$3.get_y())this._end$3=a,lt.Annotations.Designers.AnnLineDrawDesigner._setPoints$3(this.get_targetObject(),
lt.Annotations.Designers._leadPointDExtensions.get_empty(),this._end$3),this.working();a=!0}return a},onPointerUp:function(b,c){var a=lt.Annotations.Designers.AnnLineDrawDesigner.callBaseMethod(this,"onPointerUp",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left||!lt.Annotations.Designers.AnnLineDrawDesigner.callBaseMethod(this,"get_hasStarted"))return a;if(null!=this.get_targetObject()){var d=a=1;if(this.get_targetObject().get_id()===lt.Annotations.Core.AnnObject.pointerObjectId)var e=
Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPointerObject),a=a+e.get_arrowLength().get_value(),d=d+e.get_arrowLength().get_value();Math.abs(this.get_targetObject().get_bounds().get_width())>=a||Math.abs(this.get_targetObject().get_bounds().get_height())>=d?this.endWorking():this.cancel();this._started=a=!1}return a},onPointerDoubleClick:function(b,c){return lt.Annotations.Designers.AnnLineDrawDesigner.callBaseMethod(this,"onPointerDoubleClick",[b,c])}};lt.Annotations.Designers.AnnPointDrawDesigner=
function(b,c,a){lt.Annotations.Designers.AnnPointDrawDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnPointDrawDesigner.prototype={onPointerDown:function(b,c){var a=lt.Annotations.Designers.AnnPointDrawDesigner.callBaseMethod(this,"onPointerDown",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;null!=this.get_targetObject()&&c.get_button()===lt.Annotations.Core.AnnMouseButton.left&&(Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPointObject).set_centerPoint(this.clipPoint(c.get_location(),
this.get_clipRectangle())),this.startWorking()&&(this.working(),this.endWorking()),a=!0);return a}};lt.Annotations.Designers.AnnPolylineDrawDesigner=function(b,c,a){this._begin$2=lt.Annotations.Designers._leadPointDExtensions.get_empty();this._end$2=lt.Annotations.Designers._leadPointDExtensions.get_empty();lt.Annotations.Designers.AnnPolylineDrawDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnPolylineDrawDesigner.prototype={_started:!1,_saveClosed$2:!1,_firstPointOnClick$2:!0,
get_firstPointOnClick:function(){return this._firstPointOnClick$2},set_firstPointOnClick:function(b){return this._firstPointOnClick$2=b},onPointerDown:function(b,c){var a=lt.Annotations.Designers.AnnPolylineDrawDesigner.callBaseMethod(this,"onPointerDown",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return c.get_button()===lt.Annotations.Core.AnnMouseButton.right&&this.endWorking(),a;this._started?c.get_button()===lt.Annotations.Core.AnnMouseButton.left?(this._end$2=this.clipPoint(c.get_location(),
this.get_clipRectangle()),a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPolylineObject),a.get_points().add(this._end$2),this.working(),a=!0):this.endWorking():c.get_button()===lt.Annotations.Core.AnnMouseButton.left&&(this._end$2=this._begin$2=this.clipPoint(c.get_location(),this.get_clipRectangle()),a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPolylineObject),this._saveClosed$2=a.get_isClosed(),a.set_isClosed(!1),a.get_points().clear(),a.get_points().add(this._begin$2),
a.get_points().add(this._end$2),this._started=!0,this.startWorking(),a=!0);return a},onPointerMove:function(b,c){var a=lt.Annotations.Designers.AnnPolylineDrawDesigner.callBaseMethod(this,"onPointerMove",[b,c]);if(null!=this.get_targetObject()&&this._started){a=this.clipPoint(c.get_location(),this.get_clipRectangle());if(a.get_x()!==this._end$2.get_x()||a.get_y()!==this._end$2.get_y()){this._end$2=a;var d=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPolylineObject);1<d.get_points().get_count()&&
d.get_points().set_item(d.get_points().get_count()-1,a);this.working()}a=!0}return a},onPointerUp:function(b,c){var a=lt.Annotations.Designers.AnnPolylineDrawDesigner.callBaseMethod(this,"onPointerUp",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;this.get_targetObject();this._firstPointOnClick$2||(a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPolylineObject),null!=a&&2===a.get_points().get_count()&&lt.Annotations.Designers._leadPointDExtensions.isEqual(a.get_points().get_item(0),
a.get_points().get_item(1))&&this.cancel());this.invalidate(this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),this.getRenderer()));return!0},_restorePolygon$2:function(){Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPolylineObject).set_isClosed(this._saveClosed$2);this._saveClosed$2=!0},onPointerDoubleClick:function(b,c){var a=lt.Annotations.Designers.AnnPolylineDrawDesigner.callBaseMethod(this,"onPointerDoubleClick",[b,c]),d=Type.safeCast(this.get_targetObject(),
lt.Annotations.Core.AnnPolylineObject);3<d.get_points().get_count()?(d.get_points().removeAt(d.get_points().get_count()-1),d.get_points().removeAt(d.get_points().get_count()-1),this._started=!1,a=!0,this._restorePolygon$2(),this.endWorking()):this.cancel();return a}};Object.defineProperty(lt.Annotations.Designers.AnnPolylineDrawDesigner.prototype,"firstPointOnClick",{get:lt.Annotations.Designers.AnnPolylineDrawDesigner.prototype.get_firstPointOnClick,set:lt.Annotations.Designers.AnnPolylineDrawDesigner.prototype.set_firstPointOnClick,
enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnProtractorDrawDesigner=function(b,c,a){this._begin$2=lt.Annotations.Designers._leadPointDExtensions.get_empty();this._end$2=lt.Annotations.Designers._leadPointDExtensions.get_empty();lt.Annotations.Designers.AnnProtractorDrawDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnProtractorDrawDesigner.prototype={_pointCount$2:0,onPointerDown:function(b,c){var a=lt.Annotations.Designers.AnnProtractorDrawDesigner.callBaseMethod(this,
"onPointerDown",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;this._pointCount$2?(this._pointCount$2++,a=!0):(this._end$2=this._begin$2=this.clipPoint(c.get_location(),this.get_clipRectangle()),a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnProtractorObject),a.set_centerPoint(lt.Annotations.Designers._leadPointDExtensions.clone(this._begin$2)),a.set_firstPoint(lt.Annotations.Designers._leadPointDExtensions.clone(this._begin$2)),a.set_secondPoint(lt.Annotations.Designers._leadPointDExtensions.clone(this._begin$2)),
this.startWorking(),a=!0,this._pointCount$2++);return a},onPointerMove:function(b,c){var a=lt.Annotations.Designers.AnnProtractorDrawDesigner.callBaseMethod(this,"onPointerMove",[b,c]);if(3>this._pointCount$2){var d=this.clipPoint(c.get_location(),this.get_clipRectangle());if(d.get_x()!==this._end$2.get_x()||d.get_y()!==this._end$2.get_y()){this._end$2=d;a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnProtractorObject);lt.Annotations.Designers._leadPointDExtensions.get_empty();switch(this._pointCount$2){case 1:a.set_firstPoint(d);
break;case 2:a.set_secondPoint(d)}this.working();a=!0}}return a},onPointerUp:function(b,c){var a=lt.Annotations.Designers.AnnProtractorDrawDesigner.callBaseMethod(this,"onPointerUp",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;a=!0;1===this._pointCount$2&&lt.Annotations.Designers._leadPointDExtensions.isEqual(this._begin$2,this._end$2)&&null!=this.get_targetObject()&&(this.cancel(),a=!1);2===this._pointCount$2&&(this.endWorking(),a=!0);return a}};lt.Annotations.Designers.AnnRectangleDrawDesigner=
function(b,c,a){this._begin$2=lt.Annotations.Designers._leadPointDExtensions.get_empty();this._end$2=lt.Annotations.Designers._leadPointDExtensions.get_empty();lt.Annotations.Designers.AnnRectangleDrawDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnRectangleDrawDesigner.prototype={onPointerDown:function(b,c){var a=lt.Annotations.Designers.AnnRectangleDrawDesigner.callBaseMethod(this,"onPointerDown",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;c.get_button()===
lt.Annotations.Core.AnnMouseButton.left&&(this._end$2=this._begin$2=this.clipPoint(c.get_location(),this.get_clipRectangle()),Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnRectangleObject).set_rect(lt.Annotations.Designers._leadRectDExtensions.create(this._begin$2.get_x(),this._begin$2.get_y(),this._end$2.get_x()-this._begin$2.get_x(),this._end$2.get_y()-this._begin$2.get_y())),this.startWorking(),a=!0);return a},onPointerMove:function(b,c){var a=lt.Annotations.Designers.AnnRectangleDrawDesigner.callBaseMethod(this,
"onPointerMove",[b,c]);if(null!=this.get_targetObject()&&this.get_isMouseLeftButtonDown()){a=this.clipPoint(c.get_location(),this.get_clipRectangle());if(a.get_x()!==this._end$2.get_x()||a.get_y()!==this._end$2.get_y())this._end$2=a,Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnRectangleObject).set_rect(lt.Annotations.Designers._leadRectDExtensions.fromLTRB(this._begin$2.get_x(),this._begin$2.get_y(),this._end$2.get_x(),this._end$2.get_y())),this.working();a=!0}return a},onPointerUp:function(b,
c){var a=lt.Annotations.Designers.AnnRectangleDrawDesigner.callBaseMethod(this,"onPointerUp",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;if(null!=this.get_targetObject()){var a=!1,d=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnRectangleObject);1<=Math.abs(d.get_bounds().get_width())||1<=Math.abs(d.get_bounds().get_height())?(d=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnSelectionObject),null!=d&&d.adjustBounds(),this.endWorking()):(this.cancel(),
a=!1)}return a}};lt.Annotations.Designers.AnnStickyNoteDrawDesigner=function(b,c,a){lt.Annotations.Designers.AnnStickyNoteDrawDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnStickyNoteDrawDesigner.prototype={onPointerDown:function(b,c){var a=lt.Annotations.Designers.AnnStickyNoteDrawDesigner.callBaseMethod(this,"onPointerDown",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;null!=this.get_targetObject()&&c.get_button()===lt.Annotations.Core.AnnMouseButton.left&&
(this.get_targetObject().get_points().add(this.clipPoint(c.get_location(),this.get_clipRectangle())),this.startWorking()&&(this.working(),this.endWorking()),a=!0);return a}};lt.Annotations.Designers.AnnTextPointerDrawDesigner=function(b,c,a){this._begin$2=lt.Annotations.Designers._leadPointDExtensions.get_empty();this._end$2=lt.Annotations.Designers._leadPointDExtensions.get_empty();lt.Annotations.Designers.AnnTextPointerDrawDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnTextPointerDrawDesigner.prototype=
{_drawingPoint$2:!1,_defaultText$2:"AaBbZz",get_defaultText:function(){return this._defaultText$2},set_defaultText:function(b){return this._defaultText$2=b},cancel:function(){this._begin$2=this._end$2=lt.Annotations.Designers._leadPointDExtensions.get_empty();this._drawingPoint$2=!1;lt.Annotations.Designers.AnnTextPointerDrawDesigner.callBaseMethod(this,"cancel")},endWorking:function(){this._begin$2=this._end$2=lt.Annotations.Designers._leadPointDExtensions.get_empty();this._drawingPoint$2=!1;return lt.Annotations.Designers.AnnTextPointerDrawDesigner.callBaseMethod(this,
"endWorking")},onPointerDown:function(b,c){var a=lt.Annotations.Designers.AnnTextPointerDrawDesigner.callBaseMethod(this,"onPointerDown",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;if(this._drawingPoint$2)a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnTextPointerObject),a.set_pointerPosition(this.clipPoint(c.get_location(),this.get_clipRectangle())),this.endWorking();else{this._end$2=this._begin$2=this.clipPoint(c.get_location(),this.get_clipRectangle());
var a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnTextPointerObject),d=this.clipPoint(c.get_location(),this.get_clipRectangle());a.set_rect(lt.Annotations.Designers._leadRectDExtensions.create(this._begin$2.get_x(),this._begin$2.get_y(),this._end$2.get_x()-this._begin$2.get_x(),this._end$2.get_y()-this._begin$2.get_y()));a.set_pointerPosition(d);(null==a.get_text()||!a.get_text().length)&&a.set_text(this.get_defaultText());this.startWorking()}return!0},onPointerMove:function(b,c){var a=
lt.Annotations.Designers.AnnTextPointerDrawDesigner.callBaseMethod(this,"onPointerMove",[b,c]);if(null!=this.get_targetObject()&&!(this.get_operationStatus()===lt.Annotations.Core.AnnDesignerOperationStatus.end||this.get_operationStatus()===lt.Annotations.Core.AnnDesignerOperationStatus.canceled)){a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnTextPointerObject);if(this._drawingPoint$2)a.set_pointerPosition(this.clipPoint(c.get_location(),this.get_clipRectangle())),this.working();
else{var d=this.clipPoint(c.get_location(),this.get_clipRectangle());if(d.get_x()!==this._end$2.get_x()||d.get_y()!==this._end$2.get_y())this._end$2=d,a.set_rect(lt.Annotations.Designers._leadRectDExtensions.fromLTRB(this._begin$2.get_x(),this._begin$2.get_y(),this._end$2.get_x(),this._end$2.get_y())),this.working()}a=!0}return a},onPointerUp:function(b,c){var a=lt.Annotations.Designers.AnnTextPointerDrawDesigner.callBaseMethod(this,"onPointerUp",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return a;
null!=this.get_targetObject()&&(a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnTextPointerObject),1<=Math.abs(a.get_bounds().get_width())||1<=Math.abs(a.get_bounds().get_height())?(a.set_pointerPosition(this.clipPoint(c.get_location(),this.get_clipRectangle())),this._drawingPoint$2=!0):this.cancel());return!0}};Object.defineProperty(lt.Annotations.Designers.AnnTextPointerDrawDesigner.prototype,"defaultText",{get:lt.Annotations.Designers.AnnTextPointerDrawDesigner.prototype.get_defaultText,
set:lt.Annotations.Designers.AnnTextPointerDrawDesigner.prototype.set_defaultText,enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnTextReviewDrawDesigner=function(b,c,a){lt.Annotations.Designers.AnnTextReviewDrawDesigner.initializeBase(this,[b,c,lt.Annotations.Designers.AnnTextReviewDrawDesigner._dummyRectangle$3]);this._annTextReviewObject$3=a};lt.Annotations.Designers.AnnTextReviewDrawDesigner.prototype={_annTextReviewObject$3:null,get_finalTargetObject:function(){return this._annTextReviewObject$3},
endWorking:function(){this.get_container().get_children().remove(lt.Annotations.Designers.AnnTextReviewDrawDesigner._dummyRectangle$3);this._annTextReviewObject$3.get_points().clear();this._annTextReviewObject$3.addRectangle(lt.Annotations.Designers._leadRectDExtensions.clone(lt.Annotations.Designers.AnnTextReviewDrawDesigner._dummyRectangle$3.get_rect()));lt.Annotations.Designers.AnnTextReviewDrawDesigner._dummyRectangle$3.get_points().clear();1<this._annTextReviewObject$3.get_points().get_count()&&
(this.set_targetObject(this._annTextReviewObject$3),this.get_container().get_children().add(this._annTextReviewObject$3));return lt.Annotations.Designers.AnnTextReviewDrawDesigner.callBaseMethod(this,"endWorking")}};Object.defineProperty(lt.Annotations.Designers.AnnTextReviewDrawDesigner.prototype,"finalTargetObject",{get:lt.Annotations.Designers.AnnTextReviewDrawDesigner.prototype.get_finalTargetObject,enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnCrossProductEditDesigner=function(b,
c,a){lt.Annotations.Designers.AnnCrossProductEditDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnCrossProductEditDesigner.prototype={getThumbLocations:function(){var b=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnCrossProductObject),c=Array(5);c[0]=b.get_firstStartPoint();c[1]=b.get_firstEndPoint();c[2]=b.get_secondStartPoint();c[3]=b.get_secondEndPoint();c[4]=b.get_intersectionPoint();return c},moveThumb:function(b,c){var a=Type.safeCast(this.get_targetObject(),
lt.Annotations.Core.AnnCrossProductObject);switch(b){case 0:case 1:case 2:case 3:var d=a.get_points().get_item(b),d=lt.Annotations.Core.AnnTransformer.translatePoint(d,c.get_x(),c.get_y());(!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(this.get_clipRectangle(),d))&&a.updatePoint(b,d);break;case 4:lt.Annotations.Designers.AnnCrossProductEditDesigner.callBaseMethod(this,"move",[c.get_x(),c.get_y()])}},move:function(b,c){var a=Type.safeCast(this.get_targetObject(),
lt.Annotations.Core.AnnCrossProductObject);if(null!=a){var d=-1;lt.Annotations.Core.Utils.compare(a.get_hitTestedRuler(),lt.Annotations.Core.AnnCrossProductObject.firstRulerHitTestObject,!1)?d=0:lt.Annotations.Core.Utils.compare(a.get_hitTestedRuler(),lt.Annotations.Core.AnnCrossProductObject.secondRulerHitTestObject,!1)&&(d=1);-1!==d&&a.moveLine(d,b,c)}},getRotationReferencePoints:function(){var b=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnCrossProductObject);return null!=b?[b.get_firstStartPoint(),
b.get_secondEndPoint()]:null}};lt.Annotations.Designers.AnnEditDesigner=function(b,c,a){this._invalidateRect$1=lt.Annotations.Designers._leadRectDExtensions.get_empty();this._rotateFirstPoint$1=lt.Annotations.Designers._leadPointDExtensions.get_empty();this._thumbsHitTestBuffer$1=parseInt(720*(2/96));this._workingBuffer$1=parseInt(720*(2/96));this._operation$1=lt.Annotations.Core.AnnEditDesignerOperation.none;this._moveThumbIndex$1=-1;this._startWorkingLocation$1=lt.Annotations.Designers._leadPointDExtensions.get_empty();
lt.Annotations.Designers.AnnEditDesigner.initializeBase(this,[b,c,a]);null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("container");null==a&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject")};lt.Annotations.Designers.AnnEditDesigner.prototype={add_editContent:function(b){this.__editContent$1=ss.Delegate.combine(this.__editContent$1,b)},remove_editContent:function(b){this.__editContent$1=ss.Delegate.remove(this.__editContent$1,b)},__editContent$1_handler_get:function(){null==
this.__editContent$1_handler&&(this.__editContent$1_handler=ss.EventHandler.create(this,this.add_editContent,this.remove_editContent));return this.__editContent$1_handler},__editContent$1:null,__editContent$1_handler:null,_restrictRotateGripper$1:!0,_rotateModifierKey$1:0,get_rotateModifierKey:function(){return this._rotateModifierKey$1},set_rotateModifierKey:function(b){return this._rotateModifierKey$1=b},_dragOrigin$1:null,add_edit:function(b){this.__edit$1=ss.Delegate.combine(this.__edit$1,b)},
remove_edit:function(b){this.__edit$1=ss.Delegate.remove(this.__edit$1,b)},__edit$1_handler_get:function(){null==this.__edit$1_handler&&(this.__edit$1_handler=ss.EventHandler.create(this,this.add_edit,this.remove_edit));return this.__edit$1_handler},__edit$1:null,__edit$1_handler:null,getRotationReferencePoints:function(){return 2>this.get_targetObject().get_points().get_count()?null:[this.get_targetObject().get_points().get_item(0),this.get_targetObject().get_points().get_item(1)]},_getRotationUnitVector$1:function(){var b=
lt.Annotations.Designers._leadPointDExtensions.get_empty();if(1<this.get_targetObject().get_points().get_count()){var c=this.getRotationReferencePoints();null!=c&&(b=lt.Annotations.Core.Utils.getUnitVectorPerpendicular(c[0],c[1]));if(null!=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnRectangleObject)&&!lt.Annotations.Designers._leadPointDExtensions.isEmpty(b)){var a=this.get_targetObject().get_points().get_item(3),d=c[1].get_x()-c[0].get_x(),e=c[1].get_y()-c[0].get_y(),f=a.get_x()-
c[0].get_x(),c=a.get_y()-c[0].get_y();0>d*c-f*e&&(b.set_x(-b.get_x()),b.set_y(-b.get_y()))}}return b},getRotateCenterPoint:function(){var b=this.get_targetObject().get_bounds();return lt.Annotations.Designers._leadPointDExtensions.create(b.get_x()+b.get_width()/2,b.get_y()+b.get_height()/2)},_getScale:function(){var b=this.get_container().get_mapper().get_transform();return Math.sqrt(Math.pow(b.get_m11(),2)+Math.pow(b.get_m12(),2))},getRotateGripper:function(){var b=this.get_targetObject().get_internalRotateGripperLocation();
if(lt.Annotations.Designers._leadPointDExtensions.isEmpty(b)||this._restrictRotateGripper$1)var b=this.getIntersectionWithReferencePoints(),c=this.get_targetObject().get_rotateGripper().get_value()/this._getScale(),b=lt.Annotations.Core.Utils.transformPoint(this._getRotationUnitVector$1(),lt.Annotations.Designers._leadLengthDExtensions.create(c),b);return b},_useRotateThumbs$1:!0,get_useRotateThumbs:function(){return this._useRotateThumbs$1},set_useRotateThumbs:function(b){this._useRotateThumbs$1!==
b&&(this._useRotateThumbs$1=b,this._updateThumbsVisibility$1());return b},getThumbLocations:function(){return null},get_thumbsHitTestBuffer:function(){return this._thumbsHitTestBuffer$1},set_thumbsHitTestBuffer:function(b){0>b&&lt.Annotations.Core.ExceptionHelper.invalidOperationException("Thumbs hit test buffer must be a value greater than or equal to 0");return this._thumbsHitTestBuffer$1=b},get_workingBuffer:function(){return this._workingBuffer$1},set_workingBuffer:function(b){0>b&&lt.Annotations.Core.ExceptionHelper.invalidOperationException("Working buffer must be a value greater than or equal to 0");
return this._workingBuffer$1=b},cancel:function(){this.get_operation()!==lt.Annotations.Core.AnnEditDesignerOperation.none&&(this.onEdit(lt.Annotations.Core.AnnEditDesignerEventArgs.create(this.get_targetObject(),this.get_operation(),this.get_moveThumbIndex(),lt.Annotations.Core.AnnDesignerOperationStatus.canceled)),this._moveThumbIndex$1=-1,this._operation$1=lt.Annotations.Core.AnnEditDesignerOperation.none);this.get_targetObject().set_isSelected(!1)},_maintainAspectRatio$1:!1,get_maintainAspectRatio:function(){return this._maintainAspectRatio$1},
set_maintainAspectRatio:function(b){return this._maintainAspectRatio$1=b},get_operation:function(){return this._operation$1},set_operation:function(b){return this._operation$1=b},get_moveThumbIndex:function(){return this._moveThumbIndex$1},_showThumbs$1:!0,get_showThumbs:function(){return this._showThumbs$1},set_showThumbs:function(b){this._showThumbs$1=b;this._updateThumbsVisibility$1();return b},_ignoreHitTestThumbs$1:!1,get_ignoreHitTestThumbs:function(){return this._ignoreHitTestThumbs$1},set_ignoreHitTestThumbs:function(b){return this._ignoreHitTestThumbs$1=
b},_isModified$1:!1,get_isModified:function(){return this._isModified$1},set_isModified:function(b){return this._isModified$1=b},_updateThumbsVisibility$1:function(){var b=this.getRenderer();null!=b&&(null!=b.get_rotateCenterThumbStyle()&&b.get_rotateCenterThumbStyle().set_isVisible(this.get_showThumbs()&&this.get_useRotateThumbs()),null!=b.get_rotateGripperThumbStyle()&&b.get_rotateGripperThumbStyle().set_isVisible(this.get_showThumbs()&&this.get_useRotateThumbs()),null!=b.get_locationsThumbStyle()&&
b.get_locationsThumbStyle().set_isVisible(this.get_showThumbs()))},start:function(){if(!lt.Annotations.Designers.AnnEditDesigner.callBaseMethod(this,"get_hasStarted")){this._isModified$1=!1;var b=lt.Annotations.Designers.AnnEditDesigner.callBaseMethod(this,"get_container"),c=lt.Annotations.Designers.AnnEditDesigner.callBaseMethod(this,"get_targetObject"),a=b.get_selectionObject();c!==a&&(a.get_selectedObjects().contains(c)||a.get_selectedObjects().add(c),a.set_rect(lt.Annotations.Designers._leadRectDExtensions.create(0,
0,0,0)));b.select(c);this._updateThumbsVisibility$1();null!=c&&c.get_canRotate()&&this.get_showThumbs()&&this.get_useRotateThumbs()&&(c.set_internalRotateCenterLocation(c.get_rotateCenter()),lt.Annotations.Designers._leadPointDExtensions.isEmpty(c.get_internalRotateCenterLocation())&&c.set_internalRotateCenterLocation(this.getRotateCenterPoint()),c.set_rotateCenter(c.get_internalRotateCenterLocation()),c.set_internalRotateGripperLocation(this.getRotateGripper()));null!=c&&this.get_showThumbs()&&c.set_internalThumbLocations(this.getThumbLocations());
this.invalidate(c.getInvalidateRect(this.get_container().get_mapper(),this.getRenderer()))}lt.Annotations.Designers.AnnEditDesigner.callBaseMethod(this,"start")},end:function(){if(this.get_hasStarted()){var b=this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),this.getRenderer());this.get_container().unselect(this.get_targetObject());this.invalidate(b)}},hitTestThumbs:function(b){var b=lt.Annotations.Core.Utils.fixPoint(b,this.get_container().get_mapper(),this.get_targetObject().get_fixedStateOperations()),
c=this.get_targetObject();if(null==c)return!1;var a=this.getRenderer();if(null==a)return!1;var d=this.get_container().get_mapper().getHitTestBuffer(this.get_thumbsHitTestBuffer()),e=!1,f=a.get_locationsThumbStyle();if(this.get_showThumbs()&&null!=f&&(e=!1,this.get_operation()===lt.Annotations.Core.AnnEditDesignerOperation.none)){var g=this.getThumbLocations();if(null!=g)for(var h=0;h<g.length;++h)if(f.renderHitTest(g[h],b,d,this.get_container().get_mapper())){e=!0;break}}return!e&&this.get_showThumbs()&&
this.get_useRotateThumbs()&&(null!=c.get_internalRotateCenterLocation()&&null!=a.get_rotateCenterThumbStyle()&&a.get_rotateCenterThumbStyle().renderHitTest(c.get_internalRotateCenterLocation(),b,d,this.get_container().get_mapper())||null!=c.get_internalRotateGripperLocation()&&null!=a.get_rotateGripperThumbStyle()&&a.get_rotateGripperThumbStyle().renderHitTest(c.get_internalRotateGripperLocation(),b,d,this.get_container().get_mapper()))?!0:e},_startWorkingBuffer$1:0,onPointerDown:function(b,c){var a=
lt.Annotations.Designers.AnnEditDesigner.callBaseMethod(this,"onPointerDown",[b,c]);if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return this._ignoreHitTestThumbs$1=!1,a;var d=this.get_targetObject();if(null==d)return this._ignoreHitTestThumbs$1=!1,a;var e=!this.get_useRotateThumbs();e&&(e=d.get_canRotate()&&lt.Annotations.Core.Utils.checkModifierKey(this.get_rotateModifierKey()));var f=d.get_isLocked();if(this.get_hasStarted()){var g=this.get_container().get_mapper().getHitTestBuffer(this.get_thumbsHitTestBuffer());
if(f)return this._ignoreHitTestThumbs$1=!1,!0;this.get_operation()!==lt.Annotations.Core.AnnEditDesignerOperation.none?(a=!0,this.cancel()):(this._startWorkingLocation$1=lt.Annotations.Designers._leadPointDExtensions.clone(c.get_location()),this._startWorkingBuffer$1=this.get_container().get_mapper().getHitTestBuffer(this._workingBuffer$1));if(!a&&c.get_button()===lt.Annotations.Core.AnnMouseButton.left){var f=c.get_location(),h=this.getRenderer();if(e)this._rotateFirstPoint$1=f,this._doStartWorking$1(lt.Annotations.Core.AnnEditDesignerOperation.rotate,
-1,!1),a=!0;else if(!a&&this.get_showThumbs()&&null!=h){var k=h.get_locationsThumbStyle();if(!a&&!this._ignoreHitTestThumbs$1&&this.get_operation()===lt.Annotations.Core.AnnEditDesignerOperation.none&&null!=k){var i=this.getThumbLocations();if(null!=i)for(var f=lt.Annotations.Core.Utils.fixPoint(c.get_location(),this.get_container().get_mapper(),this.get_targetObject().get_fixedStateOperations()),j=0;j<i.length;++j)if(k.renderHitTest(i[j],f,g,this.get_container().get_mapper())){this._doStartWorking$1(lt.Annotations.Core.AnnEditDesignerOperation.moveThumb,
j,!0);a=!0;break}}this._dragOrigin$1=f}if(!a&&!this._ignoreHitTestThumbs$1&&this.get_operation()===lt.Annotations.Core.AnnEditDesignerOperation.none&&this.get_useRotateThumbs()){if(null!=h&&(f=lt.Annotations.Core.Utils.fixPoint(c.get_location(),this.get_container().get_mapper(),this.get_targetObject().get_fixedStateOperations()),null!=h.get_rotateCenterThumbStyle()&&h.get_rotateCenterThumbStyle().renderHitTest(d.get_internalRotateCenterLocation(),f,g,this.get_container().get_mapper())&&(this._doStartWorking$1(lt.Annotations.Core.AnnEditDesignerOperation.moveRotateCenterThumb,
-1,!0),a=!0),!a))f=lt.Annotations.Core.Utils.fixPoint(c.get_location(),this.get_container().get_mapper(),this.get_targetObject().get_fixedStateOperations()),null!=h.get_rotateGripperThumbStyle()&&h.get_rotateGripperThumbStyle().renderHitTest(d.get_internalRotateGripperLocation(),f,g,this.get_container().get_mapper())&&(this._doStartWorking$1(lt.Annotations.Core.AnnEditDesignerOperation.moveRotateGripperThumb,-1,!0),a=!0,e=e||a);e&&(this._rotateFirstPoint$1=f,this._doStartWorking$1(lt.Annotations.Core.AnnEditDesignerOperation.rotate,
-1,!1))}!a&&this.get_operation()===lt.Annotations.Core.AnnEditDesignerOperation.none&&(d=this.get_container().hitTestPoint(c.get_location()),f=lt.Annotations.Core.Utils.fixPoint(c.get_location(),this.get_container().get_mapper(),this.get_targetObject().get_fixedStateOperations()),null!=d&&0<d.length&&(e?(this._rotateFirstPoint$1=f,this._doStartWorking$1(lt.Annotations.Core.AnnEditDesignerOperation.rotate,-1,!1)):(a=f.get_x(),e=f.get_y(),a-=f.get_x(),e-=f.get_y(),(a||e)&&this.move(-a,-e),this._dragOrigin$1=
f,this._doStartWorking$1(lt.Annotations.Core.AnnEditDesignerOperation.move,-1,!0)),a=!0))}}this._ignoreHitTestThumbs$1=!1;return a},onPointerMove:function(b,c){var a=lt.Annotations.Designers.AnnEditDesigner.callBaseMethod(this,"onPointerMove",[b,c]),d=this.get_targetObject();if(null==d)return!1;if(d.get_isLocked())return!0;if(this.get_hasStarted()&&this.get_operation()!==lt.Annotations.Core.AnnEditDesignerOperation.none){if(0<this._startWorkingBuffer$1&&!lt.Annotations.Designers._leadPointDExtensions.isEmpty(this._startWorkingLocation$1)&&
Math.abs(c.get_location().get_x()-this._startWorkingLocation$1.get_x())<this._startWorkingBuffer$1&&Math.abs(c.get_location().get_y()-this._startWorkingLocation$1.get_y())<this._startWorkingBuffer$1)return!0;a=c.get_location();this.get_operation()!==lt.Annotations.Core.AnnEditDesignerOperation.moveRotateCenterThumb&&this.get_operation()!==lt.Annotations.Core.AnnEditDesignerOperation.moveRotateGripperThumb&&this.get_operation()!==lt.Annotations.Core.AnnEditDesignerOperation.rotate&&(a=this.snapPointToGrid(a,
!1));a=lt.Annotations.Core.Utils.fixPoint(a,this.get_container().get_mapper(),this.get_targetObject().get_fixedStateOperations());if(this.get_operation()===lt.Annotations.Core.AnnEditDesignerOperation.moveRotateCenterThumb)this._moveRotateCenter$1(a);else if(this.get_operation()===lt.Annotations.Core.AnnEditDesignerOperation.moveThumb){var d=a.get_x()-this._dragOrigin$1.get_x(),e=a.get_y()-this._dragOrigin$1.get_y();this.moveThumb(this.get_moveThumbIndex(),lt.Annotations.Designers._leadPointDExtensions.create(d,
e));this.invalidate(this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),this.getRenderer()));this._dragOrigin$1=a}else if(this.get_operation()===lt.Annotations.Core.AnnEditDesignerOperation.move||this.get_operation()===lt.Annotations.Core.AnnEditDesignerOperation.moveName){if(d=a.get_x()-this._dragOrigin$1.get_x(),e=a.get_y()-this._dragOrigin$1.get_y(),d||e)this.get_operation()===lt.Annotations.Core.AnnEditDesignerOperation.move&&(this.move(d,e),this.invalidate(this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),
this.getRenderer()))),this._dragOrigin$1=a}else if(this.get_operation()===lt.Annotations.Core.AnnEditDesignerOperation.rotate){var e=lt.Annotations.Core.Utils.findAngle(this._rotateFirstPoint$1,d.get_internalRotateCenterLocation()),f=lt.Annotations.Core.Utils.findAngle(a,d.get_internalRotateCenterLocation());e!==f&&(this.get_targetObject().rotate(f-e,d.get_internalRotateCenterLocation()),this.get_restrictDesigners()&&!lt.Annotations.Designers._leadRectDExtensions.containsRect(this.get_clipRectangle(),
this.get_targetObject().get_bounds())&&this.get_targetObject().rotate(e-f,d.get_internalRotateCenterLocation()),d.set_internalRotateGripperLocation(a),d=this.getIntersectionWithReferencePoints(),this._restrictRotateGripper$1&&this.get_targetObject().set_rotateGripper(lt.Annotations.Designers._leadLengthDExtensions.create(lt.Annotations.Core.Utils.distance(a,d)*this._getScale())),this._rotateFirstPoint$1=a,this.invalidate(this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),
this.getRenderer())))}this.working();a=!0}return a},getIntersectionWithReferencePoints:function(){var b=this.getRotationReferencePoints(),c=this._getRotationUnitVector$1(),c=lt.Annotations.Core.Utils.transformPoint(c,lt.Annotations.Designers._leadLengthDExtensions.create(this.get_targetObject().get_bounds().get_height()),this.get_targetObject().get_rotateCenter());return 0>=this.get_targetObject().get_bounds().get_height()?c:lt.Annotations.Core.Utils.intersect(this.get_targetObject().get_rotateCenter(),
c,b[0],b[1])},_doStartWorking$1:function(b,c){this.startWorking(b,c)},startWorking:function(b,c){this.get_hasStarted()||lt.Annotations.Core.ExceptionHelper.invalidOperationException("Designer not started. Call Start before calling this method");this._invalidateRect$1=this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),this.getRenderer());this.invalidate(this._invalidateRect$1);var a=lt.Annotations.Core.AnnEditDesignerEventArgs.create(this.get_targetObject(),b,c,lt.Annotations.Core.AnnDesignerOperationStatus.start);
this.onEdit(a);a.get_cancel()||(this._moveThumbIndex$1=c,this._operation$1=b);return!a.get_cancel()},working:function(){this.get_hasStarted()||lt.Annotations.Core.ExceptionHelper.invalidOperationException("Designer not started. Call Start before calling this method");var b=lt.Annotations.Core.AnnEditDesignerEventArgs.create(this.get_targetObject(),this.get_operation(),this.get_moveThumbIndex(),lt.Annotations.Core.AnnDesignerOperationStatus.working);this.onEdit(b);b.get_cancel()&&this.cancel();var c=
this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),this.getRenderer()),a=lt.Annotations.Designers._leadRectDExtensions.clone(c),a=lt.Annotations.Designers._leadRectDExtensions.unionRects(a,this._invalidateRect$1);this.invalidate(a);this._invalidateRect$1=c;return!b.get_cancel()},endWorking:function(){this.get_hasStarted()||lt.Annotations.Core.ExceptionHelper.invalidOperationException("Designer not started. Call Start before calling this method");var b=lt.Annotations.Core.AnnEditDesignerEventArgs.create(this.get_targetObject(),
this.get_operation(),this.get_moveThumbIndex(),lt.Annotations.Core.AnnDesignerOperationStatus.end);this.onEdit(b);b.get_cancel()?this.cancel():(this._moveThumbIndex$1=-1,this._operation$1=lt.Annotations.Core.AnnEditDesignerOperation.none);var c=this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),this.getRenderer()),c=lt.Annotations.Designers._leadRectDExtensions.clone(c),c=lt.Annotations.Designers._leadRectDExtensions.unionRects(c,this._invalidateRect$1);this.invalidate(c);
return!b.get_cancel()},onEdit:function(b){b.get_operation()!==lt.Annotations.Core.AnnEditDesignerOperation.none&&b.get_operationStatus()===lt.Annotations.Core.AnnDesignerOperationStatus.working&&(this._isModified$1=!0);null!=this.__edit$1&&this.__edit$1(this,b)},move:function(b,c){var a=this.get_targetObject();if(null!=a){var d=lt.Annotations.Designers._leadPointDExtensions.create(b,c);a.translate(d.get_x(),d.get_y());this.snapObjectToGrid(this.get_targetObject(),!1);a.get_canRotate()&&(a.set_internalRotateCenterLocation(a.get_rotateCenter()),
a.set_internalRotateGripperLocation(this.getRotateGripper()))}},callMove:function(b,c){var a=Type.safeCast(this,lt.Annotations.Designers.AnnCrossProductEditDesigner);null!=a?(a=Type.safeCast(a.get_targetObject(),lt.Annotations.Core.AnnCrossProductObject),null!=a&&(a.moveLine(0,b,c),a.moveLine(1,b,c))):(this.move(b,c),this.snapObjectToGrid(this.get_targetObject(),!0))},snapObjectToGrid:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("targetObject");if(0<b.get_points().get_count()){var a=
b.get_points().get_item(0);if(c)for(var d=b.get_bounds(),d=[lt.Annotations.Designers._leadRectDExtensions.topLeft(d),lt.Annotations.Designers._leadRectDExtensions.topRight(d),lt.Annotations.Designers._leadRectDExtensions.bottomLeft(d),lt.Annotations.Designers._leadRectDExtensions.bottomRight(d)],d=ss.IEnumerator.getEnumerator(d);d.moveNext();){var e=d.current;if(!lt.Annotations.Designers._leadRectDExtensions.containsPoint(this.get_clipRectangle(),e)){a=e;break}}d=this.snapPointToGrid(a,c);a=lt.Annotations.Designers._leadPointDExtensions.create(d.get_x()-
a.get_x(),d.get_y()-a.get_y());this.get_targetObject().translate(a.get_x(),a.get_y())}},_moveRotateCenter$1:function(b){var c=this.get_targetObject();null!=c&&(c.set_rotateCenter(b),c.set_internalRotateCenterLocation(c.get_rotateCenter()),c.set_internalRotateGripperLocation(this.getRotateGripper()))},invalidate:function(b){var c=this.getRenderer();null!=this.get_targetObject()&&null!=c&&this.get_showThumbs()&&(this.get_targetObject().set_internalThumbLocations(this.getThumbLocations()),this.get_targetObject().get_canRotate()&&
(this.get_targetObject().set_internalRotateCenterLocation(this.get_targetObject().get_rotateCenter()),lt.Annotations.Designers._leadPointDExtensions.isEmpty(this.get_targetObject().get_internalRotateCenterLocation())&&this.get_targetObject().set_internalRotateCenterLocation(this.getRotateCenterPoint()),this.get_targetObject().set_rotateCenter(this.get_targetObject().get_internalRotateCenterLocation()),this.get_targetObject().set_internalRotateGripperLocation(this.getRotateGripper())));lt.Annotations.Designers.AnnEditDesigner.callBaseMethod(this,
"invalidate",[b])},moveThumb:function(){},resetRotateThumbs:function(){this.get_targetObject().set_rotateCenter(this.getRotateCenterPoint());this.get_targetObject().set_rotateGripper(lt.Annotations.Designers._leadLengthDExtensions.create(lt.Annotations.Core.AnnObject._rotateGripperOriginal));var b=this.getRenderer();null!=this.get_targetObject()&&this.get_targetObject().get_canRotate()&&null!=b&&(this.get_targetObject().set_internalRotateCenterLocation(this.get_targetObject().get_rotateCenter()),
lt.Annotations.Designers._leadPointDExtensions.isEmpty(this.get_targetObject().get_internalRotateCenterLocation())&&this.get_targetObject().set_internalRotateCenterLocation(this.getRotateCenterPoint()),this.get_targetObject().set_rotateCenter(this.get_targetObject().get_internalRotateCenterLocation()),this.get_targetObject().set_internalRotateGripperLocation(lt.Annotations.Designers._leadPointDExtensions.get_empty()),this.get_targetObject().set_internalRotateGripperLocation(this.getRotateGripper()),
this.invalidate(this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),this.getRenderer())))},onPointerUp:function(b,c){if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return lt.Annotations.Designers.AnnEditDesigner.callBaseMethod(this,"onPointerUp",[b,c]);this.endWorking();return lt.Annotations.Designers.AnnEditDesigner.callBaseMethod(this,"onPointerUp",[b,c])},onPointerDoubleClick:function(b,c){var a=!1;if(c.get_button()!==lt.Annotations.Core.AnnMouseButton.left)return lt.Annotations.Designers.AnnEditDesigner.callBaseMethod(this,
"onPointerDoubleClick",[b,c]);var d=this.get_targetObject();if(null!=d&&!d.get_isLocked()){var e=lt.Annotations.Core.AnnEditContentEventArgs.create(d,this.get_container().get_mapper().rectFromContainerCoordinates(d.get_bounds(),d.get_fixedStateOperations()));null!=this.__editContent$1&&d.get_supportsContent()&&(a=!0,this.__editContent$1(this,e))}return a}};Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,"editContent",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.__editContent$1_handler_get,
enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,"rotateModifierKey",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.get_rotateModifierKey,set:lt.Annotations.Designers.AnnEditDesigner.prototype.set_rotateModifierKey,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,"edit",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.__edit$1_handler_get,enumerable:!0,configurable:!0});
Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,"useRotateThumbs",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.get_useRotateThumbs,set:lt.Annotations.Designers.AnnEditDesigner.prototype.set_useRotateThumbs,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,"thumbsHitTestBuffer",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.get_thumbsHitTestBuffer,set:lt.Annotations.Designers.AnnEditDesigner.prototype.set_thumbsHitTestBuffer,
enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,"workingBuffer",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.get_workingBuffer,set:lt.Annotations.Designers.AnnEditDesigner.prototype.set_workingBuffer,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,"maintainAspectRatio",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.get_maintainAspectRatio,set:lt.Annotations.Designers.AnnEditDesigner.prototype.set_maintainAspectRatio,
enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,"operation",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.get_operation,set:lt.Annotations.Designers.AnnEditDesigner.prototype.set_operation,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,"moveThumbIndex",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.get_moveThumbIndex,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,
"showThumbs",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.get_showThumbs,set:lt.Annotations.Designers.AnnEditDesigner.prototype.set_showThumbs,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,"ignoreHitTestThumbs",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.get_ignoreHitTestThumbs,set:lt.Annotations.Designers.AnnEditDesigner.prototype.set_ignoreHitTestThumbs,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnEditDesigner.prototype,
"isModified",{get:lt.Annotations.Designers.AnnEditDesigner.prototype.get_isModified,set:lt.Annotations.Designers.AnnEditDesigner.prototype.set_isModified,enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnPointEditDesigner=function(b,c,a){lt.Annotations.Designers.AnnPointEditDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnPointEditDesigner.prototype={getRotationReferencePoints:function(){return[this.get_targetObject().get_points().get_item(0),this.get_targetObject().get_points().get_item(0)]},
getThumbLocations:function(){return[Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPointObject).get_centerPoint()]},moveThumb:function(b,c){Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPointObject).translate(c.get_x(),c.get_y())}};lt.Annotations.Designers.AnnPolylineEditDesigner=function(b,c,a){lt.Annotations.Designers.AnnPolylineEditDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnPolylineEditDesigner._getGapPoints$2=function(b){return b.get_points().toArray()};
lt.Annotations.Designers.AnnPolylineEditDesigner.prototype={_distance0$2:0,_distance1$2:0,_offset$2:0,_thumbsGap$2:0,get_thumbsGap:function(){return this._thumbsGap$2},set_thumbsGap:function(b){0>b&&lt.Annotations.Core.ExceptionHelper.invalidOperationException("Thumbs gap must be a value greater than or equal to 0");return this._thumbsGap$2=b},getThumbLocations:function(){return lt.Annotations.Designers.AnnPolylineEditDesigner._getGapPoints$2(Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnPolylineObject),
this._thumbsGap$2)},moveThumb:function(b,c){var a=this.get_targetObject();if(2===a.get_points().get_count()){var d=lt.Annotations.Core.Utils.findAngle(a.get_points().get_item(0),a.get_points().get_item(1)),e=d-lt.Annotations.Core.Utils.findAngle(a.get_points().get_item(0),a.get_rotateCenter()),f=lt.Annotations.Core.Utils.distance(a.get_points().get_item(0),a.get_rotateCenter());this._offset$2=f*Math.sin(lt.Annotations.Core.Utils.degreesToRadian(e));this._distance0$2=f*Math.cos(lt.Annotations.Core.Utils.degreesToRadian(e));
e=d-lt.Annotations.Core.Utils.findAngle(a.get_rotateCenter(),a.get_points().get_item(1));f=lt.Annotations.Core.Utils.distance(a.get_points().get_item(1),a.get_rotateCenter());this._distance1$2=f*Math.cos(lt.Annotations.Core.Utils.degreesToRadian(e))}d=a.get_points().get_item(b);d=lt.Annotations.Core.AnnTransformer.translatePoint(d,c.get_x(),c.get_y());(!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(this.get_clipRectangle(),d))&&a.get_points().set_item(b,
this.snapPointToGrid(d,!1));lt.Annotations.Designers.AnnPolylineEditDesigner.callBaseMethod(this,"moveThumb",[b,c]);this._updateRotateThumbs$2()},_updateRotateThumbs$2:function(){var b=this.get_targetObject();if(2===b.get_points().get_count()){var c;c=lt.Annotations.Designers._leadPointDExtensions.get_empty();c=lt.Annotations.Core.Utils.getUnitVector(b.get_points().get_item(0),b.get_points().get_item(1));var a=lt.Annotations.Designers._leadPointDExtensions.create(c.get_y(),-c.get_x()),d=lt.Annotations.Core.Utils.distance(b.get_points().get_item(0),
b.get_points().get_item(1));c=lt.Annotations.Core.Utils.transformPoint(c,lt.Annotations.Designers._leadLengthDExtensions.create(d*this._distance0$2/(this._distance0$2+this._distance1$2)),b.get_points().get_item(0));b.set_rotateCenter(lt.Annotations.Core.Utils.transformPoint(a,lt.Annotations.Designers._leadLengthDExtensions.create(this._offset$2),c));b.set_internalRotateCenterLocation(b.get_rotateCenter());b.set_internalRotateGripperLocation(this.getRotateGripper())}},getRotateCenterPoint:function(){var b=
lt.Annotations.Designers._leadPointDExtensions.get_empty(),b=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnProtractorObject);return b=null!=b?lt.Annotations.Designers._leadPointDExtensions.clone(b.get_centerPoint()):lt.Annotations.Designers.AnnPolylineEditDesigner.callBaseMethod(this,"getRotateCenterPoint")},getRotationReferencePoints:function(){var b=null,c=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnProtractorObject);if(null!=c)var a=lt.Annotations.Core.Utils.getUnitVectorPerpendicular(c.get_centerPoint(),
c.get_secondPoint()),b=lt.Annotations.Core.Utils.transformPoint(a,lt.Annotations.Designers._leadLengthDExtensions.create(-1),c.get_centerPoint()),c=lt.Annotations.Core.Utils.transformPoint(a,lt.Annotations.Designers._leadLengthDExtensions.create(1),c.get_centerPoint()),b=[b,c];else b=lt.Annotations.Designers.AnnPolylineEditDesigner.callBaseMethod(this,"getRotationReferencePoints");return b}};Object.defineProperty(lt.Annotations.Designers.AnnPolylineEditDesigner.prototype,"thumbsGap",{get:lt.Annotations.Designers.AnnPolylineEditDesigner.prototype.get_thumbsGap,
set:lt.Annotations.Designers.AnnPolylineEditDesigner.prototype.set_thumbsGap,enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnRectangleEditDesigner=function(b,c,a){this._oldPoints$2=Array(4);this._minimumSize$2=lt.Annotations.Designers._leadSizeDExtensions.get_empty();lt.Annotations.Designers.AnnRectangleEditDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnRectangleEditDesigner._transformPoint$2=function(b,c,a){c=c.get_x()*a.get_x()+c.get_y()*a.get_y();return lt.Annotations.Designers._leadPointDExtensions.create(b.get_x()+
c*a.get_x(),b.get_y()+c*a.get_y())};lt.Annotations.Designers.AnnRectangleEditDesigner.prototype={_oldScaleX$2:0,_oldScaleY$2:0,_rotateCenterUnitVector$2:null,_xUnitVector$2:null,_yUnitVector$2:null,get_minimumSize:function(){return this._minimumSize$2},set_minimumSize:function(b){return this._minimumSize$2=b},onPointerDown:function(b,c){this._oldPoints$2=this.get_targetObject().get_points().toArray();this._rotateCenterUnitVector$2=lt.Annotations.Core.Utils.getUnitVector(this.get_targetObject().get_points().get_item(0),
this.get_targetObject().get_rotateCenter());var a=lt.Annotations.Core.Utils.distance(this.get_targetObject().get_points().get_item(0),this.get_targetObject().get_rotateCenter());this._xUnitVector$2=lt.Annotations.Core.Utils.getUnitVector(this.get_targetObject().get_points().get_item(0),this.get_targetObject().get_points().get_item(1));this._yUnitVector$2=lt.Annotations.Core.Utils.getUnitVector(this.get_targetObject().get_points().get_item(0),this.get_targetObject().get_points().get_item(3));this._oldScaleX$2=
a*(this._xUnitVector$2.get_x()*this._rotateCenterUnitVector$2.get_x()+this._xUnitVector$2.get_y()*this._rotateCenterUnitVector$2.get_y())/lt.Annotations.Core.Utils.distance(this.get_targetObject().get_points().get_item(0),this.get_targetObject().get_points().get_item(1));this._oldScaleY$2=a*(this._yUnitVector$2.get_x()*this._rotateCenterUnitVector$2.get_x()+this._yUnitVector$2.get_y()*this._rotateCenterUnitVector$2.get_y())/lt.Annotations.Core.Utils.distance(this.get_targetObject().get_points().get_item(0),
this.get_targetObject().get_points().get_item(3));return lt.Annotations.Designers.AnnRectangleEditDesigner.callBaseMethod(this,"onPointerDown",[b,c])},getThumbLocations:function(){var b=null,c=Array(8),a=this.getRenderer();if(null!=a){b=a.getRenderPoints(this.get_container().get_mapper(),this.get_targetObject());if(!b.length)return null;c[0]=b[0];c[2]=b[1];c[4]=b[2];c[6]=b[3];var b=(c[0].get_x()+c[2].get_x())/2,a=(c[0].get_y()+c[2].get_y())/2,d=(c[2].get_y()+c[4].get_y())/2,e=(c[2].get_x()+c[4].get_x())/
2,f=(c[4].get_x()+c[6].get_x())/2,g=(c[4].get_y()+c[6].get_y())/2,h=(c[0].get_y()+c[6].get_y())/2,k=(c[0].get_x()+c[6].get_x())/2;c[1]=lt.Annotations.Designers._leadPointDExtensions.create(b,a);c[3]=lt.Annotations.Designers._leadPointDExtensions.create(e,d);c[5]=lt.Annotations.Designers._leadPointDExtensions.create(f,g);c[7]=lt.Annotations.Designers._leadPointDExtensions.create(k,h);b=c}return b},_updateThumbs:function(b,c){var a=!lt.Annotations.Designers._leadSizeDExtensions.isEmpty(this.get_minimumSize()),
d=this.get_clipRectangle(),e=this.get_targetObject(),f=e.get_points().get_item(0).get_y()!==e.get_points().get_item(1).get_y();switch(b){case 0:a=this._updatePoints$2(e.get_points().get_item(3),e.get_points().get_item(0),e.get_points().get_item(1),c,this.get_maintainAspectRatio(),this._oldPoints$2[3],this._oldPoints$2[0],this._oldPoints$2[1],a);if(!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[0])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,
a[1])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[2]))!Type.canCast(e,lt.Annotations.Core.AnnSelectionObject)&&this.get__enableSnapToGrid()?(d=this.snapPointToGrid(a[1],!1),d.get_x()!==a[1].get_x()&&d.get_y()!==a[1].get_y()&&(e.get_points().set_item(0,d),e.get_points().set_item(1,lt.Annotations.Designers._leadPointDExtensions.create(a[2].get_x(),d.get_y())),e.get_points().set_item(3,lt.Annotations.Designers._leadPointDExtensions.create(d.get_x(),a[0].get_y())))):(e.get_points().set_item(3,
this.snapPointToGrid(a[0],!1)),e.get_points().set_item(0,this.snapPointToGrid(a[1],!1)),e.get_points().set_item(1,this.snapPointToGrid(a[2],!1)));break;case 1:var g=lt.Annotations.Designers._leadPointDExtensions.create((e.get_points().get_item(0).get_x()+e.get_points().get_item(1).get_x())/2,(e.get_points().get_item(0).get_y()+e.get_points().get_item(1).get_y())/2),h=lt.Annotations.Designers._leadPointDExtensions.create((this._oldPoints$2[0].get_x()+this._oldPoints$2[1].get_x())/2,(this._oldPoints$2[0].get_y()+
this._oldPoints$2[1].get_y())/2),a=this._updatePoints$2(e.get_points().get_item(0),g,e.get_points().get_item(1),c,!1,this._oldPoints$2[0],h,this._oldPoints$2[1],a);if(!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[0])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[2]))f?(e.get_points().set_item(0,a[0]),e.get_points().set_item(1,a[2])):(d=e.get_points().get_item(0).get_x(),f=e.get_points().get_item(1).get_x(),a[0]=this.snapPointToGrid(a[0],
!1),a[2]=this.snapPointToGrid(a[2],!1),e.get_points().set_item(0,lt.Annotations.Designers._leadPointDExtensions.create(d,a[0].get_y())),e.get_points().set_item(1,lt.Annotations.Designers._leadPointDExtensions.create(f,a[2].get_y())));break;case 2:a=this._updatePoints$2(e.get_points().get_item(0),e.get_points().get_item(1),e.get_points().get_item(2),c,this.get_maintainAspectRatio(),this._oldPoints$2[0],this._oldPoints$2[1],this._oldPoints$2[2],a);if(!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,
a[0])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[1])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[2]))!Type.canCast(e,lt.Annotations.Core.AnnSelectionObject)&&this.get__enableSnapToGrid()?(d=this.snapPointToGrid(a[1],!1),d.get_x()!==a[1].get_x()&&d.get_y()!==a[1].get_y()&&(e.get_points().set_item(1,d),e.get_points().set_item(0,lt.Annotations.Designers._leadPointDExtensions.create(a[0].get_x(),d.get_y())),e.get_points().set_item(2,lt.Annotations.Designers._leadPointDExtensions.create(d.get_x(),
a[2].get_y())))):(e.get_points().set_item(1,a[1]),this.get_targetObject().get_points().set_item(0,a[0]),e.get_points().set_item(2,a[2]));break;case 3:g=lt.Annotations.Designers._leadPointDExtensions.create((e.get_points().get_item(1).get_x()+e.get_points().get_item(2).get_x())/2,(e.get_points().get_item(1).get_y()+e.get_points().get_item(2).get_y())/2);h=lt.Annotations.Designers._leadPointDExtensions.create((this._oldPoints$2[1].get_x()+this._oldPoints$2[2].get_x())/2,(this._oldPoints$2[1].get_y()+
this._oldPoints$2[2].get_y())/2);a=this._updatePoints$2(e.get_points().get_item(1),g,e.get_points().get_item(2),c,!1,this._oldPoints$2[1],h,this._oldPoints$2[2],a);if(!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[0])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[2]))f?(e.get_points().set_item(1,a[0]),e.get_points().set_item(2,a[2])):(d=e.get_points().get_item(1).get_y(),f=e.get_points().get_item(2).get_y(),a[0]=this.snapPointToGrid(a[0],
!1),a[2]=this.snapPointToGrid(a[2],!1),e.get_points().set_item(1,lt.Annotations.Designers._leadPointDExtensions.create(a[0].get_x(),d)),e.get_points().set_item(2,lt.Annotations.Designers._leadPointDExtensions.create(a[2].get_x(),f)));break;case 4:a=this._updatePoints$2(e.get_points().get_item(1),e.get_points().get_item(2),e.get_points().get_item(3),c,this.get_maintainAspectRatio(),this._oldPoints$2[1],this._oldPoints$2[2],this._oldPoints$2[3],a);if(!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,
a[0])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[1])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[2]))e.get_points().set_item(1,this.snapPointToGrid(a[0],!1)),e.get_points().set_item(2,this.snapPointToGrid(a[1],!1)),e.get_points().set_item(3,this.snapPointToGrid(a[2],!1));break;case 5:g=lt.Annotations.Designers._leadPointDExtensions.create((e.get_points().get_item(2).get_x()+e.get_points().get_item(3).get_x())/2,(e.get_points().get_item(2).get_y()+e.get_points().get_item(3).get_y())/
2);h=lt.Annotations.Designers._leadPointDExtensions.create((this._oldPoints$2[2].get_x()+this._oldPoints$2[3].get_x())/2,(this._oldPoints$2[2].get_y()+this._oldPoints$2[3].get_y())/2);a=this._updatePoints$2(e.get_points().get_item(2),g,e.get_points().get_item(3),c,!1,this._oldPoints$2[2],h,this._oldPoints$2[3],a);if(!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[0])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[2]))f?(e.get_points().set_item(2,
a[0]),e.get_points().set_item(3,a[2])):(d=e.get_points().get_item(2).get_x(),f=e.get_points().get_item(3).get_x(),a[0]=this.snapPointToGrid(a[0],!1),a[2]=this.snapPointToGrid(a[2],!1),e.get_points().set_item(2,lt.Annotations.Designers._leadPointDExtensions.create(d,a[0].get_y())),e.get_points().set_item(3,lt.Annotations.Designers._leadPointDExtensions.create(f,a[2].get_y())));break;case 6:a=this._updatePoints$2(e.get_points().get_item(2),e.get_points().get_item(3),e.get_points().get_item(0),c,this.get_maintainAspectRatio(),
this._oldPoints$2[2],this._oldPoints$2[3],this._oldPoints$2[0],a);if(!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[0])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[1])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[2]))e.get_points().set_item(2,this.snapPointToGrid(a[0],!1)),e.get_points().set_item(3,this.snapPointToGrid(a[1],!1)),e.get_points().set_item(0,this.snapPointToGrid(a[2],!1));break;case 7:if(g=lt.Annotations.Designers._leadPointDExtensions.create((e.get_points().get_item(0).get_x()+
e.get_points().get_item(3).get_x())/2,(e.get_points().get_item(0).get_y()+e.get_points().get_item(3).get_y())/2),a=this._updatePoints$2(e.get_points().get_item(3),g,e.get_points().get_item(0),c,!1,this._oldPoints$2[3],g,this._oldPoints$2[0],a),!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[0])&&lt.Annotations.Designers._leadRectDExtensions.containsPoint(d,a[2]))f?(e.get_points().set_item(3,a[0]),e.get_points().set_item(0,a[2])):(d=e.get_points().get_item(3).get_y(),
f=e.get_points().get_item(0).get_y(),a[0]=this.snapPointToGrid(a[0],!1),a[2]=this.snapPointToGrid(a[2],!1),e.get_points().set_item(3,lt.Annotations.Designers._leadPointDExtensions.create(a[0].get_x(),d)),e.get_points().set_item(0,lt.Annotations.Designers._leadPointDExtensions.create(a[2].get_x(),f)))}this._oldPoints$2[0]=e.get_points().get_item(0);this._oldPoints$2[1]=e.get_points().get_item(1);this._oldPoints$2[2]=e.get_points().get_item(2);this._oldPoints$2[3]=e.get_points().get_item(3)},moveThumb:function(b,
c){var a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnEncryptObject);null!=a&&a.get_canDecrypt()||(this._updateThumbs(b,c),this._updateRotateThumbs(b,c),lt.Annotations.Designers.AnnRectangleEditDesigner.callBaseMethod(this,"moveThumb",[b,c]))},_updateRotateThumbs:function(){var b=this.get_targetObject(),c=lt.Annotations.Core.Utils.distance(b.get_points().get_item(0),b.get_points().get_item(1)),a=lt.Annotations.Core.Utils.distance(b.get_points().get_item(0),b.get_points().get_item(3));
this._xUnitVector$2=lt.Annotations.Core.Utils.getUnitVector(b.get_points().get_item(0),b.get_points().get_item(1));this._yUnitVector$2=lt.Annotations.Core.Utils.getUnitVector(b.get_points().get_item(0),b.get_points().get_item(3));c=lt.Annotations.Designers._leadPointDExtensions.create(this._xUnitVector$2.get_x()*c*this._oldScaleX$2,this._xUnitVector$2.get_y()*c*this._oldScaleX$2);a=lt.Annotations.Designers._leadPointDExtensions.create(this._yUnitVector$2.get_x()*a*this._oldScaleY$2,this._yUnitVector$2.get_y()*
a*this._oldScaleY$2);b.set_rotateCenter(lt.Annotations.Designers._leadPointDExtensions.create(c.get_x()+a.get_x()+b.get_points().get_item(0).get_x(),c.get_y()+a.get_y()+b.get_points().get_item(0).get_y()));b.set_internalRotateCenterLocation(b.get_rotateCenter());b.set_internalRotateGripperLocation(this.getRotateGripper())},_updatePoints$2:function(b,c,a,d,e,f,g,h,k){var i=Array(3),j=d,l=lt.Annotations.Core.Utils.getUnitVectorPerpendicular(b,c),m=lt.Annotations.Core.Utils.getUnitVectorPerpendicular(c,
a);lt.Annotations.Designers._leadPointDExtensions.isEmpty(l)&&(l=lt.Annotations.Core.Utils.getUnitVectorPerpendicular(f,g));lt.Annotations.Designers._leadPointDExtensions.isEmpty(m)&&(m=lt.Annotations.Core.Utils.getUnitVectorPerpendicular(g,h));if(e&&!lt.Annotations.Designers._leadPointDExtensions.isEmpty(l)&&!lt.Annotations.Designers._leadPointDExtensions.isEmpty(m)){var e=l.get_x()+m.get_x(),j=l.get_y()+m.get_y(),n=Math.sqrt(e*e+j*j),e=lt.Annotations.Designers._leadPointDExtensions.create(e/n,j/
n),e=e.get_x()*d.get_x()+e.get_y()*d.get_y(),j=lt.Annotations.Core.Utils.distance(f,h),e=e/j,j=lt.Annotations.Core.Utils.distance(f,c),n=lt.Annotations.Core.Utils.distance(c,h),n=lt.Annotations.Designers.AnnRectangleEditDesigner._transformPoint$2(c,lt.Annotations.Designers._leadPointDExtensions.create(e*n*l.get_x(),e*n*l.get_y()),l),j=n=lt.Annotations.Designers.AnnRectangleEditDesigner._transformPoint$2(n,lt.Annotations.Designers._leadPointDExtensions.create(e*j*m.get_x(),e*j*m.get_y()),m);d.set_x(j.get_x()-
c.get_x());d.set_y(j.get_y()-c.get_y())}else j=lt.Annotations.Core.AnnTransformer.translatePoint(c,d.get_x(),d.get_y());i[1]=j;lt.Annotations.Designers._leadPointDExtensions.isEmpty(l)?(l=lt.Annotations.Core.Utils.getUnitVectorPerpendicular(i[1],b),i[0]=lt.Annotations.Designers._leadPointDExtensions.isEmpty(l)?lt.Annotations.Designers._leadPointDExtensions.clone(j):lt.Annotations.Designers.AnnRectangleEditDesigner._transformPoint$2(b,d,l)):i[0]=lt.Annotations.Designers.AnnRectangleEditDesigner._transformPoint$2(b,
d,l);lt.Annotations.Designers._leadPointDExtensions.isEmpty(m)?(m=lt.Annotations.Core.Utils.getUnitVectorPerpendicular(i[1],a),i[2]=lt.Annotations.Designers._leadPointDExtensions.isEmpty(m)?lt.Annotations.Designers._leadPointDExtensions.clone(j):lt.Annotations.Designers.AnnRectangleEditDesigner._transformPoint$2(a,d,m)):i[2]=lt.Annotations.Designers.AnnRectangleEditDesigner._transformPoint$2(a,d,m);if(k&&(b=this.get_minimumSize().get_width(),c=this.get_minimumSize().get_height(),a=lt.Annotations.Core.Utils.distance(f,
g),d=lt.Annotations.Core.Utils.distance(g,h),k=lt.Annotations.Core.Utils.distance(i[0],i[1]),l=lt.Annotations.Core.Utils.distance(i[1],i[2]),!(a+5<k)&&!(d+5<l))){if(k<=b)return i=[f,g,h];l<=c&&(i=[f,g,h])}return i},snapObjectToGrid:function(b,c){var a=Type.safeCast(b,lt.Annotations.Core.AnnRectangleObject);if(null!=a&&0<a.get_points().get_count()&&a.get_points().get_item(0).get_y()===a.get_points().get_item(1).get_y()){a.get_rect().get_width();a.get_rect().get_height();var d=a.get_points().get_item(1),
e=this.snapPointToGrid(d,c),d=lt.Annotations.Designers._leadPointDExtensions.create(e.get_x()-d.get_x(),e.get_y()-d.get_y());a.translate(d.get_x(),d.get_y())}}};Object.defineProperty(lt.Annotations.Designers.AnnRectangleEditDesigner.prototype,"minimumSize",{get:lt.Annotations.Designers.AnnRectangleEditDesigner.prototype.get_minimumSize,set:lt.Annotations.Designers.AnnRectangleEditDesigner.prototype.set_minimumSize,enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnSelectionEditDesigner=function(b,
c,a){lt.Annotations.Designers.AnnSelectionEditDesigner.initializeBase(this,[b,c,a]);this.set_minimumSize(lt.Annotations.Designers._leadSizeDExtensions.create(90,90))};lt.Annotations.Designers.AnnSelectionEditDesigner.prototype={_oldWidth$3:0,_oldHeight$3:0,_oldUnitVectorX$3:null,_oldUnitVectorY$3:null,start:function(){lt.Annotations.Designers.AnnSelectionEditDesigner.callBaseMethod(this,"start");var b=this.get_targetObject();this._oldUnitVectorX$3=lt.Annotations.Core.Utils.getUnitVector(b.get_points().get_item(0),
b.get_points().get_item(1));this._oldUnitVectorY$3=lt.Annotations.Core.Utils.getUnitVector(b.get_points().get_item(0),b.get_points().get_item(3));this._oldWidth$3=parseInt(lt.Annotations.Core.Utils.distance(b.get_points().get_item(0),b.get_points().get_item(1)));this._oldHeight$3=parseInt(lt.Annotations.Core.Utils.distance(b.get_points().get_item(0),b.get_points().get_item(3)))},get_maintainAspectRatio:function(){return!0},set_maintainAspectRatio:function(b){return b},getThumbLocations:function(){var b=
null,c=this.getRenderer();null!=c&&(b=c.getRenderPoints(this.get_container().get_mapper(),this.get_targetObject()));return b},moveThumb:function(b,c){lt.Annotations.Designers.AnnSelectionEditDesigner.callBaseMethod(this,"moveThumb",[2*b,c]);var a=this.get_targetObject().get_points().toArray(),d=lt.Annotations.Core.Utils.distance(a[0],a[1]),a=lt.Annotations.Core.Utils.distance(a[0],a[3]),e=lt.Annotations.Core.Utils.getUnitVector(this.get_targetObject().get_points().get_item(0),this.get_targetObject().get_points().get_item(1)),
f=lt.Annotations.Core.Utils.getUnitVector(this.get_targetObject().get_points().get_item(0),this.get_targetObject().get_points().get_item(3)),g=e.get_x()*this._oldUnitVectorX$3.get_x()+e.get_y()*this._oldUnitVectorX$3.get_y(),h=f.get_x()*this._oldUnitVectorY$3.get_x()+f.get_y()*this._oldUnitVectorY$3.get_y();this.get_maintainAspectRatio()?(h*=a/this._oldHeight$3,g*=d/this._oldWidth$3):g=h=Math.sqrt(a*a+d*d)/Math.sqrt(this._oldHeight$3*this._oldHeight$3+this._oldWidth$3*this._oldWidth$3);var k;k=this.getThumbLocations();
var i=k.length/2,j=b>=i?b-i:b+i,i=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnSelectionObject);if(null!=i){k=k[j];for(i=ss.IEnumerator.getEnumerator(i.get_selectedObjects());i.moveNext();)j=i.current,j.get_isLocked()||(j.scaleVector(g,h,e,f,k),j.set_rotateCenter(lt.Annotations.Designers._leadPointDExtensions.get_empty()))}this._oldUnitVectorX$3=lt.Annotations.Designers._leadPointDExtensions.clone(e);this._oldUnitVectorY$3=lt.Annotations.Designers._leadPointDExtensions.clone(f);this._oldWidth$3=
d;this._oldHeight$3=a},end:function(){lt.Annotations.Designers.AnnSelectionEditDesigner.callBaseMethod(this,"end");var b=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnSelectionObject);null!=b&&(b.get_selectedObjects().clear(),this.get_container().get_selectionObject().get_selectedObjects().clear())},onPointerMove:function(b,c){this._oldUnitVectorX$3=lt.Annotations.Core.Utils.getUnitVector(this.get_targetObject().get_points().get_item(0),this.get_targetObject().get_points().get_item(1));
this._oldUnitVectorY$3=lt.Annotations.Core.Utils.getUnitVector(this.get_targetObject().get_points().get_item(0),this.get_targetObject().get_points().get_item(3));return lt.Annotations.Designers.AnnSelectionEditDesigner.callBaseMethod(this,"onPointerMove",[b,c])}};Object.defineProperty(lt.Annotations.Designers.AnnSelectionEditDesigner.prototype,"maintainAspectRatio",{get:lt.Annotations.Designers.AnnSelectionEditDesigner.prototype.get_maintainAspectRatio,set:lt.Annotations.Designers.AnnSelectionEditDesigner.prototype.set_maintainAspectRatio,
enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnTextEditDesigner=function(b,c,a){lt.Annotations.Designers.AnnTextEditDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnTextEditDesigner.prototype={_autoSizeAfterEdit$3:!1,_acceptsReturn$3:!1,add_editText:function(b){this.__editText$3=ss.Delegate.combine(this.__editText$3,b)},remove_editText:function(b){this.__editText$3=ss.Delegate.remove(this.__editText$3,b)},__editText$3_handler_get:function(){null==this.__editText$3_handler&&
(this.__editText$3_handler=ss.EventHandler.create(this,this.add_editText,this.remove_editText));return this.__editText$3_handler},__editText$3:null,__editText$3_handler:null,get_acceptsReturn:function(){return this._acceptsReturn$3},set_acceptsReturn:function(b){return this._acceptsReturn$3=b},get_autoSizeAfterEdit:function(){return this._autoSizeAfterEdit$3},set_autoSizeAfterEdit:function(b){return this._autoSizeAfterEdit$3=b},onPointerDoubleClick:function(b,c){var a=lt.Annotations.Designers.AnnTextEditDesigner.callBaseMethod(this,
"onPointerDoubleClick",[b,c]),d=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnTextObject);if(null!=d&&!d.get_isLocked()){var e=lt.Annotations.Core.AnnEditTextEventArgs.create(d,this.get_container().get_mapper().rectFromContainerCoordinates(d.get_bounds(),d.get_fixedStateOperations()));null!=this.__editText$3&&!Type.canCast(d,lt.Annotations.Core.AnnStampObject)&&(a=!0,this.__editText$3(b,e))}return a}};Object.defineProperty(lt.Annotations.Designers.AnnTextEditDesigner.prototype,"editText",
{get:lt.Annotations.Designers.AnnTextEditDesigner.prototype.__editText$3_handler_get,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnTextEditDesigner.prototype,"acceptsReturn",{get:lt.Annotations.Designers.AnnTextEditDesigner.prototype.get_acceptsReturn,set:lt.Annotations.Designers.AnnTextEditDesigner.prototype.set_acceptsReturn,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnTextEditDesigner.prototype,"autoSizeAfterEdit",{get:lt.Annotations.Designers.AnnTextEditDesigner.prototype.get_autoSizeAfterEdit,
set:lt.Annotations.Designers.AnnTextEditDesigner.prototype.set_autoSizeAfterEdit,enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnTextPointerEditDesigner=function(b,c,a){lt.Annotations.Designers.AnnTextPointerEditDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnTextPointerEditDesigner.prototype={_count$4:0,getThumbLocations:function(){for(var b=lt.Annotations.Designers.AnnTextPointerEditDesigner.callBaseMethod(this,"getThumbLocations"),c=Array(b.length+1),a=0;a<b.length;++a)c[a]=
lt.Annotations.Designers._leadPointDExtensions.clone(b[a]);this._count$4=c.length;c[this._count$4-1]=lt.Annotations.Designers._leadPointDExtensions.clone(Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnTextPointerObject).get_pointerPosition());return c},moveThumb:function(b,c){if(b!==this._count$4-1)lt.Annotations.Designers.AnnTextPointerEditDesigner.callBaseMethod(this,"moveThumb",[b,c]);else{var a=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnTextPointerObject),d=lt.Annotations.Core.AnnTransformer.translatePoint(a.get_pointerPosition(),
c.get_x(),c.get_y());(!this.get_restrictDesigners()||lt.Annotations.Designers._leadRectDExtensions.containsPoint(this.get_clipRectangle(),d))&&a.set_pointerPosition(lt.Annotations.Core.AnnTransformer.translatePoint(a.get_pointerPosition(),c.get_x(),c.get_y()))}}};lt.Annotations.Designers.AnnTextReviewEditDesigner=function(b,c,a){lt.Annotations.Designers.AnnTextReviewEditDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnTextReviewEditDesigner.prototype={move:function(b,c){var a=Type.safeCast(this.get_targetObject(),
lt.Annotations.Core.AnnTextReviewObject);(null==a||a.get_canTranslate())&&lt.Annotations.Designers.AnnTextReviewEditDesigner.callBaseMethod(this,"move",[b,c])},getThumbLocations:function(){return null}};lt.Annotations.Designers.AnnRunDesigner=function(b,c,a){lt.Annotations.Designers.AnnRunDesigner.initializeBase(this,[b,c,a])};lt.Annotations.Designers.AnnRunDesigner.prototype={add_run:function(b){this.__run$1=ss.Delegate.combine(this.__run$1,b)},remove_run:function(b){this.__run$1=ss.Delegate.remove(this.__run$1,
b)},__run$1_handler_get:function(){null==this.__run$1_handler&&(this.__run$1_handler=ss.EventHandler.create(this,this.add_run,this.remove_run));return this.__run$1_handler},__run$1:null,__run$1_handler:null,_hitTestBuffer$1:1,get_hitTestBuffer:function(){return this._hitTestBuffer$1},set_hitTestBuffer:function(b){0>b&&lt.Annotations.Core.ExceptionHelper.invalidOperationException("Hit test buffer must be a value greater than or equal to 0");return this._hitTestBuffer$1=b},cancel:function(){this.get_hasStarted()&&
(this.onRun(lt.Annotations.Core.AnnRunDesignerEventArgs.create(this.get_targetObject(),lt.Annotations.Core.AnnDesignerOperationStatus.canceled)),this.end())},startWorking:function(){this.get_hasStarted()||lt.Annotations.Core.ExceptionHelper.invalidOperationException("Designer not started. Call Start before calling this method");var b=lt.Annotations.Core.AnnRunDesignerEventArgs.create(this.get_targetObject(),lt.Annotations.Core.AnnDesignerOperationStatus.start);this.onRun(b);b.get_cancel()&&this.cancel();
return!b.get_cancel()},working:function(){this.get_hasStarted()||lt.Annotations.Core.ExceptionHelper.invalidOperationException("Designer not started. Call Start before calling this method");var b=lt.Annotations.Core.AnnRunDesignerEventArgs.create(this.get_targetObject(),lt.Annotations.Core.AnnDesignerOperationStatus.working);this.onRun(b);b.get_cancel()&&this.cancel();return!b.get_cancel()},endWorking:function(){this.get_hasStarted()||lt.Annotations.Core.ExceptionHelper.invalidOperationException("Designer not started. Call Start before calling this method");
this.end();var b=lt.Annotations.Core.AnnRunDesignerEventArgs.create(this.get_targetObject(),lt.Annotations.Core.AnnDesignerOperationStatus.end);this.onRun(b);b.get_cancel()&&this.cancel();return!b.get_cancel()},onRun:function(b){null!=this.__run$1&&this.__run$1(this,b)},onPointerDown:function(b,c){var a=lt.Annotations.Designers.AnnRunDesigner.callBaseMethod(this,"onPointerDown",[b,c]);this.get_hasStarted()&&!this.get_targetObject().get_isLocked()&&!a&&c.get_button()===lt.Annotations.Core.AnnMouseButton.left&&
(c.get_location(),a=!0,this.startWorking()&&this.working()&&this.endWorking());return a},onPointerMove:function(b,c){return lt.Annotations.Designers.AnnRunDesigner.callBaseMethod(this,"onPointerMove",[b,c])},onPointerUp:function(b,c){return lt.Annotations.Designers.AnnRunDesigner.callBaseMethod(this,"onPointerUp",[b,c])},onPointerDoubleClick:function(b,c){return lt.Annotations.Designers.AnnRunDesigner.callBaseMethod(this,"onPointerDoubleClick",[b,c])}};Object.defineProperty(lt.Annotations.Designers.AnnRunDesigner.prototype,
"run",{get:lt.Annotations.Designers.AnnRunDesigner.prototype.__run$1_handler_get,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Designers.AnnRunDesigner.prototype,"hitTestBuffer",{get:lt.Annotations.Designers.AnnRunDesigner.prototype.get_hitTestBuffer,set:lt.Annotations.Designers.AnnRunDesigner.prototype.set_hitTestBuffer,enumerable:!0,configurable:!0});lt.Annotations.Designers.AnnTextRollupRunDesigner=function(b,c,a){lt.Annotations.Designers.AnnTextRollupRunDesigner.initializeBase(this,
[b,c,a])};lt.Annotations.Designers.AnnTextRollupRunDesigner.prototype={onRun:function(b){if(b.get_operationStatus()===lt.Annotations.Core.AnnDesignerOperationStatus.end){var c=Type.safeCast(this.get_targetObject(),lt.Annotations.Core.AnnTextRollupObject);c.set_expanded(!c.get_expanded());this.invalidate(this.get_targetObject().getInvalidateRect(this.get_container().get_mapper(),this.getRenderer()))}lt.Annotations.Designers.AnnTextRollupRunDesigner.callBaseMethod(this,"onRun",[b])}};lt.Annotations.Designers._lT_VersionNumber.registerClass("lt.Annotations.Designers._lT_VersionNumber");
lt.Annotations.Designers._leadRectDExtensions.registerClass("lt.Annotations.Designers._leadRectDExtensions");lt.Annotations.Designers._leadPointDExtensions.registerClass("lt.Annotations.Designers._leadPointDExtensions");lt.Annotations.Designers._leadLengthDExtensions.registerClass("lt.Annotations.Designers._leadLengthDExtensions");lt.Annotations.Designers._leadSizeDExtensions.registerClass("lt.Annotations.Designers._leadSizeDExtensions");lt.Annotations.Designers.AnnDesigner.registerClass("lt.Annotations.Designers.AnnDesigner");
lt.Annotations.Designers.AnnDrawDesigner.registerClass("lt.Annotations.Designers.AnnDrawDesigner",lt.Annotations.Designers.AnnDesigner);lt.Annotations.Designers.AnnCrossProductDrawDesigner.registerClass("lt.Annotations.Designers.AnnCrossProductDrawDesigner",lt.Annotations.Designers.AnnDrawDesigner);lt.Annotations.Designers.AnnFreehandDrawDesigner.registerClass("lt.Annotations.Designers.AnnFreehandDrawDesigner",lt.Annotations.Designers.AnnDrawDesigner);lt.Annotations.Designers.AnnPolylineDrawDesigner.registerClass("lt.Annotations.Designers.AnnPolylineDrawDesigner",
lt.Annotations.Designers.AnnDrawDesigner);lt.Annotations.Designers.AnnLineDrawDesigner.registerClass("lt.Annotations.Designers.AnnLineDrawDesigner",lt.Annotations.Designers.AnnPolylineDrawDesigner);lt.Annotations.Designers.AnnPointDrawDesigner.registerClass("lt.Annotations.Designers.AnnPointDrawDesigner",lt.Annotations.Designers.AnnDrawDesigner);lt.Annotations.Designers.AnnProtractorDrawDesigner.registerClass("lt.Annotations.Designers.AnnProtractorDrawDesigner",lt.Annotations.Designers.AnnDrawDesigner);
lt.Annotations.Designers.AnnRectangleDrawDesigner.registerClass("lt.Annotations.Designers.AnnRectangleDrawDesigner",lt.Annotations.Designers.AnnDrawDesigner);lt.Annotations.Designers.AnnStickyNoteDrawDesigner.registerClass("lt.Annotations.Designers.AnnStickyNoteDrawDesigner",lt.Annotations.Designers.AnnDrawDesigner);lt.Annotations.Designers.AnnTextPointerDrawDesigner.registerClass("lt.Annotations.Designers.AnnTextPointerDrawDesigner",lt.Annotations.Designers.AnnDrawDesigner);lt.Annotations.Designers.AnnTextReviewDrawDesigner.registerClass("lt.Annotations.Designers.AnnTextReviewDrawDesigner",
lt.Annotations.Designers.AnnRectangleDrawDesigner);lt.Annotations.Designers.AnnEditDesigner.registerClass("lt.Annotations.Designers.AnnEditDesigner",lt.Annotations.Designers.AnnDesigner);lt.Annotations.Designers.AnnCrossProductEditDesigner.registerClass("lt.Annotations.Designers.AnnCrossProductEditDesigner",lt.Annotations.Designers.AnnEditDesigner);lt.Annotations.Designers.AnnPointEditDesigner.registerClass("lt.Annotations.Designers.AnnPointEditDesigner",lt.Annotations.Designers.AnnEditDesigner);
lt.Annotations.Designers.AnnPolylineEditDesigner.registerClass("lt.Annotations.Designers.AnnPolylineEditDesigner",lt.Annotations.Designers.AnnEditDesigner);lt.Annotations.Designers.AnnRectangleEditDesigner.registerClass("lt.Annotations.Designers.AnnRectangleEditDesigner",lt.Annotations.Designers.AnnEditDesigner);lt.Annotations.Designers.AnnSelectionEditDesigner.registerClass("lt.Annotations.Designers.AnnSelectionEditDesigner",lt.Annotations.Designers.AnnRectangleEditDesigner);lt.Annotations.Designers.AnnTextEditDesigner.registerClass("lt.Annotations.Designers.AnnTextEditDesigner",
lt.Annotations.Designers.AnnRectangleEditDesigner);lt.Annotations.Designers.AnnTextPointerEditDesigner.registerClass("lt.Annotations.Designers.AnnTextPointerEditDesigner",lt.Annotations.Designers.AnnTextEditDesigner);lt.Annotations.Designers.AnnTextReviewEditDesigner.registerClass("lt.Annotations.Designers.AnnTextReviewEditDesigner",lt.Annotations.Designers.AnnEditDesigner);lt.Annotations.Designers.AnnRunDesigner.registerClass("lt.Annotations.Designers.AnnRunDesigner",lt.Annotations.Designers.AnnDesigner);
lt.Annotations.Designers.AnnTextRollupRunDesigner.registerClass("lt.Annotations.Designers.AnnTextRollupRunDesigner",lt.Annotations.Designers.AnnRunDesigner);lt.Annotations.Designers._lT_VersionNumber.l_VER_PRODUCT="LEADTOOLS\u00ae for JavaScript";lt.Annotations.Designers._lT_VersionNumber.l_VER_COMPANYNAME_STR="LEAD Technologies, Inc.";lt.Annotations.Designers._lT_VersionNumber.l_VER_LEGALTRADEMARKS_STR="LEADTOOLS\u00ae is a trademark of LEAD Technologies, Inc.";lt.Annotations.Designers._lT_VersionNumber.l_VER_LEGALCOPYRIGHT_STR=
"\u00a9 1991-2017 LEAD Technologies, Inc.";lt.Annotations.Designers._lT_VersionNumber.l_VER_DLLEXT=".dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_EXEEXT=".exe";lt.Annotations.Designers._lT_VersionNumber.l_VER_PLATFORM="";lt.Annotations.Designers._lT_VersionNumber.l_VER_PLATFORM_FOR="";lt.Annotations.Designers._lT_VersionNumber.l_VER_PRODUCTNAME_STR="LEADTOOLS\u00ae for JavaScript";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_XXX="Leadtools.Xxx.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_XXX=
"LEADTOOLS Xxx";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_KERNEL="lt.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_KERNEL="Leadtools";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CONTROLS="lt.Controls.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CONTROLS="Controls";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_DOCUMENTS_UI="lt.Documents.UI.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_DOCUMENTS_UI=
"Documents User Interface";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CONTROLS_MEDICAL="lt.Controls.Medical.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CONTROLS_MEDICAL="Medical Controls";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_DOCUMENTS="lt.Documents.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_DOCUMENTS="Documents";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_CORE=
"lt.Annotations.Core.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_CORE="Annotations Core";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_AUTOMATION="lt.Annotations.Automation.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_AUTOMATION="Annotations Automation";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_DESIGNERS="lt.Annotations.Designers.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_DESIGNERS=
"Annotations Designers";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_RENDERING="lt.Annotations.Rendering.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_RENDERING="Annotations Rendering";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CCOW="Leadtools.Ccow.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CCOW="Leadtools CCOW Library";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_DOCUMENTS=
"Leadtools.Annotations.Documents.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_DOCUMENTS="Annotations Documents";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_LEGACY="Leadtools.Annotations.Legacy.dll";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_LEGACY="Annotations Legacy";lt.Annotations.Designers._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_JAVASCRIPT="Leadtools.Annotations.JavaScript.dll";
lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_JAVASCRIPT="JavaScripot Annotations";lt.Annotations.Designers._lT_VersionNumber.l_VER_PRODUCTVERSION_DOT_STR="********";lt.Annotations.Designers._lT_VersionNumber.l_VER_FILEVERSION_DOT_STR="********";lt.Annotations.Designers.AnnDesigner._unit=1/720;lt.Annotations.Designers.AnnTextReviewDrawDesigner._dummyRectangle$3=null;lt.Annotations.Designers.AnnTextReviewDrawDesigner._dummyRectangle$3=new lt.Annotations.Core.AnnRectangleObject;
lt.Annotations.Designers.AnnTextReviewDrawDesigner._dummyRectangle$3.set_stroke(lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("rgba(0,0,0,0.5)"),lt.Annotations.Designers._leadLengthDExtensions.create(3)))})();
