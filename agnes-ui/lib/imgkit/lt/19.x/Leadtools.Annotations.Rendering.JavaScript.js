/**************************************************
LEADTOOLS (C) 1991-2017 LEAD Technologies, Inc. ALL RIGHTS RESERVED.
This software is protected by United States and International copyright laws.
Any copying, duplication, deployment, redistribution, modification or other
disposition hereof is STRICTLY PROHIBITED without an express written license
granted by LEAD Technologies, Inc.. This notice may not be removed or otherwise
altered under any circumstances.
Portions of this product are licensed under US patent 5,327,254 and foreign
counterparts.
For more information, contact LEAD Technologies, Inc. at 704-332-5532 or visit
https://www.leadtools.com
**************************************************/
// Leadtools.Annotations.Rendering.JavaScript.js
// Version:*********
(function(){Type.registerNamespace("lt.Annotations.Rendering");window.lt.Annotations.Rendering._lT_VersionNumber=function(){};lt.Annotations.Rendering.AnnHtml5RenderingEngine=function(){lt.Annotations.Rendering.AnnHtml5RenderingEngine.initializeBase(this);this._init$1()};lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont=function(b,c){var a="";switch(c.get_fontWeight()){case lt.Annotations.Core.AnnFontWeight.bold:case lt.Annotations.Core.AnnFontWeight.extraBold:case lt.Annotations.Core.AnnFontWeight.semiBold:a=
"bold";break;default:a=""}switch(c.get_fontStyle()){case lt.Annotations.Core.AnnFontStyle.italic:a+="italic"}a=String.format("{0} {1}pt {2}",a,c.get_fontSize(),c.get_fontFamilyName());b.font=a};lt.Annotations.Rendering.AnnHtml5RenderingEngine.measureTextHeight=function(b,c){var a=document.createElement("canvas"),d=parseInt(b.measureText("gM").width)+1,e=parseInt(1.25*(96*c.get_fontSize()/72));a.width=d;a.height=e;a=a.getContext("2d");a.save();lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont(a,
c);a.textBaseline="top";a.fillStyle="#000000";a.fillText("gM",0,0);var f=0,g=0;if(0<d&&0<e){for(var h=a.getImageData(0,0,d,e),i=e,j=0;!g&&0<i;){i--;for(j=0;j<d;j++)if(h.data[4*i*d+4*j+3]){g=i;break}}for(i=0;i<e&&!f;){i++;for(j=0;j<d;j++)if(h.data[4*i*d+4*j+3]){f=i;break}}}a.restore();return g+f};lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFill=function(b,c){var a=Type.safeCast(c,lt.Annotations.Core.AnnSolidColorBrush);null!=b&&null!=a&&(b.fillStyle=a.get_color())};lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds=
function(b,c,a,d){(lt.LeadDoubleTools.lessThan(a,0)||lt.LeadDoubleTools.greaterThan(a,1))&&lt.Annotations.Core.ExceptionHelper.argumentOutOfRangeException("opacity",a,"Must be a value greater than or equals to 0 and less than or equals to 1");if(null!=b){var e=Type.safeCast(c,lt.Annotations.Core.AnnSolidColorBrush);if(null!=e)lt.Annotations.Rendering._colorUtil.addOpacityToBrush(e,a),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFill(b,e);else if(c=Type.safeCast(c,lt.Annotations.Core.AnnLinearGradientBrush),
null!=c&&null!=c&&!d.get_isEmpty()){d=d.clone();switch(c.get_linearGradientMode()){case lt.Annotations.Core.AnnLinearGradientMode.horizontal:d.set_height(0);break;case lt.Annotations.Core.AnnLinearGradientMode.vertical:d.set_width(0)}d=b.createLinearGradient(d.get_x(),d.get_y(),d.get_width(),d.get_height());for(c=ss.IEnumerator.getEnumerator(c.get_gradientStops());c.moveNext();)e=c.current,d.addColorStop(e.get_offset(),lt.Annotations.Rendering._colorUtil.addOpacityToColor(e.get_color(),a));b.fillStyle=
d}}};lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillWithOpacity=function(b,c,a){(lt.LeadDoubleTools.lessThan(a,0)||lt.LeadDoubleTools.greaterThan(a,1))&&lt.Annotations.Core.ExceptionHelper.argumentOutOfRangeException("opacity",a,"Must be a value greater than or equals to 0 and less than or equals to 1");null!=b&&lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(b,c,a,lt.LeadRectD.get_empty())};lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStroke=function(b,c){if(null!=
b&&null!=c){var a=Type.safeCast(c.get_stroke(),lt.Annotations.Core.AnnSolidColorBrush);null!=a&&(b.lineWidth=lt.LTHelper.device!==lt.LTDevice.desktop?Math.max(c.get_strokeThickness().get_value(),1.1):Math.max(c.get_strokeThickness().get_value(),1),b.strokeStyle=a.get_color());switch(c.get_strokeLineJoin()){case lt.Annotations.Core.AnnStrokeLineJoin.miter:b.lineJoin="miter";break;case lt.Annotations.Core.AnnStrokeLineJoin.bevel:b.lineJoin="bevel";break;default:b.lineJoin="round"}switch(c.get_strokeEndLineCap()){case lt.Annotations.Core.AnnStrokeLineCap.square:b.lineCap=
"square";break;default:b.lineCap="round"}}null!=c.get_strokeDashArray()&&0<c.get_strokeDashArray().length&&(null!=b.setLineDash&&b.setLineDash(c.get_strokeDashArray()),null!=b.lineDashOffset&&(b.lineDashOffset=c.get_strokeDashOffset()))};lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity=function(b,c,a){(lt.LeadDoubleTools.lessThan(a,0)||lt.LeadDoubleTools.greaterThan(a,1))&&lt.Annotations.Core.ExceptionHelper.argumentOutOfRangeException("opacity",a,"Must be a value greater than or equals to 0 and less than or equals to 1");
null!=b&&null!=c&&(lt.Annotations.Rendering._colorUtil.addOpacityToBrush(Type.safeCast(c.get_stroke(),lt.Annotations.Core.AnnSolidColorBrush),a),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStroke(b,c))};lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawCurve=function(b,c,a){c=lt.Annotations.Core.Utils.splineCurve(c,a);b.moveTo(c[0].get_x(),c[0].get_y());for(a=1;a<c.length;++a)b.lineTo(c[a].get_x(),c[a].get_y())};lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawClosedCurve=function(b,
c,a){c=lt.Annotations.Core.Utils.splineClosedCurve(c,a);b.moveTo(c[0].get_x(),c[0].get_y());for(a=1;a<c.length;++a)b.lineTo(c[a].get_x(),c[a].get_y())};lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawEllipse=function(b,c){var a=0.5522848*(c.get_width()/2),d=0.5522848*(c.get_height()/2),e=c.get_left()+c.get_width(),f=c.get_top()+c.get_height(),g=c.get_left()+c.get_width()/2,h=c.get_top()+c.get_height()/2;b.moveTo(c.get_left(),h);b.bezierCurveTo(c.get_left(),h-d,g-a,c.get_top(),g,c.get_top());
b.bezierCurveTo(g+a,c.get_top(),e,h-d,e,h);b.bezierCurveTo(e,h+d,g+a,f,g,f);b.bezierCurveTo(g-a,f,c.get_left(),h+d,c.get_left(),h)};lt.Annotations.Rendering.AnnHtml5RenderingEngine.getTextSize=function(b,c){var a=document.createElement("canvas");a.width=200;a.height=200;var d=a.getContext("2d"),a=0;lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont(d,c);null!=d&&(null!=c&&c.get_isDirty()&&(c.set_fontHeight(parseInt(lt.Annotations.Rendering.AnnHtml5RenderingEngine.measureTextHeight(d,c))),c.set_isDirty(!1)),
a=c.get_fontHeight());d=d.measureText(b).width;return lt.LeadSizeD.create(d,a)};lt.Annotations.Rendering.AnnHtml5RenderingEngine.prototype={_context$1:null,get_context:function(){return this._context$1},set_context:function(b){return this._context$1=b},_init$1:function(){this.get_renderers()[lt.Annotations.Core.AnnObject.selectObjectId]=new lt.Annotations.Rendering.AnnRectangleObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.lineObjectId]=new lt.Annotations.Rendering.AnnPolylineObjectRenderer;
this.get_renderers()[lt.Annotations.Core.AnnObject.rectangleObjectId]=new lt.Annotations.Rendering.AnnRectangleObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.ellipseObjectId]=new lt.Annotations.Rendering.AnnEllipseObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.polyRulerObjectId]=new lt.Annotations.Rendering.AnnPolyRulerObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.rulerObjectId]=new lt.Annotations.Rendering.AnnPolyRulerObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.polylineObjectId]=
new lt.Annotations.Rendering.AnnPolylineObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.polygonObjectId]=new lt.Annotations.Rendering.AnnPolylineObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.stampObjectId]=new lt.Annotations.Rendering.AnnStampObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.rubberStampObjectId]=new lt.Annotations.Rendering.AnnRubberStampObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textObjectId]=new lt.Annotations.Rendering.AnnTextObjectRenderer;
this.get_renderers()[lt.Annotations.Core.AnnObject.textRollupObjectId]=new lt.Annotations.Rendering.AnnTextRollupObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textPointerObjectId]=new lt.Annotations.Rendering.AnnTextPointerObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.protractorObjectId]=new lt.Annotations.Rendering.AnnProtractorObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.crossProductObjectId]=new lt.Annotations.Rendering.AnnCrossProductObjectRenderer;
this.get_renderers()[lt.Annotations.Core.AnnObject.freehandObjectId]=new lt.Annotations.Rendering.AnnPolylineObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.hotspotObjectId]=new lt.Annotations.Rendering.AnnHotspotObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.freehandHotspotObjectId]=new lt.Annotations.Rendering.AnnFreehandHotspotObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.noteObjectId]=new lt.Annotations.Rendering.AnnNoteObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.hiliteObjectId]=
new lt.Annotations.Rendering.AnnHiliteObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.pointObjectId]=new lt.Annotations.Rendering.AnnPointObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.curveObjectId]=new lt.Annotations.Rendering.AnnCurveObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.closedCurveObjectId]=new lt.Annotations.Rendering.AnnCurveObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.pointerObjectId]=new lt.Annotations.Rendering.AnnPointerObjectRenderer;
this.get_renderers()[lt.Annotations.Core.AnnObject.redactionObjectId]=new lt.Annotations.Rendering.AnnRectangleObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.audioObjectId]=new lt.Annotations.Rendering.AnnMediaObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.mediaObjectId]=new lt.Annotations.Rendering.AnnMediaObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.encryptObjectId]=new lt.Annotations.Rendering.AnnEncryptObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.imageObjectId]=
new lt.Annotations.Rendering.AnnImageObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textHiliteObjectId]=new lt.Annotations.Rendering.AnnTextHiliteObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textStrikeoutObjectId]=new lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textUnderlineObjectId]=new lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textRedactionObjectId]=
new lt.Annotations.Rendering.AnnTextRedactionObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.stickyNoteObjectId]=new lt.Annotations.Rendering.AnnStickyNoteObjectRenderer;var b=new lt.Annotations.Rendering.AnnRectangleThumbStyle;b.set_size(lt.LeadSizeD.create(144,144));b.set_stroke(lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("black"),lt.LeadLengthD.create(1)));b.set_fill(lt.Annotations.Core.AnnSolidColorBrush.create("lightblue"));var c=new lt.Annotations.Rendering.AnnEllipseThumbStyle;
c.set_size(lt.LeadSizeD.create(72,72));c.set_stroke(lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("black"),lt.LeadLengthD.create(1)));c.set_fill(lt.Annotations.Core.AnnSolidColorBrush.create("lightgreen"));var a=new lt.Annotations.Rendering.AnnEllipseThumbStyle;a.set_size(lt.LeadSizeD.create(144,144));a.set_stroke(lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("black"),lt.LeadLengthD.create(1)));a.set_fill(lt.Annotations.Core.AnnSolidColorBrush.create("lightgreen"));
var d=this.get_renderers(),e;for(e in d){var f=d[e];f.set_locationsThumbStyle(b);f.set_rotateCenterThumbStyle(c);f.set_rotateGripperThumbStyle(a)}lt.Annotations.Core.AnnRenderingEngine.set_containerLabelRenderer(new lt.Annotations.Rendering.AnnLabelRenderer)},attach:function(b,c){this.attachContainer(b);this._context$1=c},detach:function(){lt.Annotations.Rendering.AnnHtml5RenderingEngine.callBaseMethod(this,"detach");this._context$1=null},measureString:function(b,c){var a=lt.LeadSizeD.get_empty(),
d=document.createElement("canvas").getContext("2d");null!=d&&(d.save(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont(d,c),a=lt.LeadSizeD.create(d.measureText(b).width,lt.Annotations.Rendering.AnnHtml5RenderingEngine.measureTextHeight(d,c)),d.restore());return a},drawPicture:function(b,c,a){if(!(null==b||null==this.get_context()))if(!b.get_isDirty()&&b.get_isLoaded()&&null!=b.get_internalData()){var d=b.get_internalData();b.set_isDirty(!1);!c.get_isEmpty()&&0<c.get_width()&&0<c.get_height()&&
(b=this.get_context().globalAlpha,this.get_context().globalAlpha=a.get_opacity(),this.get_context().drawImage(d,c.get_left(),c.get_top(),c.get_width(),c.get_height()),this.get_context().globalAlpha=b)}else if(null!=b.get_source()||null!=b.get_pictureData()&&0<b.get_pictureData().length)d=this.get_container().get_mapper(),(b.get_isDirty()||!b.get_isLoaded())&&this._doLoadPicture$1(d,b,a),a=this.get_renderers()[lt.Annotations.Core.AnnObject.rectangleObjectId],null!=a&&(b=new lt.Annotations.Core.AnnRectangleObject,
b.set_stroke(this.get_loadingPictureStroke()),b.set_fill(this.get_loadingPictureFill()),b.set_rect(d.rectToContainerCoordinates(c)),a.initialize(this),a.render(d,b),b.set_stroke(null),b.set_fill(null))},_freeInternalData$1:function(b){null!=b.get_internalData()&&b.set_internalData(null)},_doLoadPicture$1:function(b,c,a){var d=this.get_container();this._freeInternalData$1(c);var e=document.createElement("img"),f=null,g=null,f=ss.Delegate.create(this,function(){e.removeEventListener("load",f,!1);e.removeEventListener("error",
g,!1);c.set_isLoaded(!0);var h=lt.LeadRectD.create(0,0,e.naturalWidth,e.naturalHeight);b.set_ignoreDpiScale(!0);h=b.rectToContainerCoordinates(h);b.set_ignoreDpiScale(!1);c.set_width(h.get_width());c.set_height(h.get_height());h=document.createElement("canvas");h.width=e.width;h.height=e.height;h.getContext("2d").drawImage(e,0,0);try{c.set_pictureData(h.toDataURL("image/png")),c.set_pictureData(c.get_pictureData().remove(0,22))}catch(i){this.onLoadPicture(lt.Annotations.Core.AnnLoadPictureEventArgs.create(c,
a,d,i));e=null;return}h=d.get_mapper().clone();d.set_mapper(b);this.onLoadPicture(lt.Annotations.Core.AnnLoadPictureEventArgs.create(c,a,d,null));d.set_mapper(h);e=null}),g=function(){c.set_isLoaded(!1);c.set_internalData(null);e.removeEventListener("load",f,!1);e.removeEventListener("error",g,!1);e=null};e.addEventListener("load",f,!1);e.addEventListener("error",g,!1);c.set_isLoaded(!1);c.set_isDirty(!1);c.set_internalData(e);String.isNullOrEmpty(c.get_pictureData())?String.isNullOrEmpty(c.get_source())||
(e.src=c.get_source()):e.src="data:image/png;base64,"+c.get_pictureData()},_needsInvalidate$1:!1,onLoadPicture:function(b){if(null==b.get_error()){this._needsInvalidate$1=!0;var c=b.get_container().get_mapper().clone(),a=this.get_renderers()[b.get_annObject().get_id()],d=b.get_container().get_children(),e=b.get_annObject().get_id();null!=a&&setTimeout(ss.Delegate.create(this,function(){if(this._needsInvalidate$1){this._needsInvalidate$1=!1;for(var b=ss.IEnumerator.getEnumerator(d);b.moveNext();){var g=
b.current;g.get_id()===e&&(a.get_renderingEngine().get_stateless()?this.render(g.getInvalidateRect(c,a),!0):(a.render(c,g),a.renderThumbs(c,g.get_internalThumbLocations(),g.get_fixedStateOperations())))}}}),200)}lt.Annotations.Rendering.AnnHtml5RenderingEngine.callBaseMethod(this,"onLoadPicture",[b])},renderGrid:function(b,c){if(null==c)throw new lt.ArgumentNullException("container");if(null!=this._context$1&&!b){var a=lt.Annotations.Core.AnnContainerMapper.createDefault(),d=this.get_snapToGridOptions(),
e=d.get_lineSpacing(),f=a.lengthToContainerCoordinates(d.get_gridLength());a.updateTransform(c.get_mapper().get_transform());var g=a.get_transform(),h=lt.LeadMatrix.create(g.get_m11(),g.get_m12(),g.get_m21(),g.get_m11(),g.get_offsetX(),g.get_offsetY());a.updateTransform(h);var h=a.lengthFromContainerCoordinates(f,lt.Annotations.Core.AnnFixedStateOperations.none),i=lt.LeadMatrix.create(g.get_m22(),g.get_m12(),g.get_m21(),g.get_m22(),g.get_offsetX(),g.get_offsetY());a.updateTransform(i);f=a.lengthFromContainerCoordinates(f,
lt.Annotations.Core.AnnFixedStateOperations.none);a.updateTransform(g);var j=c.get_mapper().rectFromContainerCoordinates(lt.LeadRectD.create(0,0,c.get_size().get_width(),c.get_size().get_height()),lt.Annotations.Core.AnnFixedStateOperations.none),a=j.get_left(),g=j.get_top(),i=j.get_right(),j=j.get_bottom(),k=new lt.LeadPointD,l=new lt.LeadPointD;k.set_y(g);l.set_y(j);var m=h*e,e=f*e;this._context$1.save();if(null!=d.get_gridStroke()){lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(this._context$1,
c.get_mapper().strokeFromContainerCoordinates(d.get_gridStroke(),lt.Annotations.Core.AnnFixedStateOperations.strokeWidth),d.get_opacity());this._context$1.beginPath();for(var n=a;n<i;n+=m)k.set_x(n),l.set_x(n),this._context$1.moveTo(k.get_x(),k.get_y()),this._context$1.lineTo(l.get_x(),l.get_y());k.set_x(a);l.set_x(i);for(n=g;n<j;n+=e)k.set_y(n),l.set_y(n),this._context$1.moveTo(k.get_x(),k.get_y()),this._context$1.lineTo(l.get_x(),l.get_y());this._context$1.closePath();this._context$1.stroke();k=
e=0;for(e=a;e<i;e+=h)for(k=g;k<j;k+=f)lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(this._context$1,d.get_gridStroke().get_stroke(),d.get_opacity(),lt.LeadRectD.get_empty()),this._context$1.fillRect(e,k,1,1)}this._context$1.restore()}}};Object.defineProperty(lt.Annotations.Rendering.AnnHtml5RenderingEngine.prototype,"context",{get:lt.Annotations.Rendering.AnnHtml5RenderingEngine.prototype.get_context,set:lt.Annotations.Rendering.AnnHtml5RenderingEngine.prototype.set_context,
enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnSvgRenderingEngine=function(){lt.Annotations.Rendering.AnnSvgRenderingEngine.initializeBase(this);this._init$1()};lt.Annotations.Rendering.AnnSvgRenderingEngine.exportSvg=function(b,c,a){var d=new lt.Annotations.Rendering.AnnSvgRenderingEngine;null!=a&&d.set_renderers(a);a=b.get_mapper();null!=c&&(a=c);var c=document.createElement("div"),e=a.sizeFromContainerCoordinates(b.get_size());c.style.width=String.format("{0}px",e.get_width());c.style.height=
String.format("{0}px",e.get_height());c.id="Save";document.documentElement.appendChild(c);e=b.clone();d.attach(e,c);b.get_children();for(b=ss.IEnumerator.getEnumerator(e.get_children());b.moveNext();)if(e=b.current,e.get_isVisible()&&!String.isNullOrEmpty(e.get_stateId())&&Object.keyExists(d.get_renderers(),e.get_id())){var f=d.get_renderers()[e.get_id()];null!=f&&(f.initialize(d),null!=f&&0<e.get_points().get_count()&&f.render(a,e))}a=lt.Annotations.Rendering._annSvgHelpers.getElementById(d.get_element().id+
"AnnThumbGroup");null!=a&&d.get_element().removeChild(a);a=c.innerHTML;d.detach();document.documentElement.removeChild(c);a=a.replaceAll('xmlns="http://www.w3.org/2000/svg"',"");a=a.replaceAll('xmlns:xlink="http://www.w3.org/1999/xlink"',"");a=a.replaceAll("xlink:href",'xmlns:xlink="http://www.w3.org/1999/xlink" xlink:href');return a=a.replaceAll("<svg",'<svg xmlns="http://www.w3.org/2000/svg"')};lt.Annotations.Rendering.AnnSvgRenderingEngine.prototype={_element$1:null,get_element:function(){return this._element$1},
set_element:function(b){return this._element$1=b},renderGrid:function(){},render:function(){var b=this.get_container().get_mapper();this.get_container().get_children();for(var c=ss.IEnumerator.getEnumerator(this.get_container().get_children());c.moveNext();){var a=c.current;if(a.get_isVisible()&&!String.isNullOrEmpty(a.get_stateId())&&Object.keyExists(this.get_renderers(),a.get_id())){var d=this.get_renderers()[a.get_id()];null!=d&&(d.initialize(this),null!=d&&0<a.get_points().get_count()&&d.render(b,
a))}}},_init$1:function(){this.get_renderers()[lt.Annotations.Core.AnnObject.selectObjectId]=new lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.lineObjectId]=new lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.rectangleObjectId]=new lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.ellipseObjectId]=new lt.Annotations.Rendering.AnnSvgEllipseObjectRenderer;
this.get_renderers()[lt.Annotations.Core.AnnObject.polyRulerObjectId]=new lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.rulerObjectId]=new lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.polylineObjectId]=new lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.polygonObjectId]=new lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer;
this.get_renderers()[lt.Annotations.Core.AnnObject.stampObjectId]=new lt.Annotations.Rendering.AnnSvgStampObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.rubberStampObjectId]=new lt.Annotations.Rendering.AnnSvgRubberStampObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textObjectId]=new lt.Annotations.Rendering.AnnSvgTextObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textRollupObjectId]=new lt.Annotations.Rendering.AnnSvgTextRollupObjectRenderer;
this.get_renderers()[lt.Annotations.Core.AnnObject.textPointerObjectId]=new lt.Annotations.Rendering.AnnSvgTextPointerObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.protractorObjectId]=new lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.crossProductObjectId]=new lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.freehandObjectId]=new lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer;
this.get_renderers()[lt.Annotations.Core.AnnObject.hotspotObjectId]=new lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.freehandHotspotObjectId]=new lt.Annotations.Rendering.AnnSvgFreehandHotspotObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.noteObjectId]=new lt.Annotations.Rendering.AnnSvgNoteObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.hiliteObjectId]=new lt.Annotations.Rendering.AnnSvgHiliteObjectRenderer;
this.get_renderers()[lt.Annotations.Core.AnnObject.pointObjectId]=new lt.Annotations.Rendering.AnnSvgPointObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.curveObjectId]=new lt.Annotations.Rendering.AnnSvgCurveObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.closedCurveObjectId]=new lt.Annotations.Rendering.AnnSvgCurveObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.pointerObjectId]=new lt.Annotations.Rendering.AnnSvgPointerObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.redactionObjectId]=
new lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.audioObjectId]=new lt.Annotations.Rendering.AnnSvgMediaObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.mediaObjectId]=new lt.Annotations.Rendering.AnnSvgMediaObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.encryptObjectId]=new lt.Annotations.Rendering.AnnSvgEncryptObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textHiliteObjectId]=new lt.Annotations.Rendering.AnnSvgTextHiliteObjectRenderer;
this.get_renderers()[lt.Annotations.Core.AnnObject.textStrikeoutObjectId]=new lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textUnderlineObjectId]=new lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.textRedactionObjectId]=new lt.Annotations.Rendering.AnnSvgTextRedactionObjectRenderer;this.get_renderers()[lt.Annotations.Core.AnnObject.stickyNoteObjectId]=new lt.Annotations.Rendering.AnnSvgStickyNoteObjectRenderer;
var b=new lt.Annotations.Rendering.AnnSvgRectangleThumbStyle;b.set_size(lt.LeadSizeD.create(144,144));b.set_stroke(lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("black"),lt.LeadLengthD.create(1)));b.set_fill(lt.Annotations.Core.AnnSolidColorBrush.create("lightblue"));var c=new lt.Annotations.Rendering.AnnSvgEllipseThumbStyle;c.set_size(lt.LeadSizeD.create(72,72));c.set_stroke(lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("black"),
lt.LeadLengthD.create(1)));c.set_fill(lt.Annotations.Core.AnnSolidColorBrush.create("lightgreen"));var a=new lt.Annotations.Rendering.AnnSvgEllipseThumbStyle;a.set_size(lt.LeadSizeD.create(144,144));a.set_stroke(lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("black"),lt.LeadLengthD.create(1)));a.set_fill(lt.Annotations.Core.AnnSolidColorBrush.create("lightgreen"));var d=this.get_renderers(),e;for(e in d){var f=d[e];f.initialize(this);f.set_locationsThumbStyle(b);
f.set_rotateCenterThumbStyle(c);f.set_rotateGripperThumbStyle(a)}lt.Annotations.Core.AnnRenderingEngine.set_containerLabelRenderer(new lt.Annotations.Rendering.AnnLabelRenderer)},measureString:function(){return new lt.LeadSizeD},_getSize$1:function(b){lt.LeadSizeD.create(0,0);var c=b.style.width,a=b.style.height;if(!c){var d=b.offsetWidth,e=b.offsetHeight;d&&(c=d,a=e);c||(d=b.clientWidth,b=b.clientHeight,d&&(c=d,a=b))}return lt.LeadSizeD.create(c,a)},attach:function(b,c){this._element$1=c;this.attachContainer(b);
var a=String.format("Annotation_Svg_{0}",c.id),d=lt.Annotations.Rendering._annSvgHelpers.getElementById(a);null==d&&(d=lt.Annotations.Rendering._annSvgHelpers.createRoot());if(null!=c&&d.parentNode!==c){var e=this._getSize$1(c);d.setAttribute("width",e.get_width());d.setAttribute("height",e.get_height());d.setAttribute("id",a);d.setAttribute("style","position: absolute;");c.appendChild(d)}},detach:function(){if(0<this.get_container().get_children().get_count())for(var b=ss.IEnumerator.getEnumerator(this.get_container().get_children());b.moveNext();){var c=
b.current;if(c.get_isVisible()&&!String.isNullOrEmpty(c.get_stateId())&&Object.keyExists(this.get_renderers(),c.get_id())){var a=this.get_renderers()[c.get_id()];null!=a&&a.removeObject(c)}}lt.Annotations.Rendering.AnnSvgRenderingEngine.callBaseMethod(this,"detach")},get_stateless:function(){return!1}};Object.defineProperty(lt.Annotations.Rendering.AnnSvgRenderingEngine.prototype,"element",{get:lt.Annotations.Rendering.AnnSvgRenderingEngine.prototype.get_element,set:lt.Annotations.Rendering.AnnSvgRenderingEngine.prototype.set_element,
enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnSvgRenderingEngine.prototype,"stateless",{get:lt.Annotations.Rendering.AnnSvgRenderingEngine.prototype.get_stateless,enumerable:!0,configurable:!0});lt.Annotations.Rendering._colorUtil=function(){};lt.Annotations.Rendering._colorUtil.addOpacityToBrush=function(b,c){1===c||null==b||b.set_color(lt.Annotations.Rendering._colorUtil.addOpacityToColor(b.get_color(),c))};lt.Annotations.Rendering._colorUtil.addOpacityToColor=
function(b,c){return 1===c?b:-1===b.toLowerCase().indexOf("transparent")?(b.startsWith("rgb")||(b=lt.Annotations.Rendering._colorUtil._nameToRGB(b)),lt.Annotations.Rendering._colorUtil._setOpacity(b,c)):b};lt.Annotations.Rendering._colorUtil._setOpacity=function(b,c){var a=b.indexOf("(")+1,d=b.indexOf(")")-a,a=b.substr(a,d).split(","),d=1;4===a.length&&(d=parseFloat(a[3]));a[3]=(c*d).toString();return"rgba("+a.toString()+")"};lt.Annotations.Rendering._colorUtil._nameToRGB=function(b){if(ss.isUndefined(lt.Annotations.Rendering._colorUtil._colorNamesToRGB[b.toLowerCase()]))throw Error.createError("Invalid Color Name",
null);return lt.Annotations.Rendering._colorUtil._colorNamesToRGB[b.toLowerCase()]};lt.Annotations.Rendering.AnnCrossProductObjectRenderer=function(){lt.Annotations.Rendering.AnnCrossProductObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnCrossProductObjectRenderer.prototype={render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(c,lt.Annotations.Core.AnnCrossProductObject);
if(null!=a){var d=new lt.Annotations.Core.AnnPolyRulerObject;d.set_showGauge(a.get_showGauge());d.set_showTickMarks(a.get_showTickMarks());d.set_showTickValue(a.get_showTickValue());d.set_tickMarksStroke(a.get_tickMarksStroke());d.get_stroke().set_stroke(a.get_stroke().get_stroke());d.get_stroke().set_strokeThickness(a.get_stroke().get_strokeThickness());d.set_measurementUnit(a.get_measurementUnit());for(var e=ss.IEnumerator.getEnumerator(Object.keys(a.get_unitsAbbreviation()));e.moveNext();){var f=
e.current;d.get_unitsAbbreviation()[f]=a.get_unitsAbbreviation()[f]}d.set_precision(a.get_precision());d.get_labels().RulerLength=a.get_labels().RulerLength;d.set_fixedStateOperations(c.get_fixedStateOperations());d.set_gaugeLength(a.get_gaugeLength());d.set_tickMarksLength(a.get_tickMarksLength());d.set_opacity(a.get_opacity());d.get_points().clear();d.get_points().add(a.get_firstStartPoint());d.get_points().add(a.get_firstEndPoint());lt.Annotations.Rendering.AnnCrossProductObjectRenderer.callBaseMethod(this,
"render",[b,d]);e=new lt.Annotations.Core.AnnPolyRulerObject;e.set_showGauge(a.get_showGauge());e.set_showTickMarks(a.get_showTickMarks());e.set_showTickValue(a.get_showTickValue());e.set_tickMarksStroke(a.get_tickMarksStroke());e.get_stroke().set_stroke(a.get_stroke().get_stroke());e.get_stroke().set_strokeThickness(a.get_stroke().get_strokeThickness());e.set_measurementUnit(a.get_measurementUnit());for(var g=ss.IEnumerator.getEnumerator(Object.keys(a.get_unitsAbbreviation()));g.moveNext();)f=g.current,
e.get_unitsAbbreviation()[f]=a.get_unitsAbbreviation()[f];e.set_precision(a.get_precision());e.set_fixedStateOperations(c.get_fixedStateOperations());e.set_gaugeLength(a.get_gaugeLength());e.set_tickMarksLength(a.get_tickMarksLength());e.set_opacity(a.get_opacity());e.get_labels().RulerLength=a.get_labels().SecondaryRulerLength;e.get_points().clear();e.get_points().add(a.get_secondStartPoint());e.get_points().add(a.get_secondEndPoint());lt.Annotations.Rendering.AnnCrossProductObjectRenderer.callBaseMethod(this,
"render",[b,e]);a.set_renderedObjectBounds(lt.LeadRectD.unionRects(d.get_renderedObjectBounds(),e.get_renderedObjectBounds()))}}};lt.Annotations.Rendering.AnnCurveObjectRenderer=function(){lt.Annotations.Rendering.AnnCurveObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnCurveObjectRenderer.prototype={_drawCurve$2:function(b,c,a){var d=Type.safeCast(a,lt.Annotations.Core.AnnCurveObject);b.get_context().beginPath();c=c.pointsFromContainerCoordinates(a.get_points().toArray(),d.get_fixedStateOperations());
2<c.length?d.get_isClosed()?lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawClosedCurve(b.get_context(),c,d.get_tension()):lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawCurve(b.get_context(),c,d.get_tension()):2===c.length&&(b.get_context().moveTo(c[0].get_x(),c[0].get_y()),b.get_context().lineTo(c[1].get_x(),c[1].get_y()))},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");
var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=a&&null!=a.get_context()){var d=Type.safeCast(c,lt.Annotations.Core.AnnCurveObject);if(null!=d){a.get_context().save();this._drawCurve$2(a,b,c);if(d.get_supportsFill()&&null!=d.get_fill()){var e=d.get_fill().clone();if(Type.canCast(e,lt.Annotations.Core.AnnHatchBrush))this.renderHatchBrushFill(a,b,d),this._drawCurve$2(a,b,c);else{var f=b.rectFromContainerCoordinates(d.get_bounds(),lt.Annotations.Core.AnnFixedStateOperations.none);
lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(a.get_context(),e,d.get_opacity(),f);a.get_context().fill()}}d.get_supportsStroke()&&null!=d.get_stroke()&&(e=b.strokeFromContainerCoordinates(d.get_stroke(),d.get_fixedStateOperations()),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(a.get_context(),e,d.get_opacity()),a.get_context().stroke());a.get_context().closePath();a.get_context().restore()}}}};lt.Annotations.Rendering.AnnEllipseObjectRenderer=function(){lt.Annotations.Rendering.AnnEllipseObjectRenderer.initializeBase(this)};
lt.Annotations.Rendering.AnnEllipseObjectRenderer.prototype={render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=a&&null!=a.get_context()){var d=Type.safeCast(c,lt.Annotations.Core.AnnEllipseObject);if(null!=d){var e=lt.LeadPointD.create(d.get_bounds().get_left()+d.get_bounds().get_width()/
2,d.get_bounds().get_top()+d.get_bounds().get_height()/2),e=b.pointFromContainerCoordinates(e,d.get_fixedStateOperations());a.get_context().save();a.get_context().beginPath();var f=new lt.LeadMatrix,e=lt.LeadPointD.create(parseInt(e.get_x()),parseInt(e.get_y())),g=d.get_rect().clone(),h=d.get_angle()+b.normalizeRectangle(g,d.get_fixedStateOperations());h&&(f.rotateAt(h,e.get_x(),e.get_y()),a.get_context().transform(f.get_m11(),f.get_m12(),f.get_m21(),f.get_m22(),f.get_offsetX(),f.get_offsetY()));
lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawEllipse(a.get_context(),g);d.get_supportsFill()&&null!=d.get_fill()&&(a.get_context().closePath(),Type.canCast(d.get_fill(),lt.Annotations.Core.AnnHatchBrush)?(this.renderHatchBrushFill(a,b,d),a.get_context().beginPath(),h&&a.get_context().transform(f.get_m11(),f.get_m12(),f.get_m21(),f.get_m22(),f.get_offsetX(),f.get_offsetY()),lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawEllipse(a.get_context(),g)):(f=d.get_fill().clone(),e=b.rectFromContainerCoordinates(d.get_bounds(),
lt.Annotations.Core.AnnFixedStateOperations.none),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(a.get_context(),f,d.get_opacity(),e),a.get_context().fill()));d.get_supportsStroke()&&null!=d.get_stroke()&&0<d.get_stroke().get_strokeThickness().get_value()&&(f=b.strokeFromContainerCoordinates(d.get_stroke(),d.get_fixedStateOperations()),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(a.get_context(),f,d.get_opacity()),a.get_context().stroke());a.get_context().closePath();
a.get_context().restore()}}}};lt.Annotations.Rendering.AnnFreehandHotspotObjectRenderer=function(){lt.Annotations.Rendering.AnnFreehandHotspotObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnFreehandHotspotObjectRenderer.prototype={render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);
if(null!=a&&null!=a.get_context()){var d=Type.safeCast(c,lt.Annotations.Core.AnnFreehandHotspotObject);if(null!=d&&a.get_container().get_userMode()!==lt.Annotations.Core.AnnUserMode.run){var e=b.rectFromContainerCoordinates(d.get_bounds(),d.get_fixedStateOperations()),f=this.beginClipPath();lt.Annotations.Rendering.AnnFreehandHotspotObjectRenderer.callBaseMethod(this,"render",[b,c]);b.pointsFromContainerCoordinates(c.get_points().toArray(),d.get_fixedStateOperations());if(null!=d.get_picture())a.drawPicture(d.get_picture(),
e,c);else{var g=this.get_renderingEngine().get_resources();null!=g&&-1<d.get_defaultPicture()&&d.get_defaultPicture()<g.get_images().length&&a.drawPicture(g.get_images()[d.get_defaultPicture()],e,c)}this.endClipPath(f)}}}};lt.Annotations.Rendering.AnnHiliteObjectRenderer=function(){lt.Annotations.Rendering.AnnHiliteObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnHiliteObjectRenderer.prototype={_drawRoundEdges$2:function(b,c,a){lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawEllipse(b,
lt.LeadRectD.create(c.get_x()+c.get_width()-a,c.get_y(),2*a,c.get_height()));lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawEllipse(b,lt.LeadRectD.create(c.get_x()-a,c.get_y(),2*a,c.get_height()))},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=a&&null!=a.get_context()){var d=
Type.safeCast(c,lt.Annotations.Core.AnnHiliteObject),e=c.get_points();if(1<e.get_count()){var f=this._saveContext();a.get_context().beginPath();var g=b.pointFromContainerCoordinates(e.get_item(0),d.get_fixedStateOperations());a.get_context().moveTo(g.get_x(),g.get_y());for(g=1;g<e.get_count();g++){var h=b.pointFromContainerCoordinates(e.get_item(g),d.get_fixedStateOperations());a.get_context().lineTo(h.get_x(),h.get_y())}a.get_context().closePath();e=d.get_hiliteColor();e=lt.Annotations.Core.AnnSolidColorBrush.create(e);
lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFill(a.get_context(),e);a.get_context().globalAlpha=0.5*d.get_opacity();a.get_context().fill();this._restoreContext(f)}}}};lt.Annotations.Rendering.AnnHotspotObjectRenderer=function(){lt.Annotations.Rendering.AnnHotspotObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnHotspotObjectRenderer.prototype={get_showAtRunMode:function(){return null!=this.get_renderingEngine()?this.get_renderingEngine().get_container().get_userMode()!==lt.Annotations.Core.AnnUserMode.run:
!1},_transformCounter$2:0,render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=a&&null!=a.get_context()){var d=Type.safeCast(c,lt.Annotations.Core.AnnHotspotObject);if(null!=d&&this.get_showAtRunMode()){var e=b.get_transform().clone(),f=b.get_rotateTransform().clone();f.get_isIdentity()||
b.updateTransform(b.get_transformWithoutRotate());var g=lt.LeadPointD.create(d.get_bounds().get_left()+d.get_bounds().get_width()/2,d.get_bounds().get_top()+d.get_bounds().get_height()/2),g=b.pointFromContainerCoordinates(g,d.get_fixedStateOperations()),h=this._saveContext(),i=d.get_rect(),i=b.rectFromContainerCoordinates(i,d.get_fixedStateOperations()),j=d.get_isFlipped()?-1:1,k=d.get_isReversed()?-1:1;if(!this._transformCounter$2){f.translatePrepend(g.get_x(),g.get_y());var l=lt.Annotations.Core.Utils.isFlipedReveresd(b)*
Math.abs(d.get_angle());(0>k||0>j)&&f.scalePrepend(k,j);lt.Annotations.Core.AnnDouble.isNaN(d.get_angle())||f.rotatePrepend(l*k*j);f.translatePrepend(-g.get_x(),-g.get_y());this._multiplyTransform(f)}this._transformCounter$2++;null!=d.get_picture()?a.drawPicture(d.get_picture(),i,c):(f=this.get_renderingEngine().get_resources(),null!=f&&-1<d.get_defaultPicture()&&d.get_defaultPicture()<f.get_images().length&&a.drawPicture(f.get_images()[d.get_defaultPicture()],i,c));this._restoreContext(h);this._transformCounter$2--;
b.updateTransform(e)}}}};Object.defineProperty(lt.Annotations.Rendering.AnnHotspotObjectRenderer.prototype,"showAtRunMode",{get:lt.Annotations.Rendering.AnnHotspotObjectRenderer.prototype.get_showAtRunMode,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnImageObjectRenderer=function(){lt.Annotations.Rendering.AnnImageObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnImageObjectRenderer.prototype={render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");
null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=this._getRotationAngle(b.get_transform()),d=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=d&&null!=d.get_context()){var e=Type.safeCast(c,lt.Annotations.Core.AnnImageObject);if(null!=e){var f=lt.LeadPointD.create(e.get_bounds().get_left()+e.get_bounds().get_width()/2,e.get_bounds().get_top()+e.get_bounds().get_height()/2),f=b.pointFromContainerCoordinates(f,e.get_fixedStateOperations());
d.get_context().save();var g=new lt.LeadMatrix,f=lt.LeadPointD.create(parseInt(f.get_x()),parseInt(f.get_y())),h=e.get_rect().clone(),h=lt.Annotations.Core.AnnTransformer.rotateRect(h,a),h=b.rectFromContainerCoordinates(h,e.get_fixedStateOperations()),i=e.get_isFlipped()?-1:1,j=e.get_isReversed()?-1:1;g.translate(-f.get_x(),-f.get_y());var k=Math.abs(e.get_angle());0>j*i&&g.scale(j,i);lt.Annotations.Core.AnnDouble.isNaN(a)||g.rotate(-a);lt.Annotations.Core.AnnDouble.isNaN(e.get_angle())||g.rotate(k);
g.translate(f.get_x(),f.get_y());d.get_context().transform(g.get_m11(),g.get_m12(),g.get_m21(),g.get_m22(),g.get_offsetX(),g.get_offsetY());null!=e.get_picture()&&d.drawPicture(e.get_picture(),h,c);d.get_context().restore()}}}};lt.Annotations.Rendering.AnnLabelRenderer=function(){};lt.Annotations.Rendering.AnnLabelRenderer.prototype={_engine:null,_rotateWithParent:!0,get_rotateWithParent:function(){return this._rotateWithParent},set_rotateWithParent:function(b){return this._rotateWithParent=b},initialize:function(b){null==
b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("renderingEngine");this._engine=b},_offsetHeight:!1,get_offsetHeight:function(){return this._offsetHeight},set_offsetHeight:function(b){return this._offsetHeight=b},get_renderingEngine:function(){return this._engine},getBounds:function(b,c,a){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("label");var d=lt.LeadRectD.get_empty();if(String.isNullOrEmpty(c.get_text()))return d;
if(null!=c.get_font()&&c.get_isVisible()){var e=this._getRotationAngle(b.get_transform()),d=lt.LeadPointD.create(c.get_originalPosition().get_x(),c.get_originalPosition().get_y());if(!this._rotateWithParent){var f=c.get_parent();null!=f&&f.get_labels().AnnObjectName===c&&(f=f.get_bounds(),-90===e?d=f.get_bottomLeft():180===e?d=f.get_bottomRight():90===e&&(d=f.get_topRight()),e=0)}c.get_positionMode()===lt.Annotations.Core.AnnLabelPositionMode.relativeToObject&&(f=c.get_parent(),null!=f&&!d.get_isEmpty()&&
(f=f.get_bounds(),d.set_x(d.get_x()+f.get_x()),d.set_y(d.get_y()+f.get_y())));d=b.pointFromContainerCoordinates(d,a);if(d.get_isEmpty())return lt.LeadRectD.get_empty();f=b.fontFromContainerCoordinates(c.get_font(),a);f=this._getTextSize(this._engine,c.get_text(),f);c.get_font().get_isDirty()&&(c.get_font().set_fontHeight(parseInt(f.get_height())),c.get_font().set_isDirty(!1));if(!c.get_offset().get_isEmpty()){var g=b.lengthFromContainerCoordinates(lt.LeadLengthD.create(c.get_offset().get_x()),lt.Annotations.Core.AnnFixedStateOperations.none),
h=b.lengthFromContainerCoordinates(lt.LeadLengthD.create(c.get_offset().get_y()),lt.Annotations.Core.AnnFixedStateOperations.none);d.set_x(d.get_x()+g);d.set_y(d.get_y()+h)}this._offsetHeight&&d.set_y(d.get_y()-f.get_height());if(c.get_restrictionMode()!==lt.Annotations.Core.AnnLabelRestriction.none){var g=b.rectFromContainerCoordinates(c.get_restrictionRectangle(),a),h=(c.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds)===lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds,
i=(c.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToUserRect)===lt.Annotations.Core.AnnLabelRestriction.restrictToUserRect,c=(c.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToContainer)===lt.Annotations.Core.AnnLabelRestriction.restrictToContainer;if((h||i)&&!g.get_isEmpty())d.set_x(Math.min(Math.max(d.get_x(),g.get_left()),g.get_left()+g.get_width()-f.get_width())),d.set_y(Math.min(Math.max(d.get_y(),g.get_top()),g.get_top()+g.get_height()-f.get_height()));
c&&(c=this.get_renderingEngine().get_clipRectangle(),c.get_isEmpty()||(c=c.get_size().clone(),b=b.rectFromContainerCoordinates(lt.LeadRectD.create(0,0,c.get_width(),c.get_height()),a),d.set_x(Math.min(Math.max(d.get_x(),b.get_left()),b.get_left()+b.get_width()-f.get_width())),d.set_y(Math.min(Math.max(d.get_y(),b.get_top()),b.get_top()+b.get_height()-f.get_height()))))}d=lt.LeadRectD.create(d.get_x(),d.get_y(),f.get_width(),f.get_height());e&&(b=new lt.LeadMatrix,b.rotateAt(-e,d.get_bottomLeft().get_x(),
d.get_bottomLeft().get_y()),d=b.transformRect(d))}d.get_isEmpty()||d.inflate(1,1);return d},_getRotationAngle:function(b){return 180*Math.atan2(b.get_m21(),b.get_m11())/Math.PI},renderLabel:function(b,c,a){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("label");if(!String.isNullOrEmpty(c.get_text())){var d=this._getRotationAngle(b.get_transform());if(!(null==this.get_renderingEngine()||(this.get_renderingEngine().get_renderState()&
lt.Annotations.Core.AnnRenderState.label)!==lt.Annotations.Core.AnnRenderState.label)&&null!=c.get_font()&&c.get_isVisible()){var e=lt.LeadPointD.create(c.get_originalPosition().get_x(),c.get_originalPosition().get_y());if(!this._rotateWithParent){var f=c.get_parent();null!=f&&f.get_labels().AnnObjectName===c&&(f=f.get_bounds(),-90===d?e=f.get_bottomLeft():180===d?e=f.get_bottomRight():90===d&&(e=f.get_topRight()),d=0)}c.get_positionMode()===lt.Annotations.Core.AnnLabelPositionMode.relativeToObject&&
(f=c.get_parent(),null!=f&&!e.get_isEmpty()&&(f=f.get_bounds(),e.set_x(e.get_x()+f.get_x()),e.set_y(e.get_y()+f.get_y())));if(this.get_renderingEngine().get_stateless()){if(f=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine),null!=f&&null!=f.get_context()&&!e.get_isEmpty()){var e=b.pointFromContainerCoordinates(e,a),g=new lt.LeadMatrix;lt.Annotations.Core.AnnDouble.isNaN(d)||g.rotateAt(-d,e.get_x(),e.get_y());f.get_context().save();var h=b.fontFromContainerCoordinates(c.get_font(),
a);lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont(f.get_context(),h);var d=h.get_fontHeight(),i=this._getTextSize(f,c.get_text(),h),h=i.get_width();c.get_font().get_isDirty()&&(d=i.get_height(),c.get_font().set_fontHeight(parseInt(d)),c.get_font().set_isDirty(!1));if(!c.get_offset().get_isEmpty()){var i=b.lengthFromContainerCoordinates(lt.LeadLengthD.create(c.get_offset().get_x()),lt.Annotations.Core.AnnFixedStateOperations.none),j=b.lengthFromContainerCoordinates(lt.LeadLengthD.create(c.get_offset().get_y()),
lt.Annotations.Core.AnnFixedStateOperations.none);e.set_x(e.get_x()+i);e.set_y(e.get_y()+j)}this._offsetHeight&&e.set_y(e.get_y()-d);if(c.get_restrictionMode()!==lt.Annotations.Core.AnnLabelRestriction.none){var i=b.rectFromContainerCoordinates(c.get_restrictionRectangle(),a),j=(c.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds)===lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds,k=(c.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToUserRect)===
lt.Annotations.Core.AnnLabelRestriction.restrictToUserRect,l=(c.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToContainer)===lt.Annotations.Core.AnnLabelRestriction.restrictToContainer;if(j||k)e.set_x(Math.min(Math.max(e.get_x(),i.get_left()),i.get_left()+i.get_width()-h)),e.set_y(Math.min(Math.max(e.get_y(),i.get_top()),i.get_top()+i.get_height()-d));l&&(i=this.get_renderingEngine().get_container().get_size().clone(),a=b.rectFromContainerCoordinates(lt.LeadRectD.create(0,
0,i.get_width(),i.get_height()),a),e.set_x(Math.min(Math.max(e.get_x(),a.get_left()),a.get_left()+a.get_width()-h)),e.set_y(Math.min(Math.max(e.get_y(),a.get_top()),a.get_top()+a.get_height()-d)))}f.get_context().transform(g.get_m11(),g.get_m12(),g.get_m21(),g.get_m22(),g.get_offsetX(),g.get_offsetY());null!=c.get_background()&&(a=c.get_background(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFill(f.get_context(),a),f.get_context().fillRect(e.get_x(),e.get_y(),h,d));null!=c.get_foreground()&&
(a=c.get_foreground(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFill(f.get_context(),a),f.get_context().textBaseline="top",String.isNullOrEmpty(c.get_text())||f.get_context().fillText(c.get_text(),e.get_x(),e.get_y()));c.get_isVisible()?c.set_renderedLabelBounds(b.rectToContainerCoordinates(lt.LeadRectD.create(e.get_x(),e.get_y(),h,d))):c.set_renderedLabelBounds(lt.LeadRectD.get_empty());f.get_context().restore()}}else if(f=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine),
null!=f&&(g=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId()),null!=g)){if(null!=g.childNodes&&0<g.childNodes.length&&(d=g.childNodes.length,0<d))for(h=0;h<d;++h)g.removeChild(g.childNodes[0]);if(!e.get_isEmpty()){e=b.pointFromContainerCoordinates(e,a);h=b.fontFromContainerCoordinates(c.get_font(),a);lt.Annotations.Rendering._annSvgHelpers.setFontAttribute(g,h);i=f.measureString(c.get_text(),h);d=i.get_height();h=i.get_width();e.set_x(e.get_x()+c.get_offset().get_x());e.set_y(e.get_y()+
(this._offsetHeight?c.get_offset().get_y()-d:c.get_offset().get_y()));if(c.get_restrictionMode()!==lt.Annotations.Core.AnnLabelRestriction.none&&(i=b.rectFromContainerCoordinates(c.get_restrictionRectangle(),a),i.get_isEmpty()||(e.set_x(Math.min(Math.max(e.get_x(),i.get_left()),i.get_left()+i.get_width()-h)),e.set_y(Math.min(Math.max(e.get_y(),i.get_top()),i.get_top()+i.get_height()-d))),(c.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToContainer)===lt.Annotations.Core.AnnLabelRestriction.restrictToContainer))a=
b.rectFromContainerCoordinates(this.get_renderingEngine().get_clipRectangle(),a),e.set_x(Math.min(Math.max(e.get_x(),a.get_left()),a.get_left()+a.get_width()-h)),e.set_y(Math.min(Math.max(e.get_y(),a.get_top()),a.get_top()+a.get_height()-d));null!=c.get_background()&&(a=c.get_background(),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(g,a,!0));null!=c.get_foreground()&&(a=c.get_foreground(),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(g,a,!0),b=lt.Annotations.Rendering._annSvgHelpers.createTSpan(c.get_text(),
e.get_x(),e.get_y()),g.appendChild(b))}}}}},_getTextSize:function(b,c,a){b=lt.LeadSizeD.create(0,0);if(String.isNullOrEmpty(c))return b;var d=document.createElement("canvas").getContext("2d");null!=d&&(d.save(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont(d,a),b=lt.LeadSizeD.create(d.measureText(c).width,1.5*a.get_fontSize()),d.restore());return b}};Object.defineProperty(lt.Annotations.Rendering.AnnLabelRenderer.prototype,"rotateWithParent",{get:lt.Annotations.Rendering.AnnLabelRenderer.prototype.get_rotateWithParent,
set:lt.Annotations.Rendering.AnnLabelRenderer.prototype.set_rotateWithParent,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnLabelRenderer.prototype,"offsetHeight",{get:lt.Annotations.Rendering.AnnLabelRenderer.prototype.get_offsetHeight,set:lt.Annotations.Rendering.AnnLabelRenderer.prototype.set_offsetHeight,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnLabelRenderer.prototype,"renderingEngine",{get:lt.Annotations.Rendering.AnnLabelRenderer.prototype.get_renderingEngine,
enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnNoteObjectRenderer=function(){lt.Annotations.Rendering.AnnNoteObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnNoteObjectRenderer.prototype={renderShadow:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=
a&&null!=a.get_context()){var d=Type.safeCast(c,lt.Annotations.Core.AnnNoteObject),e=lt.LeadPointD.get_empty();if(null!=d){var f=this.getRenderPoints(b,d),f=b.pointsFromContainerCoordinates(f,d.get_fixedStateOperations()),g=[f[1],f[2],f[3]],h=lt.Annotations.Core.Utils.getUnitVectorPerpendicular(g[0],g[1]),i=lt.Annotations.Core.Utils.getUnitVector(g[0],g[1]),f=[lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty(),
lt.LeadPointD.get_empty()],j=lt.LeadLengthD.create(b.lengthFromContainerCoordinates(lt.LeadLengthD.create(d.get_shadowBorderWidth().get_value()),lt.Annotations.Core.AnnFixedStateOperations.zooming)),e=c.get_bounds();if(j.get_value()<e.get_width()&&j.get_value()<e.get_height()){f[0]=lt.Annotations.Core.Utils.transformPoint(i,j,g[0]);f[1]=lt.Annotations.Core.Utils.transformPoint(h,j,f[0]);e=lt.Annotations.Core.Utils.transformPoint(i,j,g[1]);f[2]=lt.Annotations.Core.Utils.transformPoint(h,j,e);e=lt.Annotations.Core.Utils.transformPoint(h,
j,g[2]);f[3]=lt.Annotations.Core.Utils.transformPoint(i,j,e);f[4]=e;f[5]=g[1];f[6]=f[0];this._updateRenderedObjectBounds(f,b,d);a.get_context().save();a.get_context().beginPath();a.get_context().moveTo(f[0].get_x(),f[0].get_y());for(g=1;g<f.length;g++)a.get_context().lineTo(f[g].get_x(),f[g].get_y());a.get_context().closePath();a.get_context().fillStyle="#7F7F7F";f=a.get_context().globalAlpha;a.get_context().globalAlpha=0.5*d.get_opacity();a.get_context().fill();a.get_context().globalAlpha=f;a.get_context().restore()}}}},
render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");this.renderShadow(b,c);lt.Annotations.Rendering.AnnNoteObjectRenderer.callBaseMethod(this,"render",[b,c])}};lt.Annotations.Rendering.AnnObjectRenderer=function(){};lt.Annotations.Rendering.AnnObjectRenderer.prototype={_clipPath:!1,get_clipPath:function(){return this._clipPath},set_clipPath:function(b){return this._clipPath=
b},beginClipPath:function(){this._clipPath=!0;Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);return null},endClipPath:function(){this._clipPath=!1;var b=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);null!=b&&null!=b.get_context()&&b.get_context().restore()},_saveContext:function(){var b=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null==b||null==b.get_context())return null;
b.get_context().save();return null},_restoreContext:function(){var b=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);null!=b&&null!=b.get_context()&&b.get_context().restore()},_clipContext:function(){var b=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);null!=b&&null!=b.get_context()&&b.get_context().clip()},_multiplyTransform:function(b){var c=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);
null!=c&&null!=c.get_context()&&c.get_context().transform(b.get_m11(),b.get_m12(),b.get_m21(),b.get_m22(),b.get_offsetX(),b.get_offsetY())},addObject:function(){},removeObject:function(){},renderHatchBrushFill:function(b,c,a){var b=b.get_context(),d=c.rectFromContainerCoordinates(a.get_bounds(),lt.Annotations.Core.AnnFixedStateOperations.none);b.save();b.clip();var a=Type.safeCast(a.get_fill(),lt.Annotations.Core.AnnHatchBrush),e=8*c.get_burnScaleFactor(),f=d.get_width()+d.get_height()+Math.max(d.get_x(),
d.get_y()),d=lt.LeadRectD.create(d.get_x(),d.get_y(),f,f);b.lineWidth=1*c.get_burnScaleFactor();b.fillStyle=a.get_backgroundColor();b.strokeStyle=a.get_foregroundColor();if(a.get_hatchStyle()===lt.Annotations.Core.AnnHatchStyle.horizontal||a.get_hatchStyle()===lt.Annotations.Core.AnnHatchStyle.cross)for(c=0;c<f/e;c++)b.beginPath(),b.moveTo(d.get_left(),c*e+d.get_top()),b.lineTo(d.get_right(),c*e+d.get_top()),b.stroke();if(a.get_hatchStyle()===lt.Annotations.Core.AnnHatchStyle.vertical||a.get_hatchStyle()===
lt.Annotations.Core.AnnHatchStyle.cross)for(c=0;c<f/e;c++)b.beginPath(),b.moveTo(d.get_left()+c*e,d.get_top()),b.lineTo(d.get_left()+c*e,d.get_bottom()),b.stroke();if(a.get_hatchStyle()===lt.Annotations.Core.AnnHatchStyle.forwardDiagonal||a.get_hatchStyle()===lt.Annotations.Core.AnnHatchStyle.diagonalCross)for(c=0;c<f/e;c++)b.beginPath(),b.moveTo(d.get_left(),c*e+d.get_top()),b.lineTo(d.get_right()-c*e,d.get_bottom()),0<c&&(b.moveTo(d.get_right(),d.get_bottom()-c*e),b.lineTo(d.get_left()+c*e,d.get_top())),
b.stroke();if(a.get_hatchStyle()===lt.Annotations.Core.AnnHatchStyle.backwardDiagonal||a.get_hatchStyle()===lt.Annotations.Core.AnnHatchStyle.diagonalCross)for(c=0;c<f/e;c++)b.beginPath(),b.moveTo(d.get_left(),c*e+d.get_top()),b.lineTo(d.get_left()+c*e,d.get_top()),b.stroke();b.restore()},_locationsThumbStyle:null,get_locationsThumbStyle:function(){return this._locationsThumbStyle},set_locationsThumbStyle:function(b){return this._locationsThumbStyle=b},_rotateCenterThumbStyle:null,get_rotateCenterThumbStyle:function(){return this._rotateCenterThumbStyle},
set_rotateCenterThumbStyle:function(b){return this._rotateCenterThumbStyle=b},_rotateGripperThumbStyle:null,get_rotateGripperThumbStyle:function(){return this._rotateGripperThumbStyle},set_rotateGripperThumbStyle:function(b){return this._rotateGripperThumbStyle=b},initialize:function(b){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("renderingException");this._renderingEngine=b;null==this._labelRenderer&&(this._labelRenderer=new lt.Annotations.Rendering.AnnLabelRenderer);this._labelRenderer.initialize(b)},
renderLocked:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=this._getRotationAngle(b.get_transform()),d=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=d&&null!=d.get_context()){var e=lt.LeadPointD.create(c.get_bounds().get_left(),c.get_bounds().get_top()),f=this.get_renderingEngine().get_resources();if(null!=f){var g=
f.get_images()[c.get_lockPicture()];null==g&&-1<c.get_lockPicture()&&c.get_lockPicture()<f.get_images().length&&(g=f.get_images()[c.get_lockPicture()]);if(null!=g){f=lt.LeadSizeD.create(g.get_width(),g.get_height());b.set_ignoreDpiScale(!0);f=b.sizeFromContainerCoordinates(f);b.set_ignoreDpiScale(!1);f=b.sizeToContainerCoordinates(f);e=lt.LeadRectD.create(e.get_x(),e.get_y(),f.get_width(),f.get_height());e=b.rectFromContainerCoordinates(e,c.get_fixedStateOperations());f=new lt.LeadMatrix;d.get_context().save();
if(!lt.Annotations.Core.AnnDouble.isNaN(a)){var h=lt.LeadPointD.create(e.get_left()+e.get_width()/2,e.get_top()+e.get_height()/2);f.rotateAt(-a,h.get_x(),h.get_y())}d.get_context().transform(f.get_m11(),f.get_m12(),f.get_m21(),f.get_m22(),f.get_offsetX(),f.get_offsetY());d.drawPicture(g,e,c);d.get_context().restore()}}}},renderSelection:function(){},renderAlignmentTarget:function(b,c){if(c.get_isAlignmentTarget()){var a=Type.safeCast(this._renderingEngine,lt.Annotations.Rendering.AnnHtml5RenderingEngine);
if(null!=a&&null!=a.get_context()){a.get_context().save();var d=this.getRenderPoints(b,c),e=new lt.Annotations.Core.AnnRectangleObject;e.get_points().clear();for(d=ss.IEnumerator.getEnumerator(d);d.moveNext();){var f=d.current;e.get_points().add(f)}e=b.rectFromContainerCoordinates(c.get_bounds(),c.get_fixedStateOperations());d=lt.Annotations.Core.AnnSolidColorBrush.create("LightSkyBlue");lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(a.get_context(),d,0.4,e);a.get_context().fillRect(e.get_x(),
e.get_y(),e.get_width(),e.get_height());a.get_context().restore()}}},renderContent:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=this._getRotationAngle(b.get_transform()),d=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=d&&null!=d.get_context()){var e=c.get_bounds(),f=lt.LeadPointD.create(e.get_left()+e.get_width()/
2,e.get_top()+e.get_height()/2),g=this.get_renderingEngine().get_resources();if(null!=g&&(e=g.get_images()[c.get_contentPicture()],null==e&&-1<c.get_contentPicture()&&c.get_contentPicture()<g.get_images().length&&(e=g.get_images()[c.get_contentPicture()]),null!=e)){g=lt.LeadSizeD.create(e.get_width(),e.get_height());b.set_ignoreDpiScale(!0);g=b.sizeFromContainerCoordinates(g);b.set_ignoreDpiScale(!1);g=b.sizeToContainerCoordinates(g);f=lt.LeadRectD.create(f.get_x(),f.get_y(),g.get_width(),g.get_height());
f=b.rectFromContainerCoordinates(f,c.get_fixedStateOperations());g=new lt.LeadMatrix;d.get_context().save();if(!lt.Annotations.Core.AnnDouble.isNaN(a)){var h=lt.LeadPointD.create(f.get_left()+f.get_width()/2,f.get_top()+f.get_height()/2);g.rotateAt(-a,h.get_x(),h.get_y())}d.get_context().transform(g.get_m11(),g.get_m12(),g.get_m21(),g.get_m22(),g.get_offsetX(),g.get_offsetY());d.drawPicture(e,f,c);d.get_context().restore()}}},renderThumbs:function(b,c,a){if(null!=c&&null!=this.get_locationsThumbStyle())for(c=
ss.IEnumerator.getEnumerator(c);c.moveNext();){var d=c.current;this.get_locationsThumbStyle().render(this,b,d,a)}},renderRotatePointThumbs:function(b,c,a,d){null!=this.get_rotateCenterThumbStyle()&&this.get_rotateCenterThumbStyle().render(this,b,c,d);null!=this.get_rotateGripperThumbStyle()&&this.get_rotateGripperThumbStyle().render(this,b,a,d)},getRenderPoints:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");
return c.get_points().toArray()},_updateRenderedObjectBounds:function(b,c,a){if(null!=b){for(var d=new lt.Annotations.Core.AnnRectangleObject,b=ss.IEnumerator.getEnumerator(b);b.moveNext();){var e=b.current;d.get_points().add(c.pointToContainerCoordinates(e))}a.set_renderedObjectBounds(lt.LeadRectD.unionRects(a.get_renderedObjectBounds(),d.get_bounds()));d.get_points().clear()}},_renderingEngine:null,get_renderingEngine:function(){return this._renderingEngine},_labelRenderer:null,get_labelRenderer:function(){return this._labelRenderer},
set_labelRenderer:function(b){return this._labelRenderer=b},_getRotationAngle:function(b){return 180*Math.atan2(b.get_m21(),b.get_m11())/Math.PI}};Object.defineProperty(lt.Annotations.Rendering.AnnObjectRenderer.prototype,"clipPath",{get:lt.Annotations.Rendering.AnnObjectRenderer.prototype.get_clipPath,set:lt.Annotations.Rendering.AnnObjectRenderer.prototype.set_clipPath,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnObjectRenderer.prototype,"locationsThumbStyle",
{get:lt.Annotations.Rendering.AnnObjectRenderer.prototype.get_locationsThumbStyle,set:lt.Annotations.Rendering.AnnObjectRenderer.prototype.set_locationsThumbStyle,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnObjectRenderer.prototype,"rotateCenterThumbStyle",{get:lt.Annotations.Rendering.AnnObjectRenderer.prototype.get_rotateCenterThumbStyle,set:lt.Annotations.Rendering.AnnObjectRenderer.prototype.set_rotateCenterThumbStyle,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnObjectRenderer.prototype,
"rotateGripperThumbStyle",{get:lt.Annotations.Rendering.AnnObjectRenderer.prototype.get_rotateGripperThumbStyle,set:lt.Annotations.Rendering.AnnObjectRenderer.prototype.set_rotateGripperThumbStyle,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnObjectRenderer.prototype,"renderingEngine",{get:lt.Annotations.Rendering.AnnObjectRenderer.prototype.get_renderingEngine,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnObjectRenderer.prototype,
"labelRenderer",{get:lt.Annotations.Rendering.AnnObjectRenderer.prototype.get_labelRenderer,set:lt.Annotations.Rendering.AnnObjectRenderer.prototype.set_labelRenderer,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnPointerObjectRenderer=function(){lt.Annotations.Rendering.AnnPointerObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnPointerObjectRenderer.prototype={_drawArrow$2:function(b,c,a){var d=b.getArrowPoints(),e=c.lengthFromContainerCoordinates(b.get_arrowLength(),
b.get_fixedStateOperations()|lt.Annotations.Core.AnnFixedStateOperations.zooming|lt.Annotations.Core.AnnFixedStateOperations.lengthValue);if(null!=d&&3<=d.length){lt.LeadPointD.get_empty();a.get_context().beginPath();for(var f=c.pointFromContainerCoordinates(d[1],b.get_fixedStateOperations()),g=0;2>g;g++){var h=c.pointFromContainerCoordinates(d[2*g],b.get_fixedStateOperations()),h=lt.Annotations.Core.Utils.getUnitVector(f,h),h=lt.Annotations.Core.Utils.transformPoint(h,lt.LeadLengthD.create(e),f);
a.get_context().moveTo(h.get_x(),h.get_y());a.get_context().lineTo(f.get_x(),f.get_y());g||h.clone()}}},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");Type.canCast(c.get_fill(),lt.Annotations.Core.AnnHatchBrush)||lt.Annotations.Rendering.AnnPointerObjectRenderer.callBaseMethod(this,"render",[b,c]);var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);
if(null!=a&&null!=a.get_context()){a.get_context().save();var d=Type.safeCast(c,lt.Annotations.Core.AnnPointerObject);if(null!=d){var e=d.getArrowPoints(),f=b.lengthFromContainerCoordinates(d.get_arrowLength(),d.get_fixedStateOperations()|lt.Annotations.Core.AnnFixedStateOperations.zooming|lt.Annotations.Core.AnnFixedStateOperations.lengthValue);if(null!=e&&3<=e.length){var g=lt.LeadPointD.get_empty();a.get_context().beginPath();for(var h=b.pointFromContainerCoordinates(e[1],d.get_fixedStateOperations()),
i=0;2>i;i++){var j=b.pointFromContainerCoordinates(e[2*i],d.get_fixedStateOperations()),j=lt.Annotations.Core.Utils.getUnitVector(h,j),j=lt.Annotations.Core.Utils.transformPoint(j,lt.LeadLengthD.create(f),h);a.get_context().moveTo(j.get_x(),j.get_y());a.get_context().lineTo(h.get_x(),h.get_y());i||(g=j.clone())}if(d.get_supportsFill()&&null!=d.get_fill())if(Type.canCast(d.get_fill(),lt.Annotations.Core.AnnHatchBrush)){a.get_context().lineTo(g.get_x(),g.get_y());a.get_context().closePath();this.renderHatchBrushFill(a,
b,d);this._drawArrow$2(d,b,a);a.get_context().lineTo(g.get_x(),g.get_y());a.get_context().closePath();var f=b.pointFromContainerCoordinates(e[0],d.get_fixedStateOperations()),j=b.pointFromContainerCoordinates(e[2],d.get_fixedStateOperations()),k=b.pointFromContainerCoordinates(d.get_points().get_item(0),d.get_fixedStateOperations()),e=b.pointFromContainerCoordinates(d.get_points().get_item(1),d.get_fixedStateOperations()),g=f.get_x(),f=f.get_y(),h=j.get_x(),j=j.get_y(),i=k.get_x(),k=k.get_y(),l=e.get_x(),
m=e.get_y();a.get_context().moveTo(((g*j-f*h)*(i-l)-(g-h)*(i*m-k*l))/((g-h)*(k-m)-(f-j)*(i-l)),((g*j-f*h)*(k-m)-(f-j)*(i*m-k*l))/((g-h)*(k-m)-(f-j)*(i-l)));a.get_context().lineTo(e.get_x(),e.get_y())}else j=d.get_fill().clone(),"transparent"!==lt.Annotations.Core.Utils.toLowerCase(j.get_color())&&(a.get_context().lineTo(g.get_x(),g.get_y()),a.get_context().closePath(),e=b.rectFromContainerCoordinates(d.get_bounds(),lt.Annotations.Core.AnnFixedStateOperations.none),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(a.get_context(),
j,d.get_opacity(),e),a.get_context().fill());else a.get_context().closePath();d.get_supportsStroke()&&null!=d.get_stroke()&&(j=b.strokeFromContainerCoordinates(d.get_stroke(),d.get_fixedStateOperations()),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(a.get_context(),j,d.get_opacity()),a.get_context().stroke())}}a.get_context().restore()}}};lt.Annotations.Rendering.AnnPointObjectRenderer=function(){lt.Annotations.Rendering.AnnPointObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnPointObjectRenderer.prototype=
{render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=a&&null!=a.get_context()){var d=Type.safeCast(c,lt.Annotations.Core.AnnPointObject);if(null!=d){var e=b.get_transform().clone(),f=b.get_rotateTransform().clone();f.get_isIdentity()||b.updateTransform(b.get_transformWithoutRotate());
var g=this._saveContext();if(null==d.get_picture()){var h=this.get_renderingEngine().get_resources();null!=h&&-1<d.get_defaultPicture()&&d.get_defaultPicture()<h.get_images().length&&d.set_picture(h.get_images()[d.get_defaultPicture()])}var h=d.get_picture(),i=lt.LeadSizeD.create(0,0),j=lt.Annotations.Core.AnnContainerMapper.createDefault();if(null!=h&&null!=h.get_internalData()){var k=h.get_internalData();null!=k&&(i.set_width(k.naturalWidth),i.set_height(k.naturalHeight));if(i.get_width()||i.get_height())i=
j.sizeToContainerCoordinates(i),h.set_width(i.get_width()),h.set_height(i.get_height()),j.updateTransform(b.get_transform()),i=j.sizeFromContainerCoordinates(i)}var k=c.get_points().get_item(0),l=lt.LeadRectD.create(0,0,0,0),k=b.pointFromContainerCoordinates(k,c.get_fixedStateOperations());this._multiplyTransform(f);if(d.get_showPicture())l=lt.LeadRectD.create(k.get_x()-i.get_width()/2,k.get_y()-i.get_height()/2,i.get_width(),i.get_height()),a.drawPicture(h,l,c);else if(j.updateTransform(b.get_transform()),
f=j.lengthFromContainerCoordinates(d.get_radius(),d.get_fixedStateOperations()),l=lt.LeadRectD.create(k.get_x()-f,k.get_y()-f,2*f,2*f),f=a.get_context(),f.beginPath(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawEllipse(f,l),f.closePath(),d.get_supportsFill()&&null!=d.get_fill()&&(Type.canCast(d.get_fill(),lt.Annotations.Core.AnnHatchBrush)?(this.renderHatchBrushFill(a,b,d),f.closePath(),f.beginPath(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawEllipse(f,l),f.closePath()):(a=b.rectFromContainerCoordinates(d.get_bounds(),
lt.Annotations.Core.AnnFixedStateOperations.none),h=d.get_fill().clone(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(f,h,d.get_opacity(),a),f.fill())),d.get_supportsStroke()&&null!=d.get_stroke())a=b.strokeFromContainerCoordinates(d.get_stroke(),d.get_fixedStateOperations()),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(f,a,d.get_opacity()),f.stroke();this._restoreContext(g);b.updateTransform(e)}}}};lt.Annotations.Rendering.AnnPolylineObjectRenderer=
function(){lt.Annotations.Rendering.AnnPolylineObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnPolylineObjectRenderer.prototype={_useSplineMode$1:!1,get_useSplineMode:function(){return this._useSplineMode$1},set_useSplineMode:function(b){return this._useSplineMode$1=b},_drawPolyLine$1:function(b,c,a){b.get_context().moveTo(a[0].get_x(),a[0].get_y());if(this._useSplineMode$1&&c.get_id()===lt.Annotations.Core.AnnObject.freehandObjectId)lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawCurve(b.get_context(),
a,1);else for(var d=1;d<a.length;d++)b.get_context().lineTo(a[d].get_x(),a[d].get_y());c.get_isClosed()&&(c=a.length-1,b.get_context().moveTo(a[c].get_x(),a[c].get_y()),b.get_context().lineTo(a[0].get_x(),a[0].get_y()))},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=
a&&null!=a.get_context()){a.get_context().save();var d=Type.safeCast(c,lt.Annotations.Core.AnnPolylineObject),e=b.pointsFromContainerCoordinates(c.get_points().toArray(),d.get_fixedStateOperations());if(1<e.length){a.get_context().beginPath();this._drawPolyLine$1(a,d,e);this.get_clipPath()&&a.get_context().clip();if(d.get_supportsFill()&&null!=d.get_fill())if(Type.canCast(d.get_fill(),lt.Annotations.Core.AnnHatchBrush))this.renderHatchBrushFill(a,b,d),a.get_context().beginPath(),this._drawPolyLine$1(a,
d,e);else{var e=d.get_fill().clone(),f=b.rectFromContainerCoordinates(d.get_bounds(),lt.Annotations.Core.AnnFixedStateOperations.none);lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(a.get_context(),e,d.get_opacity(),f);a.get_context().fill()}d.get_supportsStroke()&&null!=d.get_stroke()&&0<d.get_stroke().get_strokeThickness().get_value()&&(e=b.strokeFromContainerCoordinates(d.get_stroke(),d.get_fixedStateOperations()),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(a.get_context(),
e,d.get_opacity()),a.get_context().stroke());a.get_context().closePath()}this.get_clipPath()||a.get_context().restore()}}};Object.defineProperty(lt.Annotations.Rendering.AnnPolylineObjectRenderer.prototype,"useSplineMode",{get:lt.Annotations.Rendering.AnnPolylineObjectRenderer.prototype.get_useSplineMode,set:lt.Annotations.Rendering.AnnPolylineObjectRenderer.prototype.set_useSplineMode,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnPolyRulerObjectRenderer=function(){lt.Annotations.Rendering.AnnPolyRulerObjectRenderer.initializeBase(this)};
lt.Annotations.Rendering.AnnPolyRulerObjectRenderer.prototype={_annObject$2:null,drawTickMarks:function(b,c,a,d,e,f,g){var h=null,i=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine).get_context(),h=lt.Annotations.Core.RulerHelper.getTickMarks(b,c,a,lt.LeadLengthD.create(d.get_value()),e,g);if(null!=h){i.save();i.beginPath();for(c=0;c<h.length;c+=2)i.moveTo(h[c].get_x(),h[c].get_y()),i.lineTo(h[c+1].get_x(),h[c+1].get_y());i.closePath();lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStroke(i,
b.strokeFromContainerCoordinates(f,g));i.stroke();this.get_clipPath()||i.restore()}this._updateRenderedObjectBounds(h,b,this._annObject$2)},drawGauge:function(b,c,a,d,e,f){var g=null,h=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine).get_context();c.get_x()===a.get_x()&&c.get_y()===a.get_y()||(g=lt.Annotations.Core.RulerHelper.getGaugePoints(b,c,a,d,f),null!=g&&(h.save(),h.beginPath(),h.moveTo(g[0].get_x(),g[0].get_y()),h.lineTo(g[1].get_x(),g[1].get_y()),
h.moveTo(g[2].get_x(),g[2].get_y()),h.lineTo(g[3].get_x(),g[3].get_y()),h.closePath(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStroke(h,b.strokeFromContainerCoordinates(e,f)),h.stroke(),this.get_clipPath()||h.restore()),this._updateRenderedObjectBounds(g,b,this._annObject$2))},drawLengthText:function(){},get__canDrawLength:function(){return!0},_addOpacityToStroke:function(b,c){lt.Annotations.Rendering._colorUtil.addOpacityToBrush(Type.safeCast(b.get_stroke(),lt.Annotations.Core.AnnSolidColorBrush),
c)},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");this._getRotationAngle(b.get_transform());this._annObject$2=c;var a=Type.safeCast(c,lt.Annotations.Core.AnnPolyRulerObject);if(null!=a){lt.Annotations.Rendering.AnnPolyRulerObjectRenderer.callBaseMethod(this,"render",[b,c]);var d=a.get_stroke().clone();this._addOpacityToStroke(d,a.get_opacity());var e=a.get_tickMarksStroke().clone();
this._addOpacityToStroke(e,a.get_opacity());for(var f=0;f<c.get_points().get_count()-1;++f){var g=c.get_points().get_item(f),h=c.get_points().get_item(f+1);a.get_supportsStroke()&&null!=a.get_stroke()&&(a.get_showGauge()&&this.drawGauge(b,g,h,a.get_gaugeLength(),d,a.get_fixedStateOperations()),a.get_showTickMarks()&&null!=a.get_tickMarksStroke()&&this.drawTickMarks(b,g,h,a.get_tickMarksLength(),a.get_measurementUnit(),e,a.get_fixedStateOperations()))}if(Object.keyExists(a.get_labels(),"RulerLength")&&
(d=a.get_labels().RulerLength,null!=d))g=a.get_points().toArray(),f=lt.Annotations.Core.Utils.getUnitVector(g[0],g[1]),e=a.get_points().get_item(a.get_points().get_count()-1),0>f.get_x()&&(f=lt.Annotations.Core.Utils.getUnitVector(g[1],g[0]),e=a.get_points().get_item(0)),a.get_measurementUnit()===lt.Annotations.Core.AnnUnit.pixel?(g=a.getRulerLength(1),g=b.lengthFromContainerCoordinates(g,lt.Annotations.Core.AnnFixedStateOperations.scrolling|lt.Annotations.Core.AnnFixedStateOperations.zooming),d.set_text(String.format("{0} {1}",
lt.Annotations.Core.Utils.precisionFormat(a.get_precision(),g),a.get_unitsAbbreviation()[lt.Annotations.Core.AnnUnit.pixel]))):d.set_text(a.getRulerLengthAsString(b.get_calibrationScale())),g=d.get_font().clone(),f=lt.Annotations.Core.Utils.transformPoint(f,lt.LeadLengthD.create(g.get_fontHeight()),e),f.get_isEmpty()||(f=lt.LeadPointD.create(f.get_x()-e.get_x(),f.get_y()-e.get_y())),d.set_originalPosition(lt.LeadPointD.create(e.get_x()+f.get_x(),e.get_y()+f.get_y())),(d.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds)===
lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds&&d.set_restrictionRectangle(a.get_bounds()),this.drawLengthText(b,d,a.get_fixedStateOperations()),a.set_renderedObjectBounds(lt.LeadRectD.unionRects(a.get_renderedObjectBounds(),d.get_renderedLabelBounds()))}}};lt.Annotations.Rendering.AnnProtractorObjectRenderer=function(){lt.Annotations.Rendering.AnnProtractorObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnProtractorObjectRenderer._getAngle$3=function(b,c,a,d){b-=c;
a?180<Math.abs(b)&&(b=0<b?b-360:b+360):180>Math.abs(b)&&(b=0<b?b-360:b+360);return d===lt.Annotations.Core.AnnAngularUnit.radian?lt.Annotations.Core.Utils.degreesToRadian(b):b};lt.Annotations.Rendering.AnnProtractorObjectRenderer.prototype={getAngleText:function(b,c,a,d){null==d&&lt.Annotations.Core.ExceptionHelper.argumentNullException("unitsAbbreviation");return String.format("{0} {1}",lt.Annotations.Core.Utils.precisionFormat(c,b),d[a])},_getTextPart$3:function(b,c,a,d,e,f,g){var h=b.get_y()-a.get_y(),
a=b.get_x()-a.get_x(),i=b.get_y()-c.get_y(),b=b.get_x()-c.get_x(),c=1,g=lt.Annotations.Rendering.AnnProtractorObjectRenderer._getAngle$3(d,e,f,g);0>=i&&0>=b&&0>=h&&0>=a?c=2:0<=i&&0<=b&&0<=h&&0<=a?c=4:0>=i&&0<=b&&0>=h&&0<=a?c=1:0<=i&&0>=b&&0<=h&&0>=a?c=3:0<=i&&0<=b&&0>=h&&0<=a||0<=h&&0<=a&&0>=i&&0<=b?(f=d,g=e,d>e?f=360-f:g=360-g,c=0<=i&&0<=b&&0>=h&&0<=a?f<g?1:4:f<g?4:1):0<=i&&0<=b&&0>=h&&0>=a||0<=h&&0<=a&&0>=i&&0>=b?c=0<=i&&0<=b&&0>=h&&0>=a?0>g?f?3:1:f?1:3:0>g?f?1:3:f?3:1:0<=i&&0>=b&&0>=h&&0<=a||0<=
h&&0>=a&&0>=i&&0<=b?c=0<=i&&0>=b&&0>=h&&0<=a?0>g?f?2:4:f?4:2:0>g?f?4:2:f?2:4:0<=i&&0<=b&&0<=h&&0>=a||0<=h&&0<=a&&0<=i&&0>=b?(f=d,g=e,d>e?f=180-f:g=180-g,c=0<=i&&0<=b&&0<=h&&0>=a?f<g?4:3:f<g?3:4):0<=i&&0>=b&&0>=h&&0>=a||0<=h&&0>=a&&0>=i&&0>=b?(f=d,g=e,d>e?(f=270-f,g-=90):(g=270-g,f-=90),c=0<=i&&0>=b&&0>=h&&0>=a?f<g?3:2:f<g?2:3):0>=i&&0>=b&&0>=h&&0<=a||0>=h&&0>=a&&0>=i&&0<=b?(f=d,g=e,d>e?(f=360-f,g-=180):(g=360-g,f-=180),c=0>=i&&0>=b&&0>=h&&0<=a?f<g?2:1:f<g?1:2):c=1;return c},getPoint:function(b,c,
a,d,e,f,g,h,i){c=this._getTextPart$3(a,c,d,e,f,g,i);a=6;g||(a=h);return 1===c?lt.LeadPointD.create(a,-(b.get_height()+a)):2===c?lt.LeadPointD.create(-b.get_width()-a,-(b.get_height()+a)):4===c?lt.LeadPointD.create(a,a):lt.LeadPointD.create(-b.get_width()-a,a)},drawArc:function(b,c,a,d,e,f,g){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");var h=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=h&&null!=h.get_context()){h.get_context().save();
e=b.lengthFromContainerCoordinates(e,g);h.get_context().beginPath();var i=this._getRotationAngle(b.get_transform()),a=a-i,i=lt.Annotations.Core.Utils.degreesToRadian(a+d);h.get_context().arc(c.get_x(),c.get_y(),Math.abs(e),lt.Annotations.Core.Utils.degreesToRadian(a),i,0<d?!1:!0);lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStroke(h.get_context(),b.strokeFromContainerCoordinates(f,g));h.get_context().stroke();h.get_context().closePath();h.get_context().restore()}},drawAngleText:function(){},
render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");lt.Annotations.Rendering.AnnProtractorObjectRenderer.callBaseMethod(this,"render",[b,c]);var a=Type.safeCast(c,lt.Annotations.Core.AnnProtractorObject);if(null!=a){var d=a.get_points().toArray(),d=b.pointsFromContainerCoordinates(d,a.get_fixedStateOperations()),e=lt.Annotations.Core.Utils.findAngle(a.get_points().get_item(0),
a.get_points().get_item(1)),f=Number.NaN;lt.LeadPointD.equals(a.get_points().get_item(2),a.get_points().get_item(1))||(f=lt.Annotations.Core.Utils.findAngle(a.get_points().get_item(2),a.get_points().get_item(1)));var g=0,h=0,i=a.get_acute();if(0.2<Math.abs(e-f)||!i)g=e-180,h=-lt.Annotations.Rendering.AnnProtractorObjectRenderer._getAngle$3(e,f,i,lt.Annotations.Core.AnnAngularUnit.degree);if(!isNaN(f)&&a.get_supportsStroke()&&null!=a.get_stroke()){lt.Annotations.Core.Utils.distance(a.get_firstPoint(),
a.get_centerPoint());var j=a.get_stroke().clone();this._addOpacityToStroke(j,a.get_opacity());a.get_showArc()&&this.drawArc(b,d[1],g,h,a.get_arcRadius(),j,a.get_fixedStateOperations()|lt.Annotations.Core.AnnFixedStateOperations.zooming|lt.Annotations.Core.AnnFixedStateOperations.lengthValue)}Object.keyExists(a.get_labels(),"AngleText")&&(d=a.get_labels().AngleText,null!=d&&!isNaN(f)&&(g=Math.abs(lt.Annotations.Rendering.AnnProtractorObjectRenderer._getAngle$3(e,f,a.get_acute(),a.get_angularUnit())),
lt.Annotations.Core.AnnDouble.isNaN(g)||(d.set_text(this.getAngleText(g,a.get_anglePrecision(),a.get_angularUnit(),a.get_angularUnitsAbbreviation())),g=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine),h=lt.LeadSizeD.create(g.get_context().measureText(d.get_text()).width,d.get_font().get_fontSize()),e=this.getPoint(h,a.get_firstPoint(),a.get_centerPoint(),a.get_secondPoint(),e,f,i,a.get_arcRadius().get_value(),a.get_angularUnit()),d.set_originalPosition(lt.LeadPointD.create(a.get_centerPoint().get_x()+
e.get_x(),a.get_centerPoint().get_y()+e.get_y())),(d.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds)===lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds&&d.set_restrictionRectangle(a.get_bounds()),this.drawAngleText(b,d,a.get_fixedStateOperations()),a.set_renderedObjectBounds(lt.LeadRectD.unionRects(a.get_renderedObjectBounds(),d.get_renderedLabelBounds())))));if(Object.keyExists(a.get_labels(),"FirstRulerLength")&&(d=a.get_labels().FirstRulerLength,
null!=d&&(e=a.getRulerLengthFromPoints(a.get_centerPoint(),a.get_firstPoint(),b.get_calibrationScale()),!lt.Annotations.Core.AnnDouble.isNaN(e.get_value())))){a.get_measurementUnit()===lt.Annotations.Core.AnnUnit.pixel?(a.set_measurementUnit(lt.Annotations.Core.AnnUnit.unit),e=a.getRulerLengthFromPoints(a.get_centerPoint(),a.get_firstPoint(),1),a.set_measurementUnit(lt.Annotations.Core.AnnUnit.pixel),e=b.lengthFromContainerCoordinates(e,lt.Annotations.Core.AnnFixedStateOperations.scrolling|lt.Annotations.Core.AnnFixedStateOperations.zooming),
d.set_text(String.format("{0} {1}",lt.Annotations.Core.Utils.precisionFormat(a.get_precision(),e),a.get_unitsAbbreviation()[lt.Annotations.Core.AnnUnit.pixel]))):d.set_text(a.getRulerLengthAsStringFromPoints(a.get_centerPoint(),a.get_firstPoint(),b.get_calibrationScale()));i=a.get_firstPoint().clone();e=lt.LeadPointD.create(0,0);if(i.get_x()<a.get_centerPoint().get_x()||i.get_y()<a.get_centerPoint().get_y())h=d.get_font().clone(),g=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine),
g.get_context().save(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont(g.get_context(),h),h=lt.LeadSizeD.create(g.get_context().measureText(d.get_text()).width,h.get_fontHeight()),g.get_context().restore(),i.get_y()<a.get_centerPoint().get_y()&&e.set_y(e.get_y()-h.get_height()),i.get_x()<a.get_centerPoint().get_x()&&e.set_x(e.get_x()-h.get_width());d.set_originalPosition(lt.LeadPointD.create(i.get_x()+e.get_x(),i.get_y()+e.get_y()));(d.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds)===
lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds&&d.set_restrictionRectangle(a.get_bounds());this.drawLengthText(b,d,a.get_fixedStateOperations());a.set_renderedObjectBounds(lt.LeadRectD.unionRects(a.get_renderedObjectBounds(),d.get_renderedLabelBounds()))}if(Object.keyExists(a.get_labels(),"SecondRulerLength")&&!isNaN(f)&&(d=a.get_labels().SecondRulerLength,null!=d&&(e=a.getRulerLengthFromPoints(a.get_centerPoint(),a.get_secondPoint(),b.get_calibrationScale()),!lt.Annotations.Core.AnnDouble.isNaN(e.get_value())))){a.get_measurementUnit()===
lt.Annotations.Core.AnnUnit.pixel?(a.set_measurementUnit(lt.Annotations.Core.AnnUnit.unit),e=a.getRulerLengthFromPoints(a.get_centerPoint(),a.get_secondPoint(),1),a.set_measurementUnit(lt.Annotations.Core.AnnUnit.pixel),e=b.lengthFromContainerCoordinates(e,lt.Annotations.Core.AnnFixedStateOperations.scrolling|lt.Annotations.Core.AnnFixedStateOperations.zooming),d.set_text(String.format("{0} {1}",lt.Annotations.Core.Utils.precisionFormat(a.get_precision(),e),a.get_unitsAbbreviation()[lt.Annotations.Core.AnnUnit.pixel]))):
d.set_text(a.getRulerLengthAsStringFromPoints(a.get_centerPoint(),a.get_secondPoint(),b.get_calibrationScale()));i=a.get_secondPoint().clone();e=lt.LeadPointD.create(0,0);if(i.get_x()<a.get_centerPoint().get_x()||i.get_y()<a.get_centerPoint().get_y())h=d.get_font().clone(),g=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine),g.get_context().save(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont(g.get_context(),h),h=lt.LeadSizeD.create(g.get_context().measureText(d.get_text()).width,
h.get_fontHeight()),g.get_context().restore(),i.get_y()<a.get_centerPoint().get_y()&&e.set_y(e.get_y()-h.get_height()),i.get_x()<a.get_centerPoint().get_x()&&e.set_x(e.get_x()-h.get_width());d.set_originalPosition(lt.LeadPointD.create(i.get_x()+e.get_x(),i.get_y()+e.get_y()));(d.get_restrictionMode()&lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds)===lt.Annotations.Core.AnnLabelRestriction.restrictToObjectBounds&&d.set_restrictionRectangle(a.get_bounds());this.drawLengthText(b,d,a.get_fixedStateOperations());
a.set_renderedObjectBounds(lt.LeadRectD.unionRects(a.get_renderedObjectBounds(),d.get_renderedLabelBounds()))}}}};lt.Annotations.Rendering.AnnRectangleObjectRenderer=function(){lt.Annotations.Rendering.AnnRectangleObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnRectangleObjectRenderer.prototype={render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),
lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=a&&null!=a.get_context()){a.get_context().save();var d=Type.safeCast(c,lt.Annotations.Core.AnnRectangleObject);if(null!=d){var e=this.getRenderPoints(b,c),e=b.pointsFromContainerCoordinates(e,c.get_fixedStateOperations());if(1<e.length){a.get_context().beginPath();var f=d.get_stroke(),g=d.get_opacity();-1===d.get_id()&&(f=Type.safeCast(d,lt.Annotations.Core.AnnSelectionObject).get_selectionStroke(),g=Type.safeCast(d,lt.Annotations.Core.AnnSelectionObject).get_selectionOpacity());
f=b.strokeFromContainerCoordinates(f,d.get_fixedStateOperations());if(null!=f&&0<f.get_strokeThickness().get_value()&&f.get_strokeAlignment()===lt.Annotations.Core.AnnStrokeAlignment.inset){for(var h=new lt.Annotations.Core.AnnRectangleObject,i=ss.IEnumerator.getEnumerator(e);i.moveNext();){var j=i.current;h.get_points().add(j)}h=h.get_rect();i=f.get_strokeThickness().get_value()/2;h.inflate(-i,-i);i=Array(2);i[0]=lt.LeadPointD.create(e[0].get_x(),e[0].get_y());i[1]=lt.LeadPointD.create(e[2].get_x(),
e[2].get_y());i=lt.LeadPointD.create((e[0].get_x()+e[2].get_x())/2,(e[0].get_y()+e[2].get_y())/2);j=lt.LeadMatrix.get_identity();j.rotateAt(d.get_angle(),i.get_x(),i.get_y());a.get_context().transform(j.get_m11(),j.get_m12(),j.get_m21(),j.get_m22(),j.get_offsetX(),j.get_offsetY());a.get_context().rect(h.get_x(),h.get_y(),h.get_width(),h.get_height())}else{h=e[0];a.get_context().moveTo(parseInt(h.get_x()),parseInt(h.get_y()));for(i=1;i<e.length;i++)j=e[i],a.get_context().lineTo(parseInt(j.get_x()),
parseInt(j.get_y()));a.get_context().lineTo(parseInt(h.get_x()),parseInt(h.get_y()))}if(d.get_supportsFill()&&null!=d.get_fill())if(Type.canCast(d.get_fill(),lt.Annotations.Core.AnnHatchBrush)){if(this.renderHatchBrushFill(a,b,d),a.get_context().beginPath(),d.get_supportsStroke()&&null!=f){h=e[0];a.get_context().moveTo(parseInt(h.get_x()),parseInt(h.get_y()));for(i=1;i<e.length;i++)j=e[i],a.get_context().lineTo(parseInt(j.get_x()),parseInt(j.get_y()));a.get_context().lineTo(parseInt(h.get_x()),parseInt(h.get_y()))}}else e=
d.get_fill().clone(),h=b.rectFromContainerCoordinates(d.get_bounds(),lt.Annotations.Core.AnnFixedStateOperations.none),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(a.get_context(),e,d.get_opacity(),h),a.get_context().fill();a.get_context().closePath();d.get_supportsStroke()&&null!=f&&0<f.get_strokeThickness().get_value()&&(lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(a.get_context(),f,g),a.get_context().stroke())}}a.get_context().restore()}}};lt.Annotations.Rendering.AnnRubberStampObjectRenderer=
function(){lt.Annotations.Rendering.AnnRubberStampObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnRubberStampObjectRenderer.prototype={_transformCounter$2:0,render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=a&&null!=a.get_context()){var d=Type.safeCast(c,
lt.Annotations.Core.AnnRubberStampObject);if(null!=d){var e=b.get_transform().clone(),f=b.get_rotateTransform().clone();f.get_isIdentity()||b.updateTransform(b.get_transformWithoutRotate());var g=lt.LeadPointD.create(d.get_bounds().get_left()+d.get_bounds().get_width()/2,d.get_bounds().get_top()+d.get_bounds().get_height()/2),g=b.pointFromContainerCoordinates(g,d.get_fixedStateOperations()),h=this._saveContext(),i=d.get_rect(),i=b.rectFromContainerCoordinates(i,d.get_fixedStateOperations()),j=d.get_isFlipped()?
-1:1,k=d.get_isReversed()?-1:1;if(!this._transformCounter$2){f.translatePrepend(g.get_x(),g.get_y());var l=lt.Annotations.Core.Utils.isFlipedReveresd(b)*Math.abs(d.get_angle());(0>k||0>j)&&f.scalePrepend(k,j);!lt.Annotations.Core.AnnDouble.isNaN(d.get_angle())&&l&&f.rotatePrepend(l*k*j);f.translatePrepend(-g.get_x(),-g.get_y());this._multiplyTransform(f)}this._transformCounter$2++;f=a.get_resources();null!=f&&Object.keyExists(f.get_rubberStamps(),d.get_rubberStampType())&&a.drawPicture(f.get_rubberStamps()[d.get_rubberStampType()],
i,c);this._restoreContext(h);this._transformCounter$2--;b.updateTransform(e)}}}};lt.Annotations.Rendering.AnnStampObjectRenderer=function(){lt.Annotations.Rendering.AnnStampObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnStampObjectRenderer.prototype={_transformCounter$3:0,_mySizeFromContainerCoordinates$3:function(b,c){var a=1/720;return lt.LeadSizeD.create(b.get_width()*a*c.get_deviceDpiX(),b.get_height()*a*c.get_deviceDpiY())},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");
null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(c,lt.Annotations.Core.AnnStampObject);if(null!=a){var d=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=d&&null!=d.get_context()){if(a.get_drawShadow()){var e=new lt.Annotations.Rendering.AnnNoteObjectRenderer;e.initialize(d);var f=new lt.Annotations.Core.AnnNoteObject;f.set_fixedStateOperations(a.get_fixedStateOperations());for(var g=ss.IEnumerator.getEnumerator(a.get_points());g.moveNext();){var h=
g.current;f.get_points().add(h)}e.renderShadow(b,f)}if(!this._transformCounter$3){e=new lt.Annotations.Core.AnnTextObject;for(f=ss.IEnumerator.getEnumerator(a.get_points());f.moveNext();)h=f.current,e.get_points().add(h);e.set_fill(a.get_fill());e.set_fixedStateOperations(a.get_fixedStateOperations());e.set_stroke(null);e.set_text(null);lt.Annotations.Rendering.AnnStampObjectRenderer.callBaseMethod(this,"render",[b,e])}if(null!=a.get_picture()&&(!String.isNullOrEmpty(a.get_picture().get_source())||
!String.isNullOrEmpty(a.get_picture().get_pictureData()))&&null!=d&&null!=d.get_context()){h=b.get_transform().clone();g=b.get_rotateTransform().clone();g.get_isIdentity()||b.updateTransform(b.get_transformWithoutRotate());var f=lt.LeadPointD.create(a.get_bounds().get_left()+a.get_bounds().get_width()/2,a.get_bounds().get_top()+a.get_bounds().get_height()/2),f=b.pointFromContainerCoordinates(f,a.get_fixedStateOperations()),e=this._saveContext(),i=a.get_isFlipped()?-1:1,j=a.get_isReversed()?-1:1;this._transformCounter$3||
(g.translatePrepend(f.get_x(),f.get_y()),(0>j||0>i)&&g.scalePrepend(j,i),lt.Annotations.Core.AnnDouble.isNaN(a.get_angle())||g.rotatePrepend(a.get_angle()*j*i),g.translatePrepend(-f.get_x(),-f.get_y()),this._multiplyTransform(g));g=lt.LeadRectD.get_empty();a.get_pictureSizeMode()===lt.Annotations.Core.AnnSizeMode.stretch?(f=a.get_rect(),g=b.rectFromContainerCoordinates(f,a.get_fixedStateOperations())):(g=this._mySizeFromContainerCoordinates$3(lt.LeadSizeD.create(a.get_picture().get_width(),a.get_picture().get_height()),
b),g=lt.LeadRectD.create(f.get_x()-g.get_width()/2,f.get_y()-g.get_height()/2,g.get_width(),g.get_height()),this._clipContext(b.rectFromContainerCoordinates(a.get_rect(),a.get_fixedStateOperations())));this._transformCounter$3++;d.drawPicture(a.get_picture(),g,c);this._restoreContext(e);this._transformCounter$3--;b.updateTransform(h)}this._transformCounter$3||(a=Type.safeCast(a.clone(),lt.Annotations.Core.AnnStampObject),a.set_fill(null),lt.Annotations.Rendering.AnnStampObjectRenderer.callBaseMethod(this,
"render",[b,a]))}}}};lt.Annotations.Rendering.AnnStickyNoteObjectRenderer=function(){lt.Annotations.Rendering.AnnStickyNoteObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnStickyNoteObjectRenderer.prototype={render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=
a&&null!=a.get_context()){var d=Type.safeCast(c,lt.Annotations.Core.AnnStickyNoteObject);if(null!=d){var e=b.get_transform().clone(),f=b.get_rotateTransform().clone();f.get_isIdentity()||b.updateTransform(b.get_transformWithoutRotate());var g=this._saveContext();if(null==d.get_picture()){var h=this.get_renderingEngine().get_resources();null!=h&&-1<d.get_defaultPicture()&&d.get_defaultPicture()<h.get_images().length&&d.set_picture(h.get_images()[d.get_defaultPicture()])}var d=d.get_picture(),h=lt.LeadSizeD.create(0,
0),i=lt.Annotations.Core.AnnContainerMapper.createDefault();if(null!=d&&null!=d.get_internalData()){var j=d.get_internalData();null!=j&&(h.set_width(j.naturalWidth),h.set_height(j.naturalHeight));if(h.get_width()||h.get_height())h=i.sizeToContainerCoordinates(h),d.set_width(h.get_width()),d.set_height(h.get_height()),i.updateTransform(b.get_transform()),h=i.sizeFromContainerCoordinates(h)}i=c.get_points().get_item(0);j=lt.LeadRectD.create(0,0,0,0);i=b.pointFromContainerCoordinates(i,c.get_fixedStateOperations());
this._multiplyTransform(f);j=lt.LeadRectD.create(i.get_x()-h.get_width()/2,i.get_y()-h.get_height()/2,h.get_width(),h.get_height());a.drawPicture(d,j,c);this._restoreContext(g);b.updateTransform(e)}}},renderSelection:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(!(null==a||null==
a.get_context())){var d=Type.safeCast(c,lt.Annotations.Core.AnnStickyNoteObject);c.get_bounds();var e=d.get_picture(),d=lt.LeadSizeD.create(0,0);if(null!=e&&null!=e.get_internalData()&&(e=Type.safeCast(e.get_internalData(),Image),null!=e&&(d.set_width(e.naturalWidth),d.set_height(e.naturalHeight)),d.get_width()||d.get_height()))e=lt.Annotations.Core.AnnContainerMapper.createDefault(),d=e.sizeToContainerCoordinates(d),e.updateTransform(b.get_transform()),d=e.sizeFromContainerCoordinates(d);e=c.get_points().get_item(0);
e=b.pointFromContainerCoordinates(e,c.get_fixedStateOperations());d=lt.LeadRectD.create(e.get_x()-d.get_width()/2,e.get_y()-d.get_height()/2,d.get_width(),d.get_height());e=b.strokeFromContainerCoordinates(c.get_selectionStroke(),c.get_fixedStateOperations()).clone();a.get_context().beginPath();a.get_context().rect(d.get_x(),d.get_y(),d.get_width(),d.get_height());lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(a.get_context(),e,0.5);a.get_context().stroke();a.get_context().closePath();
a.get_context().beginPath();a.get_context().rect(d.get_x(),d.get_y(),d.get_width(),d.get_height());d=lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("white"),lt.LeadLengthD.create(e.get_strokeThickness().get_value()-2));lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(a.get_context(),d,0.5);a.get_context().stroke();a.get_context().closePath()}},renderContent:function(){}};lt.Annotations.Rendering.AnnTextObjectRenderer=function(){lt.Annotations.Rendering.AnnTextObjectRenderer.initializeBase(this)};
lt.Annotations.Rendering.AnnTextObjectRenderer._getTextRotation$2=function(b,c){var a=lt.LeadMatrix.get_identity();switch(b){case lt.Annotations.Core.AnnTextRotate.rotate90:a.rotateAt(-90,c.get_x(),c.get_y());break;case lt.Annotations.Core.AnnTextRotate.rotate180:a.rotateAt(-180,c.get_x(),c.get_y());break;case lt.Annotations.Core.AnnTextRotate.rotate270:a.rotateAt(90,c.get_x(),c.get_y())}return a};lt.Annotations.Rendering.AnnTextObjectRenderer._alignRect$2=function(b,c,a,d,e,f,g){var h=c.clone(),
g=g.clone();switch(a){case lt.Annotations.Core.AnnHorizontalAlignment.center:h.set_x(Math.max(c.get_left(),c.get_left()+(c.get_width()-b.get_width())/2));h.set_width(Math.max(1,h.get_width()-e.get_right()));c.get_x()+e.get_left()>=h.get_x()&&(h.set_x(c.get_x()+e.get_left()),h.set_width(Math.max(1,c.get_width()-e.get_left()-e.get_right())));break;case lt.Annotations.Core.AnnHorizontalAlignment.right:h.set_x(c.get_right()-b.get_width());h.set_x(h.get_x()-e.get_right());h.set_width(Math.max(1,h.get_width()-
e.get_right()));c.get_x()+e.get_left()>=h.get_x()&&(h.set_x(c.get_x()+e.get_left()),h.set_width(Math.max(1,c.get_width()-e.get_left()-e.get_right())));break;default:h.set_x(h.get_x()+e.get_left()),h.set_width(Math.max(1,h.get_width()-e.get_left())),h.set_width(Math.max(1,h.get_width()-e.get_right()))}switch(d){case lt.Annotations.Core.AnnVerticalAlignment.center:h.set_y((c.get_top()+c.get_bottom()-b.get_height())/2);h.get_y()<=f.get_y()+e.get_top()&&(h.set_y(f.get_y()+e.get_top()),g.set_y(g.get_y()+
e.get_top()));f.get_bottom()-e.get_bottom()<=g.get_bottom()&&(b=g.get_bottom()-(f.get_bottom()-e.get_bottom()),h.set_height(Math.max(1,h.get_height()-b)),h.get_height()||h.set_height(1));break;case lt.Annotations.Core.AnnVerticalAlignment.bottom:h.set_y(c.get_bottom()-b.get_height()-e.get_bottom());h.get_y()<=f.get_y()+e.get_top()&&(h.set_y(f.get_y()+e.get_top()),g.set_y(g.get_y()+e.get_top()));f.get_bottom()-e.get_bottom()<=g.get_bottom()&&(b=g.get_bottom()-(f.get_bottom()-e.get_bottom()),h.set_height(Math.max(1,
h.get_height()-b)),h.get_height()||h.set_height(1));break;default:h.set_y(h.get_top()+e.get_top()),g.set_y(g.get_y()+e.get_top()),f.get_bottom()-e.get_bottom()<=g.get_bottom()&&(b=g.get_bottom()-(f.get_bottom()-e.get_bottom()),h.set_height(Math.max(1,h.get_height()-b)),h.get_height()||h.set_height(1))}return h};lt.Annotations.Rendering.AnnTextObjectRenderer.prototype={_flipReverseText$2:!1,get_flipReverseText:function(){return this._flipReverseText$2},set_flipReverseText:function(b){return this._flipReverseText$2=
b},_fixedPadding$2:!0,get_fixedPadding:function(){return this._fixedPadding$2},set_fixedPadding:function(b){return this._fixedPadding$2=b},getTextSize:function(b,c){var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine),d=0;a.get_context().save();lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont(a.get_context(),c);null!=a&&null!=a.get_context()&&(null!=c&&c.get_isDirty()&&(c.set_fontHeight(parseInt(lt.Annotations.Rendering.AnnHtml5RenderingEngine.measureTextHeight(a.get_context(),
c))),c.set_isDirty(!1)),d=c.get_fontHeight());var e=a.get_context().measureText(b).width;a.get_context().restore();return lt.LeadSizeD.create(e,d)},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(c,lt.Annotations.Core.AnnTextObject);if(!(null==a||!a.get_rect().get_width()&&!a.get_rect().get_height())){lt.Annotations.Rendering.AnnTextObjectRenderer.callBaseMethod(this,
"render",[b,c]);var d=a.get_text();if(!String.isNullOrEmpty(d)){var e=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=e&&null!=e.get_context()){e.get_context().save();var f=b.get_transform().clone(),g=b.get_rotateTransform().clone();g.get_isIdentity()||b.updateTransform(b.get_transformWithoutRotate());var h=new lt.Annotations.Core.AnnRectangleObject;h.get_points().clear();for(var i=ss.IEnumerator.getEnumerator(this.getRenderPoints(b,a));i.moveNext();){var j=
i.current;h.get_points().add(j)}var k=h.get_angle(),l=lt.LeadPointD.create(h.get_bounds().get_left()+h.get_bounds().get_width()/2,h.get_bounds().get_top()+h.get_bounds().get_height()/2),l=b.pointFromContainerCoordinates(l,a.get_fixedStateOperations()),m=lt.Annotations.Rendering.AnnTextObjectRenderer._getTextRotation$2(a.get_textRotate(),l),h=h.get_rect(),h=b.rectFromContainerCoordinates(h,a.get_fixedStateOperations()),h=m.transformRect(h),i=0;if(a.get_supportsFont()&&null!=a.get_font()){var j=b.fontFromContainerCoordinates(a.get_font(),
a.get_fixedStateOperations()),n=b.strokeFromContainerCoordinates(a.get_stroke(),a.get_fixedStateOperations());null!=n&&(i=Math.max(1,n.get_strokeThickness().get_value()),n.get_strokeAlignment()===lt.Annotations.Core.AnnStrokeAlignment.inset?h.inflate(-i,-i):h.inflate(-i/2,-i/2));var n=a.get_isFlipped()?-1:1,p=a.get_isReversed()?-1:1,m=lt.LeadMatrix.multiply(m,g);m.translatePrepend(l.get_x(),l.get_y());this.get_flipReverseText()&&(0>p||0>n)&&m.scalePrepend(p,n);lt.Annotations.Core.AnnDouble.isNaN(k)||
m.rotatePrepend(k*lt.Annotations.Core.Utils.isFlipedReveresd(b));m.translatePrepend(-l.get_x(),-l.get_y());e.get_context().transform(m.get_m11(),m.get_m12(),m.get_m21(),m.get_m22(),m.get_offsetX(),m.get_offsetY());e.get_context().clip();if(!h.get_isEmpty()&&(g=a.get_padding().clone(),this._fixedPadding$2||(g.set_left(b.lengthFromContainerCoordinates(lt.LeadLengthD.create(g.get_left()),a.get_fixedStateOperations())),g.set_right(b.lengthFromContainerCoordinates(lt.LeadLengthD.create(g.get_right()),
a.get_fixedStateOperations())),g.set_top(b.lengthFromContainerCoordinates(lt.LeadLengthD.create(g.get_top()),a.get_fixedStateOperations())),g.set_bottom(b.lengthFromContainerCoordinates(lt.LeadLengthD.create(g.get_bottom()),a.get_fixedStateOperations()))),null!=a.get_textForeground()&&null!=d))if(l=d.replaceAll("\r",""),0<l.length)if(k=null,a.get_wordWrap()?(l=this.getTextSize(a.get_text(),j,h.get_size()),k=lt.Annotations.Rendering.AnnTextObjectRenderer._alignRect$2(l,h,a.get_horizontalAlignment(),
a.get_verticalAlignment(),g,h,h),k=this._wrapText$2(d,k,j,e,a,b)):k=l.split("\n"),d=k.length,Type.canCast(a,lt.Annotations.Core.AnnTextRollupObject)&&!Type.safeCast(a,lt.Annotations.Core.AnnTextRollupObject).get_expanded()&&1<d&&(k[0]+=" ...",d=1),null!=k&&0<d){for(p=n=m=0;p<d;++p)l=lt.LeadSizeD.create(0,0),l.set_width(this._getTextWidth$2(k[p],j,e)),l.set_height(1.5*j.get_fontSize()),n+=l.get_height();var q=parseInt(h.get_top());a.get_verticalAlignment()===lt.Annotations.Core.AnnVerticalAlignment.center?
q+=(h.get_height()-n)/2:a.get_verticalAlignment()===lt.Annotations.Core.AnnVerticalAlignment.bottom&&(q+=h.get_height()-n);q<h.get_top()&&(q=parseInt(h.get_top()));for(var t=0,p=0;p<d;++p){l=lt.LeadSizeD.create(0,0);l.set_width(this._getTextWidth$2(k[p],j,e));l.set_height(1.5*j.get_fontSize());var t=Math.max(t,l.get_width()),o=lt.LeadRectD.create(h.get_left(),q,h.get_width(),l.get_height()),s=lt.LeadRectD.create(h.get_left(),h.get_top()+m,l.get_width(),l.get_height()),o=lt.Annotations.Rendering.AnnTextObjectRenderer._alignRect$2(l,
o,a.get_horizontalAlignment(),a.get_verticalAlignment(),g,h,s);0<p&&o.set_y(o.get_y()+m);s=b.rectFromContainerCoordinates(a.get_bounds(),lt.Annotations.Core.AnnFixedStateOperations.none);if(null!=a.get_textBackground()){var r=a.get_textBackground().clone(),u=o.get_height();1===u&&(u=0);var v=Math.min(o.get_width(),l.get_width());1===v&&(v=0);lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(e.get_context(),r,a.get_opacity(),s);e.get_context().fillRect(o.get_left(),o.get_top(),
v,u)}j.set_isDirty(!0);lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont(e.get_context(),j);e.get_context().textBaseline="top";r=a.get_textForeground().clone();lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(e.get_context(),r,a.get_opacity(),s);this._drawText$2(e.get_context(),k[p],o);(j.get_textDecoration()&lt.Annotations.Core.AnnTextDecorations.strikethrough)===lt.Annotations.Core.AnnTextDecorations.strikethrough&&this._drawTextDecoration$2(e.get_context(),k[p],o.get_left(),
o.get_bottom(),r.get_color(),a.get_font().get_fontSize(),a.get_horizontalAlignment(),a.get_font(),lt.Annotations.Core.AnnTextDecorations.strikethrough);(j.get_textDecoration()&lt.Annotations.Core.AnnTextDecorations.underline)===lt.Annotations.Core.AnnTextDecorations.underline&&this._drawTextDecoration$2(e.get_context(),k[p],o.get_left(),o.get_bottom(),r.get_color(),a.get_font().get_fontSize(),a.get_horizontalAlignment(),a.get_font(),lt.Annotations.Core.AnnTextDecorations.underline);(j.get_textDecoration()&
lt.Annotations.Core.AnnTextDecorations.baseline)===lt.Annotations.Core.AnnTextDecorations.baseline&&this._drawTextDecoration$2(e.get_context(),k[p],o.get_left(),o.get_bottom(),r.get_color(),a.get_font().get_fontSize(),a.get_horizontalAlignment(),a.get_font(),lt.Annotations.Core.AnnTextDecorations.baseline);(j.get_textDecoration()&lt.Annotations.Core.AnnTextDecorations.overLine)===lt.Annotations.Core.AnnTextDecorations.overLine&&this._drawTextDecoration$2(e.get_context(),k[p],o.get_left(),o.get_bottom(),
r.get_color(),a.get_font().get_fontSize(),a.get_horizontalAlignment(),a.get_font(),lt.Annotations.Core.AnnTextDecorations.overLine);m+=l.get_height()}a.get_font().set_isDirty(j.get_isDirty());a.set_textSize(b.sizeToContainerCoordinates(lt.LeadSizeD.create(t+i+2,n+i)))}}e.get_context().restore();b.updateTransform(f)}}}},_drawText$2:function(b,c,a){String.isNullOrEmpty(c)||(b.save(),b.beginPath(),b.rect(a.get_x(),a.get_y(),a.get_width(),a.get_height()),b.clip(),b.fillText(c,a.get_left(),a.get_top()),
b.closePath(),b.restore())},_drawTextDecoration$2:function(b,c,a,d,e,f,g,h,i){h=parseInt(lt.Annotations.Rendering.AnnHtml5RenderingEngine.measureTextHeight(b,h));c=b.measureText(c).width;g=0;i===lt.Annotations.Core.AnnTextDecorations.strikethrough?g=d-h/2:i===lt.Annotations.Core.AnnTextDecorations.underline?g=d-1:i===lt.Annotations.Core.AnnTextDecorations.baseline?g=d-h/4:i===lt.Annotations.Core.AnnTextDecorations.overLine&&(g=d-f/15-h);d=0;i=g;f/=15;1>f&&(f=1);b.beginPath();d=a+c;b.strokeStyle=e;
b.lineWidth=f;b.moveTo(a,g);b.lineTo(d,i);b.stroke()},_getTextWidth$2:function(b,c,a){var a=Type.safeCast(a,lt.Annotations.Rendering.AnnHtml5RenderingEngine),d=0;null!=a&&(a.get_context().save(),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFont(a.get_context(),c),d=parseInt(a.get_context().measureText(b).width),a.get_context().restore());return d},_wrapLines$2:function(b,c,a,d,e,f,g){var h=c.get_width(),b=b.split(" "),i=b.length;if(!(0>=i)){for(var j=b.clone(),k=0;k<j.length;k++)j[k]+=" ";
var l=Array(i);l[0]=this._myGetTextWidth$2(b[0],c,f,d,g);for(k=1;k<i;++k)l[k]=l[k-1]+this._myGetTextWidth$2(j[k],c,f,d,g);k=0;for(j="";k<i&&h>=l[k];)j+=b[k]+" ",k++;if(String.isNullOrEmpty(j)){this._wrapWords$2(b[k],c,a,d,e,f,g);h="";for(k+=1;k<i;++k)h+=b[k]+" ";h&&(h=h.remove(h.length-1));this._wrapLines$2(h,c,a,d,e,f,g)}else if(e.add(j),k<i){for(h="";k<i;++k)h+=b[k]+" ";h=h.remove(h.length-1);this._wrapLines$2(h,c,a,d,e,f,g)}}},_wrapWords$2:function(b,c,a,d,e,f,g){for(var h=1,i=b.length,j="",k=
0;k<i;++k){j=b.substring(0,h);h++;var l=this._myGetTextWidth$2(j,c,f,d,g),m=j.length-1;if(l>c.get_width()+0.5&&0<m){j=j.substring(0,j.length-1);break}}String.isNullOrEmpty(j)||(e.add(j),h=j.length,i!==h&&this._wrapWords$2(b.substring(h,i),c,a,d,e,f,g))},_myGetTextWidth$2:function(b,c,a,d,e){if(String.isNullOrEmpty(b))return 0;var f=new lt.Annotations.Core.AnnContainerMapper(e.get_sourceDpiX(),e.get_sourceDpiY(),e.get_sourceDpiX(),e.get_sourceDpiY());f.updateTransform(lt.LeadMatrix.get_identity());
f.set_fontRelativeToImageDpi(e.get_fontRelativeToImageDpi());f=f.fontFromContainerCoordinates(a.get_font(),a.get_fixedStateOperations());b=this._getTextWidth$2(b,f,d);c=lt.LeadRectD.create(c.get_x(),c.get_y(),b,c.get_height());b=e.get_transform().clone();d=(a.get_fixedStateOperations()&lt.Annotations.Core.AnnFixedStateOperations.fontSize)===lt.Annotations.Core.AnnFixedStateOperations.fontSize;if((a.get_fixedStateOperations()&lt.Annotations.Core.AnnFixedStateOperations.zooming)===lt.Annotations.Core.AnnFixedStateOperations.zooming||
d)b=lt.LeadMatrix.get_identity();d?b.scale(e.get_burnScaleFactor(),e.get_burnScaleFactor()):b.scale(e.get_targetDpiX()/e.get_sourceDpiX(),e.get_targetDpiY()/e.get_sourceDpiY());a=b.transformRect(c);e=Math.abs(this._getRotationAngle(e.get_transform()));return 90===e||270===e?a.get_height():a.get_width()},_wrapText$2:function(b,c,a,d,e,f){for(var g=[],h=b.split("\n"),i=[],b=0;b<h.length;++b)i.add(h[b]);h=i.length;for(b=0;b<h;++b){var j=i[b],k=this._myGetTextWidth$2(j,c,e,d,f),k=lt.Annotations.Core.Utils.precisionFormat(2,
k),l=lt.Annotations.Core.Utils.precisionFormat(2,c.get_width());c.set_width(parseFloat(l));k=parseFloat(k);k<=c.get_width()?g.add(j):(k=[],this._wrapLines$2(j,c,a,d,k,e,f)," "===k[k.length-1]&&k.removeAt(k.length-1),g.addRange(k))}c=g.length;a=null;if(0<c){a=Array(c);for(b=0;b<c;++b)a[b]=g[b]}return a}};Object.defineProperty(lt.Annotations.Rendering.AnnTextObjectRenderer.prototype,"flipReverseText",{get:lt.Annotations.Rendering.AnnTextObjectRenderer.prototype.get_flipReverseText,set:lt.Annotations.Rendering.AnnTextObjectRenderer.prototype.set_flipReverseText,
enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnTextObjectRenderer.prototype,"fixedPadding",{get:lt.Annotations.Rendering.AnnTextObjectRenderer.prototype.get_fixedPadding,set:lt.Annotations.Rendering.AnnTextObjectRenderer.prototype.set_fixedPadding,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnTextPointerObjectRenderer=function(){lt.Annotations.Rendering.AnnTextPointerObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnTextPointerObjectRenderer.prototype=
{render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");lt.Annotations.Rendering.AnnTextPointerObjectRenderer.callBaseMethod(this,"render",[b,c]);var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=a&&null!=a.get_context()){a.get_context().save();var d=Type.safeCast(c,lt.Annotations.Core.AnnTextPointerObject);if(null!=d){var e=
d.get_pointerPosition(),f=c.get_points(),g=d.getPointerPoints();d.get_showArrow()?3<f.get_count()&&!lt.LeadPointD.equals(f.get_item(0),e)&&!lt.LeadPointD.equals(f.get_item(2),e)&&(e=Type.safeCast(a.get_renderers()[lt.Annotations.Core.AnnObject.pointerObjectId],lt.Annotations.Rendering.AnnPointerObjectRenderer),e.initialize(a),null!=e&&(f=new lt.Annotations.Core.AnnPointerObject,f.get_points().add(g[1]),f.get_points().add(g[0]),f.set_stroke(d.get_stroke().clone()),f.set_pointerPosition(lt.Annotations.Core.AnnPointerPosition.end),
e.render(b,f))):(g=b.pointsFromContainerCoordinates(g,d.get_fixedStateOperations()),a.get_context().beginPath(),a.get_context().moveTo(g[0].get_x(),g[0].get_y()),a.get_context().lineTo(g[1].get_x(),g[1].get_y()),a.get_context().closePath(),d.get_supportsStroke()&&null!=d.get_stroke()&&(g=b.strokeFromContainerCoordinates(d.get_stroke(),d.get_fixedStateOperations()),lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(a.get_context(),g,d.get_opacity()),a.get_context().stroke()))}a.get_context().restore()}}};
lt.Annotations.Rendering.AnnTextRedactionObjectRenderer=function(){lt.Annotations.Rendering.AnnTextRedactionObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnTextRedactionObjectRenderer.prototype={renderShape:function(b,c,a,d){if(!d.get_isEmpty()&&!(null==c||null==b||null==a)){var e=Type.safeCast(c,lt.Annotations.Core.AnnTextRedactionObject);null!=e&&(a.beginPath(),a.rect(d.get_x(),d.get_y(),d.get_width(),d.get_height()),b=b.rectFromContainerCoordinates(c.get_bounds(),lt.Annotations.Core.AnnFixedStateOperations.none),
lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(a,e.get_fill().clone(),e.get_opacity(),b),a.fill(),a.closePath())}}};lt.Annotations.Rendering.AnnTextReviewObjectRenderer=function(){lt.Annotations.Rendering.AnnTextReviewObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnTextReviewObjectRenderer._getRectangles$1=function(b,c){var a=Type.safeCast(c,lt.Annotations.Core.AnnTextReviewObject),d=a.get_points().toArray();if(!d.length)return null;for(var d=b.pointsFromContainerCoordinates(d,
a.get_fixedStateOperations()),a=d.length/2,e=Array(a),f=0,g=0;g<a;g++){var h=2*g,i=d[h+0],h=d[h+1];e[f]=lt.LeadRectD.fromLTRB(i.get_x()-1,i.get_y()-1,h.get_x()+1,h.get_y()+1);f++}return e};lt.Annotations.Rendering.AnnTextReviewObjectRenderer.prototype={render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);
if(!(null==a||null==a.get_context())){var d=lt.Annotations.Rendering.AnnTextReviewObjectRenderer._getRectangles$1(b,c);if(null!=d)for(d=ss.IEnumerator.getEnumerator(d);d.moveNext();){var e=d.current;this.renderShape(b,Type.safeCast(c,lt.Annotations.Core.AnnTextReviewObject),a.get_context(),e)}}},renderSelection:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),
lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(!(null==a||null==a.get_context())){var d=Type.safeCast(c,lt.Annotations.Core.AnnTextReviewObject),e=lt.Annotations.Rendering.AnnTextReviewObjectRenderer._getRectangles$1(b,c);if(null!=e){a.get_context();d=b.strokeFromContainerCoordinates(d.get_selectionStroke(),d.get_fixedStateOperations()).clone();for(e=ss.IEnumerator.getEnumerator(e);e.moveNext();){var f=e.current;a.get_context().beginPath();a.get_context().rect(f.get_x(),f.get_y(),f.get_width(),
f.get_height());lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(a.get_context(),d,0.5);a.get_context().stroke();a.get_context().closePath();a.get_context().beginPath();a.get_context().rect(f.get_x(),f.get_y(),f.get_width(),f.get_height());f=lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("white"),lt.LeadLengthD.create(d.get_strokeThickness().get_value()-2));lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStrokeWithOpacity(a.get_context(),
f,0.5);a.get_context().stroke();a.get_context().closePath()}}}},renderContent:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=a&&null!=a.get_context()){var d=c.get_bounds(),e=this.get_renderingEngine().get_resources();if(null!=e){var f=e.get_images()[c.get_contentPicture()],
d=lt.LeadRectD.create(d.get_x(),d.get_y(),120,120),d=b.rectFromContainerCoordinates(d,c.get_fixedStateOperations());null!=f?a.drawPicture(f,d,c):(f=c.get_contentPicture(),-1<f&&f<e.get_images().length&&a.drawPicture(e.get_images()[f],d,c))}}}};lt.Annotations.Rendering.AnnTextHiliteObjectRenderer=function(){lt.Annotations.Rendering.AnnTextHiliteObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnTextHiliteObjectRenderer.prototype={_drawRoundEdges$2:function(b,c,a){lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawEllipse(b,
lt.LeadRectD.create(c.get_x()+c.get_width()-a,c.get_y(),2*a,c.get_height()));lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawEllipse(b,lt.LeadRectD.create(c.get_x()-a,c.get_y(),2*a,c.get_height()))},renderShape:function(b,c,a,d){if(!d.get_isEmpty()&&!(null==c||null==b||null==a)){var e=Type.safeCast(c,lt.Annotations.Core.AnnTextHiliteObject);if(null!=e){var f=this._saveContext(),g=b.rectToContainerCoordinates(d),d=b.get_transform().clone(),h=b.get_rotateTransform().clone();h.get_isIdentity()||
b.updateTransform(b.get_transformWithoutRotate());g=b.rectFromContainerCoordinates(g,lt.Annotations.Core.AnnFixedStateOperations.none);h.get_isIdentity()||this._multiplyTransform(h);a.beginPath();a.rect(g.get_x(),g.get_y(),g.get_width(),g.get_height());g=b.rectFromContainerCoordinates(e.get_bounds(),lt.Annotations.Core.AnnFixedStateOperations.none);h=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);Type.canCast(c.get_fill(),lt.Annotations.Core.AnnHatchBrush)?
this.renderHatchBrushFill(h,b,e):(lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(a,e.get_fill().clone(),e.get_opacity(),g),a.fill());a.closePath();b.updateTransform(d);this._restoreContext(f)}}}};lt.Annotations.Rendering.AnnTextRollupObjectRenderer=function(){lt.Annotations.Rendering.AnnTextRollupObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnTextRollupObjectRenderer.prototype={getRenderPoints:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");
null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=lt.Annotations.Rendering.AnnTextRollupObjectRenderer.callBaseMethod(this,"getRenderPoints",[b,c]),d=Type.safeCast(c,lt.Annotations.Core.AnnTextRollupObject);if(null!=d&&!d.get_expanded()){var e=lt.Annotations.Core.Utils.distance(a[1],a[2]);null!=d.get_font()&&(e=b.fontFromContainerCoordinates(d.get_font(),d.get_fixedStateOperations()),d=lt.Annotations.Rendering.AnnHtml5RenderingEngine.getTextSize("gM",e,d.get_bounds().get_size()),
d=b.sizeToContainerCoordinates(d),e=d.get_height());d=lt.Annotations.Core.Utils.getUnitVector(a[1],a[2]);a[2]=lt.Annotations.Core.Utils.transformPoint(d,lt.LeadLengthD.create(e),a[1]);d=lt.Annotations.Core.Utils.getUnitVector(a[0],a[3]);a[3]=lt.Annotations.Core.Utils.transformPoint(d,lt.LeadLengthD.create(e),a[0])}return a}};lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer=function(){lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer.prototype=
{_thickness$2:0.15,get_thickness:function(){return this._thickness$2},set_thickness:function(b){if(0>b||1<b)throw new lt.ArgumentOutOfRangeException("Thickness",b,"Must be a value between 0 and 1");return this._thickness$2=b},_position$2:0.5,get_position:function(){return this._position$2},set_position:function(b){if(0>b||1<b)throw new lt.ArgumentOutOfRangeException("Position",b,"Must be a value between 0 and 1");return this._position$2=b},renderShape:function(b,c,a,d){if(!d.get_isEmpty()&&!(null==
c||null==b||null==a)){var e=Type.safeCast(c,lt.Annotations.Core.AnnTextStrikeoutObject);if(null!=e){var f=lt.LeadRectD.get_empty(),f=this._getRotationAngle(b.get_transform());if(90===Math.abs(f))var f=this.get_thickness()?Math.max(d.get_width()*this.get_thickness(),1):1,g=d.get_width()*this.get_position()-f/2+d.get_x(),f=lt.LeadRectD.create(g,d.get_y(),f,d.get_height());else f=this.get_thickness()?Math.max(d.get_height()*this.get_thickness(),1):1,g=d.get_height()*this.get_position()-f/2+d.get_top(),
f=lt.LeadRectD.create(d.get_x(),g,d.get_width(),f);a.beginPath();a.rect(f.get_x(),f.get_y(),f.get_width(),f.get_height());d=b.rectFromContainerCoordinates(e.get_bounds(),lt.Annotations.Core.AnnFixedStateOperations.none);f=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);Type.canCast(c.get_fill(),lt.Annotations.Core.AnnHatchBrush)?this.renderHatchBrushFill(f,b,e):(lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(a,e.get_fill().clone(),
e.get_opacity(),d),a.fill());a.closePath()}}}};Object.defineProperty(lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer.prototype,"thickness",{get:lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer.prototype.get_thickness,set:lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer.prototype.set_thickness,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer.prototype,"position",{get:lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer.prototype.get_position,
set:lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer.prototype.set_position,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer=function(){lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer.prototype={_thickness$2:0.15,get_thickness:function(){return this._thickness$2},set_thickness:function(b){if(0>b||1<b)throw new lt.ArgumentOutOfRangeException("Thickness",b,"Must be a value between 0 and 1");
return this._thickness$2=b},_position$2:0.85,get_position:function(){return this._position$2},set_position:function(b){if(0>b||1<b)throw new lt.ArgumentOutOfRangeException("Position",b,"Must be a value between 0 and 1");return this._position$2=b},renderShape:function(b,c,a,d){if(!d.get_isEmpty()&&!(null==c||null==b||null==a)){var e=Type.safeCast(c,lt.Annotations.Core.AnnTextUnderlineObject);if(null!=e){var f=lt.LeadRectD.get_empty(),f=this._getRotationAngle(b.get_transform());if(90===Math.abs(f)){var g;
g=this.get_thickness()?Math.max(d.get_width()*this.get_thickness(),1):1;var h=0,h=-90===f?d.get_right()-(d.get_width()*this.get_position()+g/2):d.get_width()*this.get_position()-g/2+d.get_x(),f=lt.LeadRectD.create(h,d.get_y(),g,d.get_height())}else g=this.get_thickness()?Math.max(d.get_height()*this.get_thickness(),1):1,h=0,h=180===f?d.get_bottom()-(d.get_height()*this.get_position()+g/2):d.get_height()*this.get_position()-g/2+d.get_top(),f=lt.LeadRectD.create(d.get_x(),h,d.get_width(),g);a.beginPath();
a.rect(f.get_x(),f.get_y(),f.get_width(),f.get_height());d=b.rectFromContainerCoordinates(e.get_bounds(),lt.Annotations.Core.AnnFixedStateOperations.none);f=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);Type.canCast(c.get_fill(),lt.Annotations.Core.AnnHatchBrush)?this.renderHatchBrushFill(f,b,e):(lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFillOpacityAndBounds(a,e.get_fill().clone(),e.get_opacity(),d),a.fill());a.closePath()}}}};Object.defineProperty(lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer.prototype,
"thickness",{get:lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer.prototype.get_thickness,set:lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer.prototype.set_thickness,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer.prototype,"position",{get:lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer.prototype.get_position,set:lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer.prototype.set_position,enumerable:!0,configurable:!0});
lt.Annotations.Rendering.AnnThumbStyle=function(){this._size=lt.LeadSizeD.create(0,0);this._size=lt.LeadSizeD.create(0,0);this._fill=lt.Annotations.Core.AnnSolidColorBrush.create("white");this._stroke=lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("black"),lt.LeadLengthD.create(1));this._isVisible=!0};lt.Annotations.Rendering.AnnThumbStyle.prototype={_renderer:null,get_renderer:function(){return this._renderer},set_renderer:function(b){return this._renderer=b},
clone:function(){var b=this.create();b._size=this._size.clone();b._fill=null!=this._fill?this._fill.clone():null;b._stroke=null!=this._stroke?this._stroke.clone():null;b._isVisible=this._isVisible;return b},get_size:function(){return this._size.clone()},set_size:function(b){null==b&&(b=lt.LeadSizeD.create(0,0));this._size=b.clone();return b},_fill:null,get_fill:function(){return this._fill},set_fill:function(b){return this._fill=b},_stroke:null,get_stroke:function(){return this._stroke},set_stroke:function(b){return this._stroke=
b},_isVisible:!1,get_isVisible:function(){return this._isVisible},set_isVisible:function(b){return this._isVisible=b},_mySizeFromContainerCoordinates:function(b,c){var a=1/720;return lt.LeadSizeD.create(b.get_width()*a*c.get_deviceDpiX(),b.get_height()*a*c.get_deviceDpiY())},renderHitTest:function(b,c,a,d){if(!this.get_isVisible())return!1;var e=lt.LeadRectD.get_empty(),c=d.pointFromContainerCoordinates(c,lt.Annotations.Core.AnnFixedStateOperations.none),b=d.pointFromContainerCoordinates(b,lt.Annotations.Core.AnnFixedStateOperations.none),
e=this._mySizeFromContainerCoordinates(this.get_size(),d),e=lt.LeadRectD.create(b.get_x()-e.get_width()/2,b.get_y()-e.get_height()/2,e.get_width(),e.get_height()),a=d.lengthFromContainerCoordinates(lt.LeadLengthD.create(a),lt.Annotations.Core.AnnFixedStateOperations.none);e.inflate(a,a);return e.containsPoint(c)},hitTest:function(b,c,a){if(!this.get_isVisible())return!1;var d=lt.LeadRectD.get_empty();null!=this._renderer?(d=this._renderer.get_renderingEngine().get_container().get_mapper(),c=d.pointFromContainerCoordinates(c,
lt.Annotations.Core.AnnFixedStateOperations.none),b=d.pointFromContainerCoordinates(b,lt.Annotations.Core.AnnFixedStateOperations.none),d=this._mySizeFromContainerCoordinates(this.get_size(),d),d=lt.LeadRectD.create(b.get_x()-d.get_width()/2,b.get_y()-d.get_height()/2,d.get_width(),d.get_height())):d=lt.LeadRectD.create(b.get_x()-this.get_size().get_width()/2,b.get_y()-this.get_size().get_height()/2,this.get_size().get_width(),this.get_size().get_height());d.inflate(a,a);return d.containsPoint(c)},
render:function(b,c,a,d){null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("renderer");if(this.get_isVisible()&&b.get_renderingEngine().get_stateless()){b=Type.safeCast(b.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=b&&null!=b.get_context()&&(b.get_context().save(),b.get_context().beginPath(),a=c.pointFromContainerCoordinates(a,d),c=this._mySizeFromContainerCoordinates(this.get_size(),
c),c=lt.LeadRectD.create(a.get_x()-c.get_width()/2,a.get_y()-c.get_height()/2,c.get_width(),c.get_height()),this.addPath(b.get_context(),c),b.get_context().closePath(),null!=this.get_fill()&&(lt.Annotations.Rendering.AnnHtml5RenderingEngine.setFill(b.get_context(),this.get_fill()),b.get_context().fill()),null!=this.get_stroke()))lt.Annotations.Rendering.AnnHtml5RenderingEngine.setStroke(b.get_context(),this.get_stroke()),b.get_context().stroke();b.get_context().restore()}}};Object.defineProperty(lt.Annotations.Rendering.AnnThumbStyle.prototype,
"renderer",{get:lt.Annotations.Rendering.AnnThumbStyle.prototype.get_renderer,set:lt.Annotations.Rendering.AnnThumbStyle.prototype.set_renderer,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnThumbStyle.prototype,"size",{get:lt.Annotations.Rendering.AnnThumbStyle.prototype.get_size,set:lt.Annotations.Rendering.AnnThumbStyle.prototype.set_size,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnThumbStyle.prototype,"fill",{get:lt.Annotations.Rendering.AnnThumbStyle.prototype.get_fill,
set:lt.Annotations.Rendering.AnnThumbStyle.prototype.set_fill,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnThumbStyle.prototype,"stroke",{get:lt.Annotations.Rendering.AnnThumbStyle.prototype.get_stroke,set:lt.Annotations.Rendering.AnnThumbStyle.prototype.set_stroke,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnThumbStyle.prototype,"isVisible",{get:lt.Annotations.Rendering.AnnThumbStyle.prototype.get_isVisible,set:lt.Annotations.Rendering.AnnThumbStyle.prototype.set_isVisible,
enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnRectangleThumbStyle=function(){lt.Annotations.Rendering.AnnRectangleThumbStyle.initializeBase(this)};lt.Annotations.Rendering.AnnRectangleThumbStyle.prototype={create:function(){return new lt.Annotations.Rendering.AnnRectangleThumbStyle},addPath:function(b,c){null!=b&&b.rect(c.get_x(),c.get_y(),c.get_width(),c.get_height())}};lt.Annotations.Rendering.AnnEllipseThumbStyle=function(){lt.Annotations.Rendering.AnnEllipseThumbStyle.initializeBase(this)};
lt.Annotations.Rendering.AnnEllipseThumbStyle.prototype={create:function(){return new lt.Annotations.Rendering.AnnEllipseThumbStyle},addPath:function(b,c){null!=b&&lt.Annotations.Rendering.AnnHtml5RenderingEngine.drawEllipse(b,c)}};lt.Annotations.Rendering.AnnEncryptObjectRenderer=function(){lt.Annotations.Rendering.AnnEncryptObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnEncryptObjectRenderer.prototype={render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");
null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnHtml5RenderingEngine);if(null!=a&&null!=a.get_context()&&a.get_container().get_userMode()!==lt.Annotations.Core.AnnUserMode.run){var d=Type.safeCast(c,lt.Annotations.Core.AnnEncryptObject);if(null!=d){var e=b.get_transform().clone(),f=b.get_rotateTransform().clone();f.get_isIdentity()||b.updateTransform(b.get_transformWithoutRotate());var g=lt.LeadPointD.create(d.get_bounds().get_left()+
d.get_bounds().get_width()/2,d.get_bounds().get_top()+d.get_bounds().get_height()/2),g=b.pointFromContainerCoordinates(g,d.get_fixedStateOperations()),h=this._saveContext(),i=d.get_rect(),i=b.rectFromContainerCoordinates(i,d.get_fixedStateOperations());f.translatePrepend(g.get_x(),g.get_y());var j=lt.Annotations.Core.Utils.isFlipedReveresd(b)*Math.abs(d.get_angle()),k=d.get_isFlipped()?-1:1,l=d.get_isReversed()?-1:1;(0>l||0>k)&&f.scalePrepend(l,k);!lt.Annotations.Core.AnnDouble.isNaN(d.get_angle())&&
j&&f.rotatePrepend(j*l*k);f.translatePrepend(-g.get_x(),-g.get_y());this._multiplyTransform(f);d.get_encryptor()?null!=d.get_primaryPicture()?a.drawPicture(d.get_primaryPicture(),i,c):(f=this.get_renderingEngine().get_resources(),null!=f&&-1<d.get_defaultPrimaryPicture()&&d.get_defaultPrimaryPicture()<f.get_images().length&&a.drawPicture(f.get_images()[d.get_defaultPrimaryPicture()],i,c)):null!=d.get_secondaryPicture()?a.drawPicture(d.get_secondaryPicture(),i,c):(f=this.get_renderingEngine().get_resources(),
null!=f&&-1<d.get_defaultSecondaryPicture()&&d.get_defaultSecondaryPicture()<f.get_images().length&&a.drawPicture(f.get_images()[d.get_defaultSecondaryPicture()],i,c));this._restoreContext(h);b.updateTransform(e)}}}};lt.Annotations.Rendering.AnnMediaObjectRenderer=function(){lt.Annotations.Rendering.AnnMediaObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnMediaObjectRenderer.prototype={get_showAtRunMode:function(){return!0}};Object.defineProperty(lt.Annotations.Rendering.AnnMediaObjectRenderer.prototype,
"showAtRunMode",{get:lt.Annotations.Rendering.AnnMediaObjectRenderer.prototype.get_showAtRunMode,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer.callBaseMethod(this,"createObject",[b]);var c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId()),
a=lt.Annotations.Rendering._annSvgHelpers.createPolyline();a.id=lt.Annotations.Rendering._annSvgHelpers.getSecondPolylineId(b.get_stateId());lt.Annotations.Rendering._annSvgHelpers.appendElement(c,a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&lt.Annotations.Rendering._annSvgHelpers.removeElement(a,lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getSecondPolylineId(c)))}lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer.callBaseMethod(this,
"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(c,lt.Annotations.Core.AnnCrossProductObject);if(null!=a){var d,e=new lt.Annotations.Core.AnnPolyRulerObject;e.set_stateId(c.get_stateId());var f=lt.Annotations.Rendering._annSvgHelpers.getElementById(this.get_tickmarksId());e.set_showGauge(a.get_showGauge());e.set_showTickMarks(a.get_showTickMarks());
e.set_tickMarksStroke(a.get_tickMarksStroke());e.set_stroke(a.get_stroke());e.set_measurementUnit(a.get_measurementUnit());e.get_unitsAbbreviation()[e.get_measurementUnit()]=a.get_unitsAbbreviation()[e.get_measurementUnit()];d=a.get_labels().RulerLength;e.get_labels().RulerLength=d;var g=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getLabelsId(c.get_stateId())),h=lt.Annotations.Rendering._annSvgHelpers.createText();String.isNullOrEmpty(d.get_stateId())&&
d.set_stateId(String.format("{0}.Label.{1}",c.get_stateId(),"RulerLength"));h.id=d.get_stateId();g.appendChild(h);e.set_fixedStateOperations(c.get_fixedStateOperations());e.set_gaugeLength(a.get_gaugeLength());e.set_tickMarksLength(a.get_tickMarksLength());e.get_points().clear();e.get_points().add(a.get_firstStartPoint());e.get_points().add(a.get_firstEndPoint());lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer.callBaseMethod(this,"setActivePolyLineID",[lt.Annotations.Rendering._annSvgHelpers.getPolylineId(c.get_stateId())]);
lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer.callBaseMethod(this,"render",[b,e]);var i=new lt.Annotations.Core.AnnPolyRulerObject,j=f.getAttribute("d");i.set_stateId(c.get_stateId());i.set_showGauge(a.get_showGauge());i.set_showTickMarks(a.get_showTickMarks());i.set_tickMarksStroke(a.get_tickMarksStroke());i.set_stroke(a.get_stroke());i.set_measurementUnit(a.get_measurementUnit());i.get_unitsAbbreviation()[e.get_measurementUnit()]=a.get_unitsAbbreviation()[e.get_measurementUnit()];i.set_precision(a.get_precision());
i.set_fixedStateOperations(c.get_fixedStateOperations());i.set_gaugeLength(a.get_gaugeLength());i.set_tickMarksLength(a.get_tickMarksLength());i.set_internalThumbLocations(c.get_internalThumbLocations());i.set_isSelected(c.get_isSelected());i.set_internalRotateCenterLocation(c.get_internalRotateCenterLocation());i.set_internalRotateGripperLocation(c.get_internalRotateGripperLocation());d=a.get_labels().SecondaryRulerLength;i.get_labels().RulerLength=d;h=lt.Annotations.Rendering._annSvgHelpers.createText();
String.isNullOrEmpty(d.get_stateId())&&d.set_stateId(String.format("{0}.Label.{1}",c.get_stateId(),"SecondaryRulerLength"));h.id=d.get_stateId();g.appendChild(h);i.get_points().clear();i.get_points().add(a.get_secondStartPoint());i.get_points().add(a.get_secondEndPoint());lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer.callBaseMethod(this,"setActivePolyLineID",[lt.Annotations.Rendering._annSvgHelpers.getSecondPolylineId(c.get_stateId())]);lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer.callBaseMethod(this,
"render",[b,i]);a=f.getAttribute("d");f.setAttribute("d",j+a)}}};lt.Annotations.Rendering.AnnSvgCurveObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgCurveObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgCurveObjectRenderer.prototype={render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");lt.Annotations.Rendering.AnnSvgCurveObjectRenderer.callBaseMethod(this,
"render",[b,c]);var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a&&!a.get_stateless()&&(a=Type.safeCast(c,lt.Annotations.Core.AnnCurveObject),null!=a)){var d=b.pointsFromContainerCoordinates(c.get_points().toArray(),a.get_fixedStateOperations()),e=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getPolylineId(c.get_stateId()));2<d.length?lt.Annotations.Rendering._svgDrawingHelper.drawCurve(e,d,a.get_tension(),
a.get_isClosed()):2===d.length&&lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(e,d,!1);lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(e,a.get_stroke(),a.get_supportsStroke());lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(e,a.get_fill(),a.get_supportsFill())}}};lt.Annotations.Rendering.AnnSvgEllipseObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgEllipseObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgEllipseObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgEllipseObjectRenderer.callBaseMethod(this,
"createObject",[b]);var c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId()),a=lt.Annotations.Rendering._annSvgHelpers.createEllipse();a.id=lt.Annotations.Rendering._annSvgHelpers.getEllipseId(b.get_stateId());lt.Annotations.Rendering._annSvgHelpers.appendElement(c,a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getEllipseId(c)))}lt.Annotations.Rendering.AnnSvgEllipseObjectRenderer.callBaseMethod(this,
"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");if(!this.get_renderingEngine().get_stateless()){lt.Annotations.Rendering.AnnSvgEllipseObjectRenderer.callBaseMethod(this,"render",[b,c]);var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getEllipseId(c.get_stateId()));null!=a&&lt.Annotations.Rendering._svgDrawingHelper.drawEllipse(a,
b,c)}}};lt.Annotations.Rendering.AnnSvgEncryptObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgEncryptObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgEncryptObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgEncryptObjectRenderer.callBaseMethod(this,"createObject",[b]);var c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId()),a=lt.Annotations.Rendering._annSvgHelpers.createImage();a.id=lt.Annotations.Rendering._annSvgHelpers.getImageId(b.get_stateId());
lt.Annotations.Rendering._annSvgHelpers.appendElement(c,a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(c)))}lt.Annotations.Rendering.AnnSvgEncryptObjectRenderer.callBaseMethod(this,"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");
null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a&&!a.get_stateless()){a=Type.safeCast(c,lt.Annotations.Core.AnnEncryptObject);if(null!=a){var d=lt.LeadPointD.create(a.get_bounds().get_left()+a.get_bounds().get_width()/2,a.get_bounds().get_top()+a.get_bounds().get_height()/2),d=b.pointFromContainerCoordinates(d,a.get_fixedStateOperations()),e=new lt.LeadMatrix,f=
lt.LeadPointD.create(parseInt(d.get_x()),parseInt(d.get_y())),d=b.rectFromContainerCoordinates(a.get_rect(),a.get_fixedStateOperations());lt.Annotations.Core.AnnDouble.isNaN(a.get_angle())||e.rotateAt(a.get_angle(),f.get_x(),f.get_y());var f=c.get_stateId(),g=lt.Annotations.Rendering._annSvgHelpers.getElementById(f);lt.Annotations.Rendering._annSvgHelpers.setTransformAttribute(g,e);e=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(f));a.get_encryptor()?
null!=a.get_primaryPicture()?lt.Annotations.Rendering._svgDrawingHelper.drawPicture(e,a.get_primaryPicture(),d,c,b):(f=this.get_renderingEngine().get_resources(),null!=f&&-1<a.get_defaultPrimaryPicture()&&a.get_defaultPrimaryPicture()<f.get_images().length&&lt.Annotations.Rendering._svgDrawingHelper.drawPicture(e,f.get_images()[a.get_defaultPrimaryPicture()],d,c,b)):null!=a.get_secondaryPicture()?lt.Annotations.Rendering._svgDrawingHelper.drawPicture(e,a.get_secondaryPicture(),d,c,b):(f=this.get_renderingEngine().get_resources(),
null!=f&&-1<a.get_defaultSecondaryPicture()&&a.get_defaultSecondaryPicture()<f.get_images().length&&lt.Annotations.Rendering._svgDrawingHelper.drawPicture(e,f.get_images()[a.get_defaultSecondaryPicture()],d,c,b))}lt.Annotations.Rendering.AnnSvgEncryptObjectRenderer.callBaseMethod(this,"render",[b,c])}}};lt.Annotations.Rendering.AnnSvgFreehandHotspotObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgFreehandHotspotObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgFreehandHotspotObjectRenderer.prototype=
{createObject:function(b){lt.Annotations.Rendering.AnnSvgFreehandHotspotObjectRenderer.callBaseMethod(this,"createObject",[b]);var b=b.get_stateId(),c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b),a=lt.Annotations.Rendering._annSvgHelpers.createImage();a.id=lt.Annotations.Rendering._annSvgHelpers.getImageId(b);lt.Annotations.Rendering._annSvgHelpers.appendElement(c,a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);
null!=a&&a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(c)))}lt.Annotations.Rendering.AnnSvgFreehandHotspotObjectRenderer.callBaseMethod(this,"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);
if(null!=a&&!a.get_stateless()){var d=Type.safeCast(c,lt.Annotations.Core.AnnFreehandHotspotObject);if(null!=d&&a.get_container().get_userMode()===lt.Annotations.Core.AnnUserMode.design){var a=b.rectFromContainerCoordinates(d.get_bounds(),d.get_fixedStateOperations()),e=this.beginClipPath();lt.Annotations.Rendering.AnnSvgFreehandHotspotObjectRenderer.callBaseMethod(this,"render",[b,c]);var f=c.get_stateId();b.pointsFromContainerCoordinates(c.get_points().toArray(),d.get_fixedStateOperations());var g=
lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(f)),h=lt.Annotations.Rendering._annSvgHelpers.getClipPathId(c.get_stateId()),h=lt.Annotations.Rendering._annSvgHelpers.getPolylineId(h);lt.Annotations.Rendering._svgDrawingHelper.drawPolyline(lt.Annotations.Rendering._annSvgHelpers.getElementById(h),b,c);lt.Annotations.Rendering._annSvgHelpers.setClipPathAttribute(g,lt.Annotations.Rendering._annSvgHelpers.getClipPathId(f));null!=d.get_picture()?
lt.Annotations.Rendering._svgDrawingHelper.drawPicture(g,d.get_picture(),a,c,b):(f=this.get_renderingEngine().get_resources(),null!=f&&-1<d.get_defaultPicture()&&d.get_defaultPicture()<f.get_images().length&&lt.Annotations.Rendering._svgDrawingHelper.drawPicture(g,f.get_images()[d.get_defaultPicture()],a,c,b));this.endClipPath(e)}}}};lt.Annotations.Rendering.AnnSvgHiliteObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgHiliteObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgHiliteObjectRenderer.prototype=
{render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");if(!this.get_renderingEngine().get_stateless()){lt.Annotations.Rendering.AnnSvgHiliteObjectRenderer.callBaseMethod(this,"render",[b,c]);var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId()),a=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(a.id));
null!=a&&lt.Annotations.Rendering._svgDrawingHelper.drawHilite(a,b,c)}}};lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer.callBaseMethod(this,"createObject",[b]);var b=b.get_stateId(),c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b),a=lt.Annotations.Rendering._annSvgHelpers.createImage();
a.id=lt.Annotations.Rendering._annSvgHelpers.getImageId(b);lt.Annotations.Rendering._annSvgHelpers.appendElement(c,a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(c)))}lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer.callBaseMethod(this,"removeObject",[b])},get_showAtRunMode:function(){var b=
this.get_renderingEngine();return null!=b?b.get_container().get_userMode()===lt.Annotations.Core.AnnUserMode.design:!1},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a&&!a.get_stateless()){a=Type.safeCast(c,lt.Annotations.Core.AnnHotspotObject);if(null!=a&&this.get_showAtRunMode()){var d=
lt.LeadPointD.create(a.get_bounds().get_left()+a.get_bounds().get_width()/2,a.get_bounds().get_top()+a.get_bounds().get_height()/2),d=b.pointFromContainerCoordinates(d,a.get_fixedStateOperations()),e=new lt.LeadMatrix,f=lt.LeadPointD.create(parseInt(d.get_x()),parseInt(d.get_y())),d=b.rectFromContainerCoordinates(a.get_rect(),a.get_fixedStateOperations()),g=1;a.get_isFlipped()&&(g=-1);var h=1;a.get_isReversed()&&(h=-1);e.translate(-f.get_x(),-f.get_y());var i=Math.abs(a.get_angle());0>h*g&&(0>h&&
(i-=180),e.scale(h,g));lt.Annotations.Core.AnnDouble.isNaN(a.get_angle())||e.rotate(i);g=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(c.get_stateId()));e.translate(f.get_x(),f.get_y());f=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId());lt.Annotations.Rendering._annSvgHelpers.setTransformAttribute(f,e);null!=a.get_picture()?lt.Annotations.Rendering._svgDrawingHelper.drawPicture(g,a.get_picture(),d,c,b):(e=this.get_renderingEngine().get_resources(),
null!=e&&-1<a.get_defaultPicture()&&a.get_defaultPicture()<e.get_images().length&&lt.Annotations.Rendering._svgDrawingHelper.drawPicture(g,e.get_images()[a.get_defaultPicture()],d,c,b))}lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer.callBaseMethod(this,"render",[b,a])}}};Object.defineProperty(lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer.prototype,"showAtRunMode",{get:lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer.prototype.get_showAtRunMode,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnSvgMediaObjectRenderer=
function(){lt.Annotations.Rendering.AnnSvgMediaObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgMediaObjectRenderer.prototype={get_showAtRunMode:function(){return!0}};Object.defineProperty(lt.Annotations.Rendering.AnnSvgMediaObjectRenderer.prototype,"showAtRunMode",{get:lt.Annotations.Rendering.AnnSvgMediaObjectRenderer.prototype.get_showAtRunMode,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnSvgNoteObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgNoteObjectRenderer.initializeBase(this)};
lt.Annotations.Rendering.AnnSvgNoteObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgNoteObjectRenderer.callBaseMethod(this,"createObject",[b]);var c=b.get_stateId(),b=lt.Annotations.Rendering._annSvgHelpers.getElementById(c),a=lt.Annotations.Rendering._annSvgHelpers.getRectId(c),d=lt.Annotations.Rendering._annSvgHelpers.createPath();d.id=lt.Annotations.Rendering._annSvgHelpers.getShadowId(c);c=lt.Annotations.Rendering._annSvgHelpers.getElementById(a);b.insertBefore(d,
c)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&lt.Annotations.Rendering._annSvgHelpers.removeElement(a,lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getShadowId(c)))}lt.Annotations.Rendering.AnnSvgNoteObjectRenderer.callBaseMethod(this,"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");
null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a&&!a.get_stateless()){var d=Type.safeCast(c,lt.Annotations.Core.AnnNoteObject),e=lt.LeadPointD.get_empty();if(null!=d){var a=this.getRenderPoints(b,d),a=b.pointsFromContainerCoordinates(a,d.get_fixedStateOperations()),f=[a[1],a[2],a[3]],g=lt.Annotations.Core.Utils.getUnitVectorPerpendicular(f[0],f[1]),h=lt.Annotations.Core.Utils.getUnitVector(f[0],
f[1]),a=[lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty(),lt.LeadPointD.get_empty()],d=lt.LeadLengthD.create(b.lengthFromContainerCoordinates(lt.LeadLengthD.create(d.get_shadowBorderWidth().get_value()),lt.Annotations.Core.AnnFixedStateOperations.lengthValue|lt.Annotations.Core.AnnFixedStateOperations.zooming)),e=c.get_bounds();if(d.get_value()<e.get_width()&&d.get_value()<e.get_height()&&(a[0]=
lt.Annotations.Core.Utils.transformPoint(h,d,f[0]),a[1]=lt.Annotations.Core.Utils.transformPoint(g,d,a[0]),e=lt.Annotations.Core.Utils.transformPoint(h,d,f[1]),a[2]=lt.Annotations.Core.Utils.transformPoint(g,d,e),e=lt.Annotations.Core.Utils.transformPoint(g,d,f[2]),a[3]=lt.Annotations.Core.Utils.transformPoint(h,d,e),a[4]=e,a[5]=f[1],a[6]=a[0],f=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getShadowId(c.get_stateId())),null!=f)){g=String.format("M{0} {1}",
a[0].get_x(),a[0].get_y());for(h=1;h<a.length;h++)g+=String.format("L{0} {1} ",a[h].get_x(),a[h].get_y());lt.Annotations.Rendering._annSvgHelpers.setPathAttribute(f,g);lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(f,lt.Annotations.Core.AnnSolidColorBrush.create("#7F7F7F"),!0)}}}lt.Annotations.Rendering.AnnSvgNoteObjectRenderer.callBaseMethod(this,"render",[b,c])}};lt.Annotations.Rendering.AnnSvgObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgObjectRenderer.initializeBase(this)};
lt.Annotations.Rendering.AnnSvgObjectRenderer.prototype={createElement:function(){return lt.Annotations.Rendering._annSvgHelpers.createGroup()},removeElementById:function(b){var c=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine),c=String.format("Annotation_Svg_{0}",c.get_element().id);lt.Annotations.Rendering._annSvgHelpers.getElementById(c).removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(b))},createObject:function(b){var c=Type.safeCast(this.get_renderingEngine(),
lt.Annotations.Rendering.AnnSvgRenderingEngine);b.set_stateId(String.format("{0}.Object{1}",c.get_element().id,lt.Annotations.Rendering.AnnSvgObjectRenderer._count$1++));var a=this.createElement();a.id=b.get_stateId();b=String.format("Annotation_Svg_{0}",c.get_element().id);b=lt.Annotations.Rendering._annSvgHelpers.getElementById(b);lt.Annotations.Rendering._annSvgHelpers.appendElement(b,a);b=lt.Annotations.Rendering._annSvgHelpers.createClipPath();b.id=lt.Annotations.Rendering._annSvgHelpers.getClipPathId(a.id);
a.appendChild(b)},addObject:function(b){this.createObject(b);var b=b.get_stateId(),c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b),a=lt.Annotations.Rendering._annSvgHelpers.createImage();a.id=lt.Annotations.Rendering._annSvgHelpers.getLockId(b);var d=lt.Annotations.Rendering._annSvgHelpers.createGroup();d.id=lt.Annotations.Rendering._annSvgHelpers.getLabelsId(b);c.appendChild(d);c.appendChild(a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId());
null!=a&&(a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getLabelsId(c))),a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getLockId(c))),a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getClipPathId(c))),this.removeElementById(b.get_stateId()));b.set_stateId("")}},render:function(b,c){var a=Type.safeCast(this.get_renderingEngine(),
lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a){var d=String.format("Annotation_Svg_{0}",a.get_element().id),a=lt.Annotations.Rendering._annSvgHelpers.getElementById(d),d=lt.Annotations.Rendering._annSvgHelpers.getElementById(d+"AnnThumbGroup");null!=d&&lt.Annotations.Rendering._annSvgHelpers.removeElement(a,d);d=lt.Annotations.Rendering._annSvgHelpers.createGroup();d.id=a.id+"AnnThumbGroup";a.appendChild(d)}if(null!=c.get_labels()&&0<Object.getKeyCount(c.get_labels()))for(a=ss.IEnumerator.getEnumerator(Object.keys(c.get_labels()));a.moveNext();){var d=
a.current,e=c.get_labels()[d];if("AnnObjectName"===d){var f=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getLabelsId(c.get_stateId())),g=lt.Annotations.Rendering._annSvgHelpers.getElementById(e.get_stateId());null==g&&(g=lt.Annotations.Rendering._annSvgHelpers.createText(),String.isNullOrEmpty(e.get_stateId())&&e.set_stateId(String.format("{0}.Label.{1}",c.get_stateId(),d)),g.id=e.get_stateId(),null!=f&&f.appendChild(g));e.set_originalPosition(lt.LeadPointD.create(c.get_bounds().get_x(),
c.get_bounds().get_y()));null!=this.get_labelRenderer()&&(this.get_labelRenderer().set_offsetHeight(!0),this.get_labelRenderer().renderLabel(b,e,c.get_fixedStateOperations()))}}if(c.get_id()===lt.Annotations.Core.AnnObject.selectObjectId)if(a=Type.safeCast(c,lt.Annotations.Core.AnnSelectionObject).get_selectedObjects(),1===a.get_count())a=a.get_item(0),a.get_canRotate()&&this.renderRotatePointThumbs(b,a.get_internalRotateCenterLocation(),a.get_internalRotateGripperLocation(),a.get_fixedStateOperations()),
this.renderThumbs(b,a.get_internalThumbLocations(),a.get_fixedStateOperations());else{if(0<a.get_count()){for(a=ss.IEnumerator.getEnumerator(a);a.moveNext();)d=a.current,e=this.get_renderingEngine().get_renderers()[d.get_id()],null!=e&&(e.initialize(this.get_renderingEngine()),e.render(b,d));c.get_canRotate()&&this.renderRotatePointThumbs(b,c.get_internalRotateCenterLocation(),c.get_internalRotateGripperLocation(),c.get_fixedStateOperations());this.renderThumbs(b,c.get_internalThumbLocations(),c.get_fixedStateOperations())}}else c.get_isSelected()&&
(c.get_canRotate()&&this.renderRotatePointThumbs(b,c.get_internalRotateCenterLocation(),c.get_internalRotateGripperLocation(),c.get_fixedStateOperations()),this.renderThumbs(b,c.get_internalThumbLocations(),c.get_fixedStateOperations()),c.get_supportsSelectionStroke()&&this.renderSelection(b,c));this.renderLocked(b,c,c.get_fixedStateOperations())},renderLocked:function(b,c){var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a&&!a.get_stateless()){var d=
lt.LeadPointD.create(c.get_bounds().get_left(),c.get_bounds().get_top()),d=b.pointFromContainerCoordinates(d,c.get_fixedStateOperations()),a=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getLockId(c.get_stateId()));if(null!=a)if(c.get_isLocked()){lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(a,!0);var e=this.get_renderingEngine().get_resources();if(null!=e){var f=e.get_images()[c.get_lockPicture()],d=lt.LeadRectD.create(d.get_x(),d.get_y(),
32,32);null!=f?lt.Annotations.Rendering._svgDrawingHelper.drawPicture(a,f,d,c,b):-1<c.get_lockPicture()&&c.get_lockPicture()<e.get_images().length&&lt.Annotations.Rendering._svgDrawingHelper.drawPicture(a,e.get_images()[c.get_lockPicture()],d,c,b)}}else lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(a,!1)}}};lt.Annotations.Rendering.AnnSvgPointerObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgPointerObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgPointerObjectRenderer.prototype=
{createObject:function(b){lt.Annotations.Rendering.AnnSvgPointerObjectRenderer.callBaseMethod(this,"createObject",[b]);var b=b.get_stateId(),c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b),a=lt.Annotations.Rendering._annSvgHelpers.createPolyline();a.id=lt.Annotations.Rendering._annSvgHelpers.getArrowId(b);lt.Annotations.Rendering._annSvgHelpers.appendElement(c,a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);
null!=a&&a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getArrowId(c)))}lt.Annotations.Rendering.AnnSvgPointerObjectRenderer.callBaseMethod(this,"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);
if(null!=a&&!a.get_stateless()&&(lt.Annotations.Rendering.AnnSvgPointerObjectRenderer.callBaseMethod(this,"render",[b,c]),a=Type.safeCast(c,lt.Annotations.Core.AnnPointerObject),null!=a)){var d=a.getArrowPoints(),e=b.lengthFromContainerCoordinates(a.get_arrowLength(),a.get_fixedStateOperations()|lt.Annotations.Core.AnnFixedStateOperations.zooming|lt.Annotations.Core.AnnFixedStateOperations.lengthValue),f=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getArrowId(c.get_stateId()));
if(null!=d&&3<=d.length){d[1]=b.pointFromContainerCoordinates(d[1],a.get_fixedStateOperations());for(var g=0;2>g;g++){d[2*g]=b.pointFromContainerCoordinates(d[2*g],a.get_fixedStateOperations());var h=lt.Annotations.Core.Utils.getUnitVector(d[1],d[2*g]);d[2*g]=lt.Annotations.Core.Utils.transformPoint(h,lt.LeadLengthD.create(e),d[1])}lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(f,d,!1);lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(f,a.get_stroke(),a.get_supportsStroke());
lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(f,null,a.get_supportsFill())}}}};lt.Annotations.Rendering.AnnSvgPointObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgPointObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgPointObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgPointObjectRenderer.callBaseMethod(this,"createObject",[b]);var b=b.get_stateId(),c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b),a=lt.Annotations.Rendering._annSvgHelpers.createImage();
a.id=lt.Annotations.Rendering._annSvgHelpers.getImageId(b);var d=lt.Annotations.Rendering._annSvgHelpers.createEllipse();d.id=lt.Annotations.Rendering._annSvgHelpers.getEllipseId(b);lt.Annotations.Rendering._annSvgHelpers.appendElement(c,a);lt.Annotations.Rendering._annSvgHelpers.appendElement(c,d)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&(a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getEllipseId(c))),
a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(c))))}lt.Annotations.Rendering.AnnSvgPointObjectRenderer.callBaseMethod(this,"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a&&
!a.get_stateless()){a=Type.safeCast(c,lt.Annotations.Core.AnnPointObject);if(null!=a){var d=b.rectFromContainerCoordinates(c.get_bounds(),a.get_fixedStateOperations()),e=c.get_stateId(),f=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(e)),e=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getEllipseId(e));a.get_showPicture()?(lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(f,!0),lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(e,
!1),null!=a.get_picture()?lt.Annotations.Rendering._svgDrawingHelper.drawPicture(f,a.get_picture(),d,c,b):(e=this.get_renderingEngine().get_resources(),null!=e&&-1<a.get_defaultPicture()&&a.get_defaultPicture()<e.get_images().length&&(a.set_picture(e.get_images()[a.get_defaultPicture()]),d=b.rectFromContainerCoordinates(c.get_bounds(),a.get_fixedStateOperations()),lt.Annotations.Rendering._svgDrawingHelper.drawPicture(f,e.get_images()[a.get_defaultPicture()],d,c,b)))):(lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(f,
!1),lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(e,!0),lt.Annotations.Rendering._annSvgHelpers.setEllipseAttribute(e,d),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(e,a.get_fill(),a.get_supportsFill()),null!=a.get_stroke()&&lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(e,a.get_stroke(),a.get_supportsStroke()))}lt.Annotations.Rendering.AnnSvgPointObjectRenderer.callBaseMethod(this,"render",[b,c])}}};lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer=function(){this._activeId$2=
"";lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer.prototype={setActivePolyLineID:function(b){this._activeId$2=b},createObject:function(b){lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer.callBaseMethod(this,"createObject",[b]);var c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId()),a=lt.Annotations.Rendering._annSvgHelpers.createPolyline(),d=lt.Annotations.Rendering._annSvgHelpers.getPolylineId(b.get_stateId());
a.id=d;d=lt.Annotations.Rendering._annSvgHelpers.createPolyline();b=lt.Annotations.Rendering._annSvgHelpers.getClipPathId(b.get_stateId());d.id=lt.Annotations.Rendering._annSvgHelpers.getPolylineId(b);lt.Annotations.Rendering._annSvgHelpers.getElementById(b).appendChild(d);this.setActivePolyLineID("");c.appendChild(a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getClipPathId(b.get_stateId());if(!String.isNullOrEmpty(a)){var d=
lt.Annotations.Rendering._annSvgHelpers.getElementById(a);null!=d&&lt.Annotations.Rendering._annSvgHelpers.removeElement(d,lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getPolylineId(a)))}a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&lt.Annotations.Rendering._annSvgHelpers.removeElement(a,lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getPolylineId(c)))}lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer.callBaseMethod(this,
"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a){var d=c.get_stateId(),e=c.get_stateId(),f=lt.Annotations.Rendering._annSvgHelpers.getElementById(d);null!=f&&((!d.startsWith(a.get_element().id)&&!a.get_element().contains(f)&&(e=c.get_stateId(),
c.set_stateId("")),a.get_element().contains(f))?(String.isNullOrEmpty(this._activeId$2)&&(this._activeId$2=lt.Annotations.Rendering._annSvgHelpers.getPolylineId(d)),a=lt.Annotations.Rendering._annSvgHelpers.getElementById(this._activeId$2),this.get_clipPath()&&(d=lt.Annotations.Rendering._annSvgHelpers.getClipPathId(d),lt.Annotations.Rendering._svgDrawingHelper.drawPolyline(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getPolylineId(d)),b,c)),lt.Annotations.Rendering._svgDrawingHelper.drawPolyline(a,
b,c)):(this.addObject(c),this.render(b,c),c.set_stateId(e)));lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer.callBaseMethod(this,"render",[b,c]);this._activeId$2=""}}};lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer=function(){this._tickmarksId$3="";lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer.prototype={get_tickmarksId:function(){return this._tickmarksId$3},set_tickmarksId:function(b){return this._tickmarksId$3=
b},drawTickMarks:function(b,c,a,d,e,f,g){f=lt.Annotations.Rendering._annSvgHelpers.getElementById(this._tickmarksId$3);if(null!=f){var h=f.getAttribute("d"),b=lt.Annotations.Core.RulerHelper.getTickMarks(b,c,a,lt.LeadLengthD.create(d.get_value()),e,g);if(null!=b)for(c=0;c<b.length;c+=2)h+=String.format("M{0} {1} L{2} {3} ",b[c].get_x(),b[c].get_y(),b[c+1].get_x(),b[c+1].get_y());lt.Annotations.Rendering._annSvgHelpers.setPathAttribute(f,h)}},drawGauge:function(b,c,a,d,e,f){if(!(c.get_x()===a.get_x()&&
c.get_y()===a.get_y())){var g=lt.Annotations.Rendering._annSvgHelpers.getElementById(this._tickmarksId$3);null!=g&&(b=lt.Annotations.Core.RulerHelper.getGaugePoints(b,c,a,d,f),null!=b&&(c=g.getAttribute("d"),c+=String.format("M{0} {1} L{2} {3} ",b[0].get_x(),b[0].get_y(),b[1].get_x(),b[1].get_y()),c+=String.format("M{0} {1} L{2} {3}",b[2].get_x(),b[2].get_y(),b[3].get_x(),b[3].get_y()),lt.Annotations.Rendering._annSvgHelpers.setPathAttribute(g,c),lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(g,
e,!0)))}},drawLengthText:function(b,c,a){null!=this.get_labelRenderer()&&this.get_labelRenderer().renderLabel(b,c,a)},get__canDrawLength:function(){return!0},addObject:function(b){lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer.callBaseMethod(this,"addObject",[b]);var b=b.get_stateId(),b=lt.Annotations.Rendering._annSvgHelpers.getElementById(b),c=lt.Annotations.Rendering._annSvgHelpers.createPath();this._tickmarksId$3=String.format("{0}.{1}",b.id,"tickmarks");c.id=this._tickmarksId$3;lt.Annotations.Rendering._annSvgHelpers.appendElement(b,
c)},removeObject:function(b){var c=b.get_stateId();String.isNullOrEmpty(c)||(c=lt.Annotations.Rendering._annSvgHelpers.getElementById(c),null!=c&&c.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(this._tickmarksId$3)));lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer.callBaseMethod(this,"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");
lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer.callBaseMethod(this,"render",[b,c]);var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a&&!a.get_stateless()&&(a=Type.safeCast(c,lt.Annotations.Core.AnnPolyRulerObject),null!=a)){var d=lt.Annotations.Rendering._annSvgHelpers.getElementById(this._tickmarksId$3);if(null!=d&&null!=a.get_tickMarksStroke()){var e=a.get_tickMarksStroke();null==e&&(e=a.get_stroke());lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(d,
e,!0);lt.Annotations.Rendering._annSvgHelpers.setPathAttribute(d,"")}for(d=0;d<c.get_points().get_count()-1;++d){var e=c.get_points().get_item(d),f=c.get_points().get_item(d+1);a.get_supportsStroke()&&null!=a.get_stroke()&&(a.get_showGauge()&&this.drawGauge(b,e,f,a.get_gaugeLength(),a.get_stroke(),a.get_fixedStateOperations()),a.get_showTickMarks()&&null!=a.get_tickMarksStroke()&&this.drawTickMarks(b,e,f,a.get_tickMarksLength(),a.get_measurementUnit(),a.get_tickMarksStroke(),a.get_fixedStateOperations()))}if(Object.keyExists(a.get_labels(),
"RulerLength")&&(d=a.get_labels().RulerLength,null!=d&&(e=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getLabelsId(c.get_stateId())),null!=e&&(f=lt.Annotations.Rendering._annSvgHelpers.createText(),String.isNullOrEmpty(d.get_stateId())&&d.set_stateId(String.format("{0}.Label.{1}",c.get_stateId(),"RulerLength")),f.id=d.get_stateId(),e.appendChild(f)),1<a.get_points().get_count()))){e=lt.Annotations.Core.Utils.getUnitVector(a.get_points().get_item(0),
a.get_points().get_item(1));f=a.get_points().get_item(a.get_points().get_count()-1);0>e.get_x()&&(e=lt.Annotations.Core.Utils.getUnitVector(a.get_points().get_item(1),a.get_points().get_item(0)),f=a.get_points().get_item(0));if(a.get_measurementUnit()===lt.Annotations.Core.AnnUnit.pixel){a.set_measurementUnit(lt.Annotations.Core.AnnUnit.unit);var g=a.getRulerLength(b.get_calibrationScale());a.set_measurementUnit(lt.Annotations.Core.AnnUnit.pixel);g=b.lengthFromContainerCoordinates(g,lt.Annotations.Core.AnnFixedStateOperations.scrolling|
lt.Annotations.Core.AnnFixedStateOperations.zooming);d.set_text(String.format("{0} {1}",lt.Annotations.Core.Utils.precisionFormat(a.get_precision(),g),a.get_unitsAbbreviation()[lt.Annotations.Core.AnnUnit.pixel]))}else d.set_text(a.getRulerLengthAsString(b.get_calibrationScale()));d.set_originalPosition(lt.Annotations.Core.Utils.transformPoint(e,b.lengthToContainerCoordinates(d.get_font().get_fontHeight()),f));this.drawLengthText(b,d,a.get_fixedStateOperations())}}}};Object.defineProperty(lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer.prototype,
"tickmarksId",{get:lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer.prototype.get_tickmarksId,set:lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer.prototype.set_tickmarksId,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer._getAngle$4=function(b,c,a,d){b-=c;a?180<Math.abs(b)&&(b=0<b?b-360:b+360):180>Math.abs(b)&&
(b=0<b?b-360:b+360);return d===lt.Annotations.Core.AnnAngularUnit.radian?lt.Annotations.Core.Utils.degreesToRadian(b):b};lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer.prototype={_curveId$4:null,createObject:function(b){lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer.callBaseMethod(this,"createObject",[b]);b=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId());this._curveId$4=String.format("{0}.{1}",b.id,"curve");var c=lt.Annotations.Rendering._annSvgHelpers.createPath();
c.id=this._curveId$4;lt.Annotations.Rendering._annSvgHelpers.appendElement(b,c)},removeObject:function(b){var c=b.get_stateId();String.isNullOrEmpty(c)||(c=lt.Annotations.Rendering._annSvgHelpers.getElementById(c),null!=c&&c.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(this._curveId$4)));lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer.callBaseMethod(this,"removeObject",[b])},getAngleText:function(b,c,a,d){null==d&&lt.Annotations.Core.ExceptionHelper.argumentNullException("unitsAbbreviation");
return String.format("{0} {1}",lt.Annotations.Core.Utils.precisionFormat(c,b),d[a])},_getTextPart$4:function(b,c,a,d,e,f,g){var h=b.get_y()-a.get_y(),a=b.get_x()-a.get_x(),i=b.get_y()-c.get_y(),b=b.get_x()-c.get_x(),c=1,g=lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer._getAngle$4(d,e,f,g);0>=i&&0>=b&&0>=h&&0>=a?c=2:0<=i&&0<=b&&0<=h&&0<=a?c=4:0>=i&&0<=b&&0>=h&&0<=a?c=1:0<=i&&0>=b&&0<=h&&0>=a?c=3:0<=i&&0<=b&&0>=h&&0<=a||0<=h&&0<=a&&0>=i&&0<=b?(f=d,g=e,d>e?f=360-f:g=360-g,c=0<=i&&0<=b&&0>=h&&
0<=a?f<g?1:4:f<g?4:1):0<=i&&0<=b&&0>=h&&0>=a||0<=h&&0<=a&&0>=i&&0>=b?c=0<=i&&0<=b&&0>=h&&0>=a?0>g?f?3:1:f?1:3:0>g?f?1:3:f?3:1:0<=i&&0>=b&&0>=h&&0<=a||0<=h&&0>=a&&0>=i&&0<=b?c=0<=i&&0>=b&&0>=h&&0<=a?0>g?f?2:4:f?4:2:0>g?f?4:2:f?2:4:0<=i&&0<=b&&0<=h&&0>=a||0<=h&&0<=a&&0<=i&&0>=b?(f=d,g=e,d>e?f=180-f:g=180-g,c=0<=i&&0<=b&&0<=h&&0>=a?f<g?4:3:f<g?3:4):0<=i&&0>=b&&0>=h&&0>=a||0<=h&&0>=a&&0>=i&&0>=b?(f=d,g=e,d>e?(f=270-f,g-=90):(g=270-g,f-=90),c=0<=i&&0>=b&&0>=h&&0>=a?f<g?3:2:f<g?2:3):0>=i&&0>=b&&0>=h&&0<=
a||0>=h&&0>=a&&0>=i&&0<=b?(f=d,g=e,d>e?(f=360-f,g-=180):(g=360-g,f-=180),c=0>=i&&0>=b&&0>=h&&0<=a?f<g?2:1:f<g?1:2):c=1;return c},getPoint:function(b,c,a,d,e,f,g,h,i){c=this._getTextPart$4(a,c,d,e,f,g,i);a=6;g||(a=h);return 1===c?lt.LeadPointD.create(a,-(b.get_height()+a)):2===c?lt.LeadPointD.create(-b.get_width()-a,-(b.get_height()+a)):4===c?lt.LeadPointD.create(a,a):lt.LeadPointD.create(-b.get_width()-a,a)},_polarToCartesian$4:function(b,c,a){var d=lt.LeadPointD.create(0,0),a=lt.Annotations.Core.Utils.degreesToRadian(a);
d.set_x(b.get_x()+c*Math.cos(a));d.set_y(b.get_y()+c*Math.sin(a));return d},drawArc:function(b,c,a,d,e,f,g){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");var h=lt.Annotations.Rendering._annSvgHelpers.getElementById(this._curveId$4);null!=h&&(b=b.lengthFromContainerCoordinates(e,g),e=this._polarToCartesian$4(c,b,a),c=this._polarToCartesian$4(c,b,a+d),d=String.format("M{0} {1} A{2} {2} 0 {3} {4} {5} {6}",e.get_x(),e.get_y(),b,180<Math.abs(d)?1:0,0>d?0:1,c.get_x(),c.get_y()),
lt.Annotations.Rendering._annSvgHelpers.setPathAttribute(h,d),lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(h,f,!0),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(h,null,!1))},drawAngleText:function(b,c,a){null!=this.get_labelRenderer()&&this.get_labelRenderer().renderLabel(b,c,a)},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer.callBaseMethod(this,
"render",[b,c]);var a=Type.safeCast(c,lt.Annotations.Core.AnnProtractorObject);if(null!=a){var d=a.get_points().toArray(),d=b.pointsFromContainerCoordinates(d,a.get_fixedStateOperations()),e=lt.Annotations.Core.Utils.findAngle(d[0],d[1]),f=lt.Annotations.Core.Utils.findAngle(d[2],d[1]),g=0,h=0,i=a.get_acute();if(0.2<Math.abs(e-f)||!i)g=e-180,h=-lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer._getAngle$4(e,f,i,lt.Annotations.Core.AnnAngularUnit.degree);f&&a.get_supportsStroke()&&null!=a.get_stroke()&&
(lt.Annotations.Core.Utils.distance(a.get_firstPoint(),a.get_centerPoint()),this.drawArc(b,d[1],g,h,a.get_arcRadius(),a.get_stroke(),a.get_fixedStateOperations()|lt.Annotations.Core.AnnFixedStateOperations.zooming|lt.Annotations.Core.AnnFixedStateOperations.lengthValue));g="AngleText";if(Object.keyExists(a.get_labels(),g)&&(d=a.get_labels()[g],null!=d)){var h=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getLabelsId(c.get_stateId())),j=lt.Annotations.Rendering._annSvgHelpers.createText();
String.isNullOrEmpty(d.get_stateId())&&d.set_stateId(String.format("{0}.Label.{1}",c.get_stateId(),g));j.id=d.get_stateId();h.appendChild(j);g=Math.abs(lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer._getAngle$4(e,f,a.get_acute(),a.get_angularUnit()));lt.Annotations.Core.AnnDouble.isNaN(g)||(d.set_text(this.getAngleText(g,a.get_anglePrecision(),a.get_angularUnit(),a.get_angularUnitsAbbreviation())),g=lt.Annotations.Rendering.AnnHtml5RenderingEngine.getTextSize(d.get_text(),d.get_font(),lt.LeadSizeD.get_empty()),
h=b.pointToContainerCoordinates(lt.LeadPointD.create(0,0)),d.set_originalPosition(a.get_centerPoint()),e=this.getPoint(g,b.pointFromContainerCoordinates(a.get_firstPoint(),a.get_fixedStateOperations()),b.pointFromContainerCoordinates(a.get_centerPoint(),a.get_fixedStateOperations()),b.pointFromContainerCoordinates(a.get_secondPoint(),a.get_fixedStateOperations()),e,f,i,b.lengthFromContainerCoordinates(a.get_arcRadius(),lt.Annotations.Core.AnnFixedStateOperations.lengthValue),a.get_angularUnit()),
e=lt.LeadPointD.create(e.get_x(),e.get_y()+g.get_height()),e=b.pointToContainerCoordinates(e),e=lt.LeadPointD.create(e.get_x()-h.get_x(),e.get_y()-h.get_y()),d.set_originalPosition(lt.LeadPointD.create(a.get_centerPoint().get_x()+e.get_x(),a.get_centerPoint().get_y()+e.get_y())),this.drawAngleText(b,d,a.get_fixedStateOperations()))}g="FirstRulerLength";if(Object.keyExists(a.get_labels(),g)&&(d=a.get_labels()[g],null!=d&&(h=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getLabelsId(c.get_stateId())),
j=lt.Annotations.Rendering._annSvgHelpers.createText(),String.isNullOrEmpty(d.get_stateId())&&d.set_stateId(String.format("{0}.Label.{1}",c.get_stateId(),g)),j.id=d.get_stateId(),h.appendChild(j),e=a.getRulerLengthFromPoints(a.get_centerPoint(),a.get_firstPoint(),b.get_calibrationScale()),!lt.Annotations.Core.AnnDouble.isNaN(e.get_value())))){d.set_text(a.getRulerLengthAsStringFromPoints(a.get_centerPoint(),a.get_firstPoint(),b.get_calibrationScale()));e=a.get_firstPoint().clone();if(e.get_x()<a.get_centerPoint().get_x()||
e.get_y()<a.get_centerPoint().get_y())g=lt.Annotations.Rendering.AnnHtml5RenderingEngine.getTextSize(d.get_text(),d.get_font(),lt.LeadSizeD.get_empty()),e.get_y()<a.get_centerPoint().get_y()&&e.set_y(e.get_y()-720*g.get_height()/b.get_targetDpiX()),e.get_x()<a.get_centerPoint().get_x()&&e.set_x(e.get_x()-720*g.get_width()/b.get_targetDpiX());d.set_originalPosition(e.clone());this.drawLengthText(b,d,a.get_fixedStateOperations())}g="SecondRulerLength";if(Object.keyExists(a.get_labels(),g)&&f&&(d=a.get_labels()[g],
null!=d&&(h=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getLabelsId(c.get_stateId())),j=lt.Annotations.Rendering._annSvgHelpers.createText(),String.isNullOrEmpty(d.get_stateId())&&d.set_stateId(String.format("{0}.Label.{1}",c.get_stateId(),g)),j.id=d.get_stateId(),h.appendChild(j),e=a.getRulerLengthFromPoints(a.get_centerPoint(),a.get_secondPoint(),b.get_calibrationScale()),!lt.Annotations.Core.AnnDouble.isNaN(e.get_value())))){d.set_text(a.getRulerLengthAsStringFromPoints(a.get_centerPoint(),
a.get_secondPoint(),b.get_calibrationScale()));e=a.get_secondPoint().clone();if(e.get_x()<a.get_centerPoint().get_x()||e.get_y()<a.get_centerPoint().get_y())g=lt.Annotations.Rendering.AnnHtml5RenderingEngine.getTextSize(d.get_text(),d.get_font(),lt.LeadSizeD.get_empty()),e.get_y()<a.get_centerPoint().get_y()&&e.set_y(e.get_y()-720*g.get_height()/b.get_targetDpiX()),e.get_x()<a.get_centerPoint().get_x()&&e.set_x(e.get_x()-720*g.get_width()/b.get_targetDpiX());d.set_originalPosition(e.clone());this.drawLengthText(b,
d,a.get_fixedStateOperations())}}}};lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer.callBaseMethod(this,"createObject",[b]);var b=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId()),c=lt.Annotations.Rendering._annSvgHelpers.createPolyline();
c.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(b.id);b.appendChild(c)},removeObject:function(b){var c=b.get_stateId();String.isNullOrEmpty(c)||(c=lt.Annotations.Rendering._annSvgHelpers.getElementById(c),null!=c&&lt.Annotations.Rendering._annSvgHelpers.removeElement(c,lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(c.id))));lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer.callBaseMethod(this,"removeObject",[b])},render:function(b,
c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a){var d=c.get_stateId(),e=c.get_stateId(),f=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId());null!=f&&((!d.startsWith(a.get_element().id)&&!a.get_element().contains(f)&&(e=c.get_stateId(),c.set_stateId("")),a.get_element().contains(f))?
(a=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(f.id)),d=Type.safeCast(c,lt.Annotations.Core.AnnRectangleObject),e=this.getRenderPoints(b,c),e=b.pointsFromContainerCoordinates(e,d.get_fixedStateOperations()),1<e.length&&(lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(a,e,!0),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(a,d.get_fill(),d.get_supportsFill()),lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(a,
d.get_stroke(),d.get_supportsStroke()))):(this.addObject(c),this.render(b,c),c.set_stateId(e)));lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer.callBaseMethod(this,"render",[b,c])}}};lt.Annotations.Rendering.AnnSvgRubberStampObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgRubberStampObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgRubberStampObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgRubberStampObjectRenderer.callBaseMethod(this,
"createObject",[b]);var b=b.get_stateId(),c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b),a=lt.Annotations.Rendering._annSvgHelpers.createImage();a.id=lt.Annotations.Rendering._annSvgHelpers.getImageId(b);lt.Annotations.Rendering._annSvgHelpers.appendElement(c,a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(c)))}lt.Annotations.Rendering.AnnSvgRubberStampObjectRenderer.callBaseMethod(this,
"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a&&!a.get_stateless()){var d=Type.safeCast(c,lt.Annotations.Core.AnnRubberStampObject);if(null!=d){var e=lt.LeadPointD.create(d.get_bounds().get_left()+d.get_bounds().get_width()/2,d.get_bounds().get_top()+
d.get_bounds().get_height()/2),e=b.pointFromContainerCoordinates(e,d.get_fixedStateOperations()),f=new lt.LeadMatrix,g=lt.LeadPointD.create(parseInt(e.get_x()),parseInt(e.get_y())),e=b.rectFromContainerCoordinates(d.get_rect(),d.get_fixedStateOperations()),h=1;d.get_isFlipped()&&(h=-1);var i=1;d.get_isReversed()&&(i=-1);f.translate(-g.get_x(),-g.get_y());var j=Math.abs(d.get_angle());0>i*h&&(0>i&&(j-=180),f.scale(i,h));lt.Annotations.Core.AnnDouble.isNaN(d.get_angle())||f.rotate(j);h=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(c.get_stateId()));
f.translate(g.get_x(),g.get_y());lt.Annotations.Rendering._annSvgHelpers.setTransformAttribute(h,f);a=a.get_resources();null!=a&&lt.Annotations.Rendering._svgDrawingHelper.drawPicture(h,a.get_rubberStamps()[d.get_rubberStampType()],e,c,b)}lt.Annotations.Rendering.AnnSvgRubberStampObjectRenderer.callBaseMethod(this,"render",[b,c])}}};lt.Annotations.Rendering.AnnSvgStampObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgStampObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgStampObjectRenderer.prototype=
{createObject:function(b){lt.Annotations.Rendering.AnnSvgStampObjectRenderer.callBaseMethod(this,"createObject",[b]);var b=b.get_stateId(),c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b),a=lt.Annotations.Rendering._annSvgHelpers.createImage();a.id=lt.Annotations.Rendering._annSvgHelpers.getImageId(b);lt.Annotations.Rendering._annSvgHelpers.appendElement(c,a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);
null!=a&&a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(c)))}lt.Annotations.Rendering.AnnSvgStampObjectRenderer.callBaseMethod(this,"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=
a&&!a.get_stateless()){var a=Type.safeCast(c,lt.Annotations.Core.AnnStampObject),d=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(c.get_stateId()));if(null!=a){if(null!=a.get_picture()&&null!=a.get_picture().get_source()){var e=lt.LeadPointD.create(a.get_bounds().get_left()+a.get_bounds().get_width()/2,a.get_bounds().get_top()+a.get_bounds().get_height()/2),e=b.pointFromContainerCoordinates(e,a.get_fixedStateOperations()),f=new lt.LeadMatrix,
e=lt.LeadPointD.create(parseInt(e.get_x()),parseInt(e.get_y())),g=b.rectFromContainerCoordinates(a.get_rect(),a.get_fixedStateOperations());if(!lt.Annotations.Core.AnnDouble.isNaN(a.get_angle())){var h=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId());f.rotateAt(a.get_angle(),e.get_x(),e.get_y());lt.Annotations.Rendering._annSvgHelpers.setTransformAttribute(h,f)}lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(d,!0);lt.Annotations.Rendering._svgDrawingHelper.drawPicture(d,
a.get_picture(),g,c,b)}else lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(d,!1);lt.Annotations.Rendering.AnnSvgStampObjectRenderer.callBaseMethod(this,"render",[b,c])}}}};lt.Annotations.Rendering.AnnSvgStickyNoteObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgStickyNoteObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgStickyNoteObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgStickyNoteObjectRenderer.callBaseMethod(this,"createObject",
[b]);var c=b.get_stateId(),b=lt.Annotations.Rendering._annSvgHelpers.getElementById(c),a=lt.Annotations.Rendering._annSvgHelpers.createImage();a.id=lt.Annotations.Rendering._annSvgHelpers.getImageId(c);lt.Annotations.Rendering._annSvgHelpers.appendElement(b,a);c=lt.Annotations.Rendering._annSvgHelpers.createGroup();c.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(b.id)+"SelectionGroup";b.appendChild(c);a=lt.Annotations.Rendering._annSvgHelpers.createPolyline();a.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(b.id)+
"OuterSelection";c.appendChild(a);a=lt.Annotations.Rendering._annSvgHelpers.createPolyline();a.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(b.id)+"InnerSelection";c.appendChild(a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(c)))}lt.Annotations.Rendering.AnnSvgStickyNoteObjectRenderer.callBaseMethod(this,
"removeObject",[b])},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a&&!a.get_stateless()){a=Type.safeCast(c,lt.Annotations.Core.AnnStickyNoteObject);if(null!=a){var d=b.rectFromContainerCoordinates(c.get_bounds(),a.get_fixedStateOperations()),e=c.get_stateId(),e=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getImageId(e));
lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(e,!0);if(null!=a.get_picture())lt.Annotations.Rendering._svgDrawingHelper.drawPicture(e,a.get_picture(),d,c,b);else{var f=this.get_renderingEngine().get_resources();null!=f&&-1<a.get_defaultPicture()&&a.get_defaultPicture()<f.get_images().length&&(a.set_picture(f.get_images()[a.get_defaultPicture()]),d=b.rectFromContainerCoordinates(c.get_bounds(),a.get_fixedStateOperations()),lt.Annotations.Rendering._svgDrawingHelper.drawPicture(e,f.get_images()[a.get_defaultPicture()],
d,c,b))}}a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId());a=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(a.id)+"SelectionGroup");null!=a&&(c.get_isSelected()?lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(a,!0):lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute(a,!1));lt.Annotations.Rendering.AnnSvgStickyNoteObjectRenderer.callBaseMethod(this,"render",[b,c])}},renderSelection:function(b,c){null==
b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a){var d=Type.safeCast(c,lt.Annotations.Core.AnnStickyNoteObject);c.get_stateId();if(null!=a){var e=d.get_stateId();d.get_stateId();var f=lt.Annotations.Rendering._annSvgHelpers.getElementById(d.get_stateId());if(null!=f&&(!e.startsWith(a.get_element().id)&&
!a.get_element().contains(f)&&(d.get_stateId(),d.set_stateId("")),e=b.rectFromContainerCoordinates(c.get_bounds(),d.get_fixedStateOperations()),a=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(f.id)+"OuterSelection"),f=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(f.id)+"InnerSelection"),e=[e.get_topLeft(),e.get_topRight(),e.get_bottomRight(),e.get_bottomLeft()],1<e.length)){var g=
b.strokeFromContainerCoordinates(d.get_selectionStroke(),d.get_fixedStateOperations()).clone();lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(a,e,!0);lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(a,lt.Annotations.Core.AnnSolidColorBrush.create("transparent"),d.get_supportsFill());lt.Annotations.Rendering._annSvgHelpers.setStrokeOpacityAttribute(a,0.5);lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(a,g,d.get_supportsSelectionStroke());a=lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("white"),
lt.LeadLengthD.create(g.get_strokeThickness().get_value()-2));lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(f,e,!0);lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(f,lt.Annotations.Core.AnnSolidColorBrush.create("transparent"),d.get_supportsFill());lt.Annotations.Rendering._annSvgHelpers.setStrokeOpacityAttribute(f,0.5);lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(f,a,d.get_supportsSelectionStroke())}}}}};lt.Annotations.Rendering.AnnSvgTextObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgTextObjectRenderer.initializeBase(this)};
lt.Annotations.Rendering.AnnSvgTextObjectRenderer._getTextRotation$3=function(b,c){var a=lt.LeadMatrix.get_identity();switch(b){case lt.Annotations.Core.AnnTextRotate.rotate90:a.rotateAt(-90,c.get_x(),c.get_y());break;case lt.Annotations.Core.AnnTextRotate.rotate180:a.rotateAt(-180,c.get_x(),c.get_y());break;case lt.Annotations.Core.AnnTextRotate.rotate270:a.rotateAt(90,c.get_x(),c.get_y())}return a};lt.Annotations.Rendering.AnnSvgTextObjectRenderer._alignRect$3=function(b,c,a,d,e){var f=c.clone();
switch(a){case lt.Annotations.Core.AnnHorizontalAlignment.center:f.set_x(Math.max(c.get_left(),c.get_left()+(c.get_width()-b.get_width())/2));break;case lt.Annotations.Core.AnnHorizontalAlignment.right:f.set_x(c.get_right()-b.get_width()-e.get_right());break;default:f.set_x(c.get_left()+e.get_left())}switch(d){case lt.Annotations.Core.AnnVerticalAlignment.center:f.set_y((c.get_top()+c.get_bottom()-b.get_height())/2);break;case lt.Annotations.Core.AnnVerticalAlignment.bottom:f.set_y(c.get_bottom()-
b.get_height()-e.get_bottom());break;default:f.set_y(c.get_top()+e.get_top())}return f};lt.Annotations.Rendering.AnnSvgTextObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgTextObjectRenderer.callBaseMethod(this,"createObject",[b]);var c=b.get_stateId(),a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c),d=lt.Annotations.Rendering._annSvgHelpers.createPolyline(),b=lt.Annotations.Rendering._annSvgHelpers.getClipPathId(b.get_stateId());d.id=lt.Annotations.Rendering._annSvgHelpers.getPolylineId(b);
lt.Annotations.Rendering._annSvgHelpers.getElementById(b).appendChild(d);d=lt.Annotations.Rendering._annSvgHelpers.createText();d.id=lt.Annotations.Rendering._annSvgHelpers.getTextId(c);lt.Annotations.Rendering._annSvgHelpers.appendElement(a,d)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getClipPathId(c);if(!String.isNullOrEmpty(a)){var d=lt.Annotations.Rendering._annSvgHelpers.getElementById(a);null!=d&&d.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getPolylineId(a)));
a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&a.removeChild(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getTextId(c)))}}lt.Annotations.Rendering.AnnSvgTextObjectRenderer.callBaseMethod(this,"removeObject",[b])},_flipReverseText$3:!1,get_flipReverseText:function(){return this._flipReverseText$3},set_flipReverseText:function(b){return this._flipReverseText$3=b},getTextSize:function(b,c,a){return lt.Annotations.Rendering.AnnHtml5RenderingEngine.getTextSize(b,
c,a)},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");if(!this.get_renderingEngine().get_stateless()){lt.Annotations.Rendering.AnnSvgTextObjectRenderer.callBaseMethod(this,"render",[b,c]);var a=Type.safeCast(c,lt.Annotations.Core.AnnTextObject),d=c.get_stateId();if(null!=a&&null!=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine)){var e=
lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getTextId(d));if(null!=e)if(String.isNullOrEmpty(a.get_text()))for(;0<e.childNodes.length;)e.removeChild(e.firstChild);else{lt.Annotations.Rendering._annSvgHelpers.setClipPathAttribute(e,lt.Annotations.Rendering._annSvgHelpers.getClipPathId(c.get_stateId()));if(null!=e.childNodes&&0<e.childNodes.length){var f=e.childNodes.length;if(0<f)for(var g=0;g<f;++g)e.removeChild(e.childNodes[0])}c.get_points();d=
lt.Annotations.Rendering._annSvgHelpers.getClipPathId(d);lt.Annotations.Rendering._svgDrawingHelper.drawRect(lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getPolylineId(d)),b,a);d=new lt.Annotations.Core.AnnRectangleObject;d.get_points().clear();for(f=ss.IEnumerator.getEnumerator(this.getRenderPoints(b,a));f.moveNext();)g=f.current,d.get_points().add(g);g=d.get_angle();180===g&&(g=0);var h=lt.LeadPointD.create(d.get_bounds().get_left()+d.get_bounds().get_width()/
2,d.get_bounds().get_top()+d.get_bounds().get_height()/2),h=b.pointFromContainerCoordinates(h,a.get_fixedStateOperations()),i=lt.Annotations.Rendering.AnnSvgTextObjectRenderer._getTextRotation$3(a.get_textRotate(),h),d=d.get_rect().clone(),g=g+b.normalizeRectangle(d,a.get_fixedStateOperations()),d=i.transformRect(d);if(a.get_supportsFont()&&null!=a.get_font()){f=b.fontFromContainerCoordinates(a.get_font(),a.get_fixedStateOperations());null!=c.get_stroke()&&1<c.get_stroke().get_strokeThickness().get_value()&&
d.inflate(-c.get_stroke().get_strokeThickness().get_value()/2,-c.get_stroke().get_strokeThickness().get_value()/2);if(!lt.Annotations.Core.AnnDouble.isNaN(g)){var j=b.get_transform(),k=parseInt(j.get_m11()/Math.abs(j.get_m11())),j=parseInt(j.get_m22()/Math.abs(j.get_m22())),h=lt.LeadPointD.create(parseInt(h.get_x()),parseInt(h.get_y()));i.rotateAt(g*k*j,h.get_x(),h.get_y());lt.Annotations.Rendering._annSvgHelpers.setTransformAttribute(e,i)}if(!d.get_isEmpty()&&null!=a.get_textForeground()&&null!=
a.get_text()&&(lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(e,a.get_textForeground(),!0),g=a.get_text().replaceAll("\r",""),0<g.length)){i=g.split("\n");h=i.length;Type.canCast(a,lt.Annotations.Core.AnnTextRollupObject)&&!Type.safeCast(a,lt.Annotations.Core.AnnTextRollupObject).get_expanded()&&1<h&&(i[0]+=" ...",h=1);if(null!=i&&0<h){for(var l=0,j=lt.LeadSizeD.create(0,0),j=this.getTextSize(i[0],f,d.get_size()),l=l+j.get_height(),k=parseInt(d.get_top()+j.get_height()),g=1;g<h;++g)j=this.getTextSize(i[g],
f,d.get_size()),l+=j.get_height();a.get_verticalAlignment()===lt.Annotations.Core.AnnVerticalAlignment.center?k+=(d.get_height()-l)/2:a.get_verticalAlignment()===lt.Annotations.Core.AnnVerticalAlignment.bottom&&(k+=d.get_height()-l);for(g=0;g<h;++g)j=this.getTextSize(i[g],f,d.get_size()),l=lt.LeadRectD.create(d.get_left(),k,d.get_width(),j.get_height()),j=lt.Annotations.Rendering.AnnSvgTextObjectRenderer._alignRect$3(j,l,a.get_horizontalAlignment(),a.get_verticalAlignment(),a.get_padding()),f.set_fontSize(parseInt(f.get_fontSize())),
l=lt.Annotations.Rendering._annSvgHelpers.createTSpan(i[g],j.get_x(),j.get_y()),e.appendChild(l),k+=parseInt(j.get_height())}a.get_font().set_fontHeight(f.get_fontHeight());a.get_font().set_isDirty(f.get_isDirty())}lt.Annotations.Rendering._annSvgHelpers.setFontAttribute(e,f)}}}}}};Object.defineProperty(lt.Annotations.Rendering.AnnSvgTextObjectRenderer.prototype,"flipReverseText",{get:lt.Annotations.Rendering.AnnSvgTextObjectRenderer.prototype.get_flipReverseText,set:lt.Annotations.Rendering.AnnSvgTextObjectRenderer.prototype.set_flipReverseText,
enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnSvgTextPointerObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgTextPointerObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgTextPointerObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgTextPointerObjectRenderer.callBaseMethod(this,"createObject",[b]);var b=b.get_stateId(),c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b),a=lt.Annotations.Rendering._annSvgHelpers.createPath();
a.id=lt.Annotations.Rendering._annSvgHelpers.getPointerId(b);c.appendChild(a)},removeObject:function(b){var c=b.get_stateId();if(!String.isNullOrEmpty(c)){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c);null!=a&&lt.Annotations.Rendering._annSvgHelpers.removeElement(a,lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getPointerId(c)))}lt.Annotations.Rendering.AnnSvgTextPointerObjectRenderer.callBaseMethod(this,"removeObject",[b])},render:function(b,
c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");lt.Annotations.Rendering.AnnSvgTextPointerObjectRenderer.callBaseMethod(this,"render",[b,c]);var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a&&!a.get_stateless()&&(a=Type.safeCast(c,lt.Annotations.Core.AnnTextPointerObject),null!=a)){for(var d=[a.get_pointerPosition(),c.get_points().get_item(0)],
e=lt.Annotations.Core.Utils.distance(c.get_points().get_item(0),d[0]),f=1;f<c.get_points().get_count();++f){var g=lt.Annotations.Core.Utils.distance(c.get_points().get_item(f),d[0]);g<e&&(e=g,d[1]=c.get_points().get_item(f))}d=b.pointsFromContainerCoordinates(d,a.get_fixedStateOperations());e=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getPointerId(c.get_stateId()));null!=e&&(d=String.format("M{0} {1} L{2} {3}",d[0].get_x(),d[0].get_y(),d[1].get_x(),
d[1].get_y()),lt.Annotations.Rendering._annSvgHelpers.setPathAttribute(e,d),null!=a.get_stroke()&&lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(e,a.get_stroke(),a.get_supportsStroke()))}}};lt.Annotations.Rendering.AnnSvgTextRedactionObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgTextRedactionObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgTextRedactionObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgTextRedactionObjectRenderer.callBaseMethod(this,
"createObject",[b]);for(var c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId()),b=b.get_points().get_count()/2,a=0;a<b;a++){var d=lt.Annotations.Rendering._annSvgHelpers.createPolyline();d.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(c.id)+a.toString();c.appendChild(d)}},renderShape:function(b,c,a){var d=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=d){var e=c.get_stateId();c.get_stateId();b=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId());
if(null!=b){!e.startsWith(d.get_element().id)&&!d.get_element().contains(b)&&(c.get_stateId(),c.set_stateId(""));for(d=0;d<a.length;d++){var f=a[d],e=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(b.id)+d.toString()),f=[f.get_topLeft(),f.get_topRight(),f.get_bottomRight(),f.get_bottomLeft()];1<f.length&&(lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(e,f,!0),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(e,c.get_fill(),
c.get_supportsFill()))}}}}};lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer._getRectangles$2=function(b,c){var a=Type.safeCast(c,lt.Annotations.Core.AnnTextReviewObject),d=a.get_points().toArray();if(!d.length)return null;for(var d=b.pointsFromContainerCoordinates(d,a.get_fixedStateOperations()),a=d.length/2,e=Array(a),f=0,g=0;g<a;g++){var h=2*g,
i=d[h+0],h=d[h+1];e[f]=lt.LeadRectD.fromLTRB(i.get_x()-1,i.get_y()-1,h.get_x()+1,h.get_y()+1);f++}return e};lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer.callBaseMethod(this,"createObject",[b]);var c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId()),b=b.get_points().get_count()/2,a=lt.Annotations.Rendering._annSvgHelpers.createGroup();a.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(c.id)+
"SelectionGroup";c.appendChild(a);for(var d=0;d<b;d++){var e=lt.Annotations.Rendering._annSvgHelpers.createPolyline();e.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(c.id)+"OuterSelection"+d.toString();a.appendChild(e);e=lt.Annotations.Rendering._annSvgHelpers.createPolyline();e.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(c.id)+"InnerSelection"+d.toString();a.appendChild(e)}},render:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&
lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");if(null!=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine)){var a=lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer._getRectangles$2(b,c);null!=a&&0<a.length&&(this.createObject(c),this.renderShape(b,Type.safeCast(c,lt.Annotations.Core.AnnTextReviewObject),a));if(!c.get_isSelected()){var a=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId()),d=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(a.id)+
"SelectionGroup");null!=d&&a.removeChild(d)}lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer.callBaseMethod(this,"render",[b,c])}},renderSelection:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");var a=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=a){var d=Type.safeCast(c,lt.Annotations.Core.AnnTextReviewObject),e=lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer._getRectangles$2(b,
c);if(null!=e&&0<e.length&&null!=a){var f=d.get_stateId();d.get_stateId();var g=lt.Annotations.Rendering._annSvgHelpers.getElementById(d.get_stateId());if(null!=g){!f.startsWith(a.get_element().id)&&!a.get_element().contains(g)&&(d.get_stateId(),d.set_stateId(""));for(a=0;a<e.length;a++){var h=e[a],i=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(g.id)+"OuterSelection"+a.toString()),f=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(g.id)+
"InnerSelection"+a.toString()),h=[h.get_topLeft(),h.get_topRight(),h.get_bottomRight(),h.get_bottomLeft()];if(1<h.length){var j=b.strokeFromContainerCoordinates(d.get_selectionStroke(),d.get_fixedStateOperations()).clone();lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(i,h,!0);lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(i,lt.Annotations.Core.AnnSolidColorBrush.create("transparent"),d.get_supportsFill());lt.Annotations.Rendering._annSvgHelpers.setStrokeOpacityAttribute(i,0.5);
lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(i,j,d.get_supportsSelectionStroke());i=lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("white"),lt.LeadLengthD.create(j.get_strokeThickness().get_value()-2));lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(f,h,!0);lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(f,lt.Annotations.Core.AnnSolidColorBrush.create("transparent"),d.get_supportsFill());lt.Annotations.Rendering._annSvgHelpers.setStrokeOpacityAttribute(f,
0.5);lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(f,i,d.get_supportsSelectionStroke())}}}}}}};lt.Annotations.Rendering.AnnSvgTextRollupObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgTextRollupObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgTextRollupObjectRenderer.prototype={getRenderPoints:function(b,c){null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("annObject");
var a=lt.Annotations.Rendering.AnnSvgTextRollupObjectRenderer.callBaseMethod(this,"getRenderPoints",[b,c]),d=Type.safeCast(c,lt.Annotations.Core.AnnTextRollupObject);if(null!=d&&!d.get_expanded()){var e=lt.Annotations.Core.Utils.distance(a[1],a[2]);null!=d.get_font()&&(e=this.getTextSize("gM",d.get_font(),d.get_bounds().get_size()),e=b.sizeToContainerCoordinates(e),e=e.get_height());d=lt.Annotations.Core.Utils.getUnitVector(a[1],a[2]);a[2]=lt.Annotations.Core.Utils.transformPoint(d,lt.LeadLengthD.create(e),
a[1]);d=lt.Annotations.Core.Utils.getUnitVector(a[0],a[3]);a[3]=lt.Annotations.Core.Utils.transformPoint(d,lt.LeadLengthD.create(e),a[0])}return a}};lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer.prototype={_thickness$3:0.15,get_thickness:function(){return this._thickness$3},set_thickness:function(b){if(0>b||1<b)throw new lt.ArgumentOutOfRangeException("Thickness",
b,"Must be a value between 0 and 1");return this._thickness$3=b},_position$3:0.85,get_position:function(){return this._position$3},set_position:function(b){if(0>b||1<b)throw new lt.ArgumentOutOfRangeException("Position",b,"Must be a value between 0 and 1");return this._position$3=b},createObject:function(b){lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer.callBaseMethod(this,"createObject",[b]);for(var c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId()),b=b.get_points().get_count()/
2,a=0;a<b;a++){var d=lt.Annotations.Rendering._annSvgHelpers.createPolyline();d.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(c.id)+a.toString();c.appendChild(d)}},renderShape:function(b,c,a){if(!(null==c||null==b)&&null!=Type.safeCast(c,lt.Annotations.Core.AnnTextUnderlineObject)){var d=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=d){var e=c.get_stateId();c.get_stateId();var f=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId());
if(null!=f){!e.startsWith(d.get_element().id)&&!d.get_element().contains(f)&&(c.get_stateId(),c.set_stateId(""));b=this._getRotationAngle(b.get_transform());for(d=0;d<a.length;d++){var e=a[d],g=lt.LeadRectD.get_empty();if(90===Math.abs(b))var g=this.get_thickness()?Math.max(e.get_width()*this.get_thickness(),1):1,h=0,h=-90===b?e.get_right()-(e.get_width()*this.get_position()+g/2):e.get_width()*this.get_position()-g/2+e.get_x(),g=lt.LeadRectD.create(h,e.get_y(),g,e.get_height());else g=this.get_thickness()?
Math.max(e.get_height()*this.get_thickness(),1):1,h=0,h=180===b?e.get_bottom()-(e.get_height()*this.get_position()+g/2):e.get_height()*this.get_position()-g/2+e.get_top(),g=lt.LeadRectD.create(e.get_x(),h,e.get_width(),g);e=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(f.id)+d.toString());g=[g.get_topLeft(),g.get_topRight(),g.get_bottomRight(),g.get_bottomLeft()];1<g.length&&(lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(e,g,
!0),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(e,c.get_fill(),c.get_supportsFill()))}}}}}};Object.defineProperty(lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer.prototype,"thickness",{get:lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer.prototype.get_thickness,set:lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer.prototype.set_thickness,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer.prototype,
"position",{get:lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer.prototype.get_position,set:lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer.prototype.set_position,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnSvgThumbStyle=function(){this._size=lt.LeadSizeD.create(0,0);this._size=lt.LeadSizeD.create(0,0);this._fill=lt.Annotations.Core.AnnSolidColorBrush.create("white");this._stroke=lt.Annotations.Core.AnnStroke.create(lt.Annotations.Core.AnnSolidColorBrush.create("black"),
lt.LeadLengthD.create(1));this._isVisible=!0};lt.Annotations.Rendering.AnnSvgThumbStyle.prototype={_renderer:null,get_renderer:function(){return this._renderer},set_renderer:function(b){return this._renderer=b},clone:function(){var b=this.create();b._size=this._size.clone();b._fill=null!=this._fill?this._fill.clone():null;b._stroke=null!=this._stroke?this._stroke.clone():null;b._isVisible=this._isVisible;return b},get_size:function(){return this._size.clone()},set_size:function(b){null==b&&(b=lt.LeadSizeD.create(0,
0));this._size=b.clone();return b},_fill:null,get_fill:function(){return this._fill},set_fill:function(b){return this._fill=b},_stroke:null,get_stroke:function(){return this._stroke},set_stroke:function(b){return this._stroke=b},_isVisible:!1,get_isVisible:function(){return this._isVisible},set_isVisible:function(b){return this._isVisible=b},renderHitTest:function(b,c,a,d){if(!this.get_isVisible())return!1;var e=lt.LeadRectD.get_empty(),c=d.pointFromContainerCoordinates(c,lt.Annotations.Core.AnnFixedStateOperations.none),
b=d.pointFromContainerCoordinates(b,lt.Annotations.Core.AnnFixedStateOperations.none),e=d.sizeFromContainerCoordinates(this.get_size()),e=lt.LeadRectD.create(b.get_x()-e.get_width()/2,b.get_y()-e.get_height()/2,e.get_width(),e.get_height()),a=d.lengthFromContainerCoordinates(lt.LeadLengthD.create(a),lt.Annotations.Core.AnnFixedStateOperations.none);e.inflate(a,a);return e.containsPoint(c)},hitTest:function(b,c,a){if(!this.get_isVisible())return!1;var d=lt.LeadRectD.get_empty();null!=this._renderer?
(d=this._renderer.get_renderingEngine().get_container().get_mapper(),c=d.pointFromContainerCoordinates(c,lt.Annotations.Core.AnnFixedStateOperations.none),b=d.pointFromContainerCoordinates(b,lt.Annotations.Core.AnnFixedStateOperations.none),d=d.sizeFromContainerCoordinates(this.get_size()),d=lt.LeadRectD.create(b.get_x()-d.get_width()/2,b.get_y()-d.get_height()/2,d.get_width(),d.get_height())):d=lt.LeadRectD.create(b.get_x()-this.get_size().get_width()/2,b.get_y()-this.get_size().get_height()/2,this.get_size().get_width(),
this.get_size().get_height());d.inflate(a,a);return d.containsPoint(c)},render:function(b,c,a,d){null==c&&lt.Annotations.Core.ExceptionHelper.argumentNullException("mapper");null==b&&lt.Annotations.Core.ExceptionHelper.argumentNullException("renderer");this.get_isVisible()&&!b.get_renderingEngine().get_stateless()&&(a=c.pointFromContainerCoordinates(a,d),c=c.sizeFromContainerCoordinates(this.get_size()),c=lt.LeadRectD.create(a.get_x()-c.get_width()/2,a.get_y()-c.get_height()/2,c.get_width(),c.get_height()),
b=String.format("Annotation_Svg_{0}",b.get_renderingEngine().get_element().id),this.addPath(lt.Annotations.Rendering._annSvgHelpers.getElementById(b+"AnnThumbGroup"),c))}};Object.defineProperty(lt.Annotations.Rendering.AnnSvgThumbStyle.prototype,"renderer",{get:lt.Annotations.Rendering.AnnSvgThumbStyle.prototype.get_renderer,set:lt.Annotations.Rendering.AnnSvgThumbStyle.prototype.set_renderer,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnSvgThumbStyle.prototype,
"size",{get:lt.Annotations.Rendering.AnnSvgThumbStyle.prototype.get_size,set:lt.Annotations.Rendering.AnnSvgThumbStyle.prototype.set_size,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnSvgThumbStyle.prototype,"fill",{get:lt.Annotations.Rendering.AnnSvgThumbStyle.prototype.get_fill,set:lt.Annotations.Rendering.AnnSvgThumbStyle.prototype.set_fill,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnSvgThumbStyle.prototype,"stroke",{get:lt.Annotations.Rendering.AnnSvgThumbStyle.prototype.get_stroke,
set:lt.Annotations.Rendering.AnnSvgThumbStyle.prototype.set_stroke,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnSvgThumbStyle.prototype,"isVisible",{get:lt.Annotations.Rendering.AnnSvgThumbStyle.prototype.get_isVisible,set:lt.Annotations.Rendering.AnnSvgThumbStyle.prototype.set_isVisible,enumerable:!0,configurable:!0});lt.Annotations.Rendering.AnnSvgRectangleThumbStyle=function(){lt.Annotations.Rendering.AnnSvgRectangleThumbStyle.initializeBase(this)};lt.Annotations.Rendering.AnnSvgRectangleThumbStyle.prototype=
{create:function(){return new lt.Annotations.Rendering.AnnSvgRectangleThumbStyle},addPath:function(b,c){if(null!=b){var a=lt.Annotations.Rendering._annSvgHelpers.createRect();lt.Annotations.Rendering._annSvgHelpers.setRectAttribute(a,c);b.appendChild(a);lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(a,this.get_fill(),!0);lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(a,this.get_stroke(),!0)}}};lt.Annotations.Rendering.AnnSvgEllipseThumbStyle=function(){lt.Annotations.Rendering.AnnSvgEllipseThumbStyle.initializeBase(this)};
lt.Annotations.Rendering.AnnSvgEllipseThumbStyle.prototype={create:function(){return new lt.Annotations.Rendering.AnnSvgEllipseThumbStyle},addPath:function(b,c){if(null!=b){var a=lt.Annotations.Rendering._annSvgHelpers.createEllipse();lt.Annotations.Rendering._annSvgHelpers.setEllipseAttribute(a,c);lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(a,this.get_fill(),!0);lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(a,this.get_stroke(),!0);b.appendChild(a)}}};lt.Annotations.Rendering.AnnSvgTextHiliteObjectRenderer=
function(){lt.Annotations.Rendering.AnnSvgTextHiliteObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgTextHiliteObjectRenderer.prototype={createObject:function(b){lt.Annotations.Rendering.AnnSvgTextHiliteObjectRenderer.callBaseMethod(this,"createObject",[b]);for(var c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId()),b=b.get_points().get_count()/2,a=0;a<b;a++){var d=lt.Annotations.Rendering._annSvgHelpers.createPolyline();d.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(c.id)+
a.toString();c.appendChild(d)}},renderShape:function(b,c,a){var d=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);if(null!=d){var e=c.get_stateId();c.get_stateId();b=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId());if(null!=b){!e.startsWith(d.get_element().id)&&!d.get_element().contains(b)&&(c.get_stateId(),c.set_stateId(""));for(d=0;d<a.length;d++){var f=a[d],e=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(b.id)+
d.toString()),f=[f.get_topLeft(),f.get_topRight(),f.get_bottomRight(),f.get_bottomLeft()];1<f.length&&(lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(e,f,!0),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(e,c.get_fill(),c.get_supportsFill()),lt.Annotations.Rendering._annSvgHelpers.setFillOpacityAttribute(e,0.5))}}}}};lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer=function(){lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer.initializeBase(this)};lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer.prototype=
{_thickness$3:0.15,get_thickness:function(){return this._thickness$3},set_thickness:function(b){if(0>b||1<b)throw new lt.ArgumentOutOfRangeException("Thickness",b,"Must be a value between 0 and 1");return this._thickness$3=b},_position$3:0.5,get_position:function(){return this._position$3},set_position:function(b){if(0>b||1<b)throw new lt.ArgumentOutOfRangeException("Position",b,"Must be a value between 0 and 1");return this._position$3=b},createObject:function(b){lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer.callBaseMethod(this,
"createObject",[b]);for(var c=lt.Annotations.Rendering._annSvgHelpers.getElementById(b.get_stateId()),b=b.get_points().get_count()/2,a=0;a<b;a++){var d=lt.Annotations.Rendering._annSvgHelpers.createPolyline();d.id=lt.Annotations.Rendering._annSvgHelpers.getRectId(c.id)+a.toString();c.appendChild(d)}},renderShape:function(b,c,a){if(!(null==c||null==b)&&null!=Type.safeCast(c,lt.Annotations.Core.AnnTextStrikeoutObject)){var d=Type.safeCast(this.get_renderingEngine(),lt.Annotations.Rendering.AnnSvgRenderingEngine);
if(null!=d){var e=c.get_stateId();c.get_stateId();var f=lt.Annotations.Rendering._annSvgHelpers.getElementById(c.get_stateId());if(null!=f){!e.startsWith(d.get_element().id)&&!d.get_element().contains(f)&&(c.get_stateId(),c.set_stateId(""));b=this._getRotationAngle(b.get_transform());for(d=0;d<a.length;d++){var e=a[d],g=lt.LeadRectD.get_empty();if(90===Math.abs(b))var g=this.get_thickness()?Math.max(e.get_width()*this.get_thickness(),1):1,h=e.get_width()*this.get_position()-g/2+e.get_x(),g=lt.LeadRectD.create(h,
e.get_y(),g,e.get_height());else g=this.get_thickness()?Math.max(e.get_height()*this.get_thickness(),1):1,h=e.get_height()*this.get_position()-g/2+e.get_top(),g=lt.LeadRectD.create(e.get_x(),h,e.get_width(),g);e=lt.Annotations.Rendering._annSvgHelpers.getElementById(lt.Annotations.Rendering._annSvgHelpers.getRectId(f.id)+d.toString());g=[g.get_topLeft(),g.get_topRight(),g.get_bottomRight(),g.get_bottomLeft()];1<g.length&&(lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(e,g,!0),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(e,
c.get_fill(),c.get_supportsFill()))}}}}}};Object.defineProperty(lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer.prototype,"thickness",{get:lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer.prototype.get_thickness,set:lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer.prototype.set_thickness,enumerable:!0,configurable:!0});Object.defineProperty(lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer.prototype,"position",{get:lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer.prototype.get_position,
set:lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer.prototype.set_position,enumerable:!0,configurable:!0});lt.Annotations.Rendering._svgDrawingHelper=function(){};lt.Annotations.Rendering._svgDrawingHelper.drawCurve=function(b,c,a,d){c=d?lt.Annotations.Core.Utils.splineClosedCurve(c,a):lt.Annotations.Core.Utils.splineCurve(c,a);lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(b,c,!1)};lt.Annotations.Rendering._svgDrawingHelper.drawClosedCurve=function(){};lt.Annotations.Rendering._svgDrawingHelper.drawEllipse=
function(b,c,a){a=Type.safeCast(a,lt.Annotations.Core.AnnEllipseObject);if(null!=a){var d=lt.LeadPointD.create(a.get_bounds().get_left()+a.get_bounds().get_width()/2,a.get_bounds().get_top()+a.get_bounds().get_height()/2),d=c.pointFromContainerCoordinates(d,a.get_fixedStateOperations()),e=new lt.LeadMatrix,d=lt.LeadPointD.create(parseInt(d.get_x()),parseInt(d.get_y())),f=a.get_rect().clone();if(c=a.get_angle()+c.normalizeRectangle(f,a.get_fixedStateOperations()))e.rotateAt(c,d.get_x(),d.get_y()),
lt.Annotations.Rendering._annSvgHelpers.setTransformAttribute(b,e);lt.Annotations.Rendering._annSvgHelpers.setEllipseAttribute(b,f);lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(b,a.get_fill(),a.get_supportsFill());lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(b,a.get_stroke(),a.get_supportsStroke())}};lt.Annotations.Rendering._svgDrawingHelper.drawPolyline=function(b,c,a){var d=Type.safeCast(a,lt.Annotations.Core.AnnPolylineObject),c=c.pointsFromContainerCoordinates(a.get_points().toArray(),
d.get_fixedStateOperations());1<c.length&&(lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(b,c,d.get_isClosed()),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(b,d.get_fill(),d.get_supportsFill()),lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(b,d.get_stroke(),d.get_supportsStroke()))};lt.Annotations.Rendering._svgDrawingHelper.drawPicture=function(b,c,a,d,e){if(null!=c&&(d=c.get_source(),!String.isNullOrEmpty(d))){var f;null!=c.get_internalData()?f=c.get_internalData():
(f=new Image,c.set_internalData(f));f.src=d;d=lt.LeadRectD.create(0,0,f.naturalWidth,f.naturalHeight);d=e.rectToContainerCoordinates(d);c.set_width(d.get_width());c.set_height(d.get_height());lt.Annotations.Rendering._annSvgHelpers.setRectAttribute(b,a);lt.Annotations.Rendering._annSvgHelpers.setPreserveAspectRatioAttribute(b,"none");lt.Annotations.Rendering._annSvgHelpers.setSourceAttribute(b,c.get_source());c.set_isLoaded(!0)}};lt.Annotations.Rendering._svgDrawingHelper.drawHilite=function(b,c,
a){var d=Type.safeCast(a,lt.Annotations.Core.AnnHiliteObject),c=c.pointsFromContainerCoordinates(a.get_points().toArray(),d.get_fixedStateOperations());1<c.length&&(lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(b,c,!0),c=d.get_hiliteColor(),c=lt.Annotations.Core.AnnSolidColorBrush.create(c),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(b,c,d.get_supportsFill()),lt.Annotations.Rendering._annSvgHelpers.setFillOpacityAttribute(b,0.5),lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(b,
d.get_stroke(),d.get_supportsStroke()))};lt.Annotations.Rendering._svgDrawingHelper.drawRect=function(b,c,a){var d=Type.safeCast(a,lt.Annotations.Core.AnnRectangleObject),c=c.pointsFromContainerCoordinates(a.get_points().toArray(),d.get_fixedStateOperations());1<c.length&&(lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute(b,c,!0),lt.Annotations.Rendering._annSvgHelpers.setFillAttribute(b,d.get_fill(),d.get_supportsFill()),lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute(b,d.get_stroke(),
d.get_supportsStroke()))};lt.Annotations.Rendering._annSvgHelpers=function(){};lt.Annotations.Rendering._annSvgHelpers.createElementNS=function(b,c){return document.createElementNS(b,c)};lt.Annotations.Rendering._annSvgHelpers.appendElement=function(b,c){b.appendChild(c)};lt.Annotations.Rendering._annSvgHelpers.removeElement=function(b,c){b.removeChild(c)};lt.Annotations.Rendering._annSvgHelpers.getElementById=function(b){return document.getElementById(b)};lt.Annotations.Rendering._annSvgHelpers.createRoot=
function(){return lt.Annotations.Rendering._annSvgHelpers.createElementNS("http://www.w3.org/2000/svg","svg")};lt.Annotations.Rendering._annSvgHelpers.createRect=function(){return lt.Annotations.Rendering._annSvgHelpers.createElementNS("http://www.w3.org/2000/svg","rect")};lt.Annotations.Rendering._annSvgHelpers.createClipPath=function(){return lt.Annotations.Rendering._annSvgHelpers.createElementNS("http://www.w3.org/2000/svg","clipPath")};lt.Annotations.Rendering._annSvgHelpers.createImage=function(){return lt.Annotations.Rendering._annSvgHelpers.createElementNS("http://www.w3.org/2000/svg",
"image")};lt.Annotations.Rendering._annSvgHelpers.createTSpan=function(b,c,a){var d=lt.Annotations.Rendering._annSvgHelpers.createElementNS("http://www.w3.org/2000/svg","tspan");d.setAttribute("x",c);d.setAttribute("y",a);d.appendChild(document.createTextNode(b));return d};lt.Annotations.Rendering._annSvgHelpers.createGroup=function(){return lt.Annotations.Rendering._annSvgHelpers.createElementNS("http://www.w3.org/2000/svg","g")};lt.Annotations.Rendering._annSvgHelpers.createEllipse=function(){return lt.Annotations.Rendering._annSvgHelpers.createElementNS("http://www.w3.org/2000/svg",
"ellipse")};lt.Annotations.Rendering._annSvgHelpers.createText=function(){return lt.Annotations.Rendering._annSvgHelpers.createElementNS("http://www.w3.org/2000/svg","text")};lt.Annotations.Rendering._annSvgHelpers.createPolyline=function(){return lt.Annotations.Rendering._annSvgHelpers.createElementNS("http://www.w3.org/2000/svg","polyline")};lt.Annotations.Rendering._annSvgHelpers.createPath=function(){return lt.Annotations.Rendering._annSvgHelpers.createElementNS("http://www.w3.org/2000/svg","path")};
lt.Annotations.Rendering._annSvgHelpers.setTransformAttribute=function(b,c){b.setAttribute("transform",String.format("matrix({0}, {1}, {2}, {3}, {4}, {5})",c.get_m11(),c.get_m12(),c.get_m21(),c.get_m22(),c.get_offsetX(),c.get_offsetY()))};lt.Annotations.Rendering._annSvgHelpers.setFillAttribute=function(b,c,a){c=Type.safeCast(c,lt.Annotations.Core.AnnSolidColorBrush);null!=c&&a?lt.Annotations.Core.Utils.compare(c.get_color(),"transparent",!0)?b.setAttribute("fill","none"):b.setAttribute("fill",c.get_color()):
b.setAttribute("fill","none")};lt.Annotations.Rendering._annSvgHelpers.setVisibleAttribute=function(b,c){var a="visible";c||(a="hidden");b.setAttribute("visibility",a)};lt.Annotations.Rendering._annSvgHelpers.setPathAttribute=function(b,c){b.setAttribute("d",c)};lt.Annotations.Rendering._annSvgHelpers.setFillOpacityAttribute=function(b,c){b.setAttribute("fill-opacity",c)};lt.Annotations.Rendering._annSvgHelpers.setStrokeOpacityAttribute=function(b,c){b.setAttribute("stroke-opacity",c)};lt.Annotations.Rendering._annSvgHelpers.setStrokeAttribute=
function(b,c,a){if(null!=c){var d=Type.safeCast(c.get_stroke(),lt.Annotations.Core.AnnSolidColorBrush);null!=d&&a?(b.setAttribute("stroke",d.get_color()),b.setAttribute("stroke-width",c.get_strokeThickness().get_value())):b.setAttribute("stroke","none");switch(c.get_strokeEndLineCap()){case lt.Annotations.Core.AnnStrokeLineCap.square:a="square";break;case lt.Annotations.Core.AnnStrokeLineCap.round:a="round";break;default:a="flat"}d="";c=c.get_strokeDashArray();if(null!=c&&0<c.length){for(var e=0;e<
c.length-1;++e)d+=c[e].toString()+",";d+=c[c.length-1].toString()+",";b.setAttribute("stroke-dasharray",d)}b.setAttribute("stroke-linecap",a)}};lt.Annotations.Rendering._annSvgHelpers.setPointsAttribute=function(b,c,a){var d="";if(1<c.length){for(var d=String.format("{0}, {1}",c[0].get_x(),c[0].get_y()),e=1;e<c.length;e++)d+=String.format(" {0}, {1}",c[e].get_x(),c[e].get_y());a&&(d+=String.format(" {0}, {1}",c[0].get_x(),c[0].get_y()))}b.setAttribute("points",d)};lt.Annotations.Rendering._annSvgHelpers.setEllipseAttribute=
function(b,c){var a=c.get_width()/2,d=c.get_height()/2,e=c.get_left()+a,f=c.get_top()+d;b.setAttribute("cx",e);b.setAttribute("cy",f);b.setAttribute("rx",a);b.setAttribute("ry",d)};lt.Annotations.Rendering._annSvgHelpers.setBaselineAttribute=function(b,c){b.setAttribute("alignment-baseline",c)};lt.Annotations.Rendering._annSvgHelpers.setRectAttribute=function(b,c){b.setAttribute("x",String.format("{0}px",c.get_left()));b.setAttribute("y",String.format("{0}px",c.get_top()));b.setAttribute("width",
String.format("{0}px",c.get_width()));b.setAttribute("height",String.format("{0}px",c.get_height()))};lt.Annotations.Rendering._annSvgHelpers.setPreserveAspectRatioAttribute=function(b,c){b.setAttribute("preserveAspectRatio",c)};lt.Annotations.Rendering._annSvgHelpers.setSourceAttribute=function(b,c){b.setAttributeNS("http://www.w3.org/1999/xlink","href",c)};lt.Annotations.Rendering._annSvgHelpers.setClipPathAttribute=function(b,c){var a=String.format("url(#{0})",c);b.setAttribute("clip-path",a)};
lt.Annotations.Rendering._annSvgHelpers.setFontAttribute=function(b,c){b.setAttribute("font-size",c.get_fontSize()+"pt");b.setAttribute("font-family",c.get_fontFamilyName());var a="",d="";switch(c.get_fontWeight()){case lt.Annotations.Core.AnnFontWeight.bold:case lt.Annotations.Core.AnnFontWeight.extraBold:case lt.Annotations.Core.AnnFontWeight.semiBold:d="bold";break;default:d=""}switch(c.get_fontStyle()){case lt.Annotations.Core.AnnFontStyle.oblique:a="Oblique";break;case lt.Annotations.Core.AnnFontStyle.italic:a=
"italic";break;default:a="normal"}b.setAttribute("font-weight",d);b.setAttribute("font-style",a)};lt.Annotations.Rendering._annSvgHelpers.getLockId=function(b){return String.format("{0}.{1}",b,"lock")};lt.Annotations.Rendering._annSvgHelpers.getLabelsId=function(b){return String.format("{0}.{1}",b,"labels")};lt.Annotations.Rendering._annSvgHelpers.getClipPathId=function(b){return String.format("{0}.{1}",b,"clipPath")};lt.Annotations.Rendering._annSvgHelpers.getImageId=function(b){return String.format("{0}.{1}",
b,"image")};lt.Annotations.Rendering._annSvgHelpers.getPolylineId=function(b){return String.format("{0}.{1}",b,"polyline")};lt.Annotations.Rendering._annSvgHelpers.getSecondPolylineId=function(b){return String.format("{0}.{1}",b,"secondPolyline")};lt.Annotations.Rendering._annSvgHelpers.getEllipseId=function(b){return String.format("{0}.{1}",b,"ellipse")};lt.Annotations.Rendering._annSvgHelpers.getGroupId=function(b){return String.format("{0}.{1}",b,"group")};lt.Annotations.Rendering._annSvgHelpers.getShadowId=
function(b){return String.format("{0}.{1}",b,"shadow")};lt.Annotations.Rendering._annSvgHelpers.getArrowId=function(b){return String.format("{0}.{1}",b,"arrow")};lt.Annotations.Rendering._annSvgHelpers.getRectId=function(b){return String.format("{0}.{1}",b,"rectangle")};lt.Annotations.Rendering._annSvgHelpers.getTextId=function(b){return String.format("{0}.{1}",b,"text")};lt.Annotations.Rendering._annSvgHelpers.getPointerId=function(b){return String.format("{0}.{1}",b,"pointer")};lt.Annotations.Rendering._lT_VersionNumber.registerClass("lt.Annotations.Rendering._lT_VersionNumber");
lt.Annotations.Rendering.AnnHtml5RenderingEngine.registerClass("lt.Annotations.Rendering.AnnHtml5RenderingEngine",lt.Annotations.Core.AnnRenderingEngine);lt.Annotations.Rendering.AnnSvgRenderingEngine.registerClass("lt.Annotations.Rendering.AnnSvgRenderingEngine",lt.Annotations.Core.AnnRenderingEngine);lt.Annotations.Rendering._colorUtil.registerClass("lt.Annotations.Rendering._colorUtil");lt.Annotations.Rendering.AnnObjectRenderer.registerClass("lt.Annotations.Rendering.AnnObjectRenderer",null,lt.Annotations.Core.IAnnObjectRenderer);
lt.Annotations.Rendering.AnnPolylineObjectRenderer.registerClass("lt.Annotations.Rendering.AnnPolylineObjectRenderer",lt.Annotations.Rendering.AnnObjectRenderer);lt.Annotations.Rendering.AnnPolyRulerObjectRenderer.registerClass("lt.Annotations.Rendering.AnnPolyRulerObjectRenderer",lt.Annotations.Rendering.AnnPolylineObjectRenderer);lt.Annotations.Rendering.AnnCrossProductObjectRenderer.registerClass("lt.Annotations.Rendering.AnnCrossProductObjectRenderer",lt.Annotations.Rendering.AnnPolyRulerObjectRenderer);
lt.Annotations.Rendering.AnnCurveObjectRenderer.registerClass("lt.Annotations.Rendering.AnnCurveObjectRenderer",lt.Annotations.Rendering.AnnPolylineObjectRenderer);lt.Annotations.Rendering.AnnEllipseObjectRenderer.registerClass("lt.Annotations.Rendering.AnnEllipseObjectRenderer",lt.Annotations.Rendering.AnnObjectRenderer);lt.Annotations.Rendering.AnnFreehandHotspotObjectRenderer.registerClass("lt.Annotations.Rendering.AnnFreehandHotspotObjectRenderer",lt.Annotations.Rendering.AnnPolylineObjectRenderer);
lt.Annotations.Rendering.AnnRectangleObjectRenderer.registerClass("lt.Annotations.Rendering.AnnRectangleObjectRenderer",lt.Annotations.Rendering.AnnObjectRenderer);lt.Annotations.Rendering.AnnHiliteObjectRenderer.registerClass("lt.Annotations.Rendering.AnnHiliteObjectRenderer",lt.Annotations.Rendering.AnnRectangleObjectRenderer);lt.Annotations.Rendering.AnnHotspotObjectRenderer.registerClass("lt.Annotations.Rendering.AnnHotspotObjectRenderer",lt.Annotations.Rendering.AnnRectangleObjectRenderer);lt.Annotations.Rendering.AnnImageObjectRenderer.registerClass("lt.Annotations.Rendering.AnnImageObjectRenderer",
lt.Annotations.Rendering.AnnRectangleObjectRenderer);lt.Annotations.Rendering.AnnLabelRenderer.registerClass("lt.Annotations.Rendering.AnnLabelRenderer",null,lt.Annotations.Core.IAnnLabelRenderer);lt.Annotations.Rendering.AnnTextObjectRenderer.registerClass("lt.Annotations.Rendering.AnnTextObjectRenderer",lt.Annotations.Rendering.AnnRectangleObjectRenderer);lt.Annotations.Rendering.AnnNoteObjectRenderer.registerClass("lt.Annotations.Rendering.AnnNoteObjectRenderer",lt.Annotations.Rendering.AnnTextObjectRenderer);
lt.Annotations.Rendering.AnnPointerObjectRenderer.registerClass("lt.Annotations.Rendering.AnnPointerObjectRenderer",lt.Annotations.Rendering.AnnPolylineObjectRenderer);lt.Annotations.Rendering.AnnPointObjectRenderer.registerClass("lt.Annotations.Rendering.AnnPointObjectRenderer",lt.Annotations.Rendering.AnnObjectRenderer);lt.Annotations.Rendering.AnnProtractorObjectRenderer.registerClass("lt.Annotations.Rendering.AnnProtractorObjectRenderer",lt.Annotations.Rendering.AnnPolyRulerObjectRenderer);lt.Annotations.Rendering.AnnRubberStampObjectRenderer.registerClass("lt.Annotations.Rendering.AnnRubberStampObjectRenderer",
lt.Annotations.Rendering.AnnRectangleObjectRenderer);lt.Annotations.Rendering.AnnStampObjectRenderer.registerClass("lt.Annotations.Rendering.AnnStampObjectRenderer",lt.Annotations.Rendering.AnnTextObjectRenderer);lt.Annotations.Rendering.AnnStickyNoteObjectRenderer.registerClass("lt.Annotations.Rendering.AnnStickyNoteObjectRenderer",lt.Annotations.Rendering.AnnObjectRenderer);lt.Annotations.Rendering.AnnTextPointerObjectRenderer.registerClass("lt.Annotations.Rendering.AnnTextPointerObjectRenderer",
lt.Annotations.Rendering.AnnTextObjectRenderer);lt.Annotations.Rendering.AnnTextReviewObjectRenderer.registerClass("lt.Annotations.Rendering.AnnTextReviewObjectRenderer",lt.Annotations.Rendering.AnnObjectRenderer);lt.Annotations.Rendering.AnnTextRedactionObjectRenderer.registerClass("lt.Annotations.Rendering.AnnTextRedactionObjectRenderer",lt.Annotations.Rendering.AnnTextReviewObjectRenderer);lt.Annotations.Rendering.AnnTextHiliteObjectRenderer.registerClass("lt.Annotations.Rendering.AnnTextHiliteObjectRenderer",
lt.Annotations.Rendering.AnnTextReviewObjectRenderer);lt.Annotations.Rendering.AnnTextRollupObjectRenderer.registerClass("lt.Annotations.Rendering.AnnTextRollupObjectRenderer",lt.Annotations.Rendering.AnnNoteObjectRenderer);lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer.registerClass("lt.Annotations.Rendering.AnnTextStrikeoutObjectRenderer",lt.Annotations.Rendering.AnnTextReviewObjectRenderer);lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer.registerClass("lt.Annotations.Rendering.AnnTextUnderlineObjectRenderer",
lt.Annotations.Rendering.AnnTextReviewObjectRenderer);lt.Annotations.Rendering.AnnThumbStyle.registerClass("lt.Annotations.Rendering.AnnThumbStyle",null,lt.Annotations.Core.IAnnThumbStyle);lt.Annotations.Rendering.AnnRectangleThumbStyle.registerClass("lt.Annotations.Rendering.AnnRectangleThumbStyle",lt.Annotations.Rendering.AnnThumbStyle);lt.Annotations.Rendering.AnnEllipseThumbStyle.registerClass("lt.Annotations.Rendering.AnnEllipseThumbStyle",lt.Annotations.Rendering.AnnThumbStyle);lt.Annotations.Rendering.AnnEncryptObjectRenderer.registerClass("lt.Annotations.Rendering.AnnEncryptObjectRenderer",
lt.Annotations.Rendering.AnnRectangleObjectRenderer);lt.Annotations.Rendering.AnnMediaObjectRenderer.registerClass("lt.Annotations.Rendering.AnnMediaObjectRenderer",lt.Annotations.Rendering.AnnHotspotObjectRenderer);lt.Annotations.Rendering.AnnSvgObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgObjectRenderer",lt.Annotations.Rendering.AnnObjectRenderer);lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer",lt.Annotations.Rendering.AnnSvgObjectRenderer);
lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer",lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer);lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgCrossProductObjectRenderer",lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer);lt.Annotations.Rendering.AnnSvgCurveObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgCurveObjectRenderer",lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer);
lt.Annotations.Rendering.AnnSvgEllipseObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgEllipseObjectRenderer",lt.Annotations.Rendering.AnnSvgObjectRenderer);lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer",lt.Annotations.Rendering.AnnSvgObjectRenderer);lt.Annotations.Rendering.AnnSvgEncryptObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgEncryptObjectRenderer",lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer);
lt.Annotations.Rendering.AnnSvgFreehandHotspotObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgFreehandHotspotObjectRenderer",lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer);lt.Annotations.Rendering.AnnSvgHiliteObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgHiliteObjectRenderer",lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer);lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer",lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer);
lt.Annotations.Rendering.AnnSvgMediaObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgMediaObjectRenderer",lt.Annotations.Rendering.AnnSvgHotspotObjectRenderer);lt.Annotations.Rendering.AnnSvgTextObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgTextObjectRenderer",lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer);lt.Annotations.Rendering.AnnSvgNoteObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgNoteObjectRenderer",lt.Annotations.Rendering.AnnSvgTextObjectRenderer);
lt.Annotations.Rendering.AnnSvgPointerObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgPointerObjectRenderer",lt.Annotations.Rendering.AnnSvgPolylineObjectRenderer);lt.Annotations.Rendering.AnnSvgPointObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgPointObjectRenderer",lt.Annotations.Rendering.AnnSvgObjectRenderer);lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgProtractorObjectRenderer",lt.Annotations.Rendering.AnnSvgPolyRulerObjectRenderer);
lt.Annotations.Rendering.AnnSvgRubberStampObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgRubberStampObjectRenderer",lt.Annotations.Rendering.AnnSvgRectangleObjectRenderer);lt.Annotations.Rendering.AnnSvgStampObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgStampObjectRenderer",lt.Annotations.Rendering.AnnSvgTextObjectRenderer);lt.Annotations.Rendering.AnnSvgStickyNoteObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgStickyNoteObjectRenderer",lt.Annotations.Rendering.AnnSvgObjectRenderer);
lt.Annotations.Rendering.AnnSvgTextPointerObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgTextPointerObjectRenderer",lt.Annotations.Rendering.AnnSvgTextObjectRenderer);lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer",lt.Annotations.Rendering.AnnSvgObjectRenderer);lt.Annotations.Rendering.AnnSvgTextRedactionObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgTextRedactionObjectRenderer",lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer);
lt.Annotations.Rendering.AnnSvgTextRollupObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgTextRollupObjectRenderer",lt.Annotations.Rendering.AnnSvgNoteObjectRenderer);lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgTextUnderlineObjectRenderer",lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer);lt.Annotations.Rendering.AnnSvgThumbStyle.registerClass("lt.Annotations.Rendering.AnnSvgThumbStyle",null,lt.Annotations.Core.IAnnThumbStyle);
lt.Annotations.Rendering.AnnSvgRectangleThumbStyle.registerClass("lt.Annotations.Rendering.AnnSvgRectangleThumbStyle",lt.Annotations.Rendering.AnnSvgThumbStyle);lt.Annotations.Rendering.AnnSvgEllipseThumbStyle.registerClass("lt.Annotations.Rendering.AnnSvgEllipseThumbStyle",lt.Annotations.Rendering.AnnSvgThumbStyle);lt.Annotations.Rendering.AnnSvgTextHiliteObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgTextHiliteObjectRenderer",lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer);
lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer.registerClass("lt.Annotations.Rendering.AnnSvgTextStrikeoutObjectRenderer",lt.Annotations.Rendering.AnnSvgTextReviewObjectRenderer);lt.Annotations.Rendering._svgDrawingHelper.registerClass("lt.Annotations.Rendering._svgDrawingHelper");lt.Annotations.Rendering._annSvgHelpers.registerClass("lt.Annotations.Rendering._annSvgHelpers");lt.Annotations.Rendering._lT_VersionNumber.l_VER_PRODUCT="LEADTOOLS\u00ae for JavaScript";lt.Annotations.Rendering._lT_VersionNumber.l_VER_COMPANYNAME_STR=
"LEAD Technologies, Inc.";lt.Annotations.Rendering._lT_VersionNumber.l_VER_LEGALTRADEMARKS_STR="LEADTOOLS\u00ae is a trademark of LEAD Technologies, Inc.";lt.Annotations.Rendering._lT_VersionNumber.l_VER_LEGALCOPYRIGHT_STR="\u00a9 1991-2017 LEAD Technologies, Inc.";lt.Annotations.Rendering._lT_VersionNumber.l_VER_DLLEXT=".dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_EXEEXT=".exe";lt.Annotations.Rendering._lT_VersionNumber.l_VER_PLATFORM="";lt.Annotations.Rendering._lT_VersionNumber.l_VER_PLATFORM_FOR=
"";lt.Annotations.Rendering._lT_VersionNumber.l_VER_PRODUCTNAME_STR="LEADTOOLS\u00ae for JavaScript";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_XXX="Leadtools.Xxx.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_XXX="LEADTOOLS Xxx";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_KERNEL="lt.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_KERNEL="Leadtools";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CONTROLS=
"lt.Controls.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CONTROLS="Controls";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_DOCUMENTS_UI="lt.Documents.UI.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_DOCUMENTS_UI="Documents User Interface";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CONTROLS_MEDICAL="lt.Controls.Medical.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CONTROLS_MEDICAL=
"Medical Controls";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_DOCUMENTS="lt.Documents.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_DOCUMENTS="Documents";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_CORE="lt.Annotations.Core.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_CORE="Annotations Core";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_AUTOMATION=
"lt.Annotations.Automation.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_AUTOMATION="Annotations Automation";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_DESIGNERS="lt.Annotations.Designers.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_DESIGNERS="Annotations Designers";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_RENDERING="lt.Annotations.Rendering.dll";
lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_RENDERING="Annotations Rendering";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CCOW="Leadtools.Ccow.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CCOW="Leadtools CCOW Library";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_DOCUMENTS="Leadtools.Annotations.Documents.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_DOCUMENTS=
"Annotations Documents";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_LEGACY="Leadtools.Annotations.Legacy.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_LEGACY="Annotations Legacy";lt.Annotations.Rendering._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_JAVASCRIPT="Leadtools.Annotations.JavaScript.dll";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_JAVASCRIPT="JavaScripot Annotations";
lt.Annotations.Rendering._lT_VersionNumber.l_VER_PRODUCTVERSION_DOT_STR="********";lt.Annotations.Rendering._lT_VersionNumber.l_VER_FILEVERSION_DOT_STR="*********";lt.Annotations.Rendering._colorUtil._colorNamesToRGB=null;lt.Annotations.Rendering._colorUtil._colorNamesToRGB={aliceblue:"rgb(240,248,255)",antiquewhite:"rgb(250,235,215)",aqua:"rgb(0,255,255)",aquamarine:"rgb(127,255,212)",azure:"rgb(240,255,255)",beige:"rgb(245,245,220)",bisque:"rgb(255,228,196)",black:"rgb(0,0,0)",blanchedalmond:"rgb(255,235,205)",
blue:"rgb(0,0,255)",blueviolet:"rgb(138,43,226)",brown:"rgb(165,42,42)",burlywood:"rgb(222,184,135)",cadetblue:"rgb(95,158,160)",chartreuse:"rgb(127,255,0)",chocolate:"rgb(210,105,30)",coral:"rgb(255,127,80)",cornflowerblue:"rgb(100,149,237)",cornsilk:"rgb(255,248,220)",crimson:"rgb(220,20,60)",cyan:"rgb(0,255,255)",darkblue:"rgb(0,0,139)",darkcyan:"rgb(0,139,139)",darkgoldenrod:"rgb(184,134,11)",darkgray:"rgb(169,169,169)",darkgreen:"rgb(0,100,0)",darkkhaki:"rgb(189,183,107)",darkmagenta:"rgb(139,0,139)",
darkolivegreen:"rgb(85,107,47)",darkorange:"rgb(255,140,0)",darkorchid:"rgb(153,50,204)",darkred:"rgb(139,0,0)",darksalmon:"rgb(233,150,122)",darkseagreen:"rgb(143,188,143)",darkslateblue:"rgb(72,61,139)",darkslategray:"rgb(47,79,79)",darkturquoise:"rgb(0,206,209)",darkviolet:"rgb(148,0,211)",deeppink:"rgb(255,20,147)",deepskyblue:"rgb(0,191,255)",dimgray:"rgb(105,105,105)",dodgerblue:"rgb(30,144,255)",firebrick:"rgb(178,34,34)",floralwhite:"rgb(255,250,240)",forestgreen:"rgb(34,139,34)",fuchsia:"rgb(255,0,255)",
gainsboro:"rgb(220,220,220)",ghostwhite:"rgb(248,248,255)",gold:"rgb(255,215,0)",goldenrod:"rgb(218,165,32)",gray:"rgb(128,128,128)",green:"rgb(0,128,0)",greenyellow:"rgb(173,255,47)",honeydew:"rgb(240,255,240)",hotpink:"rgb(255,105,180)",indianred:"rgb(205,92,92)",indigo:"rgb(75,0,130)",ivory:"rgb(255,255,240)",khaki:"rgb(240,230,140)",lavender:"rgb(230,230,250)",lavenderblush:"rgb(255,240,245)",lawngreen:"rgb(124,252,0)",lemonchiffon:"rgb(255,250,205)",lightblue:"rgb(173,216,230)",lightcoral:"rgb(240,128,128)",
lightcyan:"rgb(224,255,255)",lightgoldenrodyellow:"rgb(250,250,210)",lightgrey:"rgb(211,211,211)",lightgreen:"rgb(144,238,144)",lightpink:"rgb(255,182,193)",lightsalmon:"rgb(255,160,122)",lightseagreen:"rgb(32,178,170)",lightskyblue:"rgb(135,206,250)",lightslategray:"rgb(119,136,153)",lightsteelblue:"rgb(176,196,222)",lightyellow:"rgb(255,255,224)",lime:"rgb(0,255,0)",limegreen:"rgb(50,205,50)",linen:"rgb(250,240,230)",magenta:"rgb(255,0,255)",maroon:"rgb(128,0,0)",mediumaquamarine:"rgb(102,205,170)",
mediumblue:"rgb(0,0,205)",mediumorchid:"rgb(186,85,211)",mediumpurple:"rgb(147,112,219)",mediumseagreen:"rgb(60,179,113)",mediumslateblue:"rgb(123,104,238)",mediumspringgreen:"rgb(0,250,154)",mediumturquoise:"rgb(72,209,204)",mediumvioletred:"rgb(199,21,133)",midnightblue:"rgb(25,25,112)",mintcream:"rgb(245,255,250)",mistyrose:"rgb(255,228,225)",moccasin:"rgb(255,228,181)",navajowhite:"rgb(255,222,173)",navy:"rgb(0,0,128)",oldlace:"rgb(253,245,230)",olive:"rgb(128,128,0)",olivedrab:"rgb(107,142,35)",
orange:"rgb(255,165,0)",orangered:"rgb(255,69,0)",orchid:"rgb(218,112,214)",palegoldenrod:"rgb(238,232,170)",palegreen:"rgb(152,251,152)",paleturquoise:"rgb(175,238,238)",palevioletred:"rgb(219,112,147)",papayawhip:"rgb(255,239,213)",peachpuff:"rgb(255,218,185)",peru:"rgb(205,133,63)",pink:"rgb(255,192,203)",plum:"rgb(221,160,221)",powderblue:"rgb(176,224,230)",purple:"rgb(128,0,128)",red:"rgb(255,0,0)",rosybrown:"rgb(188,143,143)",royalblue:"rgb(65,105,225)",saddlebrown:"rgb(139,69,19)",salmon:"rgb(250,128,114)",
sandybrown:"rgb(244,164,96)",seagreen:"rgb(46,139,87)",seashell:"rgb(255,245,238)",sienna:"rgb(160,82,45)",silver:"rgb(192,192,192)",skyblue:"rgb(135,206,235)",slateblue:"rgb(106,90,205)",slategray:"rgb(112,128,144)",snow:"rgb(255,250,250)",springgreen:"rgb(0,255,127)",steelblue:"rgb(70,130,180)",tan:"rgb(210,180,140)",teal:"rgb(0,128,128)",thistle:"rgb(216,191,216)",tomato:"rgb(255,99,71)",turquoise:"rgb(64,224,208)",violet:"rgb(238,130,238)",wheat:"rgb(245,222,179)",white:"rgb(255,255,255)",whitesmoke:"rgb(245,245,245)",
yellow:"rgb(255,255,0)",yellowgreen:"rgb(154,205,50)"};lt.Annotations.Rendering.AnnSvgObjectRenderer._count$1=0;lt.Annotations.Rendering._annSvgHelpers.svgNameSpace="http://www.w3.org/2000/svg"})();
