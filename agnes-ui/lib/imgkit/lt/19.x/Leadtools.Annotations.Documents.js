/**************************************************
LEADTOOLS (C) 1991-2017 LEAD Technologies, Inc. ALL RIGHTS RESERVED.
This software is protected by United States and International copyright laws.
Any copying, duplication, deployment, redistribution, modification or other
disposition hereof is STRICTLY PROHIBITED without an express written license
granted by LEAD Technologies, Inc.. This notice may not be removed or otherwise
altered under any circumstances.
Portions of this product are licensed under US patent 5,327,254 and foreign
counterparts.
For more information, contact LEAD Technologies, Inc. at 704-332-5532 or visit
https://www.leadtools.com
**************************************************/
// Leadtools.Annotations.Documents.js
// Version:********
(function() {
  Type.registerNamespace("lt.Annotations.Documents"); window.lt.Annotations.Documents._lT_VersionNumber=function() { }; lt.Annotations.Documents._leadRectDExtensions=function() { }; lt.Annotations.Documents._leadRectDExtensions.inflate=function(a, b, c) { a.inflate(b, c); return a }; lt.Annotations.Documents._leadRectDExtensions.intersectRects=function(a, b) { return lt.LeadRectD.intersectRects(a, b) }; lt.Annotations.Documents._leadRectDExtensions.intersectsWith=function(a, b) { var c=!1; return c=a.intersectsWith(b) };
  lt.Annotations.Documents._leadRectDExtensions.create=function(a, b, c, d) { return lt.LeadRectD.create(a, b, c, d) }; lt.Annotations.Documents._leadRectDExtensions.unionRects=function(a, b) { return lt.LeadRectD.unionRects(a, b) }; lt.Annotations.Documents._leadRectDExtensions.unionPoint=function(a, b) { return lt.Annotations.Documents._leadRectDExtensions.unionRects(a, lt.LeadRectD.create(b.get_x(), b.get_y(), 0, 0)) }; lt.Annotations.Documents._leadRectDExtensions.clone=function(a) { return a.clone() }; lt.Annotations.Documents._leadRectDExtensions.isEmpty=
    function(a) { return a.get_isEmpty() }; lt.Annotations.Documents._leadRectDExtensions.fromLTRB=function(a, b, c, d) { return lt.LeadRectD.fromLTRB(a, b, c, d) }; lt.Annotations.Documents._leadRectDExtensions.get_empty=function() { return lt.LeadRectD.get_empty() }; lt.Annotations.Documents._leadRectDExtensions.containsRect=function(a, b) { return a.containsRect(b) }; lt.Annotations.Documents._leadRectDExtensions.containsPoint=function(a, b) { return a.containsPoint(b) }; lt.Annotations.Documents._leadRectDExtensions.topLeft=
      function(a) { return a.get_topLeft() }; lt.Annotations.Documents._leadRectDExtensions.topRight=function(a) { return a.get_topRight() }; lt.Annotations.Documents._leadRectDExtensions.bottomLeft=function(a) { return a.get_bottomLeft() }; lt.Annotations.Documents._leadRectDExtensions.bottomRight=function(a) { return a.get_bottomRight() }; lt.Annotations.Documents._leadRectDExtensions.top=function(a) { return a.get_top() }; lt.Annotations.Documents._leadRectDExtensions.left=function(a) { return a.get_left() }; lt.Annotations.Documents._leadRectDExtensions.bottom=
        function(a) { return a.get_bottom() }; lt.Annotations.Documents._leadRectDExtensions.right=function(a) { return a.get_right() }; lt.Annotations.Documents._leadRectDExtensions.equals=function(a, b) { return a===b }; lt.Annotations.Documents._leadPointDExtensions=function() { }; lt.Annotations.Documents._leadPointDExtensions.create=function(a, b) { return lt.LeadPointD.create(a, b) }; lt.Annotations.Documents._leadPointDExtensions.isEqual=function(a, b) { return a===b }; lt.Annotations.Documents._leadPointDExtensions.clone=function(a) { return a.clone() };
  lt.Annotations.Documents._leadPointDExtensions.get_empty=function() { return lt.LeadPointD.get_empty() }; lt.Annotations.Documents._leadPointDExtensions.isEmpty=function(a) { return a.get_isEmpty() }; lt.Annotations.Documents._leadLengthDExtensions=function() { }; lt.Annotations.Documents._leadLengthDExtensions.isEqual=function(a, b) { return a===b }; lt.Annotations.Documents._leadLengthDExtensions.create=function(a) { return lt.LeadLengthD.create(a) }; lt.Annotations.Documents._leadLengthDExtensions.clone=function(a) { return a.clone() };
  lt.Annotations.Documents._leadSizeDExtensions=function() { }; lt.Annotations.Documents._leadSizeDExtensions.create=function(a, b) { return lt.LeadSizeD.create(a, b) }; lt.Annotations.Documents._leadSizeDExtensions.clone=function(a) { return a.clone() }; lt.Annotations.Documents._leadSizeDExtensions.get_empty=function() { return lt.LeadSizeD.get_empty() }; lt.Annotations.Documents._leadSizeDExtensions.isEmpty=function(a) { return a.get_isEmpty() }; lt.Annotations.Documents.AnnDateTimeKind=function() { }; lt.Annotations.Documents.AnnDateTimeKind.prototype=
    { utc: 0, local: 1 }; lt.Annotations.Documents.AnnDateTimeKind.registerEnum("lt.Annotations.Documents.AnnDateTimeKind", !1); lt.Annotations.Documents.IAnnBatesElement=function() { }; lt.Annotations.Documents.IAnnBatesElement.prototype={ get_friendlyName: null, set_friendlyName: null, asString: null, clone: null, add_propertyChanged: null, remove_propertyChanged: null }; lt.Annotations.Documents.IAnnBatesElement.registerInterface("lt.Annotations.Documents.IAnnBatesElement"); lt.Annotations.Documents.AnnBatesDateTime=function() {
      this._format=
      ""; this._kind=1
    }; lt.Annotations.Documents.AnnBatesDateTime.prototype={
      add_propertyChanged: function(a) { this.__propertyChanged=ss.Delegate.combine(this.__propertyChanged, a) }, remove_propertyChanged: function(a) { this.__propertyChanged=ss.Delegate.remove(this.__propertyChanged, a) }, __propertyChanged_handler_get: function() { null==this.__propertyChanged_handler&&(this.__propertyChanged_handler=ss.EventHandler.create(this, this.add_propertyChanged, this.remove_propertyChanged)); return this.__propertyChanged_handler },
      __propertyChanged: null, __propertyChanged_handler: null, _currentDateTime: null, get_currentDateTime: function() { return this._currentDateTime }, set_currentDateTime: function(a) { if(a!==this._currentDateTime) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("CurrentDateTime", lt.Annotations.Core.PropertyChangedStatus.after, this._currentDateTime, a); this._currentDateTime=a; this.onPropertyChanged(b) } return a }, get_format: function() { return this._format }, set_format: function(a) {
        if(a!==this._format) {
          var b=
            new lt.Annotations.Core.AnnPropertyChangedEventArgs("Format", lt.Annotations.Core.PropertyChangedStatus.after, this._format, a); this._format=a; this.onPropertyChanged(b)
        } return a
      }, get_kind: function() { return this._kind }, set_kind: function(a) { if(a!==this._kind) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("Kind", lt.Annotations.Core.PropertyChangedStatus.after, this._kind, a); this._kind=a; this.onPropertyChanged(b) } return a }, asString: function() {
        return lt.Annotations.Documents._dateTimeHelper.asString(this._currentDateTime,
          this._kind, this._format)
      }, _friendlyName: "BatesDateTime", get_friendlyName: function() { return this._friendlyName }, set_friendlyName: function(a) { return this._friendlyName=a }, toString: function() { return this._friendlyName }, onPropertyChanged: function(a) { null!=this.__propertyChanged&&this.__propertyChanged(this, a) }, clone: function() { var a=new lt.Annotations.Documents.AnnBatesDateTime; a.set_currentDateTime(this._currentDateTime); a.set_format(this._format); a.set_kind(this._kind); return a }
    }; Object.defineProperty(lt.Annotations.Documents.AnnBatesDateTime.prototype,
      "propertyChanged", { get: lt.Annotations.Documents.AnnBatesDateTime.prototype.__propertyChanged_handler_get, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesDateTime.prototype, "currentDateTime", { get: lt.Annotations.Documents.AnnBatesDateTime.prototype.get_currentDateTime, set: lt.Annotations.Documents.AnnBatesDateTime.prototype.set_currentDateTime, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesDateTime.prototype, "format", {
        get: lt.Annotations.Documents.AnnBatesDateTime.prototype.get_format,
        set: lt.Annotations.Documents.AnnBatesDateTime.prototype.set_format, enumerable: !0, configurable: !0
      }); Object.defineProperty(lt.Annotations.Documents.AnnBatesDateTime.prototype, "kind", { get: lt.Annotations.Documents.AnnBatesDateTime.prototype.get_kind, set: lt.Annotations.Documents.AnnBatesDateTime.prototype.set_kind, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesDateTime.prototype, "friendlyName", {
        get: lt.Annotations.Documents.AnnBatesDateTime.prototype.get_friendlyName,
        set: lt.Annotations.Documents.AnnBatesDateTime.prototype.set_friendlyName, enumerable: !0, configurable: !0
      }); lt.Annotations.Documents.AnnBatesNumber=function() { this._currentNumberValues={}; this._suffixText=this._prefixText=""; this._currentNumber=this._startNumber.toString() }; lt.Annotations.Documents.AnnBatesNumber.prototype={
        add_propertyChanged: function(a) { this.__propertyChanged=ss.Delegate.combine(this.__propertyChanged, a) }, remove_propertyChanged: function(a) {
          this.__propertyChanged=ss.Delegate.remove(this.__propertyChanged,
            a)
        }, __propertyChanged_handler_get: function() { null==this.__propertyChanged_handler&&(this.__propertyChanged_handler=ss.EventHandler.create(this, this.add_propertyChanged, this.remove_propertyChanged)); return this.__propertyChanged_handler }, __propertyChanged: null, __propertyChanged_handler: null, _currentNumber: "1", _needsUpdate: !0, _containerIndex: 0, _numberOfDigits: 6, get_numberOfDigits: function() { return this._numberOfDigits }, set_numberOfDigits: function(a) {
          if(a!==this._numberOfDigits) {
            var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("NumberOfDigits",
              lt.Annotations.Core.PropertyChangedStatus.after, this._numberOfDigits, a); this._numberOfDigits=a; this.onPropertyChanged(b)
          } return a
        }, _startNumber: 1, get_startNumber: function() { return this._startNumber }, set_startNumber: function(a) { if(a!==this._startNumber) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("StartNumber", lt.Annotations.Core.PropertyChangedStatus.after, this._startNumber, a); this._startNumber=a; this.onPropertyChanged(b); this._needsUpdate=!0 } return a }, _autoIncrement: !0, get_autoIncrement: function() { return this._autoIncrement },
        set_autoIncrement: function(a) { if(a!==this._autoIncrement) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("AutoIncrement", lt.Annotations.Core.PropertyChangedStatus.after, this._autoIncrement, a); this._autoIncrement=a; this.onPropertyChanged(b); this._needsUpdate=!0 } return a }, get_prefixText: function() { return this._prefixText }, set_prefixText: function(a) {
          if(a!==this._prefixText) {
            var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("PrefixText", lt.Annotations.Core.PropertyChangedStatus.after,
              this._prefixText, a); this._prefixText=a; this.onPropertyChanged(b)
          } return a
        }, get_suffixText: function() { return this._suffixText }, set_suffixText: function(a) { if(a!==this._suffixText) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("SuffixText", lt.Annotations.Core.PropertyChangedStatus.after, this._suffixText, a); this._suffixText=a; this.onPropertyChanged(b) } return a }, _useAllDigits: !0, get_useAllDigits: function() { return this._useAllDigits }, set_useAllDigits: function(a) {
          if(a!==this._useAllDigits) {
            var b=
              new lt.Annotations.Core.AnnPropertyChangedEventArgs("UseAllDigits", lt.Annotations.Core.PropertyChangedStatus.after, this._useAllDigits, a); this._useAllDigits=a; this.onPropertyChanged(b)
          } return a
        }, _getRandomNumber: function() {
          return window.crypto.getRandomValues(new Uint8Array(1))[0]*0.00392
        }, _generateCurrentNumber: function() {
          Object.keyExists(this._currentNumberValues, this._containerIndex)||(this._needsUpdate=!0); if(this._needsUpdate) {
            var a=0, a=this._autoIncrement? this._startNumber+this._containerIndex:this._getRandomNumber(); this._currentNumber=
              a.toString(); this._currentNumberValues[this._containerIndex]=a
          } else this._currentNumber=this._currentNumberValues[this._containerIndex].toString(); this._needsUpdate=!1; this._correctCurrentNumberFormat()
        }, _correctCurrentNumberFormat: function() {
          var a=this._currentNumber, b=a.length, c=""; if(b<this._numberOfDigits) { if(this._useAllDigits) for(var d=this._numberOfDigits-b, b=0; b<d; b++)c+="0" } else if(b>this._numberOfDigits) { d=b-this._numberOfDigits; for(b=0; b<d; b++)a=a.remove(a.length-1, 1) } this._currentNumber=
            c+a
        }, asString: function() { this._generateCurrentNumber(); return this._prefixText+this._currentNumber+this._suffixText }, _friendlyName: "BatesNumber", get_friendlyName: function() { return this._friendlyName }, set_friendlyName: function(a) { return this._friendlyName=a }, toString: function() { return this._friendlyName }, onPropertyChanged: function(a) { null!=this.__propertyChanged&&this.__propertyChanged(this, a) }, clone: function() {
          var a=new lt.Annotations.Documents.AnnBatesNumber; a.set_autoIncrement(this._autoIncrement);
          a._containerIndex=this._containerIndex; a.set_numberOfDigits(this._numberOfDigits); a.set_prefixText(this._prefixText); a.set_startNumber(this._startNumber); a.set_suffixText(this._suffixText); a.set_useAllDigits(this._useAllDigits); return a
        }
      }; Object.defineProperty(lt.Annotations.Documents.AnnBatesNumber.prototype, "propertyChanged", { get: lt.Annotations.Documents.AnnBatesNumber.prototype.__propertyChanged_handler_get, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesNumber.prototype,
        "numberOfDigits", { get: lt.Annotations.Documents.AnnBatesNumber.prototype.get_numberOfDigits, set: lt.Annotations.Documents.AnnBatesNumber.prototype.set_numberOfDigits, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesNumber.prototype, "startNumber", { get: lt.Annotations.Documents.AnnBatesNumber.prototype.get_startNumber, set: lt.Annotations.Documents.AnnBatesNumber.prototype.set_startNumber, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesNumber.prototype,
          "autoIncrement", { get: lt.Annotations.Documents.AnnBatesNumber.prototype.get_autoIncrement, set: lt.Annotations.Documents.AnnBatesNumber.prototype.set_autoIncrement, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesNumber.prototype, "prefixText", { get: lt.Annotations.Documents.AnnBatesNumber.prototype.get_prefixText, set: lt.Annotations.Documents.AnnBatesNumber.prototype.set_prefixText, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesNumber.prototype,
            "suffixText", { get: lt.Annotations.Documents.AnnBatesNumber.prototype.get_suffixText, set: lt.Annotations.Documents.AnnBatesNumber.prototype.set_suffixText, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesNumber.prototype, "useAllDigits", { get: lt.Annotations.Documents.AnnBatesNumber.prototype.get_useAllDigits, set: lt.Annotations.Documents.AnnBatesNumber.prototype.set_useAllDigits, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesNumber.prototype,
              "friendlyName", { get: lt.Annotations.Documents.AnnBatesNumber.prototype.get_friendlyName, set: lt.Annotations.Documents.AnnBatesNumber.prototype.set_friendlyName, enumerable: !0, configurable: !0 }); lt.Annotations.Documents.AnnBatesStamp=function() {
                this._targetContainers=new lt.Annotations.Core.AnnContainerCollection; this._logo=new lt.Annotations.Documents.AnnBatesStampLogo; this._font=new lt.Annotations.Core.AnnFont("Arial", 12); this._foreground=lt.Annotations.Core.AnnSolidColorBrush.create("Red"); this._horizontalAlignment=
                  lt.Annotations.Core.AnnHorizontalAlignment.left; this._verticalAlignment=lt.Annotations.Core.AnnVerticalAlignment.top; this._elements=new lt.Annotations.Documents.AnnBatesElementCollection; this._logo.add_propertyChanged(ss.Delegate.create(this, this._logo_PropertyChanged)); this._elements.add_collectionChanged(ss.Delegate.create(this, this._elements_CollectionChanged))
              }; lt.Annotations.Documents.AnnBatesStamp.prototype={
                _annTextObject: null, _annStampObject: null, add_propertyChanged: function(a) {
                  this.__propertyChanged=
                  ss.Delegate.combine(this.__propertyChanged, a)
                }, remove_propertyChanged: function(a) { this.__propertyChanged=ss.Delegate.remove(this.__propertyChanged, a) }, __propertyChanged_handler_get: function() { null==this.__propertyChanged_handler&&(this.__propertyChanged_handler=ss.EventHandler.create(this, this.add_propertyChanged, this.remove_propertyChanged)); return this.__propertyChanged_handler }, __propertyChanged: null, __propertyChanged_handler: null, get_logo: function() { return this._logo }, _friendlyName: "Bates Stamp",
                get_friendlyName: function() { return this._friendlyName }, set_friendlyName: function(a) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("FriendlyName", lt.Annotations.Core.PropertyChangedStatus.after, this._friendlyName, a); this._friendlyName=a; this.onPropertyChanged(b); return a }, get_font: function() { return this._font }, set_font: function(a) {
                  if(a!==this._font) {
                    var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("Font", lt.Annotations.Core.PropertyChangedStatus.after, this._font, a); this._font=
                      a; this.onPropertyChanged(b); this._update()
                  } return a
                }, get_foreground: function() { return this._foreground }, set_foreground: function(a) { if(a!==this._foreground) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("Foreground", lt.Annotations.Core.PropertyChangedStatus.after, this._foreground, a); this._foreground=a; this.onPropertyChanged(b); this._update() } return a }, get_horizontalAlignment: function() { return this._horizontalAlignment }, set_horizontalAlignment: function(a) {
                  if(a!==this._horizontalAlignment) {
                    var b=
                      new lt.Annotations.Core.AnnPropertyChangedEventArgs("HorizontalAlignment", lt.Annotations.Core.PropertyChangedStatus.after, this._horizontalAlignment, a); this._horizontalAlignment=a; this.onPropertyChanged(b); this._update()
                  } return a
                }, get_verticalAlignment: function() { return this._verticalAlignment }, set_verticalAlignment: function(a) {
                  if(a!==this._verticalAlignment) {
                    var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("VerticalAlignment", lt.Annotations.Core.PropertyChangedStatus.after, this._verticalAlignment,
                      a); this._verticalAlignment=a; this.onPropertyChanged(b); this._update()
                  } return a
                }, get_elements: function() { return this._elements }, _createTextObject: function() { this._annTextObject=new lt.Annotations.Core.AnnTextObject; this._annTextObject.set_text(""); this._annTextObject.set_stroke(null); this._annTextObject.set_wordWrap(!1); this._annTextObject.set_font(this._font); null!=this._logo&&this._annStampObject.set_font(this._logo.get_font()); return this._annTextObject }, _createStampObject: function() {
                  this._annStampObject=
                  new lt.Annotations.Core.AnnStampObject; this._annStampObject.set_text(""); this._annStampObject.get_stroke().set_stroke(lt.Annotations.Core.AnnSolidColorBrush.create("transparent")); this._annStampObject.set_wordWrap(!1); this._annStampObject.set_fill(null); this._annStampObject.set_verticalAlignment(lt.Annotations.Core.AnnVerticalAlignment.center); this._annStampObject.set_horizontalAlignment(lt.Annotations.Core.AnnHorizontalAlignment.center); this._annStampObject.set_picture(lt.Annotations.Core.AnnPicture.get_empty());
                  return this._annStampObject
                }, _attachStamp: function(a) { this._targetContainers.clear(); for(a=ss.IEnumerator.getEnumerator(a); a.moveNext();)this._attachContainer(a.current) }, _attachContainer: function(a) { a.get_children().add(this._createStampObject()); a.get_children().add(this._createTextObject()); this._targetContainers.add(a); this._update() }, _detachContainer: function(a) { a.get_children().clear(); this._targetContainers.remove(a) }, _detachStamp: function() {
                  for(var a=ss.IEnumerator.getEnumerator(this._targetContainers); a.moveNext();) {
                    var b=
                      a.current; b.get_children().remove(this._annTextObject); b.get_children().remove(this._annStampObject)
                  } this._targetContainers.clear()
                }, _elements_CollectionChanged: function(a, b) {
                  if(0<b.get_newItems().length) {
                    var c=Type.safeCast(b.get_newItems()[0], lt.Annotations.Documents.IAnnBatesElement); if(b.get_action()===lt.Annotations.Core.AnnNotifyCollectionChangedAction.add) c.add_propertyChanged(ss.Delegate.create(this, this._element_PropertyChanged)); else if(b.get_action()===lt.Annotations.Core.AnnNotifyCollectionChangedAction.remove) c.remove_propertyChanged(ss.Delegate.create(this,
                      this._element_PropertyChanged)); else if(b.get_action()===lt.Annotations.Core.AnnNotifyCollectionChangedAction.reset) for(c=ss.IEnumerator.getEnumerator(b.get_newItems()); c.moveNext();)Type.safeCast(c.current, lt.Annotations.Documents.IAnnBatesElement).remove_propertyChanged(ss.Delegate.create(this, this._element_PropertyChanged)); this._update()
                  }
                }, _element_PropertyChanged: function() { this._update() }, _logo_PropertyChanged: function() { this._update() }, asString: function(a) {
                  a=this._targetContainers.indexOf(a);
                  if(-1!==a) { for(var b="", c=ss.IEnumerator.getEnumerator(this._elements); c.moveNext();) { var d=c.current, e=Type.safeCast(d, lt.Annotations.Documents.AnnBatesNumber); null!=e&&(e._containerIndex=a); b+=d.asString() } return b } return ""
                }, _update: function() {
                  for(var a=ss.IEnumerator.getEnumerator(this._targetContainers); a.moveNext();) {
                    var b=a.current, c=this.asString(b); this._annTextObject.set_text(c); this._annTextObject.set_font(this._font); null!=this._logo&&this._annStampObject.set_font(this._logo.get_font());
                    this._annTextObject.set_textForeground(this._foreground); this._annStampObject.set_opacity(this._logo.get_opacity()); this._annStampObject.set_text(this._logo.get_text()); this._annStampObject.set_picture(this._logo.get_picture()); var d=b.get_mapper(), e=d.clone(); e.updateTransform(lt.LeadMatrix.get_identity()); (d.get_targetDpiX()!==d.get_sourceDpiX()||d.get_targetDpiY()!==d.get_sourceDpiY())&&e.mapResolutions(d.get_sourceDpiX(), d.get_sourceDpiY(), d.get_sourceDpiX(), d.get_sourceDpiY()); b=e.rectFromContainerCoordinates(lt.Annotations.Documents._leadRectDExtensions.create(0,
                      0, b.get_size().get_width(), b.get_size().get_height()), lt.Annotations.Core.AnnFixedStateOperations.none); d=lt.Annotations.Documents._leadRectDExtensions.get_empty(); this._logo.get_stretchLogo()? (d=lt.Annotations.Documents._leadRectDExtensions.clone(b), d=e.rectToContainerCoordinates(d)):d=this._logo.get_logoRect(); this._annStampObject.set_rect(d); d=lt.Annotations.Documents._leadPointDExtensions.create(d.get_x()+d.get_width()/2, d.get_y()+d.get_height()/2); this._annStampObject.rotate(this._logo.get_angle(),
                        d); d=lt.Annotations.Documents._leadSizeDExtensions.create(0, 0); null!=lt.Annotations.Documents.AnnBatesStampComposer.get_renderingEngine()&&!String.isNullOrEmpty(c)&&(d=e.fontFromContainerCoordinates(this._annTextObject.get_font(), this._annTextObject.get_fixedStateOperations()), d=lt.Annotations.Documents.AnnBatesStampComposer.get_renderingEngine().measureString(c, d), c=lt.Annotations.Documents._leadRectDExtensions.create(0, 0, d.get_width(), d.get_height()), c=this._alignRect(c, b), c.get_width()>b.get_width()&&
                          (c.set_width(b.get_width()), c.set_x(0)), this._annTextObject.set_rect(e.rectToContainerCoordinates(c)))
                  }
                }, _alignRect: function(a, b) {
                  var c=lt.Annotations.Documents._leadRectDExtensions.clone(a); this._horizontalAlignment===lt.Annotations.Core.AnnHorizontalAlignment.center? c.set_x(b.get_width()/2-c.get_width()/2):this._horizontalAlignment===lt.Annotations.Core.AnnHorizontalAlignment.right&&c.set_x(b.get_width()-c.get_width()); this._verticalAlignment===lt.Annotations.Core.AnnVerticalAlignment.center? c.set_y(b.get_height()/
                    2-c.get_height()/2):this._verticalAlignment===lt.Annotations.Core.AnnVerticalAlignment.bottom&&c.set_y(b.get_height()-c.get_height()); return c
                }, toString: function() { return this._friendlyName }, clone: function() {
                  var a=null, b=null; try {
                    for(var a=new lt.Annotations.Documents.AnnBatesStamp, c=ss.IEnumerator.getEnumerator(this._elements); c.moveNext();) { var d=c.current; a.get_elements().add(d.clone()) } a.set_font(this._font.clone()); a.set_foreground(this._foreground.clone()); a.set_horizontalAlignment(this._horizontalAlignment);
                    a.get_logo().set_angle(this._logo.get_angle()); a.get_logo().set_logoRect(lt.Annotations.Documents._leadRectDExtensions.clone(this._logo.get_logoRect())); a.get_logo().set_opacity(this._logo.get_opacity()); null!=this._logo.get_picture()&&a.get_logo().set_picture(this._logo.get_picture().clone()); a.get_logo().set_stretchLogo(this._logo.get_stretchLogo()); a.get_logo().set_text(this._logo.get_text()); a.get_logo().set_font(this._logo.get_font()); a.set_verticalAlignment(this._verticalAlignment); for(var e=
                      ss.IEnumerator.getEnumerator(this._targetContainers); e.moveNext();)a._targetContainers.add(e.current.clone()); b=a; a=null
                  } finally { null!=a&&a.dispose() } return b
                }, dispose: function() { this._cleanUp() }, _cleanUp: function() { this._elements.clear(); this._logo.remove_propertyChanged(ss.Delegate.create(this, this._logo_PropertyChanged)); this._elements.remove_collectionChanged(ss.Delegate.create(this, this._elements_CollectionChanged)) }, onPropertyChanged: function(a) {
                  null!=this.__propertyChanged&&this.__propertyChanged(this,
                    a)
                }
              }; Object.defineProperty(lt.Annotations.Documents.AnnBatesStamp.prototype, "propertyChanged", { get: lt.Annotations.Documents.AnnBatesStamp.prototype.__propertyChanged_handler_get, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStamp.prototype, "logo", { get: lt.Annotations.Documents.AnnBatesStamp.prototype.get_logo, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStamp.prototype, "friendlyName", {
                get: lt.Annotations.Documents.AnnBatesStamp.prototype.get_friendlyName,
                set: lt.Annotations.Documents.AnnBatesStamp.prototype.set_friendlyName, enumerable: !0, configurable: !0
              }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStamp.prototype, "font", { get: lt.Annotations.Documents.AnnBatesStamp.prototype.get_font, set: lt.Annotations.Documents.AnnBatesStamp.prototype.set_font, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStamp.prototype, "foreground", {
                get: lt.Annotations.Documents.AnnBatesStamp.prototype.get_foreground, set: lt.Annotations.Documents.AnnBatesStamp.prototype.set_foreground,
                enumerable: !0, configurable: !0
              }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStamp.prototype, "horizontalAlignment", { get: lt.Annotations.Documents.AnnBatesStamp.prototype.get_horizontalAlignment, set: lt.Annotations.Documents.AnnBatesStamp.prototype.set_horizontalAlignment, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStamp.prototype, "verticalAlignment", {
                get: lt.Annotations.Documents.AnnBatesStamp.prototype.get_verticalAlignment, set: lt.Annotations.Documents.AnnBatesStamp.prototype.set_verticalAlignment,
                enumerable: !0, configurable: !0
              }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStamp.prototype, "elements", { get: lt.Annotations.Documents.AnnBatesStamp.prototype.get_elements, enumerable: !0, configurable: !0 }); lt.Annotations.Documents.AnnBatesStampComposer=function() {
                this._stamps=new lt.Annotations.Documents.AnnBatesStampCollection; this._targetContainers=new lt.Annotations.Core.AnnContainerCollection; this._targetContainers.add_collectionChanged(ss.Delegate.create(this, this._targetContainers_CollectionChanged));
                this._stamps.add_collectionChanged(ss.Delegate.create(this, this._stamps_CollectionChanged))
              }; lt.Annotations.Documents.AnnBatesStampComposer.get_renderingEngine=function() { return lt.Annotations.Documents.AnnBatesStampComposer._renderingEngine }; lt.Annotations.Documents.AnnBatesStampComposer.set_renderingEngine=function(a) { return lt.Annotations.Documents.AnnBatesStampComposer._renderingEngine=a }; lt.Annotations.Documents.AnnBatesStampComposer._xmlDocumentToString=function(a) {
                var b=""; window.ActiveXObject?
                  (b=a.xml, ss.isUndefined(b)&&(b=new XMLSerializer, b=b.serializeToString(a))):(b=new XMLSerializer, b=b.serializeToString(a)); return b
              }; lt.Annotations.Documents.AnnBatesStampComposer._createBatesStampNode=function(a, b) { var c=lt.Annotations.Core.AnnXmlHelper.createElement(a, "AnnBatesStamp"); lt.Annotations.Documents.AnnBatesStampComposer._writeBatesStamp(a, c, b); return c }; lt.Annotations.Documents.AnnBatesStampComposer._writeBatesStamp=function(a, b, c) {
                lt.Annotations.Core.AnnXmlHelper.writeFontElement(a,
                  lt.Annotations.Core.AnnXmlHelper.font, b, c.get_font()); lt.Annotations.Core.AnnXmlHelper.writeBrushElement(a, lt.Annotations.Core.AnnXmlHelper.fill, b, c.get_foreground()); lt.Annotations.Core.AnnXmlHelper.writeStringElement(a, lt.Annotations.Core.AnnXmlHelper.horizontalAlignment, b, lt.Annotations.Core.Utils.enumToString(lt.Annotations.Core.AnnHorizontalAlignment, c.get_horizontalAlignment())); lt.Annotations.Core.AnnXmlHelper.writeStringElement(a, lt.Annotations.Core.AnnXmlHelper.verticalAlignment, b, lt.Annotations.Core.Utils.enumToString(lt.Annotations.Core.AnnVerticalAlignment,
                    c.get_verticalAlignment())); lt.Annotations.Documents.AnnBatesStampComposer._writeBatesStampLogoElement(a, b, c.get_logo()); c=(new lt.Annotations.Documents.AnnBatesStampTranslator).writeElementsToString(c.get_elements().toArray()); lt.Annotations.Core.AnnXmlHelper.writeStringElement(a, "AnnBatesStampElements", b, c)
              }; lt.Annotations.Documents.AnnBatesStampComposer._writeBatesStampLogoElement=function(a, b, c) {
                var d=lt.Annotations.Core.AnnXmlHelper.createElement(a, "AnnBatesStampLogo"); lt.Annotations.Core.AnnXmlHelper.appendChild(b,
                  d); lt.Annotations.Core.AnnXmlHelper.writeNumericElement(a, "AnnRotateAngle", d, c.get_angle()); lt.Annotations.Core.AnnXmlHelper.writeNumericElement(a, lt.Annotations.Core.AnnXmlHelper.opacity, d, c.get_opacity()); lt.Annotations.Core.AnnXmlHelper.writePictureElement(a, lt.Annotations.Core.AnnXmlHelper.picture, d, c.get_picture()); var b=new lt.Annotations.Core.LeadPointCollection, e=c.get_logoRect(); b.add(lt.Annotations.Documents._leadPointDExtensions.create(e.get_x(), e.get_y())); b.add(lt.Annotations.Documents._leadPointDExtensions.create(e.get_x()+
                    e.get_width(), e.get_y()+e.get_height())); lt.Annotations.Core.AnnXmlHelper.writePointsElement(a, lt.Annotations.Core.AnnXmlHelper.leadPointsType, d, b); lt.Annotations.Core.AnnXmlHelper.writeBooleanElement(a, "AnnLogoStretch", d, c.get_stretchLogo()); lt.Annotations.Core.AnnXmlHelper.writeStringElement(a, lt.Annotations.Core.AnnXmlHelper.text, d, c.get_text())
              }; lt.Annotations.Documents.AnnBatesStampComposer.loadFromXmlDocument=function(a) { if(null==a) throw new lt.ArgumentNullException("document"); return lt.Annotations.Documents.AnnBatesStampComposer._doLoad(a) };
  lt.Annotations.Documents.AnnBatesStampComposer.load=function(a) { if(null==a) throw new lt.ArgumentNullException("xmlData"); a=(new DOMParser).parseFromString(a, "application/xml"); return lt.Annotations.Documents.AnnBatesStampComposer._doLoad(a) }; lt.Annotations.Documents.AnnBatesStampComposer._doLoad=function(a) {
    var b=new lt.Annotations.Documents.AnnBatesStampCollection, c=lt.Annotations.Core.AnnXmlHelper.getFirstMatchingChild(a, "AnnBatesStampComposer", a); if(null!=c&&(c=lt.Annotations.Core.AnnXmlHelper.getFirstMatchingChild(a,
      "AnnBatesStamps", c), null!=c)) for(c=ss.IEnumerator.getEnumerator(lt.Annotations.Core.AnnXmlHelper.getMatchingChildren(a, "AnnBatesStamp", c)); c.moveNext();)b.add(lt.Annotations.Documents.AnnBatesStampComposer._readBatesStamp(a, c.current)); c=a=null; try { for(var a=new lt.Annotations.Documents.AnnBatesStampComposer, d=ss.IEnumerator.getEnumerator(b); d.moveNext();) { var e=d.current; a.get_stamps().add(e) } c=a; a=null } finally { null!=a&&a.dispose() } return c
  }; lt.Annotations.Documents.AnnBatesStampComposer._readBatesStamp=
    function(a, b) {
      var c=null, d=null; try {
        d=new lt.Annotations.Documents.AnnBatesStamp; d.set_font(lt.Annotations.Core.AnnXmlHelper.readFontElement(a, lt.Annotations.Core.AnnXmlHelper.font, b, d.get_font())); d.set_foreground(lt.Annotations.Core.AnnXmlHelper.readBrushElement(a, lt.Annotations.Core.AnnXmlHelper.fill, b, d.get_foreground())); d.set_horizontalAlignment(lt.Annotations.Core.Utils.enumParse(lt.Annotations.Core.AnnHorizontalAlignment, lt.Annotations.Core.AnnXmlHelper.readStringElement(a, lt.Annotations.Core.AnnXmlHelper.horizontalAlignment,
          b, d.get_horizontalAlignment().toString()))); d.set_verticalAlignment(lt.Annotations.Core.Utils.enumParse(lt.Annotations.Core.AnnVerticalAlignment, lt.Annotations.Core.AnnXmlHelper.readStringElement(a, lt.Annotations.Core.AnnXmlHelper.verticalAlignment, b, d.get_verticalAlignment().toString()))); var e=lt.Annotations.Core.AnnXmlHelper.getFirstMatchingChild(a, "AnnBatesStampLogo", b); null!=e&&lt.Annotations.Documents.AnnBatesStampComposer._readStampLogo(d.get_logo(), a, e); var f=(new lt.Annotations.Documents.AnnBatesStampTranslator).readFromString(lt.Annotations.Core.AnnXmlHelper.readStringElement(a,
            "AnnBatesStampElements", b, null)); if(null!=f) for(var g=ss.IEnumerator.getEnumerator(f); g.moveNext();) { var h=g.current; null!=h&&d.get_elements().add(h) } c=d; d=null
      } finally { null!=d&&d.dispose() } return c
    }; lt.Annotations.Documents.AnnBatesStampComposer._readStampLogo=function(a, b, c) {
      a.set_angle(lt.Annotations.Core.AnnXmlHelper.readNumericElement(b, "AnnRotateAngle", c, a.get_angle())); a.set_opacity(lt.Annotations.Core.AnnXmlHelper.readNumericElement(b, lt.Annotations.Core.AnnXmlHelper.opacity, c, a.get_opacity()));
      a.set_picture(lt.Annotations.Core.AnnXmlHelper.readPictureElement(b, lt.Annotations.Core.AnnXmlHelper.picture, c)); var d=lt.Annotations.Core.AnnXmlHelper.readPointsElement(b, lt.Annotations.Core.AnnXmlHelper.leadPointsType, c); null!=d&&a.set_logoRect(lt.Annotations.Documents._leadRectDExtensions.fromLTRB(d.get_item(0).get_x(), d.get_item(0).get_y(), d.get_item(1).get_x(), d.get_item(1).get_y())); a.set_stretchLogo(lt.Annotations.Core.AnnXmlHelper.readBooleanElement(b, "AnnLogoStretch", c, a.get_stretchLogo()));
      a.set_text(lt.Annotations.Core.AnnXmlHelper.readStringElement(b, lt.Annotations.Core.AnnXmlHelper.text, c, a.get_text()))
    }; lt.Annotations.Documents.AnnBatesStampComposer.prototype={
      get_stamps: function() { return this._stamps }, get_targetContainers: function() { return this._targetContainers }, _targetContainers_CollectionChanged: function(a, b) {
        if(b.get_action()===lt.Annotations.Core.AnnNotifyCollectionChangedAction.add) {
          var c=Type.safeCast(b.get_newItems()[0], lt.Annotations.Core.AnnContainer); c.set_isEnabled(!1);
          for(var d=ss.IEnumerator.getEnumerator(this._stamps); d.moveNext();) { var e=d.current; e._attachContainer(c) }
        } else if(b.get_action()===lt.Annotations.Core.AnnNotifyCollectionChangedAction.remove) { c=Type.safeCast(b.get_newItems()[0], lt.Annotations.Core.AnnContainer); for(d=ss.IEnumerator.getEnumerator(this._stamps); d.moveNext();)e=d.current, e._detachContainer(c) } else if(b.get_action()===lt.Annotations.Core.AnnNotifyCollectionChangedAction.reset) for(c=ss.IEnumerator.getEnumerator(b.get_newItems()); c.moveNext();)for(var d=
          c.current, f=ss.IEnumerator.getEnumerator(this._stamps); f.moveNext();)e=f.current, e._detachContainer(d)
      }, _stamps_CollectionChanged: function(a, b) {
        if(b.get_action()===lt.Annotations.Core.AnnNotifyCollectionChangedAction.add) Type.safeCast(b.get_newItems()[0], lt.Annotations.Documents.AnnBatesStamp)._attachStamp(this._targetContainers.toArray()); else if(b.get_action()===lt.Annotations.Core.AnnNotifyCollectionChangedAction.remove) Type.safeCast(b.get_newItems()[0], lt.Annotations.Documents.AnnBatesStamp)._detachStamp();
        else if(b.get_action()===lt.Annotations.Core.AnnNotifyCollectionChangedAction.reset) for(var c=ss.IEnumerator.getEnumerator(b.get_newItems()); c.moveNext();)c.current._detachStamp()
      }, dispose: function() { this._cleanUp() }, _cleanUp: function() {
        for(var a=ss.IEnumerator.getEnumerator(this._stamps); a.moveNext();)a.current.dispose(); this._targetContainers.remove_collectionChanged(ss.Delegate.create(this, this._targetContainers_CollectionChanged)); this._stamps.remove_collectionChanged(ss.Delegate.create(this,
          this._stamps_CollectionChanged)); this._targetContainers.clear(); this._stamps.clear()
      }, save: function(a) {
        if(null==a) throw new lt.ArgumentNullException("composer"); var b="", b=String.format("{0}<{1}></{2}>", '<?xml version="1.0" encoding="utf-8"?>', "AnnBatesStampComposer", "AnnBatesStampComposer"), b=(new DOMParser).parseFromString(b, "application/xml"), c=lt.Annotations.Core.AnnXmlHelper.getFirstMatchingChild(b, "AnnBatesStampComposer", b), d=lt.Annotations.Core.AnnXmlHelper.createElement(b, "AnnBatesStamps");
        c.appendChild(d); for(a=ss.IEnumerator.getEnumerator(a.get_stamps()); a.moveNext();)d.appendChild(lt.Annotations.Documents.AnnBatesStampComposer._createBatesStampNode(b, a.current)); return b=lt.Annotations.Documents.AnnBatesStampComposer._xmlDocumentToString(b)
      }
    }; Object.defineProperty(lt.Annotations.Documents.AnnBatesStampComposer, "renderingEngine", {
      get: lt.Annotations.Documents.AnnBatesStampComposer.get_renderingEngine, set: lt.Annotations.Documents.AnnBatesStampComposer.set_renderingEngine, enumerable: !0,
      configurable: !0
    }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampComposer.prototype, "stamps", { get: lt.Annotations.Documents.AnnBatesStampComposer.prototype.get_stamps, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampComposer.prototype, "targetContainers", { get: lt.Annotations.Documents.AnnBatesStampComposer.prototype.get_targetContainers, enumerable: !0, configurable: !0 }); lt.Annotations.Documents.AnnBatesStampLogo=function() {
      this._logoRect=lt.Annotations.Documents._leadRectDExtensions.create(0,
        0, 0, 0); this._text=""; this._font=new lt.Annotations.Core.AnnFont("Arial", 12)
    }; lt.Annotations.Documents.AnnBatesStampLogo.prototype={
      add_propertyChanged: function(a) { this.__propertyChanged=ss.Delegate.combine(this.__propertyChanged, a) }, remove_propertyChanged: function(a) { this.__propertyChanged=ss.Delegate.remove(this.__propertyChanged, a) }, __propertyChanged_handler_get: function() {
        null==this.__propertyChanged_handler&&(this.__propertyChanged_handler=ss.EventHandler.create(this, this.add_propertyChanged,
          this.remove_propertyChanged)); return this.__propertyChanged_handler
      }, __propertyChanged: null, __propertyChanged_handler: null, _picture: null, get_picture: function() { return this._picture }, set_picture: function(a) { if(a!==this._picture) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("Picture", lt.Annotations.Core.PropertyChangedStatus.after, this._picture, a); this._picture=a; this.onPropertyChanged(b) } return a }, get_logoRect: function() { return this._logoRect }, set_logoRect: function(a) {
        if(!lt.Annotations.Documents._leadRectDExtensions.equals(a,
          this._logoRect)) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("LogoRect", lt.Annotations.Core.PropertyChangedStatus.after, this._logoRect, a); this._logoRect=a; this.onPropertyChanged(b) } return a
      }, _stretchLogo: !0, get_stretchLogo: function() { return this._stretchLogo }, set_stretchLogo: function(a) { if(a!==this._stretchLogo) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("StretchLogo", lt.Annotations.Core.PropertyChangedStatus.after, this._stretchLogo, a); this._stretchLogo=a; this.onPropertyChanged(b) } return a },
      _angle: 0, get_angle: function() { return this._angle }, set_angle: function(a) { if(a!==this._angle) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("Angle", lt.Annotations.Core.PropertyChangedStatus.after, this._angle, a); this._angle=a; this.onPropertyChanged(b) } return a }, _opacity: 1, get_opacity: function() { return this._opacity }, set_opacity: function(a) {
        if(a!==this._opacity) {
          var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("Opacity", lt.Annotations.Core.PropertyChangedStatus.after, this._opacity,
            a); this._opacity=a; this.onPropertyChanged(b)
        } return a
      }, get_text: function() { return this._text }, set_text: function(a) { if(a!==this._text) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("Text", lt.Annotations.Core.PropertyChangedStatus.after, this._text, a); this._text=a; this.onPropertyChanged(b) } return a }, get_font: function() { return this._font }, set_font: function(a) {
        if(a!==this._font) {
          var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("Font", lt.Annotations.Core.PropertyChangedStatus.after,
            this._text, a); this._font=a; this.onPropertyChanged(b)
        } return a
      }, onPropertyChanged: function(a) { null!=this.__propertyChanged&&this.__propertyChanged(this, a) }, clone: function() { var a=new lt.Annotations.Documents.AnnBatesStampLogo; a.set_angle(this._angle); a.set_logoRect(this._logoRect); a.set_opacity(this._opacity); a.set_picture(this._picture.clone()); a.set_stretchLogo(this._stretchLogo); a.set_text(this._text); a.set_font(this._font); return a }
    }; Object.defineProperty(lt.Annotations.Documents.AnnBatesStampLogo.prototype,
      "propertyChanged", { get: lt.Annotations.Documents.AnnBatesStampLogo.prototype.__propertyChanged_handler_get, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampLogo.prototype, "picture", { get: lt.Annotations.Documents.AnnBatesStampLogo.prototype.get_picture, set: lt.Annotations.Documents.AnnBatesStampLogo.prototype.set_picture, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampLogo.prototype, "logoRect", {
        get: lt.Annotations.Documents.AnnBatesStampLogo.prototype.get_logoRect,
        set: lt.Annotations.Documents.AnnBatesStampLogo.prototype.set_logoRect, enumerable: !0, configurable: !0
      }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampLogo.prototype, "stretchLogo", { get: lt.Annotations.Documents.AnnBatesStampLogo.prototype.get_stretchLogo, set: lt.Annotations.Documents.AnnBatesStampLogo.prototype.set_stretchLogo, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampLogo.prototype, "angle", {
        get: lt.Annotations.Documents.AnnBatesStampLogo.prototype.get_angle,
        set: lt.Annotations.Documents.AnnBatesStampLogo.prototype.set_angle, enumerable: !0, configurable: !0
      }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampLogo.prototype, "opacity", { get: lt.Annotations.Documents.AnnBatesStampLogo.prototype.get_opacity, set: lt.Annotations.Documents.AnnBatesStampLogo.prototype.set_opacity, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampLogo.prototype, "text", {
        get: lt.Annotations.Documents.AnnBatesStampLogo.prototype.get_text,
        set: lt.Annotations.Documents.AnnBatesStampLogo.prototype.set_text, enumerable: !0, configurable: !0
      }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampLogo.prototype, "font", { get: lt.Annotations.Documents.AnnBatesStampLogo.prototype.get_font, set: lt.Annotations.Documents.AnnBatesStampLogo.prototype.set_font, enumerable: !0, configurable: !0 }); lt.Annotations.Documents.AnnBatesStampTranslator=function() { }; lt.Annotations.Documents.AnnBatesStampTranslator._createBatesText=function(a) { return lt.Annotations.Documents.AnnBatesText.create(a) };
  lt.Annotations.Documents.AnnBatesStampTranslator.prototype={
    _expressionStartSymbol: "{{", get_expressionStartSymbol: function() { return this._expressionStartSymbol }, set_expressionStartSymbol: function(a) { return this._expressionStartSymbol=a }, _expressionEndSymbol: "}}", get_expressionEndSymbol: function() { return this._expressionEndSymbol }, set_expressionEndSymbol: function(a) { return this._expressionEndSymbol=a }, _expressionSeparatingSymbol: "*", get_expressionSeparatingSymbol: function() { return this._expressionSeparatingSymbol },
    set_expressionSeparatingSymbol: function(a) { return this._expressionSeparatingSymbol=a }, readFromString: function(a) {
      if(String.isNullOrEmpty(a)) return null; for(var b=this._splitInputExpression(a), a=[], b=ss.IEnumerator.getEnumerator(b); b.moveNext();) {
        var c=b.current; String.isNullOrEmpty(c)||(this._isBatesText(c)? a.add(lt.Annotations.Documents.AnnBatesStampTranslator._createBatesText(c)):this._isBatesNumber(c)? a.add(this._createBatesNumber(c)):this._isBatesDateTime(c)? a.add(this._createBatesDateTime(c)):
          a.add(lt.Annotations.Documents.AnnBatesStampTranslator._createBatesText(c)))
      } return lt.Annotations.Documents._iAnnBatesElementHelper.toArray(a)
    }, _isBatesText: function(a) { return !lt.Annotations.Documents._regularExpressionHelper.matchPattern(this._expressionStartSymbol+".*"+this._expressionEndSymbol, a) }, _isBatesNumber: function(a) {
      return lt.Annotations.Documents._regularExpressionHelper.matchPattern(this._expressionStartSymbol+"BatesNumber\\"+this._expressionSeparatingSymbol+"\\d+\\"+this._expressionSeparatingSymbol+
        "(1|0)\\"+this._expressionSeparatingSymbol+"\\d+\\"+this._expressionSeparatingSymbol+"(1|0)(()|(\\"+this._expressionSeparatingSymbol+"(.*))|(\\"+this._expressionSeparatingSymbol+"(.*)\\"+this._expressionSeparatingSymbol+"(.*)))}}", a)
    }, _isBatesDateTime: function(a) {
      if(!lt.Annotations.Documents._regularExpressionHelper.matchPattern("BatesDateTime(.*?)", a)) return !1; a=lt.Annotations.Documents._stringHelper.trimStart(a, this._expressionStartSymbol); a=a.remove(0, 13); lt.Annotations.Documents._stringHelper.trimEnd(a,
        this._expressionEndSymbol); return !0
    }, _createBatesNumber: function(a) {
      a=a.remove(0, 14); a=lt.Annotations.Documents._stringHelper.trimEnd(a, this._expressionEndSymbol); arguments=lt.Annotations.Documents._stringHelper.split(a, this._expressionSeparatingSymbol); var b=new lt.Annotations.Documents.AnnBatesNumber; b.set_numberOfDigits(parseInt(arguments[0])); b.set_useAllDigits(lt.Annotations.Documents._stringHelper.toBoolean(arguments[1])); b.set_startNumber(parseInt(arguments[2])); b.set_autoIncrement(lt.Annotations.Documents._stringHelper.toBoolean(arguments[3]));
      4<arguments.length&&b.set_prefixText(arguments[4]); 5<arguments.length&&b.set_suffixText(arguments[5]); return b
    }, _createBatesDateTime: function(a) {
      a=a.remove(0, 15); a=lt.Annotations.Documents._stringHelper.trimEnd(a, this._expressionEndSymbol); arguments=lt.Annotations.Documents._stringHelper.split(a, this._expressionSeparatingSymbol); for(var b=[], c=0; c<arguments.length; c++)String.isNullOrEmpty(arguments[c])||b.add(arguments[c]); c=new lt.Annotations.Documents.AnnBatesDateTime; c.set_currentDateTime(Date.get_now());
      2===b.length&&(lt.Annotations.Documents._stringHelper.toLowerCase(b[0])===lt.Annotations.Documents._stringHelper.toLowerCase((1).toString())? c.set_kind(1):lt.Annotations.Documents._stringHelper.toLowerCase(b[0])===lt.Annotations.Documents._stringHelper.toLowerCase((0).toString())&&c.set_kind(0), c.set_format(b[1])); return c
    }, _splitInputExpression: function(a) { return lt.Annotations.Documents._regularExpressionHelper.split(this._expressionStartSymbol+".*?"+this._expressionEndSymbol, a) }, writeElementToString: function(a) {
      if(null==
        a) return ""; var b="", c=null, c=c=null, c=Type.safeCast(a, lt.Annotations.Documents.AnnBatesNumber); null!=c? b=this._writeBatesNumber(c):(c=Type.safeCast(a, lt.Annotations.Documents.AnnBatesDateTime), null!=c? b=this._writeBatesDateTime(c):(c=Type.safeCast(a, lt.Annotations.Documents.AnnBatesText), null!=c&&(b+=c.get_text()))); return b
    }, writeElementsToString: function(a) {
      if(null==a) throw new lt.ArgumentNullException("elements"); for(var b="", a=ss.IEnumerator.getEnumerator(a); a.moveNext();)b+=this.writeElementToString(a.current);
      return b
    }, _writeBatesNumber: function(a) {
      if(null==a) throw new lt.ArgumentNullException("batesNumber"); var b; b=""+this._expressionStartSymbol; b=b+"BatesNumber"+String.format(this._expressionSeparatingSymbol+"{0}", a.get_numberOfDigits()); b+=String.format(this._expressionSeparatingSymbol+"{0}", lt.Annotations.Documents._integerHelper.fromBoolean(a.get_useAllDigits())); b+=String.format(this._expressionSeparatingSymbol+"{0}", a.get_startNumber()); b+=String.format(this._expressionSeparatingSymbol+"{0}", lt.Annotations.Documents._integerHelper.fromBoolean(a.get_autoIncrement()));
      String.isNullOrEmpty(a.get_prefixText())||(b+=String.format(this._expressionSeparatingSymbol+"{0}", a.get_prefixText())); String.isNullOrEmpty(a.get_suffixText())||(String.isNullOrEmpty(a.get_prefixText())&&(b+=String.format(this._expressionSeparatingSymbol+"{0}", a.get_prefixText())), b+=String.format(this._expressionSeparatingSymbol+"{0}", a.get_suffixText())); return b+=this._expressionEndSymbol
    }, _writeBatesDateTime: function(a) {
      var b; b=""+this._expressionStartSymbol; b=b+"BatesDateTime"+String.format(this._expressionSeparatingSymbol+
        "{0}", a.get_kind().toString()); b+=String.format(this._expressionSeparatingSymbol+"{0}", a.get_format()); return b+=this._expressionEndSymbol
    }
  }; Object.defineProperty(lt.Annotations.Documents.AnnBatesStampTranslator.prototype, "expressionStartSymbol", { get: lt.Annotations.Documents.AnnBatesStampTranslator.prototype.get_expressionStartSymbol, set: lt.Annotations.Documents.AnnBatesStampTranslator.prototype.set_expressionStartSymbol, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampTranslator.prototype,
    "expressionEndSymbol", { get: lt.Annotations.Documents.AnnBatesStampTranslator.prototype.get_expressionEndSymbol, set: lt.Annotations.Documents.AnnBatesStampTranslator.prototype.set_expressionEndSymbol, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampTranslator.prototype, "expressionSeparatingSymbol", {
      get: lt.Annotations.Documents.AnnBatesStampTranslator.prototype.get_expressionSeparatingSymbol, set: lt.Annotations.Documents.AnnBatesStampTranslator.prototype.set_expressionSeparatingSymbol,
      enumerable: !0, configurable: !0
    }); lt.Annotations.Documents.AnnBatesText=function() { }; lt.Annotations.Documents.AnnBatesText.create=function(a) { var b=new lt.Annotations.Documents.AnnBatesText; b.set_text(a); return b }; lt.Annotations.Documents.AnnBatesText.prototype={
      add_propertyChanged: function(a) { this.__propertyChanged=ss.Delegate.combine(this.__propertyChanged, a) }, remove_propertyChanged: function(a) { this.__propertyChanged=ss.Delegate.remove(this.__propertyChanged, a) }, __propertyChanged_handler_get: function() {
        null==
        this.__propertyChanged_handler&&(this.__propertyChanged_handler=ss.EventHandler.create(this, this.add_propertyChanged, this.remove_propertyChanged)); return this.__propertyChanged_handler
      }, __propertyChanged: null, __propertyChanged_handler: null, _text: null, get_text: function() { return this._text }, set_text: function(a) { if(a!==this._text) { var b=new lt.Annotations.Core.AnnPropertyChangedEventArgs("Text", lt.Annotations.Core.PropertyChangedStatus.after, this._text, a); this._text=a; this.onPropertyChanged(b) } return a },
      asString: function() { return this._text }, _friendlyName: "BatesText", get_friendlyName: function() { return this._friendlyName }, set_friendlyName: function(a) { return this._friendlyName=a }, toString: function() { return this._friendlyName }, onPropertyChanged: function(a) { null!=this.__propertyChanged&&this.__propertyChanged(this, a) }, clone: function() { var a=new lt.Annotations.Documents.AnnBatesText; a.set_text(this._text); return a }
    }; Object.defineProperty(lt.Annotations.Documents.AnnBatesText.prototype, "propertyChanged",
      { get: lt.Annotations.Documents.AnnBatesText.prototype.__propertyChanged_handler_get, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesText.prototype, "text", { get: lt.Annotations.Documents.AnnBatesText.prototype.get_text, set: lt.Annotations.Documents.AnnBatesText.prototype.set_text, enumerable: !0, configurable: !0 }); Object.defineProperty(lt.Annotations.Documents.AnnBatesText.prototype, "friendlyName", {
        get: lt.Annotations.Documents.AnnBatesText.prototype.get_friendlyName,
        set: lt.Annotations.Documents.AnnBatesText.prototype.set_friendlyName, enumerable: !0, configurable: !0
      }); lt.Annotations.Documents._dateTimeHelper=function() { }; lt.Annotations.Documents._dateTimeHelper.asString=function(a, b) { return null==a? "":1===b? a.toLocaleDateString():a.toUTCString() }; lt.Annotations.Documents._iAnnBatesElementHelper=function() { }; lt.Annotations.Documents._iAnnBatesElementHelper.toArray=function(a) { if(null==a) return null; for(var b=a.length, c=Array(5), d=0; d<b; d++)c[d]=a[d].clone(); return c };
  lt.Annotations.Documents._integerHelper=function() { }; lt.Annotations.Documents._integerHelper.fromBoolean=function(a) { return a? 1:0 }; lt.Annotations.Documents._stringHelper=function() { }; lt.Annotations.Documents._stringHelper.trimEnd=function(a, b) { if(1===(String.isNullOrEmpty(a)|String.isNullOrEmpty(b))) return a; var c=b.length, d=a.length; return c>d? a:a.remove(d-c, c) }; lt.Annotations.Documents._stringHelper.trimStart=function(a, b) { return a.replace(b, "") }; lt.Annotations.Documents._stringHelper.split=function(a,
    b) { return 1===(String.isNullOrEmpty(a)|String.isNullOrEmpty(b))? null:a.split(b) }; lt.Annotations.Documents._stringHelper.toBoolean=function(a) { return String.isNullOrEmpty(a)? !1:"1"===a? !0:!1 }; lt.Annotations.Documents._stringHelper.toLowerCase=function(a) { return a.toLowerCase() }; lt.Annotations.Documents._regularExpressionHelper=function() { }; lt.Annotations.Documents._regularExpressionHelper.matchPattern=function(a, b) { return RegExp(a, "g").test(b) }; lt.Annotations.Documents._regularExpressionHelper.split=
      function(a, b) { var c=RegExp(a, "g"), d=[], e; do e=c.exec(b), null!=e&&d.add(e[0]); while(null!=e); c=Array(d.length); for(e=0; e<d.length; e++)c[e]=d[e]; return c }; lt.Annotations.Documents.AnnBatesElementCollection=function() { this._elements=[] }; lt.Annotations.Documents.AnnBatesElementCollection.prototype={
        get_count: function() { return this._elements.length }, clear: function() {
          for(var a=lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.reset), b=ss.IEnumerator.getEnumerator(this._elements); b.moveNext();) {
            var c=
              b.current; a.get_newItems().add(c)
          } this._elements.clear(); this.onCollectionChanged(a)
        }, remove: function(a) { this.removeItem(a) }, add: function(a) { this._elements.add(a); var b=lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.add); b.get_newItems().add(a); this.onCollectionChanged(b) }, contains: function(a) { return this._elements.contains(a) }, getEnumerator: function() { return this._elements.getEnumerator() }, _copyTo: function(a, b, c) {
          for(var d=0, b=
            ss.IEnumerator.getEnumerator(b); b.moveNext();)d<c||(a[d]=b.current, d++)
        }, toArray: function() { var a=Array(this._elements.length); this._copyTo(a, this._elements, 0); return a }, insertItem: function(a, b) { this._elements[a]=b; var c=lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.add); c.get_newItems().add(b); this.onCollectionChanged(c) }, move: function(a, b) { this.moveItem(a, b) }, moveItem: function(a, b) {
          var c=this._elements[a]; this._elements.remove(c);
          this._elements.insert(b, c); this.onCollectionChanged(lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.move))
        }, onCollectionChanged: function(a) { null!=this.__collectionChanged&&this.__collectionChanged(this, a) }, removeAt: function(a) { this.removeItem(this.get_item(a)) }, removeItem: function(a) {
          var b=lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.remove); b.get_newItems().add(a);
          this._elements.remove(a); this.onCollectionChanged(b)
        }, setItem: function(a, b) { this._elements[a]=b; this.onCollectionChanged(lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.replace)) }, clearItems: function() {
          for(var a=lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.reset), b=ss.IEnumerator.getEnumerator(this._elements); b.moveNext();) { var c=b.current; a.get_newItems().add(c) } this._elements.clear();
          this.onCollectionChanged(a)
        }, indexOf: function(a) { return this._elements.indexOf(a) }, add_collectionChanged: function(a) { this.__collectionChanged=ss.Delegate.combine(this.__collectionChanged, a) }, remove_collectionChanged: function(a) { this.__collectionChanged=ss.Delegate.remove(this.__collectionChanged, a) }, __collectionChanged_handler_get: function() {
          null==this.__collectionChanged_handler&&(this.__collectionChanged_handler=ss.EventHandler.create(this, this.add_collectionChanged, this.remove_collectionChanged));
          return this.__collectionChanged_handler
        }, __collectionChanged: null, __collectionChanged_handler: null, sendToBack: function() { }, bringToFront: function() { }, get_item: function(a) { return this._elements[a] }, set_item: function(a, b) { return this._elements[a]=b }, item: function(a, b) { return void 0===b? this.get_item(a):this.set_item(a, b) }
      }; Object.defineProperty(lt.Annotations.Documents.AnnBatesElementCollection.prototype, "count", {
        get: lt.Annotations.Documents.AnnBatesElementCollection.prototype.get_count, enumerable: !0,
        configurable: !0
      }); Object.defineProperty(lt.Annotations.Documents.AnnBatesElementCollection.prototype, "collectionChanged", { get: lt.Annotations.Documents.AnnBatesElementCollection.prototype.__collectionChanged_handler_get, enumerable: !0, configurable: !0 }); lt.Annotations.Documents.AnnBatesStampCollection=function() { this._elements=[] }; lt.Annotations.Documents.AnnBatesStampCollection.prototype={
        get_count: function() { return this._elements.length }, clear: function() {
          for(var a=lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.reset),
            b=ss.IEnumerator.getEnumerator(this._elements); b.moveNext();) { var c=b.current; a.get_newItems().add(c) } this._elements.clear(); this.onCollectionChanged(a)
        }, remove: function(a) { this.removeItem(a) }, add: function(a) { this._elements.add(a); var b=lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.add); b.get_newItems().add(a); this.onCollectionChanged(b) }, contains: function(a) { return this._elements.contains(a) }, getEnumerator: function() { return this._elements.getEnumerator() },
        _copyTo: function(a, b, c) { for(var d=0, b=ss.IEnumerator.getEnumerator(b); b.moveNext();)d<c||(a[d]=b.current, d++) }, toArray: function() { var a=Array(this._elements.length); this._copyTo(a, this._elements, 0); return a }, insertItem: function(a, b) { this._elements[a]=b; var c=lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.add); c.get_newItems().add(b); this.onCollectionChanged(c) }, move: function(a, b) { this.moveItem(a, b) }, moveItem: function(a, b) {
          var c=
            this._elements[a]; this._elements.remove(c); this._elements.insert(b, c); this.onCollectionChanged(lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.move))
        }, onCollectionChanged: function(a) { null!=this.__collectionChanged&&this.__collectionChanged(this, a) }, removeAt: function(a) { this.removeItem(this.get_item(a)) }, removeItem: function(a) {
          var b=lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.remove);
          b.get_newItems().add(a); this._elements.remove(a); this.onCollectionChanged(b)
        }, setItem: function(a, b) { this._elements[a]=b; this.onCollectionChanged(lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.replace)) }, clearItems: function() {
          for(var a=lt.Annotations.Core.AnnNotifyCollectionChangedEventArgs.create(lt.Annotations.Core.AnnNotifyCollectionChangedAction.reset), b=ss.IEnumerator.getEnumerator(this._elements); b.moveNext();) {
            var c=b.current;
            a.get_newItems().add(c)
          } this._elements.clear(); this.onCollectionChanged(a)
        }, indexOf: function(a) { return this._elements.indexOf(a) }, add_collectionChanged: function(a) { this.__collectionChanged=ss.Delegate.combine(this.__collectionChanged, a) }, remove_collectionChanged: function(a) { this.__collectionChanged=ss.Delegate.remove(this.__collectionChanged, a) }, __collectionChanged_handler_get: function() {
          null==this.__collectionChanged_handler&&(this.__collectionChanged_handler=ss.EventHandler.create(this, this.add_collectionChanged,
            this.remove_collectionChanged)); return this.__collectionChanged_handler
        }, __collectionChanged: null, __collectionChanged_handler: null, sendToBack: function() { }, bringToFront: function() { }, get_item: function(a) { return this._elements[a] }, set_item: function(a, b) { return this._elements[a]=b }, item: function(a, b) { return void 0===b? this.get_item(a):this.set_item(a, b) }
      }; Object.defineProperty(lt.Annotations.Documents.AnnBatesStampCollection.prototype, "count", {
        get: lt.Annotations.Documents.AnnBatesStampCollection.prototype.get_count,
        enumerable: !0, configurable: !0
      }); Object.defineProperty(lt.Annotations.Documents.AnnBatesStampCollection.prototype, "collectionChanged", { get: lt.Annotations.Documents.AnnBatesStampCollection.prototype.__collectionChanged_handler_get, enumerable: !0, configurable: !0 }); lt.Annotations.Documents._lT_VersionNumber.registerClass("lt.Annotations.Documents._lT_VersionNumber"); lt.Annotations.Documents._leadRectDExtensions.registerClass("lt.Annotations.Documents._leadRectDExtensions"); lt.Annotations.Documents._leadPointDExtensions.registerClass("lt.Annotations.Documents._leadPointDExtensions");
  lt.Annotations.Documents._leadLengthDExtensions.registerClass("lt.Annotations.Documents._leadLengthDExtensions"); lt.Annotations.Documents._leadSizeDExtensions.registerClass("lt.Annotations.Documents._leadSizeDExtensions"); lt.Annotations.Documents.AnnBatesDateTime.registerClass("lt.Annotations.Documents.AnnBatesDateTime", null, lt.Annotations.Documents.IAnnBatesElement); lt.Annotations.Documents.AnnBatesNumber.registerClass("lt.Annotations.Documents.AnnBatesNumber", null, lt.Annotations.Documents.IAnnBatesElement);
  lt.Annotations.Documents.AnnBatesStamp.registerClass("lt.Annotations.Documents.AnnBatesStamp", null, ss.IDisposable); lt.Annotations.Documents.AnnBatesStampComposer.registerClass("lt.Annotations.Documents.AnnBatesStampComposer", null, ss.IDisposable); lt.Annotations.Documents.AnnBatesStampLogo.registerClass("lt.Annotations.Documents.AnnBatesStampLogo"); lt.Annotations.Documents.AnnBatesStampTranslator.registerClass("lt.Annotations.Documents.AnnBatesStampTranslator"); lt.Annotations.Documents.AnnBatesText.registerClass("lt.Annotations.Documents.AnnBatesText",
    null, lt.Annotations.Documents.IAnnBatesElement); lt.Annotations.Documents._dateTimeHelper.registerClass("lt.Annotations.Documents._dateTimeHelper"); lt.Annotations.Documents._iAnnBatesElementHelper.registerClass("lt.Annotations.Documents._iAnnBatesElementHelper"); lt.Annotations.Documents._integerHelper.registerClass("lt.Annotations.Documents._integerHelper"); lt.Annotations.Documents._stringHelper.registerClass("lt.Annotations.Documents._stringHelper"); lt.Annotations.Documents._regularExpressionHelper.registerClass("lt.Annotations.Documents._regularExpressionHelper");
  lt.Annotations.Documents.AnnBatesElementCollection.registerClass("lt.Annotations.Documents.AnnBatesElementCollection"); lt.Annotations.Documents.AnnBatesStampCollection.registerClass("lt.Annotations.Documents.AnnBatesStampCollection"); lt.Annotations.Documents._lT_VersionNumber.l_VER_PRODUCT="LEADTOOLS\u00ae for JavaScript"; lt.Annotations.Documents._lT_VersionNumber.l_VER_COMPANYNAME_STR="LEAD Technologies, Inc."; lt.Annotations.Documents._lT_VersionNumber.l_VER_LEGALTRADEMARKS_STR="LEADTOOLS\u00ae is a trademark of LEAD Technologies, Inc.";
  lt.Annotations.Documents._lT_VersionNumber.l_VER_LEGALCOPYRIGHT_STR="\u00a9 1991-2017 LEAD Technologies, Inc."; lt.Annotations.Documents._lT_VersionNumber.l_VER_DLLEXT=".dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_EXEEXT=".exe"; lt.Annotations.Documents._lT_VersionNumber.l_VER_PLATFORM=""; lt.Annotations.Documents._lT_VersionNumber.l_VER_PLATFORM_FOR=""; lt.Annotations.Documents._lT_VersionNumber.l_VER_PRODUCTNAME_STR="LEADTOOLS\u00ae for JavaScript"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_XXX=
    "Leadtools.Xxx.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_XXX="LEADTOOLS Xxx"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_KERNEL="lt.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_KERNEL="Leadtools"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CONTROLS="lt.Controls.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CONTROLS="Controls"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_DOCUMENTS_UI=
      "lt.Documents.UI.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_DOCUMENTS_UI="Documents User Interface"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CONTROLS_MEDICAL="lt.Controls.Medical.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CONTROLS_MEDICAL="Medical Controls"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_DOCUMENTS="lt.Documents.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_DOCUMENTS=
        "Documents"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_CORE="lt.Annotations.Core.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_CORE="Annotations Core"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_AUTOMATION="lt.Annotations.Automation.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_AUTOMATION="Annotations Automation"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_DESIGNERS=
          "lt.Annotations.Designers.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_DESIGNERS="Annotations Designers"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_RENDERING="lt.Annotations.Rendering.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_RENDERING="Annotations Rendering"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_CCOW="Leadtools.Ccow.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_CCOW=
            "Leadtools CCOW Library"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_DOCUMENTS="Leadtools.Annotations.Documents.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_DOCUMENTS="Annotations Documents"; lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_LEGACY="Leadtools.Annotations.Legacy.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_LEGACY="Annotations Legacy";
  lt.Annotations.Documents._lT_VersionNumber.l_VER_ORIGINALFILENAME_STR_ANNOTATIONS_JAVASCRIPT="Leadtools.Annotations.JavaScript.dll"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEDESCRIPTION_STR_ANNOTATIONS_JAVASCRIPT="JavaScripot Annotations"; lt.Annotations.Documents._lT_VersionNumber.l_VER_PRODUCTVERSION_DOT_STR="********"; lt.Annotations.Documents._lT_VersionNumber.l_VER_FILEVERSION_DOT_STR="********"; lt.Annotations.Documents.AnnBatesStampComposer._renderingEngine=null
})();
