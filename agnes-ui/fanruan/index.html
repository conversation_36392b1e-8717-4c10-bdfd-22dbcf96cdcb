<!--
 * @Des: 
 * @version: 
 * @Author: hui.nie
 * @Date: 2024-09-26 11:08:41
 * @LastEditors: hui.nie
 * @LastEditTime: 2024-09-26 17:09:13
-->
<html>
<head>
    <script type="text/javascript" src="../lib/jQuery/jquery.js"></script>
    <script type="module">
        import { ys1Url, ys2Url, crm1Url, crm2Url, ys3Url } from '@/utils/common';
    </script>
    <script type="text/javascript">
        function getUrlSearchParams(key) {
            const searUrlStr = window.location.search
            const paramStr = searUrlStr.split('?')[1]
            const searchParams = new URLSearchParams(paramStr)
            return searchParams.get(key)
        }

        $(function () {
            $("#username").val("finereport");
            $("#password").val("Fine1199");
            doSubmit();
        })

        function doSubmit() {
            var stype= 0;
            var path = "";
            if (document.referrer && document.referrer.startsWith(ys1Url)) {
                path = ys2Url;
                stype = 1;
            } else {
                path = crm1Url;
            }

            var path = getUrlSearchParams("url");
            var username = document.getElementById("username").value; //获取输入的用户名
            var password = document.getElementById("password").value; //获取输入的参数
            const corsProxy = corsUrl;
            const targetUrl = `${corsProxy}${path}`;
            jQuery.ajax({
                url: targetUrl + "?fine_username=" + username + "&fine_password=" + password + "&validity=" + -1,
                async: true,
                success: function (res) {
                    if (res.errorCode) {
                        alert("用户名或密码错误"); //登录失败（用户名或密码错误）
                    } else {
                        //登录成功
                        if (stype == 1) {
                            window.location = path.replace(crm2Url,ys3Url); //认证成功跳转页面，因为ajax不支持重定向所有需要跳转的设置
                        }else  {
                            window.location = path; //认证成功跳转页面，因为ajax不支持重定向所有需要跳转的设置
                        }
                    }
                },
                error: function (res) {
                    if (res.errorCode) {
                        alert("登录失败服务器超时或其他错误"); //登录失败（用户名或密码错误）

                    } else {
                        //登录成功
                        if (stype == 1) {
                            window.location = url.replace(crm2Url,ys3Url); //认证成功跳转页面，因为ajax不支持重定向所有需要跳转的设置
                        }else  {
                            window.location = url; //认证成功跳转页面，因为ajax不支持重定向所有需要跳转的设置
                        }
                    }
                }
            });
        }
    </script>
</head>
<body>
<form id="login" name="login" method="POST" style="display:none" autocomplete="off">
    <p>
        <input id="username" type="text"/>
    </p>
    <p>
        <input id="password" type="password"/>
    </p>
    <input type="button" value="登录" onClick="doSubmit()"/>
</form>
<div style="height:100%;width:100%;text-align:center;display:block;line-height:100%;font-size:40px"> 登陆中,请稍候……</div>
</body>
</html>
