window.sheetFormula={name:"<PERSON>",color:"",config:{merge:{"12_2":{rs:1,cs:6,r:12,c:2},"19_2":{rs:1,cs:6,r:19,c:2},"20_6":{rs:1,cs:5,r:20,c:6},"22_6":{rs:1,cs:2,r:22,c:6},"23_6":{rs:1,cs:2,r:23,c:6},"28_2":{rs:1,cs:6,r:28,c:2},"31_6":{rs:1,cs:3,r:31,c:6},"33_6":{rs:1,cs:3,r:33,c:6},"35_6":{rs:1,cs:3,r:35,c:6},"37_6":{rs:1,cs:3,r:37,c:6},"29_6":{r:29,c:6,rs:1,cs:3}},rowlen:{},columnlen:{0:111,2:105,3:82,4:71,5:84,6:123,7:48,8:192,9:56,10:56}},index:"1",chart:[],order:"1",column:18,row:45,celldata:[{r:0,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:0,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:0,c:2,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:0,c:3,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:0,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:0,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:0,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:0,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:0,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:0,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:0,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:0,v:{v:"Basic Function",ct:{fa:"General",t:"g"},m:"Basic Function",bg:null,bl:1,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:2,v:{v:"Name",ct:{fa:"General",t:"g"},m:"Name",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:3,v:{v:"Age",ct:{fa:"General",t:"g"},m:"Age",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:6,v:{v:"Indirect Function",ct:{fa:"General",t:"g"},m:"Indirect Function",bg:null,bl:1,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:8,v:{v:"J2",ct:{fa:"General",t:"g"},m:"J2",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:9,v:{v:1,ct:{fa:"General",t:"n"},m:"1",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:1,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:2,v:{v:"Jack",ct:{fa:"General",t:"g"},m:"Jack",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:3,v:{v:17,ct:{fa:"General",t:"n"},m:"17",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:8,v:{v:"I",ct:{fa:"General",t:"g"},m:"I",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:9,v:{v:2,ct:{fa:"General",t:"n"},m:"2",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:2,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:2,v:{v:"Lily",ct:{fa:"General",t:"g"},m:"Lily",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:3,v:{v:23,ct:{fa:"General",t:"n"},m:"23",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:8,v:{v:"J",ct:{fa:"General",t:"g"},m:"J",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:9,v:{v:3,ct:{fa:"General",t:"n"},m:"3",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:3,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:2,v:{v:"Bob",ct:{fa:"General",t:"g"},m:"Bob",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:3,v:{v:30,ct:{fa:"General",t:"n"},m:"30",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:4,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:5,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:5,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:5,c:2,v:{v:"Mary",ct:{fa:"General",t:"g"},m:"Mary",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:5,c:3,v:{v:25,ct:{fa:"General",t:"n"},m:"25",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:5,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:5,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:5,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:5,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:5,c:8,v:{v:'=INDIRECT("I2")',ct:{fa:"General",t:"g"},m:'=INDIRECT("I2")',bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:5,c:9,v:{v:"J2",ct:{fa:"General",t:"g"},m:"J2",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:'=INDIRECT("I2")'}},{r:5,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:6,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:6,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:6,c:2,v:{v:"Average Age:",ct:{fa:"General",t:"g"},m:"Average Age:",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:6,c:3,v:{v:23.75,ct:{fa:"General",t:"n"},m:"23.75",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:"=AVERAGE(D3:D6)"}},{r:6,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:6,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:6,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:6,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:6,c:8,v:{v:"=INDIRECT(I2)",ct:{fa:"General",t:"g"},m:"=INDIRECT(I2)",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:6,c:9,v:{v:1,ct:{fa:"General",t:"n"},m:"1",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:"=INDIRECT(I2)"}},{r:6,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:7,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:7,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:7,c:2,v:{v:"Max Age:",ct:{fa:"General",t:"g"},m:"Max Age:",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:7,c:3,v:{v:30,ct:{fa:"General",t:"n"},m:"30",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:"=MAX(D3:D6)"}},{r:7,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:7,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:7,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:7,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:7,c:8,v:{v:'=INDIRECT("I"&(1+2))',ct:{fa:"General",t:"g"},m:'=INDIRECT("I"&(1+2))',bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:7,c:9,v:{v:"I",ct:{fa:"General",t:"g"},m:"I",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:'=INDIRECT("I"&(1+2))'}},{r:7,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:8,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:8,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:8,c:2,v:{v:"Min Age:",ct:{fa:"General",t:"g"},m:"Min Age:",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:8,c:3,v:{v:17,ct:{fa:"General",t:"n"},m:"17",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:"=MIN(D3:D6)"}},{r:8,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:8,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:8,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:8,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:8,c:8,v:{v:"=INDIRECT(I4&J3)",ct:{fa:"General",t:"g"},m:"=INDIRECT(I4&J3)",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:8,c:9,v:{v:1,ct:{fa:"General",t:"n"},m:"1",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:"=INDIRECT(I4&J3)"}},{r:8,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:9,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:9,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:9,c:2,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:9,c:3,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:9,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:9,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:9,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:9,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:9,c:8,v:{v:'=INDIRECT("Formula!"&I2)',ct:{fa:"General",t:"g"},m:'=INDIRECT("Formula!"&I2)',bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:9,c:9,v:{ct:{fa:"General",t:"n"},bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,v:1,m:"1",f:'=INDIRECT("Formula!"&I2)'}},{r:9,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:10,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:10,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:10,c:2,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:10,c:3,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:10,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:10,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:10,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:10,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:10,c:8,v:{v:'=INDIRECT("Formula!I2")',ct:{fa:"General",t:"g"},m:'=INDIRECT("Formula!I2")',bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:10,c:9,v:{ct:{fa:"General",t:"g"},bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,m:"J2",v:"J2",f:'=INDIRECT("Formula!I2")'}},{r:10,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:2,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:3,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:11,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:12,c:0,v:{v:"Array Formula",ct:{fa:"General",t:"g"},m:"Array Formula",bg:null,bl:1,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:12,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:12,c:2,v:{v:"Calculation",ct:{fa:"General",t:"g"},m:"Calculation",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{rs:1,cs:6,r:12,c:2}}},{r:12,c:3,v:{mc:{r:12,c:2}}},{r:12,c:4,v:{mc:{r:12,c:2}}},{r:12,c:5,v:{mc:{r:12,c:2}}},{r:12,c:6,v:{mc:{r:12,c:2}}},{r:12,c:7,v:{mc:{r:12,c:2}}},{r:12,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:12,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:12,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:2,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:3,v:{v:"Match",ct:{fa:"General",t:"g"},m:"Match",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:4,v:{v:"Physical",ct:{fa:"General",t:"g"},m:"Physical",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:5,v:{v:"Chemistry",ct:{fa:"General",t:"g"},m:"Chemistry",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:6,v:{v:"Alex",ct:{fa:"General",t:"g"},m:"Alex",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:7,v:{v:"Sum",ct:{fa:"General",t:"g"},m:"Sum",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:13,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:14,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:14,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:14,c:2,v:{v:"Alice",ct:{fa:"General",t:"g"},m:"Alice",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:14,c:3,v:{v:97,ct:{fa:"General",t:"n"},bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,m:"97"}},{r:14,c:4,v:{v:61,ct:{fa:"General",t:"n"},bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,m:"61"}},{r:14,c:5,v:{v:53,ct:{fa:"General",t:"n"},m:"53",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:14,c:6,v:{v:43,ct:{fa:"General",t:"n"},m:"43",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:14,c:7,v:{ct:{fa:"General",t:"n"},bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,v:207,m:"207",f:"=SUBTOTAL(9,OFFSET($D$15,ROW($D$15:$D$18)-ROW($D$15),1,3))"}},{r:14,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:14,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:14,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:15,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:15,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:15,c:2,v:{v:"John",ct:{fa:"General",t:"g"},m:"John",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:15,c:3,v:{v:65,ct:{fa:"General",t:"n"},m:"65",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:15,c:4,v:{v:76,ct:{fa:"General",t:"n"},m:"76",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:15,c:5,v:{v:65,ct:{fa:"General",t:"n"},m:"65",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:15,c:6,v:{v:55,ct:{fa:"General",t:"n"},m:"55",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:15,c:7,v:{ct:{fa:"General",t:"n"},bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,v:182,m:"182",f:"=SUBTOTAL(9,OFFSET(E15,ROW(E15:E18)-ROW(E15),1,3))"}},{r:15,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,ct:{fa:"General",t:"n"}}},{r:15,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:15,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:16,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:16,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:16,c:2,v:{v:"Bob",ct:{fa:"General",t:"g"},m:"Bob",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:16,c:3,v:{v:55,ct:{fa:"General",t:"n"},m:"55",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:16,c:4,v:{v:70,ct:{fa:"General",t:"n"},m:"70",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:16,c:5,v:{v:64,ct:{fa:"General",t:"n"},m:"64",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:16,c:6,v:{v:54,ct:{fa:"General",t:"n"},m:"54",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:16,c:7,v:{v:152,ct:{fa:"General",t:"n"},m:"152",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:"=SUBTOTAL(9,OFFSET(F15,ROW(F15:F18)-ROW(F15),1,3))"}},{r:16,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:16,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:16,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:17,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:17,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:17,c:2,v:{v:"Jack",ct:{fa:"General",t:"g"},m:"Jack",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:17,c:3,v:{v:89,ct:{fa:"General",t:"n"},m:"89",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:17,c:4,v:{v:77,ct:{fa:"General",t:"n"},m:"77",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:17,c:5,v:{v:73,ct:{fa:"General",t:"n"},m:"73",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:17,c:6,v:{v:73,ct:{fa:"General",t:"n"},m:"73",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:17,c:7,v:{v:541,ct:{fa:"General",t:"n"},m:"541",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:"=SUBTOTAL(9,OFFSET(G15,ROW(G15:G18)-ROW(G15),1,3))"}},{r:17,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:17,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:17,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:2,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:3,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:18,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:19,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:19,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:19,c:2,v:{v:"Search",ct:{fa:"General",t:"g"},m:"Search",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{rs:1,cs:6,r:19,c:2}}},{r:19,c:3,v:{mc:{r:19,c:2}}},{r:19,c:4,v:{mc:{r:19,c:2}}},{r:19,c:5,v:{mc:{r:19,c:2}}},{r:19,c:6,v:{mc:{r:19,c:2}}},{r:19,c:7,v:{mc:{r:19,c:2}}},{r:19,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:19,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:19,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:20,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:20,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:20,c:2,v:{v:"apple",ct:{fa:"General",t:"g"},m:"apple",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:20,c:3,v:{v:"apple",ct:{fa:"General",t:"g"},m:"apple",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:20,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:20,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:20,c:6,v:{ct:{fa:"General",t:"g"},bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{rs:1,cs:5,r:20,c:6}}},{r:20,c:7,v:{mc:{r:20,c:6}}},{r:20,c:8,v:{mc:{r:20,c:6}}},{r:20,c:9,v:{mc:{r:20,c:6}}},{r:20,c:10,v:{mc:{r:20,c:6}}},{r:21,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:21,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:21,c:2,v:{v:"banana",ct:{fa:"General",t:"g"},m:"banana",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:21,c:3,v:{v:"pear",ct:{fa:"General",t:"g"},m:"pear",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:21,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:21,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:21,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:21,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:21,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:21,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:21,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:22,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:22,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:22,c:2,v:{v:"pear",ct:{fa:"General",t:"g"},m:"pear",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:22,c:3,v:{v:"potato",ct:{fa:"General",t:"g"},m:"potato",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:22,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:22,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:22,c:6,v:{v:"ArrayFormula Result:",ct:{fa:"General",t:"g"},m:"ArrayFormula Result:",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{rs:1,cs:2,r:22,c:6}}},{r:22,c:7,v:{mc:{r:22,c:6}}},{r:22,c:8,v:{v:"dumpling",ct:{fa:"General",t:"g"},m:"dumpling",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:'=INDEX(D21:D25,MATCH("dumpling",D21:D25),1)'}},{r:22,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,ct:{fa:"General",t:"b"}}},{r:22,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,ct:{fa:"General",t:"n"}}},{r:23,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:23,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:23,c:2,v:{v:"tomato",ct:{fa:"General",t:"g"},m:"tomato",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:23,c:3,v:{v:"potato",ct:{fa:"General",t:"g"},m:"potato",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:23,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:23,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:23,c:6,v:{v:"NomalFormula Result:",ct:{fa:"General",t:"g"},m:"NomalFormula Result:",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{rs:1,cs:2,r:23,c:6}}},{r:23,c:7,v:{mc:{r:23,c:6}}},{r:23,c:8,v:{ct:{fa:"General",t:"b"},bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,m:"FALSE",v:!1,f:"=ISNA(MATCH(D21:D25,C21:C27,0))"}},{r:23,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,ct:{fa:"General",t:"b"}}},{r:23,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:24,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:24,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:24,c:2,v:{v:"potato",ct:{fa:"General",t:"g"},m:"potato",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:24,c:3,v:{v:"dumpling",ct:{fa:"General",t:"g"},m:"dumpling",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:24,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:24,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:24,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:24,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:24,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,ct:{fa:"General",t:"e"}}},{r:24,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:24,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:2,v:{v:"cake",ct:{fa:"General",t:"g"},m:"cake",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:3,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:25,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:2,v:{v:"noodel",ct:{fa:"General",t:"g"},m:"noodel",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:3,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:26,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:2,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:3,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:4,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:6,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:27,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:28,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:28,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:28,c:2,v:{v:"Statistics",ct:{fa:"General",t:"g"},m:"Statistics",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{rs:1,cs:6,r:28,c:2}}},{r:28,c:3,v:{mc:{r:28,c:2}}},{r:28,c:4,v:{mc:{r:28,c:2}}},{r:28,c:5,v:{mc:{r:28,c:2}}},{r:28,c:6,v:{mc:{r:28,c:2}}},{r:28,c:7,v:{mc:{r:28,c:2}}},{r:28,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:28,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:28,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:29,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:29,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:29,c:2,v:{v:"Product",ct:{fa:"General",t:"g"},m:"Product",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:29,c:3,v:{v:"Salesman",ct:{fa:"General",t:"g"},m:"Salesman",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:29,c:4,v:{v:"Units Sold",ct:{fa:"General",t:"g"},m:"Units Sold",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:29,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:29,c:6,v:{v:"Summing Sales: Faxes Sold By Brown",ct:{fa:"General",t:"g"},m:"Summing Sales: Faxes Sold By Brown",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{r:29,c:6,rs:1,cs:3}}},{r:29,c:7,v:{mc:{r:29,c:6}}},{r:29,c:8,v:{mc:{r:29,c:6}}},{r:29,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:29,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:30,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:30,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:30,c:2,v:{v:"Fax",ct:{fa:"General",t:"g"},m:"Fax",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:30,c:3,v:{v:"Brown",ct:{fa:"General",t:"g"},m:"Brown",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:30,c:4,v:{v:1,ct:{fa:"General",t:"n"},m:"1",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:30,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:30,c:6,v:{v:61,ct:{fa:"General",t:"n"},m:"61",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:'=SUM((C31:C39="Fax")*(D31:D39="Brown")*(E31:E39))'}},{r:30,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:30,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:30,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:30,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:31,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:31,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:31,c:2,v:{v:"Phone",ct:{fa:"General",t:"g"},m:"Phone",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:31,c:3,v:{v:"Smith",ct:{fa:"General",t:"g"},m:"Smith",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:31,c:4,v:{v:10,ct:{fa:"General",t:"n"},m:"10",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:31,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:31,c:6,v:{v:"Logical AND (Faxes And Brown)",ct:{fa:"General",t:"g"},m:"Logical AND (Faxes And Brown)",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{rs:1,cs:3,r:31,c:6}}},{r:31,c:7,v:{mc:{r:31,c:6}}},{r:31,c:8,v:{mc:{r:31,c:6}}},{r:31,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:31,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:32,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:32,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:32,c:2,v:{v:"Fax",ct:{fa:"General",t:"g"},m:"Fax",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:32,c:3,v:{v:"Jones",ct:{fa:"General",t:"g"},m:"Jones",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:32,c:4,v:{v:20,ct:{fa:"General",t:"n"},m:"20",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:32,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:32,c:6,v:{v:2,ct:{fa:"General",t:"n"},m:"2",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:'=SUM((C31:C39="Fax")*(D31:D39="Brown"))'}},{r:32,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:32,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:32,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:32,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:33,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:33,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:33,c:2,v:{v:"Fax",ct:{fa:"General",t:"g"},m:"Fax",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:33,c:3,v:{v:"Smith",ct:{fa:"General",t:"g"},m:"Smith",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:33,c:4,v:{v:30,ct:{fa:"General",t:"n"},m:"30",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:33,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:33,c:6,v:{v:"Logical OR (Faxes Or Jones)",ct:{fa:"General",t:"g"},m:"Logical OR (Faxes Or Jones)",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{rs:1,cs:3,r:33,c:6}}},{r:33,c:7,v:{mc:{r:33,c:6}}},{r:33,c:8,v:{mc:{r:33,c:6}}},{r:33,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:33,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:34,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:34,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:34,c:2,v:{v:"Phone",ct:{fa:"General",t:"g"},m:"Phone",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:34,c:3,v:{v:"Jones",ct:{fa:"General",t:"g"},m:"Jones",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:34,c:4,v:{v:40,ct:{fa:"General",t:"n"},m:"40",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:34,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:34,c:6,v:{v:1,ct:{fa:"General",t:"n"},m:"1",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:'=SUM(IF((C31:C39="Fax")+(D31:D39="Jones"),1,0))'}},{r:34,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:34,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:34,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:34,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:35,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:35,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:35,c:2,v:{v:"PC",ct:{fa:"General",t:"g"},m:"PC",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:35,c:3,v:{v:"Smith",ct:{fa:"General",t:"g"},m:"Smith",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:35,c:4,v:{v:50,ct:{fa:"General",t:"n"},m:"50",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:35,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:35,c:6,v:{v:"Logical XOR (Fax Or Jones but not both)",ct:{fa:"General",t:"g"},m:"Logical XOR (Fax Or Jones but not both)",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{rs:1,cs:3,r:35,c:6}}},{r:35,c:7,v:{mc:{r:35,c:6}}},{r:35,c:8,v:{mc:{r:35,c:6}}},{r:35,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:35,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:36,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:36,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:36,c:2,v:{v:"Fax",ct:{fa:"General",t:"g"},m:"Fax",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:36,c:3,v:{v:"Brown",ct:{fa:"General",t:"g"},m:"Brown",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:36,c:4,v:{v:60,ct:{fa:"General",t:"n"},m:"60",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:36,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:36,c:6,v:{v:1,ct:{fa:"General",t:"n"},m:"1",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:'=SUM(IF(MOD((C31:C39="Fax")+(D31:D39="Jones"),2),1,0))'}},{r:36,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:36,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:36,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:36,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:37,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:37,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:37,c:2,v:{v:"Phone",ct:{fa:"General",t:"g"},m:"Phone",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:37,c:3,v:{v:"Davis",ct:{fa:"General",t:"g"},m:"Davis",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:37,c:4,v:{v:70,ct:{fa:"General",t:"n"},m:"70",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:37,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:37,c:6,v:{v:"Logical NAND (All Sales Except Fax And Jones)",ct:{fa:"General",t:"g"},m:"Logical NAND (All Sales Except Fax And Jones)",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,mc:{rs:1,cs:3,r:37,c:6}}},{r:37,c:7,v:{mc:{r:37,c:6}}},{r:37,c:8,v:{mc:{r:37,c:6}}},{r:37,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:37,c:10,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:38,c:0,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:38,c:1,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:38,c:2,v:{v:"PC",ct:{fa:"General",t:"g"},m:"PC",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:38,c:3,v:{v:"Jones",ct:{fa:"General",t:"g"},m:"Jones",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:38,c:4,v:{v:80,ct:{fa:"General",t:"n"},m:"80",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:38,c:5,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:38,c:6,v:{v:1,ct:{fa:"General",t:"n"},m:"1",bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0,f:'=SUM(IF((C31:C39="Fax")+(D31:D39="Jones")<>2,1,0))'}},{r:38,c:7,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:38,c:8,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:38,c:9,v:{bg:null,bl:0,it:0,ff:9,fs:10,fc:"rgb(0, 0, 0)",ht:1,vt:0}},{r:38,c:10,v:{}}],calcChain:[{r:6,c:3,index:1,color:"w",parent:null,chidren:{},times:0},{r:7,c:3,index:1,color:"w",parent:null,chidren:{},times:0},{r:8,c:3,index:1,color:"w",parent:null,chidren:{},times:0},{r:5,c:9,index:1,color:"w",parent:null,chidren:{},times:0},{r:6,c:9,index:1,color:"w",parent:null,chidren:{},times:0},{r:7,c:9,index:1,color:"w",parent:null,chidren:{},times:0},{r:8,c:9,index:1,color:"w",parent:null,chidren:{},times:0},{r:16,c:7,index:1,color:"w",parent:null,chidren:{},times:0},{r:17,c:7,index:1,color:"w",parent:null,chidren:{},times:0},{r:22,c:8,index:1,color:"w",parent:null,chidren:{},times:0},{r:38,c:6,index:1,color:"w",parent:null,chidren:{},times:0},{r:30,c:6,index:1,color:"w",parent:null,chidren:{},times:0},{r:32,c:6,index:1,color:"w",parent:null,chidren:{},times:0},{r:34,c:6,index:1,color:"w",parent:null,chidren:{},times:0},{r:36,c:6,index:1,color:"w",parent:null,chidren:{},times:0},{r:9,c:9,index:1,color:"w",parent:null,chidren:{},times:0},{r:10,c:9,index:1,color:"w",parent:null,chidren:{},times:0},{r:14,c:7,index:1,color:"w",parent:null,chidren:{},times:0},{r:15,c:7,index:1,color:"w",parent:null,chidren:{},times:0},{r:23,c:8,index:1,color:"w",parent:null,chidren:{},times:0}],ch_width:1723,rh_height:1010,luckysheet_select_save:[{left:532,width:123,top:780,height:19,left_move:532,width_move:123,top_move:780,height_move:19,row:[39,39],column:[6,6],row_focus:39,column_focus:6}],luckysheet_selection_range:[],scrollLeft:0,scrollTop:0,frozen:{type:"row"}};