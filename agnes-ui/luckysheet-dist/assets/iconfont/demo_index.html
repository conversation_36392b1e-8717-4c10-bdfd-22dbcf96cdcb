<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>IconFont Demo</title>
  <link rel="shortcut icon" href="https://img.alicdn.com/tps/i4/TB1_oz6GVXXXXaFXpXXJDFnIXXX-64-64.ico" type="image/x-icon"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">&#xe86b;</a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=1990368" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f8;</span>
                <div class="name">链接</div>
                <div class="code-name">&amp;#xe7f8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f5;</span>
                <div class="name">打印区域</div>
                <div class="code-name">&amp;#xe7f5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f6;</span>
                <div class="name">打印页面配置</div>
                <div class="code-name">&amp;#xe7f6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f7;</span>
                <div class="name">打印标题</div>
                <div class="code-name">&amp;#xe7f7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f2;</span>
                <div class="name">分页预览</div>
                <div class="code-name">&amp;#xe7f2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f3;</span>
                <div class="name">普通</div>
                <div class="code-name">&amp;#xe7f3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f4;</span>
                <div class="name">页面布局</div>
                <div class="code-name">&amp;#xe7f4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ee;</span>
                <div class="name">表格锁定</div>
                <div class="code-name">&amp;#xe7ee;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f1;</span>
                <div class="name">转到</div>
                <div class="code-name">&amp;#xe7f1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ed;</span>
                <div class="name">右箭头</div>
                <div class="code-name">&amp;#xe7ed;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ef;</span>
                <div class="name">菜单</div>
                <div class="code-name">&amp;#xe7ef;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7f0;</span>
                <div class="name">替换</div>
                <div class="code-name">&amp;#xe7f0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e1;</span>
                <div class="name">冻结</div>
                <div class="code-name">&amp;#xe7e1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e2;</span>
                <div class="name">剪</div>
                <div class="code-name">&amp;#xe7e2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e3;</span>
                <div class="name">加</div>
                <div class="code-name">&amp;#xe7e3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e4;</span>
                <div class="name">溢出</div>
                <div class="code-name">&amp;#xe7e4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e5;</span>
                <div class="name">升序</div>
                <div class="code-name">&amp;#xe7e5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e6;</span>
                <div class="name">内框线</div>
                <div class="code-name">&amp;#xe7e6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e7;</span>
                <div class="name">清除筛选</div>
                <div class="code-name">&amp;#xe7e7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e8;</span>
                <div class="name">文本向上</div>
                <div class="code-name">&amp;#xe7e8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e9;</span>
                <div class="name">降序</div>
                <div class="code-name">&amp;#xe7e9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ea;</span>
                <div class="name">内框横线</div>
                <div class="code-name">&amp;#xe7ea;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7eb;</span>
                <div class="name">内框竖线</div>
                <div class="code-name">&amp;#xe7eb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ec;</span>
                <div class="name">自定义排序</div>
                <div class="code-name">&amp;#xe7ec;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7df;</span>
                <div class="name">logo2</div>
                <div class="code-name">&amp;#xe7df;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7e0;</span>
                <div class="name">logo</div>
                <div class="code-name">&amp;#xe7e0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7de;</span>
                <div class="name">文本倾斜</div>
                <div class="code-name">&amp;#xe7de;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d9;</span>
                <div class="name">加粗</div>
                <div class="code-name">&amp;#xe7d9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78a;</span>
                <div class="name">搜索</div>
                <div class="code-name">&amp;#xe78a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78b;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe78b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78c;</span>
                <div class="name">下一个</div>
                <div class="code-name">&amp;#xe78c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78d;</span>
                <div class="name">下拉</div>
                <div class="code-name">&amp;#xe78d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78e;</span>
                <div class="name">文本颜色</div>
                <div class="code-name">&amp;#xe78e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe78f;</span>
                <div class="name">上一个</div>
                <div class="code-name">&amp;#xe78f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe790;</span>
                <div class="name">数据透视</div>
                <div class="code-name">&amp;#xe790;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe791;</span>
                <div class="name">填充</div>
                <div class="code-name">&amp;#xe791;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe792;</span>
                <div class="name">增加小数位</div>
                <div class="code-name">&amp;#xe792;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe793;</span>
                <div class="name">编辑2</div>
                <div class="code-name">&amp;#xe793;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe794;</span>
                <div class="name">截屏</div>
                <div class="code-name">&amp;#xe794;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe796;</span>
                <div class="name">减小小数位</div>
                <div class="code-name">&amp;#xe796;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe797;</span>
                <div class="name">菜单</div>
                <div class="code-name">&amp;#xe797;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe798;</span>
                <div class="name">数据库</div>
                <div class="code-name">&amp;#xe798;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe799;</span>
                <div class="name">无边框</div>
                <div class="code-name">&amp;#xe799;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79a;</span>
                <div class="name">编辑</div>
                <div class="code-name">&amp;#xe79a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79b;</span>
                <div class="name">清除样式</div>
                <div class="code-name">&amp;#xe79b;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79c;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe79c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79d;</span>
                <div class="name">文本居中对齐</div>
                <div class="code-name">&amp;#xe79d;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79e;</span>
                <div class="name">打印</div>
                <div class="code-name">&amp;#xe79e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe79f;</span>
                <div class="name">文本分割</div>
                <div class="code-name">&amp;#xe79f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a0;</span>
                <div class="name">函数‘</div>
                <div class="code-name">&amp;#xe7a0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a1;</span>
                <div class="name">降序</div>
                <div class="code-name">&amp;#xe7a1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a2;</span>
                <div class="name">顶部对齐</div>
                <div class="code-name">&amp;#xe7a2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a3;</span>
                <div class="name">图片</div>
                <div class="code-name">&amp;#xe7a3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a4;</span>
                <div class="name">向下90</div>
                <div class="code-name">&amp;#xe7a4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a5;</span>
                <div class="name">竖排文字</div>
                <div class="code-name">&amp;#xe7a5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a6;</span>
                <div class="name">全加边框</div>
                <div class="code-name">&amp;#xe7a6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a7;</span>
                <div class="name">升序</div>
                <div class="code-name">&amp;#xe7a7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a8;</span>
                <div class="name">裁剪</div>
                <div class="code-name">&amp;#xe7a8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7a9;</span>
                <div class="name">金额</div>
                <div class="code-name">&amp;#xe7a9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7aa;</span>
                <div class="name">菜单1</div>
                <div class="code-name">&amp;#xe7aa;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ab;</span>
                <div class="name">取消合并</div>
                <div class="code-name">&amp;#xe7ab;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ac;</span>
                <div class="name">文本下划线</div>
                <div class="code-name">&amp;#xe7ac;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ad;</span>
                <div class="name">上边框</div>
                <div class="code-name">&amp;#xe7ad;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ae;</span>
                <div class="name">定位</div>
                <div class="code-name">&amp;#xe7ae;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7af;</span>
                <div class="name">四周加边框</div>
                <div class="code-name">&amp;#xe7af;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b0;</span>
                <div class="name">侧边栏收起</div>
                <div class="code-name">&amp;#xe7b0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b1;</span>
                <div class="name">合并</div>
                <div class="code-name">&amp;#xe7b1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b2;</span>
                <div class="name">向上倾斜</div>
                <div class="code-name">&amp;#xe7b2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b3;</span>
                <div class="name">水平对齐</div>
                <div class="code-name">&amp;#xe7b3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b4;</span>
                <div class="name">文本删除线</div>
                <div class="code-name">&amp;#xe7b4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b5;</span>
                <div class="name">文本右对齐</div>
                <div class="code-name">&amp;#xe7b5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b6;</span>
                <div class="name">前进</div>
                <div class="code-name">&amp;#xe7b6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b7;</span>
                <div class="name">图表</div>
                <div class="code-name">&amp;#xe7b7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b8;</span>
                <div class="name">右边框</div>
                <div class="code-name">&amp;#xe7b8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7b9;</span>
                <div class="name">百分号</div>
                <div class="code-name">&amp;#xe7b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ba;</span>
                <div class="name">格式刷</div>
                <div class="code-name">&amp;#xe7ba;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bb;</span>
                <div class="name">保存</div>
                <div class="code-name">&amp;#xe7bb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bc;</span>
                <div class="name">数据验证</div>
                <div class="code-name">&amp;#xe7bc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bd;</span>
                <div class="name">截断</div>
                <div class="code-name">&amp;#xe7bd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7be;</span>
                <div class="name">格式条件</div>
                <div class="code-name">&amp;#xe7be;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7bf;</span>
                <div class="name">自动换行</div>
                <div class="code-name">&amp;#xe7bf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c0;</span>
                <div class="name">侧边栏展开</div>
                <div class="code-name">&amp;#xe7c0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c1;</span>
                <div class="name">筛选2</div>
                <div class="code-name">&amp;#xe7c1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c2;</span>
                <div class="name">向下倾斜</div>
                <div class="code-name">&amp;#xe7c2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c3;</span>
                <div class="name">溢出</div>
                <div class="code-name">&amp;#xe7c3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c4;</span>
                <div class="name">垂直合并</div>
                <div class="code-name">&amp;#xe7c4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c5;</span>
                <div class="name">文本分散对齐</div>
                <div class="code-name">&amp;#xe7c5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c6;</span>
                <div class="name">左边框</div>
                <div class="code-name">&amp;#xe7c6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c7;</span>
                <div class="name">分页查看</div>
                <div class="code-name">&amp;#xe7c7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c8;</span>
                <div class="name">运行</div>
                <div class="code-name">&amp;#xe7c8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7c9;</span>
                <div class="name">列</div>
                <div class="code-name">&amp;#xe7c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ca;</span>
                <div class="name">全屏</div>
                <div class="code-name">&amp;#xe7ca;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cb;</span>
                <div class="name">筛选</div>
                <div class="code-name">&amp;#xe7cb;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cc;</span>
                <div class="name">更新</div>
                <div class="code-name">&amp;#xe7cc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cd;</span>
                <div class="name">清除</div>
                <div class="code-name">&amp;#xe7cd;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7ce;</span>
                <div class="name">行</div>
                <div class="code-name">&amp;#xe7ce;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7cf;</span>
                <div class="name">注释</div>
                <div class="code-name">&amp;#xe7cf;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d0;</span>
                <div class="name">剪</div>
                <div class="code-name">&amp;#xe7d0;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d1;</span>
                <div class="name">计算</div>
                <div class="code-name">&amp;#xe7d1;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d2;</span>
                <div class="name">加</div>
                <div class="code-name">&amp;#xe7d2;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d3;</span>
                <div class="name">底部对齐</div>
                <div class="code-name">&amp;#xe7d3;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d4;</span>
                <div class="name">向上90</div>
                <div class="code-name">&amp;#xe7d4;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d5;</span>
                <div class="name">无选装</div>
                <div class="code-name">&amp;#xe7d5;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d6;</span>
                <div class="name">显示隐藏网格</div>
                <div class="code-name">&amp;#xe7d6;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d7;</span>
                <div class="name">冻结</div>
                <div class="code-name">&amp;#xe7d7;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7d8;</span>
                <div class="name">文本左对齐</div>
                <div class="code-name">&amp;#xe7d8;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7da;</span>
                <div class="name">后退</div>
                <div class="code-name">&amp;#xe7da;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7db;</span>
                <div class="name">水平合并</div>
                <div class="code-name">&amp;#xe7db;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7dc;</span>
                <div class="name">下边框</div>
                <div class="code-name">&amp;#xe7dc;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe7dd;</span>
                <div class="name">设置</div>
                <div class="code-name">&amp;#xe7dd;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>兼容性最好，支持 IE6+，及所有现代浏览器。</li>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>但是因为是字体，所以不支持多色。只能使用平台里单色的图标，就算项目里有多色图标也会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持多色图标，这些多色图标在 Unicode 模式下将不能使用，如果有需求建议使用symbol 的引用方式</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.eot');
  src: url('iconfont.eot?#iefix') format('embedded-opentype'),
      url('iconfont.woff2') format('woff2'),
      url('iconfont.woff') format('woff'),
      url('iconfont.ttf') format('truetype'),
      url('iconfont.svg#iconfont') format('svg');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-lianjie"></span>
            <div class="name">
              链接
            </div>
            <div class="code-name">.luckysheet-iconfont-lianjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-dayinquyu"></span>
            <div class="name">
              打印区域
            </div>
            <div class="code-name">.luckysheet-iconfont-dayinquyu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-dayinyemianpeizhi"></span>
            <div class="name">
              打印页面配置
            </div>
            <div class="code-name">.luckysheet-iconfont-dayinyemianpeizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-dayinbiaoti"></span>
            <div class="name">
              打印标题
            </div>
            <div class="code-name">.luckysheet-iconfont-dayinbiaoti
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-fenyeyulan"></span>
            <div class="name">
              分页预览
            </div>
            <div class="code-name">.luckysheet-iconfont-fenyeyulan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-putong"></span>
            <div class="name">
              普通
            </div>
            <div class="code-name">.luckysheet-iconfont-putong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-yemianbuju"></span>
            <div class="name">
              页面布局
            </div>
            <div class="code-name">.luckysheet-iconfont-yemianbuju
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-biaogesuoding"></span>
            <div class="name">
              表格锁定
            </div>
            <div class="code-name">.luckysheet-iconfont-biaogesuoding
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-zhuandao1"></span>
            <div class="name">
              转到
            </div>
            <div class="code-name">.luckysheet-iconfont-zhuandao1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-youjiantou"></span>
            <div class="name">
              右箭头
            </div>
            <div class="code-name">.luckysheet-iconfont-youjiantou
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-caidan2"></span>
            <div class="name">
              菜单
            </div>
            <div class="code-name">.luckysheet-iconfont-caidan2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-tihuan"></span>
            <div class="name">
              替换
            </div>
            <div class="code-name">.luckysheet-iconfont-tihuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-dongjie1"></span>
            <div class="name">
              冻结
            </div>
            <div class="code-name">.luckysheet-iconfont-dongjie1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jian1"></span>
            <div class="name">
              剪
            </div>
            <div class="code-name">.luckysheet-iconfont-jian1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jia1"></span>
            <div class="name">
              加
            </div>
            <div class="code-name">.luckysheet-iconfont-jia1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-yichu1"></span>
            <div class="name">
              溢出
            </div>
            <div class="code-name">.luckysheet-iconfont-yichu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shengxu1"></span>
            <div class="name">
              升序
            </div>
            <div class="code-name">.luckysheet-iconfont-shengxu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-neikuangxian"></span>
            <div class="name">
              内框线
            </div>
            <div class="code-name">.luckysheet-iconfont-neikuangxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-qingchushaixuan"></span>
            <div class="name">
              清除筛选
            </div>
            <div class="code-name">.luckysheet-iconfont-qingchushaixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wenbenxiangshang"></span>
            <div class="name">
              文本向上
            </div>
            <div class="code-name">.luckysheet-iconfont-wenbenxiangshang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jiangxu1"></span>
            <div class="name">
              降序
            </div>
            <div class="code-name">.luckysheet-iconfont-jiangxu1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-neikuanghengxian"></span>
            <div class="name">
              内框横线
            </div>
            <div class="code-name">.luckysheet-iconfont-neikuanghengxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-neikuangshuxian"></span>
            <div class="name">
              内框竖线
            </div>
            <div class="code-name">.luckysheet-iconfont-neikuangshuxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-zidingyipaixu"></span>
            <div class="name">
              自定义排序
            </div>
            <div class="code-name">.luckysheet-iconfont-zidingyipaixu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-logo2"></span>
            <div class="name">
              logo2
            </div>
            <div class="code-name">.luckysheet-iconfont-logo2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-logo"></span>
            <div class="name">
              logo
            </div>
            <div class="code-name">.luckysheet-iconfont-logo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wenbenqingxie1"></span>
            <div class="name">
              文本倾斜
            </div>
            <div class="code-name">.luckysheet-iconfont-wenbenqingxie1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jiacu"></span>
            <div class="name">
              加粗
            </div>
            <div class="code-name">.luckysheet-iconfont-jiacu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-sousuo"></span>
            <div class="name">
              搜索
            </div>
            <div class="code-name">.luckysheet-iconfont-sousuo
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-guanbi"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.luckysheet-iconfont-guanbi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-xiayige"></span>
            <div class="name">
              下一个
            </div>
            <div class="code-name">.luckysheet-iconfont-xiayige
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-xiala"></span>
            <div class="name">
              下拉
            </div>
            <div class="code-name">.luckysheet-iconfont-xiala
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wenbenyanse"></span>
            <div class="name">
              文本颜色
            </div>
            <div class="code-name">.luckysheet-iconfont-wenbenyanse
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shangyige"></span>
            <div class="name">
              上一个
            </div>
            <div class="code-name">.luckysheet-iconfont-shangyige
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shujutoushi"></span>
            <div class="name">
              数据透视
            </div>
            <div class="code-name">.luckysheet-iconfont-shujutoushi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-tianchong"></span>
            <div class="name">
              填充
            </div>
            <div class="code-name">.luckysheet-iconfont-tianchong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-zengjiaxiaoshuwei"></span>
            <div class="name">
              增加小数位
            </div>
            <div class="code-name">.luckysheet-iconfont-zengjiaxiaoshuwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-bianji2"></span>
            <div class="name">
              编辑2
            </div>
            <div class="code-name">.luckysheet-iconfont-bianji2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jieping"></span>
            <div class="name">
              截屏
            </div>
            <div class="code-name">.luckysheet-iconfont-jieping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jianxiaoxiaoshuwei"></span>
            <div class="name">
              减小小数位
            </div>
            <div class="code-name">.luckysheet-iconfont-jianxiaoxiaoshuwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-caidan"></span>
            <div class="name">
              菜单
            </div>
            <div class="code-name">.luckysheet-iconfont-caidan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shujuku"></span>
            <div class="name">
              数据库
            </div>
            <div class="code-name">.luckysheet-iconfont-shujuku
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wubiankuang"></span>
            <div class="name">
              无边框
            </div>
            <div class="code-name">.luckysheet-iconfont-wubiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-bianji"></span>
            <div class="name">
              编辑
            </div>
            <div class="code-name">.luckysheet-iconfont-bianji
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-qingchuyangshi"></span>
            <div class="name">
              清除样式
            </div>
            <div class="code-name">.luckysheet-iconfont-qingchuyangshi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shanchu"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.luckysheet-iconfont-shanchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wenbenjuzhongduiqi"></span>
            <div class="name">
              文本居中对齐
            </div>
            <div class="code-name">.luckysheet-iconfont-wenbenjuzhongduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-dayin"></span>
            <div class="name">
              打印
            </div>
            <div class="code-name">.luckysheet-iconfont-dayin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wenbenfenge"></span>
            <div class="name">
              文本分割
            </div>
            <div class="code-name">.luckysheet-iconfont-wenbenfenge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-hanshu"></span>
            <div class="name">
              函数‘
            </div>
            <div class="code-name">.luckysheet-iconfont-hanshu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jiangxu"></span>
            <div class="name">
              降序
            </div>
            <div class="code-name">.luckysheet-iconfont-jiangxu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-dingbuduiqi"></span>
            <div class="name">
              顶部对齐
            </div>
            <div class="code-name">.luckysheet-iconfont-dingbuduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-tupian"></span>
            <div class="name">
              图片
            </div>
            <div class="code-name">.luckysheet-iconfont-tupian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-xiangxia90"></span>
            <div class="name">
              向下90
            </div>
            <div class="code-name">.luckysheet-iconfont-xiangxia90
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shupaiwenzi"></span>
            <div class="name">
              竖排文字
            </div>
            <div class="code-name">.luckysheet-iconfont-shupaiwenzi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-quanjiabiankuang"></span>
            <div class="name">
              全加边框
            </div>
            <div class="code-name">.luckysheet-iconfont-quanjiabiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shengxu"></span>
            <div class="name">
              升序
            </div>
            <div class="code-name">.luckysheet-iconfont-shengxu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-caijian"></span>
            <div class="name">
              裁剪
            </div>
            <div class="code-name">.luckysheet-iconfont-caijian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jine"></span>
            <div class="name">
              金额
            </div>
            <div class="code-name">.luckysheet-iconfont-jine
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-caidan1"></span>
            <div class="name">
              菜单1
            </div>
            <div class="code-name">.luckysheet-iconfont-caidan1
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-quxiaohebing"></span>
            <div class="name">
              取消合并
            </div>
            <div class="code-name">.luckysheet-iconfont-quxiaohebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wenbenxiahuaxian"></span>
            <div class="name">
              文本下划线
            </div>
            <div class="code-name">.luckysheet-iconfont-wenbenxiahuaxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shangbiankuang"></span>
            <div class="name">
              上边框
            </div>
            <div class="code-name">.luckysheet-iconfont-shangbiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-dingwei"></span>
            <div class="name">
              定位
            </div>
            <div class="code-name">.luckysheet-iconfont-dingwei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-sizhoujiabiankuang"></span>
            <div class="name">
              四周加边框
            </div>
            <div class="code-name">.luckysheet-iconfont-sizhoujiabiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-cebianlanshouqi"></span>
            <div class="name">
              侧边栏收起
            </div>
            <div class="code-name">.luckysheet-iconfont-cebianlanshouqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-hebing"></span>
            <div class="name">
              合并
            </div>
            <div class="code-name">.luckysheet-iconfont-hebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-xiangshangqingxie"></span>
            <div class="name">
              向上倾斜
            </div>
            <div class="code-name">.luckysheet-iconfont-xiangshangqingxie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shuipingduiqi"></span>
            <div class="name">
              水平对齐
            </div>
            <div class="code-name">.luckysheet-iconfont-shuipingduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wenbenshanchuxian"></span>
            <div class="name">
              文本删除线
            </div>
            <div class="code-name">.luckysheet-iconfont-wenbenshanchuxian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wenbenyouduiqi"></span>
            <div class="name">
              文本右对齐
            </div>
            <div class="code-name">.luckysheet-iconfont-wenbenyouduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-qianjin"></span>
            <div class="name">
              前进
            </div>
            <div class="code-name">.luckysheet-iconfont-qianjin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-tubiao"></span>
            <div class="name">
              图表
            </div>
            <div class="code-name">.luckysheet-iconfont-tubiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-youbiankuang"></span>
            <div class="name">
              右边框
            </div>
            <div class="code-name">.luckysheet-iconfont-youbiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-baifenhao"></span>
            <div class="name">
              百分号
            </div>
            <div class="code-name">.luckysheet-iconfont-baifenhao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-geshishua"></span>
            <div class="name">
              格式刷
            </div>
            <div class="code-name">.luckysheet-iconfont-geshishua
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-baocun"></span>
            <div class="name">
              保存
            </div>
            <div class="code-name">.luckysheet-iconfont-baocun
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shujuyanzheng"></span>
            <div class="name">
              数据验证
            </div>
            <div class="code-name">.luckysheet-iconfont-shujuyanzheng
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jieduan"></span>
            <div class="name">
              截断
            </div>
            <div class="code-name">.luckysheet-iconfont-jieduan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-geshitiaojian"></span>
            <div class="name">
              格式条件
            </div>
            <div class="code-name">.luckysheet-iconfont-geshitiaojian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-zidonghuanhang"></span>
            <div class="name">
              自动换行
            </div>
            <div class="code-name">.luckysheet-iconfont-zidonghuanhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-cebianlanzhankai"></span>
            <div class="name">
              侧边栏展开
            </div>
            <div class="code-name">.luckysheet-iconfont-cebianlanzhankai
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shaixuan2"></span>
            <div class="name">
              筛选2
            </div>
            <div class="code-name">.luckysheet-iconfont-shaixuan2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-xiangxiaqingxie"></span>
            <div class="name">
              向下倾斜
            </div>
            <div class="code-name">.luckysheet-iconfont-xiangxiaqingxie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-yichu"></span>
            <div class="name">
              溢出
            </div>
            <div class="code-name">.luckysheet-iconfont-yichu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-chuizhihebing"></span>
            <div class="name">
              垂直合并
            </div>
            <div class="code-name">.luckysheet-iconfont-chuizhihebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wenbenfensanduiqi"></span>
            <div class="name">
              文本分散对齐
            </div>
            <div class="code-name">.luckysheet-iconfont-wenbenfensanduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-zuobiankuang"></span>
            <div class="name">
              左边框
            </div>
            <div class="code-name">.luckysheet-iconfont-zuobiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-fenyechakan"></span>
            <div class="name">
              分页查看
            </div>
            <div class="code-name">.luckysheet-iconfont-fenyechakan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-yunhang"></span>
            <div class="name">
              运行
            </div>
            <div class="code-name">.luckysheet-iconfont-yunhang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-lie"></span>
            <div class="name">
              列
            </div>
            <div class="code-name">.luckysheet-iconfont-lie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-quanping"></span>
            <div class="name">
              全屏
            </div>
            <div class="code-name">.luckysheet-iconfont-quanping
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shaixuan"></span>
            <div class="name">
              筛选
            </div>
            <div class="code-name">.luckysheet-iconfont-shaixuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-gengxin"></span>
            <div class="name">
              更新
            </div>
            <div class="code-name">.luckysheet-iconfont-gengxin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-qingchu"></span>
            <div class="name">
              清除
            </div>
            <div class="code-name">.luckysheet-iconfont-qingchu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-hang"></span>
            <div class="name">
              行
            </div>
            <div class="code-name">.luckysheet-iconfont-hang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-zhushi"></span>
            <div class="name">
              注释
            </div>
            <div class="code-name">.luckysheet-iconfont-zhushi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jian"></span>
            <div class="name">
              剪
            </div>
            <div class="code-name">.luckysheet-iconfont-jian
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jisuan"></span>
            <div class="name">
              计算
            </div>
            <div class="code-name">.luckysheet-iconfont-jisuan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-jia"></span>
            <div class="name">
              加
            </div>
            <div class="code-name">.luckysheet-iconfont-jia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-dibuduiqi"></span>
            <div class="name">
              底部对齐
            </div>
            <div class="code-name">.luckysheet-iconfont-dibuduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-xiangshang90"></span>
            <div class="name">
              向上90
            </div>
            <div class="code-name">.luckysheet-iconfont-xiangshang90
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wuxuanzhuang"></span>
            <div class="name">
              无选装
            </div>
            <div class="code-name">.luckysheet-iconfont-wuxuanzhuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-xianshiyincangwangge"></span>
            <div class="name">
              显示隐藏网格
            </div>
            <div class="code-name">.luckysheet-iconfont-xianshiyincangwangge
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-dongjie"></span>
            <div class="name">
              冻结
            </div>
            <div class="code-name">.luckysheet-iconfont-dongjie
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-wenbenzuoduiqi"></span>
            <div class="name">
              文本左对齐
            </div>
            <div class="code-name">.luckysheet-iconfont-wenbenzuoduiqi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-houtui"></span>
            <div class="name">
              后退
            </div>
            <div class="code-name">.luckysheet-iconfont-houtui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shuipinghebing"></span>
            <div class="name">
              水平合并
            </div>
            <div class="code-name">.luckysheet-iconfont-shuipinghebing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-xiabiankuang"></span>
            <div class="name">
              下边框
            </div>
            <div class="code-name">.luckysheet-iconfont-xiabiankuang
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont luckysheet-iconfont-shezhi"></span>
            <div class="name">
              设置
            </div>
            <div class="code-name">.luckysheet-iconfont-shezhi
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>兼容性良好，支持 IE8+，及所有现代浏览器。</li>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
          <li>不过因为本质上还是使用的字体，所以多色图标还是不支持的。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont luckysheet-iconfont-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-lianjie"></use>
                </svg>
                <div class="name">链接</div>
                <div class="code-name">#luckysheet-iconfont-lianjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-dayinquyu"></use>
                </svg>
                <div class="name">打印区域</div>
                <div class="code-name">#luckysheet-iconfont-dayinquyu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-dayinyemianpeizhi"></use>
                </svg>
                <div class="name">打印页面配置</div>
                <div class="code-name">#luckysheet-iconfont-dayinyemianpeizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-dayinbiaoti"></use>
                </svg>
                <div class="name">打印标题</div>
                <div class="code-name">#luckysheet-iconfont-dayinbiaoti</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-fenyeyulan"></use>
                </svg>
                <div class="name">分页预览</div>
                <div class="code-name">#luckysheet-iconfont-fenyeyulan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-putong"></use>
                </svg>
                <div class="name">普通</div>
                <div class="code-name">#luckysheet-iconfont-putong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-yemianbuju"></use>
                </svg>
                <div class="name">页面布局</div>
                <div class="code-name">#luckysheet-iconfont-yemianbuju</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-biaogesuoding"></use>
                </svg>
                <div class="name">表格锁定</div>
                <div class="code-name">#luckysheet-iconfont-biaogesuoding</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-zhuandao1"></use>
                </svg>
                <div class="name">转到</div>
                <div class="code-name">#luckysheet-iconfont-zhuandao1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-youjiantou"></use>
                </svg>
                <div class="name">右箭头</div>
                <div class="code-name">#luckysheet-iconfont-youjiantou</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-caidan2"></use>
                </svg>
                <div class="name">菜单</div>
                <div class="code-name">#luckysheet-iconfont-caidan2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-tihuan"></use>
                </svg>
                <div class="name">替换</div>
                <div class="code-name">#luckysheet-iconfont-tihuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-dongjie1"></use>
                </svg>
                <div class="name">冻结</div>
                <div class="code-name">#luckysheet-iconfont-dongjie1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jian1"></use>
                </svg>
                <div class="name">剪</div>
                <div class="code-name">#luckysheet-iconfont-jian1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jia1"></use>
                </svg>
                <div class="name">加</div>
                <div class="code-name">#luckysheet-iconfont-jia1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-yichu1"></use>
                </svg>
                <div class="name">溢出</div>
                <div class="code-name">#luckysheet-iconfont-yichu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shengxu1"></use>
                </svg>
                <div class="name">升序</div>
                <div class="code-name">#luckysheet-iconfont-shengxu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-neikuangxian"></use>
                </svg>
                <div class="name">内框线</div>
                <div class="code-name">#luckysheet-iconfont-neikuangxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-qingchushaixuan"></use>
                </svg>
                <div class="name">清除筛选</div>
                <div class="code-name">#luckysheet-iconfont-qingchushaixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wenbenxiangshang"></use>
                </svg>
                <div class="name">文本向上</div>
                <div class="code-name">#luckysheet-iconfont-wenbenxiangshang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jiangxu1"></use>
                </svg>
                <div class="name">降序</div>
                <div class="code-name">#luckysheet-iconfont-jiangxu1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-neikuanghengxian"></use>
                </svg>
                <div class="name">内框横线</div>
                <div class="code-name">#luckysheet-iconfont-neikuanghengxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-neikuangshuxian"></use>
                </svg>
                <div class="name">内框竖线</div>
                <div class="code-name">#luckysheet-iconfont-neikuangshuxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-zidingyipaixu"></use>
                </svg>
                <div class="name">自定义排序</div>
                <div class="code-name">#luckysheet-iconfont-zidingyipaixu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-logo2"></use>
                </svg>
                <div class="name">logo2</div>
                <div class="code-name">#luckysheet-iconfont-logo2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-logo"></use>
                </svg>
                <div class="name">logo</div>
                <div class="code-name">#luckysheet-iconfont-logo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wenbenqingxie1"></use>
                </svg>
                <div class="name">文本倾斜</div>
                <div class="code-name">#luckysheet-iconfont-wenbenqingxie1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jiacu"></use>
                </svg>
                <div class="name">加粗</div>
                <div class="code-name">#luckysheet-iconfont-jiacu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-sousuo"></use>
                </svg>
                <div class="name">搜索</div>
                <div class="code-name">#luckysheet-iconfont-sousuo</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-guanbi"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#luckysheet-iconfont-guanbi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-xiayige"></use>
                </svg>
                <div class="name">下一个</div>
                <div class="code-name">#luckysheet-iconfont-xiayige</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-xiala"></use>
                </svg>
                <div class="name">下拉</div>
                <div class="code-name">#luckysheet-iconfont-xiala</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wenbenyanse"></use>
                </svg>
                <div class="name">文本颜色</div>
                <div class="code-name">#luckysheet-iconfont-wenbenyanse</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shangyige"></use>
                </svg>
                <div class="name">上一个</div>
                <div class="code-name">#luckysheet-iconfont-shangyige</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shujutoushi"></use>
                </svg>
                <div class="name">数据透视</div>
                <div class="code-name">#luckysheet-iconfont-shujutoushi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-tianchong"></use>
                </svg>
                <div class="name">填充</div>
                <div class="code-name">#luckysheet-iconfont-tianchong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-zengjiaxiaoshuwei"></use>
                </svg>
                <div class="name">增加小数位</div>
                <div class="code-name">#luckysheet-iconfont-zengjiaxiaoshuwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-bianji2"></use>
                </svg>
                <div class="name">编辑2</div>
                <div class="code-name">#luckysheet-iconfont-bianji2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jieping"></use>
                </svg>
                <div class="name">截屏</div>
                <div class="code-name">#luckysheet-iconfont-jieping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jianxiaoxiaoshuwei"></use>
                </svg>
                <div class="name">减小小数位</div>
                <div class="code-name">#luckysheet-iconfont-jianxiaoxiaoshuwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-caidan"></use>
                </svg>
                <div class="name">菜单</div>
                <div class="code-name">#luckysheet-iconfont-caidan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shujuku"></use>
                </svg>
                <div class="name">数据库</div>
                <div class="code-name">#luckysheet-iconfont-shujuku</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wubiankuang"></use>
                </svg>
                <div class="name">无边框</div>
                <div class="code-name">#luckysheet-iconfont-wubiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-bianji"></use>
                </svg>
                <div class="name">编辑</div>
                <div class="code-name">#luckysheet-iconfont-bianji</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-qingchuyangshi"></use>
                </svg>
                <div class="name">清除样式</div>
                <div class="code-name">#luckysheet-iconfont-qingchuyangshi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shanchu"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#luckysheet-iconfont-shanchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wenbenjuzhongduiqi"></use>
                </svg>
                <div class="name">文本居中对齐</div>
                <div class="code-name">#luckysheet-iconfont-wenbenjuzhongduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-dayin"></use>
                </svg>
                <div class="name">打印</div>
                <div class="code-name">#luckysheet-iconfont-dayin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wenbenfenge"></use>
                </svg>
                <div class="name">文本分割</div>
                <div class="code-name">#luckysheet-iconfont-wenbenfenge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-hanshu"></use>
                </svg>
                <div class="name">函数‘</div>
                <div class="code-name">#luckysheet-iconfont-hanshu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jiangxu"></use>
                </svg>
                <div class="name">降序</div>
                <div class="code-name">#luckysheet-iconfont-jiangxu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-dingbuduiqi"></use>
                </svg>
                <div class="name">顶部对齐</div>
                <div class="code-name">#luckysheet-iconfont-dingbuduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-tupian"></use>
                </svg>
                <div class="name">图片</div>
                <div class="code-name">#luckysheet-iconfont-tupian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-xiangxia90"></use>
                </svg>
                <div class="name">向下90</div>
                <div class="code-name">#luckysheet-iconfont-xiangxia90</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shupaiwenzi"></use>
                </svg>
                <div class="name">竖排文字</div>
                <div class="code-name">#luckysheet-iconfont-shupaiwenzi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-quanjiabiankuang"></use>
                </svg>
                <div class="name">全加边框</div>
                <div class="code-name">#luckysheet-iconfont-quanjiabiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shengxu"></use>
                </svg>
                <div class="name">升序</div>
                <div class="code-name">#luckysheet-iconfont-shengxu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-caijian"></use>
                </svg>
                <div class="name">裁剪</div>
                <div class="code-name">#luckysheet-iconfont-caijian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jine"></use>
                </svg>
                <div class="name">金额</div>
                <div class="code-name">#luckysheet-iconfont-jine</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-caidan1"></use>
                </svg>
                <div class="name">菜单1</div>
                <div class="code-name">#luckysheet-iconfont-caidan1</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-quxiaohebing"></use>
                </svg>
                <div class="name">取消合并</div>
                <div class="code-name">#luckysheet-iconfont-quxiaohebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wenbenxiahuaxian"></use>
                </svg>
                <div class="name">文本下划线</div>
                <div class="code-name">#luckysheet-iconfont-wenbenxiahuaxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shangbiankuang"></use>
                </svg>
                <div class="name">上边框</div>
                <div class="code-name">#luckysheet-iconfont-shangbiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-dingwei"></use>
                </svg>
                <div class="name">定位</div>
                <div class="code-name">#luckysheet-iconfont-dingwei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-sizhoujiabiankuang"></use>
                </svg>
                <div class="name">四周加边框</div>
                <div class="code-name">#luckysheet-iconfont-sizhoujiabiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-cebianlanshouqi"></use>
                </svg>
                <div class="name">侧边栏收起</div>
                <div class="code-name">#luckysheet-iconfont-cebianlanshouqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-hebing"></use>
                </svg>
                <div class="name">合并</div>
                <div class="code-name">#luckysheet-iconfont-hebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-xiangshangqingxie"></use>
                </svg>
                <div class="name">向上倾斜</div>
                <div class="code-name">#luckysheet-iconfont-xiangshangqingxie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shuipingduiqi"></use>
                </svg>
                <div class="name">水平对齐</div>
                <div class="code-name">#luckysheet-iconfont-shuipingduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wenbenshanchuxian"></use>
                </svg>
                <div class="name">文本删除线</div>
                <div class="code-name">#luckysheet-iconfont-wenbenshanchuxian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wenbenyouduiqi"></use>
                </svg>
                <div class="name">文本右对齐</div>
                <div class="code-name">#luckysheet-iconfont-wenbenyouduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-qianjin"></use>
                </svg>
                <div class="name">前进</div>
                <div class="code-name">#luckysheet-iconfont-qianjin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-tubiao"></use>
                </svg>
                <div class="name">图表</div>
                <div class="code-name">#luckysheet-iconfont-tubiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-youbiankuang"></use>
                </svg>
                <div class="name">右边框</div>
                <div class="code-name">#luckysheet-iconfont-youbiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-baifenhao"></use>
                </svg>
                <div class="name">百分号</div>
                <div class="code-name">#luckysheet-iconfont-baifenhao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-geshishua"></use>
                </svg>
                <div class="name">格式刷</div>
                <div class="code-name">#luckysheet-iconfont-geshishua</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-baocun"></use>
                </svg>
                <div class="name">保存</div>
                <div class="code-name">#luckysheet-iconfont-baocun</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shujuyanzheng"></use>
                </svg>
                <div class="name">数据验证</div>
                <div class="code-name">#luckysheet-iconfont-shujuyanzheng</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jieduan"></use>
                </svg>
                <div class="name">截断</div>
                <div class="code-name">#luckysheet-iconfont-jieduan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-geshitiaojian"></use>
                </svg>
                <div class="name">格式条件</div>
                <div class="code-name">#luckysheet-iconfont-geshitiaojian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-zidonghuanhang"></use>
                </svg>
                <div class="name">自动换行</div>
                <div class="code-name">#luckysheet-iconfont-zidonghuanhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-cebianlanzhankai"></use>
                </svg>
                <div class="name">侧边栏展开</div>
                <div class="code-name">#luckysheet-iconfont-cebianlanzhankai</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shaixuan2"></use>
                </svg>
                <div class="name">筛选2</div>
                <div class="code-name">#luckysheet-iconfont-shaixuan2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-xiangxiaqingxie"></use>
                </svg>
                <div class="name">向下倾斜</div>
                <div class="code-name">#luckysheet-iconfont-xiangxiaqingxie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-yichu"></use>
                </svg>
                <div class="name">溢出</div>
                <div class="code-name">#luckysheet-iconfont-yichu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-chuizhihebing"></use>
                </svg>
                <div class="name">垂直合并</div>
                <div class="code-name">#luckysheet-iconfont-chuizhihebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wenbenfensanduiqi"></use>
                </svg>
                <div class="name">文本分散对齐</div>
                <div class="code-name">#luckysheet-iconfont-wenbenfensanduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-zuobiankuang"></use>
                </svg>
                <div class="name">左边框</div>
                <div class="code-name">#luckysheet-iconfont-zuobiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-fenyechakan"></use>
                </svg>
                <div class="name">分页查看</div>
                <div class="code-name">#luckysheet-iconfont-fenyechakan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-yunhang"></use>
                </svg>
                <div class="name">运行</div>
                <div class="code-name">#luckysheet-iconfont-yunhang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-lie"></use>
                </svg>
                <div class="name">列</div>
                <div class="code-name">#luckysheet-iconfont-lie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-quanping"></use>
                </svg>
                <div class="name">全屏</div>
                <div class="code-name">#luckysheet-iconfont-quanping</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shaixuan"></use>
                </svg>
                <div class="name">筛选</div>
                <div class="code-name">#luckysheet-iconfont-shaixuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-gengxin"></use>
                </svg>
                <div class="name">更新</div>
                <div class="code-name">#luckysheet-iconfont-gengxin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-qingchu"></use>
                </svg>
                <div class="name">清除</div>
                <div class="code-name">#luckysheet-iconfont-qingchu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-hang"></use>
                </svg>
                <div class="name">行</div>
                <div class="code-name">#luckysheet-iconfont-hang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-zhushi"></use>
                </svg>
                <div class="name">注释</div>
                <div class="code-name">#luckysheet-iconfont-zhushi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jian"></use>
                </svg>
                <div class="name">剪</div>
                <div class="code-name">#luckysheet-iconfont-jian</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jisuan"></use>
                </svg>
                <div class="name">计算</div>
                <div class="code-name">#luckysheet-iconfont-jisuan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-jia"></use>
                </svg>
                <div class="name">加</div>
                <div class="code-name">#luckysheet-iconfont-jia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-dibuduiqi"></use>
                </svg>
                <div class="name">底部对齐</div>
                <div class="code-name">#luckysheet-iconfont-dibuduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-xiangshang90"></use>
                </svg>
                <div class="name">向上90</div>
                <div class="code-name">#luckysheet-iconfont-xiangshang90</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wuxuanzhuang"></use>
                </svg>
                <div class="name">无选装</div>
                <div class="code-name">#luckysheet-iconfont-wuxuanzhuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-xianshiyincangwangge"></use>
                </svg>
                <div class="name">显示隐藏网格</div>
                <div class="code-name">#luckysheet-iconfont-xianshiyincangwangge</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-dongjie"></use>
                </svg>
                <div class="name">冻结</div>
                <div class="code-name">#luckysheet-iconfont-dongjie</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-wenbenzuoduiqi"></use>
                </svg>
                <div class="name">文本左对齐</div>
                <div class="code-name">#luckysheet-iconfont-wenbenzuoduiqi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-houtui"></use>
                </svg>
                <div class="name">后退</div>
                <div class="code-name">#luckysheet-iconfont-houtui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shuipinghebing"></use>
                </svg>
                <div class="name">水平合并</div>
                <div class="code-name">#luckysheet-iconfont-shuipinghebing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-xiabiankuang"></use>
                </svg>
                <div class="name">下边框</div>
                <div class="code-name">#luckysheet-iconfont-xiabiankuang</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#luckysheet-iconfont-shezhi"></use>
                </svg>
                <div class="name">设置</div>
                <div class="code-name">#luckysheet-iconfont-shezhi</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
