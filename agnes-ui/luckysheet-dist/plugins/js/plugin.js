!function(t,e){"object"==typeof module&&"object"==typeof module.exports?module.exports=t.document?e(t,!0):function(t){if(t.document)return e(t);throw new Error("jQuery requires a window with a document")}:e(t)}("undefined"!=typeof window?window:this,(function(t,e){function n(t,e){return e.toUpperCase()}var r=[],i=t.document,o=r.slice,s=r.concat,a=r.push,u=r.indexOf,c={},l=c.toString,h=c.hasOwnProperty,f={},p="2.2.4",d=function(t,e){return new d.fn.init(t,e)},g=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,m=/^-ms-/,v=/-([\da-z])/gi;function y(t){var e=!!t&&"length"in t&&t.length,n=d.type(t);return"function"!==n&&!d.isWindow(t)&&("array"===n||0===e||"number"==typeof e&&0<e&&e-1 in t)}function b(t,e,n){for(var r=[],i=void 0!==n;(t=t[e])&&9!==t.nodeType;)if(1===t.nodeType){if(i&&d(t).is(n))break;r.push(t)}return r}function w(t,e){for(var n=[];t;t=t.nextSibling)1===t.nodeType&&t!==e&&n.push(t);return n}d.fn=d.prototype={jquery:p,constructor:d,selector:"",length:0,toArray:function(){return o.call(this)},get:function(t){return null!=t?t<0?this[t+this.length]:this[t]:o.call(this)},pushStack:function(t){return t=d.merge(this.constructor(),t),t.prevObject=this,t.context=this.context,t},each:function(t){return d.each(this,t)},map:function(t){return this.pushStack(d.map(this,(function(e,n){return t.call(e,n,e)})))},slice:function(){return this.pushStack(o.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},eq:function(t){var e=this.length;t=+t+(t<0?e:0);return this.pushStack(0<=t&&t<e?[this[t]]:[])},end:function(){return this.prevObject||this.constructor()},push:a,sort:r.sort,splice:r.splice},d.extend=d.fn.extend=function(){var t,e,n,r,i,o=arguments[0]||{},s=1,a=arguments.length,u=!1;for("boolean"==typeof o&&(u=o,o=arguments[s]||{},s++),"object"==typeof o||d.isFunction(o)||(o={}),s===a&&(o=this,s--);s<a;s++)if(null!=(t=arguments[s]))for(e in t)i=o[e],n=t[e],o!==n&&(u&&n&&(d.isPlainObject(n)||(r=d.isArray(n)))?(i=r?(r=!1,i&&d.isArray(i)?i:[]):i&&d.isPlainObject(i)?i:{},o[e]=d.extend(u,i,n)):void 0!==n&&(o[e]=n));return o},d.extend({expando:"jQuery"+(p+Math.random()).replace(/\D/g,""),isReady:!0,error:function(t){throw new Error(t)},noop:function(){},isFunction:function(t){return"function"===d.type(t)},isArray:Array.isArray,isWindow:function(t){return null!=t&&t===t.window},isNumeric:function(t){var e=t&&t.toString();return!d.isArray(t)&&0<=e-parseFloat(e)+1},isPlainObject:function(t){if("object"!==d.type(t)||t.nodeType||d.isWindow(t))return!1;if(t.constructor&&!h.call(t,"constructor")&&!h.call(t.constructor.prototype||{},"isPrototypeOf"))return!1;for(var e in t);return void 0===e||h.call(t,e)},isEmptyObject:function(t){for(var e in t)return!1;return!0},type:function(t){return null==t?t+"":"object"==typeof t||"function"==typeof t?c[l.call(t)]||"object":typeof t},globalEval:function(t){var e,n=eval;(t=d.trim(t))&&(1===t.indexOf("use strict")?((e=i.createElement("script")).text=t,i.head.appendChild(e).parentNode.removeChild(e)):n(t))},camelCase:function(t){return t.replace(m,"ms-").replace(v,n)},nodeName:function(t,e){return t.nodeName&&t.nodeName.toLowerCase()===e.toLowerCase()},each:function(t,e){var n,r=0;if(y(t))for(n=t.length;r<n&&!1!==e.call(t[r],r,t[r]);r++);else for(r in t)if(!1===e.call(t[r],r,t[r]))break;return t},trim:function(t){return null==t?"":(t+"").replace(g,"")},makeArray:function(t,e){return e=e||[],null!=t&&(y(Object(t))?d.merge(e,"string"==typeof t?[t]:t):a.call(e,t)),e},inArray:function(t,e,n){return null==e?-1:u.call(e,t,n)},merge:function(t,e){for(var n=+e.length,r=0,i=t.length;r<n;r++)t[i++]=e[r];return t.length=i,t},grep:function(t,e,n){for(var r=[],i=0,o=t.length,s=!n;i<o;i++)!e(t[i],i)!=s&&r.push(t[i]);return r},map:function(t,e,n){var r,i,o=0,a=[];if(y(t))for(r=t.length;o<r;o++)null!=(i=e(t[o],o,n))&&a.push(i);else for(o in t)i=e(t[o],o,n),null!=i&&a.push(i);return s.apply([],a)},guid:1,proxy:function(t,e){var n,r;return"string"==typeof e&&(r=t[e],e=t,t=r),d.isFunction(t)?(n=o.call(arguments,2),(r=function(){return t.apply(e||this,n.concat(o.call(arguments)))}).guid=t.guid=t.guid||d.guid++,r):void 0},now:Date.now,support:f}),"function"==typeof Symbol&&(d.fn[Symbol.iterator]=r[Symbol.iterator]),d.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),(function(t,e){c["[object "+e+"]"]=e.toLowerCase()}));p=function(t){function e(t,e,n){var r="0x"+e-65536;return r!=r||n?e:r<0?String.fromCharCode(65536+r):String.fromCharCode(r>>10|55296,1023&r|56320)}function n(){d()}var r,i,o,s,a,u,c,l,h,f,p,d,g,m,v,y,b,w,_,x="sizzle"+ +new Date,C=t.document,k=0,S=0,O=it(),E=it(),T=it(),A=function(t,e){return t===e&&(p=!0),0},I={}.hasOwnProperty,j=[],P=j.pop,N=j.push,D=j.push,R=j.slice,M=function(t,e){for(var n=0,r=t.length;n<r;n++)if(t[n]===e)return n;return-1},W="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",L="[\\x20\\t\\r\\n\\f]",F="(?:\\\\.|[\\w-]|[^\\x00-\\xa0])+",H="\\["+L+"*("+F+")(?:"+L+"*([*^$|!~]?=)"+L+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+F+"))|)"+L+"*\\]",B=":("+F+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+H+")*)|.*)\\)|)",q=new RegExp(L+"+","g"),z=new RegExp("^"+L+"+|((?:^|[^\\\\])(?:\\\\.)*)"+L+"+$","g"),U=new RegExp("^"+L+"*,"+L+"*"),G=new RegExp("^"+L+"*([>+~]|"+L+")"+L+"*"),V=new RegExp("="+L+"*([^\\]'\"]*?)"+L+"*\\]","g"),$=new RegExp(B),X=new RegExp("^"+F+"$"),Y={ID:new RegExp("^#("+F+")"),CLASS:new RegExp("^\\.("+F+")"),TAG:new RegExp("^("+F+"|[*])"),ATTR:new RegExp("^"+H),PSEUDO:new RegExp("^"+B),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+L+"*(even|odd|(([+-]|)(\\d*)n|)"+L+"*(?:([+-]|)"+L+"*(\\d+)|))"+L+"*\\)|)","i"),bool:new RegExp("^(?:"+W+")$","i"),needsContext:new RegExp("^"+L+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+L+"*((?:-\\d)?\\d*)"+L+"*\\)|)(?=[^-]|$)","i")},K=/^(?:input|select|textarea|button)$/i,Q=/^h\d$/i,J=/^[^{]+\{\s*\[native \w/,Z=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,tt=/[+~]/,et=/'|\\/g,nt=new RegExp("\\\\([\\da-f]{1,6}"+L+"?|("+L+")|.)","ig");try{D.apply(j=R.call(C.childNodes),C.childNodes),j[C.childNodes.length].nodeType}catch(r){D={apply:j.length?function(t,e){N.apply(t,R.call(e))}:function(t,e){for(var n=t.length,r=0;t[n++]=e[r++];);t.length=n-1}}}function rt(t,e,n,r){var o,s,a,c,h,f,p,m,b=e&&e.ownerDocument,w=e?e.nodeType:9;if(n=n||[],"string"!=typeof t||!t||1!==w&&9!==w&&11!==w)return n;if(!r&&((e?e.ownerDocument||e:C)!==g&&d(e),e=e||g,v)){if(11!==w&&(f=Z.exec(t)))if(o=f[1]){if(9===w){if(!(a=e.getElementById(o)))return n;if(a.id===o)return n.push(a),n}else if(b&&(a=b.getElementById(o))&&_(e,a)&&a.id===o)return n.push(a),n}else{if(f[2])return D.apply(n,e.getElementsByTagName(t)),n;if((o=f[3])&&i.getElementsByClassName&&e.getElementsByClassName)return D.apply(n,e.getElementsByClassName(o)),n}if(i.qsa&&!T[t+" "]&&(!y||!y.test(t))){if(1!==w)b=e,m=t;else if("object"!==e.nodeName.toLowerCase()){for((c=e.getAttribute("id"))?c=c.replace(et,"\\$&"):e.setAttribute("id",c=x),s=(p=u(t)).length,h=X.test(c)?"#"+c:"[id='"+c+"']";s--;)p[s]=h+" "+ft(p[s]);m=p.join(","),b=tt.test(t)&&lt(e.parentNode)||e}if(m)try{return D.apply(n,b.querySelectorAll(m)),n}catch(t){}finally{c===x&&e.removeAttribute("id")}}}return l(t.replace(z,"$1"),e,n,r)}function it(){var t=[];function e(n,r){return t.push(n+" ")>o.cacheLength&&delete e[t.shift()],e[n+" "]=r}return e}function ot(t){return t[x]=!0,t}function st(t){var e=g.createElement("div");try{return!!t(e)}catch(t){return!1}finally{e.parentNode&&e.parentNode.removeChild(e)}}function at(t,e){for(var n=t.split("|"),r=n.length;r--;)o.attrHandle[n[r]]=e}function ut(t,e){var n=e&&t,r=n&&1===t.nodeType&&1===e.nodeType&&(~e.sourceIndex||1<<31)-(~t.sourceIndex||1<<31);if(r)return r;if(n)for(;n=n.nextSibling;)if(n===e)return-1;return t?1:-1}function ct(t){return ot((function(e){return e=+e,ot((function(n,r){for(var i,o=t([],n.length,e),s=o.length;s--;)n[i=o[s]]&&(n[i]=!(r[i]=n[i]))}))}))}function lt(t){return t&&void 0!==t.getElementsByTagName&&t}for(r in i=rt.support={},a=rt.isXML=function(t){return t=t&&(t.ownerDocument||t).documentElement,!!t&&"HTML"!==t.nodeName},d=rt.setDocument=function(t){t=t?t.ownerDocument||t:C;return t!==g&&9===t.nodeType&&t.documentElement&&(m=(g=t).documentElement,v=!a(g),(t=g.defaultView)&&t.top!==t&&(t.addEventListener?t.addEventListener("unload",n,!1):t.attachEvent&&t.attachEvent("onunload",n)),i.attributes=st((function(t){return t.className="i",!t.getAttribute("className")})),i.getElementsByTagName=st((function(t){return t.appendChild(g.createComment("")),!t.getElementsByTagName("*").length})),i.getElementsByClassName=J.test(g.getElementsByClassName),i.getById=st((function(t){return m.appendChild(t).id=x,!g.getElementsByName||!g.getElementsByName(x).length})),i.getById?(o.find.ID=function(t,e){if(void 0!==e.getElementById&&v)return(e=e.getElementById(t))?[e]:[]},o.filter.ID=function(t){var n=t.replace(nt,e);return function(t){return t.getAttribute("id")===n}}):(delete o.find.ID,o.filter.ID=function(t){var n=t.replace(nt,e);return function(t){return t=void 0!==t.getAttributeNode&&t.getAttributeNode("id"),t&&t.value===n}}),o.find.TAG=i.getElementsByTagName?function(t,e){return void 0!==e.getElementsByTagName?e.getElementsByTagName(t):i.qsa?e.querySelectorAll(t):void 0}:function(t,e){var n,r=[],i=0,o=e.getElementsByTagName(t);if("*"!==t)return o;for(;n=o[i++];)1===n.nodeType&&r.push(n);return r},o.find.CLASS=i.getElementsByClassName&&function(t,e){return void 0!==e.getElementsByClassName&&v?e.getElementsByClassName(t):void 0},b=[],y=[],(i.qsa=J.test(g.querySelectorAll))&&(st((function(t){m.appendChild(t).innerHTML="<a id='"+x+"'></a><select id='"+x+"-\r\\' msallowcapture=''><option selected=''></option></select>",t.querySelectorAll("[msallowcapture^='']").length&&y.push("[*^$]="+L+"*(?:''|\"\")"),t.querySelectorAll("[selected]").length||y.push("\\["+L+"*(?:value|"+W+")"),t.querySelectorAll("[id~="+x+"-]").length||y.push("~="),t.querySelectorAll(":checked").length||y.push(":checked"),t.querySelectorAll("a#"+x+"+*").length||y.push(".#.+[+~]")})),st((function(t){var e=g.createElement("input");e.setAttribute("type","hidden"),t.appendChild(e).setAttribute("name","D"),t.querySelectorAll("[name=d]").length&&y.push("name"+L+"*[*^$|!~]?="),t.querySelectorAll(":enabled").length||y.push(":enabled",":disabled"),t.querySelectorAll("*,:x"),y.push(",.*:")}))),(i.matchesSelector=J.test(w=m.matches||m.webkitMatchesSelector||m.mozMatchesSelector||m.oMatchesSelector||m.msMatchesSelector))&&st((function(t){i.disconnectedMatch=w.call(t,"div"),w.call(t,"[s!='']:x"),b.push("!=",B)})),y=y.length&&new RegExp(y.join("|")),b=b.length&&new RegExp(b.join("|")),t=J.test(m.compareDocumentPosition),_=t||J.test(m.contains)?function(t,e){var n=9===t.nodeType?t.documentElement:t;e=e&&e.parentNode;return t===e||!(!e||1!==e.nodeType||!(n.contains?n.contains(e):t.compareDocumentPosition&&16&t.compareDocumentPosition(e)))}:function(t,e){if(e)for(;e=e.parentNode;)if(e===t)return!0;return!1},A=t?function(t,e){var n;return t===e?(p=!0,0):(n=!t.compareDocumentPosition-!e.compareDocumentPosition)||(1&(n=(t.ownerDocument||t)===(e.ownerDocument||e)?t.compareDocumentPosition(e):1)||!i.sortDetached&&e.compareDocumentPosition(t)===n?t===g||t.ownerDocument===C&&_(C,t)?-1:e===g||e.ownerDocument===C&&_(C,e)?1:f?M(f,t)-M(f,e):0:4&n?-1:1)}:function(t,e){if(t===e)return p=!0,0;var n,r=0,i=t.parentNode,o=e.parentNode,s=[t],a=[e];if(!i||!o)return t===g?-1:e===g?1:i?-1:o?1:f?M(f,t)-M(f,e):0;if(i===o)return ut(t,e);for(n=t;n=n.parentNode;)s.unshift(n);for(n=e;n=n.parentNode;)a.unshift(n);for(;s[r]===a[r];)r++;return r?ut(s[r],a[r]):s[r]===C?-1:a[r]===C?1:0}),g},rt.matches=function(t,e){return rt(t,null,null,e)},rt.matchesSelector=function(t,e){if((t.ownerDocument||t)!==g&&d(t),e=e.replace(V,"='$1']"),i.matchesSelector&&v&&!T[e+" "]&&(!b||!b.test(e))&&(!y||!y.test(e)))try{var n=w.call(t,e);if(n||i.disconnectedMatch||t.document&&11!==t.document.nodeType)return n}catch(t){}return 0<rt(e,g,null,[t]).length},rt.contains=function(t,e){return(t.ownerDocument||t)!==g&&d(t),_(t,e)},rt.attr=function(t,e){(t.ownerDocument||t)!==g&&d(t);var n=o.attrHandle[e.toLowerCase()];n=n&&I.call(o.attrHandle,e.toLowerCase())?n(t,e,!v):void 0;return void 0!==n?n:i.attributes||!v?t.getAttribute(e):(n=t.getAttributeNode(e))&&n.specified?n.value:null},rt.error=function(t){throw new Error("Syntax error, unrecognized expression: "+t)},rt.uniqueSort=function(t){var e,n=[],r=0,o=0;if(p=!i.detectDuplicates,f=!i.sortStable&&t.slice(0),t.sort(A),p){for(;e=t[o++];)e===t[o]&&(r=n.push(o));for(;r--;)t.splice(n[r],1)}return f=null,t},s=rt.getText=function(t){var e,n="",r=0,i=t.nodeType;if(i){if(1===i||9===i||11===i){if("string"==typeof t.textContent)return t.textContent;for(t=t.firstChild;t;t=t.nextSibling)n+=s(t)}else if(3===i||4===i)return t.nodeValue}else for(;e=t[r++];)n+=s(e);return n},(o=rt.selectors={cacheLength:50,createPseudo:ot,match:Y,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(t){return t[1]=t[1].replace(nt,e),t[3]=(t[3]||t[4]||t[5]||"").replace(nt,e),"~="===t[2]&&(t[3]=" "+t[3]+" "),t.slice(0,4)},CHILD:function(t){return t[1]=t[1].toLowerCase(),"nth"===t[1].slice(0,3)?(t[3]||rt.error(t[0]),t[4]=+(t[4]?t[5]+(t[6]||1):2*("even"===t[3]||"odd"===t[3])),t[5]=+(t[7]+t[8]||"odd"===t[3])):t[3]&&rt.error(t[0]),t},PSEUDO:function(t){var e,n=!t[6]&&t[2];return Y.CHILD.test(t[0])?null:(t[3]?t[2]=t[4]||t[5]||"":n&&$.test(n)&&(e=(e=u(n,!0))&&n.indexOf(")",n.length-e)-n.length)&&(t[0]=t[0].slice(0,e),t[2]=n.slice(0,e)),t.slice(0,3))}},filter:{TAG:function(t){var n=t.replace(nt,e).toLowerCase();return"*"===t?function(){return!0}:function(t){return t.nodeName&&t.nodeName.toLowerCase()===n}},CLASS:function(t){var e=O[t+" "];return e||(e=new RegExp("(^|"+L+")"+t+"("+L+"|$)"))&&O(t,(function(t){return e.test("string"==typeof t.className&&t.className||void 0!==t.getAttribute&&t.getAttribute("class")||"")}))},ATTR:function(t,e,n){return function(r){return r=rt.attr(r,t),null==r?"!="===e:!e||(r+="","="===e?r===n:"!="===e?r!==n:"^="===e?n&&0===r.indexOf(n):"*="===e?n&&-1<r.indexOf(n):"$="===e?n&&r.slice(-n.length)===n:"~="===e?-1<(" "+r.replace(q," ")+" ").indexOf(n):"|="===e&&(r===n||r.slice(0,n.length+1)===n+"-"))}},CHILD:function(t,e,n,r,i){var o="nth"!==t.slice(0,3),s="last"!==t.slice(-4),a="of-type"===e;return 1===r&&0===i?function(t){return!!t.parentNode}:function(e,n,u){var c,l,h,f,p,d,g=o!=s?"nextSibling":"previousSibling",m=e.parentNode,v=a&&e.nodeName.toLowerCase(),y=!u&&!a,b=!1;if(m){if(o){for(;g;){for(f=e;f=f[g];)if(a?f.nodeName.toLowerCase()===v:1===f.nodeType)return!1;d=g="only"===t&&!d&&"nextSibling"}return!0}if(d=[s?m.firstChild:m.lastChild],s&&y){for(b=(p=(c=(l=(h=(f=m)[x]||(f[x]={}))[f.uniqueID]||(h[f.uniqueID]={}))[t]||[])[0]===k&&c[1])&&c[2],f=p&&m.childNodes[p];f=++p&&f&&f[g]||(b=p=0,d.pop());)if(1===f.nodeType&&++b&&f===e){l[t]=[k,p,b];break}}else if(!1===(b=y?p=(c=(l=(h=(f=e)[x]||(f[x]={}))[f.uniqueID]||(h[f.uniqueID]={}))[t]||[])[0]===k&&c[1]:b))for(;(f=++p&&f&&f[g]||(b=p=0,d.pop()))&&((a?f.nodeName.toLowerCase()!==v:1!==f.nodeType)||!++b||(y&&((l=(h=f[x]||(f[x]={}))[f.uniqueID]||(h[f.uniqueID]={}))[t]=[k,b]),f!==e)););return(b-=i)===r||b%r==0&&0<=b/r}}},PSEUDO:function(t,e){var n,r=o.pseudos[t]||o.setFilters[t.toLowerCase()]||rt.error("unsupported pseudo: "+t);return r[x]?r(e):1<r.length?(n=[t,t,"",e],o.setFilters.hasOwnProperty(t.toLowerCase())?ot((function(t,n){for(var i,o=r(t,e),s=o.length;s--;)t[i=M(t,o[s])]=!(n[i]=o[s])})):function(t){return r(t,0,n)}):r}},pseudos:{not:ot((function(t){var e=[],n=[],r=c(t.replace(z,"$1"));return r[x]?ot((function(t,e,n,i){for(var o,s=r(t,null,i,[]),a=t.length;a--;)(o=s[a])&&(t[a]=!(e[a]=o))})):function(t,i,o){return e[0]=t,r(e,null,o,n),e[0]=null,!n.pop()}})),has:ot((function(t){return function(e){return 0<rt(t,e).length}})),contains:ot((function(t){return t=t.replace(nt,e),function(e){return-1<(e.textContent||e.innerText||s(e)).indexOf(t)}})),lang:ot((function(t){return X.test(t||"")||rt.error("unsupported lang: "+t),t=t.replace(nt,e).toLowerCase(),function(e){var n;do{if(n=v?e.lang:e.getAttribute("xml:lang")||e.getAttribute("lang"))return(n=n.toLowerCase())===t||0===n.indexOf(t+"-")}while((e=e.parentNode)&&1===e.nodeType);return!1}})),target:function(e){var n=t.location&&t.location.hash;return n&&n.slice(1)===e.id},root:function(t){return t===m},focus:function(t){return t===g.activeElement&&(!g.hasFocus||g.hasFocus())&&!!(t.type||t.href||~t.tabIndex)},enabled:function(t){return!1===t.disabled},disabled:function(t){return!0===t.disabled},checked:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&!!t.checked||"option"===e&&!!t.selected},selected:function(t){return t.parentNode&&t.parentNode.selectedIndex,!0===t.selected},empty:function(t){for(t=t.firstChild;t;t=t.nextSibling)if(t.nodeType<6)return!1;return!0},parent:function(t){return!o.pseudos.empty(t)},header:function(t){return Q.test(t.nodeName)},input:function(t){return K.test(t.nodeName)},button:function(t){var e=t.nodeName.toLowerCase();return"input"===e&&"button"===t.type||"button"===e},text:function(t){return"input"===t.nodeName.toLowerCase()&&"text"===t.type&&(null==(t=t.getAttribute("type"))||"text"===t.toLowerCase())},first:ct((function(){return[0]})),last:ct((function(t,e){return[e-1]})),eq:ct((function(t,e,n){return[n<0?n+e:n]})),even:ct((function(t,e){for(var n=0;n<e;n+=2)t.push(n);return t})),odd:ct((function(t,e){for(var n=1;n<e;n+=2)t.push(n);return t})),lt:ct((function(t,e,n){for(var r=n<0?n+e:n;0<=--r;)t.push(r);return t})),gt:ct((function(t,e,n){for(var r=n<0?n+e:n;++r<e;)t.push(r);return t}))}}).pseudos.nth=o.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})o.pseudos[r]=function(t){return function(e){return"input"===e.nodeName.toLowerCase()&&e.type===t}}(r);for(r in{submit:!0,reset:!0})o.pseudos[r]=function(t){return function(e){var n=e.nodeName.toLowerCase();return("input"===n||"button"===n)&&e.type===t}}(r);function ht(){}function ft(t){for(var e=0,n=t.length,r="";e<n;e++)r+=t[e].value;return r}function pt(t,e,n){var r=e.dir,i=n&&"parentNode"===r,o=S++;return e.first?function(e,n,o){for(;e=e[r];)if(1===e.nodeType||i)return t(e,n,o)}:function(e,n,s){var a,u,c=[k,o];if(s){for(;e=e[r];)if((1===e.nodeType||i)&&t(e,n,s))return!0}else for(;e=e[r];)if(1===e.nodeType||i){if((a=(u=(u=e[x]||(e[x]={}))[e.uniqueID]||(u[e.uniqueID]={}))[r])&&a[0]===k&&a[1]===o)return c[2]=a[2];if((u[r]=c)[2]=t(e,n,s))return!0}}}function dt(t){return 1<t.length?function(e,n,r){for(var i=t.length;i--;)if(!t[i](e,n,r))return!1;return!0}:t[0]}function gt(t,e,n,r,i){for(var o,s=[],a=0,u=t.length,c=null!=e;a<u;a++)!(o=t[a])||n&&!n(o,r,i)||(s.push(o),c&&e.push(a));return s}function mt(t,e,n,r,i,o){return r&&!r[x]&&(r=mt(r)),i&&!i[x]&&(i=mt(i,o)),ot((function(o,s,a,u){var c,l,h,f=[],p=[],d=s.length,g=o||function(t,e,n){for(var r=0,i=e.length;r<i;r++)rt(t,e[r],n);return n}(e||"*",a.nodeType?[a]:a,[]),m=!t||!o&&e?g:gt(g,f,t,a,u),v=n?i||(o?t:d||r)?[]:s:m;if(n&&n(m,v,a,u),r)for(c=gt(v,p),r(c,[],a,u),l=c.length;l--;)(h=c[l])&&(v[p[l]]=!(m[p[l]]=h));if(o){if(i||t){if(i){for(c=[],l=v.length;l--;)(h=v[l])&&c.push(m[l]=h);i(null,v=[],c,u)}for(l=v.length;l--;)(h=v[l])&&-1<(c=i?M(o,h):f[l])&&(o[c]=!(s[c]=h))}}else v=gt(v===s?v.splice(d,v.length):v),i?i(null,s,v,u):D.apply(s,v)}))}function vt(t,e){function n(n,s,a,u,c){var l,f,p,m=0,y="0",b=n&&[],w=[],_=h,x=n||i&&o.find.TAG("*",c),C=k+=null==_?1:Math.random()||.1,S=x.length;for(c&&(h=s===g||s||c);y!==S&&null!=(l=x[y]);y++){if(i&&l){for(f=0,s||l.ownerDocument===g||(d(l),a=!v);p=t[f++];)if(p(l,s||g,a)){u.push(l);break}c&&(k=C)}r&&((l=!p&&l)&&m--,n)&&b.push(l)}if(m+=y,r&&y!==m){for(f=0;p=e[f++];)p(b,w,s,a);if(n){if(0<m)for(;y--;)b[y]||w[y]||(w[y]=P.call(u));w=gt(w)}D.apply(u,w),c&&!n&&0<w.length&&1<m+e.length&&rt.uniqueSort(u)}return c&&(k=C,h=_),b}var r=0<e.length,i=0<t.length;return r?ot(n):n}return ht.prototype=o.filters=o.pseudos,o.setFilters=new ht,u=rt.tokenize=function(t,e){var n,r,i,s,a,u,c,l=E[t+" "];if(l)return e?0:l.slice(0);for(a=t,u=[],c=o.preFilter;a;){for(s in n&&!(r=U.exec(a))||(r&&(a=a.slice(r[0].length)||a),u.push(i=[])),n=!1,(r=G.exec(a))&&(n=r.shift(),i.push({value:n,type:r[0].replace(z," ")}),a=a.slice(n.length)),o.filter)!(r=Y[s].exec(a))||c[s]&&!(r=c[s](r))||(n=r.shift(),i.push({value:n,type:s,matches:r}),a=a.slice(n.length));if(!n)break}return e?a.length:a?rt.error(t):E(t,u).slice(0)},c=rt.compile=function(t,e){var n,r=[],i=[],s=T[t+" "];if(!s){for(n=(e=e||u(t)).length;n--;)((s=function t(e){for(var n,r,i,s=e.length,a=o.relative[e[0].type],u=a||o.relative[" "],c=a?1:0,l=pt((function(t){return t===n}),u,!0),f=pt((function(t){return-1<M(n,t)}),u,!0),p=[function(t,e,r){return t=!a&&(r||e!==h)||((n=e).nodeType?l:f)(t,e,r),n=null,t}];c<s;c++)if(r=o.relative[e[c].type])p=[pt(dt(p),r)];else{if((r=o.filter[e[c].type].apply(null,e[c].matches))[x]){for(i=++c;i<s&&!o.relative[e[i].type];i++);return mt(1<c&&dt(p),1<c&&ft(e.slice(0,c-1).concat({value:" "===e[c-2].type?"*":""})).replace(z,"$1"),r,c<i&&t(e.slice(c,i)),i<s&&t(e=e.slice(i)),i<s&&ft(e))}p.push(r)}return dt(p)}(e[n]))[x]?r:i).push(s);(s=T(t,vt(i,r))).selector=t}return s},l=rt.select=function(t,n,r,s){var a,l,h,f,p,d="function"==typeof t&&t,g=!s&&u(t=d.selector||t);if(r=r||[],1===g.length){if(2<(l=g[0]=g[0].slice(0)).length&&"ID"===(h=l[0]).type&&i.getById&&9===n.nodeType&&v&&o.relative[l[1].type]){if(!(n=(o.find.ID(h.matches[0].replace(nt,e),n)||[])[0]))return r;d&&(n=n.parentNode),t=t.slice(l.shift().value.length)}for(a=Y.needsContext.test(t)?0:l.length;a--&&(h=l[a],!o.relative[f=h.type]);)if((p=o.find[f])&&(s=p(h.matches[0].replace(nt,e),tt.test(l[0].type)&&lt(n.parentNode)||n))){if(l.splice(a,1),t=s.length&&ft(l))break;return D.apply(r,s),r}}return(d||c(t,g))(s,n,!v,r,!n||tt.test(t)&&lt(n.parentNode)||n),r},i.sortStable=x.split("").sort(A).join("")===x,i.detectDuplicates=!!p,d(),i.sortDetached=st((function(t){return 1&t.compareDocumentPosition(g.createElement("div"))})),st((function(t){return t.innerHTML="<a href='#'></a>","#"===t.firstChild.getAttribute("href")}))||at("type|href|height|width",(function(t,e,n){return n?void 0:t.getAttribute(e,"type"===e.toLowerCase()?1:2)})),i.attributes&&st((function(t){return t.innerHTML="<input/>",t.firstChild.setAttribute("value",""),""===t.firstChild.getAttribute("value")}))||at("value",(function(t,e,n){return n||"input"!==t.nodeName.toLowerCase()?void 0:t.defaultValue})),st((function(t){return null==t.getAttribute("disabled")}))||at(W,(function(t,e,n){return n?void 0:!0===t[e]?e.toLowerCase():(n=t.getAttributeNode(e))&&n.specified?n.value:null})),rt}(t);var _=(d.find=p,d.expr=p.selectors,d.expr[":"]=d.expr.pseudos,d.uniqueSort=d.unique=p.uniqueSort,d.text=p.getText,d.isXMLDoc=p.isXML,d.contains=p.contains,d.expr.match.needsContext),x=/^<([\w-]+)\s*\/?>(?:<\/\1>|)$/,C=/^.[^:#\[\.,]*$/;function k(t,e,n){if(d.isFunction(e))return d.grep(t,(function(t,r){return!!e.call(t,r,t)!==n}));if(e.nodeType)return d.grep(t,(function(t){return t===e!==n}));if("string"==typeof e){if(C.test(e))return d.filter(e,t,n);e=d.filter(e,t)}return d.grep(t,(function(t){return-1<u.call(e,t)!==n}))}d.filter=function(t,e,n){var r=e[0];return n&&(t=":not("+t+")"),1===e.length&&1===r.nodeType?d.find.matchesSelector(r,t)?[r]:[]:d.find.matches(t,d.grep(e,(function(t){return 1===t.nodeType})))},d.fn.extend({find:function(t){var e,n=this.length,r=[],i=this;if("string"!=typeof t)return this.pushStack(d(t).filter((function(){for(e=0;e<n;e++)if(d.contains(i[e],this))return!0})));for(e=0;e<n;e++)d.find(t,i[e],r);return(r=this.pushStack(1<n?d.unique(r):r)).selector=this.selector?this.selector+" "+t:t,r},filter:function(t){return this.pushStack(k(this,t||[],!1))},not:function(t){return this.pushStack(k(this,t||[],!0))},is:function(t){return!!k(this,"string"==typeof t&&_.test(t)?d(t):t||[],!1).length}});var S,O=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]*))$/,E=((d.fn.init=function(t,e,n){if(t){if(n=n||S,"string"!=typeof t)return t.nodeType?(this.context=this[0]=t,this.length=1,this):d.isFunction(t)?void 0!==n.ready?n.ready(t):t(d):(void 0!==t.selector&&(this.selector=t.selector,this.context=t.context),d.makeArray(t,this));if(!(r="<"===t[0]&&">"===t[t.length-1]&&3<=t.length?[null,t,null]:O.exec(t))||!r[1]&&e)return(!e||e.jquery?e||n:this.constructor(e)).find(t);if(r[1]){if(e=e instanceof d?e[0]:e,d.merge(this,d.parseHTML(r[1],e&&e.nodeType?e.ownerDocument||e:i,!0)),x.test(r[1])&&d.isPlainObject(e))for(var r in e)d.isFunction(this[r])?this[r](e[r]):this.attr(r,e[r])}else(n=i.getElementById(r[2]))&&n.parentNode&&(this.length=1,this[0]=n),this.context=i,this.selector=t}return this}).prototype=d.fn,S=d(i),/^(?:parents|prev(?:Until|All))/),T={children:!0,contents:!0,next:!0,prev:!0};function A(t,e){for(;(t=t[e])&&1!==t.nodeType;);return t}d.fn.extend({has:function(t){var e=d(t,this),n=e.length;return this.filter((function(){for(var t=0;t<n;t++)if(d.contains(this,e[t]))return!0}))},closest:function(t,e){for(var n,r=0,i=this.length,o=[],s=_.test(t)||"string"!=typeof t?d(t,e||this.context):0;r<i;r++)for(n=this[r];n&&n!==e;n=n.parentNode)if(n.nodeType<11&&(s?-1<s.index(n):1===n.nodeType&&d.find.matchesSelector(n,t))){o.push(n);break}return this.pushStack(1<o.length?d.uniqueSort(o):o)},index:function(t){return t?"string"==typeof t?u.call(d(t),this[0]):u.call(this,t.jquery?t[0]:t):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(t,e){return this.pushStack(d.uniqueSort(d.merge(this.get(),d(t,e))))},addBack:function(t){return this.add(null==t?this.prevObject:this.prevObject.filter(t))}}),d.each({parent:function(t){return t=t.parentNode,t&&11!==t.nodeType?t:null},parents:function(t){return b(t,"parentNode")},parentsUntil:function(t,e,n){return b(t,"parentNode",n)},next:function(t){return A(t,"nextSibling")},prev:function(t){return A(t,"previousSibling")},nextAll:function(t){return b(t,"nextSibling")},prevAll:function(t){return b(t,"previousSibling")},nextUntil:function(t,e,n){return b(t,"nextSibling",n)},prevUntil:function(t,e,n){return b(t,"previousSibling",n)},siblings:function(t){return w((t.parentNode||{}).firstChild,t)},children:function(t){return w(t.firstChild)},contents:function(t){return t.contentDocument||d.merge([],t.childNodes)}},(function(t,e){d.fn[t]=function(n,r){var i=d.map(this,e,n);return(r="Until"!==t.slice(-5)?n:r)&&"string"==typeof r&&(i=d.filter(r,i)),1<this.length&&(T[t]||d.uniqueSort(i),E.test(t))&&i.reverse(),this.pushStack(i)}}));var I,j=/\S+/g;function P(){i.removeEventListener("DOMContentLoaded",P),t.removeEventListener("load",P),d.ready()}function N(t,e,n,r,i,o,s){var a=0,u=t.length,c=null==n;if("object"===d.type(n))for(a in i=!0,n)N(t,e,a,n[a],!0,o,s);else if(void 0!==r&&(i=!0,d.isFunction(r)||(s=!0),e=c?s?(e.call(t,r),null):(c=e,function(t,e,n){return c.call(d(t),n)}):e))for(;a<u;a++)e(t[a],n,s?r:r.call(t[a],a,e(t[a],n)));return i?t:c?e.call(t):u?e(t[0],n):o}function D(t){return 1===t.nodeType||9===t.nodeType||!+t.nodeType}function R(){this.expando=d.expando+R.uid++}d.Callbacks=function(t){var e,n;function r(){for(a=t.once,s=i=!0;c.length;l=-1)for(o=c.shift();++l<u.length;)!1===u[l].apply(o[0],o[1])&&t.stopOnFalse&&(l=u.length,o=!1);t.memory||(o=!1),i=!1,a&&(u=o?[]:"")}t="string"==typeof t?(e=t,n={},d.each(e.match(j)||[],(function(t,e){n[e]=!0})),n):d.extend({},t);var i,o,s,a,u=[],c=[],l=-1,h={add:function(){return u&&(o&&!i&&(l=u.length-1,c.push(o)),function e(n){d.each(n,(function(n,r){d.isFunction(r)?t.unique&&h.has(r)||u.push(r):r&&r.length&&"string"!==d.type(r)&&e(r)}))}(arguments),o)&&!i&&r(),this},remove:function(){return d.each(arguments,(function(t,e){for(var n;-1<(n=d.inArray(e,u,n));)u.splice(n,1),n<=l&&l--})),this},has:function(t){return t?-1<d.inArray(t,u):0<u.length},empty:function(){return u=u&&[],this},disable:function(){return a=c=[],u=o="",this},disabled:function(){return!u},lock:function(){return a=c=[],o||(u=o=""),this},locked:function(){return!!a},fireWith:function(t,e){return a||(e=[t,(e=e||[]).slice?e.slice():e],c.push(e),i)||r(),this},fire:function(){return h.fireWith(this,arguments),this},fired:function(){return!!s}};return h},d.extend({Deferred:function(t){var e=[["resolve","done",d.Callbacks("once memory"),"resolved"],["reject","fail",d.Callbacks("once memory"),"rejected"],["notify","progress",d.Callbacks("memory")]],n="pending",r={state:function(){return n},always:function(){return i.done(arguments).fail(arguments),this},then:function(){var t=arguments;return d.Deferred((function(n){d.each(e,(function(e,o){var s=d.isFunction(t[e])&&t[e];i[o[1]]((function(){var t=s&&s.apply(this,arguments);t&&d.isFunction(t.promise)?t.promise().progress(n.notify).done(n.resolve).fail(n.reject):n[o[0]+"With"](this===r?n.promise():this,s?[t]:arguments)}))})),t=null})).promise()},promise:function(t){return null!=t?d.extend(t,r):r}},i={};return r.pipe=r.then,d.each(e,(function(t,o){var s=o[2],a=o[3];r[o[1]]=s.add,a&&s.add((function(){n=a}),e[1^t][2].disable,e[2][2].lock),i[o[0]]=function(){return i[o[0]+"With"](this===i?r:this,arguments),this},i[o[0]+"With"]=s.fireWith})),r.promise(i),t&&t.call(i,i),i},when:function(t){function e(t,e,r){return function(i){e[t]=this,r[t]=1<arguments.length?o.call(arguments):i,r===n?l.notifyWith(e,r):--c||l.resolveWith(e,r)}}var n,r,i,s=0,a=o.call(arguments),u=a.length,c=1!==u||t&&d.isFunction(t.promise)?u:0,l=1===c?t:d.Deferred();if(1<u)for(n=new Array(u),r=new Array(u),i=new Array(u);s<u;s++)a[s]&&d.isFunction(a[s].promise)?a[s].promise().progress(e(s,r,n)).done(e(s,i,a)).fail(l.reject):--c;return c||l.resolveWith(i,a),l.promise()}}),d.fn.ready=function(t){return d.ready.promise().done(t),this},d.extend({isReady:!1,readyWait:1,holdReady:function(t){t?d.readyWait++:d.ready(!0)},ready:function(t){(!0===t?--d.readyWait:d.isReady)||(d.isReady=!0)!==t&&0<--d.readyWait||(I.resolveWith(i,[d]),d.fn.triggerHandler&&(d(i).triggerHandler("ready"),d(i).off("ready")))}}),d.ready.promise=function(e){return I||(I=d.Deferred(),"complete"===i.readyState||"loading"!==i.readyState&&!i.documentElement.doScroll?t.setTimeout(d.ready):(i.addEventListener("DOMContentLoaded",P),t.addEventListener("load",P))),I.promise(e)},d.ready.promise(),R.uid=1,R.prototype={register:function(t,e){return e=e||{},t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,writable:!0,configurable:!0}),t[this.expando]},cache:function(t){var e;return D(t)?((e=t[this.expando])||(e={},D(t)&&(t.nodeType?t[this.expando]=e:Object.defineProperty(t,this.expando,{value:e,configurable:!0}))),e):{}},set:function(t,e,n){var r,i=this.cache(t);if("string"==typeof e)i[e]=n;else for(r in e)i[r]=e[r];return i},get:function(t,e){return void 0===e?this.cache(t):t[this.expando]&&t[this.expando][e]},access:function(t,e,n){var r;return void 0===e||e&&"string"==typeof e&&void 0===n?void 0!==(r=this.get(t,e))?r:this.get(t,d.camelCase(e)):(this.set(t,e,n),void 0!==n?n:e)},remove:function(t,e){var n,r,i,o=t[this.expando];if(void 0!==o){if(void 0===e)this.register(t);else{n=(r=d.isArray(e)?e.concat(e.map(d.camelCase)):(i=d.camelCase(e),e in o?[e,i]:(r=i)in o?[r]:r.match(j)||[])).length;for(;n--;)delete o[r[n]]}void 0!==e&&!d.isEmptyObject(o)||(t.nodeType?t[this.expando]=void 0:delete t[this.expando])}},hasData:function(t){return t=t[this.expando],void 0!==t&&!d.isEmptyObject(t)}};var M=new R,W=new R,L=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,F=/[A-Z]/g;function H(t,e,n){var r;if(void 0===n&&1===t.nodeType)if(r="data-"+e.replace(F,"-$&").toLowerCase(),"string"==typeof(n=t.getAttribute(r))){try{n="true"===n||"false"!==n&&("null"===n?null:+n+""===n?+n:L.test(n)?d.parseJSON(n):n)}catch(t){}W.set(t,e,n)}else n=void 0;return n}function B(t,e){return"none"===d.css(t=e||t,"display")||!d.contains(t.ownerDocument,t)}d.extend({hasData:function(t){return W.hasData(t)||M.hasData(t)},data:function(t,e,n){return W.access(t,e,n)},removeData:function(t,e){W.remove(t,e)},_data:function(t,e,n){return M.access(t,e,n)},_removeData:function(t,e){M.remove(t,e)}}),d.fn.extend({data:function(t,e){var n,r,i,o=this[0],s=o&&o.attributes;if(void 0!==t)return"object"==typeof t?this.each((function(){W.set(this,t)})):N(this,(function(e){var n,r;if(o&&void 0===e)return void 0!==(n=W.get(o,t)||W.get(o,t.replace(F,"-$&").toLowerCase()))||(r=d.camelCase(t),void 0!==(n=W.get(o,r)))||void 0!==(n=H(o,r,void 0))?n:void 0;r=d.camelCase(t),this.each((function(){var n=W.get(this,r);W.set(this,r,e),-1<t.indexOf("-")&&void 0!==n&&W.set(this,t,e)}))}),null,e,1<arguments.length,null,!0);if(this.length&&(i=W.get(o),1===o.nodeType)&&!M.get(o,"hasDataAttrs")){for(n=s.length;n--;)s[n]&&0===(r=s[n].name).indexOf("data-")&&(r=d.camelCase(r.slice(5)),H(o,r,i[r]));M.set(o,"hasDataAttrs",!0)}return i},removeData:function(t){return this.each((function(){W.remove(this,t)}))}}),d.extend({queue:function(t,e,n){var r;return t?(r=M.get(t,e=(e||"fx")+"queue"),n&&(!r||d.isArray(n)?r=M.access(t,e,d.makeArray(n)):r.push(n)),r||[]):void 0},dequeue:function(t,e){e=e||"fx";var n=d.queue(t,e),r=n.length,i=n.shift(),o=d._queueHooks(t,e);"inprogress"===i&&(i=n.shift(),r--),i&&("fx"===e&&n.unshift("inprogress"),delete o.stop,i.call(t,(function(){d.dequeue(t,e)}),o)),!r&&o&&o.empty.fire()},_queueHooks:function(t,e){var n=e+"queueHooks";return M.get(t,n)||M.access(t,n,{empty:d.Callbacks("once memory").add((function(){M.remove(t,[e+"queue",n])}))})}}),d.fn.extend({queue:function(t,e){var n=2;return"string"!=typeof t&&(e=t,t="fx",n--),arguments.length<n?d.queue(this[0],t):void 0===e?this:this.each((function(){var n=d.queue(this,t,e);d._queueHooks(this,t),"fx"===t&&"inprogress"!==n[0]&&d.dequeue(this,t)}))},dequeue:function(t){return this.each((function(){d.dequeue(this,t)}))},clearQueue:function(t){return this.queue(t||"fx",[])},promise:function(t,e){function n(){--i||o.resolveWith(s,[s])}var r,i=1,o=d.Deferred(),s=this,a=this.length;for("string"!=typeof t&&(e=t,t=void 0),t=t||"fx";a--;)(r=M.get(s[a],t+"queueHooks"))&&r.empty&&(i++,r.empty.add(n));return n(),o.promise(e)}});r=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source;var q=new RegExp("^(?:([+-])=|)("+r+")([a-z%]*)$","i"),z=["Top","Right","Bottom","Left"];function U(t,e,n,r){var i,o=1,s=20,a=r?function(){return r.cur()}:function(){return d.css(t,e,"")},u=a(),c=n&&n[3]||(d.cssNumber[e]?"":"px"),l=(d.cssNumber[e]||"px"!==c&&+u)&&q.exec(d.css(t,e));if(l&&l[3]!==c)for(c=c||l[3],n=n||[],l=+u||1;d.style(t,e,(l/=o=o||".5")+c),o!==(o=a()/u)&&1!==o&&--s;);return n&&(l=+l||+u||0,i=n[1]?l+(n[1]+1)*n[2]:+n[2],r)&&(r.unit=c,r.start=l,r.end=i),i}var G=/^(?:checkbox|radio)$/i,V=/<([\w:-]+)/,$=/^$|\/(?:java|ecma)script/i,X={option:[1,"<select multiple='multiple'>","</select>"],thead:[1,"<table>","</table>"],col:[2,"<table><colgroup>","</colgroup></table>"],tr:[2,"<table><tbody>","</tbody></table>"],td:[3,"<table><tbody><tr>","</tr></tbody></table>"],_default:[0,"",""]};function Y(t,e){var n=void 0!==t.getElementsByTagName?t.getElementsByTagName(e||"*"):void 0!==t.querySelectorAll?t.querySelectorAll(e||"*"):[];return void 0===e||e&&d.nodeName(t,e)?d.merge([t],n):n}function K(t,e){for(var n=0,r=t.length;n<r;n++)M.set(t[n],"globalEval",!e||M.get(e[n],"globalEval"))}X.optgroup=X.option,X.tbody=X.tfoot=X.colgroup=X.caption=X.thead,X.th=X.td;var Q=/<|&#?\w+;/;function J(t,e,n,r,i){for(var o,s,a,u,c,l=e.createDocumentFragment(),h=[],f=0,p=t.length;f<p;f++)if((o=t[f])||0===o)if("object"===d.type(o))d.merge(h,o.nodeType?[o]:o);else if(Q.test(o)){for(s=s||l.appendChild(e.createElement("div")),a=(V.exec(o)||["",""])[1].toLowerCase(),a=X[a]||X._default,s.innerHTML=a[1]+d.htmlPrefilter(o)+a[2],c=a[0];c--;)s=s.lastChild;d.merge(h,s.childNodes),(s=l.firstChild).textContent=""}else h.push(e.createTextNode(o));for(l.textContent="",f=0;o=h[f++];)if(r&&-1<d.inArray(o,r))i&&i.push(o);else if(u=d.contains(o.ownerDocument,o),s=Y(l.appendChild(o),"script"),u&&K(s),n)for(c=0;o=s[c++];)$.test(o.type||"")&&n.push(o);return l}p=i.createDocumentFragment().appendChild(i.createElement("div")),(Xt=i.createElement("input")).setAttribute("type","radio"),Xt.setAttribute("checked","checked"),Xt.setAttribute("name","t"),p.appendChild(Xt),f.checkClone=p.cloneNode(!0).cloneNode(!0).lastChild.checked,p.innerHTML="<textarea>x</textarea>",f.noCloneChecked=!!p.cloneNode(!0).lastChild.defaultValue;var Z=/^key/,tt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,et=/^([^.]*)(?:\.(.+)|)/;function nt(){return!0}function rt(){return!1}function it(){try{return i.activeElement}catch(r){}}function ot(t,e,n,r,i,o){var s,a;if("object"==typeof e){for(a in"string"!=typeof n&&(r=r||n,n=void 0),e)ot(t,a,n,r,e[a],o);return t}if(null==r&&null==i?(i=n,r=n=void 0):null==i&&("string"==typeof n?(i=r,r=void 0):(i=r,r=n,n=void 0)),!1===i)i=rt;else if(!i)return t;return 1===o&&(s=i,(i=function(t){return d().off(t),s.apply(this,arguments)}).guid=s.guid||(s.guid=d.guid++)),t.each((function(){d.event.add(this,e,i,r,n)}))}d.event={global:{},add:function(t,e,n,r,i){var o,s,a,u,c,l,h,f,p,g=M.get(t);if(g)for(n.handler&&(n=(o=n).handler,i=o.selector),n.guid||(n.guid=d.guid++),a=(a=g.events)||(g.events={}),s=(s=g.handle)||(g.handle=function(e){return void 0!==d&&d.event.triggered!==e.type?d.event.dispatch.apply(t,arguments):void 0}),u=(e=(e||"").match(j)||[""]).length;u--;)h=p=(f=et.exec(e[u])||[])[1],f=(f[2]||"").split(".").sort(),h&&(c=d.event.special[h]||{},h=(i?c.delegateType:c.bindType)||h,c=d.event.special[h]||{},p=d.extend({type:h,origType:p,data:r,handler:n,guid:n.guid,selector:i,needsContext:i&&d.expr.match.needsContext.test(i),namespace:f.join(".")},o),(l=a[h])||((l=a[h]=[]).delegateCount=0,c.setup&&!1!==c.setup.call(t,r,f,s))||t.addEventListener&&t.addEventListener(h,s),c.add&&(c.add.call(t,p),p.handler.guid||(p.handler.guid=n.guid)),i?l.splice(l.delegateCount++,0,p):l.push(p),d.event.global[h]=!0)},remove:function(t,e,n,r,i){var o,s,a,u,c,l,h,f,p,g,m,v=M.hasData(t)&&M.get(t);if(v&&(u=v.events)){for(c=(e=(e||"").match(j)||[""]).length;c--;)if(p=m=(a=et.exec(e[c])||[])[1],g=(a[2]||"").split(".").sort(),p){for(h=d.event.special[p]||{},f=u[p=(r?h.delegateType:h.bindType)||p]||[],a=a[2]&&new RegExp("(^|\\.)"+g.join("\\.(?:.*\\.|)")+"(\\.|$)"),s=o=f.length;o--;)l=f[o],!i&&m!==l.origType||n&&n.guid!==l.guid||a&&!a.test(l.namespace)||r&&r!==l.selector&&("**"!==r||!l.selector)||(f.splice(o,1),l.selector&&f.delegateCount--,h.remove&&h.remove.call(t,l));s&&!f.length&&(h.teardown&&!1!==h.teardown.call(t,g,v.handle)||d.removeEvent(t,p,v.handle),delete u[p])}else for(p in u)d.event.remove(t,p+e[c],n,r,!0);d.isEmptyObject(u)&&M.remove(t,"handle events")}},dispatch:function(t){t=d.event.fix(t);var e,n,r,i,s,a=o.call(arguments),u=(M.get(this,"events")||{})[t.type]||[],c=d.event.special[t.type]||{};if((a[0]=t).delegateTarget=this,!c.preDispatch||!1!==c.preDispatch.call(this,t)){for(s=d.event.handlers.call(this,t,u),e=0;(r=s[e++])&&!t.isPropagationStopped();)for(t.currentTarget=r.elem,n=0;(i=r.handlers[n++])&&!t.isImmediatePropagationStopped();)t.rnamespace&&!t.rnamespace.test(i.namespace)||(t.handleObj=i,t.data=i.data,void 0!==(i=((d.event.special[i.origType]||{}).handle||i.handler).apply(r.elem,a))&&!1===(t.result=i)&&(t.preventDefault(),t.stopPropagation()));return c.postDispatch&&c.postDispatch.call(this,t),t.result}},handlers:function(t,e){var n,r,i,o,s=[],a=e.delegateCount,u=t.target;if(a&&u.nodeType&&("click"!==t.type||isNaN(t.button)||t.button<1))for(;u!==this;u=u.parentNode||this)if(1===u.nodeType&&(!0!==u.disabled||"click"!==t.type)){for(r=[],n=0;n<a;n++)void 0===r[i=(o=e[n]).selector+" "]&&(r[i]=o.needsContext?-1<d(i,this).index(u):d.find(i,this,null,[u]).length),r[i]&&r.push(o);r.length&&s.push({elem:u,handlers:r})}return a<e.length&&s.push({elem:this,handlers:e.slice(a)}),s},props:"altKey bubbles cancelable ctrlKey currentTarget detail eventPhase metaKey relatedTarget shiftKey target timeStamp view which".split(" "),fixHooks:{},keyHooks:{props:"char charCode key keyCode".split(" "),filter:function(t,e){return null==t.which&&(t.which=null!=e.charCode?e.charCode:e.keyCode),t}},mouseHooks:{props:"button buttons clientX clientY offsetX offsetY pageX pageY screenX screenY toElement".split(" "),filter:function(t,e){var n,r,o=e.button;return null==t.pageX&&null!=e.clientX&&(n=(r=t.target.ownerDocument||i).documentElement,r=r.body,t.pageX=e.clientX+(n&&n.scrollLeft||r&&r.scrollLeft||0)-(n&&n.clientLeft||r&&r.clientLeft||0),t.pageY=e.clientY+(n&&n.scrollTop||r&&r.scrollTop||0)-(n&&n.clientTop||r&&r.clientTop||0)),t.which||void 0===o||(t.which=1&o?1:2&o?3:4&o?2:0),t}},fix:function(t){if(t[d.expando])return t;var e,n,r,o=t.type,s=t,a=this.fixHooks[o];for(a||(this.fixHooks[o]=a=tt.test(o)?this.mouseHooks:Z.test(o)?this.keyHooks:{}),r=a.props?this.props.concat(a.props):this.props,t=new d.Event(s),e=r.length;e--;)t[n=r[e]]=s[n];return t.target||(t.target=i),3===t.target.nodeType&&(t.target=t.target.parentNode),a.filter?a.filter(t,s):t},special:{load:{noBubble:!0},focus:{trigger:function(){return this!==it()&&this.focus?(this.focus(),!1):void 0},delegateType:"focusin"},blur:{trigger:function(){return this===it()&&this.blur?(this.blur(),!1):void 0},delegateType:"focusout"},click:{trigger:function(){return"checkbox"===this.type&&this.click&&d.nodeName(this,"input")?(this.click(),!1):void 0},_default:function(t){return d.nodeName(t.target,"a")}},beforeunload:{postDispatch:function(t){void 0!==t.result&&t.originalEvent&&(t.originalEvent.returnValue=t.result)}}}},d.removeEvent=function(t,e,n){t.removeEventListener&&t.removeEventListener(e,n)},d.Event=function(t,e){return this instanceof d.Event?(t&&t.type?(this.originalEvent=t,this.type=t.type,this.isDefaultPrevented=t.defaultPrevented||void 0===t.defaultPrevented&&!1===t.returnValue?nt:rt):this.type=t,e&&d.extend(this,e),this.timeStamp=t&&t.timeStamp||d.now(),void(this[d.expando]=!0)):new d.Event(t,e)},d.Event.prototype={constructor:d.Event,isDefaultPrevented:rt,isPropagationStopped:rt,isImmediatePropagationStopped:rt,isSimulated:!1,preventDefault:function(){var t=this.originalEvent;this.isDefaultPrevented=nt,t&&!this.isSimulated&&t.preventDefault()},stopPropagation:function(){var t=this.originalEvent;this.isPropagationStopped=nt,t&&!this.isSimulated&&t.stopPropagation()},stopImmediatePropagation:function(){var t=this.originalEvent;this.isImmediatePropagationStopped=nt,t&&!this.isSimulated&&t.stopImmediatePropagation(),this.stopPropagation()}},d.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},(function(t,e){d.event.special[t]={delegateType:e,bindType:e,handle:function(t){var n,r=t.relatedTarget,i=t.handleObj;return r&&(r===this||d.contains(this,r))||(t.type=i.origType,n=i.handler.apply(this,arguments),t.type=e),n}}})),d.fn.extend({on:function(t,e,n,r){return ot(this,t,e,n,r)},one:function(t,e,n,r){return ot(this,t,e,n,r,1)},off:function(t,e,n){var r,i;if(t&&t.preventDefault&&t.handleObj)r=t.handleObj,d(t.delegateTarget).off(r.namespace?r.origType+"."+r.namespace:r.origType,r.selector,r.handler);else{if("object"!=typeof t)return!1!==e&&"function"!=typeof e||(n=e,e=void 0),!1===n&&(n=rt),this.each((function(){d.event.remove(this,t,n,e)}));for(i in t)this.off(i,e,t[i])}return this}});var st=/<(?!area|br|col|embed|hr|img|input|link|meta|param)(([\w:-]+)[^>]*)\/>/gi,at=/<script|<style|<link/i,ut=/checked\s*(?:[^=]|=\s*.checked.)/i,ct=/^true\/(.*)/,lt=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;function ht(t,e){return d.nodeName(t,"table")&&d.nodeName(11!==e.nodeType?e:e.firstChild,"tr")?t.getElementsByTagName("tbody")[0]||t.appendChild(t.ownerDocument.createElement("tbody")):t}function ft(t){return t.type=(null!==t.getAttribute("type"))+"/"+t.type,t}function pt(t){var e=ct.exec(t.type);return e?t.type=e[1]:t.removeAttribute("type"),t}function dt(t,e){var n,r,i,o,s,a;if(1===e.nodeType){if(M.hasData(t)&&(o=M.access(t),s=M.set(e,o),a=o.events))for(i in delete s.handle,s.events={},a)for(n=0,r=a[i].length;n<r;n++)d.event.add(e,i,a[i][n]);W.hasData(t)&&(o=W.access(t),s=d.extend({},o),W.set(e,s))}}function gt(t,e,n,r){e=s.apply([],e);var i,o,a,u,c,l,h=0,p=t.length,g=p-1,m=e[0],v=d.isFunction(m);if(v||1<p&&"string"==typeof m&&!f.checkClone&&ut.test(m))return t.each((function(i){var o=t.eq(i);v&&(e[0]=m.call(this,i,o.html())),gt(o,e,n,r)}));if(p&&(o=(i=J(e,t[0].ownerDocument,!1,t,r)).firstChild,1===i.childNodes.length&&(i=o),o||r)){for(u=(a=d.map(Y(i,"script"),ft)).length;h<p;h++)c=i,h!==g&&(c=d.clone(c,!0,!0),u)&&d.merge(a,Y(c,"script")),n.call(t[h],c,h);if(u)for(l=a[a.length-1].ownerDocument,d.map(a,pt),h=0;h<u;h++)c=a[h],$.test(c.type||"")&&!M.access(c,"globalEval")&&d.contains(l,c)&&(c.src?d._evalUrl&&d._evalUrl(c.src):d.globalEval(c.textContent.replace(lt,"")))}return t}function mt(t,e,n){for(var r,i=e?d.filter(e,t):t,o=0;null!=(r=i[o]);o++)n||1!==r.nodeType||d.cleanData(Y(r)),r.parentNode&&(n&&d.contains(r.ownerDocument,r)&&K(Y(r,"script")),r.parentNode.removeChild(r));return t}d.extend({htmlPrefilter:function(t){return t.replace(st,"<$1></$2>")},clone:function(t,e,n){var r,i,o,s,a,u,c,l=t.cloneNode(!0),h=d.contains(t.ownerDocument,t);if(!(f.noCloneChecked||1!==t.nodeType&&11!==t.nodeType||d.isXMLDoc(t)))for(s=Y(l),r=0,i=(o=Y(t)).length;r<i;r++)a=o[r],u=s[r],c=void 0,"input"===(c=u.nodeName.toLowerCase())&&G.test(a.type)?u.checked=a.checked:"input"!==c&&"textarea"!==c||(u.defaultValue=a.defaultValue);if(e)if(n)for(o=o||Y(t),s=s||Y(l),r=0,i=o.length;r<i;r++)dt(o[r],s[r]);else dt(t,l);return 0<(s=Y(l,"script")).length&&K(s,!h&&Y(t,"script")),l},cleanData:function(t){for(var e,n,r,i=d.event.special,o=0;void 0!==(n=t[o]);o++)if(D(n)){if(e=n[M.expando]){if(e.events)for(r in e.events)i[r]?d.event.remove(n,r):d.removeEvent(n,r,e.handle);n[M.expando]=void 0}n[W.expando]&&(n[W.expando]=void 0)}}}),d.fn.extend({domManip:gt,detach:function(t){return mt(this,t,!0)},remove:function(t){return mt(this,t)},text:function(t){return N(this,(function(t){return void 0===t?d.text(this):this.empty().each((function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=t)}))}),null,t,arguments.length)},append:function(){return gt(this,arguments,(function(t){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||ht(this,t).appendChild(t)}))},prepend:function(){return gt(this,arguments,(function(t){var e;1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(e=ht(this,t)).insertBefore(t,e.firstChild)}))},before:function(){return gt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this)}))},after:function(){return gt(this,arguments,(function(t){this.parentNode&&this.parentNode.insertBefore(t,this.nextSibling)}))},empty:function(){for(var t,e=0;null!=(t=this[e]);e++)1===t.nodeType&&(d.cleanData(Y(t,!1)),t.textContent="");return this},clone:function(t,e){return t=null!=t&&t,e=null==e?t:e,this.map((function(){return d.clone(this,t,e)}))},html:function(t){return N(this,(function(t){var e=this[0]||{},n=0,r=this.length;if(void 0===t&&1===e.nodeType)return e.innerHTML;if("string"==typeof t&&!at.test(t)&&!X[(V.exec(t)||["",""])[1].toLowerCase()]){t=d.htmlPrefilter(t);try{for(;n<r;n++)1===(e=this[n]||{}).nodeType&&(d.cleanData(Y(e,!1)),e.innerHTML=t);e=0}catch(t){}}e&&this.empty().append(t)}),null,t,arguments.length)},replaceWith:function(){var t=[];return gt(this,arguments,(function(e){var n=this.parentNode;d.inArray(this,t)<0&&(d.cleanData(Y(this)),n)&&n.replaceChild(e,this)}),t)}}),d.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},(function(t,e){d.fn[t]=function(t){for(var n,r=[],i=d(t),o=i.length-1,s=0;s<=o;s++)n=s===o?this:this.clone(!0),d(i[s])[e](n),a.apply(r,n.get());return this.pushStack(r)}}));var vt,yt={HTML:"block",BODY:"block"};function bt(t,e){return t=d(e.createElement(t)).appendTo(e.body),e=d.css(t[0],"display"),t.detach(),e}function wt(t){var e=i,n=yt[t];return n||("none"!==(n=bt(t,e))&&n||((e=(vt=(vt||d("<iframe frameborder='0' width='0' height='0'/>")).appendTo(e.documentElement))[0].contentDocument).write(),e.close(),n=bt(t,e),vt.detach()),yt[t]=n),n}function _t(e){var n=e.ownerDocument.defaultView;return(n=n&&n.opener?n:t).getComputedStyle(e)}function xt(t,e,n,r){var i,o={};for(i in e)o[i]=t.style[i],t.style[i]=e[i];for(i in n=n.apply(t,r||[]),e)t.style[i]=o[i];return n}var Ct,kt,St,Ot,Et,Tt,At=/^margin/,It=new RegExp("^("+r+")(?!px)[a-z%]+$","i"),jt=i.documentElement;function Pt(){Tt.style.cssText="-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;position:relative;display:block;margin:auto;border:1px;padding:1px;top:1%;width:50%",Tt.innerHTML="",jt.appendChild(Et);var e=t.getComputedStyle(Tt);Ct="1%"!==e.top,Ot="2px"===e.marginLeft,kt="4px"===e.width,Tt.style.marginRight="50%",St="4px"===e.marginRight,jt.removeChild(Et)}function Nt(t,e,n){var r,i,o=t.style;return""!==(i=(n=n||_t(t))?n.getPropertyValue(e)||n[e]:void 0)&&void 0!==i||d.contains(t.ownerDocument,t)||(i=d.style(t,e)),n&&!f.pixelMarginRight()&&It.test(i)&&At.test(e)&&(t=o.width,e=o.minWidth,r=o.maxWidth,o.minWidth=o.maxWidth=o.width=i,i=n.width,o.width=t,o.minWidth=e,o.maxWidth=r),void 0!==i?i+"":i}function Dt(t,e){return{get:function(){return t()?void delete this.get:(this.get=e).apply(this,arguments)}}}Et=i.createElement("div"),(Tt=i.createElement("div")).style&&(Tt.style.backgroundClip="content-box",Tt.cloneNode(!0).style.backgroundClip="",f.clearCloneStyle="content-box"===Tt.style.backgroundClip,Et.style.cssText="border:0;width:8px;height:0;top:0;left:-9999px;padding:0;margin-top:1px;position:absolute",Et.appendChild(Tt),d.extend(f,{pixelPosition:function(){return Pt(),Ct},boxSizingReliable:function(){return null==kt&&Pt(),kt},pixelMarginRight:function(){return null==kt&&Pt(),St},reliableMarginLeft:function(){return null==kt&&Pt(),Ot},reliableMarginRight:function(){var e,n=Tt.appendChild(i.createElement("div"));return n.style.cssText=Tt.style.cssText="-webkit-box-sizing:content-box;box-sizing:content-box;display:block;margin:0;border:0;padding:0",n.style.marginRight=n.style.width="0",Tt.style.width="1px",jt.appendChild(Et),e=!parseFloat(t.getComputedStyle(n).marginRight),jt.removeChild(Et),Tt.removeChild(n),e}}));var Rt=/^(none|table(?!-c[ea]).+)/,Mt={position:"absolute",visibility:"hidden",display:"block"},Wt={letterSpacing:"0",fontWeight:"400"},Lt=["Webkit","O","Moz","ms"],Ft=i.createElement("div").style;function Ht(t){if(t in Ft)return t;for(var e=t[0].toUpperCase()+t.slice(1),n=Lt.length;n--;)if((t=Lt[n]+e)in Ft)return t}function Bt(t,e,n){var r=q.exec(e);return r?Math.max(0,r[2]-(n||0))+(r[3]||"px"):e}function qt(t,e,n,r,i){for(var o=n===(r?"border":"content")?4:"width"===e?1:0,s=0;o<4;o+=2)"margin"===n&&(s+=d.css(t,n+z[o],!0,i)),r?("content"===n&&(s-=d.css(t,"padding"+z[o],!0,i)),"margin"!==n&&(s-=d.css(t,"border"+z[o]+"Width",!0,i))):(s+=d.css(t,"padding"+z[o],!0,i),"padding"!==n&&(s+=d.css(t,"border"+z[o]+"Width",!0,i)));return s}function zt(t,e,n){var r=!0,i="width"===e?t.offsetWidth:t.offsetHeight,o=_t(t),s="border-box"===d.css(t,"boxSizing",!1,o);if(i<=0||null==i){if(((i=Nt(t,e,o))<0||null==i)&&(i=t.style[e]),It.test(i))return i;r=s&&(f.boxSizingReliable()||i===t.style[e]),i=parseFloat(i)||0}return i+qt(t,e,n||(s?"border":"content"),r,o)+"px"}function Ut(t,e){for(var n,r,i,o=[],s=0,a=t.length;s<a;s++)(r=t[s]).style&&(o[s]=M.get(r,"olddisplay"),n=r.style.display,e?(o[s]||"none"!==n||(r.style.display=""),""===r.style.display&&B(r)&&(o[s]=M.access(r,"olddisplay",wt(r.nodeName)))):(i=B(r),"none"===n&&i||M.set(r,"olddisplay",i?n:d.css(r,"display"))));for(s=0;s<a;s++)!(r=t[s]).style||e&&"none"!==r.style.display&&""!==r.style.display||(r.style.display=e?o[s]||"":"none");return t}function Gt(t,e,n,r,i){return new Gt.prototype.init(t,e,n,r,i)}d.extend({cssHooks:{opacity:{get:function(t,e){if(e)return""===(e=Nt(t,"opacity"))?"1":e}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{float:"cssFloat"},style:function(t,e,n,r){var i,o,s,a,u;if(t&&3!==t.nodeType&&8!==t.nodeType&&t.style)return a=d.camelCase(e),u=t.style,e=d.cssProps[a]||(d.cssProps[a]=Ht(a)||a),s=d.cssHooks[e]||d.cssHooks[a],void 0===n?s&&"get"in s&&void 0!==(i=s.get(t,!1,r))?i:u[e]:("string"===(o=typeof n)&&(i=q.exec(n))&&i[1]&&(n=U(t,e,i),o="number"),void(null!=n&&n==n&&("number"===o&&(n+=i&&i[3]||(d.cssNumber[a]?"":"px")),f.clearCloneStyle||""!==n||0!==e.indexOf("background")||(u[e]="inherit"),s&&"set"in s&&void 0===(n=s.set(t,n,r))||(u[e]=n))))},css:function(t,e,n,r){var i,o=d.camelCase(e);return e=d.cssProps[o]||(d.cssProps[o]=Ht(o)||o),"normal"===(i=void 0===(i=(o=d.cssHooks[e]||d.cssHooks[o])&&"get"in o?o.get(t,!0,n):i)?Nt(t,e,r):i)&&e in Wt&&(i=Wt[e]),(""===n||n)&&(o=parseFloat(i),!0===n||isFinite(o))?o||0:i}}),d.each(["height","width"],(function(t,e){d.cssHooks[e]={get:function(t,n,r){return n?Rt.test(d.css(t,"display"))&&0===t.offsetWidth?xt(t,Mt,(function(){return zt(t,e,r)})):zt(t,e,r):void 0},set:function(t,n,r){var i=r&&_t(t);r=r&&qt(t,e,r,"border-box"===d.css(t,"boxSizing",!1,i),i);return r&&(i=q.exec(n))&&"px"!==(i[3]||"px")&&(t.style[e]=n,n=d.css(t,e)),Bt(0,n,r)}}})),d.cssHooks.marginLeft=Dt(f.reliableMarginLeft,(function(t,e){return e?(parseFloat(Nt(t,"marginLeft"))||t.getBoundingClientRect().left-xt(t,{marginLeft:0},(function(){return t.getBoundingClientRect().left})))+"px":void 0})),d.cssHooks.marginRight=Dt(f.reliableMarginRight,(function(t,e){return e?xt(t,{display:"inline-block"},Nt,[t,"marginRight"]):void 0})),d.each({margin:"",padding:"",border:"Width"},(function(t,e){d.cssHooks[t+e]={expand:function(n){for(var r=0,i={},o="string"==typeof n?n.split(" "):[n];r<4;r++)i[t+z[r]+e]=o[r]||o[r-2]||o[0];return i}},At.test(t)||(d.cssHooks[t+e].set=Bt)})),d.fn.extend({css:function(t,e){return N(this,(function(t,e,n){var r,i,o={},s=0;if(d.isArray(e)){for(r=_t(t),i=e.length;s<i;s++)o[e[s]]=d.css(t,e[s],!1,r);return o}return void 0!==n?d.style(t,e,n):d.css(t,e)}),t,e,1<arguments.length)},show:function(){return Ut(this,!0)},hide:function(){return Ut(this)},toggle:function(t){return"boolean"==typeof t?t?this.show():this.hide():this.each((function(){B(this)?d(this).show():d(this).hide()}))}}),((d.Tween=Gt).prototype={constructor:Gt,init:function(t,e,n,r,i,o){this.elem=t,this.prop=n,this.easing=i||d.easing._default,this.options=e,this.start=this.now=this.cur(),this.end=r,this.unit=o||(d.cssNumber[n]?"":"px")},cur:function(){var t=Gt.propHooks[this.prop];return(t&&t.get?t:Gt.propHooks._default).get(this)},run:function(t){var e,n=Gt.propHooks[this.prop];return this.options.duration?this.pos=e=d.easing[this.easing](t,this.options.duration*t,0,1,this.options.duration):this.pos=e=t,this.now=(this.end-this.start)*e+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),(n&&n.set?n:Gt.propHooks._default).set(this),this}}).init.prototype=Gt.prototype,(Gt.propHooks={_default:{get:function(t){return 1!==t.elem.nodeType||null!=t.elem[t.prop]&&null==t.elem.style[t.prop]?t.elem[t.prop]:(t=d.css(t.elem,t.prop,""))&&"auto"!==t?t:0},set:function(t){d.fx.step[t.prop]?d.fx.step[t.prop](t):1!==t.elem.nodeType||null==t.elem.style[d.cssProps[t.prop]]&&!d.cssHooks[t.prop]?t.elem[t.prop]=t.now:d.style(t.elem,t.prop,t.now+t.unit)}}}).scrollTop=Gt.propHooks.scrollLeft={set:function(t){t.elem.nodeType&&t.elem.parentNode&&(t.elem[t.prop]=t.now)}},d.easing={linear:function(t){return t},swing:function(t){return.5-Math.cos(t*Math.PI)/2},_default:"swing"},d.fx=Gt.prototype.init,d.fx.step={};var Vt,$t,Xt,Yt=/^(?:toggle|show|hide)$/,Kt=/queueHooks$/;function Qt(){return t.setTimeout((function(){Vt=void 0})),Vt=d.now()}function Jt(t,e){var n,r=0,i={height:t};for(e=e?1:0;r<4;r+=2-e)i["margin"+(n=z[r])]=i["padding"+n]=t;return e&&(i.opacity=i.width=t),i}function Zt(t,e,n){for(var r,i=(te.tweeners[e]||[]).concat(te.tweeners["*"]),o=0,s=i.length;o<s;o++)if(r=i[o].call(n,e,t))return r}function te(t,e,n){var r,i,o,s,a,u,c,l=0,h=te.prefilters.length,f=d.Deferred().always((function(){delete p.elem})),p=function(){if(i)return!1;for(var e=Vt||Qt(),n=(e=Math.max(0,g.startTime+g.duration-e),1-(e/g.duration||0)),r=0,o=g.tweens.length;r<o;r++)g.tweens[r].run(n);return f.notifyWith(t,[g,n,e]),n<1&&o?e:(f.resolveWith(t,[g]),!1)},g=f.promise({elem:t,props:d.extend({},e),opts:d.extend(!0,{specialEasing:{},easing:d.easing._default},n),originalProperties:e,originalOptions:n,startTime:Vt||Qt(),duration:n.duration,tweens:[],createTween:function(e,n){return n=d.Tween(t,g.opts,e,n,g.opts.specialEasing[e]||g.opts.easing),g.tweens.push(n),n},stop:function(e){var n=0,r=e?g.tweens.length:0;if(!i){for(i=!0;n<r;n++)g.tweens[n].run(1);e?(f.notifyWith(t,[g,1,0]),f.resolveWith(t,[g,e])):f.rejectWith(t,[g,e])}return this}}),m=g.props,v=m,y=g.opts.specialEasing;for(o in v)if(s=d.camelCase(o),a=y[s],u=v[o],d.isArray(u)&&(a=u[1],u=v[o]=u[0]),o!==s&&(v[s]=u,delete v[o]),c=d.cssHooks[s],c&&"expand"in c)for(o in u=c.expand(u),delete v[s],u)o in v||(v[o]=u[o],y[o]=a);else y[s]=a;for(;l<h;l++)if(r=te.prefilters[l].call(g,t,m,g.opts))return d.isFunction(r.stop)&&(d._queueHooks(g.elem,g.opts.queue).stop=d.proxy(r.stop,r)),r;return d.map(m,Zt,g),d.isFunction(g.opts.start)&&g.opts.start.call(t,g),d.fx.timer(d.extend(p,{elem:t,anim:g,queue:g.opts.queue})),g.progress(g.opts.progress).done(g.opts.done,g.opts.complete).fail(g.opts.fail).always(g.opts.always)}d.Animation=d.extend(te,{tweeners:{"*":[function(t,e){var n=this.createTween(t,e);return U(n.elem,t,q.exec(e),n),n}]},tweener:function(t,e){for(var n,r=0,i=(t=d.isFunction(t)?(e=t,["*"]):t.match(j)).length;r<i;r++)n=t[r],te.tweeners[n]=te.tweeners[n]||[],te.tweeners[n].unshift(e)},prefilters:[function(t,e,n){var r,i,o,s,a,u,c,l=this,h={},f=t.style,p=t.nodeType&&B(t),g=M.get(t,"fxshow");for(r in n.queue||(null==(a=d._queueHooks(t,"fx")).unqueued&&(a.unqueued=0,u=a.empty.fire,a.empty.fire=function(){a.unqueued||u()}),a.unqueued++,l.always((function(){l.always((function(){a.unqueued--,d.queue(t,"fx").length||a.empty.fire()}))}))),1===t.nodeType&&("height"in e||"width"in e)&&(n.overflow=[f.overflow,f.overflowX,f.overflowY],"inline"===("none"===(c=d.css(t,"display"))?M.get(t,"olddisplay")||wt(t.nodeName):c))&&"none"===d.css(t,"float")&&(f.display="inline-block"),n.overflow&&(f.overflow="hidden",l.always((function(){f.overflow=n.overflow[0],f.overflowX=n.overflow[1],f.overflowY=n.overflow[2]}))),e)if(i=e[r],Yt.exec(i)){if(delete e[r],o=o||"toggle"===i,i===(p?"hide":"show")){if("show"!==i||!g||void 0===g[r])continue;p=!0}h[r]=g&&g[r]||d.style(t,r)}else c=void 0;if(d.isEmptyObject(h))"inline"===("none"===c?wt(t.nodeName):c)&&(f.display=c);else for(r in g?"hidden"in g&&(p=g.hidden):g=M.access(t,"fxshow",{}),o&&(g.hidden=!p),p?d(t).show():l.done((function(){d(t).hide()})),l.done((function(){for(var e in M.remove(t,"fxshow"),h)d.style(t,e,h[e])})),h)s=Zt(p?g[r]:0,r,l),r in g||(g[r]=s.start,p&&(s.end=s.start,s.start="width"===r||"height"===r?1:0))}],prefilter:function(t,e){e?te.prefilters.unshift(t):te.prefilters.push(t)}}),d.speed=function(t,e,n){var r=t&&"object"==typeof t?d.extend({},t):{complete:n||!n&&e||d.isFunction(t)&&t,duration:t,easing:n&&e||e&&!d.isFunction(e)&&e};return r.duration=d.fx.off?0:"number"==typeof r.duration?r.duration:r.duration in d.fx.speeds?d.fx.speeds[r.duration]:d.fx.speeds._default,null!=r.queue&&!0!==r.queue||(r.queue="fx"),r.old=r.complete,r.complete=function(){d.isFunction(r.old)&&r.old.call(this),r.queue&&d.dequeue(this,r.queue)},r},d.fn.extend({fadeTo:function(t,e,n,r){return this.filter(B).css("opacity",0).show().end().animate({opacity:e},t,n,r)},animate:function(t,e,n,r){function i(){var e=te(this,d.extend({},t),s);(o||M.get(this,"finish"))&&e.stop(!0)}var o=d.isEmptyObject(t),s=d.speed(e,n,r);return i.finish=i,o||!1===s.queue?this.each(i):this.queue(s.queue,i)},stop:function(t,e,n){function r(t){var e=t.stop;delete t.stop,e(n)}return"string"!=typeof t&&(n=e,e=t,t=void 0),e&&!1!==t&&this.queue(t||"fx",[]),this.each((function(){var e=!0,i=null!=t&&t+"queueHooks",o=d.timers,s=M.get(this);if(i)s[i]&&s[i].stop&&r(s[i]);else for(i in s)s[i]&&s[i].stop&&Kt.test(i)&&r(s[i]);for(i=o.length;i--;)o[i].elem!==this||null!=t&&o[i].queue!==t||(o[i].anim.stop(n),e=!1,o.splice(i,1));!e&&n||d.dequeue(this,t)}))},finish:function(t){return!1!==t&&(t=t||"fx"),this.each((function(){var e,n=M.get(this),r=n[t+"queue"],i=n[t+"queueHooks"],o=d.timers,s=r?r.length:0;for(n.finish=!0,d.queue(this,t,[]),i&&i.stop&&i.stop.call(this,!0),e=o.length;e--;)o[e].elem===this&&o[e].queue===t&&(o[e].anim.stop(!0),o.splice(e,1));for(e=0;e<s;e++)r[e]&&r[e].finish&&r[e].finish.call(this);delete n.finish}))}}),d.each(["toggle","show","hide"],(function(t,e){var n=d.fn[e];d.fn[e]=function(t,r,i){return null==t||"boolean"==typeof t?n.apply(this,arguments):this.animate(Jt(e,!0),t,r,i)}})),d.each({slideDown:Jt("show"),slideUp:Jt("hide"),slideToggle:Jt("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},(function(t,e){d.fn[t]=function(t,n,r){return this.animate(e,t,n,r)}})),d.timers=[],d.fx.tick=function(){var t,e=0,n=d.timers;for(Vt=d.now();e<n.length;e++)(t=n[e])()||n[e]!==t||n.splice(e--,1);n.length||d.fx.stop(),Vt=void 0},d.fx.timer=function(t){d.timers.push(t),t()?d.fx.start():d.timers.pop()},d.fx.interval=13,d.fx.start=function(){$t=$t||t.setInterval(d.fx.tick,d.fx.interval)},d.fx.stop=function(){t.clearInterval($t),$t=null},d.fx.speeds={slow:600,fast:200,_default:400},d.fn.delay=function(e,n){return e=d.fx&&d.fx.speeds[e]||e,this.queue(n=n||"fx",(function(n,r){var i=t.setTimeout(n,e);r.stop=function(){t.clearTimeout(i)}}))},Xt=i.createElement("input"),p=i.createElement("select"),r=p.appendChild(i.createElement("option")),Xt.type="checkbox",f.checkOn=""!==Xt.value,f.optSelected=r.selected,p.disabled=!0,f.optDisabled=!r.disabled,(Xt=i.createElement("input")).value="t",Xt.type="radio",f.radioValue="t"===Xt.value;var ee,ne=d.expr.attrHandle,re=(d.fn.extend({attr:function(t,e){return N(this,d.attr,t,e,1<arguments.length)},removeAttr:function(t){return this.each((function(){d.removeAttr(this,t)}))}}),d.extend({attr:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return void 0===t.getAttribute?d.prop(t,e,n):(1===o&&d.isXMLDoc(t)||(e=e.toLowerCase(),i=d.attrHooks[e]||(d.expr.match.bool.test(e)?ee:void 0)),void 0!==n?null===n?void d.removeAttr(t,e):i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:(t.setAttribute(e,n+""),n):i&&"get"in i&&null!==(r=i.get(t,e))||null!=(r=d.find.attr(t,e))?r:void 0)},attrHooks:{type:{set:function(t,e){var n;if(!f.radioValue&&"radio"===e&&d.nodeName(t,"input"))return n=t.value,t.setAttribute("type",e),n&&(t.value=n),e}}},removeAttr:function(t,e){var n,r,i=0,o=e&&e.match(j);if(o&&1===t.nodeType)for(;n=o[i++];)r=d.propFix[n]||n,d.expr.match.bool.test(n)&&(t[r]=!1),t.removeAttribute(n)}}),ee={set:function(t,e,n){return!1===e?d.removeAttr(t,n):t.setAttribute(n,n),n}},d.each(d.expr.match.bool.source.match(/\w+/g),(function(t,e){var n=ne[e]||d.find.attr;ne[e]=function(t,e,r){var i,o;return r||(o=ne[e],ne[e]=i,i=null!=n(t,e,r)?e.toLowerCase():null,ne[e]=o),i}})),/^(?:input|select|textarea|button)$/i),ie=/^(?:a|area)$/i,oe=(d.fn.extend({prop:function(t,e){return N(this,d.prop,t,e,1<arguments.length)},removeProp:function(t){return this.each((function(){delete this[d.propFix[t]||t]}))}}),d.extend({prop:function(t,e,n){var r,i,o=t.nodeType;if(3!==o&&8!==o&&2!==o)return 1===o&&d.isXMLDoc(t)||(e=d.propFix[e]||e,i=d.propHooks[e]),void 0!==n?i&&"set"in i&&void 0!==(r=i.set(t,n,e))?r:t[e]=n:i&&"get"in i&&null!==(r=i.get(t,e))?r:t[e]},propHooks:{tabIndex:{get:function(t){var e=d.find.attr(t,"tabindex");return e?parseInt(e,10):re.test(t.nodeName)||ie.test(t.nodeName)&&t.href?0:-1}}},propFix:{for:"htmlFor",class:"className"}}),f.optSelected||(d.propHooks.selected={get:function(t){return t=t.parentNode,t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(t){t=t.parentNode,t&&(t.selectedIndex,t.parentNode)&&t.parentNode.selectedIndex}}),d.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],(function(){d.propFix[this.toLowerCase()]=this})),/[\t\r\n\f]/g);function se(t){return t.getAttribute&&t.getAttribute("class")||""}d.fn.extend({addClass:function(t){var e,n,r,i,o,s,a=0;if(d.isFunction(t))return this.each((function(e){d(this).addClass(t.call(this,e,se(this)))}));if("string"==typeof t&&t)for(e=t.match(j)||[];n=this[a++];)if(s=se(n),r=1===n.nodeType&&(" "+s+" ").replace(oe," ")){for(o=0;i=e[o++];)r.indexOf(" "+i+" ")<0&&(r+=i+" ");s!==(s=d.trim(r))&&n.setAttribute("class",s)}return this},removeClass:function(t){var e,n,r,i,o,s,a=0;if(d.isFunction(t))return this.each((function(e){d(this).removeClass(t.call(this,e,se(this)))}));if(!arguments.length)return this.attr("class","");if("string"==typeof t&&t)for(e=t.match(j)||[];n=this[a++];)if(s=se(n),r=1===n.nodeType&&(" "+s+" ").replace(oe," ")){for(o=0;i=e[o++];)for(;-1<r.indexOf(" "+i+" ");)r=r.replace(" "+i+" "," ");s!==(s=d.trim(r))&&n.setAttribute("class",s)}return this},toggleClass:function(t,e){var n=typeof t;return"boolean"==typeof e&&"string"==n?e?this.addClass(t):this.removeClass(t):d.isFunction(t)?this.each((function(n){d(this).toggleClass(t.call(this,n,se(this),e),e)})):this.each((function(){var e,r,i,o;if("string"==n)for(r=0,i=d(this),o=t.match(j)||[];e=o[r++];)i.hasClass(e)?i.removeClass(e):i.addClass(e);else void 0!==t&&"boolean"!=n||((e=se(this))&&M.set(this,"__className__",e),this.setAttribute&&this.setAttribute("class",!e&&!1!==t&&M.get(this,"__className__")||""))}))},hasClass:function(t){for(var e,n=0,r=" "+t+" ";e=this[n++];)if(1===e.nodeType&&-1<(" "+se(e)+" ").replace(oe," ").indexOf(r))return!0;return!1}});var ae=/\r/g,ue=/[\x20\t\r\n\f]+/g,ce=(d.fn.extend({val:function(t){var e,n,r,i=this[0];return arguments.length?(r=d.isFunction(t),this.each((function(n){1===this.nodeType&&(null==(n=r?t.call(this,n,d(this).val()):t)?n="":"number"==typeof n?n+="":d.isArray(n)&&(n=d.map(n,(function(t){return null==t?"":t+""}))),(e=d.valHooks[this.type]||d.valHooks[this.nodeName.toLowerCase()])&&"set"in e&&void 0!==e.set(this,n,"value")||(this.value=n))}))):i?(e=d.valHooks[i.type]||d.valHooks[i.nodeName.toLowerCase()])&&"get"in e&&void 0!==(n=e.get(i,"value"))?n:"string"==typeof(n=i.value)?n.replace(ae,""):null==n?"":n:void 0}}),d.extend({valHooks:{option:{get:function(t){var e=d.find.attr(t,"value");return null!=e?e:d.trim(d.text(t)).replace(ue," ")}},select:{get:function(t){for(var e,n=t.options,r=t.selectedIndex,i="select-one"===t.type||r<0,o=i?null:[],s=i?r+1:n.length,a=r<0?s:i?r:0;a<s;a++)if(((e=n[a]).selected||a===r)&&(f.optDisabled?!e.disabled:null===e.getAttribute("disabled"))&&(!e.parentNode.disabled||!d.nodeName(e.parentNode,"optgroup"))){if(e=d(e).val(),i)return e;o.push(e)}return o},set:function(t,e){for(var n,r,i=t.options,o=d.makeArray(e),s=i.length;s--;)((r=i[s]).selected=-1<d.inArray(d.valHooks.option.get(r),o))&&(n=!0);return n||(t.selectedIndex=-1),o}}}}),d.each(["radio","checkbox"],(function(){d.valHooks[this]={set:function(t,e){return d.isArray(e)?t.checked=-1<d.inArray(d(t).val(),e):void 0}},f.checkOn||(d.valHooks[this].get=function(t){return null===t.getAttribute("value")?"on":t.value})})),/^(?:focusinfocus|focusoutblur)$/),le=(d.extend(d.event,{trigger:function(e,n,r,o){var s,a,u,c,l,f,p=[r||i],g=h.call(e,"type")?e.type:e,m=h.call(e,"namespace")?e.namespace.split("."):[],v=a=r=r||i;if(3!==r.nodeType&&8!==r.nodeType&&!ce.test(g+d.event.triggered)&&(-1<g.indexOf(".")&&(g=(m=g.split(".")).shift(),m.sort()),c=g.indexOf(":")<0&&"on"+g,(e=e[d.expando]?e:new d.Event(g,"object"==typeof e&&e)).isTrigger=o?2:3,e.namespace=m.join("."),e.rnamespace=e.namespace?new RegExp("(^|\\.)"+m.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,e.result=void 0,e.target||(e.target=r),n=null==n?[e]:d.makeArray(n,[e]),f=d.event.special[g]||{},o||!f.trigger||!1!==f.trigger.apply(r,n))){if(!o&&!f.noBubble&&!d.isWindow(r)){for(u=f.delegateType||g,ce.test(u+g)||(v=v.parentNode);v;v=v.parentNode)p.push(v),a=v;a===(r.ownerDocument||i)&&p.push(a.defaultView||a.parentWindow||t)}for(s=0;(v=p[s++])&&!e.isPropagationStopped();)e.type=1<s?u:f.bindType||g,(l=(M.get(v,"events")||{})[e.type]&&M.get(v,"handle"))&&l.apply(v,n),(l=c&&v[c])&&l.apply&&D(v)&&(e.result=l.apply(v,n),!1===e.result)&&e.preventDefault();return e.type=g,o||e.isDefaultPrevented()||f._default&&!1!==f._default.apply(p.pop(),n)||!D(r)||c&&d.isFunction(r[g])&&!d.isWindow(r)&&((a=r[c])&&(r[c]=null),r[d.event.triggered=g](),d.event.triggered=void 0,a)&&(r[c]=a),e.result}},simulate:function(t,e,n){n=d.extend(new d.Event,n,{type:t,isSimulated:!0}),d.event.trigger(n,null,e)}}),d.fn.extend({trigger:function(t,e){return this.each((function(){d.event.trigger(t,e,this)}))},triggerHandler:function(t,e){var n=this[0];return n?d.event.trigger(t,e,n,!0):void 0}}),d.each("blur focus focusin focusout load resize scroll unload click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup error contextmenu".split(" "),(function(t,e){d.fn[e]=function(t,n){return 0<arguments.length?this.on(e,null,t,n):this.trigger(e)}})),d.fn.extend({hover:function(t,e){return this.mouseenter(t).mouseleave(e||t)}}),f.focusin="onfocusin"in t,f.focusin||d.each({focus:"focusin",blur:"focusout"},(function(t,e){function n(t){d.event.simulate(e,t.target,d.event.fix(t))}d.event.special[e]={setup:function(){var r=this.ownerDocument||this,i=M.access(r,e);i||r.addEventListener(t,n,!0),M.access(r,e,(i||0)+1)},teardown:function(){var r=this.ownerDocument||this,i=M.access(r,e)-1;i?M.access(r,e,i):(r.removeEventListener(t,n,!0),M.remove(r,e))}}})),t.location),he=d.now(),fe=/\?/,pe=(d.parseJSON=function(t){return JSON.parse(t+"")},d.parseXML=function(e){var n;if(!e||"string"!=typeof e)return null;try{n=(new t.DOMParser).parseFromString(e,"text/xml")}catch(e){n=void 0}return n&&!n.getElementsByTagName("parsererror").length||d.error("Invalid XML: "+e),n},/#.*$/),de=/([?&])_=[^&]*/,ge=/^(.*?):[ \t]*([^\r\n]*)$/gm,me=/^(?:GET|HEAD)$/,ve=/^\/\//,ye={},be={},we="*/".concat("*"),_e=i.createElement("a");function xe(t){return function(e,n){"string"!=typeof e&&(n=e,e="*");var r,i=0,o=e.toLowerCase().match(j)||[];if(d.isFunction(n))for(;r=o[i++];)"+"===r[0]?(r=r.slice(1)||"*",(t[r]=t[r]||[]).unshift(n)):(t[r]=t[r]||[]).push(n)}}function Ce(t,e,n,r){var i={},o=t===be;function s(a){var u;return i[a]=!0,d.each(t[a]||[],(function(t,a){return a=a(e,n,r),"string"!=typeof a||o||i[a]?o?!(u=a):void 0:(e.dataTypes.unshift(a),s(a),!1)})),u}return s(e.dataTypes[0])||!i["*"]&&s("*")}function ke(t,e){var n,r,i=d.ajaxSettings.flatOptions||{};for(n in e)void 0!==e[n]&&((i[n]?t:r=r||{})[n]=e[n]);return r&&d.extend(!0,t,r),t}_e.href=le.href,d.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:le.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(le.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":we,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":d.parseJSON,"text xml":d.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(t,e){return e?ke(ke(t,d.ajaxSettings),e):ke(d.ajaxSettings,t)},ajaxPrefilter:xe(ye),ajaxTransport:xe(be),ajax:function(e,n){"object"==typeof e&&(n=e,e=void 0);var r,o,s,a,u,c,l,h=d.ajaxSetup({},n=n||{}),f=h.context||h,p=h.context&&(f.nodeType||f.jquery)?d(f):d.event,g=d.Deferred(),m=d.Callbacks("once memory"),v=h.statusCode||{},y={},b={},w=0,_="canceled",x={readyState:0,getResponseHeader:function(t){var e;if(2===w){if(!a)for(a={};e=ge.exec(s);)a[e[1].toLowerCase()]=e[2];e=a[t.toLowerCase()]}return null==e?null:e},getAllResponseHeaders:function(){return 2===w?s:null},setRequestHeader:function(t,e){var n=t.toLowerCase();return w||(t=b[n]=b[n]||t,y[t]=e),this},overrideMimeType:function(t){return w||(h.mimeType=t),this},statusCode:function(t){if(t)if(w<2)for(var e in t)v[e]=[v[e],t[e]];else x.always(t[x.status]);return this},abort:function(t){return t=t||_,r&&r.abort(t),C(0,t),this}};if(g.promise(x).complete=m.add,x.success=x.done,x.error=x.fail,h.url=((e||h.url||le.href)+"").replace(pe,"").replace(ve,le.protocol+"//"),h.type=n.method||n.type||h.method||h.type,h.dataTypes=d.trim(h.dataType||"*").toLowerCase().match(j)||[""],null==h.crossDomain){e=i.createElement("a");try{e.href=h.url,e.href=e.href,h.crossDomain=_e.protocol+"//"+_e.host!=e.protocol+"//"+e.host}catch(e){h.crossDomain=!0}}if(h.data&&h.processData&&"string"!=typeof h.data&&(h.data=d.param(h.data,h.traditional)),Ce(ye,h,n,x),2!==w){for(l in(c=d.event&&h.global)&&0==d.active++&&d.event.trigger("ajaxStart"),h.type=h.type.toUpperCase(),h.hasContent=!me.test(h.type),o=h.url,h.hasContent||(h.data&&(o=h.url+=(fe.test(o)?"&":"?")+h.data,delete h.data),!1===h.cache&&(h.url=de.test(o)?o.replace(de,"$1_="+he++):o+(fe.test(o)?"&":"?")+"_="+he++)),h.ifModified&&(d.lastModified[o]&&x.setRequestHeader("If-Modified-Since",d.lastModified[o]),d.etag[o])&&x.setRequestHeader("If-None-Match",d.etag[o]),(h.data&&h.hasContent&&!1!==h.contentType||n.contentType)&&x.setRequestHeader("Content-Type",h.contentType),x.setRequestHeader("Accept",h.dataTypes[0]&&h.accepts[h.dataTypes[0]]?h.accepts[h.dataTypes[0]]+("*"!==h.dataTypes[0]?", "+we+"; q=0.01":""):h.accepts["*"]),h.headers)x.setRequestHeader(l,h.headers[l]);if(h.beforeSend&&(!1===h.beforeSend.call(f,x,h)||2===w))return x.abort();for(l in _="abort",{success:1,error:1,complete:1})x[l](h[l]);if(r=Ce(be,h,n,x)){if(x.readyState=1,c&&p.trigger("ajaxSend",[x,h]),2===w)return x;h.async&&0<h.timeout&&(u=t.setTimeout((function(){x.abort("timeout")}),h.timeout));try{w=1,r.send(y,C)}catch(e){if(!(w<2))throw e;C(-1,e)}}else C(-1,"No Transport")}return x;function C(e,n,i,a){var l,y,b,_=n;2!==w&&(w=2,u&&t.clearTimeout(u),r=void 0,s=a||"",x.readyState=0<e?4:0,a=200<=e&&e<300||304===e,i&&(b=function(t,e,n){for(var r,i,o,s,a=t.contents,u=t.dataTypes;"*"===u[0];)u.shift(),void 0===r&&(r=t.mimeType||e.getResponseHeader("Content-Type"));if(r)for(i in a)if(a[i]&&a[i].test(r)){u.unshift(i);break}if(u[0]in n)o=u[0];else{for(i in n){if(!u[0]||t.converters[i+" "+u[0]]){o=i;break}s=s||i}o=o||s}return o?(o!==u[0]&&u.unshift(o),n[o]):void 0}(h,x,i)),b=function(t,e,n,r){var i,o,s,a,u,c={},l=t.dataTypes.slice();if(l[1])for(s in t.converters)c[s.toLowerCase()]=t.converters[s];for(o=l.shift();o;)if(t.responseFields[o]&&(n[t.responseFields[o]]=e),!u&&r&&t.dataFilter&&(e=t.dataFilter(e,t.dataType)),u=o,o=l.shift())if("*"===o)o=u;else if("*"!==u&&u!==o){if(!(s=c[u+" "+o]||c["* "+o]))for(i in c)if(a=i.split(" "),a[1]===o&&(s=c[u+" "+a[0]]||c["* "+a[0]])){!0===s?s=c[i]:!0!==c[i]&&(o=a[0],l.unshift(a[1]));break}if(!0!==s)if(s&&t.throws)e=s(e);else try{e=s(e)}catch(t){return{state:"parsererror",error:s?t:"No conversion from "+u+" to "+o}}}return{state:"success",data:e}}(h,b,x,a),a?(h.ifModified&&((i=x.getResponseHeader("Last-Modified"))&&(d.lastModified[o]=i),i=x.getResponseHeader("etag"))&&(d.etag[o]=i),204===e||"HEAD"===h.type?_="nocontent":304===e?_="notmodified":(_=b.state,l=b.data,a=!(y=b.error))):(y=_,!e&&_||(_="error",e<0&&(e=0))),x.status=e,x.statusText=(n||_)+"",a?g.resolveWith(f,[l,_,x]):g.rejectWith(f,[x,_,y]),x.statusCode(v),v=void 0,c&&p.trigger(a?"ajaxSuccess":"ajaxError",[x,h,a?l:y]),m.fireWith(f,[x,_]),c)&&(p.trigger("ajaxComplete",[x,h]),--d.active||d.event.trigger("ajaxStop"))}},getJSON:function(t,e,n){return d.get(t,e,n,"json")},getScript:function(t,e){return d.get(t,void 0,e,"script")}}),d.each(["get","post"],(function(t,e){d[e]=function(t,n,r,i){return d.isFunction(n)&&(i=i||r,r=n,n=void 0),d.ajax(d.extend({url:t,type:e,dataType:i,data:n,success:r},d.isPlainObject(t)&&t))}})),d._evalUrl=function(t){return d.ajax({url:t,type:"GET",dataType:"script",async:!1,global:!1,throws:!0})},d.fn.extend({wrapAll:function(t){var e;return d.isFunction(t)?this.each((function(e){d(this).wrapAll(t.call(this,e))})):(this[0]&&(e=d(t,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&e.insertBefore(this[0]),e.map((function(){for(var t=this;t.firstElementChild;)t=t.firstElementChild;return t})).append(this)),this)},wrapInner:function(t){return d.isFunction(t)?this.each((function(e){d(this).wrapInner(t.call(this,e))})):this.each((function(){var e=d(this),n=e.contents();n.length?n.wrapAll(t):e.append(t)}))},wrap:function(t){var e=d.isFunction(t);return this.each((function(n){d(this).wrapAll(e?t.call(this,n):t)}))},unwrap:function(){return this.parent().each((function(){d.nodeName(this,"body")||d(this).replaceWith(this.childNodes)})).end()}}),d.expr.filters.hidden=function(t){return!d.expr.filters.visible(t)},d.expr.filters.visible=function(t){return 0<t.offsetWidth||0<t.offsetHeight||0<t.getClientRects().length};var Se=/%20/g,Oe=/\[\]$/,Ee=/\r?\n/g,Te=/^(?:submit|button|image|reset|file)$/i,Ae=/^(?:input|select|textarea|keygen)/i;d.param=function(t,e){function n(t,e){e=d.isFunction(e)?e():null==e?"":e,i[i.length]=encodeURIComponent(t)+"="+encodeURIComponent(e)}var r,i=[];if(void 0===e&&(e=d.ajaxSettings&&d.ajaxSettings.traditional),d.isArray(t)||t.jquery&&!d.isPlainObject(t))d.each(t,(function(){n(this.name,this.value)}));else for(r in t)!function t(e,n,r,i){if(d.isArray(n))d.each(n,(function(n,o){r||Oe.test(e)?i(e,o):t(e+"["+("object"==typeof o&&null!=o?n:"")+"]",o,r,i)}));else if(r||"object"!==d.type(n))i(e,n);else for(var o in n)t(e+"["+o+"]",n[o],r,i)}(r,t[r],e,n);return i.join("&").replace(Se,"+")},d.fn.extend({serialize:function(){return d.param(this.serializeArray())},serializeArray:function(){return this.map((function(){var t=d.prop(this,"elements");return t?d.makeArray(t):this})).filter((function(){var t=this.type;return this.name&&!d(this).is(":disabled")&&Ae.test(this.nodeName)&&!Te.test(t)&&(this.checked||!G.test(t))})).map((function(t,e){var n=d(this).val();return null==n?null:d.isArray(n)?d.map(n,(function(t){return{name:e.name,value:t.replace(Ee,"\r\n")}})):{name:e.name,value:n.replace(Ee,"\r\n")}})).get()}}),d.ajaxSettings.xhr=function(){try{return new t.XMLHttpRequest}catch(r){}};var Ie={0:200,1223:204},je=d.ajaxSettings.xhr(),Pe=(f.cors=!!je&&"withCredentials"in je,f.ajax=je=!!je,d.ajaxTransport((function(e){var n,r;return f.cors||je&&!e.crossDomain?{send:function(i,o){var s,a=e.xhr();if(a.open(e.type,e.url,e.async,e.username,e.password),e.xhrFields)for(s in e.xhrFields)a[s]=e.xhrFields[s];for(s in e.mimeType&&a.overrideMimeType&&a.overrideMimeType(e.mimeType),e.crossDomain||i["X-Requested-With"]||(i["X-Requested-With"]="XMLHttpRequest"),i)a.setRequestHeader(s,i[s]);n=function(t){return function(){n&&(n=r=a.onload=a.onerror=a.onabort=a.onreadystatechange=null,"abort"===t?a.abort():"error"===t?"number"!=typeof a.status?o(0,"error"):o(a.status,a.statusText):o(Ie[a.status]||a.status,a.statusText,"text"!==(a.responseType||"text")||"string"!=typeof a.responseText?{binary:a.response}:{text:a.responseText},a.getAllResponseHeaders()))}},a.onload=n(),r=a.onerror=n("error"),void 0!==a.onabort?a.onabort=r:a.onreadystatechange=function(){4===a.readyState&&t.setTimeout((function(){n&&r()}))},n=n("abort");try{a.send(e.hasContent&&e.data||null)}catch(i){if(n)throw i}},abort:function(){n&&n()}}:void 0})),d.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(t){return d.globalEval(t),t}}}),d.ajaxPrefilter("script",(function(t){void 0===t.cache&&(t.cache=!1),t.crossDomain&&(t.type="GET")})),d.ajaxTransport("script",(function(t){var e,n;if(t.crossDomain)return{send:function(r,o){e=d("<script>").prop({charset:t.scriptCharset,src:t.url}).on("load error",n=function(t){e.remove(),n=null,t&&o("error"===t.type?404:200,t.type)}),i.head.appendChild(e[0])},abort:function(){n&&n()}}})),[]),Ne=/(=)\?(?=&|$)|\?\?/,De=(d.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var t=Pe.pop()||d.expando+"_"+he++;return this[t]=!0,t}}),d.ajaxPrefilter("json jsonp",(function(e,n,r){var i,o,s,a=!1!==e.jsonp&&(Ne.test(e.url)?"url":"string"==typeof e.data&&0===(e.contentType||"").indexOf("application/x-www-form-urlencoded")&&Ne.test(e.data)&&"data");return a||"jsonp"===e.dataTypes[0]?(i=e.jsonpCallback=d.isFunction(e.jsonpCallback)?e.jsonpCallback():e.jsonpCallback,a?e[a]=e[a].replace(Ne,"$1"+i):!1!==e.jsonp&&(e.url+=(fe.test(e.url)?"&":"?")+e.jsonp+"="+i),e.converters["script json"]=function(){return s||d.error(i+" was not called"),s[0]},e.dataTypes[0]="json",o=t[i],t[i]=function(){s=arguments},r.always((function(){void 0===o?d(t).removeProp(i):t[i]=o,e[i]&&(e.jsonpCallback=n.jsonpCallback,Pe.push(i)),s&&d.isFunction(o)&&o(s[0]),s=o=void 0})),"script"):void 0})),d.parseHTML=function(t,e,n){if(!t||"string"!=typeof t)return null;"boolean"==typeof e&&(n=e,e=!1),e=e||i;var r=x.exec(t);n=!n&&[];return r?[e.createElement(r[1])]:(r=J([t],e,n),n&&n.length&&d(n).remove(),d.merge([],r.childNodes))},d.fn.load);function Re(t){return d.isWindow(t)?t:9===t.nodeType&&t.defaultView}d.fn.load=function(t,e,n){var r,i,o,s,a;return"string"!=typeof t&&De?De.apply(this,arguments):(s=this,-1<(a=t.indexOf(" "))&&(r=d.trim(t.slice(a)),t=t.slice(0,a)),d.isFunction(e)?(n=e,e=void 0):e&&"object"==typeof e&&(i="POST"),0<s.length&&d.ajax({url:t,type:i||"GET",dataType:"html",data:e}).done((function(t){o=arguments,s.html(r?d("<div>").append(d.parseHTML(t)).find(r):t)})).always(n&&function(t,e){s.each((function(){n.apply(this,o||[t.responseText,e,t])}))}),this)},d.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],(function(t,e){d.fn[e]=function(t){return this.on(e,t)}})),d.expr.filters.animated=function(t){return d.grep(d.timers,(function(e){return t===e.elem})).length},d.offset={setOffset:function(t,e,n){var r,i,o,s,a=d.css(t,"position"),u=d(t),c={};"static"===a&&(t.style.position="relative"),o=u.offset(),r=d.css(t,"top"),s=d.css(t,"left"),a=("absolute"===a||"fixed"===a)&&-1<(r+s).indexOf("auto")?(i=(a=u.position()).top,a.left):(i=parseFloat(r)||0,parseFloat(s)||0),null!=(e=d.isFunction(e)?e.call(t,n,d.extend({},o)):e).top&&(c.top=e.top-o.top+i),null!=e.left&&(c.left=e.left-o.left+a),"using"in e?e.using.call(t,c):u.css(c)}},d.fn.extend({offset:function(t){var e,n,r,i;return arguments.length?void 0===t?this:this.each((function(e){d.offset.setOffset(this,t,e)})):(r={top:0,left:0},(i=(n=this[0])&&n.ownerDocument)?(e=i.documentElement,d.contains(e,n)?(r=n.getBoundingClientRect(),n=Re(i),{top:r.top+n.pageYOffset-e.clientTop,left:r.left+n.pageXOffset-e.clientLeft}):r):void 0)},position:function(){var t,e,n,r;if(this[0])return n=this[0],r={top:0,left:0},"fixed"===d.css(n,"position")?e=n.getBoundingClientRect():(t=this.offsetParent(),e=this.offset(),(r=d.nodeName(t[0],"html")?r:t.offset()).top+=d.css(t[0],"borderTopWidth",!0),r.left+=d.css(t[0],"borderLeftWidth",!0)),{top:e.top-r.top-d.css(n,"marginTop",!0),left:e.left-r.left-d.css(n,"marginLeft",!0)}},offsetParent:function(){return this.map((function(){for(var t=this.offsetParent;t&&"static"===d.css(t,"position");)t=t.offsetParent;return t||jt}))}}),d.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},(function(t,e){var n="pageYOffset"===e;d.fn[t]=function(r){return N(this,(function(t,r,i){var o=Re(t);return void 0===i?o?o[e]:t[r]:void(o?o.scrollTo(n?o.pageXOffset:i,n?i:o.pageYOffset):t[r]=i)}),t,r,arguments.length)}})),d.each(["top","left"],(function(t,e){d.cssHooks[e]=Dt(f.pixelPosition,(function(t,n){return n?(n=Nt(t,e),It.test(n)?d(t).position()[e]+"px":n):void 0}))})),d.each({Height:"height",Width:"width"},(function(t,e){d.each({padding:"inner"+t,content:e,"":"outer"+t},(function(n,r){d.fn[r]=function(r,i){var o=arguments.length&&(n||"boolean"!=typeof r),s=n||(!0===r||!0===i?"margin":"border");return N(this,(function(e,n,r){var i;return d.isWindow(e)?e.document.documentElement["client"+t]:9===e.nodeType?(i=e.documentElement,Math.max(e.body["scroll"+t],i["scroll"+t],e.body["offset"+t],i["offset"+t],i["client"+t])):void 0===r?d.css(e,n,s):d.style(e,n,r,s)}),e,o?r:void 0,o,null)}}))})),d.fn.extend({bind:function(t,e,n){return this.on(t,null,e,n)},unbind:function(t,e){return this.off(t,null,e)},delegate:function(t,e,n,r){return this.on(e,t,n,r)},undelegate:function(t,e,n){return 1===arguments.length?this.off(t,"**"):this.off(e,t||"**",n)},size:function(){return this.length}}),d.fn.andSelf=d.fn.addBack,"function"==typeof define&&define.amd&&define("jquery",[],(function(){return d}));var Me=t.jQuery,We=t.$;return d.noConflict=function(e){return t.$===d&&(t.$=We),e&&t.jQuery===d&&(t.jQuery=Me),d},e||(t.jQuery=t.$=d),d})),function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.clipboard=e():t.clipboard=e()}(this,(function(){return e=[function(t,e,n){"use strict";function r(t){var e=new v;t=function(t,e,n){f("listener called"),t.success=!0,e.forEach((function(e,r){n.clipboardData.setData(r,e),r===g&&n.clipboardData.getData(r)!=e&&(f("setting text/plain failed"),t.success=!1)})),n.preventDefault()}.bind(this,e,t);document.addEventListener("copy",t);try{document.execCommand("copy")}finally{document.removeEventListener("copy",t)}return e.success}function i(t,e){return o(t),t=r(e),s(),t}function o(t){var e=document.getSelection(),n=document.createRange();n.selectNodeContents(t),e.removeAllRanges(),e.addRange(n)}function s(){document.getSelection().removeAllRanges()}function a(){return"undefined"==typeof ClipboardEvent&&void 0!==window.clipboardData&&void 0!==window.clipboardData.setData}function u(){return new h((function(t,e){var n=window.clipboardData.getData("Text");""===n?e(new Error("Empty clipboard or could not read plain text from clipboard")):t(n)}))}Object.defineProperty(e,"__esModule",{value:!0});var c=n(1),l=n(5),h="undefined"==typeof Promise?c.Promise:Promise,f=function(t){},p=!0,d=function(){(console.warn||console.log).call(arguments)}.bind(console,"[clipboard-polyfill]"),g="text/plain";m.setDebugLog=function(t){f=t},m.suppressWarnings=function(){p=!1,l.suppressDTWarnings()},m.write=function(t){return p&&!t.getData(g)&&d("clipboard.write() was called without a `text/plain` data type. On some platforms, this may result in an empty clipboard. Call clipboard.suppressWarnings() to suppress this warning."),new h((function(e,n){var u,c,l,h;a()?function(t){if(void 0!==(t=t.getData(g)))return window.clipboardData.setData("Text",t);throw"No `text/plain` value was specified."}(t)?e():n(new Error("Copying failed, possibly because the user rejected it.")):r(t)?(f("regular execCopy worked"),e()):-1<navigator.userAgent.indexOf("Edge")?(f('UA "Edge" => assuming success'),e()):i(document.body,t)?(f("copyUsingTempSelection worked"),e()):(u=t,(c=document.createElement("div")).setAttribute("style","-webkit-user-select: text !important"),c.textContent="temporary element",document.body.appendChild(c),u=i(c,u),document.body.removeChild(c),u?(f("copyUsingTempElem worked"),e()):void 0!==(c=t.getData(g))&&(u=c,f("copyTextUsingDOM"),(c=document.createElement("div")).setAttribute("style","-webkit-user-select: text !important"),(l=c).attachShadow&&(f("Using shadow DOM."),l=c.attachShadow({mode:"open"})),(h=document.createElement("span")).innerText=u,l.appendChild(h),document.body.appendChild(c),o(h),u=document.execCommand("copy"),s(),document.body.removeChild(c),u)?(f("copyTextUsingDOM worked"),e()):n(new Error("Copy command failed.")))}))},m.writeText=function(t){var e;return navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(t):((e=new l.DT).setData(g,t),this.write(e))},m.read=function(){return new h((function(t,e){a()?u().then((function(e){return t(((n=new l.DT).setData(g,e),n));var n}),e):e("Read is not supported in your browser.")}))},m.readText=function(){return navigator.clipboard&&navigator.clipboard.readText?navigator.clipboard.readText():a()?u():new h((function(t,e){e("Read is not supported in your browser.")}))},m.DT=l.DT,n=m;function m(){}e.default=n;var v=function(){this.success=!1};t.exports=n},function(t,e,n){(function(e,r){t.exports=function(){"use strict";function t(t){var e=typeof t;return null!==t&&("object"===e||"function"===e)}function i(t){return"function"==typeof t}function o(t){U=t}function s(t){G=t}function a(){return void 0!==z?function(){z(c)}:u()}function u(){var t=setTimeout;return function(){return t(c,1)}}function c(){for(var t=0;t<q;t+=2)(0,Q[t])(Q[t+1]),Q[t]=void 0,Q[t+1]=void 0;q=0}function l(t,e){var n=arguments,r=this,i=new this.constructor(f);void 0===i[Z]&&j(i);var o=r._state;return o?function(){var t=n[o-1];G((function(){return T(o,i,t,r._result)}))}():k(r,i,t,e),i}function h(t){var e=this;if(t&&"object"==typeof t&&t.constructor===e)return t;var n=new e(f);return w(n,t),n}function f(){}function p(){return new TypeError("You cannot resolve a promise with itself")}function d(){return new TypeError("A promises callback cannot return that same promise.")}function g(t){try{return t.then}catch(t){return rt.error=t,rt}}function m(t,e,n,r){try{t.call(e,n,r)}catch(t){return t}}function v(t,e,n){G((function(t){var r=!1,i=m(n,e,(function(n){r||(r=!0,e!==n?w(t,n):x(t,n))}),(function(e){r||(r=!0,C(t,e))}),"Settle: "+(t._label||" unknown promise"));!r&&i&&(r=!0,C(t,i))}),t)}function y(t,e){e._state===et?x(t,e._result):e._state===nt?C(t,e._result):k(e,void 0,(function(e){return w(t,e)}),(function(e){return C(t,e)}))}function b(t,e,n){e.constructor===t.constructor&&n===l&&e.constructor.resolve===h?y(t,e):n===rt?(C(t,rt.error),rt.error=null):void 0===n?x(t,e):i(n)?v(t,e,n):x(t,e)}function w(e,n){e===n?C(e,p()):t(n)?b(e,n,g(n)):x(e,n)}function _(t){t._onerror&&t._onerror(t._result),S(t)}function x(t,e){t._state===tt&&(t._result=e,t._state=et,0!==t._subscribers.length&&G(S,t))}function C(t,e){t._state===tt&&(t._state=nt,t._result=e,G(_,t))}function k(t,e,n,r){var i=t._subscribers,o=i.length;t._onerror=null,i[o]=e,i[o+et]=n,i[o+nt]=r,0===o&&t._state&&G(S,t)}function S(t){var e=t._subscribers,n=t._state;if(0!==e.length){for(var r=void 0,i=void 0,o=t._result,s=0;s<e.length;s+=3)r=e[s],i=e[s+n],r?T(n,r,i,o):i(o);t._subscribers.length=0}}function O(){this.error=null}function E(t,e){try{return t(e)}catch(t){return it.error=t,it}}function T(t,e,n,r){var o=i(n),s=void 0,a=void 0,u=void 0,c=void 0;if(o){if(s=E(n,r),s===it?(c=!0,a=s.error,s.error=null):u=!0,e===s)return void C(e,d())}else s=r,u=!0;e._state!==tt||(o&&u?w(e,s):c?C(e,a):t===et?x(e,s):t===nt&&C(e,s))}function A(t,e){try{e((function(e){w(t,e)}),(function(e){C(t,e)}))}catch(e){C(t,e)}}function I(){return ot++}function j(t){t[Z]=ot++,t._state=void 0,t._result=void 0,t._subscribers=[]}function P(t,e){this._instanceConstructor=t,this.promise=new t(f),this.promise[Z]||j(this.promise),B(e)?(this.length=e.length,this._remaining=e.length,this._result=new Array(this.length),0===this.length?x(this.promise,this._result):(this.length=this.length||0,this._enumerate(e),0===this._remaining&&x(this.promise,this._result))):C(this.promise,N())}function N(){return new Error("Array Methods must be provided an Array")}function D(t){return new P(this,t).promise}function R(t){var e=this;return new e(B(t)?function(n,r){for(var i=t.length,o=0;o<i;o++)e.resolve(t[o]).then(n,r)}:function(t,e){return e(new TypeError("You must pass an array to race."))})}function M(t){var e=this,n=new e(f);return C(n,t),n}function W(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function L(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}function F(t){this[Z]=I(),this._result=this._state=void 0,this._subscribers=[],f!==t&&("function"!=typeof t&&W(),this instanceof F?A(this,t):L())}function H(){var t=void 0;if(void 0!==r)t=r;else if("undefined"!=typeof self)t=self;else try{t=Function("return this")()}catch(t){throw new Error("polyfill failed because global object is unavailable in this environment")}var e=t.Promise;if(e){var n=null;try{n=Object.prototype.toString.call(e.resolve())}catch(t){}if("[object Promise]"===n&&!e.cast)return}t.Promise=F}var B=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)},q=0,z=void 0,U=void 0,G=function(t,e){Q[q]=t,Q[q+1]=e,2===(q+=2)&&(U?U(c):J())},V="undefined"!=typeof window?window:void 0,$=V||{},X=$.MutationObserver||$.WebKitMutationObserver,Y="undefined"==typeof self&&void 0!==e&&"[object process]"==={}.toString.call(e),K="undefined"!=typeof Uint8ClampedArray&&"undefined"!=typeof importScripts&&"undefined"!=typeof MessageChannel,Q=new Array(1e3),J=void 0,Z=(J=Y?function(){return function(){return e.nextTick(c)}}():X?function(){var t=0,e=new X(c),n=document.createTextNode("");return e.observe(n,{characterData:!0}),function(){n.data=t=++t%2}}():K?function(){var t=new MessageChannel;return t.port1.onmessage=c,function(){return t.port2.postMessage(0)}}():void 0===V?function(){try{var t=n(4);return z=t.runOnLoop||t.runOnContext,a()}catch(t){return u()}}():u(),Math.random().toString(36).substring(16)),tt=void 0,et=1,nt=2,rt=new O,it=new O,ot=0;return P.prototype._enumerate=function(t){for(var e=0;this._state===tt&&e<t.length;e++)this._eachEntry(t[e],e)},P.prototype._eachEntry=function(t,e){var n=this._instanceConstructor,r=n.resolve;if(r===h){var i=g(t);if(i===l&&t._state!==tt)this._settledAt(t._state,e,t._result);else if("function"!=typeof i)this._remaining--,this._result[e]=t;else if(n===F){var o=new n(f);b(o,t,i),this._willSettleAt(o,e)}else this._willSettleAt(new n((function(e){return e(t)})),e)}else this._willSettleAt(r(t),e)},P.prototype._settledAt=function(t,e,n){var r=this.promise;r._state===tt&&(this._remaining--,t===nt?C(r,n):this._result[e]=n),0===this._remaining&&x(r,this._result)},P.prototype._willSettleAt=function(t,e){var n=this;k(t,void 0,(function(t){return n._settledAt(et,e,t)}),(function(t){return n._settledAt(nt,e,t)}))},F.all=D,F.race=R,F.resolve=h,F.reject=M,F._setScheduler=o,F._setAsap=s,F._asap=G,F.prototype={constructor:F,then:l,catch:function(t){return this.then(null,t)}},F.polyfill=H,F.Promise=F}()}).call(e,n(2),n(3))},function(t,e){function n(){throw new Error("setTimeout has not been defined")}function r(){throw new Error("clearTimeout has not been defined")}function i(e){if(c===setTimeout)return setTimeout(e,0);if((c===n||!c)&&setTimeout)return(c=setTimeout)(e,0);try{return c(e,0)}catch(t){try{return c.call(null,e,0)}catch(t){return c.call(this,e,0)}}}function o(){p&&h&&(p=!1,h.length?f=h.concat(f):d=-1,f.length)&&s()}function s(){if(!p){var t=i(o);p=!0;for(var e=f.length;e;){for(h=f,f=[];++d<e;)h&&h[d].run();d=-1,e=f.length}h=null,p=!1,function(e){if(l===clearTimeout)return clearTimeout(e);if((l===r||!l)&&clearTimeout)return(l=clearTimeout)(e);try{l(e)}catch(t){try{return l.call(null,e)}catch(t){return l.call(this,e)}}}(t)}}function a(t,e){this.fun=t,this.array=e}function u(){}var c,l;t=t.exports={};try{c="function"==typeof setTimeout?setTimeout:n}catch(t){c=n}try{l="function"==typeof clearTimeout?clearTimeout:r}catch(t){l=r}var h,f=[],p=!1,d=-1;t.nextTick=function(t){var e=new Array(arguments.length-1);if(1<arguments.length)for(var n=1;n<arguments.length;n++)e[n-1]=arguments[n];f.push(new a(t,e)),1!==f.length||p||i(s)},a.prototype.run=function(){this.fun.apply(null,this.array)},t.title="browser",t.browser=!0,t.env={},t.argv=[],t.version="",t.versions={},t.on=u,t.addListener=u,t.once=u,t.off=u,t.removeListener=u,t.removeAllListeners=u,t.emit=u,t.prependListener=u,t.prependOnceListener=u,t.listeners=function(t){return[]},t.binding=function(t){throw new Error("process.binding is not supported")},t.cwd=function(){return"/"},t.chdir=function(t){throw new Error("process.chdir is not supported")},t.umask=function(){return 0}},function(t,e){var n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e){},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=["text/plain","text/html"],i=function(){(console.warn||console.log).call(arguments)}.bind(console,"[clipboard-polyfill]"),o=!0,s=(e.suppressDTWarnings=function(){o=!1},a.prototype.setData=function(t,e){o&&-1===r.indexOf(t)&&i("Unknown data type: "+t,"Call clipboard.suppressWarnings() to suppress this warning."),this.m[t]=e},a.prototype.getData=function(t){return this.m[t]},a.prototype.forEach=function(t){for(var e in this.m)t(this.m[e],e)},a);function a(){this.m={}}e.DT=s}],n={},t.m=e,t.c=n,t.d=function(e,n,r){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:r})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},t.p="",t(t.s=0);function t(r){var i;return(n[r]||(i=n[r]={i:r,l:!1,exports:{}},e[r].call(i.exports,i,i.exports,t),i.l=!0,i)).exports}var e,n})),function(t){"use strict";"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports&&"object"==typeof module?module.exports=t(require("jquery")):t(jQuery)}((function(t,e){"use strict";var n={beforeShow:h,move:h,change:h,show:h,hide:h,color:!1,flat:!1,showInput:!1,allowEmpty:!1,showButtons:!0,clickoutFiresChange:!0,showInitial:!1,showPalette:!1,showPaletteOnly:!1,hideAfterPaletteSelect:!1,togglePaletteOnly:!1,showSelectionPalette:!0,localStorageKey:!1,appendTo:"body",maxSelectionSize:7,cancelText:"cancel",chooseText:"choose",togglePaletteMoreText:"more",togglePaletteLessText:"less",clearText:"Clear Color Selection",noColorSelectedText:"No Color Selected",preferredFormat:!1,className:"",containerClassName:"",replacerClassName:"",showAlpha:!1,theme:"sp-light",palette:[["#ffffff","#000000","#ff0000","#ff8000","#ffff00","#008000","#0000ff","#4b0082","#9400d3"]],selectionPalette:[],disabled:!1,offset:null},r=[],i=!!/msie/i.exec(window.navigator.userAgent),o=((E=document.createElement("div").style).cssText="background-color:rgba(0,0,0,.5)",u(E.backgroundColor,"rgba")||u(E.backgroundColor,"hsla")),s=["<div class='sp-replacer'>","<div class='sp-preview'><div class='sp-preview-inner'></div></div>","<div class='sp-dd'>&#9660;</div>","</div>"].join(""),a=function(){var t="";if(i)for(var e=1;e<=6;e++)t+="<div class='sp-"+e+"'></div>";return["<div class='sp-container sp-hidden'>","<div class='sp-palette-container'>","<div class='sp-palette sp-thumb sp-cf'></div>","<div class='sp-palette-button-container sp-cf'>","<button type='button' class='sp-palette-toggle'></button>","</div>","</div>","<div class='sp-picker-container'>","<div class='sp-top sp-cf'>","<div class='sp-fill'></div>","<div class='sp-top-inner'>","<div class='sp-color'>","<div class='sp-sat'>","<div class='sp-val'>","<div class='sp-dragger'></div>","</div>","</div>","</div>","<div class='sp-clear sp-clear-display'>","</div>","<div class='sp-hue'>","<div class='sp-slider'></div>",t,"</div>","</div>","<div class='sp-alpha'><div class='sp-alpha-inner'><div class='sp-alpha-handle'></div></div></div>","</div>","<div class='sp-input-container sp-cf'>","<input class='sp-input formulaInputFocus' type='text' spellcheck='false'  />","</div>","<div class='sp-initial sp-thumb sp-cf'></div>","<div class='sp-button-container sp-cf'>","<a class='sp-cancel' href='#'></a>","<button type='button' class='sp-choose'></button>","</div>","</div>","</div>"].join("")}();function u(t,e){return!!~(""+t).indexOf(e)}function c(e,n,r,i){for(var s=[],a=0;a<e.length;a++){var u,c,l,h=e[a];h?(c=(u=tinycolor(h)).toHsl().l<.5?"sp-thumb-el sp-thumb-dark":"sp-thumb-el sp-thumb-light",c+=tinycolor.equals(n,h)?" sp-thumb-active":"",h=u.toString(i.preferredFormat||"rgb"),l=o?"background-color:"+u.toRgbString():"filter:"+u.toFilter(),s.push('<span title="'+h+'" data-color="'+u.toRgbString()+'" class="'+c+'"><span class="sp-thumb-inner" style="'+l+';" /></span>')):s.push(t("<div />").append(t('<span data-color="" style="background-color:transparent;" class="sp-clear-display"></span>').attr("title",i.noColorSelectedText)).html())}return"<div class='sp-cf "+r+"'>"+s.join("")+"</div>"}function l(u,l){x=u,(l=t.extend({},n,l)).callbacks={move:p(l.move,x),change:p(l.change,x),show:p(l.show,x),hide:p(l.hide,x),beforeShow:p(l.beforeShow,x)};var h,m,v,y=l,b=y.flat,w=y.showSelectionPalette,_=y.localStorageKey,x=y.theme,C=y.callbacks,k=(h=Ft,function(){var t=this,e=arguments;m=m||setTimeout((function(){m=null,h.apply(t,e)}),10)}),S=!1,O=!1,E=0,T=0,A=0,I=0,j=0,P=0,N=0,D=0,R=0,M=0,W=1,L=[],F=[],H={},B=y.selectionPalette.slice(0),q=y.maxSelectionSize,z="sp-dragging",U=null,G=u.ownerDocument,V=(G.body,t(u)),$=!1,X=t(a,G).addClass(x),Y=X.find(".sp-picker-container"),K=X.find(".sp-color"),Q=X.find(".sp-dragger"),J=X.find(".sp-hue"),Z=X.find(".sp-slider"),tt=X.find(".sp-alpha-inner"),et=X.find(".sp-alpha"),nt=X.find(".sp-alpha-handle"),rt=X.find(".sp-input"),it=X.find(".sp-palette"),ot=X.find(".sp-initial"),st=(l=X.find(".sp-cancel"),u=X.find(".sp-clear"),X.find(".sp-choose")),at=X.find(".sp-palette-toggle"),ut=V.is("input"),ct=ut&&"color"===V.attr("type")&&g(),lt=ut&&!b,ht=lt?t(s).addClass(x).addClass(y.className).addClass(y.replacerClassName):t([]),ft=lt?ht:V,pt=ht.find(".sp-preview-inner"),dt=(x=y.color||ut&&V.val(),!1),gt=y.preferredFormat,mt=!y.showButtons||y.clickoutFiresChange,vt=!x,yt=y.allowEmpty&&!ct;function bt(){if(y.showPaletteOnly&&(y.showPalette=!0),at.text(y.showPaletteOnly?y.togglePaletteMoreText:y.togglePaletteLessText),y.palette){L=y.palette.slice(0),F=t.isArray(L[0])?L:[L],H={};for(var e=0;e<F.length;e++)for(var n=0;n<F[e].length;n++){var r=tinycolor(F[e][n]).toRgbString();H[r]=!0}}X.toggleClass("sp-flat",b),X.toggleClass("sp-input-disabled",!y.showInput),X.toggleClass("sp-alpha-enabled",y.showAlpha),X.toggleClass("sp-clear-enabled",yt),X.toggleClass("sp-buttons-disabled",!y.showButtons),X.toggleClass("sp-palette-buttons-disabled",!y.togglePaletteOnly),X.toggleClass("sp-palette-disabled",!y.showPalette),X.toggleClass("sp-palette-only",y.showPaletteOnly),X.toggleClass("sp-initial-disabled",!y.showInitial),X.addClass(y.className).addClass(y.containerClassName),Ft()}function wt(){if(_&&window.localStorage){try{var e=window.localStorage[_].split(",#");1<e.length&&(delete window.localStorage[_],t.each(e,(function(t,e){_t(e)})))}catch(e){}try{B=window.localStorage[_].split(";")}catch(e){}}}function _t(e){if(w){var n=tinycolor(e).toRgbString();if(!H[n]&&-1===t.inArray(n,B))for(B.push(n);B.length>q;)B.shift();if(_&&window.localStorage)try{window.localStorage[_]=B.join(";")}catch(e){}}}function xt(){var e=Dt(),n=t.map(F,(function(t,n){return c(t,e,"sp-palette-row sp-palette-row-"+n,y)}));wt(),B&&n.push(c(function(){var t=[];if(y.showPalette)for(var e=0;e<B.length;e++){var n=tinycolor(B[e]).toRgbString();H[n]||t.push(B[e])}return t.reverse().slice(0,y.maxSelectionSize)}(),e,"sp-palette-row sp-palette-row-selection",y)),it.html(n.join(""))}function Ct(){var t,e;y.showInitial&&(t=dt,e=Dt(),ot.html(c([t,e],e,"sp-palette-row-initial",y)))}function kt(){(T<=0||E<=0||I<=0)&&Ft(),O=!0,X.addClass(z),U=null,V.trigger("dragstart.spectrum",[Dt()])}function St(){O=!1,X.removeClass(z),V.trigger("dragstop.spectrum",[Dt()])}function Ot(){var t=rt.val();null!==t&&""!==t||!yt?(t=tinycolor(t)).isValid()?(Nt(t),Lt(!0)):rt.addClass("sp-validation-error"):(Nt(null),Lt(!0))}function Et(){(S?jt:Tt)()}function Tt(){var e=t.Event("beforeShow.spectrum");if(S)Ft();else if(V.trigger(e,[Dt()]),!1!==C.beforeShow(Dt())&&!e.isDefaultPrevented()){for(var n=0;n<r.length;n++)r[n]&&r[n].hide();S=!0,t(G).bind("keydown.spectrum",At),t(G).bind("click.spectrum",It),t(window).bind("resize.spectrum",k),ht.addClass("sp-active"),X.removeClass("sp-hidden"),Ft(),Mt(),dt=Dt(),Ct(),C.show(dt),V.trigger("show.spectrum",[dt])}}function At(t){27===t.keyCode&&jt()}function It(t){2==t.button||O||(mt?Lt(!0):Pt(),jt())}function jt(){S&&!b&&(S=!1,t(G).unbind("keydown.spectrum",At),t(G).unbind("click.spectrum",It),t(window).unbind("resize.spectrum",k),ht.removeClass("sp-active"),X.addClass("sp-hidden"),C.hide(Dt()),V.trigger("hide.spectrum",[Dt()]))}function Pt(){Nt(dt,!0)}function Nt(t,e){var n;tinycolor.equals(t,Dt())?Mt():(!t&&yt?vt=!0:(vt=!1,t=(n=tinycolor(t)).toHsv(),D=t.h%360/360,R=t.s,M=t.v,W=t.a),Mt(),n&&n.isValid()&&!e&&(gt=y.preferredFormat||n.getFormat()))}function Dt(t){return t=t||{},yt&&vt?null:tinycolor.fromRatio({h:D,s:R,v:M,a:Math.round(100*W)/100},{format:t.format||gt})}function Rt(){Mt(),C.move(Dt()),V.trigger("move.spectrum",[Dt()])}function Mt(){rt.removeClass("sp-validation-error"),Wt();var t,e,n,r=tinycolor.fromRatio({h:D,s:1,v:1}),s=(r=(K.css("background-color",r.toHexString()),gt),Dt({format:r=!(W<1)||0===W&&"name"===gt||"hex"!==gt&&"hex3"!==gt&&"hex6"!==gt&&"name"!==gt?gt:"rgb"})),a="";pt.removeClass("sp-clear-display"),pt.css("background-color","transparent"),!s&&yt?pt.addClass("sp-clear-display"):(t=s.toHexString(),e=s.toRgbString(),o||1===s.alpha?pt.css("background-color",e):(pt.css("background-color","transparent"),pt.css("filter",s.toFilter())),y.showAlpha&&((e=s.toRgb()).a=0,n="linear-gradient(left, "+(e=tinycolor(e).toRgbString())+", "+t+")",i?tt.css("filter",tinycolor(e).toFilter({gradientType:1},t)):(tt.css("background","-webkit-"+n),tt.css("background","-moz-"+n),tt.css("background","-ms-"+n),tt.css("background","linear-gradient(to right, "+e+", "+t+")"))),a=s.toString(r)),y.showInput&&rt.val(a),y.showPalette&&xt(),Ct()}function Wt(){var t=R,e=M;yt&&vt?(nt.hide(),Z.hide(),Q.hide()):(nt.show(),Z.show(),Q.show(),t*=E,e=T-e*T,t=Math.max(-A,Math.min(E-A,t-A)),e=Math.max(-A,Math.min(T-A,e-A)),Q.css({top:e+"px",left:t+"px"}),e=W*j,nt.css({left:e-P/2+"px"}),t=D*I,Z.css({top:t-N+"px"}))}function Lt(t){var e=Dt(),n="";e&&(n=e.toString(gt),_t(e)),ut&&V.val(n),t&&(C.change(e),V.trigger("change",[e]))}function Ft(){var e,n,r,i,o,s,a;S&&(E=K.width(),T=K.height(),A=Q.height(),J.width(),I=J.height(),N=Z.height(),j=et.width(),P=nt.width(),b||(X.css("position","absolute"),y.offset?X.offset(y.offset):X.offset((e=ft,n=(a=X).outerWidth(),r=a.outerHeight(),i=e.outerHeight(),o=(s=(a=a[0].ownerDocument).documentElement).clientWidth+t(a).scrollLeft(),s=s.clientHeight+t(a).scrollTop(),(a=e.offset()).top+=i,a.left-=Math.min(a.left,a.left+n>o&&n<o?Math.abs(a.left+n-o):0),a.top-=Math.min(a.top,a.top+r>s&&r<s?Math.abs(+(r+i)):0),a))),Wt(),y.showPalette&&xt(),V.trigger("reflow.spectrum"))}function Ht(){jt(),$=!0,V.attr("disabled",!0),ft.addClass("sp-disabled")}function Bt(e){return e.data&&e.data.ignore?(Nt(t(e.target).closest(".sp-thumb-el").data("color")),Rt()):(Nt(t(e.target).closest(".sp-thumb-el").data("color")),Rt(),Lt(!0),y.hideAfterPaletteSelect&&jt()),!1}i&&X.find("*:not(input)").attr("unselectable","on"),bt(),lt&&V.after(ht).hide(),yt||u.hide(),b?V.after(X).hide():(v=1!==(v="parent"===y.appendTo?V.parent():t(y.appendTo)).length?t("body"):v).append(X),wt(),ft.bind("click.spectrum touchstart.spectrum",(function(e){$||Et(),e.stopPropagation(),t(e.target).is("input")||e.preventDefault()})),!V.is(":disabled")&&!0!==y.disabled||Ht(),X.click(f),rt.change(Ot),rt.bind("paste",(function(){setTimeout(Ot,1)})),rt.keydown((function(t){13==t.keyCode&&Ot()})),l.text(y.cancelText),l.bind("click.spectrum",(function(t){t.stopPropagation(),t.preventDefault(),Pt(),jt()})),u.attr("title",y.clearText),u.bind("click.spectrum",(function(t){t.stopPropagation(),t.preventDefault(),vt=!0,Rt(),b&&Lt(!0)})),st.text(y.chooseText),st.bind("click.spectrum",(function(t){t.stopPropagation(),t.preventDefault(),i&&rt.is(":focus")&&rt.trigger("change"),rt.hasClass("sp-validation-error")||(Lt(!0),jt())})),at.text(y.showPaletteOnly?y.togglePaletteMoreText:y.togglePaletteLessText),at.bind("click.spectrum",(function(t){t.stopPropagation(),t.preventDefault(),y.showPaletteOnly=!y.showPaletteOnly,y.showPaletteOnly||b||X.css("left","-="+(Y.outerWidth(!0)+5)),bt()})),d(et,(function(t,e,n){W=t/j,vt=!1,n.shiftKey&&(W=Math.round(10*W)/10),Rt()}),kt,St),d(J,(function(t,e){D=parseFloat(e/I),vt=!1,y.showAlpha||(W=1),Rt()}),kt,St),d(K,(function(t,e,n){n.shiftKey?U||(n=R*E,r=T-M*T,n=Math.abs(t-n)>Math.abs(e-r),U=n?"x":"y"):U=null;var r=!U||"y"===U;U&&"x"!==U||(R=parseFloat(t/E)),r&&(M=parseFloat((T-e)/T)),vt=!1,y.showAlpha||(W=1),Rt()}),kt,St),x?(Nt(x),Mt(),gt=y.preferredFormat||tinycolor(x).format,_t(x)):Mt(),b&&Tt(),v=i?"mousedown.spectrum":"click.spectrum touchstart.spectrum",it.delegate(".sp-thumb-el",v,Bt),ot.delegate(".sp-thumb-el:nth-child(1)",v,{ignore:!0},Bt);var qt={show:Tt,hide:jt,toggle:Et,reflow:Ft,option:function(n,r){return n===e?t.extend({},y):r===e?y[n]:(y[n]=r,"preferredFormat"===n&&(gt=y.preferredFormat),void bt())},enable:function(){$=!1,V.attr("disabled",!1),ft.removeClass("sp-disabled")},disable:Ht,offset:function(t){y.offset=t,Ft()},set:function(t){Nt(t),Lt()},get:Dt,destroy:function(){V.show(),ft.unbind("click.spectrum touchstart.spectrum"),X.remove(),ht.remove(),r[qt.id]=null},container:X};return qt.id=r.push(qt)-1,qt}function h(){}function f(t){t.stopPropagation()}function p(t,e){var n=Array.prototype.slice,r=n.call(arguments,2);return function(){return t.apply(e,r.concat(n.call(arguments)))}}function d(e,n,r,o){n=n||function(){},r=r||function(){},o=o||function(){};var s=document,a=!1,u={},c=0,l=0,h="ontouchstart"in window,f={};function p(t){t.stopPropagation&&t.stopPropagation(),t.preventDefault&&t.preventDefault(),t.returnValue=!1}function d(t){if(a){if(i&&s.documentMode<9&&!t.button)return g();var r=t.originalEvent&&t.originalEvent.touches&&t.originalEvent.touches[0],o=r&&r.pageX||t.pageX;r=r&&r.pageY||t.pageY,o=Math.max(0,Math.min(o-u.left,l)),r=Math.max(0,Math.min(r-u.top,c));h&&p(t),n.apply(e,[o,r,t])}}function g(){a&&(t(s).unbind(f),t(s.body).removeClass("sp-dragging"),setTimeout((function(){o.apply(e,arguments)}),0)),a=!1}f.selectstart=p,f.dragstart=p,f["touchmove mousemove"]=d,f["touchend mouseup"]=g,t(e).bind("touchstart mousedown",(function(n){(n.which?3==n.which:2==n.button)||a||!1!==r.apply(e,arguments)&&(a=!0,c=t(e).height(),l=t(e).width(),u=t(e).offset(),t(s).bind(f),t(s.body).addClass("sp-dragging"),d(n),p(n))}))}function g(){return t.fn.spectrum.inputTypeColorSupport()}var m,v,y,b,w,_,x,C,k,S,O,E,T,A="spectrum.id";function I(t,e){var n,r,i,o,s,a,u,c,l,h,f,p,d,g;return e=e||{},(t=t||"")instanceof I?t:this instanceof I?(f={r:0,g:0,b:0},g=d=!(p=1),p=X(p="object"==typeof(n="string"==typeof(n=t)?function(t){t=t.replace(m,"").replace(v,"").toLowerCase();var e,n=!1;if(k[t])t=k[t],n=!0;else if("transparent"==t)return{r:0,g:0,b:0,a:0,format:"name"};return(e=T.rgb.exec(t))?{r:e[1],g:e[2],b:e[3]}:(e=T.rgba.exec(t))?{r:e[1],g:e[2],b:e[3],a:e[4]}:(e=T.hsl.exec(t))?{h:e[1],s:e[2],l:e[3]}:(e=T.hsla.exec(t))?{h:e[1],s:e[2],l:e[3],a:e[4]}:(e=T.hsv.exec(t))?{h:e[1],s:e[2],v:e[3]}:(e=T.hsva.exec(t))?{h:e[1],s:e[2],v:e[3],a:e[4]}:(e=T.hex8.exec(t))?{a:Q(e[1])/255,r:Q(e[2]),g:Q(e[3]),b:Q(e[4]),format:n?"name":"hex8"}:(e=T.hex6.exec(t))?{r:Q(e[1]),g:Q(e[2]),b:Q(e[3]),format:n?"name":"hex"}:!!(e=T.hex3.exec(t))&&{r:Q(e[1]+""+e[1]),g:Q(e[2]+""+e[2]),b:Q(e[3]+""+e[3]),format:n?"name":"hex"}}(n):n)&&(n.hasOwnProperty("r")&&n.hasOwnProperty("g")&&n.hasOwnProperty("b")?(c=n.r,l=n.g,h=n.b,f={r:255*Y(c,255),g:255*Y(l,255),b:255*Y(h,255)},d=!0,g="%"===String(n.r).substr(-1)?"prgb":"rgb"):n.hasOwnProperty("h")&&n.hasOwnProperty("s")&&n.hasOwnProperty("v")?(n.s=Z(n.s),n.v=Z(n.v),c=n.h,l=n.s,h=n.v,c=6*Y(c,360),l=Y(l,100),h=Y(h,100),s=b.floor(c),f={r:255*[h,u=h*(1-(c-=s)*l),a=h*(1-l),a,c=h*(1-(1-c)*l),h][l=s%6],g:255*[c,h,h,u,a,a][l],b:255*[a,a,c,h,h,u][l]},d=!0,g="hsv"):n.hasOwnProperty("h")&&n.hasOwnProperty("s")&&n.hasOwnProperty("l")&&(n.s=Z(n.s),n.l=Z(n.l),s=n.h,a=n.s,c=n.l,s=Y(s,360),a=Y(a,100),c=Y(c,100),0===a?r=i=o=c:(r=C(a=2*c-(c=c<.5?c*(1+a):c+a-c*a),c,s+1/3),i=C(a,c,s),o=C(a,c,s-1/3)),f={r:255*r,g:255*i,b:255*o},d=!0,g="hsl"),n.hasOwnProperty("a"))?n.a:p),h={ok:d,format:n.format||g,r:_(255,x(f.r,0)),g:_(255,x(f.g,0)),b:_(255,x(f.b,0)),a:p},this._originalInput=t,this._r=h.r,this._g=h.g,this._b=h.b,this._a=h.a,this._roundA=w(100*this._a)/100,this._format=e.format||h.format,this._gradientType=e.gradientType,this._r<1&&(this._r=w(this._r)),this._g<1&&(this._g=w(this._g)),this._b<1&&(this._b=w(this._b)),this._ok=h.ok,void(this._tc_id=y++)):new I(t,e);function C(t,e,n){return n<0&&(n+=1),1<n&&--n,n<1/6?t+6*(e-t)*n:n<.5?e:n<2/3?t+(e-t)*(2/3-n)*6:t}}function j(t,e,n){t=Y(t,255),e=Y(e,255),n=Y(n,255);var r,i=x(t,e,n),o=_(t,e,n),s=(i+o)/2;if(i==o)r=u=0;else{var a=i-o,u=.5<s?a/(2-i-o):a/(i+o);switch(i){case t:r=(e-n)/a+(e<n?6:0);break;case e:r=(n-t)/a+2;break;case n:r=(t-e)/a+4}r/=6}return{h:r,s:u,l:s}}function P(t,e,n){t=Y(t,255),e=Y(e,255),n=Y(n,255);var r,i=x(t,e,n),o=_(t,e,n),s=i,a=i-o,u=0===i?0:a/i;if(i==o)r=0;else{switch(i){case t:r=(e-n)/a+(e<n?6:0);break;case e:r=(n-t)/a+2;break;case n:r=(t-e)/a+4}r/=6}return{h:r,s:u,v:s}}function N(t,e,n,r){return t=[J(w(t).toString(16)),J(w(e).toString(16)),J(w(n).toString(16))],r&&t[0].charAt(0)==t[0].charAt(1)&&t[1].charAt(0)==t[1].charAt(1)&&t[2].charAt(0)==t[2].charAt(1)?t[0].charAt(0)+t[1].charAt(0)+t[2].charAt(0):t.join("")}function D(t,e,n,r){return[J(Math.round(255*parseFloat(r)).toString(16)),J(w(t).toString(16)),J(w(e).toString(16)),J(w(n).toString(16))].join("")}function R(t,e){return e=0===e?0:e||10,t=I(t).toHsl(),t.s-=e/100,t.s=K(t.s),I(t)}function M(t,e){return e=0===e?0:e||10,t=I(t).toHsl(),t.s+=e/100,t.s=K(t.s),I(t)}function W(t){return I(t).desaturate(100)}function L(t,e){return e=0===e?0:e||10,t=I(t).toHsl(),t.l+=e/100,t.l=K(t.l),I(t)}function F(t,e){return e=0===e?0:e||10,t=I(t).toRgb(),t.r=x(0,_(255,t.r-w(-e/100*255))),t.g=x(0,_(255,t.g-w(-e/100*255))),t.b=x(0,_(255,t.b-w(-e/100*255))),I(t)}function H(t,e){return e=0===e?0:e||10,t=I(t).toHsl(),t.l-=e/100,t.l=K(t.l),I(t)}function B(t,e){return t=I(t).toHsl(),e=(w(t.h)+e)%360,t.h=e<0?360+e:e,I(t)}function q(t){return t=I(t).toHsl(),t.h=(t.h+180)%360,I(t)}function z(t){var e=I(t).toHsl(),n=e.h;return[I(t),I({h:(n+120)%360,s:e.s,l:e.l}),I({h:(n+240)%360,s:e.s,l:e.l})]}function U(t){var e=I(t).toHsl(),n=e.h;return[I(t),I({h:(n+90)%360,s:e.s,l:e.l}),I({h:(n+180)%360,s:e.s,l:e.l}),I({h:(n+270)%360,s:e.s,l:e.l})]}function G(t){var e=I(t).toHsl(),n=e.h;return[I(t),I({h:(n+72)%360,s:e.s,l:e.l}),I({h:(n+216)%360,s:e.s,l:e.l})]}function V(t,e,n){e=e||6,n=n||30;var r=I(t).toHsl(),i=360/n,o=[I(t)];for(r.h=(r.h-(i*e>>1)+720)%360;--e;)r.h=(r.h+i)%360,o.push(I(r));return o}function $(t,e){e=e||6;t=I(t).toHsv();for(var n=t.h,r=t.s,i=t.v,o=[],s=1/e;e--;)o.push(I({h:n,s:r,v:i})),i=(i+s)%1;return o}function X(t){return t=parseFloat(t),isNaN(t)||t<0||1<t?1:t}function Y(t,e){var n="string"==typeof(t="string"==typeof(n=t)&&-1!=n.indexOf(".")&&1===parseFloat(n)?"100%":t)&&-1!=t.indexOf("%");return t=_(e,x(0,parseFloat(t))),n&&(t=parseInt(t*e,10)/100),b.abs(t-e)<1e-6?1:t%e/parseFloat(e)}function K(t){return _(1,x(0,t))}function Q(t){return parseInt(t,16)}function J(t){return 1==t.length?"0"+t:""+t}function Z(t){return t<=1?100*t+"%":t}t.fn.spectrum=function(e,n){var i,o;return"string"==typeof e?(i=this,o=Array.prototype.slice.call(arguments,1),this.each((function(){var n=r[t(this).data(A)];if(n){var s=n[e];if(!s)throw new Error("Spectrum: no such method: '"+e+"'");"get"==e?i=n.get():"container"==e?i=n.container:"option"==e?i=n.option.apply(n,o):"destroy"==e?(n.destroy(),t(this).removeData(A)):s.apply(n,o)}})),i):this.spectrum("destroy").each((function(){var n=l(this,t.extend({},e,t(this).data()));t(this).data(A,n.id)}))},t.fn.spectrum.load=!0,t.fn.spectrum.loadOpts={},t.fn.spectrum.draggable=d,t.fn.spectrum.defaults=n,t.fn.spectrum.inputTypeColorSupport=function e(){var n;return void 0===e._cachedResult&&(n=t("<input type='color'/>")[0],e._cachedResult="color"===n.type&&""!==n.value),e._cachedResult},t.spectrum={},t.spectrum.localization={},t.spectrum.palettes={},t.fn.spectrum.processNativeColorInputs=function(){var e=t("input[type=color]");e.length&&!g()&&e.spectrum({preferredFormat:"hex6"})},m=/^[\s,#]+/,v=/\s+$/,y=0,b=Math,w=b.round,_=b.min,x=b.max,C=b.random,I.prototype={isDark:function(){return this.getBrightness()<128},isLight:function(){return!this.isDark()},isValid:function(){return this._ok},getOriginalInput:function(){return this._originalInput},getFormat:function(){return this._format},getAlpha:function(){return this._a},getBrightness:function(){var t=this.toRgb();return(299*t.r+587*t.g+114*t.b)/1e3},setAlpha:function(t){return this._a=X(t),this._roundA=w(100*this._a)/100,this},toHsv:function(){var t=P(this._r,this._g,this._b);return{h:360*t.h,s:t.s,v:t.v,a:this._a}},toHsvString:function(){var t=P(this._r,this._g,this._b),e=w(360*t.h),n=w(100*t.s);t=w(100*t.v);return 1==this._a?"hsv("+e+", "+n+"%, "+t+"%)":"hsva("+e+", "+n+"%, "+t+"%, "+this._roundA+")"},toHsl:function(){var t=j(this._r,this._g,this._b);return{h:360*t.h,s:t.s,l:t.l,a:this._a}},toHslString:function(){var t=j(this._r,this._g,this._b),e=w(360*t.h),n=w(100*t.s);t=w(100*t.l);return 1==this._a?"hsl("+e+", "+n+"%, "+t+"%)":"hsla("+e+", "+n+"%, "+t+"%, "+this._roundA+")"},toHex:function(t){return N(this._r,this._g,this._b,t)},toHexString:function(t){return"#"+this.toHex(t)},toHex8:function(){return D(this._r,this._g,this._b,this._a)},toHex8String:function(){return"#"+this.toHex8()},toRgb:function(){return{r:w(this._r),g:w(this._g),b:w(this._b),a:this._a}},toRgbString:function(){return 1==this._a?"rgb("+w(this._r)+", "+w(this._g)+", "+w(this._b)+")":"rgba("+w(this._r)+", "+w(this._g)+", "+w(this._b)+", "+this._roundA+")"},toPercentageRgb:function(){return{r:w(100*Y(this._r,255))+"%",g:w(100*Y(this._g,255))+"%",b:w(100*Y(this._b,255))+"%",a:this._a}},toPercentageRgbString:function(){return 1==this._a?"rgb("+w(100*Y(this._r,255))+"%, "+w(100*Y(this._g,255))+"%, "+w(100*Y(this._b,255))+"%)":"rgba("+w(100*Y(this._r,255))+"%, "+w(100*Y(this._g,255))+"%, "+w(100*Y(this._b,255))+"%, "+this._roundA+")"},toName:function(){return 0===this._a?"transparent":!(this._a<1)&&(S[N(this._r,this._g,this._b,!0)]||!1)},toFilter:function(t){var e="#"+D(this._r,this._g,this._b,this._a),n=e;return"progid:DXImageTransform.Microsoft.gradient("+(this._gradientType?"GradientType = 1, ":"")+"startColorstr="+e+",endColorstr="+(n=t?I(t).toHex8String():n)+")"},toString:function(t){var e=!!t,n=(t=t||this._format,!1),r=this._a<1&&0<=this._a;return e||!r||"hex"!==t&&"hex6"!==t&&"hex3"!==t&&"name"!==t?("rgb"===t&&(n=this.toRgbString()),"prgb"===t&&(n=this.toPercentageRgbString()),"hex"!==t&&"hex6"!==t||(n=this.toHexString()),"hex3"===t&&(n=this.toHexString(!0)),"hex8"===t&&(n=this.toHex8String()),"name"===t&&(n=this.toName()),"hsl"===t&&(n=this.toHslString()),(n="hsv"===t?this.toHsvString():n)||this.toHexString()):"name"===t&&0===this._a?this.toName():this.toRgbString()},_applyModification:function(t,e){return t=t.apply(null,[this].concat([].slice.call(e))),this._r=t._r,this._g=t._g,this._b=t._b,this.setAlpha(t._a),this},lighten:function(){return this._applyModification(L,arguments)},brighten:function(){return this._applyModification(F,arguments)},darken:function(){return this._applyModification(H,arguments)},desaturate:function(){return this._applyModification(R,arguments)},saturate:function(){return this._applyModification(M,arguments)},greyscale:function(){return this._applyModification(W,arguments)},spin:function(){return this._applyModification(B,arguments)},_applyCombination:function(t,e){return t.apply(null,[this].concat([].slice.call(e)))},analogous:function(){return this._applyCombination(V,arguments)},complement:function(){return this._applyCombination(q,arguments)},monochromatic:function(){return this._applyCombination($,arguments)},splitcomplement:function(){return this._applyCombination(G,arguments)},triad:function(){return this._applyCombination(z,arguments)},tetrad:function(){return this._applyCombination(U,arguments)}},I.fromRatio=function(t,e){if("object"==typeof t){var n,r={};for(n in t)t.hasOwnProperty(n)&&(r[n]="a"===n?t[n]:Z(t[n]));t=r}return I(t,e)},I.equals=function(t,e){return!(!t||!e)&&I(t).toRgbString()==I(e).toRgbString()},I.random=function(){return I.fromRatio({r:C(),g:C(),b:C()})},I.mix=function(t,e,n){n=0===n?0:n||50;t=I(t).toRgb(),e=I(e).toRgb(),n/=100;var r=2*n-1,i=e.a-t.a;i=1-(r=(1+(r*i==-1?r:(r+i)/(1+r*i)))/2),r={r:e.r*r+t.r*i,g:e.g*r+t.g*i,b:e.b*r+t.b*i,a:e.a*n+t.a*(1-n)};return I(r)},I.readability=function(t,e){t=I(t),e=I(e);var n=t.toRgb(),r=e.toRgb();t=t.getBrightness(),e=e.getBrightness(),n=Math.max(n.r,r.r)-Math.min(n.r,r.r)+Math.max(n.g,r.g)-Math.min(n.g,r.g)+Math.max(n.b,r.b)-Math.min(n.b,r.b);return{brightness:Math.abs(t-e),color:n}},I.isReadable=function(t,e){return t=I.readability(t,e),125<t.brightness&&500<t.color},I.mostReadable=function(t,e){for(var n=null,r=0,i=!1,o=0;o<e.length;o++){var s=I.readability(t,e[o]),a=125<s.brightness&&500<s.color;s=s.brightness/125*3+s.color/500;(a&&!i||a&&i&&r<s||!a&&!i&&r<s)&&(i=a,r=s,n=I(e[o]))}return n},k=I.names={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"0ff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000",blanchedalmond:"ffebcd",blue:"00f",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",burntsienna:"ea7e5d",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"0ff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkgrey:"a9a9a9",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkslategrey:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dimgrey:"696969",dodgerblue:"1e90ff",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"f0f",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",grey:"808080",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgray:"d3d3d3",lightgreen:"90ee90",lightgrey:"d3d3d3",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslategray:"789",lightslategrey:"789",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"0f0",limegreen:"32cd32",linen:"faf0e6",magenta:"f0f",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370db",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"db7093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",rebeccapurple:"663399",red:"f00",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",slategrey:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",wheat:"f5deb3",white:"fff",whitesmoke:"f5f5f5",yellow:"ff0",yellowgreen:"9acd32"},S=I.hexNames=function(t){var e,n={};for(e in t)t.hasOwnProperty(e)&&(n[t[e]]=e);return n}(k),O="[\\s|\\(]+("+(E="(?:[-\\+]?\\d*\\.\\d+%?)|(?:[-\\+]?\\d+%?)")+")[,|\\s]+("+E+")[,|\\s]+("+E+")\\s*\\)?",E="[\\s|\\(]+("+E+")[,|\\s]+("+E+")[,|\\s]+("+E+")[,|\\s]+("+E+")\\s*\\)?",T={rgb:new RegExp("rgb"+O),rgba:new RegExp("rgba"+E),hsl:new RegExp("hsl"+O),hsla:new RegExp("hsla"+E),hsv:new RegExp("hsv"+O),hsva:new RegExp("hsva"+E),hex3:/^([0-9a-fA-F]{1})([0-9a-fA-F]{1})([0-9a-fA-F]{1})$/,hex6:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/,hex8:/^([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})([0-9a-fA-F]{2})$/},window.tinycolor=I,t((function(){t.fn.spectrum.load&&t.fn.spectrum.processNativeColorInputs()}))})),function(){var t=function(t){t.ui=t.ui||{},t.ui.version="1.12.1";var e,n,r,i,o,s,a,u,c,l,h=0,f=Array.prototype.slice;function p(t,e,n){return[parseFloat(t[0])*(c.test(t[0])?e/100:1),parseFloat(t[1])*(c.test(t[1])?n/100:1)]}function d(e,n){return parseInt(t.css(e,n),10)||0}t.cleanData=(e=t.cleanData,function(n){for(var r,i,o=0;null!=(i=n[o]);o++)try{(r=t._data(i,"events"))&&r.remove&&t(i).triggerHandler("remove")}catch(n){}e(n)}),t.widget=function(e,n,r){var i,o,s,a={},u=e.split(".")[0],c=u+"-"+(e=e.split(".")[1]);return r||(r=n,n=t.Widget),t.isArray(r)&&(r=t.extend.apply(null,[{}].concat(r))),t.expr[":"][c.toLowerCase()]=function(e){return!!t.data(e,c)},t[u]=t[u]||{},i=t[u][e],o=t[u][e]=function(t,e){if(!this._createWidget)return new o(t,e);arguments.length&&this._createWidget(t,e)},t.extend(o,i,{version:r.version,_proto:t.extend({},r),_childConstructors:[]}),(s=new n).options=t.widget.extend({},s.options),t.each(r,(function(e,r){function i(){return n.prototype[e].apply(this,arguments)}function o(t){return n.prototype[e].apply(this,t)}t.isFunction(r)?a[e]=function(){var t,e=this._super,n=this._superApply;return this._super=i,this._superApply=o,t=r.apply(this,arguments),this._super=e,this._superApply=n,t}:a[e]=r})),o.prototype=t.widget.extend(s,{widgetEventPrefix:i&&s.widgetEventPrefix||e},a,{constructor:o,namespace:u,widgetName:e,widgetFullName:c}),i?(t.each(i._childConstructors,(function(e,n){var r=n.prototype;t.widget(r.namespace+"."+r.widgetName,o,n._proto)})),delete i._childConstructors):n._childConstructors.push(o),t.widget.bridge(e,o),o},t.widget.extend=function(e){for(var n,r,i=f.call(arguments,1),o=0,s=i.length;o<s;o++)for(n in i[o])r=i[o][n],i[o].hasOwnProperty(n)&&void 0!==r&&(t.isPlainObject(r)?e[n]=t.isPlainObject(e[n])?t.widget.extend({},e[n],r):t.widget.extend({},r):e[n]=r);return e},t.widget.bridge=function(e,n){var r=n.prototype.widgetFullName||e;t.fn[e]=function(i){var o="string"==typeof i,s=f.call(arguments,1),a=this;return o?this.length||"instance"!==i?this.each((function(){var n,o=t.data(this,r);return"instance"===i?(a=o,!1):o?t.isFunction(o[i])&&"_"!==i.charAt(0)?(n=o[i].apply(o,s))!==o&&void 0!==n?(a=n&&n.jquery?a.pushStack(n.get()):n,!1):void 0:t.error("no such method '"+i+"' for "+e+" widget instance"):t.error("cannot call methods on "+e+" prior to initialization; attempted to call method '"+i+"'")})):a=void 0:(s.length&&(i=t.widget.extend.apply(null,[i].concat(s))),this.each((function(){var e=t.data(this,r);e?(e.option(i||{}),e._init&&e._init()):t.data(this,r,new n(i,this))}))),a}},t.Widget=function(){},t.Widget._childConstructors=[],t.Widget.prototype={widgetName:"widget",widgetEventPrefix:"",defaultElement:"<div>",options:{classes:{},disabled:!1,create:null},_createWidget:function(e,n){n=t(n||this.defaultElement||this)[0],this.element=t(n),this.uuid=h++,this.eventNamespace="."+this.widgetName+this.uuid,this.bindings=t(),this.hoverable=t(),this.focusable=t(),this.classesElementLookup={},n!==this&&(t.data(n,this.widgetFullName,this),this._on(!0,this.element,{remove:function(t){t.target===n&&this.destroy()}}),this.document=t(n.style?n.ownerDocument:n.document||n),this.window=t(this.document[0].defaultView||this.document[0].parentWindow)),this.options=t.widget.extend({},this.options,this._getCreateOptions(),e),this._create(),this.options.disabled&&this._setOptionDisabled(this.options.disabled),this._trigger("create",null,this._getCreateEventData()),this._init()},_getCreateOptions:function(){return{}},_getCreateEventData:t.noop,_create:t.noop,_init:t.noop,destroy:function(){var e=this;this._destroy(),t.each(this.classesElementLookup,(function(t,n){e._removeClass(n,t)})),this.element.off(this.eventNamespace).removeData(this.widgetFullName),this.widget().off(this.eventNamespace).removeAttr("aria-disabled"),this.bindings.off(this.eventNamespace)},_destroy:t.noop,widget:function(){return this.element},option:function(e,n){var r,i,o,s=e;if(0===arguments.length)return t.widget.extend({},this.options);if("string"==typeof e)if(s={},e=(r=e.split(".")).shift(),r.length){for(i=s[e]=t.widget.extend({},this.options[e]),o=0;o<r.length-1;o++)i[r[o]]=i[r[o]]||{},i=i[r[o]];if(e=r.pop(),1===arguments.length)return void 0===i[e]?null:i[e];i[e]=n}else{if(1===arguments.length)return void 0===this.options[e]?null:this.options[e];s[e]=n}return this._setOptions(s),this},_setOptions:function(t){for(var e in t)this._setOption(e,t[e]);return this},_setOption:function(t,e){return"classes"===t&&this._setOptionClasses(e),this.options[t]=e,"disabled"===t&&this._setOptionDisabled(e),this},_setOptionClasses:function(e){var n,r,i;for(n in e)i=this.classesElementLookup[n],e[n]!==this.options.classes[n]&&i&&i.length&&(r=t(i.get()),this._removeClass(i,n),r.addClass(this._classes({element:r,keys:n,classes:e,add:!0})))},_setOptionDisabled:function(t){this._toggleClass(this.widget(),this.widgetFullName+"-disabled",null,!!t),t&&(this._removeClass(this.hoverable,null,"ui-state-hover"),this._removeClass(this.focusable,null,"ui-state-focus"))},enable:function(){return this._setOptions({disabled:!1})},disable:function(){return this._setOptions({disabled:!0})},_classes:function(e){var n=[],r=this;function i(i,o){for(var s,a=0;a<i.length;a++)s=r.classesElementLookup[i[a]]||t(),s=e.add?t(t.unique(s.get().concat(e.element.get()))):t(s.not(e.element).get()),r.classesElementLookup[i[a]]=s,n.push(i[a]),o&&e.classes[i[a]]&&n.push(e.classes[i[a]])}return e=t.extend({element:this.element,classes:this.options.classes||{}},e),this._on(e.element,{remove:"_untrackClassesElement"}),e.keys&&i(e.keys.match(/\S+/g)||[],!0),e.extra&&i(e.extra.match(/\S+/g)||[]),n.join(" ")},_untrackClassesElement:function(e){var n=this;t.each(n.classesElementLookup,(function(r,i){-1!==t.inArray(e.target,i)&&(n.classesElementLookup[r]=t(i.not(e.target).get()))}))},_removeClass:function(t,e,n){return this._toggleClass(t,e,n,!1)},_addClass:function(t,e,n){return this._toggleClass(t,e,n,!0)},_toggleClass:function(t,e,n,r){var i="string"==typeof t||null===t;e={extra:i?e:n,keys:i?t:e,element:i?this.element:t,add:r="boolean"==typeof r?r:n};return e.element.toggleClass(this._classes(e),r),this},_on:function(e,n,r){var i,o=this;"boolean"!=typeof e&&(r=n,n=e,e=!1),r?(n=i=t(n),this.bindings=this.bindings.add(n)):(r=n,n=this.element,i=this.widget()),t.each(r,(function(r,s){function a(){if(e||!0!==o.options.disabled&&!t(this).hasClass("ui-state-disabled"))return("string"==typeof s?o[s]:s).apply(o,arguments)}"string"!=typeof s&&(a.guid=s.guid=s.guid||a.guid||t.guid++);r=r.match(/^([\w:-]*)\s*(.*)$/);var u=r[1]+o.eventNamespace;r=r[2];r?i.on(u,r,a):n.on(u,a)}))},_off:function(e,n){n=(n||"").split(" ").join(this.eventNamespace+" ")+this.eventNamespace,e.off(n).off(n),this.bindings=t(this.bindings.not(e).get()),this.focusable=t(this.focusable.not(e).get()),this.hoverable=t(this.hoverable.not(e).get())},_delay:function(t,e){var n=this;return setTimeout((function(){return("string"==typeof t?n[t]:t).apply(n,arguments)}),e||0)},_hoverable:function(e){this.hoverable=this.hoverable.add(e),this._on(e,{mouseenter:function(e){this._addClass(t(e.currentTarget),null,"ui-state-hover")},mouseleave:function(e){this._removeClass(t(e.currentTarget),null,"ui-state-hover")}})},_focusable:function(e){this.focusable=this.focusable.add(e),this._on(e,{focusin:function(e){this._addClass(t(e.currentTarget),null,"ui-state-focus")},focusout:function(e){this._removeClass(t(e.currentTarget),null,"ui-state-focus")}})},_trigger:function(e,n,r){var i,o,s=this.options[e];if(r=r||{},(n=t.Event(n)).type=(e===this.widgetEventPrefix?e:this.widgetEventPrefix+e).toLowerCase(),n.target=this.element[0],o=n.originalEvent)for(i in o)i in n||(n[i]=o[i]);return this.element.trigger(n,r),!(t.isFunction(s)&&!1===s.apply(this.element[0],[n].concat(r))||n.isDefaultPrevented())}},t.each({show:"fadeIn",hide:"fadeOut"},(function(e,n){t.Widget.prototype["_"+e]=function(r,i,o){var s,a=(i="string"==typeof i?{effect:i}:i)?!0!==i&&"number"!=typeof i&&i.effect||n:e;"number"==typeof(i=i||{})&&(i={duration:i}),s=!t.isEmptyObject(i),i.complete=o,i.delay&&r.delay(i.delay),s&&t.effects&&t.effects.effect[a]?r[e](i):a!==e&&r[a]?r[a](i.duration,i.easing,o):r.queue((function(n){t(this)[e](),o&&o.call(r[0]),n()}))}})),t.widget,r=Math.max,i=Math.abs,o=/left|center|right/,s=/top|center|bottom/,a=/[\+\-]\d+(\.[\d]+)?%?/,u=/^\w+/,c=/%$/,l=t.fn.position,t.position={scrollbarWidth:function(){var e,r,i;return void 0!==n?n:(i=(r=t("<div style='display:block;position:absolute;width:50px;height:50px;overflow:hidden;'><div style='height:100px;width:auto;'></div></div>")).children()[0],t("body").append(r),e=i.offsetWidth,r.css("overflow","scroll"),e===(i=i.offsetWidth)&&(i=r[0].clientWidth),r.remove(),n=e-i)},getScrollInfo:function(e){var n=e.isWindow||e.isDocument?"":e.element.css("overflow-x"),r=e.isWindow||e.isDocument?"":e.element.css("overflow-y");n="scroll"===n||"auto"===n&&e.width<e.element[0].scrollWidth;return{width:"scroll"===r||"auto"===r&&e.height<e.element[0].scrollHeight?t.position.scrollbarWidth():0,height:n?t.position.scrollbarWidth():0}},getWithinInfo:function(e){var n=t(e||window),r=t.isWindow(n[0]),i=!!n[0]&&9===n[0].nodeType;return{element:n,isWindow:r,isDocument:i,offset:r||i?{left:0,top:0}:t(e).offset(),scrollLeft:n.scrollLeft(),scrollTop:n.scrollTop(),width:n.outerWidth(),height:n.outerHeight()}}},t.fn.position=function(e){if(!e||!e.of)return l.apply(this,arguments);e=t.extend({},e);var n,c,h,f,g,m,v=t(e.of),y=t.position.getWithinInfo(e.within),b=t.position.getScrollInfo(y),w=(e.collision||"flip").split(" "),_={},x=9===(x=(m=v)[0]).nodeType?{width:m.width(),height:m.height(),offset:{top:0,left:0}}:t.isWindow(x)?{width:m.width(),height:m.height(),offset:{top:m.scrollTop(),left:m.scrollLeft()}}:x.preventDefault?{width:0,height:0,offset:{top:x.pageY,left:x.pageX}}:{width:m.outerWidth(),height:m.outerHeight(),offset:m.offset()};return v[0].preventDefault&&(e.at="left top"),c=x.width,h=x.height,g=t.extend({},f=x.offset),t.each(["my","at"],(function(){var t,n,r=(e[this]||"").split(" ");(r=1===r.length?o.test(r[0])?r.concat(["center"]):s.test(r[0])?["center"].concat(r):["center","center"]:r)[0]=o.test(r[0])?r[0]:"center",r[1]=s.test(r[1])?r[1]:"center",t=a.exec(r[0]),n=a.exec(r[1]),_[this]=[t?t[0]:0,n?n[0]:0],e[this]=[u.exec(r[0])[0],u.exec(r[1])[0]]})),1===w.length&&(w[1]=w[0]),"right"===e.at[0]?g.left+=c:"center"===e.at[0]&&(g.left+=c/2),"bottom"===e.at[1]?g.top+=h:"center"===e.at[1]&&(g.top+=h/2),n=p(_.at,c,h),g.left+=n[0],g.top+=n[1],this.each((function(){var o,s,a=t(this),u=a.outerWidth(),l=a.outerHeight(),m=d(this,"marginLeft"),x=d(this,"marginTop"),C=u+m+d(this,"marginRight")+b.width,k=l+x+d(this,"marginBottom")+b.height,S=t.extend({},g),O=p(_.my,a.outerWidth(),a.outerHeight());"right"===e.my[0]?S.left-=u:"center"===e.my[0]&&(S.left-=u/2),"bottom"===e.my[1]?S.top-=l:"center"===e.my[1]&&(S.top-=l/2),S.left+=O[0],S.top+=O[1],o={marginLeft:m,marginTop:x},t.each(["left","top"],(function(r,i){t.ui.position[w[r]]&&t.ui.position[w[r]][i](S,{targetWidth:c,targetHeight:h,elemWidth:u,elemHeight:l,collisionPosition:o,collisionWidth:C,collisionHeight:k,offset:[n[0]+O[0],n[1]+O[1]],my:e.my,at:e.at,within:y,elem:a})})),e.using&&(s=function(t){var n=f.left-S.left,o=n+c-u,s=f.top-S.top,p=s+h-l,d={target:{element:v,left:f.left,top:f.top,width:c,height:h},element:{element:a,left:S.left,top:S.top,width:u,height:l},horizontal:o<0?"left":0<n?"right":"center",vertical:p<0?"top":0<s?"bottom":"middle"};c<u&&i(n+o)<c&&(d.horizontal="center"),h<l&&i(s+p)<h&&(d.vertical="middle"),r(i(n),i(o))>r(i(s),i(p))?d.important="horizontal":d.important="vertical",e.using.call(this,t,d)}),a.offset(t.extend(S,{using:s}))}))},t.ui.position={fit:{left:function(t,e){var n,i=e.within,o=i.isWindow?i.scrollLeft:i.offset.left,s=(i=i.width,t.left-e.collisionPosition.marginLeft),a=o-s,u=s+e.collisionWidth-i-o;e.collisionWidth>i?0<a&&u<=0?(n=t.left+a+e.collisionWidth-i-o,t.left+=a-n):t.left=!(0<u&&a<=0)&&u<a?o+i-e.collisionWidth:o:0<a?t.left+=a:0<u?t.left-=u:t.left=r(t.left-s,t.left)},top:function(t,e){var n,i=e.within,o=(i=i.isWindow?i.scrollTop:i.offset.top,e.within.height),s=t.top-e.collisionPosition.marginTop,a=i-s,u=s+e.collisionHeight-o-i;e.collisionHeight>o?0<a&&u<=0?(n=t.top+a+e.collisionHeight-o-i,t.top+=a-n):t.top=!(0<u&&a<=0)&&u<a?i+o-e.collisionHeight:i:0<a?t.top+=a:0<u?t.top-=u:t.top=r(t.top-s,t.top)}},flip:{left:function(t,e){var n=e.within,r=n.offset.left+n.scrollLeft,o=n.width,s=(n=n.isWindow?n.scrollLeft:n.offset.left,t.left-e.collisionPosition.marginLeft),a=s-n,u=(s=s+e.collisionWidth-o-n,"left"===e.my[0]?-e.elemWidth:"right"===e.my[0]?e.elemWidth:0),c="left"===e.at[0]?e.targetWidth:"right"===e.at[0]?-e.targetWidth:0,l=-2*e.offset[0];a<0?((o=t.left+u+c+l+e.collisionWidth-o-r)<0||o<i(a))&&(t.left+=u+c+l):0<s&&(0<(r=t.left-e.collisionPosition.marginLeft+u+c+l-n)||i(r)<s)&&(t.left+=u+c+l)},top:function(t,e){var n=e.within,r=n.offset.top+n.scrollTop,o=n.height,s=(n=n.isWindow?n.scrollTop:n.offset.top,t.top-e.collisionPosition.marginTop),a=s-n,u=(s=s+e.collisionHeight-o-n,"top"===e.my[1]?-e.elemHeight:"bottom"===e.my[1]?e.elemHeight:0),c="top"===e.at[1]?e.targetHeight:"bottom"===e.at[1]?-e.targetHeight:0,l=-2*e.offset[1];a<0?((o=t.top+u+c+l+e.collisionHeight-o-r)<0||o<i(a))&&(t.top+=u+c+l):0<s&&(0<(r=t.top-e.collisionPosition.marginTop+u+c+l-n)||i(r)<s)&&(t.top+=u+c+l)}},flipfit:{left:function(){t.ui.position.flip.left.apply(this,arguments),t.ui.position.fit.left.apply(this,arguments)},top:function(){t.ui.position.flip.top.apply(this,arguments),t.ui.position.fit.top.apply(this,arguments)}}},t.ui.position,t.fn.form=function(){return"string"==typeof this[0].form?this.closest("form"):t(this[0].form)},t.ui.formResetMixin={_formResetHandler:function(){var e=t(this);setTimeout((function(){var n=e.data("ui-form-reset-instances");t.each(n,(function(){this.refresh()}))}))},_bindFormResetHandler:function(){var t;this.form=this.element.form(),this.form.length&&((t=this.form.data("ui-form-reset-instances")||[]).length||this.form.on("reset.ui-form-reset",this._formResetHandler),t.push(this),this.form.data("ui-form-reset-instances",t))},_unbindFormResetHandler:function(){var e;this.form.length&&((e=this.form.data("ui-form-reset-instances")).splice(t.inArray(this,e),1),e.length?this.form.data("ui-form-reset-instances",e):this.form.removeData("ui-form-reset-instances").off("reset.ui-form-reset"))}},t.ui.keyCode={BACKSPACE:8,COMMA:188,DELETE:46,DOWN:40,END:35,ENTER:13,ESCAPE:27,HOME:36,LEFT:37,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,RIGHT:39,SPACE:32,TAB:9,UP:38},t.ui.escapeSelector=(g=/([!"#$%&'()*+,./:;<=>?@[\]^`{|}~])/g,function(t){return t.replace(g,"\\$1")}),t.fn.labels=function(){var e,n,r;return this[0].labels&&this[0].labels.length?this.pushStack(this[0].labels):(n=this.eq(0).parents("label"),(e=this.attr("id"))&&(r=(r=this.eq(0).parents().last()).add((r.length?r:this).siblings()),e="label[for='"+t.ui.escapeSelector(e)+"']",n=n.add(r.find(e).addBack(e))),this.pushStack(n))},t.fn.extend({uniqueId:(m=0,function(){return this.each((function(){this.id||(this.id="ui-id-"+ ++m)}))}),removeUniqueId:function(){return this.each((function(){/^ui-id-\d+$/.test(this.id)&&t(this).removeAttr("id")}))}});var g,m,v,y=/ui-corner-([a-z]){2,6}/g,b=(t.widget("ui.controlgroup",{version:"1.12.1",defaultElement:"<div>",options:{direction:"horizontal",disabled:null,onlyVisible:!0,items:{button:"input[type=button], input[type=submit], input[type=reset], button, a",controlgroupLabel:".ui-controlgroup-label",checkboxradio:"input[type='checkbox'], input[type='radio']",selectmenu:"select",spinner:".ui-spinner-input"}},_create:function(){this._enhance()},_enhance:function(){this.element.attr("role","toolbar"),this.refresh()},_destroy:function(){this._callChildMethod("destroy"),this.childWidgets.removeData("ui-controlgroup-data"),this.element.removeAttr("role"),this.options.items.controlgroupLabel&&this.element.find(this.options.items.controlgroupLabel).find(".ui-controlgroup-label-contents").contents().unwrap()},_initWidgets:function(){var e=this,n=[];t.each(this.options.items,(function(r,i){var o,s;if(i)return"controlgroupLabel"===r?((o=e.element.find(i)).each((function(){var e=t(this);e.children(".ui-controlgroup-label-contents").length||e.contents().wrapAll("<span class='ui-controlgroup-label-contents'></span>")})),e._addClass(o,null,"ui-widget ui-widget-content ui-state-default"),void(n=n.concat(o.get()))):void(t.fn[r]&&(s=e["_"+r+"Options"]?e["_"+r+"Options"]("middle"):{classes:{}},e.element.find(i).each((function(){var i=t(this),o=i[r]("instance"),a=t.widget.extend({},s);"button"===r&&i.parent(".ui-spinner").length||((o=o||i[r]()[r]("instance"))&&(a.classes=e._resolveClassesValues(a.classes,o)),i[r](a),a=i[r]("widget"),t.data(a[0],"ui-controlgroup-data",o||i[r]("instance")),n.push(a[0]))}))))})),this.childWidgets=t(t.unique(n)),this._addClass(this.childWidgets,"ui-controlgroup-item")},_callChildMethod:function(e){this.childWidgets.each((function(){var n=t(this).data("ui-controlgroup-data");n&&n[e]&&n[e]()}))},_updateCornerClass:function(t,e){e=this._buildSimpleOptions(e,"label").classes.label,this._removeClass(t,null,"ui-corner-top ui-corner-bottom ui-corner-left ui-corner-right ui-corner-all"),this._addClass(t,null,e)},_buildSimpleOptions:function(t,e){var n="vertical"===this.options.direction,r={classes:{}};return r.classes[e]={middle:"",first:"ui-corner-"+(n?"top":"left"),last:"ui-corner-"+(n?"bottom":"right"),only:"ui-corner-all"}[t],r},_spinnerOptions:function(t){return t=this._buildSimpleOptions(t,"ui-spinner"),t.classes["ui-spinner-up"]="",t.classes["ui-spinner-down"]="",t},_buttonOptions:function(t){return this._buildSimpleOptions(t,"ui-button")},_checkboxradioOptions:function(t){return this._buildSimpleOptions(t,"ui-checkboxradio-label")},_selectmenuOptions:function(t){var e="vertical"===this.options.direction;return{width:!!e&&"auto",classes:{middle:{"ui-selectmenu-button-open":"","ui-selectmenu-button-closed":""},first:{"ui-selectmenu-button-open":"ui-corner-"+(e?"top":"tl"),"ui-selectmenu-button-closed":"ui-corner-"+(e?"top":"left")},last:{"ui-selectmenu-button-open":e?"":"ui-corner-tr","ui-selectmenu-button-closed":"ui-corner-"+(e?"bottom":"right")},only:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"}}[t]}},_resolveClassesValues:function(e,n){var r={};return t.each(e,(function(i){var o=n.options.classes[i]||"";o=t.trim(o.replace(y,""));r[i]=(o+" "+e[i]).replace(/\s+/g," ")})),r},_setOption:function(t,e){"direction"===t&&this._removeClass("ui-controlgroup-"+this.options.direction),this._super(t,e),"disabled"!==t?this.refresh():this._callChildMethod(e?"disable":"enable")},refresh:function(){var e,n=this;this._addClass("ui-controlgroup ui-controlgroup-"+this.options.direction),"horizontal"===this.options.direction&&this._addClass(null,"ui-helper-clearfix"),this._initWidgets(),e=this.childWidgets,(e=this.options.onlyVisible?e.filter(":visible"):e).length&&(t.each(["first","last"],(function(t,r){var i,o=e[r]().data("ui-controlgroup-data");o&&n["_"+o.widgetName+"Options"]?((i=n["_"+o.widgetName+"Options"](1===e.length?"only":r)).classes=n._resolveClassesValues(i.classes,o),o.element[o.widgetName](i)):n._updateCornerClass(e[r](),r)})),this._callChildMethod("refresh"))}}),t.widget("ui.checkboxradio",[t.ui.formResetMixin,{version:"1.12.1",options:{disabled:null,label:null,icon:!0,classes:{"ui-checkboxradio-label":"ui-corner-all","ui-checkboxradio-icon":"ui-corner-all"}},_getCreateOptions:function(){var e,n=this,r=this._super()||{};return this._readType(),e=this.element.labels(),this.label=t(e[e.length-1]),this.label.length||t.error("No label found for checkboxradio widget"),this.originalLabel="",this.label.contents().not(this.element[0]).each((function(){n.originalLabel+=3===this.nodeType?t(this).text():this.outerHTML})),this.originalLabel&&(r.label=this.originalLabel),null!=(e=this.element[0].disabled)&&(r.disabled=e),r},_create:function(){var t=this.element[0].checked;this._bindFormResetHandler(),null==this.options.disabled&&(this.options.disabled=this.element[0].disabled),this._setOption("disabled",this.options.disabled),this._addClass("ui-checkboxradio","ui-helper-hidden-accessible"),this._addClass(this.label,"ui-checkboxradio-label","ui-button ui-widget"),"radio"===this.type&&this._addClass(this.label,"ui-checkboxradio-radio-label"),this.options.label&&this.options.label!==this.originalLabel?this._updateLabel():this.originalLabel&&(this.options.label=this.originalLabel),this._enhance(),t&&(this._addClass(this.label,"ui-checkboxradio-checked","ui-state-active"),this.icon)&&this._addClass(this.icon,null,"ui-state-hover"),this._on({change:"_toggleClasses",focus:function(){this._addClass(this.label,null,"ui-state-focus ui-visual-focus")},blur:function(){this._removeClass(this.label,null,"ui-state-focus ui-visual-focus")}})},_readType:function(){var e=this.element[0].nodeName.toLowerCase();this.type=this.element[0].type,"input"===e&&/radio|checkbox/.test(this.type)||t.error("Can't create checkboxradio on element.nodeName="+e+" and element.type="+this.type)},_enhance:function(){this._updateIcon(this.element[0].checked)},widget:function(){return this.label},_getRadioGroup:function(){var e=this.element[0].name,n="input[name='"+t.ui.escapeSelector(e)+"']";return e?(this.form.length?t(this.form[0].elements).filter(n):t(n).filter((function(){return 0===t(this).form().length}))).not(this.element):t([])},_toggleClasses:function(){var e=this.element[0].checked;this._toggleClass(this.label,"ui-checkboxradio-checked","ui-state-active",e),this.options.icon&&"checkbox"===this.type&&this._toggleClass(this.icon,null,"ui-icon-check ui-state-checked",e)._toggleClass(this.icon,null,"ui-icon-blank",!e),"radio"===this.type&&this._getRadioGroup().each((function(){var e=t(this).checkboxradio("instance");e&&e._removeClass(e.label,"ui-checkboxradio-checked","ui-state-active")}))},_destroy:function(){this._unbindFormResetHandler(),this.icon&&(this.icon.remove(),this.iconSpace.remove())},_setOption:function(t,e){"label"===t&&!e||(this._super(t,e),"disabled"===t?(this._toggleClass(this.label,null,"ui-state-disabled",e),this.element[0].disabled=e):this.refresh())},_updateIcon:function(e){var n="ui-icon ui-icon-background ";this.options.icon?(this.icon||(this.icon=t("<span>"),this.iconSpace=t("<span> </span>"),this._addClass(this.iconSpace,"ui-checkboxradio-icon-space")),"checkbox"===this.type?(n+=e?"ui-icon-check ui-state-checked":"ui-icon-blank",this._removeClass(this.icon,null,e?"ui-icon-blank":"ui-icon-check")):n+="ui-icon-blank",this._addClass(this.icon,"ui-checkboxradio-icon",n),e||this._removeClass(this.icon,null,"ui-icon-check ui-state-checked"),this.icon.prependTo(this.label).after(this.iconSpace)):void 0!==this.icon&&(this.icon.remove(),this.iconSpace.remove(),delete this.icon)},_updateLabel:function(){var t=this.label.contents().not(this.element[0]);this.icon&&(t=t.not(this.icon[0])),(t=this.iconSpace?t.not(this.iconSpace[0]):t).remove(),this.label.append(this.options.label)},refresh:function(){var t=this.element[0].checked,e=this.element[0].disabled;this._updateIcon(t),this._toggleClass(this.label,"ui-checkboxradio-checked","ui-state-active",t),null!==this.options.label&&this._updateLabel(),e!==this.options.disabled&&this._setOptions({disabled:e})}}]),t.ui.checkboxradio,t.widget("ui.button",{version:"1.12.1",defaultElement:"<button>",options:{classes:{"ui-button":"ui-corner-all"},disabled:null,icon:null,iconPosition:"beginning",label:null,showLabel:!0},_getCreateOptions:function(){var t,e=this._super()||{};return this.isInput=this.element.is("input"),null!=(t=this.element[0].disabled)&&(e.disabled=t),this.originalLabel=this.isInput?this.element.val():this.element.html(),this.originalLabel&&(e.label=this.originalLabel),e},_create:function(){!this.option.showLabel&!this.options.icon&&(this.options.showLabel=!0),null==this.options.disabled&&(this.options.disabled=this.element[0].disabled||!1),this.hasTitle=!!this.element.attr("title"),this.options.label&&this.options.label!==this.originalLabel&&(this.isInput?this.element.val(this.options.label):this.element.html(this.options.label)),this._addClass("ui-button","ui-widget"),this._setOption("disabled",this.options.disabled),this._enhance(),this.element.is("a")&&this._on({keyup:function(e){e.keyCode===t.ui.keyCode.SPACE&&(e.preventDefault(),this.element[0].click?this.element[0].click():this.element.trigger("click"))}})},_enhance:function(){this.element.is("button")||this.element.attr("role","button"),this.options.icon&&(this._updateIcon("icon",this.options.icon),this._updateTooltip())},_updateTooltip:function(){this.title=this.element.attr("title"),this.options.showLabel||this.title||this.element.attr("title",this.options.label)},_updateIcon:function(e,n){e="iconPosition"!==e;var r=e?this.options.iconPosition:n,i="top"===r||"bottom"===r;this.icon?e&&this._removeClass(this.icon,null,this.options.icon):(this.icon=t("<span>"),this._addClass(this.icon,"ui-button-icon","ui-icon"),this.options.showLabel||this._addClass("ui-button-icon-only")),e&&this._addClass(this.icon,null,n),this._attachIcon(r),i?(this._addClass(this.icon,null,"ui-widget-icon-block"),this.iconSpace&&this.iconSpace.remove()):(this.iconSpace||(this.iconSpace=t("<span> </span>"),this._addClass(this.iconSpace,"ui-button-icon-space")),this._removeClass(this.icon,null,"ui-wiget-icon-block"),this._attachIconSpace(r))},_destroy:function(){this.element.removeAttr("role"),this.icon&&this.icon.remove(),this.iconSpace&&this.iconSpace.remove(),this.hasTitle||this.element.removeAttr("title")},_attachIconSpace:function(t){this.icon[/^(?:end|bottom)/.test(t)?"before":"after"](this.iconSpace)},_attachIcon:function(t){this.element[/^(?:end|bottom)/.test(t)?"append":"prepend"](this.icon)},_setOptions:function(t){var e=(void 0===t.showLabel?this.options:t).showLabel,n=(void 0===t.icon?this.options:t).icon;e||n||(t.showLabel=!0),this._super(t)},_setOption:function(t,e){"icon"===t&&(e?this._updateIcon(t,e):this.icon&&(this.icon.remove(),this.iconSpace)&&this.iconSpace.remove()),"iconPosition"===t&&this._updateIcon(t,e),"showLabel"===t&&(this._toggleClass("ui-button-icon-only",null,!e),this._updateTooltip()),"label"===t&&(this.isInput?this.element.val(e):(this.element.html(e),this.icon&&(this._attachIcon(this.options.iconPosition),this._attachIconSpace(this.options.iconPosition)))),this._super(t,e),"disabled"===t&&(this._toggleClass(null,"ui-state-disabled",e),this.element[0].disabled=e)&&this.element.blur()},refresh:function(){var t=this.element.is("input, button")?this.element[0].disabled:this.element.hasClass("ui-button-disabled");t!==this.options.disabled&&this._setOptions({disabled:t}),this._updateTooltip()}}),!1!==t.uiBackCompat&&(t.widget("ui.button",t.ui.button,{options:{text:!0,icons:{primary:null,secondary:null}},_create:function(){this.options.showLabel&&!this.options.text&&(this.options.showLabel=this.options.text),!this.options.showLabel&&this.options.text&&(this.options.text=this.options.showLabel),this.options.icon||!this.options.icons.primary&&!this.options.icons.secondary?this.options.icon&&(this.options.icons.primary=this.options.icon):this.options.icons.primary?this.options.icon=this.options.icons.primary:(this.options.icon=this.options.icons.secondary,this.options.iconPosition="end"),this._super()},_setOption:function(t,e){"text"!==t?("showLabel"===t&&(this.options.text=e),"icon"===t&&(this.options.icons.primary=e),"icons"===t&&(e.primary?(this._super("icon",e.primary),this._super("iconPosition","beginning")):e.secondary&&(this._super("icon",e.secondary),this._super("iconPosition","end"))),this._superApply(arguments)):this._super("showLabel",e)}}),t.fn.button=(v=t.fn.button,function(){return!this.length||this.length&&"INPUT"!==this[0].tagName||this.length&&"INPUT"===this[0].tagName&&"checkbox"!==this.attr("type")&&"radio"!==this.attr("type")?v.apply(this,arguments):(t.ui.checkboxradio||t.error("Checkboxradio widget missing"),0===arguments.length?this.checkboxradio({icon:!1}):this.checkboxradio.apply(this,arguments))}),t.fn.buttonset=function(){return t.ui.controlgroup||t.error("Controlgroup widget missing"),"option"===arguments[0]&&"items"===arguments[1]&&arguments[2]?this.controlgroup.apply(this,[arguments[0],"items.button",arguments[2]]):"option"===arguments[0]&&"items"===arguments[1]?this.controlgroup.apply(this,[arguments[0],"items.button"]):("object"==typeof arguments[0]&&arguments[0].items&&(arguments[0].items={button:arguments[0].items}),this.controlgroup.apply(this,arguments))}),t.ui.button,t.ui.safeActiveElement=function(t){var e;try{e=t.activeElement}catch(m){e=t.body}return(e=e||t.body).nodeName?e:t.body},t.widget("ui.menu",{version:"1.12.1",defaultElement:"<ul>",delay:300,options:{icons:{submenu:"ui-icon-caret-1-e"},items:"> *",menus:"ul",position:{my:"left top",at:"right top"},role:"menu",blur:null,focus:null,select:null},_create:function(){this.activeMenu=this.element,this.mouseHandled=!1,this.element.uniqueId().attr({role:this.options.role,tabIndex:0}),this._addClass("ui-menu","ui-widget ui-widget-content"),this._on({"mousedown .ui-menu-item":function(t){t.preventDefault()},"click .ui-menu-item":function(e){var n=t(e.target),r=t(t.ui.safeActiveElement(this.document[0]));!this.mouseHandled&&n.not(".ui-state-disabled").length&&(this.select(e),e.isPropagationStopped()||(this.mouseHandled=!0),n.has(".ui-menu").length?this.expand(e):!this.element.is(":focus")&&r.closest(".ui-menu").length&&(this.element.trigger("focus",[!0]),this.active)&&1===this.active.parents(".ui-menu").length&&clearTimeout(this.timer))},"mouseenter .ui-menu-item":function(e){var n,r;this.previousFilter||(n=t(e.target).closest(".ui-menu-item"),r=t(e.currentTarget),n[0]===r[0]&&(this._removeClass(r.siblings().children(".ui-state-active"),null,"ui-state-active"),this.focus(e,r)))},mouseleave:"collapseAll","mouseleave .ui-menu":"collapseAll",focus:function(t,e){var n=this.active||this.element.find(this.options.items).eq(0);e||this.focus(t,n)},blur:function(e){this._delay((function(){t.contains(this.element[0],t.ui.safeActiveElement(this.document[0]))||this.collapseAll(e)}))},keydown:"_keydown"}),this.refresh(),this._on(this.document,{click:function(t){this._closeOnDocumentClick(t)&&this.collapseAll(t),this.mouseHandled=!1}})},_destroy:function(){var e=this.element.find(".ui-menu-item").removeAttr("role aria-disabled").children(".ui-menu-item-wrapper").removeUniqueId().removeAttr("tabIndex role aria-haspopup");this.element.removeAttr("aria-activedescendant").find(".ui-menu").addBack().removeAttr("role aria-labelledby aria-expanded aria-hidden aria-disabled tabIndex").removeUniqueId().show(),e.children().each((function(){var e=t(this);e.data("ui-menu-submenu-caret")&&e.remove()}))},_keydown:function(e){var n,r,i,o=!0;switch(e.keyCode){case t.ui.keyCode.PAGE_UP:this.previousPage(e);break;case t.ui.keyCode.PAGE_DOWN:this.nextPage(e);break;case t.ui.keyCode.HOME:this._move("first","first",e);break;case t.ui.keyCode.END:this._move("last","last",e);break;case t.ui.keyCode.UP:this.previous(e);break;case t.ui.keyCode.DOWN:this.next(e);break;case t.ui.keyCode.LEFT:this.collapse(e);break;case t.ui.keyCode.RIGHT:this.active&&!this.active.is(".ui-state-disabled")&&this.expand(e);break;case t.ui.keyCode.ENTER:case t.ui.keyCode.SPACE:this._activate(e);break;case t.ui.keyCode.ESCAPE:this.collapse(e);break;default:n=this.previousFilter||"",i=o=!1,r=96<=e.keyCode&&e.keyCode<=105?(e.keyCode-96).toString():String.fromCharCode(e.keyCode),clearTimeout(this.filterTimer),r===n?i=!0:r=n+r,n=this._filterMenuItems(r),(n=i&&-1!==n.index(this.active.next())?this.active.nextAll(".ui-menu-item"):n).length||(r=String.fromCharCode(e.keyCode),n=this._filterMenuItems(r)),n.length?(this.focus(e,n),this.previousFilter=r,this.filterTimer=this._delay((function(){delete this.previousFilter}),1e3)):delete this.previousFilter}o&&e.preventDefault()},_activate:function(t){this.active&&!this.active.is(".ui-state-disabled")&&(this.active.children("[aria-haspopup='true']").length?this.expand(t):this.select(t))},refresh:function(){var e,n,r=this,i=this.options.icons.submenu,o=this.element.find(this.options.menus);this._toggleClass("ui-menu-icons",null,!!this.element.find(".ui-icon").length),e=o.filter(":not(.ui-menu)").hide().attr({role:this.options.role,"aria-hidden":"true","aria-expanded":"false"}).each((function(){var e=t(this),n=e.prev(),o=t("<span>").data("ui-menu-submenu-caret",!0);r._addClass(o,"ui-menu-icon","ui-icon "+i),n.attr("aria-haspopup","true").prepend(o),e.attr("aria-labelledby",n.attr("id"))})),this._addClass(e,"ui-menu","ui-widget ui-widget-content ui-front"),(e=o.add(this.element).find(this.options.items)).not(".ui-menu-item").each((function(){var e=t(this);r._isDivider(e)&&r._addClass(e,"ui-menu-divider","ui-widget-content")})),n=(o=e.not(".ui-menu-item, .ui-menu-divider")).children().not(".ui-menu").uniqueId().attr({tabIndex:-1,role:this._itemRole()}),this._addClass(o,"ui-menu-item")._addClass(n,"ui-menu-item-wrapper"),e.filter(".ui-state-disabled").attr("aria-disabled","true"),this.active&&!t.contains(this.element[0],this.active[0])&&this.blur()},_itemRole:function(){return{menu:"menuitem",listbox:"option"}[this.options.role]},_setOption:function(t,e){var n;"icons"===t&&(n=this.element.find(".ui-menu-icon"),this._removeClass(n,null,this.options.icons.submenu)._addClass(n,null,e.submenu)),this._super(t,e)},_setOptionDisabled:function(t){this._super(t),this.element.attr("aria-disabled",String(t)),this._toggleClass(null,"ui-state-disabled",!!t)},focus:function(t,e){var n;this.blur(t,t&&"focus"===t.type),this._scrollIntoView(e),this.active=e.first(),n=this.active.children(".ui-menu-item-wrapper"),this._addClass(n,null,"ui-state-active"),this.options.role&&this.element.attr("aria-activedescendant",n.attr("id")),n=this.active.parent().closest(".ui-menu-item").children(".ui-menu-item-wrapper"),this._addClass(n,null,"ui-state-active"),t&&"keydown"===t.type?this._close():this.timer=this._delay((function(){this._close()}),this.delay),(n=e.children(".ui-menu")).length&&t&&/^mouse/.test(t.type)&&this._startOpening(n),this.activeMenu=e.parent(),this._trigger("focus",t,{item:e})},_scrollIntoView:function(e){var n,r,i;this._hasScroll()&&(n=parseFloat(t.css(this.activeMenu[0],"borderTopWidth"))||0,r=parseFloat(t.css(this.activeMenu[0],"paddingTop"))||0,n=e.offset().top-this.activeMenu.offset().top-n-r,r=this.activeMenu.scrollTop(),i=this.activeMenu.height(),e=e.outerHeight(),n<0?this.activeMenu.scrollTop(r+n):i<n+e&&this.activeMenu.scrollTop(r+n-i+e))},blur:function(t,e){e||clearTimeout(this.timer),this.active&&(this._removeClass(this.active.children(".ui-menu-item-wrapper"),null,"ui-state-active"),this._trigger("blur",t,{item:this.active}),this.active=null)},_startOpening:function(t){clearTimeout(this.timer),"true"===t.attr("aria-hidden")&&(this.timer=this._delay((function(){this._close(),this._open(t)}),this.delay))},_open:function(e){var n=t.extend({of:this.active},this.options.position);clearTimeout(this.timer),this.element.find(".ui-menu").not(e.parents(".ui-menu")).hide().attr("aria-hidden","true"),e.show().removeAttr("aria-hidden").attr("aria-expanded","true").position(n)},collapseAll:function(e,n){clearTimeout(this.timer),this.timer=this._delay((function(){var r=n?this.element:t(e&&e.target).closest(this.element.find(".ui-menu"));r.length||(r=this.element),this._close(r),this.blur(e),this._removeClass(r.find(".ui-state-active"),null,"ui-state-active"),this.activeMenu=r}),this.delay)},_close:function(t){(t=t||(this.active?this.active.parent():this.element)).find(".ui-menu").hide().attr("aria-hidden","true").attr("aria-expanded","false")},_closeOnDocumentClick:function(e){return!t(e.target).closest(".ui-menu").length},_isDivider:function(t){return!/[^\-\u2014\u2013\s]/.test(t.text())},collapse:function(t){var e=this.active&&this.active.parent().closest(".ui-menu-item",this.element);e&&e.length&&(this._close(),this.focus(t,e))},expand:function(t){var e=this.active&&this.active.children(".ui-menu ").find(this.options.items).first();e&&e.length&&(this._open(e.parent()),this._delay((function(){this.focus(t,e)})))},next:function(t){this._move("next","first",t)},previous:function(t){this._move("prev","last",t)},isFirstItem:function(){return this.active&&!this.active.preval(".ui-menu-item").length},isLastItem:function(){return this.active&&!this.active.nextAll(".ui-menu-item").length},_move:function(t,e,n){var r;(r=this.active?"first"===t||"last"===t?this.active["first"===t?"prevAll":"nextAll"](".ui-menu-item").eq(-1):this.active[t+"All"](".ui-menu-item").eq(0):r)&&r.length&&this.active||(r=this.activeMenu.find(this.options.items)[e]()),this.focus(n,r)},nextPage:function(e){var n,r,i;this.active?this.isLastItem()||(this._hasScroll()?(r=this.active.offset().top,i=this.element.height(),this.active.nextAll(".ui-menu-item").each((function(){return(n=t(this)).offset().top-r-i<0})),this.focus(e,n)):this.focus(e,this.activeMenu.find(this.options.items)[this.active?"last":"first"]())):this.next(e)},previousPage:function(e){var n,r,i;this.active?this.isFirstItem()||(this._hasScroll()?(r=this.active.offset().top,i=this.element.height(),this.active.preval(".ui-menu-item").each((function(){return 0<(n=t(this)).offset().top-r+i})),this.focus(e,n)):this.focus(e,this.activeMenu.find(this.options.items).first())):this.next(e)},_hasScroll:function(){return this.element.outerHeight()<this.element.prop("scrollHeight")},select:function(e){this.active=this.active||t(e.target).closest(".ui-menu-item");var n={item:this.active};this.active.has(".ui-menu").length||this.collapseAll(e,!0),this._trigger("select",e,n)},_filterMenuItems:function(e){e=e.replace(/[\-\[\]{}()*+?.,\\\^$|#\s]/g,"\\$&");var n=new RegExp("^"+e,"i");return this.activeMenu.find(this.options.items).filter(".ui-menu-item").filter((function(){return n.test(t.trim(t(this).children(".ui-menu-item-wrapper").text()))}))}}),t.ui.ie=!!/msie [\w.]+/.exec(navigator.userAgent.toLowerCase()),!1);function w(t){return function(){var e=this.element.val();t.apply(this,arguments),this._refresh(),e!==this.element.val()&&this._trigger("change")}}t(document).on("mouseup",(function(){b=!1})),t.widget("ui.mouse",{version:"1.12.1",options:{cancel:"input, textarea, button, select, option",distance:1,delay:0},_mouseInit:function(){var e=this;this.element.on("mousedown."+this.widgetName,(function(t){return e._mouseDown(t)})).on("click."+this.widgetName,(function(n){if(!0===t.data(n.target,e.widgetName+".preventClickEvent"))return t.removeData(n.target,e.widgetName+".preventClickEvent"),n.stopImmediatePropagation(),!1})),this.started=!1},_mouseDestroy:function(){this.element.off("."+this.widgetName),this._mouseMoveDelegate&&this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate)},_mouseDown:function(e){var n,r,i;if(!b)return this._mouseMoved=!1,this._mouseStarted&&this._mouseUp(e),r=1===(this._mouseDownEvent=e).which,i=!("string"!=typeof(n=this).options.cancel||!e.target.nodeName)&&t(e.target).closest(this.options.cancel).length,!(r&&!i&&this._mouseCapture(e))||(this.mouseDelayMet=!this.options.delay,this.mouseDelayMet||(this._mouseDelayTimer=setTimeout((function(){n.mouseDelayMet=!0}),this.options.delay)),this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(e),!this._mouseStarted)?(e.preventDefault(),!0):(!0===t.data(e.target,this.widgetName+".preventClickEvent")&&t.removeData(e.target,this.widgetName+".preventClickEvent"),this._mouseMoveDelegate=function(t){return n._mouseMove(t)},this._mouseUpDelegate=function(t){return n._mouseUp(t)},this.document.on("mousemove."+this.widgetName,this._mouseMoveDelegate).on("mouseup."+this.widgetName,this._mouseUpDelegate),e.preventDefault(),b=!0))},_mouseMove:function(e){if(this._mouseMoved){if(t.ui.ie&&(!document.documentMode||document.documentMode<9)&&!e.button)return this._mouseUp(e);if(!e.which)if(e.originalEvent.altKey||e.originalEvent.ctrlKey||e.originalEvent.metaKey||e.originalEvent.shiftKey)this.ignoreMissingWhich=!0;else if(!this.ignoreMissingWhich)return this._mouseUp(e)}return(e.which||e.button)&&(this._mouseMoved=!0),this._mouseStarted?(this._mouseDrag(e),e.preventDefault()):(this._mouseDistanceMet(e)&&this._mouseDelayMet(e)&&(this._mouseStarted=!1!==this._mouseStart(this._mouseDownEvent,e),this._mouseStarted?this._mouseDrag(e):this._mouseUp(e)),!this._mouseStarted)},_mouseUp:function(e){this.document.off("mousemove."+this.widgetName,this._mouseMoveDelegate).off("mouseup."+this.widgetName,this._mouseUpDelegate),this._mouseStarted&&(this._mouseStarted=!1,e.target===this._mouseDownEvent.target&&t.data(e.target,this.widgetName+".preventClickEvent",!0),this._mouseStop(e)),this._mouseDelayTimer&&(clearTimeout(this._mouseDelayTimer),delete this._mouseDelayTimer),this.ignoreMissingWhich=!1,b=!1,e.preventDefault()},_mouseDistanceMet:function(t){return Math.max(Math.abs(this._mouseDownEvent.pageX-t.pageX),Math.abs(this._mouseDownEvent.pageY-t.pageY))>=this.options.distance},_mouseDelayMet:function(){return this.mouseDelayMet},_mouseStart:function(){},_mouseDrag:function(){},_mouseStop:function(){},_mouseCapture:function(){return!0}}),t.widget("ui.selectmenu",[t.ui.formResetMixin,{version:"1.12.1",defaultElement:"<select>",options:{appendTo:null,classes:{"ui-selectmenu-button-open":"ui-corner-top","ui-selectmenu-button-closed":"ui-corner-all"},disabled:null,icons:{button:"ui-icon-triangle-1-s"},position:{my:"left top",at:"left bottom",collision:"none"},width:!1,change:null,close:null,focus:null,open:null,select:null},_create:function(){var e=this.element.uniqueId().attr("id");this.ids={element:e,button:e+"-button",menu:e+"-menu"},this._drawButton(),this._drawMenu(),this._bindFormResetHandler(),this._rendered=!1,this.menuItems=t()},_drawButton:function(){var e,n=this,r=this._parseOption(this.element.find("option:selected"),this.element[0].selectedIndex);this.labels=this.element.labels().attr("for",this.ids.button),this._on(this.labels,{click:function(t){this.button.focus(),t.preventDefault()}}),this.element.hide(),this.button=t("<span>",{tabindex:this.options.disabled?-1:0,id:this.ids.button,role:"combobox","aria-expanded":"false","aria-autocomplete":"list","aria-owns":this.ids.menu,"aria-haspopup":"true",title:this.element.attr("title")}).insertAfter(this.element),this._addClass(this.button,"ui-selectmenu-button ui-selectmenu-button-closed","ui-button ui-widget"),e=t("<span>").appendTo(this.button),this._addClass(e,"ui-selectmenu-icon","ui-icon "+this.options.icons.button),this.buttonItem=this._renderButtonItem(r).appendTo(this.button),!1!==this.options.width&&this._resizeButton(),this._on(this.button,this._buttonEvents),this.button.one("focusin",(function(){n._rendered||n._refreshMenu()}))},_drawMenu:function(){var e=this;this.menu=t("<ul>",{"aria-hidden":"true","aria-labelledby":this.ids.button,id:this.ids.menu}),this.menuWrap=t("<div>").append(this.menu),this._addClass(this.menuWrap,"ui-selectmenu-menu","ui-front"),this.menuWrap.appendTo(this._appendTo()),this.menuInstance=this.menu.menu({classes:{"ui-menu":"ui-corner-bottom"},role:"listbox",select:function(t,n){t.preventDefault(),e._setSelection(),e._select(n.item.data("ui-selectmenu-item"),t)},focus:function(t,n){n=n.item.data("ui-selectmenu-item"),null!=e.focusIndex&&n.index!==e.focusIndex&&(e._trigger("focus",t,{item:n}),e.isOpen||e._select(n,t)),e.focusIndex=n.index,e.button.attr("aria-activedescendant",e.menuItems.eq(n.index).attr("id"))}}).menu("instance"),this.menuInstance._off(this.menu,"mouseleave"),this.menuInstance._closeOnDocumentClick=function(){return!1},this.menuInstance._isDivider=function(){return!1}},refresh:function(){this._refreshMenu(),this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(this._getSelectedItem().data("ui-selectmenu-item")||{})),null===this.options.width&&this._resizeButton()},_refreshMenu:function(){var t=this.element.find("option");this.menu.empty(),this._parseOptions(t),this._renderMenu(this.menu,this.items),this.menuInstance.refresh(),this.menuItems=this.menu.find("li").not(".ui-selectmenu-optgroup").find(".ui-menu-item-wrapper"),this._rendered=!0,t.length&&(t=this._getSelectedItem(),this.menuInstance.focus(null,t),this._setAria(t.data("ui-selectmenu-item")),this._setOption("disabled",this.element.prop("disabled")))},open:function(t){this.options.disabled||(this._rendered?(this._removeClass(this.menu.find(".ui-state-active"),null,"ui-state-active"),this.menuInstance.focus(null,this._getSelectedItem())):this._refreshMenu(),this.menuItems.length&&(this.isOpen=!0,this._toggleAttr(),this._resizeMenu(),this._position(),this._on(this.document,this._documentClick),this._trigger("open",t)))},_position:function(){this.menuWrap.position(t.extend({of:this.button},this.options.position))},close:function(t){this.isOpen&&(this.isOpen=!1,this._toggleAttr(),this.range=null,this._off(this.document),this._trigger("close",t))},widget:function(){return this.button},menuWidget:function(){return this.menu},_renderButtonItem:function(e){var n=t("<span>");return this._setText(n,e.label),this._addClass(n,"ui-selectmenu-text"),n},_renderMenu:function(e,n){var r=this,i="";t.each(n,(function(n,o){var s;o.optgroup!==i&&(s=t("<li>",{text:o.optgroup}),r._addClass(s,"ui-selectmenu-optgroup","ui-menu-divider"+(o.element.parent("optgroup").prop("disabled")?" ui-state-disabled":"")),s.appendTo(e),i=o.optgroup),r._renderItemData(e,o)}))},_renderItemData:function(t,e){return this._renderItem(t,e).data("ui-selectmenu-item",e)},_renderItem:function(e,n){var r=t("<li>"),i=t("<div>",{title:n.element.attr("title")});return n.disabled&&this._addClass(r,null,"ui-state-disabled"),this._setText(i,n.label),r.append(i).appendTo(e)},_setText:function(t,e){e?t.text(e):t.html("&#160;")},_move:function(t,e){var n,r=".ui-menu-item";this.isOpen?n=this.menuItems.eq(this.focusIndex).parent("li"):(n=this.menuItems.eq(this.element[0].selectedIndex).parent("li"),r+=":not(.ui-state-disabled)"),(n="first"===t||"last"===t?n["first"===t?"prevAll":"nextAll"](r).eq(-1):n[t+"All"](r).eq(0)).length&&this.menuInstance.focus(e,n)},_getSelectedItem:function(){return this.menuItems.eq(this.element[0].selectedIndex).parent("li")},_toggle:function(t){this[this.isOpen?"close":"open"](t)},_setSelection:function(){var t;this.range&&(window.getSelection?((t=window.getSelection()).removeAllRanges(),t.addRange(this.range)):this.range.select(),this.button.focus())},_documentClick:{mousedown:function(e){!this.isOpen||t(e.target).closest(".ui-selectmenu-menu, #"+t.ui.escapeSelector(this.ids.button)).length||this.close(e)}},_buttonEvents:{mousedown:function(){var t;window.getSelection?(t=window.getSelection()).rangeCount&&(this.range=t.getRangeAt(0)):this.range=document.selection.createRange()},click:function(t){this._setSelection(),this._toggle(t)},keydown:function(e){var n=!0;switch(e.keyCode){case t.ui.keyCode.TAB:case t.ui.keyCode.ESCAPE:this.close(e),n=!1;break;case t.ui.keyCode.ENTER:this.isOpen&&this._selectFocusedItem(e);break;case t.ui.keyCode.UP:e.altKey?this._toggle(e):this._move("prev",e);break;case t.ui.keyCode.DOWN:e.altKey?this._toggle(e):this._move("next",e);break;case t.ui.keyCode.SPACE:this.isOpen?this._selectFocusedItem(e):this._toggle(e);break;case t.ui.keyCode.LEFT:this._move("prev",e);break;case t.ui.keyCode.RIGHT:this._move("next",e);break;case t.ui.keyCode.HOME:case t.ui.keyCode.PAGE_UP:this._move("first",e);break;case t.ui.keyCode.END:case t.ui.keyCode.PAGE_DOWN:this._move("last",e);break;default:this.menu.trigger(e),n=!1}n&&e.preventDefault()}},_selectFocusedItem:function(t){var e=this.menuItems.eq(this.focusIndex).parent("li");e.hasClass("ui-state-disabled")||this._select(e.data("ui-selectmenu-item"),t)},_select:function(t,e){var n=this.element[0].selectedIndex;this.element[0].selectedIndex=t.index,this.buttonItem.replaceWith(this.buttonItem=this._renderButtonItem(t)),this._setAria(t),this._trigger("select",e,{item:t}),t.index!==n&&this._trigger("change",e,{item:t}),this.close(e)},_setAria:function(t){t=this.menuItems.eq(t.index).attr("id"),this.button.attr({"aria-labelledby":t,"aria-activedescendant":t}),this.menu.attr("aria-activedescendant",t)},_setOption:function(t,e){var n;"icons"===t&&(n=this.button.find("span.ui-icon"),this._removeClass(n,null,this.options.icons.button)._addClass(n,null,e.button)),this._super(t,e),"appendTo"===t&&this.menuWrap.appendTo(this._appendTo()),"width"===t&&this._resizeButton()},_setOptionDisabled:function(t){this._super(t),this.menuInstance.option("disabled",t),this.button.attr("aria-disabled",t),this._toggleClass(this.button,null,"ui-state-disabled",t),this.element.prop("disabled",t),t?(this.button.attr("tabindex",-1),this.close()):this.button.attr("tabindex",0)},_appendTo:function(){var e=this.options.appendTo;return(e=(e=e&&(e.jquery||e.nodeType?t(e):this.document.find(e).eq(0)))&&e[0]?e:this.element.closest(".ui-front, dialog")).length?e:this.document[0].body},_toggleAttr:function(){this.button.attr("aria-expanded",this.isOpen),this._removeClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"closed":"open"))._addClass(this.button,"ui-selectmenu-button-"+(this.isOpen?"open":"closed"))._toggleClass(this.menuWrap,"ui-selectmenu-open",null,this.isOpen),this.menu.attr("aria-hidden",!this.isOpen)},_resizeButton:function(){var t=this.options.width;!1!==t?(null===t&&(t=this.element.show().outerWidth(),this.element.hide()),this.button.outerWidth(t)):this.button.css("width","")},_resizeMenu:function(){this.menu.outerWidth(Math.max(this.button.outerWidth(),this.menu.width("").outerWidth()+1))},_getCreateOptions:function(){var t=this._super();return t.disabled=this.element.prop("disabled"),t},_parseOptions:function(e){var n=this,r=[];e.each((function(e,i){r.push(n._parseOption(t(i),e))})),this.items=r},_parseOption:function(t,e){var n=t.parent("optgroup");return{element:t,index:e,value:t.val(),label:t.text(),optgroup:n.attr("label")||"",disabled:n.prop("disabled")||t.prop("disabled")}},_destroy:function(){this._unbindFormResetHandler(),this.menuWrap.remove(),this.button.remove(),this.element.show(),this.element.removeUniqueId(),this.labels.attr("for",this.ids.element)}}]),t.widget("ui.slider",t.ui.mouse,{version:"1.12.1",widgetEventPrefix:"slide",options:{animate:!1,classes:{"ui-slider":"ui-corner-all","ui-slider-handle":"ui-corner-all","ui-slider-range":"ui-corner-all ui-widget-header"},distance:0,max:100,min:0,orientation:"horizontal",range:!1,step:1,value:0,values:null,change:null,slide:null,start:null,stop:null},numPages:5,_create:function(){this._keySliding=!1,this._mouseSliding=!1,this._animateOff=!0,this._handleIndex=null,this._detectOrientation(),this._mouseInit(),this._calculateNewMax(),this._addClass("ui-slider ui-slider-"+this.orientation,"ui-widget ui-widget-content"),this._refresh(),this._animateOff=!1},_refresh:function(){this._createRange(),this._createHandles(),this._setupEvents(),this._refreshValue()},_createHandles:function(){var e,n=this.options,r=this.element.find(".ui-slider-handle"),i=[],o=n.values&&n.values.length||1;for(r.length>o&&(r.slice(o).remove(),r=r.slice(0,o)),e=r.length;e<o;e++)i.push("<span tabindex='0'></span>");this.handles=r.add(t(i.join("")).appendTo(this.element)),this._addClass(this.handles,"ui-slider-handle","ui-state-default"),this.handle=this.handles.eq(0),this.handles.each((function(e){t(this).data("ui-slider-handle-index",e).attr("tabIndex",0)}))},_createRange:function(){var e=this.options;e.range?(!0===e.range&&(e.values?e.values.length&&2!==e.values.length?e.values=[e.values[0],e.values[0]]:t.isArray(e.values)&&(e.values=e.values.slice(0)):e.values=[this._valueMin(),this._valueMin()]),this.range&&this.range.length?(this._removeClass(this.range,"ui-slider-range-min ui-slider-range-max"),this.range.css({left:"",bottom:""})):(this.range=t("<div>").appendTo(this.element),this._addClass(this.range,"ui-slider-range")),"min"!==e.range&&"max"!==e.range||this._addClass(this.range,"ui-slider-range-"+e.range)):(this.range&&this.range.remove(),this.range=null)},_setupEvents:function(){this._off(this.handles),this._on(this.handles,this._handleEvents),this._hoverable(this.handles),this._focusable(this.handles)},_destroy:function(){this.handles.remove(),this.range&&this.range.remove(),this._mouseDestroy()},_mouseCapture:function(e){var n,r,i,o,s,a,u=this,c=this.options;return!c.disabled&&(this.elementSize={width:this.element.outerWidth(),height:this.element.outerHeight()},this.elementOffset=this.element.offset(),s={x:e.pageX,y:e.pageY},n=this._normValueFromMouse(s),r=this._valueMax()-this._valueMin()+1,this.handles.each((function(e){var s=Math.abs(n-u.values(e));(s<r||r===s&&(e===u._lastChangedValue||u.values(e)===c.min))&&(r=s,i=t(this),o=e)})),!1!==this._start(e,o))&&(this._mouseSliding=!0,this._handleIndex=o,this._addClass(i,null,"ui-state-active"),i.trigger("focus"),s=i.offset(),a=!t(e.target).parents().addBack().is(".ui-slider-handle"),this._clickOffset=a?{left:0,top:0}:{left:e.pageX-s.left,top:e.pageY-s.top-i.height()/2-(parseInt(i.css("borderTopWidth"),10)||0)-(parseInt(i.css("borderBottomWidth"),10)||0)+(parseInt(i.css("marginTop"),10)||0)},this._animateOff=!0)},_mouseStart:function(){return!0},_mouseDrag:function(t){var e={x:t.pageX,y:t.pageY};e=this._normValueFromMouse(e);return this._slide(t,this._handleIndex,e),!1},_mouseStop:function(t){return this._removeClass(this.handles,null,"ui-state-active"),this._mouseSliding=!1,this._stop(t,this._handleIndex),this._change(t,this._handleIndex),this._handleIndex=null,this._clickOffset=null,this._animateOff=!1},_detectOrientation:function(){this.orientation="vertical"===this.options.orientation?"vertical":"horizontal"},_normValueFromMouse:function(t){var e;t="horizontal"===this.orientation?(e=this.elementSize.width,t.x-this.elementOffset.left-(this._clickOffset?this._clickOffset.left:0)):(e=this.elementSize.height,t.y-this.elementOffset.top-(this._clickOffset?this._clickOffset.top:0));return(t=1<(t/=e)?1:t)<0&&(t=0),"vertical"===this.orientation&&(t=1-t),e=this._valueMax()-this._valueMin(),t=this._valueMin()+t*e,this._trimAlignValue(t)},_uiHash:function(t,e,n){var r={handle:this.handles[t],handleIndex:t,value:void 0!==e?e:this.value()};return this._hasMultipleValues()&&(r.value=void 0!==e?e:this.values(t),r.values=n||this.values()),r},_hasMultipleValues:function(){return this.options.values&&this.options.values.length},_start:function(t,e){return this._trigger("start",t,this._uiHash(e))},_slide:function(t,e,n){var r,i=this.value(),o=this.values();this._hasMultipleValues()&&(r=this.values(e?0:1),i=this.values(e),2===this.options.values.length&&!0===this.options.range&&(n=0===e?Math.min(r,n):Math.max(r,n)),o[e]=n),n!==i&&!1!==this._trigger("slide",t,this._uiHash(e,n,o))&&(this._hasMultipleValues()?this.values(e,n):this.value(n))},_stop:function(t,e){this._trigger("stop",t,this._uiHash(e))},_change:function(t,e){this._keySliding||this._mouseSliding||(this._lastChangedValue=e,this._trigger("change",t,this._uiHash(e)))},value:function(t){return arguments.length?(this.options.value=this._trimAlignValue(t),this._refreshValue(),void this._change(null,0)):this._value()},values:function(e,n){var r,i,o;if(1<arguments.length)this.options.values[e]=this._trimAlignValue(n),this._refreshValue(),this._change(null,e);else{if(!arguments.length)return this._values();if(!t.isArray(e))return this._hasMultipleValues()?this._values(e):this.value();for(r=this.options.values,i=e,o=0;o<r.length;o+=1)r[o]=this._trimAlignValue(i[o]),this._change(null,o);this._refreshValue()}},_setOption:function(e,n){var r,i=0;switch("range"===e&&!0===this.options.range&&("min"===n?(this.options.value=this._values(0),this.options.values=null):"max"===n&&(this.options.value=this._values(this.options.values.length-1),this.options.values=null)),t.isArray(this.options.values)&&(i=this.options.values.length),this._super(e,n),e){case"orientation":this._detectOrientation(),this._removeClass("ui-slider-horizontal ui-slider-vertical")._addClass("ui-slider-"+this.orientation),this._refreshValue(),this.options.range&&this._refreshRange(n),this.handles.css("horizontal"===n?"bottom":"left","");break;case"value":this._animateOff=!0,this._refreshValue(),this._change(null,0),this._animateOff=!1;break;case"values":for(this._animateOff=!0,this._refreshValue(),r=i-1;0<=r;r--)this._change(null,r);this._animateOff=!1;break;case"step":case"min":case"max":this._animateOff=!0,this._calculateNewMax(),this._refreshValue(),this._animateOff=!1;break;case"range":this._animateOff=!0,this._refresh(),this._animateOff=!1}},_setOptionDisabled:function(t){this._super(t),this._toggleClass(null,"ui-state-disabled",!!t)},_value:function(){var t=this.options.value;return this._trimAlignValue(t)},_values:function(t){var e,n,r;if(arguments.length)return e=this.options.values[t],this._trimAlignValue(e);if(this._hasMultipleValues()){for(n=this.options.values.slice(),r=0;r<n.length;r+=1)n[r]=this._trimAlignValue(n[r]);return n}return[]},_trimAlignValue:function(t){var e,n;return t<=this._valueMin()?this._valueMin():t>=this._valueMax()?this._valueMax():(e=0<this.options.step?this.options.step:1,n=t-(t=(t-this._valueMin())%e),2*Math.abs(t)>=e&&(n+=0<t?e:-e),parseFloat(n.toFixed(5)))},_calculateNewMax:function(){var t=this.options.max,e=this._valueMin(),n=this.options.step;(t=Math.round((t-e)/n)*n+e)>this.options.max&&(t-=n),this.max=parseFloat(t.toFixed(this._precision()))},_precision:function(){var t=this._precisionOf(this.options.step);return null!==this.options.min?Math.max(t,this._precisionOf(this.options.min)):t},_precisionOf:function(t){t=t.toString();var e=t.indexOf(".");return-1===e?0:t.length-e-1},_valueMin:function(){return this.options.min},_valueMax:function(){return this.max},_refreshRange:function(t){"vertical"===t&&this.range.css({width:"",left:""}),"horizontal"===t&&this.range.css({height:"",bottom:""})},_refreshValue:function(){var e,n,r,i,o,s=this.options.range,a=this.options,u=this,c=!this._animateOff&&a.animate,l={};this._hasMultipleValues()?this.handles.each((function(r){n=(u.values(r)-u._valueMin())/(u._valueMax()-u._valueMin())*100,l["horizontal"===u.orientation?"left":"bottom"]=n+"%",t(this).stop(1,1)[c?"animate":"css"](l,a.animate),!0===u.options.range&&("horizontal"===u.orientation?(0===r&&u.range.stop(1,1)[c?"animate":"css"]({left:n+"%"},a.animate),1===r&&u.range[c?"animate":"css"]({width:n-e+"%"},{queue:!1,duration:a.animate})):(0===r&&u.range.stop(1,1)[c?"animate":"css"]({bottom:n+"%"},a.animate),1===r&&u.range[c?"animate":"css"]({height:n-e+"%"},{queue:!1,duration:a.animate}))),e=n})):(r=this.value(),i=this._valueMin(),o=this._valueMax(),n=o!==i?(r-i)/(o-i)*100:0,l["horizontal"===this.orientation?"left":"bottom"]=n+"%",this.handle.stop(1,1)[c?"animate":"css"](l,a.animate),"min"===s&&"horizontal"===this.orientation&&this.range.stop(1,1)[c?"animate":"css"]({width:n+"%"},a.animate),"max"===s&&"horizontal"===this.orientation&&this.range.stop(1,1)[c?"animate":"css"]({width:100-n+"%"},a.animate),"min"===s&&"vertical"===this.orientation&&this.range.stop(1,1)[c?"animate":"css"]({height:n+"%"},a.animate),"max"===s&&"vertical"===this.orientation&&this.range.stop(1,1)[c?"animate":"css"]({height:100-n+"%"},a.animate))},_handleEvents:{keydown:function(e){var n,r,i,o=t(e.target).data("ui-slider-handle-index");switch(e.keyCode){case t.ui.keyCode.HOME:case t.ui.keyCode.END:case t.ui.keyCode.PAGE_UP:case t.ui.keyCode.PAGE_DOWN:case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(e.preventDefault(),!this._keySliding&&(this._keySliding=!0,this._addClass(t(e.target),null,"ui-state-active"),!1===this._start(e,o)))return}switch(i=this.options.step,n=r=this._hasMultipleValues()?this.values(o):this.value(),e.keyCode){case t.ui.keyCode.HOME:r=this._valueMin();break;case t.ui.keyCode.END:r=this._valueMax();break;case t.ui.keyCode.PAGE_UP:r=this._trimAlignValue(n+(this._valueMax()-this._valueMin())/this.numPages);break;case t.ui.keyCode.PAGE_DOWN:r=this._trimAlignValue(n-(this._valueMax()-this._valueMin())/this.numPages);break;case t.ui.keyCode.UP:case t.ui.keyCode.RIGHT:if(n===this._valueMax())return;r=this._trimAlignValue(n+i);break;case t.ui.keyCode.DOWN:case t.ui.keyCode.LEFT:if(n===this._valueMin())return;r=this._trimAlignValue(n-i)}this._slide(e,o,r)},keyup:function(e){var n=t(e.target).data("ui-slider-handle-index");this._keySliding&&(this._keySliding=!1,this._stop(e,n),this._change(e,n),this._removeClass(t(e.target),null,"ui-state-active"))}}}),t.widget("ui.spinner",{version:"1.12.1",defaultElement:"<input>",widgetEventPrefix:"spin",options:{classes:{"ui-spinner":"ui-corner-all","ui-spinner-down":"ui-corner-br","ui-spinner-up":"ui-corner-tr"},culture:null,icons:{down:"ui-icon-triangle-1-s",up:"ui-icon-triangle-1-n"},incremental:!0,max:null,min:null,numberFormat:null,page:10,step:1,change:null,spin:null,start:null,stop:null},_create:function(){this._setOption("max",this.options.max),this._setOption("min",this.options.min),this._setOption("step",this.options.step),""!==this.value()&&this._value(this.element.val(),!0),this._draw(),this._on(this._events),this._refresh(),this._on(this.window,{beforeunload:function(){this.element.removeAttr("autocomplete")}})},_getCreateOptions:function(){var e=this._super(),n=this.element;return t.each(["min","max","step"],(function(t,r){var i=n.attr(r);null!=i&&i.length&&(e[r]=i)})),e},_events:{keydown:function(t){this._start(t)&&this._keydown(t)&&t.preventDefault()},keyup:"_stop",focus:function(){this.previous=this.element.val()},blur:function(t){this.cancelBlur?delete this.cancelBlur:(this._stop(),this._refresh(),this.previous!==this.element.val()&&this._trigger("change",t))},mousewheel:function(t,e){if(e){if(!this.spinning&&!this._start(t))return!1;this._spin((0<e?1:-1)*this.options.step,t),clearTimeout(this.mousewheelTimer),this.mousewheelTimer=this._delay((function(){this.spinning&&this._stop(t)}),100),t.preventDefault()}},"mousedown .ui-spinner-button":function(e){var n;function r(){this.element[0]!==t.ui.safeActiveElement(this.document[0])&&(this.element.trigger("focus"),this.previous=n,this._delay((function(){this.previous=n})))}n=this.element[0]===t.ui.safeActiveElement(this.document[0])?this.previous:this.element.val(),e.preventDefault(),r.call(this),this.cancelBlur=!0,this._delay((function(){delete this.cancelBlur,r.call(this)})),!1!==this._start(e)&&this._repeat(null,t(e.currentTarget).hasClass("ui-spinner-up")?1:-1,e)},"mouseup .ui-spinner-button":"_stop","mouseenter .ui-spinner-button":function(e){if(t(e.currentTarget).hasClass("ui-state-active"))return!1!==this._start(e)&&void this._repeat(null,t(e.currentTarget).hasClass("ui-spinner-up")?1:-1,e)},"mouseleave .ui-spinner-button":"_stop"},_enhance:function(){this.uiSpinner=this.element.attr("autocomplete","off").wrap("<span>").parent().append("<a></a><a></a>")},_draw:function(){this._enhance(),this._addClass(this.uiSpinner,"ui-spinner","ui-widget ui-widget-content"),this._addClass("ui-spinner-input"),this.element.attr("role","spinbutton"),this.buttons=this.uiSpinner.children("a").attr("tabIndex",-1).attr("aria-hidden",!0).button({classes:{"ui-button":""}}),this._removeClass(this.buttons,"ui-corner-all"),this._addClass(this.buttons.first(),"ui-spinner-button ui-spinner-up"),this._addClass(this.buttons.last(),"ui-spinner-button ui-spinner-down"),this.buttons.first().button({icon:this.options.icons.up,showLabel:!1}),this.buttons.last().button({icon:this.options.icons.down,showLabel:!1}),this.buttons.height()>Math.ceil(.5*this.uiSpinner.height())&&0<this.uiSpinner.height()&&this.uiSpinner.height(this.uiSpinner.height())},_keydown:function(e){var n=this.options,r=t.ui.keyCode;switch(e.keyCode){case r.UP:return this._repeat(null,1,e),!0;case r.DOWN:return this._repeat(null,-1,e),!0;case r.PAGE_UP:return this._repeat(null,n.page,e),!0;case r.PAGE_DOWN:return this._repeat(null,-n.page,e),!0}return!1},_start:function(t){return!(!this.spinning&&!1===this._trigger("start",t))&&(this.counter||(this.counter=1),this.spinning=!0)},_repeat:function(t,e,n){t=t||500,clearTimeout(this.timer),this.timer=this._delay((function(){this._repeat(40,e,n)}),t),this._spin(e*this.options.step,n)},_spin:function(t,e){var n=this.value()||0;this.counter||(this.counter=1),n=this._adjustValue(n+t*this._increment(this.counter)),this.spinning&&!1===this._trigger("spin",e,{value:n})||(this._value(n),this.counter++)},_increment:function(e){var n=this.options.incremental;return n?t.isFunction(n)?n(e):Math.floor(e*e*e/5e4-e*e/500+17*e/200+1):1},_precision:function(){var t=this._precisionOf(this.options.step);return null!==this.options.min?Math.max(t,this._precisionOf(this.options.min)):t},_precisionOf:function(t){t=t.toString();var e=t.indexOf(".");return-1===e?0:t.length-e-1},_adjustValue:function(t){var e,n=this.options,r=t-(e=null!==n.min?n.min:0);return t=e+Math.round(r/n.step)*n.step,t=parseFloat(t.toFixed(this._precision())),null!==n.max&&t>n.max?n.max:null!==n.min&&t<n.min?n.min:t},_stop:function(t){this.spinning&&(clearTimeout(this.timer),clearTimeout(this.mousewheelTimer),this.counter=0,this.spinning=!1,this._trigger("stop",t))},_setOption:function(t,e){var n;"culture"===t||"numberFormat"===t?(n=this._parse(this.element.val()),this.options[t]=e,this.element.val(this._format(n))):("max"!==t&&"min"!==t&&"step"!==t||"string"==typeof e&&(e=this._parse(e)),"icons"===t&&(n=this.buttons.first().find(".ui-icon"),this._removeClass(n,null,this.options.icons.up),this._addClass(n,null,e.up),n=this.buttons.last().find(".ui-icon"),this._removeClass(n,null,this.options.icons.down),this._addClass(n,null,e.down)),this._super(t,e))},_setOptionDisabled:function(t){this._super(t),this._toggleClass(this.uiSpinner,null,"ui-state-disabled",!!t),this.element.prop("disabled",!!t),this.buttons.button(t?"disable":"enable")},_setOptions:w((function(t){this._super(t)})),_parse:function(t){return""===(t="string"==typeof t&&""!==t?window.Globalize&&this.options.numberFormat?Globalize.parseFloat(t,10,this.options.culture):+t:t)||isNaN(t)?null:t},_format:function(t){return""===t?"":window.Globalize&&this.options.numberFormat?Globalize.format(t,this.options.numberFormat,this.options.culture):t},_refresh:function(){this.element.attr({"aria-valuemin":this.options.min,"aria-valuemax":this.options.max,"aria-valuenow":this._parse(this.element.val())})},isValid:function(){var t=this.value();return null!==t&&t===this._adjustValue(t)},_value:function(t,e){var n;""!==t&&null!==(n=this._parse(t))&&(e||(n=this._adjustValue(n)),t=this._format(n)),this.element.val(t),this._refresh()},_destroy:function(){this.element.prop("disabled",!1).removeAttr("autocomplete role aria-valuemin aria-valuemax aria-valuenow"),this.uiSpinner.replaceWith(this.element)},stepUp:w((function(t){this._stepUp(t)})),_stepUp:function(t){this._start()&&(this._spin((t||1)*this.options.step),this._stop())},stepDown:w((function(t){this._stepDown(t)})),_stepDown:function(t){this._start()&&(this._spin((t||1)*-this.options.step),this._stop())},pageUp:w((function(t){this._stepUp((t||1)*this.options.page)})),pageDown:w((function(t){this._stepDown((t||1)*this.options.page)})),value:function(t){if(!arguments.length)return this._parse(this.element.val());w(this._value).call(this,t)},widget:function(){return this.uiSpinner}}),!1!==t.uiBackCompat&&t.widget("ui.spinner",t.ui.spinner,{_enhance:function(){this.uiSpinner=this.element.attr("autocomplete","off").wrap(this._uiSpinnerHtml()).parent().append(this._buttonHtml())},_uiSpinnerHtml:function(){return"<span>"},_buttonHtml:function(){return"<a></a><a></a>"}}),t.ui.spinner};"function"==typeof define&&define.amd?define(["jquery"],t):t(jQuery)}(),function(t){"function"==typeof define&&define.amd?define(["jquery"],t):"object"==typeof exports?module.exports=t:t(jQuery)}((function(t){function e(e){var s,a=e||window.event,c=u.call(arguments,1),h=0,f=0,p=0,d=0,g=0,m=0;if((e=t.event.fix(a)).type="mousewheel","detail"in a&&(p=-1*a.detail),"wheelDelta"in a&&(p=a.wheelDelta),"wheelDeltaY"in a&&(p=a.wheelDeltaY),"wheelDeltaX"in a&&(f=-1*a.wheelDeltaX),"axis"in a&&a.axis===a.HORIZONTAL_AXIS&&(f=-1*p,p=0),h=0===p?f:p,"deltaY"in a&&(h=p=-1*a.deltaY),"deltaX"in a&&(f=a.deltaX,0===p)&&(h=-1*f),0!==p||0!==f)return 1===a.deltaMode?(h*=s=t.data(this,"mousewheel-line-height"),p*=s,f*=s):2===a.deltaMode&&(h*=s=t.data(this,"mousewheel-page-height"),p*=s,f*=s),d=Math.max(Math.abs(p),Math.abs(f)),(!o||d<o)&&r(a,o=d)&&(o/=40),r(a,d)&&(h/=40,f/=40,p/=40),h=Math[1<=h?"floor":"ceil"](h/o),f=Math[1<=f?"floor":"ceil"](f/o),p=Math[1<=p?"floor":"ceil"](p/o),l.settings.normalizeOffset&&this.getBoundingClientRect&&(s=this.getBoundingClientRect(),g=e.clientX-s.left,m=e.clientY-s.top),e.deltaX=f,e.deltaY=p,e.deltaFactor=o,e.offsetX=g,e.offsetY=m,e.deltaMode=0,c.unshift(e,h,f,p),i&&clearTimeout(i),i=setTimeout(n,200),(t.event.dispatch||t.event.handle).apply(this,c)}function n(){o=null}function r(t,e){return l.settings.adjustOldDeltas&&"mousewheel"===t.type&&e%120==0}var i,o,s=["wheel","mousewheel","DOMMouseScroll","MozMousePixelScroll"],a="onwheel"in document||9<=document.documentMode?["wheel"]:["mousewheel","DomMouseScroll","MozMousePixelScroll"],u=Array.prototype.slice;if(t.event.fixHooks)for(var c=s.length;c;)t.event.fixHooks[s[--c]]=t.event.mouseHooks;var l=t.event.special.mousewheel={version:"3.1.12",setup:function(){if(this.addEventListener)for(var n=a.length;n;)this.addEventListener(a[--n],e,!1);else this.onmousewheel=e;t.data(this,"mousewheel-line-height",l.getLineHeight(this)),t.data(this,"mousewheel-page-height",l.getPageHeight(this))},teardown:function(){if(this.removeEventListener)for(var n=a.length;n;)this.removeEventListener(a[--n],e,!1);else this.onmousewheel=null;t.removeData(this,"mousewheel-line-height"),t.removeData(this,"mousewheel-page-height")},getLineHeight:function(e){e=t(e);var n=e["offsetParent"in t.fn?"offsetParent":"parent"]();return n.length||(n=t("body")),parseInt(n.css("fontSize"),10)||parseInt(e.css("fontSize"),10)||16},getPageHeight:function(e){return t(e).height()},settings:{adjustOldDeltas:!0,normalizeOffset:!0}};t.fn.extend({mousewheel:function(t){return t?this.bind("mousewheel",t):this.trigger("mousewheel")},unmousewheel:function(t){return this.unbind("mousewheel",t)}})})),function(t){var e;"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?e=window:"undefined"!=typeof global?e=global:"undefined"!=typeof self&&(e=self),e.html2canvas=t())}((function(){return function t(e,n,r){function i(s,a){if(!n[s]){if(!e[s]){var u="function"==typeof require&&require;if(!a&&u)return u(s,!0);if(o)return o(s,!0);throw a=new Error("Cannot find module '"+s+"'"),a.code="MODULE_NOT_FOUND",a}u=n[s]={exports:{}},e[s][0].call(u.exports,(function(t){var n=e[s][1][t];return i(n||t)}),u,u.exports,t,e,n,r)}return n[s].exports}for(var o="function"==typeof require&&require,s=0;s<r.length;s++)i(r[s]);return i}({1:[function(t,e,n){(function(t){var r=this;function i(t){throw RangeError(k[t])}function o(t,e){for(var n=t.length;n--;)t[n]=e(t[n]);return t}function s(t,e){return o(t.split(C),e).join(".")}function a(t){for(var e,n,r=[],i=0,o=t.length;i<o;)55296<=(e=t.charCodeAt(i++))&&e<=56319&&i<o?56320==(64512&(n=t.charCodeAt(i++)))?r.push(((1023&e)<<10)+(1023&n)+65536):(r.push(e),i--):r.push(e);return r}function u(t){return o(t,(function(t){var e="";return 65535<t&&(e+=E((t-=65536)>>>10&1023|55296),t=56320|1023&t),e+E(t)})).join("")}function c(t,e){return t+22+75*(t<26)-((0!=e)<<5)}function l(t,e,n){var r=0;for(t=n?O(t/w):t>>1,t+=O(t/e);S*y>>1<t;r+=v)t=O(t/S);return O(r+(S+1)*t/(t+b))}function h(t){var e,n,r,o,s,a,c,h=[],f=t.length,p=0,d=128,g=72,b=t.lastIndexOf("-");for(b<0&&(b=0),n=0;n<b;++n)128<=t.charCodeAt(n)&&i("not-basic"),h.push(t.charCodeAt(n));for(r=0<b?b+1:0;r<f;){for(o=p,s=1,a=v;f<=r&&i("invalid-input"),c=t.charCodeAt(r++),(v<=(c=c-48<10?c-22:c-65<26?c-65:c-97<26?c-97:v)||c>O((m-p)/s))&&i("overflow"),p+=c*s,!(c<(c=a<=g?1:g+y<=a?y:a-g));a+=v)s>O(m/(c=v-c))&&i("overflow"),s*=c;g=l(p-o,e=h.length+1,0==o),O(p/e)>m-d&&i("overflow"),d+=O(p/e),p%=e,h.splice(p++,0,d)}return u(h)}function f(t){for(var e,n,r,o,s,u,h,f,p,d,g=[],b=(t=a(t)).length,w=128,_=72,x=e=0;x<b;++x)(h=t[x])<128&&g.push(E(h));for(n=r=g.length,r&&g.push("-");n<b;){for(o=m,x=0;x<b;++x)w<=(h=t[x])&&h<o&&(o=h);for(o-w>O((m-e)/(f=n+1))&&i("overflow"),e+=(o-w)*f,w=o,x=0;x<b;++x)if((h=t[x])<w&&++e>m&&i("overflow"),h==w){for(s=e,u=v;!(s<(p=u<=_?1:_+y<=u?y:u-_));u+=v)g.push(E(c(p+(d=s-p)%(p=v-p),0))),s=O(d/p);g.push(E(c(s,0))),_=l(e,f,n==r),e=0,++n}++e,++w}return g.join("")}var p="object"==typeof n&&n,d="object"==typeof e&&e&&e.exports==p&&e;t="object"==typeof t&&t;t.global!==t&&t.window!==t||(r=t);var g,m=2147483647,v=36,y=26,b=38,w=700,_=/^xn--/,x=/[^ -~]/,C=/\x2E|\u3002|\uFF0E|\uFF61/g,k={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},S=v-1,O=Math.floor,E=String.fromCharCode,T={version:"1.2.4",ucs2:{decode:a,encode:u},decode:h,encode:f,toASCII:function(t){return s(t,(function(t){return x.test(t)?"xn--"+f(t):t}))},toUnicode:function(t){return s(t,(function(t){return _.test(t)?h(t.slice(4).toLowerCase()):t}))}};if(p&&!p.nodeType)if(d)d.exports=T;else for(g in T)T.hasOwnProperty(g)&&(p[g]=T[g]);else r.punycode=T}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(t,e){function n(t,e){for(var i=3===t.nodeType?document.createTextNode(t.nodeValue):t.cloneNode(!1),o=t.firstChild;o;)!0!==e&&1===o.nodeType&&"SCRIPT"===o.nodeName||i.appendChild(n(o,e)),o=o.nextSibling;if(1===t.nodeType)if(i._scrollTop=t.scrollTop,i._scrollLeft=t.scrollLeft,"CANVAS"===t.nodeName){var s=t,a=i;try{a&&(a.width=s.width,a.height=s.height,a.getContext("2d").putImageData(s.getContext("2d").getImageData(0,0,s.width,s.height),0,0))}catch(t){r("Unable to copy canvas content from",s,t)}}else"TEXTAREA"!==t.nodeName&&"SELECT"!==t.nodeName||(i.value=t.value);return i}var r=t("./log");e.exports=function(t,e,r,i,o,s,a){var u=n(t.documentElement,o.javascriptEnabled),c=e.createElement("iframe");return c.className="html2canvas-container",c.style.visibility="hidden",c.style.position="fixed",c.style.left="-10000px",c.style.top="0px",c.style.border="0",c.width=r,c.height=i,c.scrolling="no",e.body.appendChild(c),new Promise((function(e){var n,r,i,l=c.contentWindow.document;c.contentWindow.onload=c.onload=function(){var t=setInterval((function(){0<l.body.childNodes.length&&(function t(e){if(1===e.nodeType){e.scrollTop=e._scrollTop,e.scrollLeft=e._scrollLeft;for(var n=e.firstChild;n;)t(n),n=n.nextSibling}}(l.documentElement),clearInterval(t),"view"===o.type&&(c.contentWindow.scrollTo(s,a),!/(iPad|iPhone|iPod)/g.test(navigator.userAgent)||c.contentWindow.scrollY===a&&c.contentWindow.scrollX===s||(l.documentElement.style.top=-a+"px",l.documentElement.style.left=-s+"px",l.documentElement.style.position="absolute")),e(c))}),50)},l.open(),l.write("<!DOCTYPE html><html></html>"),r=s,i=a,!(n=t).defaultView||r===n.defaultView.pageXOffset&&i===n.defaultView.pageYOffset||n.defaultView.scrollTo(r,i),l.replaceChild(l.adoptNode(u),l.documentElement),l.close()}))}},{"./log":13}],3:[function(t,e){function n(t){this.r=0,this.g=0,this.b=0,this.a=null,this.fromArray(t)||this.namedColor(t)||this.rgb(t)||this.rgba(t)||this.hex6(t)||this.hex3(t)}n.prototype.darken=function(t){return t=1-t,new n([Math.round(this.r*t),Math.round(this.g*t),Math.round(this.b*t),this.a])},n.prototype.isTransparent=function(){return 0===this.a},n.prototype.isBlack=function(){return 0===this.r&&0===this.g&&0===this.b},n.prototype.fromArray=function(t){return Array.isArray(t)&&(this.r=Math.min(t[0],255),this.g=Math.min(t[1],255),this.b=Math.min(t[2],255),3<t.length)&&(this.a=t[3]),Array.isArray(t)};var r=/^#([a-f0-9]{3})$/i,i=(n.prototype.hex3=function(t){return null!==(t=t.match(r))&&(this.r=parseInt(t[1][0]+t[1][0],16),this.g=parseInt(t[1][1]+t[1][1],16),this.b=parseInt(t[1][2]+t[1][2],16)),null!==t},/^#([a-f0-9]{6})$/i),o=(n.prototype.hex6=function(t){var e=null;return null!==(e=t.match(i))&&(this.r=parseInt(e[1].substring(0,2),16),this.g=parseInt(e[1].substring(2,4),16),this.b=parseInt(e[1].substring(4,6),16)),null!==e},/^rgb\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*\)$/),s=(n.prototype.rgb=function(t){return null!==(t=t.match(o))&&(this.r=Number(t[1]),this.g=Number(t[2]),this.b=Number(t[3])),null!==t},/^rgba\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d?\.?\d+)\s*\)$/),a=(n.prototype.rgba=function(t){return null!==(t=t.match(s))&&(this.r=Number(t[1]),this.g=Number(t[2]),this.b=Number(t[3]),this.a=Number(t[4])),null!==t},n.prototype.toString=function(){return null!==this.a&&1!==this.a?"rgba("+[this.r,this.g,this.b,this.a].join(",")+")":"rgb("+[this.r,this.g,this.b].join(",")+")"},n.prototype.namedColor=function(t){t=t.toLowerCase();var e=a[t];if(e)this.r=e[0],this.g=e[1],this.b=e[2];else if("transparent"===t)return!(this.r=this.g=this.b=this.a=0);return!!e},n.prototype.isColor=!0,{aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]});e.exports=n},{}],4:[function(t,e){function n(t,e){var n,i,o,a,u,c,h,d,v,y=m++;return(e=e||{}).logging&&(l.options.logging=!0,l.options.start=Date.now()),e.async=void 0===e.async||e.async,e.allowTaint=void 0!==e.allowTaint&&e.allowTaint,e.removeContainer=void 0===e.removeContainer||e.removeContainer,e.javascriptEnabled=void 0!==e.javascriptEnabled&&e.javascriptEnabled,e.imageTimeout=void 0===e.imageTimeout?1e4:e.imageTimeout,e.renderer="function"==typeof e.renderer?e.renderer:s,e.strict=!!e.strict,"string"==typeof t?"string"!=typeof e.proxy?Promise.reject("Proxy must be used when rendering url"):(n=null!=e.width?e.width:window.innerWidth,i=null!=e.height?e.height:window.innerHeight,p((a=t,(o=document.createElement("a")).href=a,o.href=o.href,o),e.proxy,document,n,i,e).then((function(t){return r(t.contentWindow.document.documentElement,t,e,n,i)}))):((a=(void 0===t?[document.documentElement]:t.length?t:[t])[0]).setAttribute(g+y,y),u=a.ownerDocument,c=e,h=a.ownerDocument.defaultView.innerWidth,d=a.ownerDocument.defaultView.innerHeight,v=y,f(u,u,h,d,c,u.defaultView.pageXOffset,u.defaultView.pageYOffset).then((function(t){l("Document cloned");var e=g+v,n="["+e+"='"+v+"']",i=(e=(u.querySelector(n).removeAttribute(e),t.contentWindow),e.document.querySelector(n));return Promise.resolve("function"!=typeof c.onclone||c.onclone(e.document)).then((function(){return r(i,t,c,h,d)}))})).then((function(t){return"function"==typeof e.onrendered&&(l("options.onrendered is deprecated, html2canvas returns a Promise containing the canvas"),e.onrendered(t)),t})))}function r(t,e,n,r,s){var c=e.contentWindow,h=new o(c.document),f=new a(n,h),p=d(t),g=(r="view"===n.type?r:(r=c.document,Math.max(Math.max(r.body.scrollWidth,r.documentElement.scrollWidth),Math.max(r.body.offsetWidth,r.documentElement.offsetWidth),Math.max(r.body.clientWidth,r.documentElement.clientWidth))),s="view"===n.type?s:(s=c.document,Math.max(Math.max(s.body.scrollHeight,s.documentElement.scrollHeight),Math.max(s.body.offsetHeight,s.documentElement.offsetHeight),Math.max(s.body.clientHeight,s.documentElement.clientHeight))),new n.renderer(r,s,f,n,document));return new u(t,g,h,f,n).ready.then((function(){var r,o;return l("Finished rendering"),r="view"===n.type?i(g.canvas,{width:g.canvas.width,height:g.canvas.height,top:0,left:0,x:0,y:0}):t===c.document.body||t===c.document.documentElement||null!=n.canvas?g.canvas:i(g.canvas,{width:(null!=n.width?n:p).width,height:(null!=n.height?n:p).height,top:p.top,left:p.left,x:0,y:0}),o=e,n.removeContainer&&(o.parentNode.removeChild(o),l("Cleaned up container")),r}))}function i(t,e){var n=document.createElement("canvas"),r=Math.min(t.width-1,Math.max(0,e.left)),i=Math.min(t.width,Math.max(1,e.left+e.width)),o=Math.min(t.height-1,Math.max(0,e.top)),s=Math.min(t.height,Math.max(1,e.top+e.height));n.width=e.width,n.height=e.height,i-=r,s-=o;return l("Cropping canvas at:","left:",e.left,"top:",e.top,"width:",i,"height:",s),l("Resulting crop with width",e.width,"and height",e.height,"with x",r,"and y",o),n.getContext("2d").drawImage(t,r,o,i,s,e.x,e.y,i,s),n}var o=t("./support"),s=t("./renderers/canvas"),a=t("./imageloader"),u=t("./nodeparser"),c=t("./nodecontainer"),l=t("./log"),h=t("./utils"),f=t("./clone"),p=t("./proxy").loadUrlDocument,d=h.getBounds,g="data-html2canvas-node",m=0;n.CanvasRenderer=s,n.NodeContainer=c,n.log=l,n.utils=h,t="undefined"==typeof document||"function"!=typeof Object.create||"function"!=typeof document.createElement("canvas").getContext?function(){return Promise.reject("No canvas support")}:n;e.exports=t},{"./clone":2,"./imageloader":11,"./log":13,"./nodecontainer":14,"./nodeparser":15,"./proxy":16,"./renderers/canvas":20,"./support":22,"./utils":26}],5:[function(t,e){function n(t){var e;this.src=t,r("DummyImageContainer for",t),this.promise&&this.image||(r("Initiating DummyImageContainer"),n.prototype.image=new Image,e=this.image,n.prototype.promise=new Promise((function(t,n){e.onload=t,e.onerror=n,e.src=i(),!0===e.complete&&t(e)})))}var r=t("./log"),i=t("./utils").smallImage;e.exports=n},{"./log":13,"./utils":26}],6:[function(t,e){var n=t("./utils").smallImage;e.exports=function(t,e){var r=document.createElement("div"),i=document.createElement("img"),o=document.createElement("span"),s="Hidden Text";r.style.visibility="hidden",r.style.fontFamily=t,r.style.fontSize=e,r.style.margin=0,r.style.padding=0,document.body.appendChild(r),i.src=n(),i.width=1,i.height=1,i.style.margin=0,i.style.padding=0,i.style.verticalAlign="baseline",o.style.fontFamily=t,o.style.fontSize=e,o.style.margin=0,o.style.padding=0,o.appendChild(document.createTextNode(s)),r.appendChild(o),r.appendChild(i),t=i.offsetTop-o.offsetTop+1,r.removeChild(o),r.appendChild(document.createTextNode(s)),r.style.lineHeight="normal",i.style.verticalAlign="super",e=i.offsetTop-r.offsetTop+1,document.body.removeChild(r),this.baseline=t,this.lineWidth=1,this.middle=e}},{"./utils":26}],7:[function(t,e){function n(){this.data={}}var r=t("./font");n.prototype.getMetrics=function(t,e){return void 0===this.data[t+"-"+e]&&(this.data[t+"-"+e]=new r(t,e)),this.data[t+"-"+e]},e.exports=n},{"./font":6}],8:[function(t,e){function n(e,n,i){this.image=null,this.src=e;var o=this,s=r(e);this.promise=(n?new Promise((function(t){"about:blank"===e.contentWindow.document.URL||null==e.contentWindow.document.documentElement?e.contentWindow.onload=e.onload=function(){t(e)}:t(e)})):this.proxyLoad(i.proxy,s,i)).then((function(e){return t("./core")(e.contentWindow.document.documentElement,{type:"view",width:e.width,height:e.height,proxy:i.proxy,javascriptEnabled:i.javascriptEnabled,removeContainer:i.removeContainer,allowTaint:i.allowTaint,imageTimeout:i.imageTimeout/2})})).then((function(t){return o.image=t}))}var r=t("./utils").getBounds,i=t("./proxy").loadUrlDocument;n.prototype.proxyLoad=function(t,e,n){var r=this.src;return i(r.src,t,r.ownerDocument,e.width,e.height,n)},e.exports=n},{"./core":4,"./proxy":16,"./utils":26}],9:[function(t,e){function n(t){this.src=t.value,this.colorStops=[],this.type=null,this.x0=.5,this.y0=.5,this.x1=.5,this.y1=.5,this.promise=Promise.resolve(!0)}n.TYPES={LINEAR:1,RADIAL:2},n.REGEXP_COLORSTOP=/^\s*(rgba?\(\s*\d{1,3},\s*\d{1,3},\s*\d{1,3}(?:,\s*[0-9\.]+)?\s*\)|[a-z]{3,20}|#[a-f0-9]{3,6})(?:\s+(\d{1,3}(?:\.\d+)?)(%|px)?)?(?:\s|$)/i,e.exports=n},{}],10:[function(t,e){e.exports=function(t,e){this.src=t,this.image=new Image;var n=this;this.tainted=null,this.promise=new Promise((function(r,i){n.image.onload=r,n.image.onerror=i,e&&(n.image.crossOrigin="anonymous"),n.image.src=t,!0===n.image.complete&&r(n.image)}))}},{}],11:[function(t,e){function n(t,e){this.link=null,this.options=t,this.support=e,this.origin=this.getOrigin(window.location.href)}var r=t("./log"),i=t("./imagecontainer"),o=t("./dummyimagecontainer"),s=t("./proxyimagecontainer"),a=t("./framecontainer"),u=t("./svgcontainer"),c=t("./svgnodecontainer"),l=t("./lineargradientcontainer"),h=t("./webkitgradientcontainer"),f=t("./utils").bind;n.prototype.findImages=function(t){var e=[];return t.reduce((function(t,e){switch(e.node.nodeName){case"IMG":return t.concat([{args:[e.node.src],method:"url"}]);case"svg":case"IFRAME":return t.concat([{args:[e.node],method:e.node.nodeName}])}return t}),[]).forEach(this.addImage(e,this.loadImage),this),e},n.prototype.findBackgroundImage=function(t,e){return e.parseBackgroundImages().filter(this.hasImageBackground).forEach(this.addImage(t,this.loadImage),this),t},n.prototype.addImage=function(t,e){return function(n){n.args.forEach((function(i){this.imageExists(t,i)||(t.splice(0,0,e.call(this,n)),r("Added image #"+t.length,"string"==typeof i?i.substring(0,100):i))}),this)}},n.prototype.hasImageBackground=function(t){return"none"!==t.method},n.prototype.loadImage=function(t){var e;return"url"===t.method?(e=t.args[0],!this.isSVG(e)||this.support.svg||this.options.allowTaint?e.match(/data:image\/.*;base64,/i)?new i(e.replace(/url\(['"]{0,}|['"]{0,}\)$/gi,""),!1):this.isSameOrigin(e)||!0===this.options.allowTaint||this.isSVG(e)?new i(e,!1):this.support.cors&&!this.options.allowTaint&&this.options.useCORS?new i(e,!0):this.options.proxy?new s(e,this.options.proxy):new o(e):new u(e)):"linear-gradient"===t.method?new l(t):"gradient"===t.method?new h(t):"svg"===t.method?new c(t.args[0],this.support.svg):"IFRAME"===t.method?new a(t.args[0],this.isSameOrigin(t.args[0].src),this.options):new o(t)},n.prototype.isSVG=function(t){return"svg"===t.substring(t.length-3).toLowerCase()||u.prototype.isInline(t)},n.prototype.imageExists=function(t,e){return t.some((function(t){return t.src===e}))},n.prototype.isSameOrigin=function(t){return this.getOrigin(t)===this.origin},n.prototype.getOrigin=function(t){var e=this.link||(this.link=document.createElement("a"));return e.href=t,e.href=e.href,e.protocol+e.hostname+e.port},n.prototype.getPromise=function(t){return this.timeout(t,this.options.imageTimeout).catch((function(){return new o(t.src).promise.then((function(e){t.image=e}))}))},n.prototype.get=function(t){var e=null;return this.images.some((function(n){return(e=n).src===t}))?e:null},n.prototype.fetch=function(t){return this.images=t.reduce(f(this.findBackgroundImage,this),this.findImages(t)),this.images.forEach((function(t,e){t.promise.then((function(){r("Succesfully loaded image #"+(e+1),t)}),(function(n){r("Failed loading image #"+(e+1),t,n)}))})),this.ready=Promise.all(this.images.map(this.getPromise,this)),r("Finished searching images"),this},n.prototype.timeout=function(t,e){var n,i=Promise.race([t.promise,new Promise((function(i,o){n=setTimeout((function(){r("Timed out loading image",t),o(t)}),e)}))]).then((function(t){return clearTimeout(n),t}));return i.catch((function(){clearTimeout(n)})),i},e.exports=n},{"./dummyimagecontainer":5,"./framecontainer":8,"./imagecontainer":10,"./lineargradientcontainer":12,"./log":13,"./proxyimagecontainer":17,"./svgcontainer":23,"./svgnodecontainer":24,"./utils":26,"./webkitgradientcontainer":27}],12:[function(t,e){function n(t){r.apply(this,arguments),this.type=r.TYPES.LINEAR;var e=n.REGEXP_DIRECTION.test(t.args[0])||!r.REGEXP_COLORSTOP.test(t.args[0]);e?t.args[0].split(/\s+/).reverse().forEach((function(t,e){switch(t){case"left":this.x0=0,this.x1=1;break;case"top":this.y0=0,this.y1=1;break;case"right":this.x0=1,this.x1=0;break;case"bottom":this.y0=1,this.y1=0;break;case"to":var n=this.y0,r=this.x0;this.y0=this.y1,this.x0=this.x1,this.x1=r,this.y1=n;break;case"center":break;default:r=.01*parseFloat(t,10),isNaN(r)||(0===e?(this.y0=r,this.y1=1-this.y0):(this.x0=r,this.x1=1-this.x0))}}),this):(this.y0=0,this.y1=1),this.colorStops=t.args.slice(e?1:0).map((function(t){t=t.match(r.REGEXP_COLORSTOP);var e=+t[2],n=0==e?"%":t[3];return{color:new i(t[1]),stop:"%"===n?e/100:null}})),null===this.colorStops[0].stop&&(this.colorStops[0].stop=0),null===this.colorStops[this.colorStops.length-1].stop&&(this.colorStops[this.colorStops.length-1].stop=1),this.colorStops.forEach((function(t,e){null===t.stop&&this.colorStops.slice(e).some((function(n,r){return null!==n.stop&&(t.stop=(n.stop-this.colorStops[e-1].stop)/(r+1)+this.colorStops[e-1].stop,!0)}),this)}),this)}var r=t("./gradientcontainer"),i=t("./color");n.prototype=Object.create(r.prototype),n.REGEXP_DIRECTION=/^\s*(?:to|left|right|top|bottom|center|\d{1,3}(?:\.\d+)?%?)(?:\s|$)/i,e.exports=n},{"./color":3,"./gradientcontainer":9}],13:[function(t,e){function n(){n.options.logging&&window.console&&window.console.log&&Function.prototype.bind.call(window.console.log,window.console).apply(window.console,[Date.now()-n.options.start+"ms","html2canvas:"].concat([].slice.call(arguments,0)))}n.options={logging:!1},e.exports=n},{}],14:[function(t,e){function n(t,e){this.node=t,this.parent=e,this.stack=null,this.bounds=null,this.borders=null,this.clip=[],this.backgroundClip=[],this.offsetBounds=null,this.visible=null,this.computedStyles=null,this.colors={},this.styles={},this.backgroundImages=null,this.transformData=null,this.transformMatrix=null,this.isPseudoElement=!1,this.opacity=null}function r(t){return-1!==t.toString().indexOf("%")}function i(t){return t.replace("px","")}function o(t){return parseFloat(t)}var s=t("./color"),a=(t=t("./utils"),t.getBounds),u=t.parseBackgrounds,c=t.offsetBounds;n.prototype.cloneTo=function(t){t.visible=this.visible,t.borders=this.borders,t.bounds=this.bounds,t.clip=this.clip,t.backgroundClip=this.backgroundClip,t.computedStyles=this.computedStyles,t.styles=this.styles,t.backgroundImages=this.backgroundImages,t.opacity=this.opacity},n.prototype.getOpacity=function(){return null===this.opacity?this.opacity=this.cssFloat("opacity"):this.opacity},n.prototype.assignStack=function(t){(this.stack=t).children.push(this)},n.prototype.isElementVisible=function(){return this.node.nodeType===Node.TEXT_NODE?this.parent.visible:"none"!==this.css("display")&&"hidden"!==this.css("visibility")&&!this.node.hasAttribute("data-html2canvas-ignore")&&("INPUT"!==this.node.nodeName||"hidden"!==this.node.getAttribute("type"))},n.prototype.css=function(t){return this.computedStyles||(this.computedStyles=this.isPseudoElement?this.parent.computedStyle(this.before?":before":":after"):this.computedStyle(null)),this.styles[t]||(this.styles[t]=this.computedStyles[t])},n.prototype.prefixedCss=function(t){var e=this.css(t);return void 0===e&&["webkit","moz","ms","o"].some((function(n){return void 0!==(e=this.css(n+t.substr(0,1).toUpperCase()+t.substr(1)))}),this),void 0===e?null:e},n.prototype.computedStyle=function(t){return this.node.ownerDocument.defaultView.getComputedStyle(this.node,t)},n.prototype.cssInt=function(t){return t=parseInt(this.css(t),10),isNaN(t)?0:t},n.prototype.color=function(t){return this.colors[t]||(this.colors[t]=new s(this.css(t)))},n.prototype.cssFloat=function(t){return t=parseFloat(this.css(t)),isNaN(t)?0:t},n.prototype.fontWeight=function(){var t=this.css("fontWeight");switch(parseInt(t,10)){case 401:t="bold";break;case 400:t="normal"}return t},n.prototype.parseClip=function(){var t=this.css("clip").match(this.CLIP);return t?{top:parseInt(t[1],10),right:parseInt(t[2],10),bottom:parseInt(t[3],10),left:parseInt(t[4],10)}:null},n.prototype.parseBackgroundImages=function(){return this.backgroundImages||(this.backgroundImages=u(this.css("backgroundImage")))},n.prototype.cssList=function(t,e){return t=(this.css(t)||"").split(","),1===(t=(t=t[e||0]||t[0]||"auto").trim().split(" ")).length?[t[0],r(t[0])?"auto":t[0]]:t},n.prototype.parseBackgroundSize=function(t,e,n){var i,o;n=this.cssList("backgroundSize",n);if(r(n[0]))i=t.width*parseFloat(n[0])/100;else{if(/contain|cover/.test(n[0]))return t.width/t.height<(o=e.width/e.height)^"contain"===n[0]?{width:t.height*o,height:t.height}:{width:t.width,height:t.width/o};i=parseInt(n[0],10)}return o="auto"===n[0]&&"auto"===n[1]?e.height:"auto"===n[1]?i/e.width*e.height:r(n[1])?t.height*parseFloat(n[1])/100:parseInt(n[1],10),{width:i="auto"===n[0]?o/e.height*e.width:i,height:o}},n.prototype.parseBackgroundPosition=function(t,e,n,i){n=this.cssList("backgroundPosition",n);var o=r(n[0])?(t.width-(i||e).width)*(parseFloat(n[0])/100):parseInt(n[0],10);t="auto"===n[1]?o/e.width*e.height:r(n[1])?(t.height-(i||e).height)*parseFloat(n[1])/100:parseInt(n[1],10);return{left:o="auto"===n[0]?t/e.height*e.width:o,top:t}},n.prototype.parseBackgroundRepeat=function(t){return this.cssList("backgroundRepeat",t)[0]},n.prototype.parseTextShadows=function(){var t=this.css("textShadow"),e=[];if(t&&"none"!==t)for(var n=t.match(this.TEXT_SHADOW_PROPERTY),r=0;n&&r<n.length;r++){var i=n[r].match(this.TEXT_SHADOW_VALUES);e.push({color:new s(i[0]),offsetX:i[1]?parseFloat(i[1].replace("px","")):0,offsetY:i[2]?parseFloat(i[2].replace("px","")):0,blur:i[3]?i[3].replace("px",""):0})}return e},n.prototype.parseTransform=function(){var t,e;return this.transformData||(this.hasTransform()?(t=this.parseBounds(),(e=this.prefixedCss("transformOrigin").split(" ").map(i).map(o))[0]+=t.left,e[1]+=t.top,this.transformData={origin:e,matrix:this.parseTransformMatrix()}):this.transformData={origin:[0,0],matrix:[1,0,0,1,0,0]}),this.transformData},n.prototype.parseTransformMatrix=function(){var t;return this.transformMatrix||(t=(t=this.prefixedCss("transform"))?(t=t.match(this.MATRIX_PROPERTY))&&"matrix"===t[1]?t[2].split(",").map((function(t){return parseFloat(t.trim())})):t&&"matrix3d"===t[1]?[(t=t[2].split(",").map((function(t){return parseFloat(t.trim())})))[0],t[1],t[4],t[5],t[12],t[13]]:void 0:null,this.transformMatrix=t||[1,0,0,1,0,0]),this.transformMatrix},n.prototype.parseBounds=function(){return this.bounds||(this.bounds=(this.hasTransform()?c:a)(this.node))},n.prototype.hasTransform=function(){return"1,0,0,1,0,0"!==this.parseTransformMatrix().join(",")||this.parent&&this.parent.hasTransform()},n.prototype.getValue=function(){var t,e=this.node.value||"";return"SELECT"===this.node.tagName?e=(t=(t=this.node).options[t.selectedIndex||0])&&t.text||"":"password"===this.node.type&&(e=Array(e.length+1).join("•")),0===e.length?this.node.placeholder||"":e},n.prototype.MATRIX_PROPERTY=/(matrix|matrix3d)\((.+)\)/,n.prototype.TEXT_SHADOW_PROPERTY=/((rgba|rgb)\([^\)]+\)(\s-?\d+px){0,})/g,n.prototype.TEXT_SHADOW_VALUES=/(-?\d+px)|(#.+)|(rgb\(.+\))|(rgba\(.+\))/g,n.prototype.CLIP=/^rect\((\d+)px,? (\d+)px,? (\d+)px,? (\d+)px\)$/,e.exports=n},{"./color":3,"./utils":26}],15:[function(t,e){function n(t,e,n,r,i){T("Starting NodeParser"),this.renderer=e,this.options=i,this.range=null,this.support=n,this.renderQueue=[],this.stack=new R(!0,1,t.ownerDocument,null);var o;n=new I(t,null);i.background&&e.rectangle(0,0,e.width,e.height,new D(i.background)),t===t.ownerDocument.documentElement&&(o=new I(n.color("backgroundColor").isTransparent()?t.ownerDocument.body:t.ownerDocument.documentElement,null),e.rectangle(0,0,e.width,e.height,o.color("backgroundColor"))),n.visibile=n.isElementVisible(),this.createPseudoHideStyles(t.ownerDocument),this.disableAnimations(t.ownerDocument),this.nodes=E([n].concat(this.getChildren(n)).filter((function(t){return t.visible=t.isElementVisible()})).map(this.getPseudoElements,this)),this.fontMetrics=new N,T("Fetched nodes, total:",this.nodes.length),T("Calculate overflow clips"),this.calculateOverflowClips(),T("Start fetching images"),this.images=r.fetch(this.nodes.filter(_)),this.ready=this.images.ready.then(M((function(){return T("Images loaded, starting parsing"),T("Creating stacking contexts"),this.createStackingContexts(),T("Sorting stacking contexts"),this.sortStackingContexts(this.stack),this.parse(this.stack),T("Render queue created with "+this.renderQueue.length+" items"),new Promise(M((function(t){i.async?"function"==typeof i.async?i.async.call(this,this.renderQueue,t):0<this.renderQueue.length?(this.renderIndex=0,this.asyncRenderer(this.renderQueue,t)):t():(this.renderQueue.forEach(this.paint,this),t())}),this))}),this))}function r(t){return t.parent&&t.parent.clip.length}function i(){}function o(t,e,n,r){return t.map((function(i,o){if(0<i.width){var s=e.left,a=e.top,u=e.width,l=e.height-t[2].width;switch(o){case 0:l=t[0].width,i.args=c({c1:[s,a],c2:[s+u,a],c3:[s+u-t[1].width,a+l],c4:[s+t[3].width,a+l]},r[0],r[1],n.topLeftOuter,n.topLeftInner,n.topRightOuter,n.topRightInner);break;case 1:s=e.left+e.width-t[1].width,u=t[1].width,i.args=c({c1:[s+u,a],c2:[s+u,a+l+t[2].width],c3:[s,a+l],c4:[s,a+t[0].width]},r[1],r[2],n.topRightOuter,n.topRightInner,n.bottomRightOuter,n.bottomRightInner);break;case 2:a=a+e.height-t[2].width,l=t[2].width,i.args=c({c1:[s+u,a+l],c2:[s,a+l],c3:[s+t[3].width,a],c4:[s+u-t[3].width,a]},r[2],r[3],n.bottomRightOuter,n.bottomRightInner,n.bottomLeftOuter,n.bottomLeftInner);break;case 3:u=t[3].width,i.args=c({c1:[s,a+l+t[2].width],c2:[s,a],c3:[s+u,a+t[0].width],c4:[s+u,a+l]},r[3],r[0],n.bottomLeftOuter,n.bottomLeftInner,n.topLeftOuter,n.topLeftInner)}}return i}))}function s(t,e,n,r){var i=(Math.sqrt(2)-1)/3*4,o=n*i;i*=r,n=t+n,r=e+r;return{topLeft:u({x:t,y:r},{x:t,y:r-i},{x:n-o,y:e},{x:n,y:e}),topRight:u({x:t,y:e},{x:t+o,y:e},{x:n,y:r-i},{x:n,y:r}),bottomRight:u({x:n,y:e},{x:n,y:e+i},{x:t+o,y:r},{x:t,y:r}),bottomLeft:u({x:n,y:r},{x:n-o,y:r},{x:t,y:e+i},{x:t,y:e})}}function a(t,e,n){var r=t.left,i=t.top,o=t.width,a=(t=t.height,e[0][0]<o/2?e[0][0]:o/2),u=e[0][1]<t/2?e[0][1]:t/2,c=e[1][0]<o/2?e[1][0]:o/2,l=e[1][1]<t/2?e[1][1]:t/2,h=e[2][0]<o/2?e[2][0]:o/2,f=e[2][1]<t/2?e[2][1]:t/2,p=e[3][0]<o/2?e[3][0]:o/2,d=(e=e[3][1]<t/2?e[3][1]:t/2,o-c),g=t-f,m=o-h,v=t-e;return{topLeftOuter:s(r,i,a,u).topLeft.subdivide(.5),topLeftInner:s(r+n[3].width,i+n[0].width,Math.max(0,a-n[3].width),Math.max(0,u-n[0].width)).topLeft.subdivide(.5),topRightOuter:s(r+d,i,c,l).topRight.subdivide(.5),topRightInner:s(r+Math.min(d,o+n[3].width),i+n[0].width,d>o+n[3].width?0:c-n[3].width,l-n[0].width).topRight.subdivide(.5),bottomRightOuter:s(r+m,i+g,h,f).bottomRight.subdivide(.5),bottomRightInner:s(r+Math.min(m,o-n[3].width),i+Math.min(g,t+n[0].width),Math.max(0,h-n[1].width),f-n[2].width).bottomRight.subdivide(.5),bottomLeftOuter:s(r,i+v,p,e).bottomLeft.subdivide(.5),bottomLeftInner:s(r+n[3].width,i+v,Math.max(0,p-n[3].width),e-n[2].width).bottomLeft.subdivide(.5)}}function u(t,e,n,r){function i(t,e,n){return{x:t.x+(e.x-t.x)*n,y:t.y+(e.y-t.y)*n}}return{start:t,startControl:e,endControl:n,end:r,subdivide:function(o){var s=i(t,e,o),a=i(e,n,o),c=i(n,r,o),l=i(s,a,o);a=i(a,c,o),o=i(l,a,o);return[u(t,s,l,o),u(o,a,c,r)]},curveTo:function(t){t.push(["bezierCurve",e.x,e.y,n.x,n.y,r.x,r.y])},curveToReversed:function(r){r.push(["bezierCurve",n.x,n.y,e.x,e.y,t.x,t.y])}}}function c(t,e,n,r,i,o,s){var a=[];return 0<e[0]||0<e[1]?(a.push(["line",r[1].start.x,r[1].start.y]),r[1].curveTo(a)):a.push(["line",t.c1[0],t.c1[1]]),0<n[0]||0<n[1]?(a.push(["line",o[0].start.x,o[0].start.y]),o[0].curveTo(a),a.push(["line",s[0].end.x,s[0].end.y]),s[0].curveToReversed(a)):(a.push(["line",t.c2[0],t.c2[1]]),a.push(["line",t.c3[0],t.c3[1]])),0<e[0]||0<e[1]?(a.push(["line",i[1].end.x,i[1].end.y]),i[1].curveToReversed(a)):a.push(["line",t.c4[0],t.c4[1]]),a}function l(t,e,n,r,i,o,s){0<e[0]||0<e[1]?(t.push(["line",r[0].start.x,r[0].start.y]),r[0].curveTo(t),r[1].curveTo(t)):t.push(["line",o,s]),(0<n[0]||0<n[1])&&t.push(["line",i[0].start.x,i[0].start.y])}function h(t){return t.cssInt("zIndex")<0}function f(t){return 0<t.cssInt("zIndex")}function p(t){return 0===t.cssInt("zIndex")}function d(t){return-1!==["inline","inline-block","inline-table"].indexOf(t.css("display"))}function g(t){return t instanceof R}function m(t){return 0<t.node.data.trim().length}function v(t){return t.nodeType===Node.TEXT_NODE||t.nodeType===Node.ELEMENT_NODE}function y(t){return"static"!==t.css("position")}function b(t){return"none"!==t.css("float")}function w(t){var e=this;return function(){return!t.apply(e,arguments)}}function _(t){return t.node.nodeType===Node.ELEMENT_NODE}function x(t){return!0===t.isPseudoElement}function C(t){return t.node.nodeType===Node.TEXT_NODE}function k(t){return parseInt(t,10)}function S(t){return t.width}function O(t){return t.node.nodeType!==Node.ELEMENT_NODE||-1===["SCRIPT","HEAD","TITLE","OBJECT","BR","OPTION"].indexOf(t.node.nodeName)}function E(t){return[].concat.apply([],t)}var T=t("./log"),A=t("punycode"),I=t("./nodecontainer"),j=t("./textcontainer"),P=t("./pseudoelementcontainer"),N=t("./fontmetrics"),D=t("./color"),R=t("./stackingcontext"),M=(t=t("./utils"),t.bind),W=t.getBounds,L=t.parseBackgrounds,F=t.offsetBounds,H=(n.prototype.calculateOverflowClips=function(){this.nodes.forEach((function(t){var e,n;_(t)?(x(t)&&t.appendToDOM(),t.borders=this.parseBorders(t),e="hidden"===t.css("overflow")?[t.borders.clip]:[],(n=t.parseClip())&&-1!==["absolute","fixed"].indexOf(t.css("position"))&&e.push([["rect",t.bounds.left+n.left,t.bounds.top+n.top,n.right-n.left,n.bottom-n.top]]),t.clip=r(t)?t.parent.clip.concat(e):e,t.backgroundClip="hidden"!==t.css("overflow")?t.clip.concat([t.borders.clip]):t.clip,x(t)&&t.cleanDOM()):C(t)&&(t.clip=r(t)?t.parent.clip:[]),x(t)||(t.bounds=null)}),this)},n.prototype.asyncRenderer=function(t,e,n){n=n||Date.now(),this.paint(t[this.renderIndex++]),t.length===this.renderIndex?e():n+20>Date.now()?this.asyncRenderer(t,e,n):setTimeout(M((function(){this.asyncRenderer(t,e)}),this),0)},n.prototype.createPseudoHideStyles=function(t){this.createStyles(t,"."+P.prototype.PSEUDO_HIDE_ELEMENT_CLASS_BEFORE+':before { content: "" !important; display: none !important; }.'+P.prototype.PSEUDO_HIDE_ELEMENT_CLASS_AFTER+':after { content: "" !important; display: none !important; }')},n.prototype.disableAnimations=function(t){this.createStyles(t,"* { -webkit-animation: none !important; -moz-animation: none !important; -o-animation: none !important; animation: none !important; -webkit-transition: none !important; -moz-transition: none !important; -o-transition: none !important; transition: none !important;}")},n.prototype.createStyles=function(t,e){var n=t.createElement("style");n.innerHTML=e,t.body.appendChild(n)},n.prototype.getPseudoElements=function(t){var e,n=[[t]];return t.node.nodeType===Node.ELEMENT_NODE&&(e=this.getPseudoElement(t,":before"),t=this.getPseudoElement(t,":after"),e&&n.push(e),t)&&n.push(t),E(n)},n.prototype.getPseudoElement=function(t,e){var n=t.computedStyle(e);if(!n||!n.content||"none"===n.content||"-moz-alt-content"===n.content||"none"===n.display)return null;i=n.content;for(var r=(r=i.substr(0,1))===i.substr(i.length-1)&&r.match(/'|"/)?i.substr(1,i.length-2):i,i="url"===r.substr(0,3),o=document.createElement(i?"img":"html2canvaspseudoelement"),s=(t=new P(o,t,e),n.length-1);0<=s;s--){var a=n.item(s).replace(/(\-[a-z])/g,(function(t){return t.toUpperCase().replace("-","")}));o.style[a]=n[a]}return o.className=P.prototype.PSEUDO_HIDE_ELEMENT_CLASS_BEFORE+" "+P.prototype.PSEUDO_HIDE_ELEMENT_CLASS_AFTER,i?(o.src=L(r)[0].args[0],[t]):(e=document.createTextNode(r),o.appendChild(e),[t,new j(e,t)])},n.prototype.getChildren=function(t){return E([].filter.call(t.node.childNodes,v).map((function(e){var n=[new(e.nodeType===Node.TEXT_NODE?j:I)(e,t)].filter(O);return e.nodeType===Node.ELEMENT_NODE&&n.length&&"TEXTAREA"!==e.tagName?n[0].isElementVisible()?n.concat(this.getChildren(n[0])):[]:n}),this))},n.prototype.newStackingContext=function(t,e){var n=new R(e,t.getOpacity(),t.node,t.parent);t.cloneTo(n),(e?n.getParentStack(this):n.parent.stack).contexts.push(n),t.stack=n},n.prototype.createStackingContexts=function(){this.nodes.forEach((function(t){var e,n;_(t)&&(this.isRootElement(t)||t.getOpacity()<1||(n=(e=t).css("position"),"auto"!==(-1!==["absolute","relative","fixed"].indexOf(n)?e.css("zIndex"):"auto"))||this.isBodyWithTransparentRoot(t)||t.hasTransform())?this.newStackingContext(t,!0):_(t)&&(y(t)&&p(t)||-1!==["inline-block","inline-table"].indexOf(t.css("display"))||b(t))?this.newStackingContext(t,!1):t.assignStack(t.parent.stack)}),this)},n.prototype.isBodyWithTransparentRoot=function(t){return"BODY"===t.node.nodeName&&t.parent.color("backgroundColor").isTransparent()},n.prototype.isRootElement=function(t){return null===t.parent},n.prototype.sortStackingContexts=function(t){var e;t.contexts.sort((e=t.contexts.slice(0),function(t,n){return t.cssInt("zIndex")+e.indexOf(t)/e.length-(n.cssInt("zIndex")+e.indexOf(n)/e.length)})),t.contexts.forEach(this.sortStackingContexts,this)},n.prototype.parseTextBounds=function(t){return function(e,n,r){if("none"!==t.parent.css("textDecoration").substr(0,4)||0!==e.trim().length){if(this.support.rangeBounds&&!t.parent.hasTransform())return r=r.slice(0,n).join("").length,this.getRangeBounds(t.node,r,e.length);if(t.node&&"string"==typeof t.node.data)return n=t.node.splitText(e.length),r=this.getWrapperBounds(t.node,t.parent.hasTransform()),t.node=n,r}else this.support.rangeBounds&&!t.parent.hasTransform()||(t.node=t.node.splitText(e.length));return{}}},n.prototype.getWrapperBounds=function(t,e){var n=t.ownerDocument.createElement("html2canvaswrapper"),r=t.parentNode,i=t.cloneNode(!0);n.appendChild(t.cloneNode(!0)),r.replaceChild(n,t),t=(e?F:W)(n);return r.replaceChild(i,n),t},n.prototype.getRangeBounds=function(t,e,n){var r=this.range||(this.range=t.ownerDocument.createRange());return r.setStart(t,e),r.setEnd(t,e+n),r.getBoundingClientRect()},n.prototype.parse=function(t){var e=t.contexts.filter(h),n=t.children.filter(_),r=n.filter(w(b)),o=r.filter(w(y)).filter(w(d)),s=(n=n.filter(w(y)).filter(b),r.filter(w(y)).filter(d)),a=(r=t.contexts.concat(r.filter(y)).filter(p),t.children.filter(C).filter(m));t=t.contexts.filter(f);e.concat(o).concat(n).concat(s).concat(r).concat(a).concat(t).forEach((function(t){this.renderQueue.push(t),g(t)&&(this.parse(t),this.renderQueue.push(new i))}),this)},n.prototype.paint=function(t){try{t instanceof i?this.renderer.ctx.restore():C(t)?(x(t.parent)&&t.parent.appendToDOM(),this.paintText(t),x(t.parent)&&t.parent.cleanDOM()):this.paintNode(t)}catch(t){if(T(t),this.options.strict)throw t}},n.prototype.paintNode=function(t){g(t)&&(this.renderer.setOpacity(t.opacity),this.renderer.ctx.save(),t.hasTransform())&&this.renderer.setTransform(t.parseTransform()),"INPUT"===t.node.nodeName&&"checkbox"===t.node.type?this.paintCheckbox(t):"INPUT"===t.node.nodeName&&"radio"===t.node.type?this.paintRadio(t):this.paintElement(t)},n.prototype.paintElement=function(t){var e=t.parseBounds();this.renderer.clip(t.backgroundClip,(function(){this.renderer.renderBackground(t,e,t.borders.borders.map(S))}),this),this.renderer.clip(t.clip,(function(){this.renderer.renderBorders(t.borders.borders)}),this),this.renderer.clip(t.backgroundClip,(function(){switch(t.node.nodeName){case"svg":case"IFRAME":var n=this.images.get(t.node);n?this.renderer.renderImage(t,e,t.borders,n):T("Error loading <"+t.node.nodeName+">",t.node);break;case"IMG":n=this.images.get(t.node.src),n?this.renderer.renderImage(t,e,t.borders,n):T("Error loading <img>",t.node.src);break;case"CANVAS":this.renderer.renderImage(t,e,t.borders,{image:t.node});break;case"SELECT":case"INPUT":case"TEXTAREA":this.paintFormValue(t)}}),this)},n.prototype.paintCheckbox=function(t){var e=t.parseBounds(),n=Math.min(e.width,e.height),r={width:n-1,height:n-1,top:e.top,left:e.left},i=(e=[3,3],[e,e,e,e]),s=[1,1,1,1].map((function(t){return{color:new D("#A5A5A5"),width:t}})),u=a(r,i,s);this.renderer.clip(t.backgroundClip,(function(){this.renderer.rectangle(r.left+1,r.top+1,r.width-2,r.height-2,new D("#DEDEDE")),this.renderer.renderBorders(o(s,r,u,i)),t.node.checked&&(this.renderer.font(new D("#424242"),"normal","normal","bold",n-3+"px","arial"),this.renderer.text("✔",r.left+n/6,r.top+n-1))}),this)},n.prototype.paintRadio=function(t){var e=t.parseBounds(),n=Math.min(e.width,e.height)-2;this.renderer.clip(t.backgroundClip,(function(){this.renderer.circleStroke(e.left+1,e.top+1,n,new D("#DEDEDE"),1,new D("#A5A5A5")),t.node.checked&&this.renderer.circle(Math.ceil(e.left+n/4)+1,Math.ceil(e.top+n/4)+1,Math.floor(n/2),new D("#424242"))}),this)},n.prototype.paintFormValue=function(t){var e,n,r,i=t.getValue();0<i.length&&(e=t.node.ownerDocument,n=e.createElement("html2canvaswrapper"),["lineHeight","textAlign","fontFamily","fontWeight","fontSize","color","paddingLeft","paddingTop","paddingRight","paddingBottom","width","height","borderLeftStyle","borderTopStyle","borderLeftWidth","borderTopWidth","boxSizing","whiteSpace","wordWrap"].forEach((function(e){try{n.style[e]=t.css(e)}catch(e){T("html2canvas: Parse: Exception caught in renderFormValue: "+e.message)}})),r=t.parseBounds(),n.style.position="fixed",n.style.left=r.left+"px",n.style.top=r.top+"px",n.textContent=i,e.body.appendChild(n),this.paintText(new j(n.firstChild,t)),e.body.removeChild(n))},n.prototype.paintText=function(t){t.applyTextTransform();var e=A.ucs2.decode(t.node.data),n=this.options.letterRendering&&!/^(normal|none|0px)$/.test(t.parent.css("letterSpacing"))||/[^\u0000-\u00ff]/.test(t.node.data)?e.map((function(t){return A.ucs2.encode([t])})):function(t){for(var e,n=[],r=0,i=!1;t.length;)-1!==[32,13,10,9,45].indexOf(t[r])===i?((e=t.splice(0,r)).length&&n.push(A.ucs2.encode(e)),i=!i,r=0):r++,r>=t.length&&(e=t.splice(0,r)).length&&n.push(A.ucs2.encode(e));return n}(e),r=(e=t.parent.fontWeight(),t.parent.css("fontSize")),i=t.parent.css("fontFamily"),o=t.parent.parseTextShadows();this.renderer.font(t.parent.color("color"),t.parent.css("fontStyle"),t.parent.css("fontVariant"),e,r,i),o.length?this.renderer.fontShadow(o[0].color,o[0].offsetX,o[0].offsetY,o[0].blur):this.renderer.clearShadow(),this.renderer.clip(t.parent.clip,(function(){n.map(this.parseTextBounds(t),this).forEach((function(e,o){e&&(this.renderer.text(n[o],e.left,e.bottom),this.renderTextDecoration(t.parent,e,this.fontMetrics.getMetrics(i,r)))}),this)}),this)},n.prototype.renderTextDecoration=function(t,e,n){switch(t.css("textDecoration").split(" ")[0]){case"underline":this.renderer.rectangle(e.left,Math.round(e.top+n.baseline+n.lineWidth),e.width,1,t.color("color"));break;case"overline":this.renderer.rectangle(e.left,Math.round(e.top),e.width,1,t.color("color"));break;case"line-through":this.renderer.rectangle(e.left,Math.ceil(e.top+n.middle+n.lineWidth),e.width,1,t.color("color"))}},{inset:[["darken",.6],["darken",.1],["darken",.1],["darken",.6]]});n.prototype.parseBorders=function(t){var e,n=t.parseBounds(),r=(e=t,["TopLeft","TopRight","BottomRight","BottomLeft"].map((function(t){return t=e.css("border"+t+"Radius").split(" "),t.length<=1&&(t[1]=t[0]),t.map(k)}))),i=["Top","Right","Bottom","Left"].map((function(e,n){var r=t.css("border"+e+"Style"),i=t.color("border"+e+"Color");"inset"===r&&i.isBlack()&&(i=new D([255,255,255,i.a])),r=H[r]?H[r][n]:null;return{width:t.cssInt("border"+e+"Width"),color:r?i[r[0]](r[1]):i,args:null}})),s=a(n,r,i);return{clip:this.parseBackgroundClip(t,s,i,r,n),borders:o(i,n,s,r)}},n.prototype.parseBackgroundClip=function(t,e,n,r,i){var o=[];switch(t.css("backgroundClip")){case"content-box":case"padding-box":l(o,r[0],r[1],e.topLeftInner,e.topRightInner,i.left+n[3].width,i.top+n[0].width),l(o,r[1],r[2],e.topRightInner,e.bottomRightInner,i.left+i.width-n[1].width,i.top+n[0].width),l(o,r[2],r[3],e.bottomRightInner,e.bottomLeftInner,i.left+i.width-n[1].width,i.top+i.height-n[2].width),l(o,r[3],r[0],e.bottomLeftInner,e.topLeftInner,i.left+n[3].width,i.top+i.height-n[2].width);break;default:l(o,r[0],r[1],e.topLeftOuter,e.topRightOuter,i.left,i.top),l(o,r[1],r[2],e.topRightOuter,e.bottomRightOuter,i.left+i.width,i.top),l(o,r[2],r[3],e.bottomRightOuter,e.bottomLeftOuter,i.left+i.width,i.top+i.height),l(o,r[3],r[0],e.bottomLeftOuter,e.topLeftOuter,i.left,i.top+i.height)}return o},e.exports=n},{"./color":3,"./fontmetrics":7,"./log":13,"./nodecontainer":14,"./pseudoelementcontainer":18,"./stackingcontext":21,"./textcontainer":25,"./utils":26,punycode:1}],16:[function(t,e,n){function r(t,e,n){var r="withCredentials"in new XMLHttpRequest;return e?(t=s(e,t,e=o(r)),r?a(t):i(n,t,e).then((function(t){return h(t.content)}))):Promise.reject("No proxy configured")}function i(t,e,n){return new Promise((function(r,i){function o(){delete window.html2canvas.proxy[n],t.body.removeChild(s)}var s=t.createElement("script");window.html2canvas.proxy[n]=function(t){o(),r(t)},s.src=e,s.onerror=function(t){o(),i(t)},t.body.appendChild(s)}))}function o(t){return t?"":"html2canvas_"+Date.now()+"_"+ ++f+"_"+Math.round(1e5*Math.random())}function s(t,e,n){return t+"?url="+encodeURIComponent(e)+(n.length?"&callback=html2canvas.proxy."+n:"")}var a=t("./xhr"),u=t("./utils"),c=t("./log"),l=t("./clone"),h=u.decode64,f=0;n.Proxy=r,n.ProxyURL=function(t,e,n){var r="crossOrigin"in new Image,a=o(r);e=s(e,t,a);return r?Promise.resolve(e):i(n,e,a).then((function(t){return"data:"+t.type+";base64,"+t.content}))},n.loadUrlDocument=function(t,e,n,i,o,s){return new r(t,e,window.document).then((a=t,function(t){var e,n=new DOMParser;try{e=n.parseFromString(t,"text/html")}catch(n){c("DOMParser not supported, falling back to createHTMLDocument"),e=document.implementation.createHTMLDocument("");try{e.open(),e.write(t),e.close()}catch(n){c("createHTMLDocument write not supported, falling back to document.body.innerHTML"),e.body.innerHTML=t}}return n=e.querySelector("base"),n&&n.href.host||((t=e.createElement("base")).href=a,e.head.insertBefore(t,e.head.firstChild)),e})).then((function(t){return l(t,n,i,o,s,0,0)}));var a}},{"./clone":2,"./log":13,"./utils":26,"./xhr":28}],17:[function(t,e){var n=t("./proxy").ProxyURL;e.exports=function(t,e){var r=document.createElement("a"),i=(r.href=t,t=r.href,this.src=t,this.image=new Image,this);this.promise=new Promise((function(r,o){i.image.crossOrigin="Anonymous",i.image.onload=r,i.image.onerror=o,new n(t,e,document).then((function(t){i.image.src=t})).catch(o)}))}},{"./proxy":16}],18:[function(t,e){function n(t,e,n){r.call(this,t,e),this.isPseudoElement=!0,this.before=":before"===n}var r=t("./nodecontainer");n.prototype.cloneTo=function(t){n.prototype.cloneTo.call(this,t),t.isPseudoElement=!0,t.before=this.before},(n.prototype=Object.create(r.prototype)).appendToDOM=function(){this.before?this.parent.node.insertBefore(this.node,this.parent.node.firstChild):this.parent.node.appendChild(this.node),this.parent.node.className+=" "+this.getHideClass()},n.prototype.cleanDOM=function(){this.node.parentNode.removeChild(this.node),this.parent.node.className=this.parent.node.className.replace(this.getHideClass(),"")},n.prototype.getHideClass=function(){return this["PSEUDO_HIDE_ELEMENT_CLASS_"+(this.before?"BEFORE":"AFTER")]},n.prototype.PSEUDO_HIDE_ELEMENT_CLASS_BEFORE="___html2canvas___pseudoelement_before",n.prototype.PSEUDO_HIDE_ELEMENT_CLASS_AFTER="___html2canvas___pseudoelement_after",e.exports=n},{"./nodecontainer":14}],19:[function(t,e){function n(t,e,n,r,i){this.width=t,this.height=e,this.images=n,this.options=r,this.document=i}var r=t("./log");n.prototype.renderImage=function(t,e,n,r){var i=t.cssInt("paddingLeft"),o=t.cssInt("paddingTop"),s=t.cssInt("paddingRight");t=t.cssInt("paddingBottom"),n=n.borders,s=e.width-(n[1].width+n[3].width+i+s),t=e.height-(n[0].width+n[2].width+o+t);this.drawImage(r,0,0,r.image.width||s,r.image.height||t,e.left+i+n[3].width,e.top+o+n[0].width,s,t)},n.prototype.renderBackground=function(t,e,n){0<e.height&&0<e.width&&(this.renderBackgroundColor(t,e),this.renderBackgroundImage(t,e,n))},n.prototype.renderBackgroundColor=function(t,e){t=t.color("backgroundColor"),t.isTransparent()||this.rectangle(e.left,e.top,e.width,e.height,t)},n.prototype.renderBorders=function(t){t.forEach(this.renderBorder,this)},n.prototype.renderBorder=function(t){t.color.isTransparent()||null===t.args||this.drawShape(t.args,t.color)},n.prototype.renderBackgroundImage=function(t,e,n){t.parseBackgroundImages().reverse().forEach((function(i,o,s){switch(i.method){case"url":var a=this.images.get(i.args[0]);a?this.renderBackgroundRepeating(t,e,a,s.length-(o+1),n):r("Error loading background-image",i.args[0]);break;case"linear-gradient":case"gradient":a=this.images.get(i.value),a?this.renderBackgroundGradient(a,e,n):r("Error loading background-image",i.args[0]);break;case"none":break;default:r("Unknown background-image type",i.args[0])}}),this)},n.prototype.renderBackgroundRepeating=function(t,e,n,r,i){var o=t.parseBackgroundSize(e,n.image,r),s=t.parseBackgroundPosition(e,n.image,r,o);switch(t.parseBackgroundRepeat(r)){case"repeat-x":case"repeat no-repeat":this.backgroundRepeatShape(n,s,o,e,e.left+i[3],e.top+s.top+i[0],99999,o.height,i);break;case"repeat-y":case"no-repeat repeat":this.backgroundRepeatShape(n,s,o,e,e.left+s.left+i[3],e.top+i[0],o.width,99999,i);break;case"no-repeat":this.backgroundRepeatShape(n,s,o,e,e.left+s.left+i[3],e.top+s.top+i[0],o.width,o.height,i);break;default:this.renderBackgroundRepeat(n,s,o,{top:e.top,left:e.left},i[3],i[0])}},e.exports=n},{"./log":13}],20:[function(t,e){function n(t,e){i.apply(this,arguments),this.canvas=this.options.canvas||this.document.createElement("canvas"),this.options.canvas||(this.canvas.width=t,this.canvas.height=e),this.ctx=this.canvas.getContext("2d"),this.taintCtx=this.document.createElement("canvas").getContext("2d"),this.ctx.textBaseline="bottom",this.variables={},s("Initialized CanvasRenderer with size",t,"x",e)}function r(t){return 0<t.length}var i=t("../renderer"),o=t("../lineargradientcontainer"),s=t("../log");(n.prototype=Object.create(i.prototype)).setFillStyle=function(t){return this.ctx.fillStyle="object"==typeof t&&t.isColor?t.toString():t,this.ctx},n.prototype.rectangle=function(t,e,n,r,i){this.setFillStyle(i).fillRect(t,e,n,r)},n.prototype.circle=function(t,e,n,r){this.setFillStyle(r),this.ctx.beginPath(),this.ctx.arc(t+n/2,e+n/2,n/2,0,2*Math.PI,!0),this.ctx.closePath(),this.ctx.fill()},n.prototype.circleStroke=function(t,e,n,r,i,o){this.circle(t,e,n,r),this.ctx.strokeStyle=o.toString(),this.ctx.stroke()},n.prototype.drawShape=function(t,e){this.shape(t),this.setFillStyle(e).fill()},n.prototype.taints=function(e){if(null===e.tainted){this.taintCtx.drawImage(e.image,0,0);try{this.taintCtx.getImageData(0,0,1,1),e.tainted=!1}catch(t){this.taintCtx=document.createElement("canvas").getContext("2d"),e.tainted=!0}}return e.tainted},n.prototype.drawImage=function(t,e,n,r,i,o,s,a,u){this.taints(t)&&!this.options.allowTaint||this.ctx.drawImage(t.image,e,n,r,i,o,s,a,u)},n.prototype.clip=function(t,e,n){this.ctx.save(),t.filter(r).forEach((function(t){this.shape(t).clip()}),this),e.call(n),this.ctx.restore()},n.prototype.shape=function(t){return this.ctx.beginPath(),t.forEach((function(t,e){("rect"===t[0]?this.ctx.rect:this.ctx[0===e?"moveTo":t[0]+"To"]).apply(this.ctx,t.slice(1))}),this),this.ctx.closePath(),this.ctx},n.prototype.font=function(t,e,n,r,i,o){this.setFillStyle(t).font=[e,n,r,i,o].join(" ").split(",")[0]},n.prototype.fontShadow=function(t,e,n,r){this.setVariable("shadowColor",t.toString()).setVariable("shadowOffsetY",e).setVariable("shadowOffsetX",n).setVariable("shadowBlur",r)},n.prototype.clearShadow=function(){this.setVariable("shadowColor","rgba(0,0,0,0)")},n.prototype.setOpacity=function(t){this.ctx.globalAlpha=t},n.prototype.setTransform=function(t){this.ctx.translate(t.origin[0],t.origin[1]),this.ctx.transform.apply(this.ctx,t.matrix),this.ctx.translate(-t.origin[0],-t.origin[1])},n.prototype.setVariable=function(t,e){return this.variables[t]!==e&&(this.variables[t]=this.ctx[t]=e),this},n.prototype.text=function(t,e,n){this.ctx.fillText(t,e,n)},n.prototype.backgroundRepeatShape=function(t,e,n,r,i,o,s,a,u){s=[["line",Math.round(i),Math.round(o)],["line",Math.round(i+s),Math.round(o)],["line",Math.round(i+s),Math.round(a+o)],["line",Math.round(i),Math.round(a+o)]],this.clip([s],(function(){this.renderBackgroundRepeat(t,e,n,r,u[3],u[0])}),this)},n.prototype.renderBackgroundRepeat=function(t,e,n,r,i,o){i=Math.round(r.left+e.left+i),r=Math.round(r.top+e.top+o),this.setFillStyle(this.ctx.createPattern(this.resizeImage(t,n),"repeat")),this.ctx.translate(i,r),this.ctx.fill(),this.ctx.translate(-i,-r)},n.prototype.renderBackgroundGradient=function(t,e){var n;t instanceof o&&(n=this.ctx.createLinearGradient(e.left+e.width*t.x0,e.top+e.height*t.y0,e.left+e.width*t.x1,e.top+e.height*t.y1),t.colorStops.forEach((function(t){n.addColorStop(t.stop,t.color.toString())})),this.rectangle(e.left,e.top,e.width,e.height,n))},n.prototype.resizeImage=function(t,e){var n;t=t.image;return t.width===e.width&&t.height===e.height?t:((n=document.createElement("canvas")).width=e.width,n.height=e.height,n.getContext("2d").drawImage(t,0,0,t.width,t.height,0,0,e.width,e.height),n)},e.exports=n},{"../lineargradientcontainer":12,"../log":13,"../renderer":19}],21:[function(t,e){function n(t,e,n,i){r.call(this,n,i),this.ownStacking=t,this.contexts=[],this.children=[],this.opacity=(this.parent?this.parent.stack.opacity:1)*e}var r=t("./nodecontainer");(n.prototype=Object.create(r.prototype)).getParentStack=function(t){var e=this.parent?this.parent.stack:null;return e?e.ownStacking?e:e.getParentStack(t):t.stack},e.exports=n},{"./nodecontainer":14}],22:[function(t,e){function n(t){this.rangeBounds=this.testRangeBounds(t),this.cors=this.testCORS(),this.svg=this.testSVG()}n.prototype.testRangeBounds=function(t){var e,n,r=!1;return t.createRange&&(e=t.createRange()).getBoundingClientRect&&((n=t.createElement("boundtest")).style.height="123px",n.style.display="block",t.body.appendChild(n),e.selectNode(n),123===e.getBoundingClientRect().height&&(r=!0),t.body.removeChild(n)),r},n.prototype.testCORS=function(){return void 0!==(new Image).crossOrigin},n.prototype.testSVG=function(){var t=new Image,e=document.createElement("canvas"),n=e.getContext("2d");t.src="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg'></svg>";try{n.drawImage(t,0,0),e.toDataURL()}catch(t){return!1}return!0},e.exports=n},{}],23:[function(t,e){function n(t){this.src=t,this.image=null;var e=this;this.promise=this.hasFabric().then((function(){return e.isInline(t)?Promise.resolve(e.inlineFormatting(t)):r(t)})).then((function(t){return new Promise((function(n){window.html2canvas.svg.fabric.loadSVGFromString(t,e.createCanvas.call(e,n))}))}))}var r=t("./xhr"),i=t("./utils").decode64;n.prototype.hasFabric=function(){return window.html2canvas.svg&&window.html2canvas.svg.fabric?Promise.resolve():Promise.reject(new Error("html2canvas.svg.js is not loaded, cannot render svg"))},n.prototype.inlineFormatting=function(t){return/^data:image\/svg\+xml;base64,/.test(t)?this.decode64(this.removeContentType(t)):this.removeContentType(t)},n.prototype.removeContentType=function(t){return t.replace(/^data:image\/svg\+xml(;base64)?,/,"")},n.prototype.isInline=function(t){return/^data:image\/svg\+xml/i.test(t)},n.prototype.createCanvas=function(t){var e=this;return function(n,r){var i=new window.html2canvas.svg.fabric.StaticCanvas("c");e.image=i.lowerCanvasEl,i.setWidth(r.width).setHeight(r.height).add(window.html2canvas.svg.fabric.util.groupSVGElements(n,r)).renderAll(),t(i.lowerCanvasEl)}},n.prototype.decode64=function(t){return"function"==typeof window.atob?window.atob(t):i(t)},e.exports=n},{"./utils":26,"./xhr":28}],24:[function(t,e){function n(t,e){this.src=t,this.image=null;var n=this;this.promise=e?new Promise((function(e,r){n.image=new Image,n.image.onload=e,n.image.onerror=r,n.image.src="data:image/svg+xml,"+(new XMLSerializer).serializeToString(t),!0===n.image.complete&&e(n.image)})):this.hasFabric().then((function(){return new Promise((function(e){window.html2canvas.svg.fabric.parseSVGDocument(t,n.createCanvas.call(n,e))}))}))}t=t("./svgcontainer"),n.prototype=Object.create(t.prototype),e.exports=n},{"./svgcontainer":23}],25:[function(t,e){function n(t,e){i.call(this,t,e)}function r(t,e,n){return 0<t.length?e+n.toUpperCase():void 0}var i=t("./nodecontainer");(n.prototype=Object.create(i.prototype)).applyTextTransform=function(){this.node.data=this.transform(this.parent.css("textTransform"))},n.prototype.transform=function(t){var e=this.node.data;switch(t){case"lowercase":return e.toLowerCase();case"capitalize":return e.replace(/(^|\s|:|-|\(|\))([a-z])/g,r);case"uppercase":return e.toUpperCase();default:return e}},e.exports=n},{"./nodecontainer":14}],26:[function(t,e,n){n.smallImage=function(){return"data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7"},n.bind=function(t,e){return function(){return t.apply(e,arguments)}},n.decode64=function(t){for(var e,n,r,i,o,s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",a=t.length,u="",c=0;c<a;c+=4)r=s.indexOf(t[c])<<2|(e=s.indexOf(t[c+1]))>>4,i=(15&e)<<4|(e=s.indexOf(t[c+2]))>>2,o=(3&e)<<6|(n=s.indexOf(t[c+3])),u+=64===e?String.fromCharCode(r):64===n||-1===n?String.fromCharCode(r,i):String.fromCharCode(r,i,o);return u},n.getBounds=function(t){var e,n;return t.getBoundingClientRect?(e=t.getBoundingClientRect(),n=null==t.offsetWidth?e.width:t.offsetWidth,{top:e.top,bottom:e.bottom||e.top+e.height,right:e.left+n,left:e.left,width:n,height:null==t.offsetHeight?e.height:t.offsetHeight}):{}},n.offsetBounds=function(t){var e=t.offsetParent?n.offsetBounds(t.offsetParent):{top:0,left:0};return{top:t.offsetTop+e.top,bottom:t.offsetTop+t.offsetHeight+e.top,right:t.offsetLeft+e.left+t.offsetWidth,left:t.offsetLeft+e.left,width:t.offsetWidth,height:t.offsetHeight}},n.parseBackgrounds=function(t){function e(){h&&((n='"'===n.substr(0,1)?n.substr(1,n.length-2):n)&&l.push(n),"-"===h.substr(0,1)&&0<(i=h.indexOf("-",1)+1)&&(r=h.substr(0,i),h=h.substr(i)),a.push({prefix:r,method:h.toLowerCase(),value:o,args:l,image:null})),l=[],h=r=n=o=""}var n,r,i,o,s,a=[],u=0,c=0,l=[],h=r=n=o="";return t.split("").forEach((function(t){if(!(0===u&&-1<" \r\n\t".indexOf(t))){switch(t){case'"':s?s===t&&(s=null):s=t;break;case"(":if(!s){if(0===u)return u=1,void(o+=t);c++}break;case")":if(!s&&1===u){if(0===c)return u=0,o+=t,void e();c--}break;case",":if(!s){if(0===u)return void e();if(1===u&&0===c&&!h.match(/^url$/i))return l.push(n),n="",void(o+=t)}}o+=t,0===u?h+=t:n+=t}})),e(),a}},{}],27:[function(t,e){function n(t){r.apply(this,arguments),this.type="linear"===t.args[0]?r.TYPES.LINEAR:r.TYPES.RADIAL}var r=t("./gradientcontainer");n.prototype=Object.create(r.prototype),e.exports=n},{"./gradientcontainer":9}],28:[function(t,e){e.exports=function(t){return new Promise((function(e,n){var r=new XMLHttpRequest;r.open("GET",t),r.onload=function(){200===r.status?e(r.responseText):n(new Error(r.statusText))},r.onerror=function(){n(new Error("Network Error"))},r.send()}))}},{}]},{},[4])(4)})),function(t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).localforage=t()}((function(){return function t(e,n,r){function i(s,a){if(!n[s]){if(!e[s]){var u="function"==typeof require&&require;if(!a&&u)return u(s,!0);if(o)return o(s,!0);throw a=new Error("Cannot find module '"+s+"'"),a.code="MODULE_NOT_FOUND",a}u=n[s]={exports:{}},e[s][0].call(u.exports,(function(t){return i(e[s][1][t]||t)}),u,u.exports,t,e,n,r)}return n[s].exports}for(var o="function"==typeof require&&require,s=0;s<r.length;s++)i(r[s]);return i}({1:[function(t,e,n){(function(t){"use strict";function n(){s=!0;for(var t,e,n=c.length;n;){for(e=c,c=[],t=-1;++t<n;)e[t]();n=c.length}s=!1}var r,i,o,s,a=t.MutationObserver||t.WebKitMutationObserver,u=a?(r=0,a=new a(n),i=t.document.createTextNode(""),a.observe(i,{characterData:!0}),function(){i.data=r=++r%2}):t.setImmediate||void 0===t.MessageChannel?"document"in t&&"onreadystatechange"in t.document.createElement("script")?function(){var e=t.document.createElement("script");e.onreadystatechange=function(){n(),e.onreadystatechange=null,e.parentNode.removeChild(e),e=null},t.document.documentElement.appendChild(e)}:function(){setTimeout(n,0)}:((o=new t.MessageChannel).port1.onmessage=n,function(){o.port2.postMessage(0)}),c=[];e.exports=function(t){1!==c.push(t)||s||u()}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],2:[function(t,e,n){"use strict";function r(){}function i(t){if("function"!=typeof t)throw new TypeError("resolver must be a function");this.state=d,this.queue=[],this.outcome=void 0,t!==r&&u(this,t)}function o(t,e,n){this.promise=t,"function"==typeof e&&(this.onFulfilled=e,this.callFulfilled=this.otherCallFulfilled),"function"==typeof n&&(this.onRejected=n,this.callRejected=this.otherCallRejected)}function s(t,e,n){l((function(){var r;try{r=e(n)}catch(r){return h.reject(t,r)}r===t?h.reject(t,new TypeError("Cannot resolve promise with itself")):h.resolve(t,r)}))}function a(t){var e=t&&t.then;if(t&&("object"==typeof t||"function"==typeof t)&&"function"==typeof e)return function(){e.apply(t,arguments)}}function u(t,e){function n(e){i||(i=!0,h.reject(t,e))}function r(e){i||(i=!0,h.resolve(t,e))}var i=!1,o=c((function(){e(r,n)}));"error"===o.status&&n(o.value)}function c(t,e){var n={};try{n.value=t(e),n.status="success"}catch(t){n.status="error",n.value=t}return n}var l=t(1),h={},f=["REJECTED"],p=["FULFILLED"],d=["PENDING"];(e.exports=i).prototype.catch=function(t){return this.then(null,t)},i.prototype.then=function(t,e){var n;return"function"!=typeof t&&this.state===p||"function"!=typeof e&&this.state===f?this:(n=new this.constructor(r),this.state!==d?s(n,this.state===p?t:e,this.outcome):this.queue.push(new o(n,t,e)),n)},o.prototype.callFulfilled=function(t){h.resolve(this.promise,t)},o.prototype.otherCallFulfilled=function(t){s(this.promise,this.onFulfilled,t)},o.prototype.callRejected=function(t){h.reject(this.promise,t)},o.prototype.otherCallRejected=function(t){s(this.promise,this.onRejected,t)},h.resolve=function(t,e){var n=c(a,e);if("error"===n.status)return h.reject(t,n.value);if(n=n.value,n)u(t,n);else{t.state=p,t.outcome=e;for(var r=-1,i=t.queue.length;++r<i;)t.queue[r].callFulfilled(e)}return t},h.reject=function(t,e){t.state=f,t.outcome=e;for(var n=-1,r=t.queue.length;++n<r;)t.queue[n].callRejected(e);return t},i.resolve=function(t){return t instanceof this?t:h.resolve(new this(r),t)},i.reject=function(t){var e=new this(r);return h.reject(e,t)},i.all=function(t){function e(t,e){n.resolve(t).then((function(t){s[e]=t,++a!==i||o||(o=!0,h.resolve(c,s))}),(function(t){o||(o=!0,h.reject(c,t))}))}var n=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var i=t.length,o=!1;if(!i)return this.resolve([]);for(var s=new Array(i),a=0,u=-1,c=new this(r);++u<i;)e(t[u],u);return c},i.race=function(t){var e=this;if("[object Array]"!==Object.prototype.toString.call(t))return this.reject(new TypeError("must be an array"));var n=t.length,i=!1;if(!n)return this.resolve([]);for(var o,s=-1,a=new this(r);++s<n;)o=t[s],e.resolve(o).then((function(t){i||(i=!0,h.resolve(a,t))}),(function(t){i||(i=!0,h.reject(a,t))}));return a}},{1:1}],3:[function(t,e,n){(function(e){"use strict";"function"!=typeof e.Promise&&(e.Promise=t(2))}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{2:2}],4:[function(t,e,n){"use strict";function r(e,n){e=e||[],n=n||{};try{return new Blob(e,n)}catch(t){if("TypeError"!==t.name)throw t;for(var r=new("undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder?MozBlobBuilder:WebKitBlobBuilder),i=0;i<e.length;i+=1)r.append(e[i]);return r.getBlob(n.type)}}function i(t,e){e&&t.then((function(t){e(null,t)}),(function(t){e(t)}))}function o(t,e,n){"function"==typeof e&&t.then(e),"function"==typeof n&&t.catch(n)}function s(t){return"string"!=typeof t?String(t):t}function a(){if(arguments.length&&"function"==typeof arguments[arguments.length-1])return arguments[arguments.length-1]}function u(t){return"boolean"==typeof D?P.resolve(D):(e=t,new P((function(t){var n=e.transaction(N,L),i=r([""]);n.objectStore(N).put(i,"key"),n.onabort=function(e){e.preventDefault(),e.stopPropagation(),t(!1)},n.oncomplete=function(){var e=navigator.userAgent.match(/Chrome\/(\d+)/),n=navigator.userAgent.match(/Edge\//);t(n||!e||43<=parseInt(e[1],10))}})).catch((function(){return!1})).then((function(t){return D=t})));var e}function c(t){t=R[t.name];var e={};e.promise=new P((function(t,n){e.resolve=t,e.reject=n})),t.deferredOperations.push(e),t.dbReady?t.dbReady=t.dbReady.then((function(){return e.promise})):t.dbReady=e.promise}function l(t){t=R[t.name].deferredOperations.pop(),t&&(t.resolve(),t.promise)}function h(t,e){if(t=R[t.name].deferredOperations.pop(),t)return t.reject(e),t.promise}function f(t,e){return new P((function(n,r){if(R[t.name]=R[t.name]||w(),t.db){if(!e)return n(t.db);c(t),t.db.close()}var i=[t.name],o=(e&&i.push(t.version),j.open.apply(j,i));e&&(o.onupgradeneeded=function(e){var n=o.result;try{n.createObjectStore(t.storeName),e.oldVersion<=1&&n.createObjectStore(N)}catch(n){if("ConstraintError"!==n.name)throw n}}),o.onerror=function(t){t.preventDefault(),r(o.error)},o.onsuccess=function(){n(o.result),l(t)}}))}function p(t){return f(t,!1)}function d(t){return f(t,!0)}function g(t){var e,n,r;return!t.db||(e=!t.db.objectStoreNames.contains(t.storeName),r=t.version<t.db.version,n=t.version>t.db.version,r&&(t.version,t.version=t.db.version),(n||e)&&(e&&(r=t.db.version+1)>t.version&&(t.version=r),1))}function m(t){return r([function(t){for(var e=t.length,n=new ArrayBuffer(e),r=new Uint8Array(n),i=0;i<e;i++)r[i]=t.charCodeAt(i);return n}(atob(t.data))],{type:t.type})}function v(t){return t&&t.__local_forage_encoded_blob}function y(t){var e=this,n=e._initReady().then((function(){var t=R[e._dbInfo.name];if(t&&t.dbReady)return t.dbReady}));return o(n,t,t),n}function b(t,e,n,r){void 0===r&&(r=1);try{var i=t.db.transaction(t.storeName,e);n(null,i)}catch(i){if(0<r&&(!t.db||"InvalidStateError"===i.name||"NotFoundError"===i.name))return P.resolve().then((function(){if(!t.db||"NotFoundError"===i.name&&!t.db.objectStoreNames.contains(t.storeName)&&t.version<=t.db.version)return t.db&&(t.version=t.db.version+1),d(t)})).then((function(){return function(t){c(t);for(var e=R[t.name],n=e.forages,r=0;r<n.length;r++){var i=n[r];i._dbInfo.db&&(i._dbInfo.db.close(),i._dbInfo.db=null)}return t.db=null,p(t).then((function(e){return t.db=e,g(t)?d(t):e})).then((function(r){t.db=e.db=r;for(var i=0;i<n.length;i++)n[i]._dbInfo.db=r})).catch((function(e){throw h(t,e),e}))}(t).then((function(){b(t,e,n,r-1)}))})).catch(n);n(i)}}function w(){return{forages:[],db:null,dbReady:null,deferredOperations:[]}}function _(t){for(var e,n,r,i,o=.75*t.length,s=t.length,a=0,u=(o=("="===t[t.length-1]&&(o--,"="===t[t.length-2])&&o--,new ArrayBuffer(o)),new Uint8Array(o)),c=0;c<s;c+=4)e=F.indexOf(t[c]),n=F.indexOf(t[c+1]),r=F.indexOf(t[c+2]),i=F.indexOf(t[c+3]),u[a++]=e<<2|n>>4,u[a++]=(15&n)<<4|r>>2,u[a++]=(3&r)<<6|63&i;return o}function x(t){for(var e=new Uint8Array(t),n="",r=0;r<e.length;r+=3)n=(n=(n=(n+=F[e[r]>>2])+F[(3&e[r])<<4|e[r+1]>>4])+F[(15&e[r+1])<<2|e[r+2]>>6])+F[63&e[r+2]];return e.length%3==2?n=n.substring(0,n.length-1)+"=":e.length%3==1&&(n=n.substring(0,n.length-2)+"=="),n}function C(t,e,n,r){t.executeSql("CREATE TABLE IF NOT EXISTS "+e.storeName+" (id INTEGER PRIMARY KEY, key unique, value)",[],n,r)}function k(t,e,n,r,i,o){t.executeSql(n,r,i,(function(t,s){s.code===s.SYNTAX_ERR?t.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name = ?",[name],(function(t,a){a.rows.length?o(t,s):C(t,e,(function(){t.executeSql(n,r,i,o)}),o)}),o):o(t,s)}),o)}function S(t,e,n,r){var o=this,a=(t=s(t),new P((function(i,s){o.ready().then((function(){var a=e=void 0===e?null:e,u=o._dbInfo;u.serializer.serialize(e,(function(e,c){c?s(c):u.db.transaction((function(n){k(n,u,"INSERT OR REPLACE INTO "+u.storeName+" (key, value) VALUES (?, ?)",[t,e],(function(){i(a)}),(function(t,e){s(e)}))}),(function(e){e.code===e.QUOTA_ERR&&(0<r?i(S.apply(o,[t,a,n,r-1])):s(e))}))}))})).catch(s)})));return i(a,n),a}function O(t,e){var n=t.name+"/";return t.storeName!==e.storeName&&(n+=t.storeName+"/"),n}function E(){return!function(){var t="_localforage_support_test";try{return localStorage.setItem(t,!0),localStorage.removeItem(t),0}catch(t){return 1}}()||0<localStorage.length}function T(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];if(n)for(var r in n)n.hasOwnProperty(r)&&(X(n[r])?t[r]=n[r].slice():t[r]=n[r])}return t}function A(t,e){for(var n,r,i=t.length,o=0;o<i;){if((n=t[o])===(r=e)||"number"==typeof n&&"number"==typeof r&&isNaN(n)&&isNaN(r))return 1;o++}}var I="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},j=function(){try{return"undefined"!=typeof indexedDB?indexedDB:"undefined"!=typeof webkitIndexedDB?webkitIndexedDB:"undefined"!=typeof mozIndexedDB?mozIndexedDB:"undefined"!=typeof OIndexedDB?OIndexedDB:"undefined"!=typeof msIndexedDB?msIndexedDB:void 0}catch(t){}}(),P=("undefined"==typeof Promise&&t(3),Promise),N="local-forage-detect-blob-support",D=void 0,R={},M=Object.prototype.toString,W="readonly",L="readwrite",F=(t={_driver:"asyncStorage",_initStorage:function(t){function e(){return P.resolve()}var n=this,r={db:null};if(t)for(var i in t)r[i]=t[i];var o=R[r.name];o||(o=w(),R[r.name]=o),o.forages.push(n),n._initReady||(n._initReady=n.ready,n.ready=y);for(var s=[],a=0;a<o.forages.length;a++){var u=o.forages[a];u!==n&&s.push(u._initReady().catch(e))}var c=o.forages.slice(0);return P.all(s).then((function(){return r.db=o.db,p(r)})).then((function(t){return r.db=t,g(r,n._defaultConfig.version)?d(r):t})).then((function(t){r.db=o.db=t,n._dbInfo=r;for(var e=0;e<c.length;e++){var i=c[e];i!==n&&(i._dbInfo.db=r.db,i._dbInfo.version=r.version)}}))},_support:function(){try{var t,e;return!!j&&(t="undefined"!=typeof openDatabase&&/(Safari|iPhone|iPad|iPod)/.test(navigator.userAgent)&&!/Chrome/.test(navigator.userAgent)&&!/BlackBerry/.test(navigator.platform),e="function"==typeof fetch&&-1!==fetch.toString().indexOf("[native code"),(!t||e)&&"undefined"!=typeof indexedDB&&"undefined"!=typeof IDBKeyRange)}catch(t){return!1}}(),iterate:function(t,e){var n=this,r=new P((function(e,r){n.ready().then((function(){b(n._dbInfo,W,(function(i,o){if(i)return r(i);try{var s=o.objectStore(n._dbInfo.storeName).openCursor(),a=1;s.onsuccess=function(){var n,r=s.result;r?(v(n=r.value)&&(n=m(n)),void 0!==(n=t(n,r.key,a++))?e(n):r.continue()):e()},s.onerror=function(){r(s.error)}}catch(i){r(i)}}))})).catch(r)}));return i(r,e),r},getItem:function(t,e){var n=this,r=(t=s(t),new P((function(e,r){n.ready().then((function(){b(n._dbInfo,W,(function(i,o){if(i)return r(i);try{var s=o.objectStore(n._dbInfo.storeName).get(t);s.onsuccess=function(){var t=s.result;v(t=void 0===t?null:t)&&(t=m(t)),e(t)},s.onerror=function(){r(s.error)}}catch(i){r(i)}}))})).catch(r)})));return i(r,e),r},setItem:function(t,e,n){var r=this,o=(t=s(t),new P((function(n,i){var o;r.ready().then((function(){return o=r._dbInfo,"[object Blob]"===M.call(e)?u(o.db).then((function(t){return t?e:(n=e,new P((function(t,e){var r=new FileReader;r.onerror=e,r.onloadend=function(e){e=btoa(e.target.result||""),t({__local_forage_encoded_blob:!0,data:e,type:n.type})},r.readAsBinaryString(n)})));var n})):e})).then((function(e){b(r._dbInfo,L,(function(o,s){if(o)return i(o);try{var a=s.objectStore(r._dbInfo.storeName),u=(null===e&&(e=void 0),a.put(e,t));s.oncomplete=function(){n(e=void 0===e?null:e)},s.onabort=s.onerror=function(){var t=u.error||u.transaction.error;i(t)}}catch(o){i(o)}}))})).catch(i)})));return i(o,n),o},removeItem:function(t,e){var n=this,r=(t=s(t),new P((function(e,r){n.ready().then((function(){b(n._dbInfo,L,(function(i,o){if(i)return r(i);try{var s=o.objectStore(n._dbInfo.storeName).delete(t);o.oncomplete=function(){e()},o.onerror=function(){r(s.error)},o.onabort=function(){var t=s.error||s.transaction.error;r(t)}}catch(i){r(i)}}))})).catch(r)})));return i(r,e),r},clear:function(t){var e=this,n=new P((function(t,n){e.ready().then((function(){b(e._dbInfo,L,(function(r,i){if(r)return n(r);try{var o=i.objectStore(e._dbInfo.storeName).clear();i.oncomplete=function(){t()},i.onabort=i.onerror=function(){var t=o.error||o.transaction.error;n(t)}}catch(r){n(r)}}))})).catch(n)}));return i(n,t),n},length:function(t){var e=this,n=new P((function(t,n){e.ready().then((function(){b(e._dbInfo,W,(function(r,i){if(r)return n(r);try{var o=i.objectStore(e._dbInfo.storeName).count();o.onsuccess=function(){t(o.result)},o.onerror=function(){n(o.error)}}catch(r){n(r)}}))})).catch(n)}));return i(n,t),n},key:function(t,e){var n=this,r=new P((function(e,r){t<0?e(null):n.ready().then((function(){b(n._dbInfo,W,(function(i,o){if(i)return r(i);try{var s=o.objectStore(n._dbInfo.storeName),a=!1,u=s.openCursor();u.onsuccess=function(){var n=u.result;n?0===t||a?e(n.key):(a=!0,n.advance(t)):e(null)},u.onerror=function(){r(u.error)}}catch(i){r(i)}}))})).catch(r)}));return i(r,e),r},keys:function(t){var e=this,n=new P((function(t,n){e.ready().then((function(){b(e._dbInfo,W,(function(r,i){if(r)return n(r);try{var o=i.objectStore(e._dbInfo.storeName).openCursor(),s=[];o.onsuccess=function(){var e=o.result;e?(s.push(e.key),e.continue()):t(s)},o.onerror=function(){n(o.error)}}catch(r){n(r)}}))})).catch(n)}));return i(n,t),n},dropInstance:function(t,e){e=a.apply(this,arguments);var n=this.config();return(t="function"!=typeof t&&t||{}).name||(t.name=t.name||n.name,t.storeName=t.storeName||n.storeName),i(n=t.name?(n=t.name===n.name&&this._dbInfo.db?P.resolve(this._dbInfo.db):p(t).then((function(e){var n=R[t.name],r=n.forages;n.db=e;for(var i=0;i<r.length;i++)r[i]._dbInfo.db=e;return e})),t.storeName?n.then((function(e){if(e.objectStoreNames.contains(t.storeName)){var n=e.version+1,r=(c(t),R[t.name]),i=r.forages;e.close();for(var o=0;o<i.length;o++){var s=i[o];s._dbInfo.db=null,s._dbInfo.version=n}return new P((function(e,r){var i=j.open(t.name,n);i.onerror=function(t){i.result.close(),r(t)},i.onupgradeneeded=function(){i.result.deleteObjectStore(t.storeName)},i.onsuccess=function(){var t=i.result;t.close(),e(t)}})).then((function(t){r.db=t;for(var e=0;e<i.length;e++){var n=i[e];n._dbInfo.db=t,l(n._dbInfo)}})).catch((function(e){throw(h(t,e)||P.resolve()).catch((function(){})),e}))}})):n.then((function(e){c(t);var n=R[t.name],r=n.forages;e.close();for(var i=0;i<r.length;i++)r[i]._dbInfo.db=null;return new P((function(e,n){var r=j.deleteDatabase(t.name);r.onerror=r.onblocked=function(t){var e=r.result;e&&e.close(),n(t)},r.onsuccess=function(){var t=r.result;t&&t.close(),e(t)}})).then((function(t){n.db=t;for(var e=0;e<r.length;e++)l(r[e]._dbInfo)})).catch((function(e){throw(h(t,e)||P.resolve()).catch((function(){})),e}))}))):P.reject("Invalid arguments"),e),n}},"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/"),H=/^~~local_forage_type~([^~]+)~/,B="__lfsc__:",q=B.length,z=q+4,U=Object.prototype.toString,G={serialize:function(t,e){var n="";if(t&&(n=U.call(t)),t&&("[object ArrayBuffer]"===n||t.buffer&&"[object ArrayBuffer]"===U.call(t.buffer))){var r,i=B;t instanceof ArrayBuffer?(r=t,i+="arbf"):(r=t.buffer,"[object Int8Array]"===n?i+="si08":"[object Uint8Array]"===n?i+="ui08":"[object Uint8ClampedArray]"===n?i+="uic8":"[object Int16Array]"===n?i+="si16":"[object Uint16Array]"===n?i+="ur16":"[object Int32Array]"===n?i+="si32":"[object Uint32Array]"===n?i+="ui32":"[object Float32Array]"===n?i+="fl32":"[object Float64Array]"===n?i+="fl64":e(new Error("Failed to get type for BinaryArray"))),e(i+x(r))}else if("[object Blob]"===n)i=new FileReader,i.onload=function(){var n="~~local_forage_type~"+t.type+"~"+x(this.result);e(B+"blob"+n)},i.readAsArrayBuffer(t);else try{e(JSON.stringify(t))}catch(n){e(null,n)}},deserialize:function(t){if(t.substring(0,q)!==B)return JSON.parse(t);var e,n=t.substring(z),i=t.substring(q,z),o=("blob"===i&&H.test(n)&&(e=(t=n.match(H))[1],n=n.substring(t[0].length)),_(n));switch(i){case"arbf":return o;case"blob":return r([o],{type:e});case"si08":return new Int8Array(o);case"ui08":return new Uint8Array(o);case"uic8":return new Uint8ClampedArray(o);case"si16":return new Int16Array(o);case"ur16":return new Uint16Array(o);case"si32":return new Int32Array(o);case"ui32":return new Uint32Array(o);case"fl32":return new Float32Array(o);case"fl64":return new Float64Array(o);default:throw new Error("Unkown type: "+i)}},stringToBuffer:_,bufferToString:x},V={_driver:"webSQLStorage",_initStorage:function(t){var e=this,n={db:null};if(t)for(var r in t)n[r]="string"!=typeof t[r]?t[r].toString():t[r];var i=new P((function(t,r){try{n.db=openDatabase(n.name,String(n.version),n.description,n.size)}catch(t){return r(t)}n.db.transaction((function(i){C(i,n,(function(){e._dbInfo=n,t()}),(function(t,e){r(e)}))}),r)}));return n.serializer=G,i},_support:"function"==typeof openDatabase,iterate:function(t,e){var n=this,r=new P((function(e,r){n.ready().then((function(){var i=n._dbInfo;i.db.transaction((function(n){k(n,i,"SELECT * FROM "+i.storeName,[],(function(n,r){for(var o=r.rows,s=o.length,a=0;a<s;a++){var u=o.item(a),c=(c=u.value)&&i.serializer.deserialize(c);if(void 0!==(c=t(c,u.key,a+1)))return void e(c)}e()}),(function(t,e){r(e)}))}))})).catch(r)}));return i(r,e),r},getItem:function(t,e){var n=this,r=(t=s(t),new P((function(e,r){n.ready().then((function(){var i=n._dbInfo;i.db.transaction((function(n){k(n,i,"SELECT * FROM "+i.storeName+" WHERE key = ? LIMIT 1",[t],(function(t,n){n=(n=n.rows.length?n.rows.item(0).value:null)&&i.serializer.deserialize(n),e(n)}),(function(t,e){r(e)}))}))})).catch(r)})));return i(r,e),r},setItem:function(t,e,n){return S.apply(this,[t,e,n,1])},removeItem:function(t,e){var n=this,r=(t=s(t),new P((function(e,r){n.ready().then((function(){var i=n._dbInfo;i.db.transaction((function(n){k(n,i,"DELETE FROM "+i.storeName+" WHERE key = ?",[t],(function(){e()}),(function(t,e){r(e)}))}))})).catch(r)})));return i(r,e),r},clear:function(t){var e=this,n=new P((function(t,n){e.ready().then((function(){var r=e._dbInfo;r.db.transaction((function(e){k(e,r,"DELETE FROM "+r.storeName,[],(function(){t()}),(function(t,e){n(e)}))}))})).catch(n)}));return i(n,t),n},length:function(t){var e=this,n=new P((function(t,n){e.ready().then((function(){var r=e._dbInfo;r.db.transaction((function(e){k(e,r,"SELECT COUNT(key) as c FROM "+r.storeName,[],(function(e,n){n=n.rows.item(0).c,t(n)}),(function(t,e){n(e)}))}))})).catch(n)}));return i(n,t),n},key:function(t,e){var n=this,r=new P((function(e,r){n.ready().then((function(){var i=n._dbInfo;i.db.transaction((function(n){k(n,i,"SELECT key FROM "+i.storeName+" WHERE id = ? LIMIT 1",[t+1],(function(t,n){n=n.rows.length?n.rows.item(0).key:null,e(n)}),(function(t,e){r(e)}))}))})).catch(r)}));return i(r,e),r},keys:function(t){var e=this,n=new P((function(t,n){e.ready().then((function(){var r=e._dbInfo;r.db.transaction((function(e){k(e,r,"SELECT key FROM "+r.storeName,[],(function(e,n){for(var r=[],i=0;i<n.rows.length;i++)r.push(n.rows.item(i).key);t(r)}),(function(t,e){n(e)}))}))})).catch(n)}));return i(n,t),n},dropInstance:function(t,e){e=a.apply(this,arguments);var n=this.config(),r=((t="function"!=typeof t&&t||{}).name||(t.name=t.name||n.name,t.storeName=t.storeName||n.storeName),this),o=t.name?new P((function(e){var i,o=t.name===n.name?r._dbInfo.db:openDatabase(t.name,"","",0);e(t.storeName?{db:o,storeNames:[t.storeName]}:(i=o,new P((function(t,e){i.transaction((function(n){n.executeSql("SELECT name FROM sqlite_master WHERE type='table' AND name <> '__WebKitDatabaseInfoTable__'",[],(function(e,n){for(var r=[],o=0;o<n.rows.length;o++)r.push(n.rows.item(o).name);t({db:i,storeNames:r})}),(function(t,n){e(n)}))}),(function(t){e(t)}))}))))})).then((function(t){return new P((function(e,n){t.db.transaction((function(r){for(var i=[],o=0,s=t.storeNames.length;o<s;o++)i.push(function(t){return new P((function(e,n){r.executeSql("DROP TABLE IF EXISTS "+t,[],(function(){e()}),(function(t,e){n(e)}))}))}(t.storeNames[o]));P.all(i).then((function(){e()})).catch((function(t){n(t)}))}),(function(t){n(t)}))}))})):P.reject("Invalid arguments");return i(o,e),o}},$={_driver:"localStorageWrapper",_initStorage:function(t){var e={};if(t)for(var n in t)e[n]=t[n];return e.keyPrefix=O(t,this._defaultConfig),E()?((this._dbInfo=e).serializer=G,P.resolve()):P.reject()},_support:function(){try{return"undefined"!=typeof localStorage&&"setItem"in localStorage&&!!localStorage.setItem}catch(t){return!1}}(),iterate:function(t,e){var n=this,r=n.ready().then((function(){for(var e=n._dbInfo,r=e.keyPrefix,i=r.length,o=localStorage.length,s=1,a=0;a<o;a++){var u=localStorage.key(a);if(0===u.indexOf(r)){var c=(c=localStorage.getItem(u))&&e.serializer.deserialize(c);if(void 0!==(c=t(c,u.substring(i),s++)))return c}}}));return i(r,e),r},getItem:function(t,e){var n=this,r=(t=s(t),n.ready().then((function(){var e=n._dbInfo,r=localStorage.getItem(e.keyPrefix+t);return r&&e.serializer.deserialize(r)})));return i(r,e),r},setItem:function(t,e,n){var r=this,o=(t=s(t),r.ready().then((function(){var n=e=void 0===e?null:e;return new P((function(i,o){var s=r._dbInfo;s.serializer.serialize(e,(function(e,r){if(r)o(r);else try{localStorage.setItem(s.keyPrefix+t,e),i(n)}catch(e){"QuotaExceededError"!==e.name&&"NS_ERROR_DOM_QUOTA_REACHED"!==e.name||o(e),o(e)}}))}))})));return i(o,n),o},removeItem:function(t,e){var n=this,r=(t=s(t),n.ready().then((function(){var e=n._dbInfo;localStorage.removeItem(e.keyPrefix+t)})));return i(r,e),r},clear:function(t){var e=this,n=e.ready().then((function(){for(var t=e._dbInfo.keyPrefix,n=localStorage.length-1;0<=n;n--){var r=localStorage.key(n);0===r.indexOf(t)&&localStorage.removeItem(r)}}));return i(n,t),n},length:function(t){var e=this.keys().then((function(t){return t.length}));return i(e,t),e},key:function(t,e){var n=this,r=n.ready().then((function(){var e,r=n._dbInfo;try{e=localStorage.key(t)}catch(r){e=null}return e&&e.substring(r.keyPrefix.length)}));return i(r,e),r},keys:function(t){var e=this,n=e.ready().then((function(){for(var t=e._dbInfo,n=localStorage.length,r=[],i=0;i<n;i++){var o=localStorage.key(i);0===o.indexOf(t.keyPrefix)&&r.push(o.substring(t.keyPrefix.length))}return r}));return i(n,t),n},dropInstance:function(t,e){e=a.apply(this,arguments),(t="function"!=typeof t&&t||{}).name||(r=this.config(),t.name=t.name||r.name,t.storeName=t.storeName||r.storeName);var n=this,r=t.name?new P((function(e){e(t.storeName?O(t,n._defaultConfig):t.name+"/")})).then((function(t){for(var e=localStorage.length-1;0<=e;e--){var n=localStorage.key(e);0===n.indexOf(t)&&localStorage.removeItem(n)}})):P.reject("Invalid arguments");return i(r,e),r}},X=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},Y={},K={},Q={INDEXEDDB:t,WEBSQL:V,LOCALSTORAGE:$},J=(t=[Q.INDEXEDDB._driver,Q.WEBSQL._driver,Q.LOCALSTORAGE._driver],["dropInstance"]),Z=["clear","getItem","iterate","key","keys","length","removeItem","setItem"].concat(J),tt={description:"",driver:t.slice(),name:"localforage",size:4980736,storeName:"keyvaluepairs",version:1};function et(t){var e,n,r;if(!(this instanceof et))throw new TypeError("Cannot call a class as a function");for(e in Q)Q.hasOwnProperty(e)&&(r=(n=Q[e])._driver,this[e]=r,Y[r]||this.defineDriver(n));this._defaultConfig=T({},tt),this._config=T({},this._defaultConfig,t),this._driverSet=null,this._initDriver=null,this._ready=!1,this._dbInfo=null,this._wrapLibraryMethodsWithReady(),this.setDriver(this._config.driver).catch((function(){}))}et.prototype.config=function(t){if("object"!==(void 0===t?"undefined":I(t)))return"string"==typeof t?this._config[t]:this._config;if(this._ready)return new Error("Can't call config() after localforage has been used.");for(var e in t){if("storeName"===e&&(t[e]=t[e].replace(/\W/g,"_")),"version"===e&&"number"!=typeof t[e])return new Error("Database version must be a number.");this._config[e]=t[e]}return!("driver"in t&&t.driver)||this.setDriver(this._config.driver)},et.prototype.defineDriver=function(t,e,n){var r=new P((function(e,n){try{var r=t._driver,o=new Error("Custom driver not compliant; see https://mozilla.github.io/localForage/#definedriver");if(t._driver){for(var s=Z.concat("_initStorage"),a=0,u=s.length;a<u;a++){var c=s[a];if((!A(J,c)||t[c])&&"function"!=typeof t[c])return void n(o)}for(var l=0,h=J.length;l<h;l++){var f=J[l];t[f]||(t[f]=function(t){return function(){var e=new Error("Method "+t+" is not implemented by the current driver");e=P.reject(e);return i(e,arguments[arguments.length-1]),e}}(f))}var p=function(n){Y[r]=t,K[r]=n,e()};"_support"in t?t._support&&"function"==typeof t._support?t._support().then(p,n):p(!!t._support):p(!0)}else n(o)}catch(o){n(o)}}));return o(r,e,n),r},et.prototype.driver=function(){return this._driver||null},et.prototype.getDriver=function(t,e,n){return t=Y[t]?P.resolve(Y[t]):P.reject(new Error("Driver not found.")),o(t,e,n),t},et.prototype.getSerializer=function(t){var e=P.resolve(G);return o(e,t),e},et.prototype.ready=function(t){var e=this,n=e._driverSet.then((function(){return null===e._ready&&(e._ready=e._initDriver()),e._ready}));return o(n,t,t),n},et.prototype.setDriver=function(t,e,n){function r(){a._config.driver=a.driver()}function i(t){return a._extend(t),r(),a._ready=a._initStorage(a._config),a._ready}function s(t){return function(){var e=0;return function n(){for(;e<t.length;){var o=t[e];return e++,a._dbInfo=null,a._ready=null,a.getDriver(o).then(i).catch(n)}r();var s=new Error("No available storage method found.");return a._driverSet=P.reject(s),a._driverSet}()}}var a=this,u=(X(t)||(t=[t]),this._getSupportedDrivers(t));t=null!==this._driverSet?this._driverSet.catch((function(){return P.resolve()})):P.resolve();return this._driverSet=t.then((function(){var t=u[0];return a._dbInfo=null,a._ready=null,a.getDriver(t).then((function(t){a._driver=t._driver,r(),a._wrapLibraryMethodsWithReady(),a._initDriver=s(u)}))})).catch((function(){r();var t=new Error("No available storage method found.");return a._driverSet=P.reject(t),a._driverSet})),o(this._driverSet,e,n),this._driverSet},et.prototype.supports=function(t){return!!K[t]},et.prototype._extend=function(t){T(this,t)},et.prototype._getSupportedDrivers=function(t){for(var e=[],n=0,r=t.length;n<r;n++){var i=t[n];this.supports(i)&&e.push(i)}return e},et.prototype._wrapLibraryMethodsWithReady=function(){for(var t=0,e=Z.length;t<e;t++)!function(t,e){t[e]=function(){var n=arguments;return t.ready().then((function(){return t[e].apply(t,n)}))}}(this,Z[t])},et.prototype.createInstance=function(t){return new et(t)},V=new et,e.exports=V},{3:3}]},{},[4])(4)})),function(){function t(t,e){return t.set(e[0],e[1]),t}function e(t,e){return t.add(e),t}function n(t,e,n){switch(n.length){case 0:return t.call(e);case 1:return t.call(e,n[0]);case 2:return t.call(e,n[0],n[1]);case 3:return t.call(e,n[0],n[1],n[2])}return t.apply(e,n)}function r(t,e,n,r){for(var i=-1,o=null==t?0:t.length;++i<o;){var s=t[i];e(r,s,n(s),t)}return r}function i(t,e){for(var n=-1,r=null==t?0:t.length;++n<r&&!1!==e(t[n],n,t););return t}function o(t,e){for(var n=null==t?0:t.length;n--&&!1!==e(t[n],n,t););return t}function s(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(!e(t[n],n,t))return!1;return!0}function a(t,e){for(var n=-1,r=null==t?0:t.length,i=0,o=[];++n<r;){var s=t[n];e(s,n,t)&&(o[i++]=s)}return o}function u(t,e){return!(null==t||!t.length)&&-1<v(t,e,0)}function c(t,e,n){for(var r=-1,i=null==t?0:t.length;++r<i;)if(n(e,t[r]))return!0;return!1}function l(t,e){for(var n=-1,r=null==t?0:t.length,i=Array(r);++n<r;)i[n]=e(t[n],n,t);return i}function h(t,e){for(var n=-1,r=e.length,i=t.length;++n<r;)t[i+n]=e[n];return t}function f(t,e,n,r){var i=-1,o=null==t?0:t.length;for(r&&o&&(n=t[++i]);++i<o;)n=e(n,t[i],i,t);return n}function p(t,e,n,r){var i=null==t?0:t.length;for(r&&i&&(n=t[--i]);i--;)n=e(n,t[i],i,t);return n}function d(t,e){for(var n=-1,r=null==t?0:t.length;++n<r;)if(e(t[n],n,t))return!0;return!1}function g(t,e,n){var r;return n(t,(function(t,n,i){if(e(t,n,i))return r=n,!1})),r}function m(t,e,n,r){for(var i=t.length,o=n+(r?1:-1);r?o--:++o<i;)if(e(t[o],o,t))return o;return-1}function v(t,e,n){if(e!=e)return m(t,b,n);for(var r=t,i=e,o=n-1,s=r.length;++o<s;)if(r[o]===i)return o;return-1}function y(t,e,n,r){for(var i=n-1,o=t.length;++i<o;)if(r(t[i],e))return i;return-1}function b(t){return t!=t}function w(t,e){var n=null==t?0:t.length;return n?k(t,e)/n:U}function _(t){return function(e){return null==e?F:e[t]}}function x(t){return function(e){return null==t?F:t[e]}}function C(t,e,n,r,i){return i(t,(function(t,i,o){n=r?(r=!1,t):e(n,t,i,o)})),n}function k(t,e){for(var n,r=-1,i=t.length;++r<i;){var o=e(t[r]);o!==F&&(n=n===F?o:n+o)}return n}function S(t,e){for(var n=-1,r=Array(t);++n<t;)r[n]=e(n);return r}function O(t){return function(e){return t(e)}}function E(t,e){return l(e,(function(e){return t[e]}))}function T(t,e){return t.has(e)}function A(t,e){for(var n=-1,r=t.length;++n<r&&-1<v(e,t[n],0););return n}function I(t,e){for(var n=t.length;n--&&-1<v(e,t[n],0););return n}function j(t){return"\\"+Ae[t]}function P(t){return Ce.test(t)}function N(t){var e=-1,n=Array(t.size);return t.forEach((function(t,r){n[++e]=[r,t]})),n}function D(t,e){return function(n){return t(e(n))}}function R(t,e){for(var n=-1,r=t.length,i=0,o=[];++n<r;){var s=t[n];s!==e&&s!==q||(t[n]=q,o[i++]=n)}return o}function M(t){var e=-1,n=Array(t.size);return t.forEach((function(t){n[++e]=t})),n}function W(t){return(P(t)?function(t){for(var e=_e.lastIndex=0;_e.test(t);)++e;return e}:Be)(t)}function L(t){return P(t)?t.match(_e)||[]:t.split("")}var F,H="Expected a function",B="__lodash_hash_undefined__",q="__lodash_placeholder__",z=9007199254740991,U=NaN,G=4294967295,V=[["ary",128],["bind",1],["bindKey",2],["curry",8],["curryRight",16],["flip",512],["partial",32],["partialRight",64],["rearg",256]],$="[object Arguments]",X="[object Array]",Y="[object Boolean]",K="[object Date]",Q="[object Error]",J="[object Function]",Z="[object GeneratorFunction]",tt="[object Map]",et="[object Number]",nt="[object Object]",rt="[object Promise]",it="[object RegExp]",ot="[object Set]",st="[object String]",at="[object Symbol]",ut="[object WeakMap]",ct="[object ArrayBuffer]",lt="[object DataView]",ht="[object Float32Array]",ft="[object Float64Array]",pt="[object Int8Array]",dt="[object Int16Array]",gt="[object Int32Array]",mt="[object Uint8Array]",vt="[object Uint8ClampedArray]",yt="[object Uint16Array]",bt="[object Uint32Array]",wt=/\b__p \+= '';/g,_t=/\b(__p \+=) '' \+/g,xt=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Ct=/&(?:amp|lt|gt|quot|#39);/g,kt=/[&<>"']/g,St=RegExp(Ct.source),Ot=RegExp(kt.source),Et=/<%-([\s\S]+?)%>/g,Tt=/<%([\s\S]+?)%>/g,At=/<%=([\s\S]+?)%>/g,It=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,jt=/^\w*$/,Pt=/^\./,Nt=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,Dt=/[\\^$.*+?()[\]{}|]/g,Rt=RegExp(Dt.source),Mt=/^\s+|\s+$/g,Wt=/^\s+/,Lt=/\s+$/,Ft=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Ht=/\{\n\/\* \[wrapped with (.+)\] \*/,Bt=/,? & /,qt=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,zt=/\\(\\)?/g,Ut=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Gt=/\w*$/,Vt=/^[-+]0x[0-9a-f]+$/i,$t=/^0b[01]+$/i,Xt=/^\[object .+?Constructor\]$/,Yt=/^0o[0-7]+$/i,Kt=/^(?:0|[1-9]\d*)$/,Qt=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Jt=/($^)/,Zt=/['\n\r\u2028\u2029\\]/g,te="\\ud800-\\udfff",ee="\\u0300-\\u036f\\ufe20-\\ufe2f\\u20d0-\\u20ff",ne="\\u2700-\\u27bf",re="a-z\\xdf-\\xf6\\xf8-\\xff",ie="A-Z\\xc0-\\xd6\\xd8-\\xde",oe="\\ufe0e\\ufe0f",se="\\xac\\xb1\\xd7\\xf7\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf\\u2000-\\u206f \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",ae="["+te+"]",ue="["+se+"]",ce="["+ee+"]",le="["+ne+"]",he="["+re+"]",fe=(se="[^"+te+se+"\\d+"+ne+re+ie+"]",ne="\\ud83c[\\udffb-\\udfff]",re="[^"+te+"]","(?:\\ud83c[\\udde6-\\uddff]){2}"),pe="[\\ud800-\\udbff][\\udc00-\\udfff]",de=(ie="["+ie+"]","(?:"+he+"|"+se+")"),ge=(se="(?:"+ie+"|"+se+")","(?:['’](?:d|ll|m|re|s|t|ve))?"),me="(?:['’](?:D|LL|M|RE|S|T|VE))?",ve="(?:"+ce+"|"+ne+")?",ye="["+oe+"]?",be=(ye=ye+ve+"(?:\\u200d(?:"+[re,fe,pe].join("|")+")"+ye+ve+")*",ve="(?:"+[le,fe,pe].join("|")+")"+ye,le="(?:"+[re+ce+"?",ce,fe,pe,ae].join("|")+")",RegExp("['’]","g")),we=RegExp(ce,"g"),_e=RegExp(ne+"(?="+ne+")|"+le+ye,"g"),xe=RegExp([ie+"?"+he+"+"+ge+"(?="+[ue,ie,"$"].join("|")+")",se+"+"+me+"(?="+[ue,ie+de,"$"].join("|")+")",ie+"?"+de+"+"+ge,ie+"+"+me,"\\d*(?:(?:1ST|2ND|3RD|(?![123])\\dTH)\\b)","\\d*(?:(?:1st|2nd|3rd|(?![123])\\dth)\\b)","\\d+",ve].join("|"),"g"),Ce=RegExp("[\\u200d"+te+ee+oe+"]"),ke=/[a-z][A-Z]|[A-Z]{2,}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Se=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Oe=-1,Ee={},Te=(Ee[ht]=Ee[ft]=Ee[pt]=Ee[dt]=Ee[gt]=Ee[mt]=Ee[vt]=Ee[yt]=Ee[bt]=!0,Ee[$]=Ee[X]=Ee[ct]=Ee[Y]=Ee[lt]=Ee[K]=Ee[Q]=Ee[J]=Ee[tt]=Ee[et]=Ee[nt]=Ee[it]=Ee[ot]=Ee[st]=Ee[ut]=!1,{}),Ae=(Te[$]=Te[X]=Te[ct]=Te[lt]=Te[Y]=Te[K]=Te[ht]=Te[ft]=Te[pt]=Te[dt]=Te[gt]=Te[tt]=Te[et]=Te[nt]=Te[it]=Te[ot]=Te[st]=Te[at]=Te[mt]=Te[vt]=Te[yt]=Te[bt]=!0,Te[Q]=Te[J]=Te[ut]=!1,{"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"}),Ie=parseFloat,je=parseInt,Pe=(re="object"==typeof global&&global&&global.Object===Object&&global,fe="object"==typeof self&&self&&self.Object===Object&&self,re||fe||Function("return this")()),Ne=(pe="object"==typeof exports&&exports&&!exports.nodeType&&exports,ae=pe&&"object"==typeof module&&module&&!module.nodeType&&module,ae&&ae.exports===pe),De=Ne&&re.process,Re=(ce=function(){try{return De&&De.binding&&De.binding("util")}catch(re){}}(),ce&&ce.isArrayBuffer),Me=ce&&ce.isDate,We=ce&&ce.isMap,Le=ce&&ce.isRegExp,Fe=ce&&ce.isSet,He=ce&&ce.isTypedArray,Be=_("length"),qe=x({"À":"A","Á":"A","Â":"A","Ã":"A","Ä":"A","Å":"A","à":"a","á":"a","â":"a","ã":"a","ä":"a","å":"a","Ç":"C","ç":"c","Ð":"D","ð":"d","È":"E","É":"E","Ê":"E","Ë":"E","è":"e","é":"e","ê":"e","ë":"e","Ì":"I","Í":"I","Î":"I","Ï":"I","ì":"i","í":"i","î":"i","ï":"i","Ñ":"N","ñ":"n","Ò":"O","Ó":"O","Ô":"O","Õ":"O","Ö":"O","Ø":"O","ò":"o","ó":"o","ô":"o","õ":"o","ö":"o","ø":"o","Ù":"U","Ú":"U","Û":"U","Ü":"U","ù":"u","ú":"u","û":"u","ü":"u","Ý":"Y","ý":"y","ÿ":"y","Æ":"Ae","æ":"ae","Þ":"Th","þ":"th","ß":"ss","Ā":"A","Ă":"A","Ą":"A","ā":"a","ă":"a","ą":"a","Ć":"C","Ĉ":"C","Ċ":"C","Č":"C","ć":"c","ĉ":"c","ċ":"c","č":"c","Ď":"D","Đ":"D","ď":"d","đ":"d","Ē":"E","Ĕ":"E","Ė":"E","Ę":"E","Ě":"E","ē":"e","ĕ":"e","ė":"e","ę":"e","ě":"e","Ĝ":"G","Ğ":"G","Ġ":"G","Ģ":"G","ĝ":"g","ğ":"g","ġ":"g","ģ":"g","Ĥ":"H","Ħ":"H","ĥ":"h","ħ":"h","Ĩ":"I","Ī":"I","Ĭ":"I","Į":"I","İ":"I","ĩ":"i","ī":"i","ĭ":"i","į":"i","ı":"i","Ĵ":"J","ĵ":"j","Ķ":"K","ķ":"k","ĸ":"k","Ĺ":"L","Ļ":"L","Ľ":"L","Ŀ":"L","Ł":"L","ĺ":"l","ļ":"l","ľ":"l","ŀ":"l","ł":"l","Ń":"N","Ņ":"N","Ň":"N","Ŋ":"N","ń":"n","ņ":"n","ň":"n","ŋ":"n","Ō":"O","Ŏ":"O","Ő":"O","ō":"o","ŏ":"o","ő":"o","Ŕ":"R","Ŗ":"R","Ř":"R","ŕ":"r","ŗ":"r","ř":"r","Ś":"S","Ŝ":"S","Ş":"S","Š":"S","ś":"s","ŝ":"s","ş":"s","š":"s","Ţ":"T","Ť":"T","Ŧ":"T","ţ":"t","ť":"t","ŧ":"t","Ũ":"U","Ū":"U","Ŭ":"U","Ů":"U","Ű":"U","Ų":"U","ũ":"u","ū":"u","ŭ":"u","ů":"u","ű":"u","ų":"u","Ŵ":"W","ŵ":"w","Ŷ":"Y","ŷ":"y","Ÿ":"Y","Ź":"Z","Ż":"Z","Ž":"Z","ź":"z","ż":"z","ž":"z","Ĳ":"IJ","ĳ":"ij","Œ":"Oe","œ":"oe","ŉ":"'n","ſ":"s"}),ze=x({"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"}),Ue=x({"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"}),Ge=function x(te){function ee(t){if(Pi(t)&&!la(t)&&!(t instanceof ie)){if(t instanceof re)return t;if(xo.call(t,"__wrapped__"))return ii(t)}return new re(t)}function ne(){}function re(t,e){this.__wrapped__=t,this.__actions__=[],this.__chain__=!!e,this.__index__=0,this.__values__=F}function ie(t){this.__wrapped__=t,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=G,this.__views__=[]}function oe(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function se(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function ae(t){var e=-1,n=null==t?0:t.length;for(this.clear();++e<n;){var r=t[e];this.set(r[0],r[1])}}function ue(t){var e=-1,n=null==t?0:t.length;for(this.__data__=new ae;++e<n;)this.add(t[e])}function ce(t){t=this.__data__=new se(t),this.size=t.size}function le(t,e){var n,r=la(t),i=!r&&ca(t),o=!r&&!i&&fa(t),s=!r&&!i&&!o&&ma(t),a=r||i||o||s,u=a?S(t.length,go):[],c=u.length;for(n in t)!e&&!xo.call(t,n)||a&&("length"==n||o&&("offset"==n||"parent"==n)||s&&("buffer"==n||"byteLength"==n||"byteOffset"==n)||Ur(n,c))||u.push(n);return u}function he(t){var e=t.length;return e?t[En(0,e-1)]:F}function fe(t,e){return ei(ir(t),Ae(e,0,t.length))}function pe(t){return ei(ir(t))}function de(t,e,n){(n===F||ki(t[e],n))&&(n!==F||e in t)||_e(t,e,n)}function ge(t,e,n){var r=t[e];xo.call(t,e)&&ki(r,n)&&(n!==F||e in t)||_e(t,e,n)}function me(t,e){for(var n=t.length;n--;)if(ki(t[n][0],e))return n;return-1}function ve(t,e,n,r){return ys(t,(function(t,i,o){e(r,t,n(t),o)})),r}function ye(t,e){return t&&or(e,Vi(e),t)}function _e(t,e,n){"__proto__"==e&&Fo?Fo(t,e,{configurable:!0,enumerable:!0,value:n,writable:!0}):t[e]=n}function Ce(t,e){for(var n=-1,r=e.length,i=ao(r),o=null==t;++n<r;)i[n]=o?F:Ui(t,e[n]);return i}function Ae(t,e,n){return t==t&&(n!==F&&(t=t<=n?t:n),e!==F)?e<=t?t:e:t}function De(n,r,o,s,a,u){var c,l=1&r,h=2&r,p=4&r;if((c=o?a?o(n,s,a,u):o(n):c)===F){if(!ji(n))return n;var d,g,m;s=la(n);if(s){if(v=(y=n).length,m=y.constructor(v),v&&"string"==typeof y[0]&&xo.call(y,"index")&&(m.index=y.index,m.input=y.input),c=m,!l)return ir(n,c)}else{var v=As(n),y=v==J||v==Z;if(fa(n))return Jn(n,l);if(v==nt||v==$||y&&!a){if(c=h||y?{}:qr(n),!l)return h?(g=m=n,g=(d=c)&&or(g,$i(g),d),or(m,Ts(m),g)):(g=ye(c,d=n),or(d,Es(d),g))}else{if(!Te[v])return a?n:{};c=function(n,r,i,o){var s=n.constructor;switch(r){case ct:return Zn(n);case Y:case K:return new s(+n);case lt:return function(t,e){return e=e?Zn(t.buffer):t.buffer,new t.constructor(e,t.byteOffset,t.byteLength)}(n,o);case ht:case ft:case pt:case dt:case gt:case mt:case vt:case yt:case bt:return tr(n,o);case tt:return function(e,n,r){return f(n?r(N(e),1):N(e),t,new e.constructor)}(n,o,i);case et:case st:return new s(n);case it:return function(t){var e=new t.constructor(t.source,Gt.exec(t));return e.lastIndex=t.lastIndex,e}(n);case ot:return function(t,n,r){return f(n?r(M(t),1):M(t),e,new t.constructor)}(n,o,i);case at:return function(t){return fs?fo(fs.call(t)):{}}(n)}}(n,v,De,l)}}if(a=(u=u||new ce).get(n),a)return a;u.set(n,c);var b=s?F:(p?h?Dr:Nr:h?$i:Vi)(n);i(b||n,(function(t,e){b&&(t=n[e=t]),ge(c,e,De(t,r,o,e,n,u))}))}return c}function Be(t,e,n){var r=n.length;if(null==t)return!r;for(t=fo(t);r--;){var i=n[r],o=e[i],s=t[i];if(s===F&&!(i in t)||!o(s))return!1}return!0}function Ve(t,e,n){if("function"!=typeof t)throw new mo(H);return Ps((function(){t.apply(F,n)}),e)}function $e(t,e,n,r){var i=-1,o=u,s=!0,a=t.length,h=[],f=e.length;if(a){n&&(e=l(e,O(n))),r?(o=c,s=!1):200<=e.length&&(o=T,s=!1,e=new ue(e));t:for(;++i<a;){var p=t[i],d=null==n?p:n(p);p=r||0!==p?p:0;if(s&&d==d){for(var g=f;g--;)if(e[g]===d)continue t;h.push(p)}else o(e,d,r)||h.push(p)}}return h}function Xe(t,e){var n=!0;return ys(t,(function(t,r,i){return n=!!e(t,r,i)})),n}function Ye(t,e,n){for(var r=-1,i=t.length;++r<i;){var o,s,a=t[r],u=e(a);null!=u&&(o===F?u==u&&!Mi(u):n(u,o))&&(o=u,s=a)}return s}function Ke(t,e){var n=[];return ys(t,(function(t,r,i){e(t,r,i)&&n.push(t)})),n}function Qe(t,e,n,r,i){var o=-1,s=t.length;for(n=n||zr,i=i||[];++o<s;){var a=t[o];0<e&&n(a)?1<e?Qe(a,e-1,n,r,i):h(i,a):r||(i[i.length]=a)}return i}function Je(t,e){return t&&ws(t,e,Vi)}function Ze(t,e){return t&&_s(t,e,Vi)}function tn(t,e){return a(e,(function(e){return Ti(t[e])}))}function en(t,e){for(var n=0,r=(e=Kn(e,t)).length;null!=t&&n<r;)t=t[ni(e[n++])];return n&&n==r?t:F}function nn(t,e,n){return e=e(t),la(t)?e:h(e,n(t))}function rn(t){if(null==t)return t===F?"[object Undefined]":"[object Null]";if(Lo&&Lo in fo(t)){var e=t,n=xo.call(e,Lo),r=e[Lo];try{e[Lo]=F;var i=!0}catch(e){}var o=So.call(e);return i&&(n?e[Lo]=r:delete e[Lo]),o}return So.call(t)}function on(t,e){return e<t}function sn(t,e){return null!=t&&xo.call(t,e)}function an(t,e){return null!=t&&e in fo(t)}function un(t,e,n){for(var r=n?c:u,i=t[0].length,o=t.length,s=o,a=ao(o),h=1/0,f=[];s--;){var p=t[s];s&&e&&(p=l(p,O(e))),h=Ko(p.length,h),a[s]=!n&&(e||120<=i&&120<=p.length)?new ue(s&&p):F}p=t[0];var d=-1,g=a[0];t:for(;++d<i&&f.length<h;){var m=p[d],v=e?e(m):m;m=n||0!==m?m:0;if(!(g?T(g,v):r(f,v,n))){for(s=o;--s;){var y=a[s];if(!(y?T(y,v):r(t[s],v,n)))continue t}g&&g.push(v),f.push(m)}}return f}function cn(t,e,r){return e=null==(t=Jr(t,e=Kn(e,t)))?t:t[ni(ci(e))],null==e?F:n(e,t,r)}function ln(t){return Pi(t)&&rn(t)==$}function hn(t,e,n,r,i){return t===e||(null==t||null==e||!Pi(t)&&!Pi(e)?t!=t&&e!=e:function(t,e,n,r,i,o){var s=la(t),a=la(e),u=s?X:As(t),c=(a=a?X:As(e),(u=u==$?nt:u)==nt),l=(a=a==$?nt:a)==nt;a=u==a;if(a&&fa(t)){if(!fa(e))return!1;c=!(s=!0)}return a&&!c?(o=o||new ce,s||ma(t)?jr(t,e,n,r,i,o):function(t,e,n,r,i,o,s){switch(n){case lt:if(t.byteLength!=e.byteLength||t.byteOffset!=e.byteOffset)return!1;t=t.buffer,e=e.buffer;case ct:return!(t.byteLength!=e.byteLength||!o(new Io(t),new Io(e)));case Y:case K:case et:return ki(+t,+e);case Q:return t.name==e.name&&t.message==e.message;case it:case st:return t==e+"";case tt:var a=N;case ot:var u=1&r;return a=a||M,t.size==e.size||u?(u=s.get(t),u?u==e:(r|=2,s.set(t,e),u=jr(a(t),a(e),r,i,o,s),s.delete(t),u)):!1;case at:if(fs)return fs.call(t)==fs.call(e)}return!1}(t,e,u,n,r,i,o)):1&n||(s=c&&xo.call(t,"__wrapped__"),u=l&&xo.call(e,"__wrapped__"),!s&&!u)?!!a&&(o=o||new ce,function(t,e,n,r,i,o){var s=1&n,a=Nr(t),u=a.length,c=Nr(e).length;if(u!=c&&!s)return!1;for(var l=u;l--;){var h=a[l];if(!(s?h in e:xo.call(e,h)))return!1}if(c=o.get(t),c&&o.get(e))return c==e;var f,p=!0;o.set(t,e),o.set(e,t);for(var d=s;++l<u;){h=a[l];var g,m=t[h],v=e[h];if(!((g=r?s?r(v,m,h,e,t,o):r(m,v,h,t,e,o):g)===F?m===v||i(m,v,n,r,o):g)){p=!1;break}d=d||"constructor"==h}return p&&!d&&(c=t.constructor,f=e.constructor,c!=f)&&"constructor"in t&&"constructor"in e&&!("function"==typeof c&&c instanceof c&&"function"==typeof f&&f instanceof f)&&(p=!1),o.delete(t),o.delete(e),p}(t,e,n,r,i,o)):(c=s?t.value():t,l=u?e.value():e,o=o||new ce,i(c,l,n,r,o))}(t,e,n,r,hn,i))}function fn(t,e,n,r){var i=n.length,o=i,s=!r;if(null==t)return!o;for(t=fo(t);i--;){var a=n[i];if(s&&a[2]?a[1]!==t[a[0]]:!(a[0]in t))return!1}for(;++i<o;){var u=(a=n[i])[0],c=t[u],l=a[1];if(s&&a[2]){if(c===F&&!(u in t))return!1}else{var h,f=new ce;if(!((h=r?r(c,l,u,t,e,f):h)===F?hn(l,c,3,r,f):h))return!1}}return!0}function pn(t){var e;return!(!ji(t)||(e=t,ko&&ko in e))&&(Ti(t)?To:Xt).test(ri(t))}function dn(t){return"function"==typeof t?t:null==t?to:"object"==typeof t?la(t)?wn(t[0],t[1]):bn(t):io(t)}function gn(t){if(!Xr(t))return Xo(t);var e,n=[];for(e in fo(t))xo.call(t,e)&&"constructor"!=e&&n.push(e);return n}function mn(t){if(ji(t)){var e,n=Xr(t),r=[];for(e in t)("constructor"!=e||!n&&xo.call(t,e))&&r.push(e);return r}var i=t,o=[];if(null!=i)for(var s in fo(i))o.push(s);return o}function vn(t,e){return t<e}function yn(t,e){var n=-1,r=Si(t)?ao(t.length):[];return ys(t,(function(t,i,o){r[++n]=e(t,i,o)})),r}function bn(t){var e=Fr(t);return 1==e.length&&e[0][2]?Kr(e[0][0],e[0][1]):function(n){return n===t||fn(n,t,e)}}function wn(t,e){return Vr(t)&&Yr(e)?Kr(ni(t),e):function(n){var r=Ui(n,t);return r===F&&r===e?Gi(n,t):hn(e,r,3)}}function _n(t,e,n,r,i){t!==e&&ws(e,(function(o,s){var a,u,c,l,h,f,p,d,g,m,v,y,b;ji(o)?(i=i||new ce,u=e,l=n,h=_n,f=r,p=i,v=(a=t)[c=s],y=u[c],(b=p.get(y))||(b=f?f(v,y,c+"",a,u,p):F,(u=b===F)&&(d=la(y),g=!d&&fa(y),m=!d&&!g&&ma(y),b=y,d||g||m?b=la(v)?v:Oi(v)?ir(v):g?Jn(y,!(u=!1)):m?tr(y,!(u=!1)):[]:Di(y)||ca(y)?ca(b=v)?b=qi(v):(!ji(v)||l&&Ti(v))&&(b=qr(y)):u=!1),u&&(p.set(y,b),h(b,y,l,f,p),p.delete(y))),de(a,c,b)):(d=r?r(t[s],o,s+"",t,e,i):F,de(t,s,d=d===F?o:d))}),$i)}function xn(t,e){var n=t.length;if(n)return Ur(e+=e<0?n:0,n)?t[e]:F}function Cn(t,e,n){var r=-1;e=l(e.length?e:[to],O(Wr()));var i=yn(t,(function(t,n,i){return{criteria:l(e,(function(e){return e(t)})),index:++r,value:t}})),o=(t=function(t,e){for(var r=n,i=-1,o=t.criteria,s=e.criteria,a=o.length,u=r.length;++i<a;){var c,l=er(o[i],s[i]);if(l)return u<=i?l:(c=r[i],l*("desc"==c?-1:1))}return t.index-e.index},i.length);for(i.sort(t);o--;)i[o]=i[o].value;return i}function kn(t,e,n){for(var r=-1,i=e.length,o={};++r<i;){var s=e[r],a=en(t,s);n(a,s)&&Pn(o,Kn(s,t),a)}return o}function Sn(t,e,n,r){var i=r?y:v,o=-1,s=e.length,a=t;for(t===e&&(e=ir(e)),n&&(a=l(t,O(n)));++o<s;)for(var u=0,c=e[o],h=n?n(c):c;-1<(u=i(a,h,u,r));)a!==t&&Ro.call(a,u,1),Ro.call(t,u,1);return t}function On(t,e){for(var n=t?e.length:0,r=n-1;n--;){var i,o=e[n];n!=r&&o===i||(Ur(i=o)?Ro.call(t,o,1):qn(t,o))}}function En(t,e){return t+Uo(Zo()*(e-t+1))}function Tn(t,e){var n="";if(!(!t||e<1||z<e))for(;e%2&&(n+=t),(e=Uo(e/2))&&(t+=t),e;);return n}function An(t,e){return Ns(Qr(t,e,to),t+"")}function In(t){return he(Yi(t))}function jn(t,e){return t=Yi(t),ei(t,Ae(e,0,t.length))}function Pn(t,e,n,r){if(ji(t))for(var i=-1,o=(e=Kn(e,t)).length,s=o-1,a=t;null!=a&&++i<o;){var u,c=ni(e[i]),l=n;ge(a,c,l=i!=s&&(u=a[c],(l=r?r(u,c,a):F)===F)?ji(u)?u:Ur(e[i+1])?[]:{}:l),a=a[c]}return t}function Nn(t){return ei(Yi(t))}function Dn(t,e,n){var r=-1,i=t.length;(n=i<n?i:n)<0&&(n+=i),i=n<(e=e<0?i<-e?0:i+e:e)?0:n-e>>>0,e>>>=0;for(var o=ao(i);++r<i;)o[r]=t[r+e];return o}function Rn(t,e){var n;return ys(t,(function(t,r,i){return!(n=e(t,r,i))})),!!n}function Mn(t,e,n){var r=0,i=null==t?r:t.length;if("number"==typeof e&&e==e&&i<=2147483647){for(;r<i;){var o=r+i>>>1,s=t[o];null!==s&&!Mi(s)&&(n?s<=e:s<e)?r=1+o:i=o}return i}return Wn(t,e,to,n)}function Wn(t,e,n,r){e=n(e);for(var i=0,o=null==t?0:t.length,s=e!=e,a=null===e,u=Mi(e),c=e===F;i<o;){var l=Uo((i+o)/2),h=n(t[l]),f=h!==F,p=null===h,d=h==h,g=Mi(h);d=s?r||d:c?d&&(r||f):a?d&&f&&(r||!p):u?d&&f&&!p&&(r||!g):!p&&!g&&(r?h<=e:h<e);d?i=l+1:o=l}return Ko(o,4294967294)}function Ln(t,e){for(var n=-1,r=t.length,i=0,o=[];++n<r;){var s,a=t[n],u=e?e(a):a;n&&ki(u,s)||(s=u,o[i++]=0===a?0:a)}return o}function Fn(t){return"number"==typeof t?t:Mi(t)?U:+t}function Hn(t){var e;return"string"==typeof t?t:la(t)?l(t,Hn)+"":Mi(t)?ps?ps.call(t):"":"0"==(e=t+"")&&1/t==-1/0?"-0":e}function Bn(t,e,n){var r=-1,i=u,o=t.length,s=!0,a=[],l=a;if(n)s=!1,i=c;else if(200<=o){var h=e?null:Ss(t);if(h)return M(h);s=!1,i=T,l=new ue}else l=e?[]:a;t:for(;++r<o;){var f=t[r],p=e?e(f):f;f=n||0!==f?f:0;if(s&&p==p){for(var d=l.length;d--;)if(l[d]===p)continue t;e&&l.push(p),a.push(f)}else i(l,p,n)||(l!==a&&l.push(p),a.push(f))}return a}function qn(t,e){return null==(t=Jr(t,e=Kn(e,t)))||delete t[ni(ci(e))]}function zn(t,e,n,r){return Pn(t,e,n(en(t,e)),r)}function Un(t,e,n,r){for(var i=t.length,o=r?i:-1;(r?o--:++o<i)&&e(t[o],o,t););return n?Dn(t,r?0:o,r?o+1:i):Dn(t,r?o+1:0,r?i:o)}function Gn(t,e){var n=t;return f(e,(function(t,e){return e.func.apply(e.thisArg,h([t],e.args))}),n=t instanceof ie?t.value():n)}function Vn(t,e,n){var r=t.length;if(r<2)return r?Bn(t[0]):[];for(var i=-1,o=ao(r);++i<r;)for(var s=t[i],a=-1;++a<r;)a!=i&&(o[i]=$e(o[i]||s,t[a],e,n));return Bn(Qe(o,1),e,n)}function $n(t,e,n){for(var r=-1,i=t.length,o=e.length,s={};++r<i;){var a=r<o?e[r]:F;n(s,t[r],a)}return s}function Xn(t){return Oi(t)?t:[]}function Yn(t){return"function"==typeof t?t:to}function Kn(t,e){return la(t)?t:Vr(t,e)?[t]:Ds(zi(t))}function Qn(t,e,n){var r=t.length;return n=n===F?r:n,!e&&r<=n?t:Dn(t,e,n)}function Jn(t,e){return e?t.slice():(e=t.length,e=jo?jo(e):new t.constructor(e),t.copy(e),e)}function Zn(t){var e=new t.constructor(t.byteLength);return new Io(e).set(new Io(t)),e}function tr(t,e){return e=e?Zn(t.buffer):t.buffer,new t.constructor(e,t.byteOffset,t.length)}function er(t,e){if(t!==e){var n=t!==F,r=null===t,i=t==t,o=Mi(t),s=e!==F,a=null===e,u=e==e,c=Mi(e);if(!a&&!c&&!o&&e<t||o&&s&&u&&!a&&!c||r&&s&&u||!n&&u||!i)return 1;if(!r&&!o&&!c&&t<e||c&&n&&i&&!r&&!o||a&&n&&i||!s&&i||!u)return-1}return 0}function nr(t,e,n,r){for(var i=-1,o=t.length,s=n.length,a=-1,u=e.length,c=Yo(o-s,0),l=ao(u+c),h=!r;++a<u;)l[a]=e[a];for(;++i<s;)(h||i<o)&&(l[n[i]]=t[i]);for(;c--;)l[a++]=t[i++];return l}function rr(t,e,n,r){for(var i=-1,o=t.length,s=-1,a=n.length,u=-1,c=e.length,l=Yo(o-a,0),h=ao(l+c),f=!r;++i<l;)h[i]=t[i];for(var p=i;++u<c;)h[p+u]=e[u];for(;++s<a;)(f||i<o)&&(h[p+n[s]]=t[i++]);return h}function ir(t,e){var n=-1,r=t.length;for(e=e||ao(r);++n<r;)e[n]=t[n];return e}function or(t,e,n,r){var i=!n;n=n||{};for(var o=-1,s=e.length;++o<s;){var a=e[o],u=r?r(n[a],t[a],a,n,t):F;(i?_e:ge)(n,a,u=u===F?t[a]:u)}return n}function sr(t,e){return function(n,i){var o=la(n)?r:ve,s=e?e():{};return o(n,t,Wr(i,2),s)}}function ar(t){return An((function(e,n){var r=-1,i=n.length,o=1<i?n[i-1]:F,s=2<i?n[2]:F;o=3<t.length&&"function"==typeof o?(i--,o):F;for(s&&Gr(n[0],n[1],s)&&(o=i<3?F:o,i=1),e=fo(e);++r<i;){var a=n[r];a&&t(e,a,r,o)}return e}))}function ur(t,e){return function(n,r){if(null!=n){if(!Si(n))return t(n,r);for(var i=n.length,o=e?i:-1,s=fo(n);(e?o--:++o<i)&&!1!==r(s[o],o,s););}return n}}function cr(t){return function(e,n,r){for(var i=-1,o=fo(e),s=r(e),a=s.length;a--;){var u=s[t?a:++i];if(!1===n(o[u],u,o))break}return e}}function lr(t){return function(e){var n=P(e=zi(e))?L(e):F,r=n?n[0]:e.charAt(0);n=n?Qn(n,1).join(""):e.slice(1);return r[t]()+n}}function hr(t){return function(e){return f(Ji(Qi(e).replace(be,"")),t,"")}}function fr(t){return function(){var e=arguments;switch(e.length){case 0:return new t;case 1:return new t(e[0]);case 2:return new t(e[0],e[1]);case 3:return new t(e[0],e[1],e[2]);case 4:return new t(e[0],e[1],e[2],e[3]);case 5:return new t(e[0],e[1],e[2],e[3],e[4]);case 6:return new t(e[0],e[1],e[2],e[3],e[4],e[5]);case 7:return new t(e[0],e[1],e[2],e[3],e[4],e[5],e[6])}var n=ds(t.prototype),r=t.apply(n,e);return ji(r)?r:n}}function pr(t,e,r){var i=fr(t);return function o(){for(var s=arguments.length,a=ao(s),u=s,c=Mr(o);u--;)a[u]=arguments[u];return c=s<3&&a[0]!==c&&a[s-1]!==c?[]:R(a,c),(s-=c.length)<r?kr(t,e,mr,o.placeholder,F,a,c,F,F,r-s):n(this&&this!==Pe&&this instanceof o?i:t,this,a)}}function dr(t){return function(e,n,r){var i,o=fo(e);Si(e)||(i=Wr(n,3),e=Vi(e),n=function(t){return i(o[t],t,o)}),n=t(e,n,r);return-1<n?o[i?e[n]:n]:F}}function gr(t){return Pr((function(e){var n=e.length,r=n,i=re.prototype.thru;for(t&&e.reverse();r--;){var o=e[r];if("function"!=typeof o)throw new mo(H);i&&!u&&"wrapper"==Rr(o)&&(u=new re([],!0))}for(r=u?r:n;++r<n;)var s=Rr(o=e[r]),a="wrapper"==s?Os(o):F,u=a&&$r(a[0])&&424==a[1]&&!a[4].length&&1==a[9]?u[Rr(a[0])].apply(u,a[3]):1==o.length&&$r(o)?u[s]():u.thru(o);return function(){var t=arguments,r=t[0];if(u&&1==t.length&&la(r))return u.plant(r).value();for(var i=0,o=n?e[i].apply(this,t):r;++i<n;)o=e[i].call(this,o);return o}}))}function mr(t,e,n,r,i,o,s,a,u,c){var l=128&e,h=1&e,f=2&e,p=24&e,d=512&e,g=f?F:fr(t);return function m(){for(var v,y,b=arguments.length,w=ao(b),_=b;_--;)w[_]=arguments[_];return p&&(y=function(t,e){for(var n=t.length,r=0;n--;)t[n]===e&&++r;return r}(w,v=Mr(m))),r&&(w=nr(w,r,i,p)),o&&(w=rr(w,o,s,p)),b-=y,p&&b<c?(y=R(w,v),kr(t,e,mr,m.placeholder,n,w,y,a,u,c-b)):(v=h?n:this,y=f?v[t]:t,b=w.length,a?w=function(t,e){for(var n=t.length,r=Ko(e.length,n),i=ir(t);r--;){var o=e[r];t[r]=Ur(o,n)?i[o]:F}return t}(w,a):d&&1<b&&w.reverse(),l&&u<b&&(w.length=u),(y=this&&this!==Pe&&this instanceof m?g||fr(y):y).apply(v,w))}}function vr(t,e){return function(n,r){return i=t,o=e(r),s={},Je(n,(function(t,e,n){i(s,o(t),e,n)})),s;var i,o,s}}function yr(t,e){return function(n,r){var i;if(n===F&&r===F)return e;if(n!==F&&(i=n),r!==F){if(i===F)return r;r=("string"==typeof n||"string"==typeof r?(n=Hn(n),Hn):(n=Fn(n),Fn))(r),i=t(n,r)}return i}}function br(t){return Pr((function(e){return e=l(e,O(Wr())),An((function(r){var i=this;return t(e,(function(t){return n(t,i,r)}))}))}))}function wr(t,e){var n=(e=e===F?" ":Hn(e)).length;return n<2?n?Tn(e,t):e:(n=Tn(e,zo(t/W(e))),P(e)?Qn(L(n),0,t).join(""):n.slice(0,t))}function _r(t,e,r,i){var o=1&e,s=fr(t);return function e(){for(var a=-1,u=arguments.length,c=-1,l=i.length,h=ao(l+u),f=this&&this!==Pe&&this instanceof e?s:t;++c<l;)h[c]=i[c];for(;u--;)h[c++]=arguments[++a];return n(f,o?r:this,h)}}function xr(t){return function(e,n,r){r&&"number"!=typeof r&&Gr(e,n,r)&&(n=r=F),e=Li(e),n===F?(n=e,e=0):n=Li(n),r=r===F?e<n?1:-1:Li(r);for(var i=e,o=r,s=t,a=-1,u=Yo(zo((n-i)/(o||1)),0),c=ao(u);u--;)c[s?u:++a]=i,i+=o;return c}}function Cr(t){return function(e,n){return"string"==typeof e&&"string"==typeof n||(e=Bi(e),n=Bi(n)),t(e,n)}}function kr(t,e,n,r,i,o,s,a,u,c){var l=8&e;4&(e=(e|(l?32:64))&~(l?64:32))||(e&=-4),i=[t,e,i,l?o:F,l?s:F,l?F:o,l?F:s,a,u,c],o=n.apply(F,i);return $r(t)&&js(o,i),o.placeholder=r,Zr(o,t,e)}function Sr(t){var e=ho[t];return function(t,n){var r;return t=Bi(t),(n=null==n?0:Ko(Fi(n),292))?(r=(zi(t)+"e").split("e"),+((r=(zi(e(r[0]+"e"+(+r[1]+n)))+"e").split("e"))[0]+"e"+(+r[1]-n))):e(t)}}function Or(t){return function(e){var n,r,i,o=As(e);return o==tt?N(e):o==ot?(o=e,r=-1,i=Array(o.size),o.forEach((function(t){i[++r]=[t,t]})),i):l(t(n=e),(function(t){return[t,n[t]]}))}}function Er(t,e,n,r,i,o,s,a){var u,c,l,h,f,p,d,g,m,v,y,b,w,_=2&e;if(_||"function"==typeof t)return(u=r?r.length:0)||(e&=-97,r=i=F),s=s===F?s:Yo(Fi(s),0),a=a===F?a:Fi(a),u-=i?i.length:0,64&e&&(l=r,g=i,r=i=F),c=_?F:Os(t),l=[t,e,n,r,i,l,g,o,s,a],c&&(g=c,s=(o=l)[1],v=g[1],b=(y=s|v)<131,w=128==v&&8==s||128==v&&256==s&&o[7].length<=g[8]||384==v&&g[7].length<=g[8]&&8==s,b||w)&&(1&v&&(o[2]=g[2],y|=1&s?0:4),(b=g[3])&&(m=o[3],o[3]=m?nr(m,b,g[4]):b,o[4]=m?R(o[3],q):g[4]),(b=g[5])&&(m=o[5],o[5]=m?rr(m,b,g[6]):b,o[6]=m?R(o[5],q):g[6]),(b=g[7])&&(o[7]=b),128&v&&(o[8]=null==o[8]?g[8]:Ko(o[8],g[8])),null==o[9]&&(o[9]=g[9]),o[0]=g[0],o[1]=y),t=l[0],e=l[1],n=l[2],r=l[3],i=l[4],!(a=l[9]=l[9]===F?_?0:t.length:Yo(l[9]-u,0))&&24&e&&(e&=-25),w=e&&1!=e?8==e||16==e?pr(t,e,a):32!=e&&33!=e||i.length?mr.apply(F,l):_r(t,e,n,r):(f=n,p=1&e,d=fr(h=t),function t(){return(this&&this!==Pe&&this instanceof t?d:h).apply(p?f:this,arguments)}),Zr((c?xs:js)(w,l),t,e);throw new mo(H)}function Tr(t,e,n,r){return t===F||ki(t,bo[n])&&!xo.call(r,n)?e:t}function Ar(t,e,n,r,i,o){return ji(t)&&ji(e)&&(o.set(e,t),_n(t,e,F,Ar,o),o.delete(e)),t}function Ir(t){return Di(t)?F:t}function jr(t,e,n,r,i,o){var s=1&n,a=t.length,u=e.length;if(a!=u&&!(s&&a<u))return!1;if(u=o.get(t),u&&o.get(e))return u==e;var c=-1,l=!0,h=2&n?new ue:F;for(o.set(t,e),o.set(e,t);++c<a;){var f,p=t[c],g=e[c];if((f=r?s?r(g,p,c,e,t,o):r(p,g,c,t,e,o):f)!==F){if(f)continue;l=!1;break}if(h){if(!d(e,(function(t,e){return!T(h,e)&&(p===t||i(p,t,n,r,o))&&h.push(e)}))){l=!1;break}}else if(p!==g&&!i(p,g,n,r,o)){l=!1;break}}return o.delete(t),o.delete(e),l}function Pr(t){return Ns(Qr(t,F,ai),t+"")}function Nr(t){return nn(t,Vi,Es)}function Dr(t){return nn(t,$i,Ts)}function Rr(t){for(var e=t.name+"",n=ss[e],r=xo.call(ss,e)?n.length:0;r--;){var i=n[r],o=i.func;if(null==o||o==t)return i.name}return e}function Mr(t){return(xo.call(ee,"placeholder")?ee:t).placeholder}function Wr(){var t=(t=ee.iteratee||eo)===eo?dn:t;return arguments.length?t(arguments[0],arguments[1]):t}function Lr(t,e){var n,r;t=t.__data__;return("string"==(r=typeof(n=e))||"number"==r||"symbol"==r||"boolean"==r?"__proto__"!==n:null===n)?t["string"==typeof e?"string":"hash"]:t.map}function Fr(t){for(var e=Vi(t),n=e.length;n--;){var r=e[n],i=t[r];e[n]=[r,i,Yr(i)]}return e}function Hr(t,e){return t=null==t?F:t[e],pn(t)?t:F}function Br(t,e,n){for(var r=-1,i=(e=Kn(e,t)).length,o=!1;++r<i;){var s=ni(e[r]);if(!(o=null!=t&&n(t,s)))break;t=t[s]}return o||++r!=i?o:!!(i=null==t?0:t.length)&&Ii(i)&&Ur(s,i)&&(la(t)||ca(t))}function qr(t){return"function"!=typeof t.constructor||Xr(t)?{}:ds(Po(t))}function zr(t){return la(t)||ca(t)||!!(Mo&&t&&t[Mo])}function Ur(t,e){return!!(e=null==e?z:e)&&("number"==typeof t||Kt.test(t))&&-1<t&&t%1==0&&t<e}function Gr(t,e,n){var r;if(ji(n))return("number"==(r=typeof e)?Si(n)&&Ur(e,n.length):"string"==r&&e in n)&&ki(n[e],t)}function Vr(t,e){var n;if(!la(t))return"number"==(n=typeof t)||"symbol"==n||"boolean"==n||null==t||Mi(t)||jt.test(t)||!It.test(t)||null!=e&&t in fo(e)}function $r(t){var e=Rr(t),n=ee[e];return"function"==typeof n&&e in ie.prototype&&(t===n||(e=Os(n))&&t===e[0])}function Xr(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||bo)}function Yr(t){return t==t&&!ji(t)}function Kr(t,e){return function(n){return null!=n&&n[t]===e&&(e!==F||t in fo(n))}}function Qr(t,e,r){return e=Yo(e===F?t.length-1:e,0),function(){for(var i=arguments,o=-1,s=Yo(i.length-e,0),a=ao(s);++o<s;)a[o]=i[e+o];o=-1;for(var u=ao(e+1);++o<e;)u[o]=i[o];return u[e]=r(a),n(t,this,u)}}function Jr(t,e){return e.length<2?t:en(t,Dn(e,0,-1))}function Zr(t,e,n){var r,o,s;e+="";return Ns(t,(o=(e=(e=t=e).match(Ht))?e[1].split(Bt):[],s=n,i(V,(function(t){var e="_."+t[0];s&t[1]&&!u(o,e)&&o.push(e)})),e=o.sort(),(n=e.length)?(e[r=n-1]=(1<n?"& ":"")+e[r],e=e.join(2<n?", ":" "),t.replace(Ft,"{\n/* [wrapped with "+e+"] */\n")):t))}function ti(t){var e=0,n=0;return function(){var r=Qo(),i=16-(r-n);if(n=r,0<i){if(800<=++e)return arguments[0]}else e=0;return t.apply(F,arguments)}}function ei(t,e){var n=-1,r=t.length,i=r-1;for(e=e===F?r:e;++n<e;){var o=En(n,i),s=t[o];t[o]=t[n],t[n]=s}return t.length=e,t}function ni(t){var e;return"string"==typeof t||Mi(t)?t:"0"==(e=t+"")&&1/t==-1/0?"-0":e}function ri(t){if(null!=t){try{return _o.call(t)}catch(t){}try{return t+""}catch(t){}}return""}function ii(t){var e;return t instanceof ie?t.clone():((e=new re(t.__wrapped__,t.__chain__)).__actions__=ir(t.__actions__),e.__index__=t.__index__,e.__values__=t.__values__,e)}function oi(t,e,n){var r=null==t?0:t.length;return r?((n=null==n?0:Fi(n))<0&&(n=Yo(r+n,0)),m(t,Wr(e,3),n)):-1}function si(t,e,n){var r,i=null==t?0:t.length;return i?(r=i-1,n!==F&&(r=Fi(n),r=n<0?Yo(i+r,0):Ko(r,i-1)),m(t,Wr(e,3),r,!0)):-1}function ai(t){return null!=t&&t.length?Qe(t,1):[]}function ui(t){return t&&t.length?t[0]:F}function ci(t){var e=null==t?0:t.length;return e?t[e-1]:F}function li(t,e){return t&&t.length&&e&&e.length?Sn(t,e):t}function hi(t){return null==t?t:ts.call(t)}function fi(t){var e;return t&&t.length?(e=0,t=a(t,(function(t){return Oi(t)&&(e=Yo(t.length,e),1)})),S(e,(function(e){return l(t,_(e))}))):[]}function pi(t,e){return t&&t.length?(t=fi(t),null==e?t:l(t,(function(t){return n(e,F,t)}))):[]}function di(t){return t=ee(t),t.__chain__=!0,t}function gi(t,e){return e(t)}function mi(t,e){return(la(t)?i:ys)(t,Wr(e,3))}function vi(t,e){return(la(t)?o:bs)(t,Wr(e,3))}function yi(t,e){return(la(t)?l:yn)(t,Wr(e,3))}function bi(t,e,n){return e=n?F:e,e=t&&null==e?t.length:e,Er(t,128,F,F,F,F,e)}function wi(t,e){var n;if("function"!=typeof e)throw new mo(H);return t=Fi(t),function(){return 0<--t&&(n=e.apply(this,arguments)),t<=1&&(e=F),n}}function _i(t,e,n){function r(e){var n=u,r=c;return u=c=F,d=e,h=t.apply(r,n)}function i(t){var n=t-p;return p===F||e<=n||n<0||m&&l<=t-d}function o(){var t,n=ta();return i(n)?s(n):void(f=Ps(o,(t=e-(n-p),m?Ko(t,l-(n-d)):t)))}function s(t){return f=F,v&&u?r(t):(u=c=F,h)}function a(){var t=ta(),n=i(t);if(u=arguments,c=this,p=t,n){if(f===F)return d=t=p,f=Ps(o,e),g?r(t):h;if(m)return f=Ps(o,e),r(p)}return f===F&&(f=Ps(o,e)),h}var u,c,l,h,f,p,d=0,g=!1,m=!1,v=!0;if("function"!=typeof t)throw new mo(H);return e=Bi(e)||0,ji(n)&&(g=!!n.leading,m="maxWait"in n,l=m?Yo(Bi(n.maxWait)||0,e):l,v="trailing"in n?!!n.trailing:v),a.cancel=function(){f!==F&&ks(f),d=0,u=p=c=f=F},a.flush=function(){return f===F?h:s(ta())},a}function xi(t,e){if("function"!=typeof t||null!=e&&"function"!=typeof e)throw new mo(H);function n(){var r=arguments,i=e?e.apply(this,r):r[0],o=n.cache;return o.has(i)?o.get(i):(r=t.apply(this,r),n.cache=o.set(i,r)||o,r)}return n.cache=new(xi.Cache||ae),n}function Ci(t){if("function"!=typeof t)throw new mo(H);return function(){var e=arguments;switch(e.length){case 0:return!t.call(this);case 1:return!t.call(this,e[0]);case 2:return!t.call(this,e[0],e[1]);case 3:return!t.call(this,e[0],e[1],e[2])}return!t.apply(this,e)}}function ki(t,e){return t===e||t!=t&&e!=e}function Si(t){return null!=t&&Ii(t.length)&&!Ti(t)}function Oi(t){return Pi(t)&&Si(t)}function Ei(t){var e;return!!Pi(t)&&((e=rn(t))==Q||"[object DOMException]"==e||"string"==typeof t.message&&"string"==typeof t.name&&!Di(t))}function Ti(t){return!!ji(t)&&((t=rn(t))==J||t==Z||"[object AsyncFunction]"==t||"[object Proxy]"==t)}function Ai(t){return"number"==typeof t&&t==Fi(t)}function Ii(t){return"number"==typeof t&&-1<t&&t%1==0&&t<=z}function ji(t){var e=typeof t;return null!=t&&("object"==e||"function"==e)}function Pi(t){return null!=t&&"object"==typeof t}function Ni(t){return"number"==typeof t||Pi(t)&&rn(t)==et}function Di(t){return!(!Pi(t)||rn(t)!=nt)&&(null===(t=Po(t))||"function"==typeof(t=xo.call(t,"constructor")&&t.constructor)&&t instanceof t&&_o.call(t)==Oo)}function Ri(t){return"string"==typeof t||!la(t)&&Pi(t)&&rn(t)==st}function Mi(t){return"symbol"==typeof t||Pi(t)&&rn(t)==at}function Wi(t){if(!t)return[];if(Si(t))return(Ri(t)?L:ir)(t);var e;if(Wo&&t[Wo]){for(var n,r=t[Wo](),i=[];!(n=r.next()).done;)i.push(n.value);return i}return((e=As(t))==tt?N:e==ot?M:Yi)(t)}function Li(t){return t?(t=Bi(t))===1/0||t===-1/0?17976931348623157e292*(t<0?-1:1):t==t?t:0:0===t?t:0}function Fi(t){t=Li(t);var e=t%1;return t==t?e?t-e:t:0}function Hi(t){return t?Ae(Fi(t),0,G):0}function Bi(t){if("number"==typeof t)return t;if(Mi(t))return U;if("string"!=typeof(t=ji(t)?ji(e="function"==typeof t.valueOf?t.valueOf():t)?e+"":e:t))return 0===t?t:+t;t=t.replace(Mt,"");var e=$t.test(t);return e||Yt.test(t)?je(t.slice(2),e?2:8):Vt.test(t)?U:+t}function qi(t){return or(t,$i(t))}function zi(t){return null==t?"":Hn(t)}function Ui(t,e,n){return t=null==t?F:en(t,e),t===F?n:t}function Gi(t,e){return null!=t&&Br(t,e,an)}function Vi(t){return(Si(t)?le:gn)(t)}function $i(t){return Si(t)?le(t,!0):mn(t)}function Xi(t,e){var n;return null==t?{}:(n=l(Dr(t),(function(t){return[t]})),e=Wr(e),kn(t,n,(function(t,n){return e(t,n[0])})))}function Yi(t){return null==t?[]:E(t,Vi(t))}function Ki(t){return qa(zi(t).toLowerCase())}function Qi(t){return(t=zi(t))&&t.replace(Qt,qe).replace(we,"")}function Ji(t,e,n){return t=zi(t),(e=n?F:e)===F?(n=t,ke.test(n)?t.match(xe)||[]:t.match(qt)||[]):t.match(e)||[]}function Zi(t){return function(){return t}}function to(t){return t}function eo(t){return dn("function"==typeof t?t:De(t,1))}function no(t,e,n){var r=Vi(e),o=tn(e,r),s=(null!=n||ji(e)&&(o.length||!r.length)||(n=e,e=t,t=this,o=tn(e,Vi(e))),!(ji(n)&&"chain"in n&&!n.chain)),a=Ti(t);return i(o,(function(n){var r=e[n];t[n]=r,a&&(t.prototype[n]=function(){var e,n=this.__chain__;return s||n?(((e=t(this.__wrapped__)).__actions__=ir(this.__actions__)).push({func:r,args:arguments,thisArg:t}),e.__chain__=n,e):r.apply(t,h([this.value()],arguments))})})),t}function ro(){}function io(t){return Vr(t)?_(ni(t)):(e=t,function(t){return en(t,e)});var e}function oo(){return[]}function so(){return!1}var ao=(te=null==te?Pe:Ge.defaults(Pe.Object(),te,Ge.pick(Pe,Se))).Array,uo=te.Date,co=te.Error,lo=te.Function,ho=te.Math,fo=te.Object,po=te.RegExp,go=te.String,mo=te.TypeError,vo=ao.prototype,yo=lo.prototype,bo=fo.prototype,wo=te["__core-js_shared__"],_o=yo.toString,xo=bo.hasOwnProperty,Co=0,ko=(yo=/[^.]+$/.exec(wo&&wo.keys&&wo.keys.IE_PROTO||""))?"Symbol(src)_1."+yo:"",So=bo.toString,Oo=_o.call(fo),Eo=Pe._,To=po("^"+_o.call(xo).replace(Dt,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Ao=(yo=Ne?te.Buffer:F,te.Symbol),Io=te.Uint8Array,jo=yo?yo.allocUnsafe:F,Po=D(fo.getPrototypeOf,fo),No=fo.create,Do=bo.propertyIsEnumerable,Ro=vo.splice,Mo=Ao?Ao.isConcatSpreadable:F,Wo=Ao?Ao.iterator:F,Lo=Ao?Ao.toStringTag:F,Fo=function(){try{var t=Hr(fo,"defineProperty");return t({},"",{}),t}catch(t){}}(),Ho=te.clearTimeout!==Pe.clearTimeout&&te.clearTimeout,Bo=uo&&uo.now!==Pe.Date.now&&uo.now,qo=te.setTimeout!==Pe.setTimeout&&te.setTimeout,zo=ho.ceil,Uo=ho.floor,Go=fo.getOwnPropertySymbols,Vo=(yo=yo?yo.isBuffer:F,te.isFinite),$o=vo.join,Xo=D(fo.keys,fo),Yo=ho.max,Ko=ho.min,Qo=uo.now,Jo=te.parseInt,Zo=ho.random,ts=vo.reverse,es=(uo=Hr(te,"DataView"),Hr(te,"Map")),ns=Hr(te,"Promise"),rs=Hr(te,"Set"),is=(te=Hr(te,"WeakMap"),Hr(fo,"create")),os=te&&new te,ss={},as=ri(uo),us=ri(es),cs=ri(ns),ls=ri(rs),hs=ri(te),fs=(Ao=Ao?Ao.prototype:F,Ao?Ao.valueOf:F),ps=Ao?Ao.toString:F,ds=function(t){return ji(t)?No?No(t):(gs.prototype=t,t=new gs,gs.prototype=F,t):{}};function gs(){}ee.templateSettings={escape:Et,evaluate:Tt,interpolate:At,variable:"",imports:{_:ee}},(ee.prototype=ne.prototype).constructor=ee,(re.prototype=ds(ne.prototype)).constructor=re,(ie.prototype=ds(ne.prototype)).constructor=ie,oe.prototype.clear=function(){this.__data__=is?is(null):{},this.size=0},oe.prototype.delete=function(t){return t=this.has(t)&&delete this.__data__[t],this.size-=t?1:0,t},oe.prototype.get=function(t){var e,n=this.__data__;return is?(e=n[t])===B?F:e:xo.call(n,t)?n[t]:F},oe.prototype.has=function(t){var e=this.__data__;return is?e[t]!==F:xo.call(e,t)},oe.prototype.set=function(t,e){var n=this.__data__;return this.size+=this.has(t)?0:1,n[t]=is&&e===F?B:e,this},se.prototype.clear=function(){this.__data__=[],this.size=0},se.prototype.delete=function(t){var e=this.__data__;return!((t=me(e,t))<0||(t==e.length-1?e.pop():Ro.call(e,t,1),--this.size,0))},se.prototype.get=function(t){var e=this.__data__;return(t=me(e,t))<0?F:e[t][1]},se.prototype.has=function(t){return-1<me(this.__data__,t)},se.prototype.set=function(t,e){var n=this.__data__,r=me(n,t);return r<0?(++this.size,n.push([t,e])):n[r][1]=e,this},ae.prototype.clear=function(){this.size=0,this.__data__={hash:new oe,map:new(es||se),string:new oe}},ae.prototype.delete=function(t){return t=Lr(this,t).delete(t),this.size-=t?1:0,t},ae.prototype.get=function(t){return Lr(this,t).get(t)},ae.prototype.has=function(t){return Lr(this,t).has(t)},ae.prototype.set=function(t,e){var n=Lr(this,t),r=n.size;return n.set(t,e),this.size+=n.size==r?0:1,this},ue.prototype.add=ue.prototype.push=function(t){return this.__data__.set(t,B),this},ue.prototype.has=function(t){return this.__data__.has(t)},ce.prototype.clear=function(){this.__data__=new se,this.size=0},ce.prototype.delete=function(t){var e=this.__data__;t=e.delete(t);return this.size=e.size,t},ce.prototype.get=function(t){return this.__data__.get(t)},ce.prototype.has=function(t){return this.__data__.has(t)},ce.prototype.set=function(t,e){var n=this.__data__;if(n instanceof se){var r=n.__data__;if(!es||r.length<199)return r.push([t,e]),this.size=++n.size,this;n=this.__data__=new ae(r)}return n.set(t,e),this.size=n.size,this};var ms,vs,ys=ur(Je),bs=ur(Ze,!0),ws=cr(),_s=cr(!0),xs=os?function(t,e){return os.set(t,e),t}:to,Cs=(Ao=Fo?function(t,e){return Fo(t,"toString",{configurable:!0,enumerable:!1,value:Zi(e),writable:!0})}:to,An),ks=Ho||function(t){return Pe.clearTimeout(t)},Ss=rs&&1/M(new rs([,-0]))[1]==1/0?function(t){return new rs(t)}:ro,Os=os?function(t){return os.get(t)}:ro,Es=Go?function(t){return null==t?[]:(t=fo(t),a(Go(t),(function(e){return Do.call(t,e)})))}:oo,Ts=Go?function(t){for(var e=[];t;)h(e,Es(t)),t=Po(t);return e}:oo,As=rn,Is=((uo&&As(new uo(new ArrayBuffer(1)))!=lt||es&&As(new es)!=tt||ns&&As(ns.resolve())!=rt||rs&&As(new rs)!=ot||te&&As(new te)!=ut)&&(As=function(t){var e=rn(t);t=e==nt?t.constructor:F,t=t?ri(t):"";if(t)switch(t){case as:return lt;case us:return tt;case cs:return rt;case ls:return ot;case hs:return ut}return e}),wo?Ti:so),js=ti(xs),Ps=qo||function(t,e){return Pe.setTimeout(t,e)},Ns=ti(Ao),Ds=(ms=(Ho=xi(Ho=function(t){var e=[];return Pt.test(t)&&e.push(""),t.replace(Nt,(function(t,n,r,i){e.push(r?i.replace(zt,"$1"):n||t)})),e},(function(t){return 500===ms.size&&ms.clear(),t}))).cache,Ho),Rs=(uo=An((function(t,e){return Oi(t)?$e(t,Qe(e,1,Oi,!0)):[]})),ns=An((function(t,e){var n=ci(e);return Oi(n)&&(n=F),Oi(t)?$e(t,Qe(e,1,Oi,!0),Wr(n,2)):[]})),te=An((function(t,e){var n=ci(e);return Oi(n)&&(n=F),Oi(t)?$e(t,Qe(e,1,Oi,!0),F,n):[]})),wo=An((function(t){var e=l(t,Xn);return e.length&&e[0]===t[0]?un(e):[]})),qo=An((function(t){var e=ci(t),n=l(t,Xn);return e===ci(n)?e=F:n.pop(),n.length&&n[0]===t[0]?un(n,Wr(e,2)):[]})),Ao=An((function(t){var e=ci(t),n=l(t,Xn);return(e="function"==typeof e?e:F)&&n.pop(),n.length&&n[0]===t[0]?un(n,F,e):[]})),Ho=An(li),Pr((function(t,e){var n=null==t?0:t.length,r=Ce(t,e);return On(t,l(e,(function(t){return Ur(t,n)?+t:t})).sort(er)),r}))),Ms=An((function(t){return Bn(Qe(t,1,Oi,!0))})),Ws=An((function(t){var e=ci(t);return Oi(e)&&(e=F),Bn(Qe(t,1,Oi,!0),Wr(e,2))})),Ls=An((function(t){var e="function"==typeof(e=ci(t))?e:F;return Bn(Qe(t,1,Oi,!0),F,e)})),Fs=An((function(t,e){return Oi(t)?$e(t,e):[]})),Hs=An((function(t){return Vn(a(t,Oi))})),Bs=An((function(t){var e=ci(t);return Oi(e)&&(e=F),Vn(a(t,Oi),Wr(e,2))})),qs=An((function(t){var e="function"==typeof(e=ci(t))?e:F;return Vn(a(t,Oi),F,e)})),zs=An(fi),Us=An((function(t){var e=t.length;e="function"==typeof(e=1<e?t[e-1]:F)?(t.pop(),e):F;return pi(t,e)})),Gs=Pr((function(t){function e(e){return Ce(e,t)}var n=t.length,r=n?t[0]:0,i=this.__wrapped__;return!(1<n||this.__actions__.length)&&i instanceof ie&&Ur(r)?((i=i.slice(r,+r+(n?1:0))).__actions__.push({func:gi,args:[e],thisArg:F}),new re(i,this.__chain__).thru((function(t){return n&&!t.length&&t.push(F),t}))):this.thru(e)})),Vs=sr((function(t,e,n){xo.call(t,n)?++t[n]:_e(t,n,1)})),$s=dr(oi),Xs=dr(si),Ys=sr((function(t,e,n){xo.call(t,n)?t[n].push(e):_e(t,n,[e])})),Ks=An((function(t,e,r){var i=-1,o="function"==typeof e,s=Si(t)?ao(t.length):[];return ys(t,(function(t){s[++i]=o?n(e,t,r):cn(t,e,r)})),s})),Qs=sr((function(t,e,n){_e(t,n,e)})),Js=sr((function(t,e,n){t[n?0:1].push(e)}),(function(){return[[],[]]})),Zs=An((function(t,e){var n;return null==t?[]:(1<(n=e.length)&&Gr(t,e[0],e[1])?e=[]:2<n&&Gr(e[0],e[1],e[2])&&(e=[e[0]]),Cn(t,Qe(e,1),[]))})),ta=Bo||function(){return Pe.Date.now()},ea=An((function(t,e,n){var r,i=1;return n.length&&(r=R(n,Mr(ea)),i|=32),Er(t,i,e,n,r)})),na=An((function(t,e,n){var r,i=3;return n.length&&(r=R(n,Mr(na)),i|=32),Er(e,i,t,n,r)})),ra=(Bo=An((function(t,e){return Ve(t,1,e)})),An((function(t,e,n){return Ve(t,Bi(e)||0,n)}))),ia=(Cs=(xi.Cache=ae,Cs((function(t,e){var r=(e=1==e.length&&la(e[0])?l(e[0],O(Wr())):l(Qe(e,1),O(Wr()))).length;return An((function(i){for(var o=-1,s=Ko(i.length,r);++o<s;)i[o]=e[o].call(this,i[o]);return n(t,this,i)}))}))),An((function(t,e){var n=R(e,Mr(ia));return Er(t,32,F,e,n)}))),oa=An((function(t,e){var n=R(e,Mr(oa));return Er(t,64,F,e,n)})),sa=Pr((function(t,e){return Er(t,256,F,F,F,e)})),aa=Cr(on),ua=Cr((function(t,e){return e<=t})),ca=ln(function(){return arguments}())?ln:function(t){return Pi(t)&&xo.call(t,"callee")&&!Do.call(t,"callee")},la=ao.isArray,ha=Re?O(Re):function(t){return Pi(t)&&rn(t)==ct},fa=yo||so,pa=(yo=Me?O(Me):function(t){return Pi(t)&&rn(t)==K},We?O(We):function(t){return Pi(t)&&As(t)==tt}),da=Le?O(Le):function(t){return Pi(t)&&rn(t)==it},ga=Fe?O(Fe):function(t){return Pi(t)&&As(t)==ot},ma=He?O(He):function(t){return Pi(t)&&Ii(t.length)&&!!Ee[rn(t)]},va=Cr(vn),ya=Cr((function(t,e){return t<=e})),ba=ar((function(t,e){if(Xr(e)||Si(e))or(e,Vi(e),t);else for(var n in e)xo.call(e,n)&&ge(t,n,e[n])})),wa=ar((function(t,e){or(e,$i(e),t)})),_a=ar((function(t,e,n,r){or(e,$i(e),t,r)})),xa=ar((function(t,e,n,r){or(e,Vi(e),t,r)})),Ca=Pr(Ce),ka=An((function(t){return t.push(F,Tr),n(_a,F,t)})),Sa=An((function(t){return t.push(F,Ar),n(Ia,F,t)})),Oa=vr((function(t,e,n){t[e]=n}),Zi(to)),Ea=vr((function(t,e,n){xo.call(t,e)?t[e].push(n):t[e]=[n]}),Wr),Ta=An(cn),Aa=ar((function(t,e,n){_n(t,e,n)})),Ia=ar((function(t,e,n,r){_n(t,e,n,r)})),ja=Pr((function(t,e){var n={};if(null!=t){var r=!1;e=l(e,(function(e){return e=Kn(e,t),r=r||1<e.length,e})),or(t,Dr(t),n),r&&(n=De(n,7,Ir));for(var i=e.length;i--;)qn(n,e[i])}return n})),Pa=Pr((function(t,e){return null==t?{}:kn(n=t,e,(function(t,e){return Gi(n,e)}));var n})),Na=Or(Vi),Da=Or($i),Ra=hr((function(t,e,n){return e=e.toLowerCase(),t+(n?Ki(e):e)})),Ma=hr((function(t,e,n){return t+(n?"-":"")+e.toLowerCase()})),Wa=hr((function(t,e,n){return t+(n?" ":"")+e.toLowerCase()})),La=lr("toLowerCase"),Fa=hr((function(t,e,n){return t+(n?"_":"")+e.toLowerCase()})),Ha=hr((function(t,e,n){return t+(n?" ":"")+qa(e)})),Ba=hr((function(t,e,n){return t+(n?" ":"")+e.toUpperCase()})),qa=lr("toUpperCase"),za=An((function(t,e){try{return n(t,F,e)}catch(t){return Ei(t)?t:new co(t)}})),Ua=Pr((function(t,e){return i(e,(function(e){e=ni(e),_e(t,e,ea(t[e],t))})),t})),Ga=gr(),Va=gr(!0),$a=An((function(t,e){return function(n){return cn(n,t,e)}})),Xa=An((function(t,e){return function(n){return cn(t,n,e)}})),Ya=br(l),Ka=br(s),Qa=br(d),Ja=xr(),Za=xr(!0),tu=yr((function(t,e){return t+e}),0),eu=Sr("ceil"),nu=yr((function(t,e){return t/e}),1),ru=Sr("floor"),iu=yr((function(t,e){return t*e}),1),ou=Sr("round"),su=yr((function(t,e){return t-e}),0);return ee.after=function(t,e){if("function"!=typeof e)throw new mo(H);return t=Fi(t),function(){if(--t<1)return e.apply(this,arguments)}},ee.ary=bi,ee.assign=ba,ee.assignIn=wa,ee.assignInWith=_a,ee.assignWith=xa,ee.at=Ca,ee.before=wi,ee.bind=ea,ee.bindAll=Ua,ee.bindKey=na,ee.castArray=function(){var t;return arguments.length?la(t=arguments[0])?t:[t]:[]},ee.chain=di,ee.chunk=function(t,e,n){e=(n?Gr(t,e,n):e===F)?1:Yo(Fi(e),0);var r=null==t?0:t.length;if(!r||e<1)return[];for(var i=0,o=0,s=ao(zo(r/e));i<r;)s[o++]=Dn(t,i,i+=e);return s},ee.compact=function(t){for(var e=-1,n=null==t?0:t.length,r=0,i=[];++e<n;){var o=t[e];o&&(i[r++]=o)}return i},ee.concat=function(){var t=arguments.length;if(!t)return[];for(var e=ao(t-1),n=arguments[0],r=t;r--;)e[r-1]=arguments[r];return h(la(n)?ir(n):[n],Qe(e,1))},ee.cond=function(t){var e=null==t?0:t.length,r=Wr();return t=e?l(t,(function(t){if("function"!=typeof t[1])throw new mo(H);return[r(t[0]),t[1]]})):[],An((function(r){for(var i=-1;++i<e;){var o=t[i];if(n(o[0],this,r))return n(o[1],this,r)}}))},ee.conforms=function(t){return e=De(t,1),n=Vi(e),function(t){return Be(t,e,n)};var e,n},ee.constant=Zi,ee.countBy=Vs,ee.create=function(t,e){return t=ds(t),null==e?t:ye(t,e)},ee.curry=function t(e,n,r){return e=Er(e,8,F,F,F,F,F,n=r?F:n),e.placeholder=t.placeholder,e},ee.curryRight=function t(e,n,r){return e=Er(e,16,F,F,F,F,F,n=r?F:n),e.placeholder=t.placeholder,e},ee.debounce=_i,ee.defaults=ka,ee.defaultsDeep=Sa,ee.defer=Bo,ee.delay=ra,ee.difference=uo,ee.differenceBy=ns,ee.differenceWith=te,ee.drop=function(t,e,n){var r=null==t?0:t.length;return r?Dn(t,(e=n||e===F?1:Fi(e))<0?0:e,r):[]},ee.dropRight=function(t,e,n){var r=null==t?0:t.length;return r?Dn(t,0,(e=r-(e=n||e===F?1:Fi(e)))<0?0:e):[]},ee.dropRightWhile=function(t,e){return t&&t.length?Un(t,Wr(e,3),!0,!0):[]},ee.dropWhile=function(t,e){return t&&t.length?Un(t,Wr(e,3),!0):[]},ee.fill=function(t,e,n,r){var i=null==t?0:t.length;if(i){n&&"number"!=typeof n&&Gr(t,e,n)&&(n=0,r=i);var o=t,s=e,a=n,u=r;i=o.length;for((a=Fi(a))<0&&(a=i<-a?0:i+a),(u=u===F||i<u?i:Fi(u))<0&&(u+=i),u=u<a?0:Hi(u);a<u;)o[a++]=s;return o}return[]},ee.filter=function(t,e){return(la(t)?a:Ke)(t,Wr(e,3))},ee.flatMap=function(t,e){return Qe(yi(t,e),1)},ee.flatMapDeep=function(t,e){return Qe(yi(t,e),1/0)},ee.flatMapDepth=function(t,e,n){return n=n===F?1:Fi(n),Qe(yi(t,e),n)},ee.flatten=ai,ee.flattenDeep=function(t){return null!=t&&t.length?Qe(t,1/0):[]},ee.flattenDepth=function(t,e){return null!=t&&t.length?Qe(t,e=e===F?1:Fi(e)):[]},ee.flip=function(t){return Er(t,512)},ee.flow=Ga,ee.flowRight=Va,ee.fromPairs=function(t){for(var e=-1,n=null==t?0:t.length,r={};++e<n;){var i=t[e];r[i[0]]=i[1]}return r},ee.functions=function(t){return null==t?[]:tn(t,Vi(t))},ee.functionsIn=function(t){return null==t?[]:tn(t,$i(t))},ee.groupBy=Ys,ee.initial=function(t){return null!=t&&t.length?Dn(t,0,-1):[]},ee.intersection=wo,ee.intersectionBy=qo,ee.intersectionWith=Ao,ee.invert=Oa,ee.invertBy=Ea,ee.invokeMap=Ks,ee.iteratee=eo,ee.keyBy=Qs,ee.keys=Vi,ee.keysIn=$i,ee.map=yi,ee.mapKeys=function(t,e){var n={};return e=Wr(e,3),Je(t,(function(t,r,i){_e(n,e(t,r,i),t)})),n},ee.mapValues=function(t,e){var n={};return e=Wr(e,3),Je(t,(function(t,r,i){_e(n,r,e(t,r,i))})),n},ee.matches=function(t){return bn(De(t,1))},ee.matchesProperty=function(t,e){return wn(t,De(e,1))},ee.memoize=xi,ee.merge=Aa,ee.mergeWith=Ia,ee.method=$a,ee.methodOf=Xa,ee.mixin=no,ee.negate=Ci,ee.nthArg=function(t){return t=Fi(t),An((function(e){return xn(e,t)}))},ee.omit=ja,ee.omitBy=function(t,e){return Xi(t,Ci(Wr(e)))},ee.once=function(t){return wi(2,t)},ee.orderBy=function(t,e,n,r){return null==t?[]:Cn(t,e=la(e)?e:null==e?[]:[e],n=la(n=r?F:n)?n:null==n?[]:[n])},ee.over=Ya,ee.overArgs=Cs,ee.overEvery=Ka,ee.overSome=Qa,ee.partial=ia,ee.partialRight=oa,ee.partition=Js,ee.pick=Pa,ee.pickBy=Xi,ee.property=io,ee.propertyOf=function(t){return function(e){return null==t?F:en(t,e)}},ee.pull=Ho,ee.pullAll=li,ee.pullAllBy=function(t,e,n){return t&&t.length&&e&&e.length?Sn(t,e,Wr(n,2)):t},ee.pullAllWith=function(t,e,n){return t&&t.length&&e&&e.length?Sn(t,e,F,n):t},ee.pullAt=Rs,ee.range=Ja,ee.rangeRight=Za,ee.rearg=sa,ee.reject=function(t,e){return(la(t)?a:Ke)(t,Ci(Wr(e,3)))},ee.remove=function(t,e){var n=[];if(t&&t.length){var r=-1,i=[],o=t.length;for(e=Wr(e,3);++r<o;){var s=t[r];e(s,r,t)&&(n.push(s),i.push(r))}On(t,i)}return n},ee.rest=function(t,e){if("function"!=typeof t)throw new mo(H);return An(t,e=e===F?e:Fi(e))},ee.reverse=hi,ee.sampleSize=function(t,e,n){return e=(n?Gr(t,e,n):e===F)?1:Fi(e),(la(t)?fe:jn)(t,e)},ee.set=function(t,e,n){return null==t?t:Pn(t,e,n)},ee.setWith=function(t,e,n,r){return r="function"==typeof r?r:F,null==t?t:Pn(t,e,n,r)},ee.shuffle=function(t){return(la(t)?pe:Nn)(t)},ee.slice=function(t,e,n){var r=null==t?0:t.length;return r?(n=n&&"number"!=typeof n&&Gr(t,e,n)?(e=0,r):(e=null==e?0:Fi(e),n===F?r:Fi(n)),Dn(t,e,n)):[]},ee.sortBy=Zs,ee.sortedUniq=function(t){return t&&t.length?Ln(t):[]},ee.sortedUniqBy=function(t,e){return t&&t.length?Ln(t,Wr(e,2)):[]},ee.split=function(t,e,n){return n&&"number"!=typeof n&&Gr(t,e,n)&&(e=n=F),(n=n===F?G:n>>>0)?(t=zi(t))&&("string"==typeof e||null!=e&&!da(e))&&!(e=Hn(e))&&P(t)?Qn(L(t),0,n):t.split(e,n):[]},ee.spread=function(t,e){if("function"!=typeof t)throw new mo(H);return e=null==e?0:Yo(Fi(e),0),An((function(r){var i=r[e];r=Qn(r,0,e);return i&&h(r,i),n(t,this,r)}))},ee.tail=function(t){var e=null==t?0:t.length;return e?Dn(t,1,e):[]},ee.take=function(t,e,n){return t&&t.length?Dn(t,0,(e=n||e===F?1:Fi(e))<0?0:e):[]},ee.takeRight=function(t,e,n){var r=null==t?0:t.length;return r?Dn(t,(e=r-(e=n||e===F?1:Fi(e)))<0?0:e,r):[]},ee.takeRightWhile=function(t,e){return t&&t.length?Un(t,Wr(e,3),!1,!0):[]},ee.takeWhile=function(t,e){return t&&t.length?Un(t,Wr(e,3)):[]},ee.tap=function(t,e){return e(t),t},ee.throttle=function(t,e,n){var r=!0,i=!0;if("function"!=typeof t)throw new mo(H);return ji(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),_i(t,e,{leading:r,maxWait:e,trailing:i})},ee.thru=gi,ee.toArray=Wi,ee.toPairs=Na,ee.toPairsIn=Da,ee.toPath=function(t){return la(t)?l(t,ni):Mi(t)?[t]:ir(Ds(zi(t)))},ee.toPlainObject=qi,ee.transform=function(t,e,n){var r,o=la(t),s=o||fa(t)||ma(t);return e=Wr(e,4),null==n&&(r=t&&t.constructor,n=s?o?new r:[]:ji(t)&&Ti(r)?ds(Po(t)):{}),(s?i:Je)(t,(function(t,r,i){return e(n,t,r,i)})),n},ee.unary=function(t){return bi(t,1)},ee.union=Ms,ee.unionBy=Ws,ee.unionWith=Ls,ee.uniq=function(t){return t&&t.length?Bn(t):[]},ee.uniqBy=function(t,e){return t&&t.length?Bn(t,Wr(e,2)):[]},ee.uniqWith=function(t,e){return e="function"==typeof e?e:F,t&&t.length?Bn(t,F,e):[]},ee.unset=function(t,e){return null==t||qn(t,e)},ee.unzip=fi,ee.unzipWith=pi,ee.update=function(t,e,n){return null==t?t:zn(t,e,Yn(n))},ee.updateWith=function(t,e,n,r){return r="function"==typeof r?r:F,null==t?t:zn(t,e,Yn(n),r)},ee.values=Yi,ee.valuesIn=function(t){return null==t?[]:E(t,$i(t))},ee.without=Fs,ee.words=Ji,ee.wrap=function(t,e){return ia(Yn(e),t)},ee.xor=Hs,ee.xorBy=Bs,ee.xorWith=qs,ee.zip=zs,ee.zipObject=function(t,e){return $n(t||[],e||[],ge)},ee.zipObjectDeep=function(t,e){return $n(t||[],e||[],Pn)},ee.zipWith=Us,ee.entries=Na,ee.entriesIn=Da,ee.extend=wa,ee.extendWith=_a,no(ee,ee),ee.add=tu,ee.attempt=za,ee.camelCase=Ra,ee.capitalize=Ki,ee.ceil=eu,ee.clamp=function(t,e,n){return n===F&&(n=e,e=F),n!==F&&(n=(n=Bi(n))==n?n:0),e!==F&&(e=(e=Bi(e))==e?e:0),Ae(Bi(t),e,n)},ee.clone=function(t){return De(t,4)},ee.cloneDeep=function(t){return De(t,5)},ee.cloneDeepWith=function(t,e){return De(t,5,e="function"==typeof e?e:F)},ee.cloneWith=function(t,e){return De(t,4,e="function"==typeof e?e:F)},ee.conformsTo=function(t,e){return null==e||Be(t,e,Vi(e))},ee.deburr=Qi,ee.defaultTo=function(t,e){return null==t||t!=t?e:t},ee.divide=nu,ee.endsWith=function(t,e,n){t=zi(t),e=Hn(e);var r=t.length;r=n=n===F?r:Ae(Fi(n),0,r);return 0<=(n-=e.length)&&t.slice(n,r)==e},ee.eq=ki,ee.escape=function(t){return(t=zi(t))&&Ot.test(t)?t.replace(kt,ze):t},ee.escapeRegExp=function(t){return(t=zi(t))&&Rt.test(t)?t.replace(Dt,"\\$&"):t},ee.every=function(t,e,n){return(la(t)?s:Xe)(t,Wr(e=n&&Gr(t,e,n)?F:e,3))},ee.find=$s,ee.findIndex=oi,ee.findKey=function(t,e){return g(t,Wr(e,3),Je)},ee.findLast=Xs,ee.findLastIndex=si,ee.findLastKey=function(t,e){return g(t,Wr(e,3),Ze)},ee.floor=ru,ee.forEach=mi,ee.forEachRight=vi,ee.forIn=function(t,e){return null==t?t:ws(t,Wr(e,3),$i)},ee.forInRight=function(t,e){return null==t?t:_s(t,Wr(e,3),$i)},ee.forOwn=function(t,e){return t&&Je(t,Wr(e,3))},ee.forOwnRight=function(t,e){return t&&Ze(t,Wr(e,3))},ee.get=Ui,ee.gt=aa,ee.gte=ua,ee.has=function(t,e){return null!=t&&Br(t,e,sn)},ee.hasIn=Gi,ee.head=ui,ee.identity=to,ee.includes=function(t,e,n,r){return t=Si(t)?t:Yi(t),n=n&&!r?Fi(n):0,r=t.length,n<0&&(n=Yo(r+n,0)),Ri(t)?n<=r&&-1<t.indexOf(e,n):!!r&&-1<v(t,e,n)},ee.indexOf=function(t,e,n){var r=null==t?0:t.length;return r?v(t,e,t=(t=null==n?0:Fi(n))<0?Yo(r+t,0):t):-1},ee.inRange=function(t,e,n){return e=Li(e),n===F?(n=e,e=0):n=Li(n),(t=t=Bi(t))>=Ko(e,n)&&t<Yo(e,n)},ee.invoke=Ta,ee.isArguments=ca,ee.isArray=la,ee.isArrayBuffer=ha,ee.isArrayLike=Si,ee.isArrayLikeObject=Oi,ee.isBoolean=function(t){return!0===t||!1===t||Pi(t)&&rn(t)==Y},ee.isBuffer=fa,ee.isDate=yo,ee.isElement=function(t){return Pi(t)&&1===t.nodeType&&!Di(t)},ee.isEmpty=function(t){if(null!=t){if(Si(t)&&(la(t)||"string"==typeof t||"function"==typeof t.splice||fa(t)||ma(t)||ca(t)))return!t.length;var e,n=As(t);if(n==tt||n==ot)return!t.size;if(Xr(t))return!gn(t).length;for(e in t)if(xo.call(t,e))return!1}return!0},ee.isEqual=function(t,e){return hn(t,e)},ee.isEqualWith=function(t,e,n){var r=(n="function"==typeof n?n:F)?n(t,e):F;return r===F?hn(t,e,F,n):!!r},ee.isError=Ei,ee.isFinite=function(t){return"number"==typeof t&&Vo(t)},ee.isFunction=Ti,ee.isInteger=Ai,ee.isLength=Ii,ee.isMap=pa,ee.isMatch=function(t,e){return t===e||fn(t,e,Fr(e))},ee.isMatchWith=function(t,e,n){return n="function"==typeof n?n:F,fn(t,e,Fr(e),n)},ee.isNaN=function(t){return Ni(t)&&t!=+t},ee.isNative=function(t){if(Is(t))throw new co("Unsupported core-js use. Try https://npms.io/search?q=ponyfill.");return pn(t)},ee.isNil=function(t){return null==t},ee.isNull=function(t){return null===t},ee.isNumber=Ni,ee.isObject=ji,ee.isObjectLike=Pi,ee.isPlainObject=Di,ee.isRegExp=da,ee.isSafeInteger=function(t){return Ai(t)&&-z<=t&&t<=z},ee.isSet=ga,ee.isString=Ri,ee.isSymbol=Mi,ee.isTypedArray=ma,ee.isUndefined=function(t){return t===F},ee.isWeakMap=function(t){return Pi(t)&&As(t)==ut},ee.isWeakSet=function(t){return Pi(t)&&"[object WeakSet]"==rn(t)},ee.join=function(t,e){return null==t?"":$o.call(t,e)},ee.kebabCase=Ma,ee.last=ci,ee.lastIndexOf=function(t,e,n){var r=null==t?0:t.length;if(r){var i=r;if(n!==F&&(i=(i=Fi(n))<0?Yo(r+i,0):Ko(i,r-1)),e==e){var o=t,s=e;n=i;for(var a=n+1;a--;)if(o[a]===s)return a;return a}return m(t,b,i,!0)}return-1},ee.lowerCase=Wa,ee.lowerFirst=La,ee.lt=va,ee.lte=ya,ee.max=function(t){return t&&t.length?Ye(t,to,on):F},ee.maxBy=function(t,e){return t&&t.length?Ye(t,Wr(e,2),on):F},ee.mean=function(t){return w(t,to)},ee.meanBy=function(t,e){return w(t,Wr(e,2))},ee.min=function(t){return t&&t.length?Ye(t,to,vn):F},ee.minBy=function(t,e){return t&&t.length?Ye(t,Wr(e,2),vn):F},ee.stubArray=oo,ee.stubFalse=so,ee.stubObject=function(){return{}},ee.stubString=function(){return""},ee.stubTrue=function(){return!0},ee.multiply=iu,ee.nth=function(t,e){return t&&t.length?xn(t,Fi(e)):F},ee.noConflict=function(){return Pe._===this&&(Pe._=Eo),this},ee.noop=ro,ee.now=ta,ee.pad=function(t,e,n){t=zi(t);var r=(e=Fi(e))?W(t):0;return!e||e<=r?t:wr(Uo(e=(e-r)/2),n)+t+wr(zo(e),n)},ee.padEnd=function(t,e,n){t=zi(t);var r=(e=Fi(e))?W(t):0;return e&&r<e?t+wr(e-r,n):t},ee.padStart=function(t,e,n){t=zi(t);var r=(e=Fi(e))?W(t):0;return e&&r<e?wr(e-r,n)+t:t},ee.parseInt=function(t,e,n){return e=n||null==e?0:e&&+e,Jo(zi(t).replace(Wt,""),e||0)},ee.random=function(t,e,n){var r;return n&&"boolean"!=typeof n&&Gr(t,e,n)&&(e=n=F),n===F&&("boolean"==typeof e?(n=e,e=F):"boolean"==typeof t&&(n=t,t=F)),t===F&&e===F?(t=0,e=1):(t=Li(t),e===F?(e=t,t=0):e=Li(e)),e<t&&(r=t,t=e,e=r),n||t%1||e%1?(r=Zo(),Ko(t+r*(e-t+Ie("1e-"+((r+"").length-1))),e)):En(t,e)},ee.reduce=function(t,e,n){var r=la(t)?f:C,i=arguments.length<3;return r(t,Wr(e,4),n,i,ys)},ee.reduceRight=function(t,e,n){var r=la(t)?p:C,i=arguments.length<3;return r(t,Wr(e,4),n,i,bs)},ee.repeat=function(t,e,n){return e=(n?Gr(t,e,n):e===F)?1:Fi(e),Tn(zi(t),e)},ee.replace=function(){var t=arguments,e=zi(t[0]);return t.length<3?e:e.replace(t[1],t[2])},ee.result=function(t,e,n){var r=-1,i=(e=Kn(e,t)).length;for(i||(i=1,t=F);++r<i;){var o=null==t?F:t[ni(e[r])];o===F&&(r=i,o=n),t=Ti(o)?o.call(t):o}return t},ee.round=ou,ee.runInContext=x,ee.sample=function(t){return(la(t)?he:In)(t)},ee.size=function(t){var e;return null==t?0:Si(t)?Ri(t)?W(t):t.length:(e=As(t))==tt||e==ot?t.size:gn(t).length},ee.snakeCase=Fa,ee.some=function(t,e,n){return(la(t)?d:Rn)(t,Wr(e=n&&Gr(t,e,n)?F:e,3))},ee.sortedIndex=function(t,e){return Mn(t,e)},ee.sortedIndexBy=function(t,e,n){return Wn(t,e,Wr(n,2))},ee.sortedIndexOf=function(t,e){var n=null==t?0:t.length;if(n){var r=Mn(t,e);if(r<n&&ki(t[r],e))return r}return-1},ee.sortedLastIndex=function(t,e){return Mn(t,e,!0)},ee.sortedLastIndexBy=function(t,e,n){return Wn(t,e,Wr(n,2),!0)},ee.sortedLastIndexOf=function(t,e){if(null!=t&&t.length){var n=Mn(t,e,!0)-1;if(ki(t[n],e))return n}return-1},ee.startCase=Ha,ee.startsWith=function(t,e,n){return t=zi(t),n=null==n?0:Ae(Fi(n),0,t.length),e=Hn(e),t.slice(n,n+e.length)==e},ee.subtract=su,ee.sum=function(t){return t&&t.length?k(t,to):0},ee.sumBy=function(t,e){return t&&t.length?k(t,Wr(e,2)):0},ee.template=function(t,e,n){var r=ee.templateSettings;n&&Gr(t,e,n)&&(e=F),t=zi(t),e=_a({},e,r,Tr);var i,o,s=Vi(n=_a({},e.imports,r.imports,Tr)),a=E(n,s),u=0,c=(r=e.interpolate||Jt,"__p += '"),l=(n=po((e.escape||Jt).source+"|"+r.source+"|"+(r===At?Ut:Jt).source+"|"+(e.evaluate||Jt).source+"|$","g"),"//# sourceURL="+("sourceURL"in e?e.sourceURL:"lodash.templateSources["+ ++Oe+"]")+"\n");if(t.replace(n,(function(e,n,r,s,a,l){return r=r||s,c+=t.slice(u,l).replace(Zt,j),n&&(i=!0,c+="' +\n__e("+n+") +\n'"),a&&(o=!0,c+="';\n"+a+";\n__p += '"),r&&(c+="' +\n((__t = ("+r+")) == null ? '' : __t) +\n'"),u=l+e.length,e})),c+="';\n",(r=e.variable)||(c="with (obj) {\n"+c+"\n}\n"),c=(o?c.replace(wt,""):c).replace(_t,"$1").replace(xt,"$1;"),c="function("+(r||"obj")+") {\n"+(r?"":"obj || (obj = {});\n")+"var __t, __p = ''"+(i?", __e = _.escape":"")+(o?", __j = Array.prototype.join;\nfunction print() { __p += __j.call(arguments, '') }\n":";\n")+c+"return __p\n}",(n=za((function(){return lo(s,l+"return "+c).apply(F,a)}))).source=c,Ei(n))throw n;return n},ee.times=function(t,e){if((t=Fi(t))<1||z<t)return[];var n=G,r=Ko(t,G);for(e=Wr(e),t-=G,r=S(r,e);++n<t;)e(n);return r},ee.toFinite=Li,ee.toInteger=Fi,ee.toLength=Hi,ee.toLower=function(t){return zi(t).toLowerCase()},ee.toNumber=Bi,ee.toSafeInteger=function(t){return t?Ae(Fi(t),-z,z):0===t?t:0},ee.toString=zi,ee.toUpper=function(t){return zi(t).toUpperCase()},ee.trim=function(t,e,n){return(t=zi(t))&&(n||e===F)?t.replace(Mt,""):t&&(e=Hn(e))?Qn(n=L(t),A(n,e=L(e)),I(n,e)+1).join(""):t},ee.trimEnd=function(t,e,n){return(t=zi(t))&&(n||e===F)?t.replace(Lt,""):t&&(e=Hn(e))?Qn(n=L(t),0,I(n,L(e))+1).join(""):t},ee.trimStart=function(t,e,n){return(t=zi(t))&&(n||e===F)?t.replace(Wt,""):t&&(e=Hn(e))?Qn(n=L(t),A(n,L(e))).join(""):t},ee.truncate=function(t,e){var n,r=30,i="...";ji(e)&&(n="separator"in e?e.separator:n,r="length"in e?Fi(e.length):r,i="omission"in e?Hn(e.omission):i),e=(t=zi(t)).length;if((e=P(t)?(o=L(t)).length:e)<=r)return t;if((e=r-W(i))<1)return i;var o;r=o?Qn(o,0,e).join(""):t.slice(0,e);if(n!==F)if(o&&(e+=r.length-e),da(n)){if(t.slice(e).search(n)){var s,a=r;for((n=n.global?n:po(n.source,zi(Gt.exec(n))+"g")).lastIndex=0;s=n.exec(a);)var u=s.index;r=r.slice(0,u===F?e:u)}}else t.indexOf(Hn(n),e)!=e&&-1<(o=r.lastIndexOf(n))&&(r=r.slice(0,o));return r+i},ee.unescape=function(t){return(t=zi(t))&&St.test(t)?t.replace(Ct,Ue):t},ee.uniqueId=function(t){var e=++Co;return zi(t)+e},ee.upperCase=Ba,ee.upperFirst=qa,ee.each=mi,ee.eachRight=vi,ee.first=ui,no(ee,(vs={},Je(ee,(function(t,e){xo.call(ee.prototype,e)||(vs[e]=t)})),vs),{chain:!1}),ee.VERSION="4.17.4",i(["bind","bindKey","curry","curryRight","partial","partialRight"],(function(t){ee[t].placeholder=ee})),i(["drop","take"],(function(t,e){ie.prototype[t]=function(n){n=n===F?1:Yo(Fi(n),0);var r=this.__filtered__&&!e?new ie(this):this.clone();return r.__filtered__?r.__takeCount__=Ko(n,r.__takeCount__):r.__views__.push({size:Ko(n,G),type:t+(r.__dir__<0?"Right":"")}),r},ie.prototype[t+"Right"]=function(e){return this.reverse()[t](e).reverse()}})),i(["filter","map","takeWhile"],(function(t,e){var n=e+1,r=1==n||3==n;ie.prototype[t]=function(t){var e=this.clone();return e.__iteratees__.push({iteratee:Wr(t,3),type:n}),e.__filtered__=e.__filtered__||r,e}})),i(["head","last"],(function(t,e){var n="take"+(e?"Right":"");ie.prototype[t]=function(){return this[n](1).value()[0]}})),i(["initial","tail"],(function(t,e){var n="drop"+(e?"":"Right");ie.prototype[t]=function(){return this.__filtered__?new ie(this):this[n](1)}})),ie.prototype.compact=function(){return this.filter(to)},ie.prototype.find=function(t){return this.filter(t).head()},ie.prototype.findLast=function(t){return this.reverse().find(t)},ie.prototype.invokeMap=An((function(t,e){return"function"==typeof t?new ie(this):this.map((function(n){return cn(n,t,e)}))})),ie.prototype.reject=function(t){return this.filter(Ci(Wr(t)))},ie.prototype.slice=function(t,e){t=Fi(t);var n=this;return n.__filtered__&&(0<t||e<0)?new ie(n):(t<0?n=n.takeRight(-t):t&&(n=n.drop(t)),n=e!==F?(e=Fi(e))<0?n.dropRight(-e):n.take(e-t):n)},ie.prototype.takeRightWhile=function(t){return this.reverse().takeWhile(t).reverse()},ie.prototype.toArray=function(){return this.take(G)},Je(ie.prototype,(function(t,e){var n=/^(?:filter|find|map|reject)|While$/.test(e),r=/^(?:head|last)$/.test(e),i=ee[r?"take"+("last"==e?"Right":""):e],o=r||/^find/.test(e);i&&(ee.prototype[e]=function(){function e(t){return t=i.apply(ee,h([t],u)),r&&p?t[0]:t}var s,a=this.__wrapped__,u=r?[1]:arguments,c=a instanceof ie,l=u[0],f=c||la(a),p=(f&&n&&"function"==typeof l&&1!=l.length&&(c=f=!1),this.__chain__),d=(l=!!this.__actions__.length,o&&!p);c=c&&!l;return!o&&f?(a=c?a:new ie(this),(s=t.apply(a,u)).__actions__.push({func:gi,args:[e],thisArg:F}),new re(s,p)):d&&c?t.apply(this,u):(s=this.thru(e),d?r?s.value()[0]:s.value():s)})})),i(["pop","push","shift","sort","splice","unshift"],(function(t){var e=vo[t],n=/^(?:push|sort|unshift)$/.test(t)?"tap":"thru",r=/^(?:pop|shift)$/.test(t);ee.prototype[t]=function(){var t,i=arguments;return r&&!this.__chain__?(t=this.value(),e.apply(la(t)?t:[],i)):this[n]((function(t){return e.apply(la(t)?t:[],i)}))}})),Je(ie.prototype,(function(t,e){var n,r=ee[e];r&&(n=r.name+"",(ss[n]||(ss[n]=[])).push({name:e,func:r}))})),ss[mr(F,2).name]=[{name:"wrapper",func:F}],ie.prototype.clone=function(){var t=new ie(this.__wrapped__);return t.__actions__=ir(this.__actions__),t.__dir__=this.__dir__,t.__filtered__=this.__filtered__,t.__iteratees__=ir(this.__iteratees__),t.__takeCount__=this.__takeCount__,t.__views__=ir(this.__views__),t},ie.prototype.reverse=function(){var t;return this.__filtered__?((t=new ie(this)).__dir__=-1,t.__filtered__=!0):(t=this.clone()).__dir__*=-1,t},ie.prototype.value=function(){var t=this.__wrapped__.value(),e=this.__dir__,n=la(t),r=e<0,i=n?t.length:0,o=function(t,e,n){for(var r=-1,i=n.length;++r<i;){var o=n[r],s=o.size;switch(o.type){case"drop":t+=s;break;case"dropRight":e-=s;break;case"take":e=Ko(e,t+s);break;case"takeRight":t=Yo(t,e-s)}}return{start:t,end:e}}(0,i,this.__views__),s=o.start,a=(o=o.end)-s,u=r?o:s-1,c=this.__iteratees__,l=c.length,h=0,f=Ko(a,this.__takeCount__);if(!n||!r&&i==a&&f==a)return Gn(t,this.__actions__);var p=[];t:for(;a--&&h<f;){for(var d=-1,g=t[u+=e];++d<l;){var m=c[d],v=m.iteratee;m=m.type,v=v(g);if(2==m)g=v;else if(!v){if(1==m)continue t;break t}}p[h++]=g}return p},ee.prototype.at=Gs,ee.prototype.chain=function(){return di(this)},ee.prototype.commit=function(){return new re(this.value(),this.__chain__)},ee.prototype.next=function(){this.__values__===F&&(this.__values__=Wi(this.value()));var t=this.__index__>=this.__values__.length;return{done:t,value:t?F:this.__values__[this.__index__++]}},ee.prototype.plant=function(t){for(var e,n=this;n instanceof ne;){var r=ii(n),i=(r.__index__=0,r.__values__=F,e?i.__wrapped__=r:e=r,r);n=n.__wrapped__}return i.__wrapped__=t,e},ee.prototype.reverse=function(){var t=this.__wrapped__;return t instanceof ie?((t=(t=this.__actions__.length?new ie(this):t).reverse()).__actions__.push({func:gi,args:[hi],thisArg:F}),new re(t,this.__chain__)):this.thru(hi)},ee.prototype.toJSON=ee.prototype.valueOf=ee.prototype.value=function(){return Gn(this.__wrapped__,this.__actions__)},ee.prototype.first=ee.prototype.head,Wo&&(ee.prototype[Wo]=function(){return this}),ee}();"function"==typeof define&&"object"==typeof define.amd&&define.amd?(Pe._=Ge,define((function(){return Ge}))):ae?((ae.exports=Ge)._=Ge,pe._=Ge):Pe._=Ge}.call(this),function(t,e){"object"==typeof exports?module.exports=e():"function"==typeof define&&define.amd?define(e):t.jStat=e()}(this,(function(){var t,e,n,r,i,o=function(t,e){var n=Array.prototype.concat,r=Array.prototype.slice,i=Object.prototype.toString;function o(e,n){return e=n<e?e:n,t.pow(10,17-~~(t.log(0<e?e:-e)*t.LOG10E))}var s=Array.isArray||function(t){return"[object Array]"===i.call(t)};function a(t){return"[object Function]"===i.call(t)}function u(t){return"number"==typeof t&&t-t==0}function c(){return new c._init(arguments)}function l(){return 0}function h(){return 1}function f(t,e){return t===e?1:0}function p(t,n,r,i){var o,s=[],a=t.length;if(n===e&&r===e&&i===e)return c.copy(t);if(i=i||1,(n=0<=(n=n||0)?n:a+n)===(r=0<=(r=r||t.length)?r:a+r)||0===i)return[];if(n<r&&i<0)return[];if(r<n&&0<i)return[];if(0<i)for(o=n;o<r;o+=i)s.push(t[o]);else for(o=n;r<o;o+=i)s.push(t[o]);return s}c.fn=c.prototype,(c._init=function(t){if(s(t[0]))if(s(t[0][0])){a(t[1])&&(t[0]=c.map(t[0],t[1]));for(var e=0;e<t[0].length;e++)this[e]=t[0][e];this.length=t[0].length}else this[0]=a(t[1])?c.map(t[0],t[1]):t[0],this.length=1;else{if(u(t[0]))this[0]=c.seq.apply(null,t);else{if(t[0]instanceof c)return c(t[0].toArray());this[0]=[]}this.length=1}return this}).prototype=c.prototype,(c._init.constructor=c).utils={calcRdx:o,isArray:s,isFunction:a,isNumber:u,toVector:function(t){return n.apply([],t)}},c._random_fn=t.random,c.setRandom=function(t){if("function"!=typeof t)throw new TypeError("fn is not a function");c._random_fn=t},c.extend=function(t){var e,n;if(1===arguments.length){for(n in t)c[n]=t[n];return this}for(e=1;e<arguments.length;e++)for(n in arguments[e])t[n]=arguments[e][n];return t},c.rows=function(t){return t.length||1},c.cols=function(t){return t[0].length||1},c.dimensions=function(t){return{rows:c.rows(t),cols:c.cols(t)}},c.row=function(t,e){return s(e)?e.map((function(e){return c.row(t,e)})):t[e]},c.rowa=function(t,e){return c.row(t,e)},c.col=function(t,e){var n;if(s(e))return n=c.arange(t.length).map((function(){return new Array(e.length)})),e.forEach((function(e,r){c.arange(t.length).forEach((function(i){n[i][r]=t[i][e]}))})),n;for(var r=new Array(t.length),i=0;i<t.length;i++)r[i]=[t[i][e]];return r},c.cola=function(t,e){return c.col(t,e).map((function(t){return t[0]}))},c.diag=function(t){for(var e=c.rows(t),n=new Array(e),r=0;r<e;r++)n[r]=[t[r][r]];return n},c.antidiag=function(t){for(var e=c.rows(t)-1,n=new Array(e),r=0;0<=e;e--,r++)n[r]=[t[r][e]];return n},c.transpose=function(t){for(var e,n,r=[],i=(t=s(t[0])?t:[t]).length,o=t[0].length,a=0;a<o;a++){for(e=new Array(i),n=0;n<i;n++)e[n]=t[n][a];r.push(e)}return 1===r.length?r[0]:r},c.map=function(t,e,n){for(var r,i=(t=s(t[0])?t:[t]).length,o=t[0].length,a=n?t:new Array(i),u=0;u<i;u++)for(a[u]||(a[u]=new Array(o)),r=0;r<o;r++)a[u][r]=e(t[u][r],u,r);return 1===a.length?a[0]:a},c.cumreduce=function(t,e,n){for(var r,i=(t=s(t[0])?t:[t]).length,o=t[0].length,a=n?t:new Array(i),u=0;u<i;u++)for(a[u]||(a[u]=new Array(o)),0<o&&(a[u][0]=t[u][0]),r=1;r<o;r++)a[u][r]=e(a[u][r-1],t[u][r]);return 1===a.length?a[0]:a},c.alter=function(t,e){return c.map(t,e,!0)},c.create=function(t,e,n){var r,i,o=new Array(t);for(a(e)&&(n=e,e=t),r=0;r<t;r++)for(o[r]=new Array(e),i=0;i<e;i++)o[r][i]=n(r,i);return o},c.zeros=function(t,e){return u(e)||(e=t),c.create(t,e,l)},c.ones=function(t,e){return u(e)||(e=t),c.create(t,e,h)},c.rand=function(t,e){return u(e)||(e=t),c.create(t,e,c._random_fn)},c.identity=function(t,e){return u(e)||(e=t),c.create(t,e,f)},c.symmetric=function(t){var e,n,r=t.length;if(t.length!==t[0].length)return!1;for(e=0;e<r;e++)for(n=0;n<r;n++)if(t[n][e]!==t[e][n])return!1;return!0},c.clear=function(t){return c.alter(t,l)},c.seq=function(t,e,n,r){a(r)||(r=!1);for(var i=[],s=o(t,e),u=(e*s-t*s)/((n-1)*s),c=t,l=0;c<=e&&l<n;c=(t*s+u*s*++l)/s)i.push(r?r(c,l):c);return i},c.arange=function(t,n,r){var i,o=[];if(r=r||1,n===e&&(n=t,t=0),t===n||0===r)return[];if(t<n&&r<0)return[];if(n<t&&0<r)return[];if(0<r)for(i=t;i<n;i+=r)o.push(i);else for(i=t;n<i;i+=r)o.push(i);return o},c.slice=function(t,e){var n,r;return u((e=e||{}).row)?u(e.col)?t[e.row][e.col]:p(c.rowa(t,e.row),(n=e.col||{}).start,n.end,n.step):u(e.col)?p(c.cola(t,e.col),(r=e.row||{}).start,r.end,r.step):(r=e.row||{},n=e.col||{},p(t,r.start,r.end,r.step).map((function(t){return p(t,n.start,n.end,n.step)})))},c.sliceAssign=function(n,r,i){var o,s;if(u(r.row)){if(u(r.col))return n[r.row][r.col]=i;r.col=r.col||{},r.col.start=r.col.start||0,r.col.end=r.col.end||n[0].length,r.col.step=r.col.step||1;var a=c.arange(r.col.start,t.min(n.length,r.col.end),r.col.step),l=r.row;a.forEach((function(t,e){n[l][t]=i[e]}))}else u(r.col)?(r.row=r.row||{},r.row.start=r.row.start||0,r.row.end=r.row.end||n.length,r.row.step=r.row.step||1,o=c.arange(r.row.start,t.min(n[0].length,r.row.end),r.row.step),s=r.col,o.forEach((function(t,e){n[t][s]=i[e]}))):(i[0].length===e&&(i=[i]),r.row.start=r.row.start||0,r.row.end=r.row.end||n.length,r.row.step=r.row.step||1,r.col.start=r.col.start||0,r.col.end=r.col.end||n[0].length,r.col.step=r.col.step||1,o=c.arange(r.row.start,t.min(n.length,r.row.end),r.row.step),a=c.arange(r.col.start,t.min(n[0].length,r.col.end),r.col.step),o.forEach((function(t,e){a.forEach((function(r,o){n[t][r]=i[e][o]}))})));return n},c.diagonal=function(t){var e=c.zeros(t.length,t.length);return t.forEach((function(t,n){e[n][n]=t})),e},c.copy=function(t){return t.map((function(t){return u(t)?t:t.map((function(t){return t}))}))};for(var d=c.prototype,g=(d.length=0,d.push=Array.prototype.push,d.sort=Array.prototype.sort,d.splice=Array.prototype.splice,d.slice=Array.prototype.slice,d.toArray=function(){return 1<this.length?r.call(this):r.call(this)[0]},d.map=function(t,e){return c(c.map(this,t,e))},d.cumreduce=function(t,e){return c(c.cumreduce(this,t,e))},d.alter=function(t){return c.alter(this,t),this},"transpose clear symmetric rows cols dimensions diag antidiag".split(" ")),m=0;m<g.length;m++)!function(t){d[t]=function(e){var n,r=this;return e?(setTimeout((function(){e.call(r,d[t].call(r))})),this):(n=c[t](this),s(n)?c(n):n)}}(g[m]);for(var v="row col".split(" "),y=0;y<v.length;y++)!function(t){d[t]=function(e,n){var r=this;return n?(setTimeout((function(){n.call(r,d[t].call(r,e))})),this):c(c[t](this,e))}}(v[y]);for(var b="create zeros ones rand identity".split(" "),w=0;w<b.length;w++)!function(t){d[t]=function(){return c(c[t].apply(null,arguments))}}(b[w]);return c}(Math),s=o,a=Math,u=s.utils.isFunction;function c(t,e){return t-e}function l(t,e,n){return a.max(e,a.min(t,n))}s.sum=function(t){for(var e=0,n=t.length;0<=--n;)e+=t[n];return e},s.sumsqrd=function(t){for(var e=0,n=t.length;0<=--n;)e+=t[n]*t[n];return e},s.sumsqerr=function(t){for(var e,n=s.mean(t),r=0,i=t.length;0<=--i;)r+=(e=t[i]-n)*e;return r},s.sumrow=function(t){for(var e=0,n=t.length;0<=--n;)e+=t[n];return e},s.product=function(t){for(var e=1,n=t.length;0<=--n;)e*=t[n];return e},s.min=function(t){for(var e=t[0],n=0;++n<t.length;)t[n]<e&&(e=t[n]);return e},s.max=function(t){for(var e=t[0],n=0;++n<t.length;)t[n]>e&&(e=t[n]);return e},s.unique=function(t){for(var e={},n=[],r=0;r<t.length;r++)e[t[r]]||(e[t[r]]=!0,n.push(t[r]));return n},s.mean=function(t){return s.sum(t)/t.length},s.meansqerr=function(t){return s.sumsqerr(t)/t.length},s.geomean=function(t){return a.pow(s.product(t),1/t.length)},s.median=function(t){var e=t.length;t=t.slice().sort(c);return 1&e?t[e/2|0]:(t[e/2-1]+t[e/2])/2},s.cumsum=function(t){return s.cumreduce(t,(function(t,e){return t+e}))},s.cumprod=function(t){return s.cumreduce(t,(function(t,e){return t*e}))},s.diff=function(t){for(var e=[],n=t.length,r=1;r<n;r++)e.push(t[r]-t[r-1]);return e},s.rank=function(t){for(var e=[],n={},r=0;r<t.length;r++)n[a=t[r]]?n[a]++:(n[a]=1,e.push(a));var i=e.sort(c),o={},s=1;for(r=0;r<i.length;r++){var a,u=n[a=i[r]],l=s;o[a]=(l+(s+u-1))/2,s+=u}return t.map((function(t){return o[t]}))},s.mode=function(t){for(var e=t.length,n=t.slice().sort(c),r=1,i=0,o=0,s=[],a=0;a<e;a++)n[a]===n[a+1]?r++:(i<r?(s=[n[a]],i=r,o=0):r===i&&(s.push(n[a]),o++),r=1);return 0===o?s[0]:s},s.range=function(t){return s.max(t)-s.min(t)},s.variance=function(t,e){return s.sumsqerr(t)/(t.length-(e?1:0))},s.pooledvariance=function(t){return t.reduce((function(t,e){return t+s.sumsqerr(e)}),0)/(t.reduce((function(t,e){return t+e.length}),0)-t.length)},s.deviation=function(t){for(var e=s.mean(t),n=t.length,r=new Array(n),i=0;i<n;i++)r[i]=t[i]-e;return r},s.stdev=function(t,e){return a.sqrt(s.variance(t,e))},s.pooledstdev=function(t){return a.sqrt(s.pooledvariance(t))},s.meandev=function(t){for(var e=s.mean(t),n=[],r=t.length-1;0<=r;r--)n.push(a.abs(t[r]-e));return s.mean(n)},s.meddev=function(t){for(var e=s.median(t),n=[],r=t.length-1;0<=r;r--)n.push(a.abs(t[r]-e));return s.median(n)},s.coeffvar=function(t){return s.stdev(t)/s.mean(t)},s.quartiles=function(t){var e=t.length;t=t.slice().sort(c);return[t[a.round(e/4)-1],t[a.round(e/2)-1],t[a.round(3*e/4)-1]]},s.quantiles=function(t,e,n,r){var i,o,s,u=t.slice().sort(c),h=[e.length],f=t.length;for(void 0===n&&(n=3/8),void 0===r&&(r=3/8),i=0;i<e.length;i++)s=e[i],o=a.floor(l(s=f*s+(n+s*(1-n-r)),1,f-1)),s=l(s-o,0,1),h[i]=(1-s)*u[o-1]+s*u[o];return h},s.percentile=function(t,e,n){return t=t.slice().sort(c),e=e*(t.length+(n?1:-1))+(n?0:1),n=parseInt(e),n+1<t.length?t[n-1]+(e-n)*(t[n]-t[n-1]):t[n-1]},s.percentileOfScore=function(t,e,n){for(var r,i=0,o=t.length,s="strict"===n,a=0;a<o;a++)r=t[a],(s&&r<e||!s&&r<=e)&&i++;return i/o},s.histogram=function(t,e){e=e||4;for(var n=s.min(t),r=(s.max(t)-n)/e,i=t.length,o=[],u=0;u<e;u++)o[u]=0;for(u=0;u<i;u++)o[a.min(a.floor((t[u]-n)/r),e-1)]+=1;return o},s.covariance=function(t,e){for(var n=s.mean(t),r=s.mean(e),i=t.length,o=new Array(i),a=0;a<i;a++)o[a]=(t[a]-n)*(e[a]-r);return s.sum(o)/(i-1)},s.corrcoeff=function(t,e){return s.covariance(t,e)/s.stdev(t,1)/s.stdev(e,1)},s.spearmancoeff=function(t,e){return t=s.rank(t),e=s.rank(e),s.corrcoeff(t,e)},s.stanMoment=function(t,e){for(var n=s.mean(t),r=s.stdev(t),i=t.length,o=0,u=0;u<i;u++)o+=a.pow((t[u]-n)/r,e);return o/t.length},s.skewness=function(t){return s.stanMoment(t,3)},s.kurtosis=function(t){return s.stanMoment(t,4)-3};for(var h=s.prototype,f="cumsum cumprod".split(" "),p=0;p<f.length;p++)!function(t){h[t]=function(e,n){var r=[],i=0,o=this;if(u(e)&&(n=e,e=!1),n)return setTimeout((function(){n.call(o,h[t].call(o,e))})),this;if(1<this.length){for(o=!0===e?this:this.transpose();i<o.length;i++)r[i]=s[t](o[i]);return r}return s[t](this[0],e)}}(f[p]);for(var d="sum sumsqrd sumsqerr sumrow product min max unique mean meansqerr geomean median diff rank mode range variance deviation stdev meandev meddev coeffvar quartiles histogram skewness kurtosis".split(" "),g=0;g<d.length;g++)!function(t){h[t]=function(e,n){var r=[],i=0,o=this;if(u(e)&&(n=e,e=!1),n)return setTimeout((function(){n.call(o,h[t].call(o,e))})),this;if(1<this.length){for("sumrow"!==t&&(o=!0===e?this:this.transpose());i<o.length;i++)r[i]=s[t](o[i]);return!0===e?s[t](s.utils.toVector(r)):r}return s[t](this[0],e)}}(d[g]);for(var m="quantiles percentileOfScore".split(" "),v=0;v<m.length;v++)!function(t){h[t]=function(){var e,n,r,i=[],o=0,a=this,c=Array.prototype.slice.call(arguments);if(u(c[c.length-1]))return e=c[c.length-1],n=c.slice(0,c.length-1),setTimeout((function(){e.call(a,h[t].apply(a,n))})),this;if(e=void 0,r=function(e){return s[t].apply(a,[e].concat(c))},1<this.length){for(a=a.transpose();o<a.length;o++)i[o]=r(a[o]);return i}return r(this[0])}}(m[v]);var y=o,b=Math;y.gammaln=function(t){var e,n=0,r=[76.18009172947146,-86.50532032941678,24.01409824083091,-1.231739572450155,.001208650973866179,-5395239384953e-18],i=1.000000000190015,o=(e=t)+5.5;for(o-=(t+.5)*b.log(o);n<6;n++)i+=r[n]/++e;return b.log(2.5066282746310007*i/t)-o},y.loggam=function(t){var e,n,r,i,o,s=[.08333333333333333,-.002777777777777778,.0007936507936507937,-.0005952380952380952,.0008417508417508418,-.001917526917526918,.00641025641025641,-.02955065359477124,.1796443723688307,-1.3924322169059],a=t,u=0;if(1==t||2==t)return 0;for(e=1/((a=t<=7?t+(u=b.floor(7-t)):a)*a),n=2*b.PI,i=s[9],o=8;0<=o;o--)i=i*e+s[o];if(r=i/a+.5*b.log(n)+(a-.5)*b.log(a)-a,t<=7)for(o=1;o<=u;o++)r-=b.log(a-1),--a;return r},y.gammafn=function(t){var e,n,r,i=[-1.716185138865495,24.76565080557592,-379.80425647094563,629.3311553128184,866.9662027904133,-31451.272968848367,-36144.413418691176,66456.14382024054],o=[-30.8402300119739,315.35062697960416,-1015.1563674902192,-3107.771671572311,22538.11842098015,4755.846277527881,-134659.9598649693,-115132.2596755535],s=!1,a=0,u=0,c=0,l=t;if(171.6243769536076<t)return 1/0;if(l<=0){if(!(r=l%1+36e-17))return 1/0;s=(1&l?-1:1)*b.PI/b.sin(b.PI*r),l=1-l}for(n=(t=l)<1?l++:(l-=a=(0|l)-1)-1,e=0;e<8;++e)c=(c+i[e])*n,u=u*n+o[e];if(r=c/u+1,t<l)r/=t;else if(l<t)for(e=0;e<a;++e)r*=l,l++;return s?s/r:r},y.gammap=function(t,e){return y.lowRegGamma(t,e)*y.gammafn(t)},y.lowRegGamma=function(t,e){var n,r=y.gammaln(t),i=t,o=1/t,s=o,a=e+1-t,u=1/1e-30,c=1/a,l=c,h=1,f=-~(8.5*b.log(1<=t?t:1/t)+.4*t+17);if(e<0||t<=0)return NaN;if(e<t+1){for(;h<=f;h++)o+=s*=e/++i;return o*b.exp(-e+t*b.log(e)-r)}for(;h<=f;h++)l*=(c=1/(c=(n=-h*(h-t))*c+(a+=2)))*(u=a+n/u);return 1-l*b.exp(-e+t*b.log(e)-r)},y.factorialln=function(t){return t<0?NaN:y.gammaln(t+1)},y.factorial=function(t){return t<0?NaN:y.gammafn(t+1)},y.combination=function(t,e){return 170<t||170<e?b.exp(y.combinationln(t,e)):y.factorial(t)/y.factorial(e)/y.factorial(t-e)},y.combinationln=function(t,e){return y.factorialln(t)-y.factorialln(e)-y.factorialln(t-e)},y.permutation=function(t,e){return y.factorial(t)/y.factorial(t-e)},y.betafn=function(t,e){if(!(t<=0||e<=0))return 170<t+e?b.exp(y.betaln(t,e)):y.gammafn(t)*y.gammafn(e)/y.gammafn(t+e)},y.betaln=function(t,e){return y.gammaln(t)+y.gammaln(e)-y.gammaln(t+e)},y.betacf=function(t,e,n){for(var r,i,o=1e-30,s=1,a=e+n,u=e+1,c=e-1,l=1,h=1-a*t/u,f=h=1/(h=b.abs(h)<o?o:h);s<=100&&(f=(f*=(h=1/(h=b.abs(h=1+(r=s*(n-s)*t/((c+(i=2*s))*(e+i)))*h)<o?o:h))*(l=b.abs(l=1+r/l)<o?o:l))*(i=(h=1/(h=b.abs(h=1+(r=-(e+s)*(a+s)*t/((e+i)*(u+i)))*h)<o?o:h))*(l=b.abs(l=1+r/l)<o?o:l)),!(b.abs(i-1)<3e-7));s++);return f},y.gammapinv=function(t,e){var n,r,i,o,s,a=0,u=e-1,c=y.gammaln(e);if(1<=t)return b.max(100,e+100*b.sqrt(e));if(t<=0)return 0;for(n=1<e?(o=b.log(u),s=b.exp(u*(o-1)-c),n=(2.30753+.27061*(r=b.sqrt(-2*b.log(t<.5?t:1-t))))/(1+r*(.99229+.04481*r))-r,b.max(.001,e*b.pow(1-1/(9*e)-(n=t<.5?-n:n)/(3*b.sqrt(e)),3))):t<(r=1-e*(.253+.12*e))?b.pow(t/r,1/e):1-b.log(1-(t-r)/(1-r));a<12;a++){if(n<=0)return 0;if((n-=r=(i=(y.lowRegGamma(e,n)-t)/(r=1<e?s*b.exp(-(n-u)+u*(b.log(n)-o)):b.exp(-n+u*b.log(n)-c)))/(1-.5*b.min(1,i*((e-1)/n-1))))<=0&&(n=.5*(n+r)),b.abs(r)<1e-8*n)break}return n},y.erf=function(t){var e,n,r,i=[-1.3026537197817094,.6419697923564902,.019476473204185836,-.00956151478680863,-.000946595344482036,.000366839497852761,42523324806907e-18,-20278578112534e-18,-1624290004647e-18,130365583558e-17,1.5626441722e-8,-8.5238095915e-8,6.529054439e-9,5.059343495e-9,-9.91364156e-10,-2.27365122e-10,96467911e-18,2394038e-18,-6886027e-18,894487e-18,313092e-18,-112708e-18,381e-18,7106e-18,-1523e-18,-94e-18,121e-18,-28e-18],o=i.length-1,s=!1,a=0,u=0;for(t<0&&(t=-t,s=!0),e=4*(r=2/(2+t))-2;0<o;o--)a=e*(n=a)-u+i[o],u=n;return r*=b.exp(-t*t+.5*(i[0]+e*a)-u),s?r-1:1-r},y.erfc=function(t){return 1-y.erf(t)},y.erfcinv=function(t){var e,n,r,i,o=0;if(2<=t)return-100;if(t<=0)return 100;for(e=-.70711*((2.30753+.27061*(r=b.sqrt(-2*b.log((i=t<1?t:2-t)/2))))/(1+r*(.99229+.04481*r))-r);o<2;o++)e+=(n=y.erfc(e)-i)/(1.1283791670955126*b.exp(-e*e)-e*n);return t<1?e:-e},y.ibetainv=function(t,e,n){var r,i,o,s,a,u,c,l=e-1,h=n-1,f=0;if(t<=0)return 0;if(1<=t)return 1;for(s=1<=e&&1<=n?(s=(2.30753+.27061*(i=b.sqrt(-2*b.log(t<.5?t:1-t))))/(1+i*(.99229+.04481*i))-i,u=(s=t<.5?-s:s)*b.sqrt((u=(s*s-3)/6)+(a=2/(1/(2*e-1)+1/(2*n-1))))/a-(1/(2*n-1)-1/(2*e-1))*(u+5/6-2/(3*a)),e/(e+n*b.exp(2*u))):(a=b.log(e/(e+n)),r=b.log(n/(e+n)),t<(i=b.exp(e*a)/e)/(u=i+(o=b.exp(n*r)/n))?b.pow(e*u*t,1/e):1-b.pow(n*u*(1-t),1/n)),c=-y.gammaln(e)-y.gammaln(n)+y.gammaln(e+n);f<10;f++){if(0===s||1===s)return s;if(1<=(s=(s-=i=(o=(y.ibeta(s,e,n)-t)/(i=b.exp(l*b.log(s)+h*b.log(1-s)+c)))/(1-.5*b.min(1,o*(l/s-h/(1-s)))))<=0?.5*(s+i):s)&&(s=.5*(s+i+1)),b.abs(i)<1e-8*s&&0<f)break}return s},y.ibeta=function(t,e,n){var r=0===t||1===t?0:b.exp(y.gammaln(e+n)-y.gammaln(e)-y.gammaln(n)+e*b.log(t)+n*b.log(1-t));return!(t<0||1<t)&&(t<(e+1)/(e+n+2)?r*y.betacf(t,e,n)/e:1-r*y.betacf(1-t,n,e)/n)},y.randn=function(t,e){var n,r,i,o;if(e=e||t,t)return y.create(t,e,(function(){return y.randn()}));for(;n=y._random_fn(),r=1.7156*(y._random_fn()-.5),.27597<(o=(i=n-.449871)*i+(o=b.abs(r)+.386595)*(.196*o-.25472*i))&&(.27846<o||r*r>-4*b.log(n)*n*n););return r/n},y.randg=function(t,e,n){var r,i,o,s,a,u=t;if(n=n||e,t=t||1,e)return(e=y.zeros(e,n)).alter((function(){return y.randg(t)})),e;t<1&&(t+=1),r=t-1/3,i=1/b.sqrt(9*r);do{for(;(s=1+i*(a=y.randn()))<=0;);}while(s*=s*s,(o=y._random_fn())>1-.331*b.pow(a,4)&&b.log(o)>.5*a*a+r*(1-s+b.log(s)));if(t==u)return r*s;for(;0===(o=y._random_fn()););return b.pow(o,1/u)*r*s};for(var w="gammaln gammafn factorial factorialln".split(" "),_=0;_<w.length;_++)!function(t){y.fn[t]=function(){return y(y.map(this,(function(e){return y[t](e)})))}}(w[_]);for(var x="randn".split(" "),C=0;C<x.length;C++)!function(t){y.fn[t]=function(){return y(y[t].apply(null,arguments))}}(x[C]);for(var k=o,S=Math,O="beta centralF cauchy chisquare exponential gamma invgamma kumaraswamy laplace lognormal noncentralt normal pareto studentt weibull uniform binomial negbin hypgeom poisson triangular tukey arcsine".split(" "),E=0;E<O.length;E++)!function(t){k[t]=function(t,e,n){return this instanceof arguments.callee?(this._a=t,this._b=e,this._c=n,this):new arguments.callee(t,e,n)},k.fn[t]=function(e,n,r){return e=k[t](e,n,r),e.data=this,e},k[t].prototype.sample=function(e){var n=this._a,r=this._b,i=this._c;return e?k.alter(e,(function(){return k[t].sample(n,r,i)})):k[t].sample(n,r,i)};for(var e="pdf cdf inv".split(" "),n=0;n<e.length;n++)!function(e){k[t].prototype[e]=function(n){var r=this._a,i=this._b,o=this._c;return"number"!=typeof(n=n||0===n?n:this.data)?k.fn.map.call(n,(function(n){return k[t][e](n,r,i,o)})):k[t][e](n,r,i,o)}}(e[n]);for(var r="mean median mode variance".split(" "),i=0;i<r.length;i++)!function(e){k[t].prototype[e]=function(){return k[t][e](this._a,this._b,this._c)}}(r[i])}(O[E]);function T(t,e,n,r){for(var i,o=0,s=1,a=1,u=1,c=0,l=0;S.abs((a-l)/a)>r;)s=u+(i=-(e+c)*(e+n+c)*t/(e+2*c)/(e+2*c+1))*s,a=(o=(l=a)+i*o)+(i=(c+=1)*(n-c)*t/(e+2*c-1)/(e+2*c))*a,o/=u=s+i*u,s/=u,a/=u,u=1;return a/e}function A(t,e,n){var r=[.9815606342467192,.9041172563704749,.7699026741943047,.5873179542866175,.3678314989981802,.1252334085114689],i=[.04717533638651183,.10693932599531843,.16007832854334622,.20316742672306592,.2334925365383548,.24914704581340277],o=.5*t;if(8<=o)return 1;for(var s=(s=2*k.normal.cdf(o,0,1,1,0)-1)>=S.exp(-50/n)?S.pow(s,n):0,a=3<t?2:3,u=o,c=(8-o)/a,l=u+c,h=0,f=n-1,p=1;p<=a;p++){for(var d=0,g=.5*(l+u),m=.5*(l-u),v=1;v<=12;v++){var y,b=6<v?r[(y=12-v+1)-1]:-r[(y=v)-1],w=(b=g+m*b,b*b);if(60<w)break;b=2*k.normal.cdf(b,0,1,1,0)*.5-2*k.normal.cdf(b,t,1,1,0)*.5,b>=S.exp(-30/f)&&(d+=i[y-1]*S.exp(-.5*w)*S.pow(b,f))}h+=d*=2*m*n/S.sqrt(2*S.PI),u=l,l+=c}return(s+=h)<=S.exp(-30/e)?0:1<=(s=S.pow(s,e))?1:s}k.extend(k.beta,{pdf:function(t,e,n){return 1<t||t<0?0:1==e&&1==n?1:e<512&&n<512?S.pow(t,e-1)*S.pow(1-t,n-1)/k.betafn(e,n):S.exp((e-1)*S.log(t)+(n-1)*S.log(1-t)-k.betaln(e,n))},cdf:function(t,e,n){return 1<t||t<0?+(1<t):k.ibeta(t,e,n)},inv:function(t,e,n){return k.ibetainv(t,e,n)},mean:function(t,e){return t/(t+e)},median:function(t,e){return k.ibetainv(.5,t,e)},mode:function(t,e){return(t-1)/(t+e-2)},sample:function(t,e){return t=k.randg(t),t/(t+k.randg(e))},variance:function(t,e){return t*e/(S.pow(t+e,2)*(t+e+1))}}),k.extend(k.centralF,{pdf:function(t,e,n){return t<0?0:e<=2?0===t&&e<2?1/0:0===t&&2===e?1:1/k.betafn(e/2,n/2)*S.pow(e/n,e/2)*S.pow(t,e/2-1)*S.pow(1+e/n*t,-(e+n)/2):e*(n/(n+t*e))/2*k.binomial.pdf((e-2)/2,(e+n-2)/2,e*t/(n+t*e))},cdf:function(t,e,n){return t<0?0:k.ibeta(e*t/(e*t+n),e/2,n/2)},inv:function(t,e,n){return n/(e*(1/k.ibetainv(t,e/2,n/2)-1))},mean:function(t,e){return 2<e?e/(e-2):void 0},mode:function(t,e){return 2<t?e*(t-2)/(t*(e+2)):void 0},sample:function(t,e){return 2*k.randg(t/2)/t/(2*k.randg(e/2)/e)},variance:function(t,e){if(!(e<=4))return 2*e*e*(t+e-2)/(t*(e-2)*(e-2)*(e-4))}}),k.extend(k.cauchy,{pdf:function(t,e,n){return n<0?0:n/(S.pow(t-e,2)+S.pow(n,2))/S.PI},cdf:function(t,e,n){return S.atan((t-e)/n)/S.PI+.5},inv:function(t,e,n){return e+n*S.tan(S.PI*(t-.5))},median:function(t){return t},mode:function(t){return t},sample:function(t,e){return k.randn()*S.sqrt(1/(2*k.randg(.5)))*e+t}}),k.extend(k.chisquare,{pdf:function(t,e){return t<0?0:0===t&&2===e?.5:S.exp((e/2-1)*S.log(t)-t/2-e/2*S.log(2)-k.gammaln(e/2))},cdf:function(t,e){return t<0?0:k.lowRegGamma(e/2,t/2)},inv:function(t,e){return 2*k.gammapinv(t,.5*e)},mean:function(t){return t},median:function(t){return t*S.pow(1-2/(9*t),3)},mode:function(t){return 0<t-2?t-2:0},sample:function(t){return 2*k.randg(t/2)},variance:function(t){return 2*t}}),k.extend(k.exponential,{pdf:function(t,e){return t<0?0:e*S.exp(-e*t)},cdf:function(t,e){return t<0?0:1-S.exp(-e*t)},inv:function(t,e){return-S.log(1-t)/e},mean:function(t){return 1/t},median:function(t){return 1/t*S.log(2)},mode:function(){return 0},sample:function(t){return-1/t*S.log(k._random_fn())},variance:function(t){return S.pow(t,-2)}}),k.extend(k.gamma,{pdf:function(t,e,n){return t<0?0:0===t&&1===e?1/n:S.exp((e-1)*S.log(t)-t/n-k.gammaln(e)-e*S.log(n))},cdf:function(t,e,n){return t<0?0:k.lowRegGamma(e,t/n)},inv:function(t,e,n){return k.gammapinv(t,e)*n},mean:function(t,e){return t*e},mode:function(t,e){if(1<t)return(t-1)*e},sample:function(t,e){return k.randg(t)*e},variance:function(t,e){return t*e*e}}),k.extend(k.invgamma,{pdf:function(t,e,n){return t<=0?0:S.exp(-(e+1)*S.log(t)-n/t-k.gammaln(e)+e*S.log(n))},cdf:function(t,e,n){return t<=0?0:1-k.lowRegGamma(e,n/t)},inv:function(t,e,n){return n/k.gammapinv(1-t,e)},mean:function(t,e){return 1<t?e/(t-1):void 0},mode:function(t,e){return e/(t+1)},sample:function(t,e){return e/k.randg(t)},variance:function(t,e){if(!(t<=2))return e*e/((t-1)*(t-1)*(t-2))}}),k.extend(k.kumaraswamy,{pdf:function(t,e,n){return 0===t&&1===e?n:1===t&&1===n?e:S.exp(S.log(e)+S.log(n)+(e-1)*S.log(t)+(n-1)*S.log(1-S.pow(t,e)))},cdf:function(t,e,n){return t<0?0:1<t?1:1-S.pow(1-S.pow(t,e),n)},inv:function(t,e,n){return S.pow(1-S.pow(1-t,1/n),1/e)},mean:function(t,e){return e*k.gammafn(1+1/t)*k.gammafn(e)/k.gammafn(1+1/t+e)},median:function(t,e){return S.pow(1-S.pow(2,-1/e),1/t)},mode:function(t,e){if(1<=t&&1<=e&&1!==t&&1!==e)return S.pow((t-1)/(t*e-1),1/t)},variance:function(){throw new Error("variance not yet implemented")}}),k.extend(k.lognormal,{pdf:function(t,e,n){return t<=0?0:S.exp(-S.log(t)-.5*S.log(2*S.PI)-S.log(n)-S.pow(S.log(t)-e,2)/(2*n*n))},cdf:function(t,e,n){return t<0?0:.5+.5*k.erf((S.log(t)-e)/S.sqrt(2*n*n))},inv:function(t,e,n){return S.exp(-1.4142135623730951*n*k.erfcinv(2*t)+e)},mean:function(t,e){return S.exp(t+e*e/2)},median:function(t){return S.exp(t)},mode:function(t,e){return S.exp(t-e*e)},sample:function(t,e){return S.exp(k.randn()*e+t)},variance:function(t,e){return(S.exp(e*e)-1)*S.exp(2*t+e*e)}}),k.extend(k.noncentralt,{pdf:function(t,e,n){return S.abs(n)<1e-14?k.studentt.pdf(t,e):S.abs(t)<1e-14?S.exp(k.gammaln((e+1)/2)-n*n/2-.5*S.log(S.PI*e)-k.gammaln(e/2)):e/t*(k.noncentralt.cdf(t*S.sqrt(1+2/e),e+2,n)-k.noncentralt.cdf(t,e,n))},cdf:function(t,e,n){if(S.abs(n)<1e-14)return k.studentt.cdf(t,e);for(var r=!1,i=(t<0&&(r=!0,n=-n),k.normal.cdf(-n,0,1)),o=1e-14+1,s=o,a=t*t/(t*t+e),u=0,c=S.exp(-n*n/2),l=S.exp(-n*n/2-.5*S.log(2)-k.gammaln(1.5))*n;u<200||1e-14<s||1e-14<o;)s=o,0<u&&(c*=n*n/(2*u),l*=n*n/(2*(u+.5))),i+=.5*(o=c*k.beta.cdf(a,u+.5,e/2)+l*k.beta.cdf(a,u+1,e/2)),u++;return r?1-i:i}}),k.extend(k.normal,{pdf:function(t,e,n){return S.exp(-.5*S.log(2*S.PI)-S.log(n)-S.pow(t-e,2)/(2*n*n))},cdf:function(t,e,n){return.5*(1+k.erf((t-e)/S.sqrt(2*n*n)))},inv:function(t,e,n){return-1.4142135623730951*n*k.erfcinv(2*t)+e},mean:function(t){return t},median:function(t){return t},mode:function(t){return t},sample:function(t,e){return k.randn()*e+t},variance:function(t,e){return e*e}}),k.extend(k.pareto,{pdf:function(t,e,n){return t<e?0:n*S.pow(e,n)/S.pow(t,n+1)},cdf:function(t,e,n){return t<e?0:1-S.pow(e/t,n)},inv:function(t,e,n){return e/S.pow(1-t,1/n)},mean:function(t,e){if(!(e<=1))return e*S.pow(t,e)/(e-1)},median:function(t,e){return t*(e*S.SQRT2)},mode:function(t){return t},variance:function(t,e){if(!(e<=2))return t*t*e/(S.pow(e-1,2)*(e-2))}}),k.extend(k.studentt,{pdf:function(t,e){return 1/(S.sqrt(e=1e100<e?1e100:e)*k.betafn(.5,e/2))*S.pow(1+t*t/e,-(e+1)/2)},cdf:function(t,e){var n=e/2;return k.ibeta((t+S.sqrt(t*t+e))/(2*S.sqrt(t*t+e)),n,n)},inv:function(t,e){var n=k.ibetainv(2*S.min(t,1-t),.5*e,.5);n=S.sqrt(e*(1-n)/n);return.5<t?n:-n},mean:function(t){return 1<t?0:void 0},median:function(){return 0},mode:function(){return 0},sample:function(t){return k.randn()*S.sqrt(t/(2*k.randg(t/2)))},variance:function(t){return 2<t?t/(t-2):1<t?1/0:void 0}}),k.extend(k.weibull,{pdf:function(t,e,n){return t<0||e<0||n<0?0:n/e*S.pow(t/e,n-1)*S.exp(-S.pow(t/e,n))},cdf:function(t,e,n){return t<0?0:1-S.exp(-S.pow(t/e,n))},inv:function(t,e,n){return e*S.pow(-S.log(1-t),1/n)},mean:function(t,e){return t*k.gammafn(1+1/e)},median:function(t,e){return t*S.pow(S.log(2),1/e)},mode:function(t,e){return e<=1?0:t*S.pow((e-1)/e,1/e)},sample:function(t,e){return t*S.pow(-S.log(k._random_fn()),1/e)},variance:function(t,e){return t*t*k.gammafn(1+2/e)-S.pow(k.weibull.mean(t,e),2)}}),k.extend(k.uniform,{pdf:function(t,e,n){return t<e||n<t?0:1/(n-e)},cdf:function(t,e,n){return t<e?0:t<n?(t-e)/(n-e):1},inv:function(t,e,n){return e+t*(n-e)},mean:function(t,e){return.5*(t+e)},median:function(t,e){return k.mean(t,e)},mode:function(){throw new Error("mode is not yet implemented")},sample:function(t,e){return t/2+e/2+(e/2-t/2)*(2*k._random_fn()-1)},variance:function(t,e){return S.pow(e-t,2)/12}}),k.extend(k.binomial,{pdf:function(t,e,n){return 0===n||1===n?e*n===t?1:0:k.combination(e,t)*S.pow(n,t)*S.pow(1-n,e-t)},cdf:function(t,e,n){var r,i;return t<0?0:e<=t?1:n<0||1<n||e<=0?NaN:(r=(t=S.floor(t))+1,i=S.exp(k.gammaln(t=r+(e-=t))-k.gammaln(e)-k.gammaln(r)+r*S.log(n)+e*S.log(1-n)),t=n<(r+1)/(t+2)?i*T(n,r,e,1e-10):1-i*T(1-n,e,r,1e-10),S.round(1e10*(1-t))/1e10)}}),k.extend(k.negbin,{pdf:function(t,e,n){return t===t>>>0&&(t<0?0:k.combination(t+e-1,e-1)*S.pow(1-n,t)*S.pow(n,e))},cdf:function(t,e,n){var r=0,i=0;if(t<0)return 0;for(;i<=t;i++)r+=k.negbin.pdf(i,e,n);return r}}),k.extend(k.hypgeom,{pdf:function(t,e,n,r){if(t!=t|0)return!1;if(t<0||t<n-(e-r))return 0;if(r<t||n<t)return 0;if(e<2*n)return e<2*r?k.hypgeom.pdf(e-n-r+t,e,e-n,e-r):k.hypgeom.pdf(r-t,e,e-n,r);if(e<2*r)return k.hypgeom.pdf(n-t,e,n,e-r);if(n<r)return k.hypgeom.pdf(t,e,r,n);for(var i=1,o=0,s=0;s<t;s++){for(;1<i&&o<r;)i*=1-n/(e-o),o++;i*=(r-s)*(n-s)/((s+1)*(e-n-r+s+1))}for(;o<r;o++)i*=1-n/(e-o);return S.min(1,S.max(0,i))},cdf:function(t,e,n,r){if(t<0||t<n-(e-r))return 0;if(r<=t||n<=t)return 1;if(e<2*n)return e<2*r?k.hypgeom.cdf(e-n-r+t,e,e-n,e-r):1-k.hypgeom.cdf(r-t-1,e,e-n,r);if(e<2*r)return 1-k.hypgeom.cdf(n-t-1,e,n,e-r);if(n<r)return k.hypgeom.cdf(t,e,r,n);for(var i=1,o=1,s=0,a=0;a<t;a++){for(;1<i&&s<r;){var u=1-n/(e-s);o*=u,i*=u,s++}i+=o*=(r-a)*(n-a)/((a+1)*(e-n-r+a+1))}for(;s<r;s++)i*=1-n/(e-s);return S.min(1,S.max(0,i))}}),k.extend(k.poisson,{pdf:function(t,e){return e<0||t%1!=0||t<0?0:S.pow(e,t)*S.exp(-e)/k.factorial(t)},cdf:function(t,e){var n=[],r=0;if(t<0)return 0;for(;r<=t;r++)n.push(k.poisson.pdf(r,e));return k.sum(n)},mean:function(t){return t},variance:function(t){return t},sampleSmall:function(t){for(var e=1,n=0,r=S.exp(-t);n++,r<(e*=k._random_fn()););return n-1},sampleLarge:function(t){for(var e,n,r,i=t,o=(t=S.sqrt(i),S.log(i)),s=.931+2.53*t,a=.02483*s-.059,u=1.1239+1.1328/(s-3.4),c=.9277-3.6224/(s-2);;){if(e=S.random()-.5,n=S.random(),r=.5-S.abs(e),e=S.floor((2*a/r+s)*e+i+.43),.07<=r&&n<=c)return e;if(!(e<0||r<.013&&r<n)&&S.log(n)+S.log(u)-S.log(a/(r*r)+s)<=e*o-i-k.loggam(e+1))return e}},sample:function(t){return t<10?this.sampleSmall(t):this.sampleLarge(t)}}),k.extend(k.triangular,{pdf:function(t,e,n,r){return n<=e||r<e||n<r?NaN:t<e||n<t?0:t<r?2*(t-e)/((n-e)*(r-e)):t===r?2/(n-e):2*(n-t)/((n-e)*(n-r))},cdf:function(t,e,n,r){return n<=e||r<e||n<r?NaN:t<=e?0:n<=t?1:t<=r?S.pow(t-e,2)/((n-e)*(r-e)):1-S.pow(n-t,2)/((n-e)*(n-r))},inv:function(t,e,n,r){return n<=e||r<e||n<r?NaN:t<=(r-e)/(n-e)?e+(n-e)*S.sqrt(t*((r-e)/(n-e))):e+(n-e)*(1-S.sqrt((1-t)*(1-(r-e)/(n-e))))},mean:function(t,e,n){return(t+e+n)/3},median:function(t,e,n){return n<=(t+e)/2?e-S.sqrt((e-t)*(e-n))/S.sqrt(2):(t+e)/2<n?t+S.sqrt((e-t)*(n-t))/S.sqrt(2):void 0},mode:function(t,e,n){return n},sample:function(t,e,n){var r=k._random_fn();return r<(n-t)/(e-t)?t+S.sqrt(r*(e-t)*(n-t)):e-S.sqrt((1-r)*(e-t)*(e-n))},variance:function(t,e,n){return(t*t+e*e+n*n-t*e-t*n-e*n)/18}}),k.extend(k.arcsine,{pdf:function(t,e,n){return n<=e?NaN:t<=e||n<=t?0:2/S.PI*S.pow(S.pow(n-e,2)-S.pow(2*t-e-n,2),-.5)},cdf:function(t,e,n){return t<e?0:t<n?2/S.PI*S.asin(S.sqrt((t-e)/(n-e))):1},inv:function(t,e,n){return e+(.5-.5*S.cos(S.PI*t))*(n-e)},mean:function(t,e){return e<=t?NaN:(t+e)/2},median:function(t,e){return e<=t?NaN:(t+e)/2},mode:function(){throw new Error("mode is not yet implemented")},sample:function(t,e){return(t+e)/2+(e-t)/2*S.sin(2*S.PI*k.uniform.sample(0,1))},variance:function(t,e){return e<=t?NaN:S.pow(e-t,2)/8}}),k.extend(k.laplace,{pdf:function(t,e,n){return n<=0?0:S.exp(-S.abs(t-e)/n)/(2*n)},cdf:function(t,e,n){return n<=0?0:t<e?.5*S.exp((t-e)/n):1-.5*S.exp(-(t-e)/n)},mean:function(t){return t},median:function(t){return t},mode:function(t){return t},variance:function(t,e){return 2*e*e},sample:function(t,e){var n=k._random_fn()-.5;return t-e*(n/S.abs(n))*S.log(1-2*S.abs(n))}}),k.extend(k.tukey,{cdf:function(t,e,n){var r=e,i=[.9894009349916499,.9445750230732326,.8656312023878318,.755404408355003,.6178762444026438,.45801677765722737,.2816035507792589,.09501250983763744],o=[.027152459411754096,.062253523938647894,.09515851168249279,.12462897125553388,.14959598881657674,.16915651939500254,.18260341504492358,.1894506104550685];if(t<=0)return 0;if(n<2||r<2)return NaN;if(!Number.isFinite(t))return 1;if(25e3<n)return A(t,1,r);e=.5*n;var s=e*S.log(n)-n*S.log(2)-k.gammaln(e),a=e-1,u=.25*n,c=n<=100?1:n<=800?.5:n<=5e3?.25:.125;s+=S.log(c);for(var l=0,h=1;h<=50;h++){for(var f=0,p=(2*h-1)*c,d=1;d<=16;d++){var g,m=8<d?s+a*S.log(p+i[g=d-8-1]*c)-(i[g]*c+p)*u:s+a*S.log(p-i[g=d-1]*c)+(i[g]*c-p)*u;-30<=m&&(f+=A(8<d?t*S.sqrt(.5*(i[g]*c+p)):t*S.sqrt(.5*(-i[g]*c+p)),1,r)*o[g]*S.exp(m))}if(1<=h*c&&f<=1e-14)break;l+=f}if(1e-14<f)throw new Error("tukey.cdf failed to converge");return 1<l?1:l},inv:function(t,e,n){if(n<2||e<2)return NaN;if(t<0||1<t)return NaN;if(0===t)return 0;if(1===t)return 1/0;i=e,o=n,r=.5-.5*(r=t),r=(r=S.sqrt(S.log(1/(r*r))))+((((-453642210148e-16*r-.204231210125)*r-.342242088547)*r-1)*r+.322232421088)/((((.0038560700634*r+.10353775285)*r+.531103462366)*r+.588581570495)*r+.099348462606),o<120&&(r+=(r*r*r+r)/o/4),s=.8832-.2368*r,o<120&&(s+=-1.214/o+1.208*r/o);for(var r,i,o,s,a,u=r*(s*S.log(i-1)+1.4142),c=k.tukey.cdf(u,e,n)-t,l=(f=0<c?S.max(0,u-1):u+1,k.tukey.cdf(f,e,n)-t),h=1;h<50;h++){a=f-l*(f-u)/(l-c),c=l,u=f,a<0&&(a=0,l=-t);l=k.tukey.cdf(a,e,n)-t;var f=a;if(S.abs(f-u)<1e-4)return a}throw new Error("tukey.inv failed to converge")}});var I,j,P=o,N=Math,D=Array.prototype.push,R=P.utils.isArray;function M(t){return R(t)||t instanceof P}P.extend({add:function(t,e){return M(e)?(M(e[0])||(e=[e]),P.map(t,(function(t,n,r){return t+e[n][r]}))):P.map(t,(function(t){return t+e}))},subtract:function(t,e){return M(e)?(M(e[0])||(e=[e]),P.map(t,(function(t,n,r){return t-e[n][r]||0}))):P.map(t,(function(t){return t-e}))},divide:function(t,e){return M(e)?(M(e[0])||(e=[e]),P.multiply(t,P.inv(e))):P.map(t,(function(t){return t/e}))},multiply:function(t,e){var n,r,i,o,s,a,u,c;if(void 0===t.length&&void 0===e.length)return t*e;if(s=t.length,a=t[0].length,u=P.zeros(s,i=M(e)?e[0].length:a),c=0,M(e)){for(;c<i;c++)for(n=0;n<s;n++){for(r=o=0;r<a;r++)o+=t[n][r]*e[r][c];u[n][c]=o}return 1===s&&1===c?u[0][0]:u}return P.map(t,(function(t){return t*e}))},outer:function(t,e){return P.multiply(t.map((function(t){return[t]})),[e])},dot:function(t,e){M(t[0])||(t=[t]),M(e[0])||(e=[e]);for(var n,r,i=1===t[0].length&&1!==t.length?P.transpose(t):t,o=1===e[0].length&&1!==e.length?P.transpose(e):e,s=[],a=0,u=i.length,c=i[0].length;a<u;a++){for(s[a]=[],r=n=0;r<c;r++)n+=i[a][r]*o[a][r];s[a]=n}return 1===s.length?s[0]:s},pow:function(t,e){return P.map(t,(function(t){return N.pow(t,e)}))},exp:function(t){return P.map(t,(function(t){return N.exp(t)}))},log:function(t){return P.map(t,(function(t){return N.log(t)}))},abs:function(t){return P.map(t,(function(t){return N.abs(t)}))},norm:function(t,e){var n=0,r=0;for(isNaN(e)&&(e=2),M(t[0])&&(t=t[0]);r<t.length;r++)n+=N.pow(N.abs(t[r]),e);return N.pow(n,1/e)},angle:function(t,e){return N.acos(P.dot(t,e)/(P.norm(t)*P.norm(e)))},aug:function(t,e){for(var n=[],r=0;r<t.length;r++)n.push(t[r].slice());for(r=0;r<n.length;r++)D.apply(n[r],e[r]);return n},inv:function(t){for(var e,n=t.length,r=t[0].length,i=P.identity(n,r),o=P.gauss_jordan(t,i),s=[],a=0;a<n;a++)for(s[a]=[],e=r;e<o[0].length;e++)s[a][e-r]=o[a][e];return s},det:function(t){var e,n=t.length,r=2*n,i=new Array(r),o=n-1,s=r-1,a=o-n+1,u=s,c=0,l=0;if(2===n)return t[0][0]*t[1][1]-t[0][1]*t[1][0];for(;c<r;c++)i[c]=1;for(c=0;c<n;c++){for(e=0;e<n;e++)i[a<0?a+n:a]*=t[c][e],i[u<n?u+n:u]*=t[c][e],a++,u--;a=--o-n+1,u=--s}for(c=0;c<n;c++)l+=i[c];for(;c<r;c++)l-=i[c];return l},gauss_elimination:function(t,e){var n,r,i,o,s=0,a=0,u=t.length,c=t[0].length,l=0,h=[],f=(t=P.aug(t,e))[0].length;for(s=0;s<u;s++){for(r=t[s][s],o=(a=s)+1;o<c;o++)r<N.abs(t[o][s])&&(r=t[o][s],a=o);if(a!=s)for(o=0;o<f;o++)i=t[s][o],t[s][o]=t[a][o],t[a][o]=i;for(a=s+1;a<u;a++)for(n=t[a][s]/t[s][s],o=s;o<f;o++)t[a][o]=t[a][o]-n*t[s][o]}for(s=u-1;0<=s;s--){for(l=0,a=s+1;a<=u-1;a++)l+=h[a]*t[s][a];h[s]=(t[s][f-1]-l)/t[s][s]}return h},gauss_jordan:function(t,e){for(var n,r=P.aug(t,e),i=r.length,o=r[0].length,s=0,a=0;a<i;a++){for(var u=a,c=a+1;c<i;c++)N.abs(r[c][a])>N.abs(r[u][a])&&(u=c);var l=r[a];for(r[a]=r[u],r[u]=l,c=a+1;c<i;c++)for(s=r[c][a]/r[a][a],n=a;n<o;n++)r[c][n]-=r[a][n]*s}for(a=i-1;0<=a;a--){for(s=r[a][a],c=0;c<a;c++)for(n=o-1;a-1<n;n--)r[c][n]-=r[a][n]*r[c][a]/s;for(r[a][a]/=s,n=i;n<o;n++)r[a][n]/=s}return r},triaUpSolve:function(t,e){var n,r=t[0].length,i=P.zeros(1,r)[0],o=!1;return null!=e[0].length&&(e=e.map((function(t){return t[0]})),o=!0),P.arange(r-1,-1,-1).forEach((function(o){n=P.arange(o+1,r).map((function(e){return i[e]*t[o][e]})),i[o]=(e[o]-P.sum(n))/t[o][o]})),o?i.map((function(t){return[t]})):i},triaLowSolve:function(t,e){var n,r=t[0].length,i=P.zeros(1,r)[0],o=!1;return null!=e[0].length&&(e=e.map((function(t){return t[0]})),o=!0),P.arange(r).forEach((function(r){n=P.arange(r).map((function(e){return t[r][e]*i[e]})),i[r]=(e[r]-P.sum(n))/t[r][r]})),o?i.map((function(t){return[t]})):i},lu:function(t){var e,n=t.length,r=P.identity(n),i=P.zeros(t.length,t[0].length);return P.arange(n).forEach((function(e){i[0][e]=t[0][e]})),P.arange(1,n).forEach((function(o){P.arange(o).forEach((function(n){e=P.arange(n).map((function(t){return r[o][t]*i[t][n]})),r[o][n]=(t[o][n]-P.sum(e))/i[n][n]})),P.arange(o,n).forEach((function(n){e=P.arange(o).map((function(t){return r[o][t]*i[t][n]})),i[o][n]=t[e.length][n]-P.sum(e)}))})),[r,i]},cholesky:function(t){var e,n=t.length,r=P.zeros(t.length,t[0].length);return P.arange(n).forEach((function(i){e=P.arange(i).map((function(t){return N.pow(r[i][t],2)})),r[i][i]=N.sqrt(t[i][i]-P.sum(e)),P.arange(i+1,n).forEach((function(n){e=P.arange(i).map((function(t){return r[i][t]*r[n][t]})),r[n][i]=(t[i][n]-P.sum(e))/r[i][i]}))})),r},gauss_jacobi:function(t,e,n,r){for(var i,o,s,a,u=0,c=0,l=t.length,h=[],f=[],p=[];u<l;u++)for(h[u]=[],f[u]=[],p[u]=[],c=0;c<l;c++)c<u?(h[u][c]=t[u][c],f[u][c]=p[u][c]=0):u<c?(f[u][c]=t[u][c],h[u][c]=p[u][c]=0):(p[u][c]=t[u][c],h[u][c]=f[u][c]=0);for(s=P.multiply(P.multiply(P.inv(p),P.add(h,f)),-1),o=P.multiply(P.inv(p),e),a=P.add(P.multiply(s,i=n),o),u=2;N.abs(P.norm(P.subtract(a,i)))>r;)i=a,a=P.add(P.multiply(s,i),o),u++;return a},gauss_seidel:function(t,e,n,r){for(var i,o,s,a,u,c=0,l=t.length,h=[],f=[],p=[];c<l;c++)for(h[c]=[],f[c]=[],p[c]=[],i=0;i<l;i++)i<c?(h[c][i]=t[c][i],f[c][i]=p[c][i]=0):c<i?(f[c][i]=t[c][i],h[c][i]=p[c][i]=0):(p[c][i]=t[c][i],h[c][i]=f[c][i]=0);for(a=P.multiply(P.multiply(P.inv(P.add(p,h)),f),-1),s=P.multiply(P.inv(P.add(p,h)),e),u=P.add(P.multiply(a,o=n),s),c=2;N.abs(P.norm(P.subtract(u,o)))>r;)o=u,u=P.add(P.multiply(a,o),s),c+=1;return u},SOR:function(t,e,n,r,i){for(var o,s,a,u,c,l=0,h=t.length,f=[],p=[],d=[];l<h;l++)for(f[l]=[],p[l]=[],d[l]=[],o=0;o<h;o++)o<l?(f[l][o]=t[l][o],p[l][o]=d[l][o]=0):l<o?(p[l][o]=t[l][o],f[l][o]=d[l][o]=0):(d[l][o]=t[l][o],f[l][o]=p[l][o]=0);for(u=P.multiply(P.inv(P.add(d,P.multiply(f,i))),P.subtract(P.multiply(d,1-i),P.multiply(p,i))),a=P.multiply(P.multiply(P.inv(P.add(d,P.multiply(f,i))),e),i),c=P.add(P.multiply(u,s=n),a),l=2;N.abs(P.norm(P.subtract(c,s)))>r;)s=c,c=P.add(P.multiply(u,s),a),l++;return c},householder:function(t){for(var e,n,r,i,o,s=t.length,a=t[0].length,u=0,c=[];u<s-1;u++){for(n=0,o=u+1;o<a;o++)n+=t[o][u]*t[o][u];for(n=(0<t[u+1][u]?-1:1)*N.sqrt(n),r=N.sqrt((n*n-t[u+1][u]*n)/2),(c=P.zeros(s,1))[u+1][0]=(t[u+1][u]-n)/(2*r),i=u+2;i<s;i++)c[i][0]=t[i][u]/(2*r);e=P.subtract(P.identity(s,a),P.multiply(P.multiply(c,P.transpose(c)),2)),t=P.multiply(e,P.multiply(t,e))}return t},QR:(I=P.sum,j=P.arange,function(t){var e,n,r,i=t.length,o=t[0].length,s=P.zeros(o,o);for(t=P.copy(t),n=0;n<o;n++){for(s[n][n]=N.sqrt(I(j(i).map((function(e){return t[e][n]*t[e][n]})))),e=0;e<i;e++)t[e][n]=t[e][n]/s[n][n];for(r=n+1;r<o;r++)for(s[n][r]=I(j(i).map((function(e){return t[e][n]*t[e][r]}))),e=0;e<i;e++)t[e][r]=t[e][r]-t[e][n]*s[n][r]}return[t,s]}),lstsq:function(t,e){var n,r,i=!1;void 0===e[0].length&&(e=e.map((function(t){return[t]})),i=!0);var o=(s=P.QR(t))[0],s=s[1];t=t[0].length,o=P.slice(o,{col:{end:t}}),s=P.slice(s,{row:{end:t}}),n=s,t=(n=P.copy(n)).length,r=P.identity(t),P.arange(t-1,-1,-1).forEach((function(t){P.sliceAssign(r,{row:t},P.divide(P.slice(r,{row:t}),n[t][t])),P.sliceAssign(n,{row:t},P.divide(P.slice(n,{row:t}),n[t][t])),P.arange(t).forEach((function(e){var i=P.multiply(n[e][t],-1),o=P.slice(n,{row:e}),s=P.multiply(P.slice(n,{row:t}),i);P.sliceAssign(n,{row:e},P.add(o,s)),o=P.slice(r,{row:e}),s=P.multiply(P.slice(r,{row:t}),i);P.sliceAssign(r,{row:e},P.add(o,s))}))})),s=r;return void 0===(t=P.transpose(o))[0].length&&(t=[t]),void 0===(o=P.multiply(P.multiply(s,t),e)).length&&(o=[[o]]),i?o.map((function(t){return t[0]})):o},jacobi:function(t){for(var e,n,r,i,o,s,a,u=1,c=t.length,l=P.identity(c,c),h=[];1===u;){for(o=t[0][1],i=1,e=r=0;e<c;e++)for(n=0;n<c;n++)e!=n&&o<N.abs(t[e][n])&&(o=N.abs(t[e][n]),r=e,i=n);for(s=t[r][r]===t[i][i]?0<t[r][i]?N.PI/4:-N.PI/4:N.atan(2*t[r][i]/(t[r][r]-t[i][i]))/2,(a=P.identity(c,c))[r][r]=N.cos(s),a[r][i]=-N.sin(s),a[i][r]=N.sin(s),a[i][i]=N.cos(s),l=P.multiply(l,a),t=P.multiply(P.multiply(P.inv(a),t),a),u=0,e=1;e<c;e++)for(n=1;n<c;n++)e!=n&&.001<N.abs(t[e][n])&&(u=1)}for(e=0;e<c;e++)h.push(t[e][e]);return[l,h]},rungekutta:function(t,e,n,r,i,o){var s,a,u;if(2===o)for(;r<=n;)i+=((s=e*t(r,i))+(a=e*t(r+e,i+s)))/2,r+=e;if(4===o)for(;r<=n;)i+=((s=e*t(r,i))+2*(a=e*t(r+e/2,i+s/2))+2*(u=e*t(r+e/2,i+a/2))+e*t(r+e,i+u))/6,r+=e;return i},romberg:function(t,e,n,r){for(var i,o,s,a,u,c=0,l=(n-e)/2,h=[],f=[],p=[];c<r/2;){for(u=t(e),s=e,a=0;s<=n;s+=l,a++)h[a]=s;for(i=h.length,s=1;s<i-1;s++)u+=(s%2!=0?4:2)*t(h[s]);u=l/3*(u+t(n)),p[c]=u,l/=2,c++}for(o=p.length,i=1;1!==o;){for(s=0;s<o-1;s++)f[s]=(N.pow(4,i)*p[s+1]-p[s])/(N.pow(4,i)-1);o=f.length,p=f,f=[],i++}return p},richardson:function(t,e,n,r){function i(t,e){for(var n,r=0,i=t.length;r<i;r++)t[r]===e&&(n=r);return n}for(var o,s,a,u,c,l=N.abs(n-t[i(t,n)+1]),h=0,f=[],p=[];l<=r;)o=i(t,n+r),s=i(t,n),f[h]=(e[o]-2*e[s]+e[2*s-o])/(r*r),r/=2,h++;for(u=f.length,a=1;1!=u;){for(c=0;c<u-1;c++)p[c]=(N.pow(4,a)*f[c+1]-f[c])/(N.pow(4,a)-1);u=p.length,f=p,p=[],a++}return f},simpson:function(t,e,n,r){for(var i,o=(n-e)/r,s=t(e),a=[],u=e,c=0,l=1;u<=n;u+=o,c++)a[c]=u;for(i=a.length;l<i-1;l++)s+=(l%2!=0?4:2)*t(a[l]);return o/3*(s+t(n))},hermite:function(t,e,n,r){for(var i,o=t.length,s=0,a=0,u=[],c=[],l=[],h=[];a<o;a++){for(u[a]=1,i=0;i<o;i++)a!=i&&(u[a]*=(r-t[i])/(t[a]-t[i]));for(i=c[a]=0;i<o;i++)a!=i&&(c[a]+=1/(t[a]-t[i]));l[a]=(1-2*(r-t[a])*c[a])*(u[a]*u[a]),h[a]=(r-t[a])*(u[a]*u[a]),s+=l[a]*e[a]+h[a]*n[a]}return s},lagrange:function(t,e,n){for(var r,i,o=0,s=0,a=t.length;s<a;s++){for(i=e[s],r=0;r<a;r++)s!=r&&(i*=(n-t[r])/(t[s]-t[r]));o+=i}return o},cubic_spline:function(t,e,n){for(var r,i,o=t.length,s=0,a=[],u=[],c=[],l=[],h=[],f=[];s<o-1;s++)l[s]=t[s+1]-t[s];for(c[0]=0,s=1;s<o-1;s++)c[s]=3/l[s]*(e[s+1]-e[s])-3/l[s-1]*(e[s]-e[s-1]);for(s=1;s<o-1;s++)a[s]=[],u[s]=[],a[s][s-1]=l[s-1],a[s][s]=2*(l[s-1]+l[s]),a[s][s+1]=l[s],u[s][0]=c[s];for(i=P.multiply(P.inv(a),u),r=0;r<o-1;r++)h[r]=(e[r+1]-e[r])/l[r]-l[r]*(i[r+1][0]+2*i[r][0])/3,f[r]=(i[r+1][0]-i[r][0])/(3*l[r]);for(r=0;r<o&&!(t[r]>n);r++);return e[--r]+(n-t[r])*h[r]+P.sq(n-t[r])*i[r]+(n-t[r])*P.sq(n-t[r])*f[r]},gauss_quadrature:function(){throw new Error("gauss_quadrature not yet implemented")},PCA:function(t){var e,n,r,i,o,s=t.length,a=t[0].length,u=0,c=[],l=[],h=[],f=[],p=[],d=[];for(u=0;u<s;u++)c[u]=P.sum(t[u])/a;for(u=0;u<a;u++)for(f[u]=[],e=0;e<s;e++)f[u][e]=t[e][u]-c[e];for(f=P.transpose(f),u=0;u<s;u++)for(p[u]=[],e=0;e<s;e++)p[u][e]=P.dot([f[u]],[f[e]])/(a-1);for(o=(n=P.jacobi(p))[0],l=n[1],d=P.transpose(o),u=0;u<l.length;u++)for(e=u;e<l.length;e++)l[u]<l[e]&&(r=l[u],l[u]=l[e],l[e]=r,r=d[u],d[u]=d[e],d[e]=r);for(i=P.transpose(f),u=0;u<s;u++)for(h[u]=[],e=0;e<i.length;e++)h[u][e]=P.dot([d[u]],[i[e]]);return[t,l,d,h]}});for(var W="add divide multiply subtract dot pow exp log abs norm angle".split(" "),L=0;L<W.length;L++)!function(t){P.fn[t]=function(e,n){var r=this;return n?(setTimeout((function(){n.call(r,P.fn[t].call(r,e))}),15),this):"number"==typeof P[t](this,e)?P[t](this,e):P(P[t](this,e))}}(W[L]);function F(t,n,r,i){if(1<t||1<r||t<=0||r<=0)throw new Error("Proportions should be greater than 0 and less than 1");var o=(t*n+r*i)/(n+i);return(t-r)/e.sqrt(o*(1-o)*(1/n+1/i))}function H(t,e){var n=t.length,r=e[0].length-1,i=n-r-1,s=o.lstsq(e,t),a=o.multiply(e,s.map((function(t){return[t]}))).map((function(t){return t[0]})),u=o.subtract(t,a),c=o.mean(t),l=o.sum(a.map((function(t){return Math.pow(t-c,2)}))),h=o.sum(t.map((function(t,e){return Math.pow(t-a[e],2)}))),f=l+h;return{exog:e,endog:t,nobs:n,df_model:r,df_resid:i,coef:s,predict:a,resid:u,ybar:c,SST:f,SSE:l,SSR:h,R2:l/f}}function B(t){e=t.exog,n=e[0].length;var e,n,r=o.arange(n).map((function(t){var r=o.arange(n).filter((function(e){return e!==t}));return H(o.col(e,t).map((function(t){return t[0]})),o.col(e,r))})),i=Math.sqrt(t.SSR/t.df_resid),s=r.map((function(t){var e=t.SST;t=t.R2;return i/Math.sqrt(e*(1-t))})),a=(r=t.coef.map((function(t,e){return+t/s[e]})),r.map((function(e){return e=o.studentt.cdf(e,t.df_resid),2*(.5<e?1-e:e)}))),u=o.studentt.inv(.975,t.df_resid),c=t.coef.map((function(t,e){return e=u*s[e],[t-e,t+e]}));return{se:s,t:r,p:a,sigmaHat:i,interval95:c}}return t=o,e=Math,n=[].slice,r=t.utils.isNumber,i=t.utils.isArray,t.extend({zscore:function(){var e=n.call(arguments);return r(e[1])?(e[0]-e[1])/e[2]:(e[0]-t.mean(e[1]))/t.stdev(e[1],e[2])},ztest:function(){var r,o=n.call(arguments);return i(o[1])?(r=t.zscore(o[0],o[1],o[3]),1===o[2]?t.normal.cdf(-e.abs(r),0,1):2*t.normal.cdf(-e.abs(r),0,1)):2<o.length?(r=t.zscore(o[0],o[1],o[2]),1===o[3]?t.normal.cdf(-e.abs(r),0,1):2*t.normal.cdf(-e.abs(r),0,1)):(r=o[0],1===o[1]?t.normal.cdf(-e.abs(r),0,1):2*t.normal.cdf(-e.abs(r),0,1))}}),t.extend(t.fn,{zscore:function(t,e){return(t-this.mean())/this.stdev(e)},ztest:function(n,r,i){return n=e.abs(this.zscore(n,i)),1===r?t.normal.cdf(-n,0,1):2*t.normal.cdf(-n,0,1)}}),t.extend({tscore:function(){var r=n.call(arguments);return 4===r.length?(r[0]-r[1])/(r[2]/e.sqrt(r[3])):(r[0]-t.mean(r[1]))/(t.stdev(r[1],!0)/e.sqrt(r[1].length))},ttest:function(){var i,o=n.call(arguments);return 5===o.length?(i=e.abs(t.tscore(o[0],o[1],o[2],o[3])),1===o[4]?t.studentt.cdf(-i,o[3]-1):2*t.studentt.cdf(-i,o[3]-1)):r(o[1])?(i=e.abs(o[0]),1==o[2]?t.studentt.cdf(-i,o[1]-1):2*t.studentt.cdf(-i,o[1]-1)):(i=e.abs(t.tscore(o[0],o[1])),1==o[2]?t.studentt.cdf(-i,o[1].length-1):2*t.studentt.cdf(-i,o[1].length-1))}}),t.extend(t.fn,{tscore:function(t){return(t-this.mean())/(this.stdev(!0)/e.sqrt(this.cols()))},ttest:function(n,r){return 1===r?1-t.studentt.cdf(e.abs(this.tscore(n)),this.cols()-1):2*t.studentt.cdf(-e.abs(this.tscore(n)),this.cols()-1)}}),t.extend({anovafscore:function(){var r,i,o,s,a,u,c,l,h=n.call(arguments);if(1===h.length){for(a=new Array(h[0].length),c=0;c<h[0].length;c++)a[c]=h[0][c];h=a}for(i=new Array,c=0;c<h.length;c++)i=i.concat(h[c]);for(o=t.mean(i),c=r=0;c<h.length;c++)r+=h[c].length*e.pow(t.mean(h[c])-o,2);for(r/=h.length-1,c=u=0;c<h.length;c++)for(s=t.mean(h[c]),l=0;l<h[c].length;l++)u+=e.pow(h[c][l]-s,2);return r/(u/(i.length-h.length))},anovaftest:function(){var e=n.call(arguments);if(r(e[0]))return 1-t.centralF.cdf(e[0],e[1],e[2]);for(var i=t.anovafscore(e),o=e.length-1,s=0,a=0;a<e.length;a++)s+=e[a].length;return 1-t.centralF.cdf(i,o,s-o-1)},ftest:function(e,n,r){return 1-t.centralF.cdf(e,n,r)}}),t.extend(t.fn,{anovafscore:function(){return t.anovafscore(this.toArray())},anovaftes:function(){for(var e=0,n=0;n<this.length;n++)e+=this[n].length;return t.ftest(this.anovafscore(),this.length-1,e-this.length)}}),t.extend({qscore:function(){var i,o,s,a,u=n.call(arguments);u=r(u[0])?(i=u[0],o=u[1],s=u[2],a=u[3],u[4]):(i=t.mean(u[0]),o=t.mean(u[1]),s=u[0].length,a=u[1].length,u[2]);return e.abs(i-o)/(u*e.sqrt((1/s+1/a)/2))},qtest:function(){var e,r=n.call(arguments),i=(r=3===r.length?(e=r[0],r.slice(1)):7===r.length?(e=t.qscore(r[0],r[1],r[2],r[3],r[4]),r.slice(5)):(e=t.qscore(r[0],r[1],r[2]),r.slice(3)))[0];r=r[1];return 1-t.tukey.cdf(e,r,i-r)},tukeyhsd:function(e){for(var n=t.pooledstdev(e),r=e.map((function(e){return t.mean(e)})),i=e.reduce((function(t,e){return t+e.length}),0),o=[],s=0;s<e.length;++s)for(var a=s+1;a<e.length;++a){var u=t.qtest(r[s],r[a],e[s].length,e[a].length,n,i,e.length);o.push([[s,a],u])}return o}}),t.extend({normalci:function(){var r=n.call(arguments),i=new Array(2),o=4===r.length?e.abs(t.normal.inv(r[1]/2,0,1)*r[2]/e.sqrt(r[3])):e.abs(t.normal.inv(r[1]/2,0,1)*t.stdev(r[2])/e.sqrt(r[2].length));return i[0]=r[0]-o,i[1]=r[0]+o,i},tci:function(){var r=n.call(arguments),i=new Array(2),o=4===r.length?e.abs(t.studentt.inv(r[1]/2,r[3]-1)*r[2]/e.sqrt(r[3])):e.abs(t.studentt.inv(r[1]/2,r[2].length-1)*t.stdev(r[2],!0)/e.sqrt(r[2].length));return i[0]=r[0]-o,i[1]=r[0]+o,i},significant:function(t,e){return t<e}}),t.extend(t.fn,{normalci:function(e,n){return t.normalci(e,n,this.toArray())},tci:function(e,n){return t.tci(e,n,this.toArray())}}),t.extend(t.fn,{oneSidedDifferenceOfProportions:function(e,n,r,i){return e=F(e,n,r,i),t.ztest(e,1)},twoSidedDifferenceOfProportions:function(e,n,r,i){return e=F(e,n,r,i),t.ztest(e,2)}}),o.models={ols:function(t,e){e=B(t=H(t,e));var n,r=(i=(r=t).R2/r.df_model/((1-r.R2)/r.df_resid),n=1-(n=r.df_model,r=r.df_resid,o.beta.cdf(i/(r/n+i),n/2,r/2)),{F_statistic:i,pvalue:n}),i=1-(1-t.R2)*((t.nobs-1)/t.df_resid);return t.t=e,t.f=r,t.adjust_R2=i,t}},o.extend({buildxmatrix:function(){for(var t=new Array(arguments.length),e=0;e<arguments.length;e++)t[e]=[1].concat(arguments[e]);return o(t)},builddxmatrix:function(){for(var t=new Array(arguments[0].length),e=0;e<arguments[0].length;e++)t[e]=[1].concat(arguments[0][e]);return o(t)},buildjxmatrix:function(t){for(var e=new Array(t.length),n=0;n<t.length;n++)e[n]=t[n];return o.builddxmatrix(e)},buildymatrix:function(t){return o(t).transpose()},buildjymatrix:function(t){return t.transpose()},matrixmult:function(t,e){var n,r,i,s,a;if(t.cols()==e.rows()){if(1<e.rows())for(s=[],n=0;n<t.rows();n++)for(s[n]=[],r=0;r<e.cols();r++){for(i=a=0;i<t.cols();i++)a+=t.toArray()[n][i]*e.toArray()[i][r];s[n][r]=a}else for(s=[],n=0;n<t.rows();n++)for(s[n]=[],r=0;r<e.cols();r++){for(i=a=0;i<t.cols();i++)a+=t.toArray()[n][i]*e.toArray()[r];s[n][r]=a}return o(s)}},regress:function(t,e){var n=o.xtranspxinv(t);t=t.transpose(),n=o.matrixmult(o(n),t);return o.matrixmult(n,e)},regresst:function(t,e,n){for(var r,i,s,a=o.regress(t,e),u={anova:{}},c=o.jMatYBar(t,a),l=(u.yBar=c,e.mean()),h=(u.anova.residuals=o.residuals(e,c),u.anova.ssr=o.ssr(c,l),u.anova.msr=u.anova.ssr/(t[0].length-1),u.anova.sse=o.sse(e,c),u.anova.mse=u.anova.sse/(e.length-(t[0].length-1)-1),u.anova.sst=o.sst(e,l),u.anova.mst=u.anova.sst/(e.length-1),u.anova.r2=1-u.anova.sse/u.anova.sst,u.anova.r2<0&&(u.anova.r2=0),u.anova.fratio=u.anova.msr/u.anova.mse,u.anova.pvalue=o.anovaftest(u.anova.fratio,t[0].length-1,e.length-(t[0].length-1)-1),u.anova.rmse=Math.sqrt(u.anova.mse),u.anova.r2adj=1-u.anova.mse/u.anova.mst,u.anova.r2adj<0&&(u.anova.r2adj=0),u.stats=new Array(t[0].length),o.xtranspxinv(t)),f=0;f<a.length;f++)r=Math.sqrt(u.anova.mse*Math.abs(h[f][f])),i=Math.abs(a[f]/r),s=o.ttest(i,e.length-t[0].length-1,n),u.stats[f]=[a[f],r,i,s];return u.regress=a,u},xtranspx:function(t){return o.matrixmult(t.transpose(),t)},xtranspxinv:function(t){return t=o.matrixmult(t.transpose(),t),o.inv(t)},jMatYBar:function(t,e){return t=o.matrixmult(t,e),new o(t)},residuals:function(t,e){return o.matrixsubtract(t,e)},ssr:function(t,e){for(var n=0,r=0;r<t.length;r++)n+=Math.pow(t[r]-e,2);return n},sse:function(t,e){for(var n=0,r=0;r<t.length;r++)n+=Math.pow(t[r]-e[r],2);return n},sst:function(t,e){for(var n=0,r=0;r<t.length;r++)n+=Math.pow(t[r]-e,2);return n},matrixsubtract:function(t,e){for(var n=new Array(t.length),r=0;r<t.length;r++){n[r]=new Array(t[r].length);for(var i=0;i<t[r].length;i++)n[r][i]=t[r][i]-e[r][i]}return o(n)}}),o.jStat=o}));var CryptoApi=function(t){var e={};function n(r){var i;return(e[r]||(i=e[r]={i:r,l:!1,exports:{}},t[r].call(i.exports,i,i.exports,n),i.l=!0,i)).exports}return n.m=t,n.c=e,n.d=function(t,e,r){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:r})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var r=Object.create(null);if(n.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var i in t)n.d(r,i,function(e){return t[e]}.bind(null,i));return r},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=29)}([function(t,e,n){"use strict";function r(t,e){return t<<e|t>>>32-e}function i(t,e){return t>>>e|t<<32-e}function o(t,e,n){return 32===n?e:32<n?o(e,t,n-32):4294967295&(t>>>n|e<<32-n)}function s(t,e,n){return 32===n?t:32<n?s(e,t,n-32):4294967295&(e>>>n|t<<32-n)}n.d(e,"a",(function(){return r})),n.d(e,"b",(function(){return i})),n.d(e,"d",(function(){return s})),n.d(e,"c",(function(){return o}))},function(t,e,n){"use strict";function r(t){for(var e="",n=0,r=t.length;n<r;n++){var i=t.charCodeAt(n);i<128?e+=String.fromCharCode(i):(i<2048?e+=String.fromCharCode(192|i>>6):(i<55296||57344<=i?e+=String.fromCharCode(224|i>>12):(n++,i=65536+((1023&i)<<10|1023&t.charCodeAt(n)),e=(e+=String.fromCharCode(240|i>>18))+String.fromCharCode(128|i>>12&63)),e+=String.fromCharCode(128|i>>6&63)),e+=String.fromCharCode(128|63&i))}return e}n.d(e,"a",(function(){return r}))},function(t,e,n){"use strict";function r(t){for(var e="",n=0,r=t.length;n<r;n++)e+=(t.charCodeAt(n)<16?"0":"")+t.charCodeAt(n).toString(16);return e}n.d(e,"a",(function(){return r}))},function(t,e,n){"use strict";var r=n(4);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function s(t,e){return(s=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}n=function(){function t(e){if(this instanceof t)return(e=function(t,e){if(!e||"object"!==i(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,o(t).call(this,e))).unitOrder=1,e.blockUnits=[],e;throw new TypeError("Cannot call a class as a function")}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&s(e,n);e=[{key:"process",value:function(){for(;this.state.message.length>=this.blockSizeInBytes;){this.blockUnits=[];for(var t=0;t<this.blockSizeInBytes;t+=4)this.blockUnits.push(this.state.message.charCodeAt(t)<<24|this.state.message.charCodeAt(t+1)<<16|this.state.message.charCodeAt(t+2)<<8|this.state.message.charCodeAt(t+3));this.state.message=this.state.message.substr(this.blockSizeInBytes),this.processBlock(this.blockUnits)}}},{key:"processBlock",value:function(t){}},{key:"getStateHash",value:function(t){t=t||this.state.hash.length;for(var e="",n=0;n<t;n++)e+=String.fromCharCode(this.state.hash[n]>>24&255)+String.fromCharCode(this.state.hash[n]>>16&255)+String.fromCharCode(this.state.hash[n]>>8&255)+String.fromCharCode(255&this.state.hash[n]);return e}},{key:"addLengthBits",value:function(){this.state.message+="\0\0\0"+String.fromCharCode(this.state.length>>29&255)+String.fromCharCode(this.state.length>>21&255)+String.fromCharCode(this.state.length>>13&255)+String.fromCharCode(this.state.length>>5&255)+String.fromCharCode(this.state.length<<3&255)}}];for(var a=t.prototype,u=e,c=0;c<u.length;c++){var l=u[c];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(a,l.key,l)}return t}(),e.a=n},function(t,e,n){"use strict";var r=function(){function t(e){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");this.unitSize=4,this.unitOrder=0,this.blockSize=16,this.blockSizeInBytes=this.blockSize*this.unitSize,this.options=e||{},this.reset()}for(var e=[{key:"reset",value:function(){this.state={},this.state.message="",this.state.length=0}},{key:"getState",value:function(){return JSON.parse(JSON.stringify(this.state))}},{key:"setState",value:function(t){this.state=t}},{key:"update",value:function(t){this.state.message+=t,this.state.length+=t.length,this.process()}},{key:"process",value:function(){}},{key:"finalize",value:function(){return""}},{key:"getStateHash",value:function(t){return""}},{key:"addPaddingPKCS7",value:function(t){this.state.message+=new Array(t+1).join(String.fromCharCode(t))}},{key:"addPaddingISO7816",value:function(t){this.state.message+=""+new Array(t).join("\0")}},{key:"addPaddingZero",value:function(t){this.state.message+=new Array(t+1).join("\0")}}],n=t.prototype,r=e,i=0;i<r.length;i++){var o=r[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(n,o.key,o)}return t}();e.a=r},function(t,e,n){"use strict";var r=n(4);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function s(t,e){return(s=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}n=function(){function t(e){if(this instanceof t)return(e=function(t,e){if(!e||"object"!==i(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,o(t).call(this,e))).blockUnits=[],e;throw new TypeError("Cannot call a class as a function")}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&s(e,n);e=[{key:"process",value:function(){for(;this.state.message.length>=this.blockSizeInBytes;){this.blockUnits=[];for(var t=0;t<this.blockSizeInBytes;t+=4)this.blockUnits.push(this.state.message.charCodeAt(t)|this.state.message.charCodeAt(t+1)<<8|this.state.message.charCodeAt(t+2)<<16|this.state.message.charCodeAt(t+3)<<24);this.state.message=this.state.message.substr(this.blockSizeInBytes),this.processBlock(this.blockUnits)}}},{key:"processBlock",value:function(t){}},{key:"getStateHash",value:function(t){t=t||this.state.hash.length;for(var e="",n=0;n<t;n++)e+=String.fromCharCode(255&this.state.hash[n])+String.fromCharCode(this.state.hash[n]>>8&255)+String.fromCharCode(this.state.hash[n]>>16&255)+String.fromCharCode(this.state.hash[n]>>24&255);return e}},{key:"addLengthBits",value:function(){this.state.message+=String.fromCharCode(this.state.length<<3&255)+String.fromCharCode(this.state.length>>5&255)+String.fromCharCode(this.state.length>>13&255)+String.fromCharCode(this.state.length>>21&255)+String.fromCharCode(this.state.length>>29&255)+"\0\0\0"}}];for(var a=t.prototype,u=e,c=0;c<u.length;c++){var l=u[c];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(a,l.key,l)}return t}(),e.a=n},function(t,e,n){"use strict";var r=n(3),i=n(0);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e,n){return(s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=a(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var c=[1116352408,3609767458,1899447441,602891725,3049323471,3964484399,3921009573,2173295548,961987163,4081628472,1508970993,3053834265,2453635748,2937671579,2870763221,3664609560,3624381080,2734883394,310598401,1164996542,607225278,1323610764,1426881987,3590304994,1925078388,4068182383,2162078206,991336113,2614888103,633803317,3248222580,3479774868,3835390401,2666613458,4022224774,944711139,264347078,2341262773,604807628,2007800933,770255983,1495990901,1249150122,1856431235,1555081692,3175218132,1996064986,2198950837,2554220882,3999719339,2821834349,766784016,2952996808,2566594879,3210313671,3203337956,3336571891,1034457026,3584528711,2466948901,113926993,3758326383,338241895,168717936,666307205,1188179964,773529912,1546045734,1294757372,1522805485,1396182291,2643833823,1695183700,2343527390,1986661051,1014477480,2177026350,1206759142,2456956037,344077627,2730485921,1290863460,2820302411,3158454273,3259730800,3505952657,3345764771,106217008,3516065817,3606008344,3600352804,1432725776,4094571909,1467031594,275423344,851169720,430227734,3100823752,506948616,1363258195,659060556,3750685593,883997877,3785050280,958139571,3318307427,1322822218,3812723403,1537002063,2003034995,1747873779,3602036899,1955562222,1575990012,2024104815,1125592928,2227730452,2716904306,2361852424,442776044,2428436474,593698344,2756734187,3733110249,3204031479,2999351573,3329325298,3815920427,3391569614,3928383900,3515267271,566280711,3940187606,3454069534,4118630271,4000239992,116418474,1914138554,174292421,2731055270,289380356,3203993006,460393269,320620315,685471733,587496836,852142971,1086792851,1017036298,365543100,1126000580,2618297676,1288033470,3409855158,1501505948,4234509866,1607167915,987167468,1816402316,1246189591];n=function(){function t(e){if(this instanceof t)return(e=e||{}).length=e.length||512,e.rounds=e.rounds||160,(e=function(t,e){if(!e||"object"!==o(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,a(t).call(this,e))).blockSize=32,e.blockSizeInBytes=e.blockSize*e.unitSize,e.W=new Array(160),e;throw new TypeError("Cannot call a class as a function")}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&u(e,n);e=[{key:"reset",value:function(){switch(s(a(t.prototype),"reset",this).call(this),this.options.length){case 384:this.state.hash=[-876896931,-1056596264,1654270250,914150663,-1856437926,812702999,355462360,-150054599,1731405415,-4191439,-1900787065,1750603025,-619958771,1694076839,1203062813,-1090891868];break;case 512:this.state.hash=[1779033703,-205731576,-1150833019,-2067093701,1013904242,-23791573,-1521486534,1595750129,1359893119,-1377402159,-1694144372,725511199,528734635,-79577749,1541459225,327033209];break;default:for(var e=new t,n=0;n<16;n++)e.state.hash[n]=2779096485^e.state.hash[n];e.update("SHA-512/"+this.options.length);var r=e.finalize();this.state.hash=[];for(var i=0;i<64;i+=4)this.state.hash.push(r.charCodeAt(i)<<24|r.charCodeAt(i+1)<<16|r.charCodeAt(i+2)<<8|r.charCodeAt(i+3))}}},{key:"processBlock",value:function(t){for(var e=this.state.hash[0],n=this.state.hash[1],r=this.state.hash[2],o=this.state.hash[3],s=this.state.hash[4],a=this.state.hash[5],u=this.state.hash[6],l=this.state.hash[7],h=this.state.hash[8],f=this.state.hash[9],p=this.state.hash[10],d=this.state.hash[11],g=this.state.hash[12],m=this.state.hash[13],v=this.state.hash[14],y=this.state.hash[15],b=0;b<this.options.rounds;b+=2){b<32?(this.W[b]=t[b],this.W[b+1]=t[b+1]):(w=Object(i.c)(this.W[b-30],this.W[b-29],1)^Object(i.c)(this.W[b-30],this.W[b-29],8)^this.W[b-30]>>>7,_=Object(i.d)(this.W[b-30],this.W[b-29],1)^Object(i.d)(this.W[b-30],this.W[b-29],8)^(this.W[b-29]>>>7|this.W[b-30]<<25),x=Object(i.c)(this.W[b-4],this.W[b-3],19)^Object(i.c)(this.W[b-4],this.W[b-3],61)^this.W[b-4]>>>6,C=Object(i.d)(this.W[b-4],this.W[b-3],19)^Object(i.d)(this.W[b-4],this.W[b-3],61)^(this.W[b-3]>>>6|this.W[b-4]<<26),T=(65535&this.W[b-13])+(65535&this.W[b-31])+(65535&_)+(65535&C)|0,O=(this.W[b-13]>>>16)+(this.W[b-31]>>>16)+(_>>>16)+(C>>>16)+(T>>>16)|0,S=(65535&this.W[b-14])+(65535&this.W[b-32])+(65535&w)+(65535&x)+(O>>>16)|0,k=(this.W[b-14]>>>16)+(this.W[b-32]>>>16)+(w>>>16)+(x>>>16)+(S>>>16)|0,this.W[b]=4294967295&(k<<16|65535&S),this.W[b+1]=4294967295&(O<<16|65535&T)),w=Object(i.c)(e,n,28)^Object(i.c)(e,n,34)^Object(i.c)(e,n,39),_=Object(i.d)(e,n,28)^Object(i.d)(e,n,34)^Object(i.d)(e,n,39);var w,_,x,C,k=f&d^~f&m,S=e&r^e&s^r&s,O=n&o^n&a^o&a,E=((v+(x=Object(i.c)(h,f,14)^Object(i.c)(h,f,18)^Object(i.c)(h,f,41))+((T=y+(C=Object(i.d)(h,f,14)^Object(i.d)(h,f,18)^Object(i.d)(h,f,41))|0)>>>0<y>>>0?1:0)|0)+(h&p^~h&g)+((T=T+k|0)>>>0<k>>>0?1:0)|0)+c[b]+((T=T+c[b+1]|0)>>>0<c[b+1]>>>0?1:0)|0,T=T+this.W[b+1]|0,A=_+O|0;v=g,y=m,g=p,m=d,p=h,d=f,h=u+(E=E+this.W[b]+(T>>>0<this.W[b+1]>>>0?1:0)|0)+((f=l+T|0)>>>0<l>>>0?1:0)|0,u=s,l=a,s=r,a=o,r=e,o=n,e=E+(w+S+(A>>>0<_>>>0?1:0)|0)+((n=T+A|0)>>>0<T>>>0?1:0)|0}this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[0]=this.state.hash[0]+e+(this.state.hash[1]>>>0<n>>>0?1:0)|0,this.state.hash[3]=this.state.hash[3]+o|0,this.state.hash[2]=this.state.hash[2]+r+(this.state.hash[3]>>>0<o>>>0?1:0)|0,this.state.hash[5]=this.state.hash[5]+a|0,this.state.hash[4]=this.state.hash[4]+s+(this.state.hash[5]>>>0<a>>>0?1:0)|0,this.state.hash[7]=this.state.hash[7]+l|0,this.state.hash[6]=this.state.hash[6]+u+(this.state.hash[7]>>>0<l>>>0?1:0)|0,this.state.hash[9]=this.state.hash[9]+f|0,this.state.hash[8]=this.state.hash[8]+h+(this.state.hash[9]>>>0<f>>>0?1:0)|0,this.state.hash[11]=this.state.hash[11]+d|0,this.state.hash[10]=this.state.hash[10]+p+(this.state.hash[11]>>>0<d>>>0?1:0)|0,this.state.hash[13]=this.state.hash[13]+m|0,this.state.hash[12]=this.state.hash[12]+g+(this.state.hash[13]>>>0<m>>>0?1:0)|0,this.state.hash[15]=this.state.hash[15]+y|0,this.state.hash[14]=this.state.hash[14]+v+(this.state.hash[15]>>>0<y>>>0?1:0)|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<112?112-this.state.message.length|0:240-this.state.message.length|0),this.state.message+="\0\0\0\0\0\0\0\0",this.addLengthBits(),this.process(),this.getStateHash(this.options.length/32|0)}}];for(var l=t.prototype,h=e,f=0;f<h.length;f++){var p=h[f];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(l,p.key,p)}return t}();e.a=n},function(t,e,n){"use strict";var r=n(3),i=n(0);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e,n){return(s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=a(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var c=[1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298];n=function(){function t(e){if(this instanceof t)return(e=e||{}).length=e.length||256,e.rounds=e.rounds||64,(e=function(t,e){if(!e||"object"!==o(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,a(t).call(this,e))).W=new Array(64),e;throw new TypeError("Cannot call a class as a function")}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&u(e,n);e=[{key:"reset",value:function(){224===(s(a(t.prototype),"reset",this).call(this),this.options.length)?this.state.hash=[-1056596264,914150663,812702999,-150054599,-4191439,1750603025,1694076839,-1090891868]:this.state.hash=[1779033703,-1150833019,1013904242,-1521486534,1359893119,-1694144372,528734635,1541459225]}},{key:"processBlock",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],o=0|this.state.hash[3],s=0|this.state.hash[4],a=0|this.state.hash[5],u=0|this.state.hash[6],l=0|this.state.hash[7],h=0;h<this.options.rounds;h++){this.W[h]=h<16?0|t[h]:this.W[h-16]+(Object(i.b)(this.W[h-15],7)^Object(i.b)(this.W[h-15],18)^this.W[h-15]>>>3)+this.W[h-7]+(Object(i.b)(this.W[h-2],17)^Object(i.b)(this.W[h-2],19)^this.W[h-2]>>>10)|0;var f=l+(Object(i.b)(s,6)^Object(i.b)(s,11)^Object(i.b)(s,25))+(s&a^~s&u)+c[h]+this.W[h]|0,p=(Object(i.b)(e,2)^Object(i.b)(e,13)^Object(i.b)(e,22))+(e&n^e&r^n&r)|0;l=u,u=a,a=s,s=o+f|0,o=r,r=n,n=e,e=f+p|0}this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+o|0,this.state.hash[4]=this.state.hash[4]+s|0,this.state.hash[5]=this.state.hash[5]+a|0,this.state.hash[6]=this.state.hash[6]+u|0,this.state.hash[7]=this.state.hash[7]+l|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash(this.options.length/32|0)}}];for(var l=t.prototype,h=e,f=0;f<h.length;f++){var p=h[f];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(l,p.key,p)}return t}();e.a=n},function(t,e,n){"use strict";var r=n(5),i=n(0);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function a(t,e,n){return(a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=u(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function c(t,e){return(c=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var l=[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13],h=[5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11],f=[11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6],p=[8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11];n=function(){function t(e){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");(e=e||{}).length=e.length||160;var n=this;e=u(t).call(this,e);if(!e||"object"!==o(e)&&"function"!=typeof e){if(void 0!==n)return n;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");return e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&c(e,n),e=[{key:"F",value:function(t,e,n){return t^e^n}},{key:"G",value:function(t,e,n){return t&e|~t&n}},{key:"H",value:function(t,e,n){return(t|~e)^n}},{key:"I",value:function(t,e,n){return t&n|e&~n}},{key:"J",value:function(t,e,n){return t^(e|~n)}},{key:"T",value:function(t,e,n,r){return t<16?this.F(e,n,r):t<32?this.G(e,n,r)+1518500249|0:t<48?this.H(e,n,r)+1859775393|0:t<64?this.I(e,n,r)+2400959708|0:this.J(e,n,r)+2840853838|0}},{key:"T64",value:function(t,e,n,r){return t<16?this.I(e,n,r)+1352829926|0:t<32?this.H(e,n,r)+1548603684|0:t<48?this.G(e,n,r)+1836072691|0:this.F(e,n,r)}},{key:"T80",value:function(t,e,n,r){return t<16?this.J(e,n,r)+1352829926|0:t<32?this.I(e,n,r)+1548603684|0:t<48?this.H(e,n,r)+1836072691|0:t<64?this.G(e,n,r)+2053994217|0:this.F(e,n,r)}}],s((n=t).prototype,[{key:"reset",value:function(){switch(a(u(t.prototype),"reset",this).call(this),this.options.length){case 128:this.state.hash=[1732584193,4023233417,2562383102,271733878],this.processBlock=this.processBlock128;break;case 256:this.state.hash=[1732584193,4023233417,2562383102,271733878,1985229328,4275878552,2309737967,19088743],this.processBlock=this.processBlock256;break;case 320:this.state.hash=[1732584193,4023233417,2562383102,271733878,3285377520,1985229328,4275878552,2309737967,19088743,1009589775],this.processBlock=this.processBlock320;break;default:this.state.hash=[1732584193,4023233417,2562383102,271733878,3285377520],this.processBlock=this.processBlock160}}},{key:"processBlock128",value:function(e){for(var n=c=0|this.state.hash[0],r=m=0|this.state.hash[1],o=g=0|this.state.hash[2],s=d=0|this.state.hash[3],a=0;a<64;a++){var u=(c+e[l[a]]|0)+t.T(a,m,g,d)|0,c=d,d=g,g=m,m=Object(i.a)(u,f[a]);u=(n+e[h[a]]|0)+t.T64(a,r,o,s)|0,n=s,s=o,o=r,r=Object(i.a)(u,p[a])}var v=this.state.hash[1]+g+s|0;this.state.hash[1]=this.state.hash[2]+d+n|0,this.state.hash[2]=this.state.hash[3]+c+r|0,this.state.hash[3]=this.state.hash[0]+m+o|0,this.state.hash[0]=v}},{key:"processBlock160",value:function(e){for(var n=0|this.state.hash[0],r=0|this.state.hash[1],o=0|this.state.hash[2],s=0|this.state.hash[3],a=0|this.state.hash[4],u=n,c=r,d=o,g=s,m=a,v=0;v<80;v++){var y=(n+e[l[v]]|0)+t.T(v,r,o,s)|0;y=Object(i.a)(y,f[v])+a|0,n=a,a=s,s=Object(i.a)(o,10),o=r,r=y,y=(u+e[h[v]]|0)+t.T80(v,c,d,g)|0,y=Object(i.a)(y,p[v])+m|0,u=m,m=g,g=Object(i.a)(d,10),d=c,c=y}var b=this.state.hash[1]+o+g|0;this.state.hash[1]=this.state.hash[2]+s+m|0,this.state.hash[2]=this.state.hash[3]+a+u|0,this.state.hash[3]=this.state.hash[4]+n+c|0,this.state.hash[4]=this.state.hash[0]+r+d|0,this.state.hash[0]=b}},{key:"processBlock256",value:function(e){for(var n=0|this.state.hash[0],r=0|this.state.hash[1],o=0|this.state.hash[2],s=0|this.state.hash[3],a=0|this.state.hash[4],u=0|this.state.hash[5],c=0|this.state.hash[6],d=0|this.state.hash[7],g=0;g<64;g+=1){var m=(m=n+e[l[g]]|0)+t.T(g,r,o,s)|0;n=s,s=o,o=r,r=m=Object(i.a)(m,f[g]);switch(m=(m=a+e[h[g]]|0)+t.T64(g,u,c,d)|0,a=d,d=c,c=u,u=m=Object(i.a)(m,p[g]),g){case 15:m=n,n=a,a=m;break;case 31:m=r,r=u,u=m;break;case 47:m=o,o=c,c=m;break;case 63:m=s,s=d,d=m}}this.state.hash[0]=this.state.hash[0]+n|0,this.state.hash[1]=this.state.hash[1]+r|0,this.state.hash[2]=this.state.hash[2]+o|0,this.state.hash[3]=this.state.hash[3]+s|0,this.state.hash[4]=this.state.hash[4]+a|0,this.state.hash[5]=this.state.hash[5]+u|0,this.state.hash[6]=this.state.hash[6]+c|0,this.state.hash[7]=this.state.hash[7]+d|0}},{key:"processBlock320",value:function(e){for(var n=0|this.state.hash[0],r=0|this.state.hash[1],o=0|this.state.hash[2],s=0|this.state.hash[3],a=0|this.state.hash[4],u=0|this.state.hash[5],c=0|this.state.hash[6],d=0|this.state.hash[7],g=0|this.state.hash[8],m=0|this.state.hash[9],v=0;v<80;v+=1){var y=(y=n+e[l[v]]|0)+t.T(v,r,o,s)|0;switch(y=(y=Object(i.a)(y,f[v]))+a|0,n=a,a=s,s=Object(i.a)(o,10),o=r,r=y,y=(y=u+e[h[v]]|0)+t.T80(v,c,d,g)|0,y=(y=Object(i.a)(y,p[v]))+m|0,u=m,m=g,g=Object(i.a)(d,10),d=c,c=y,v){case 15:y=r,r=c,c=y;break;case 31:y=s,s=g,g=y;break;case 47:y=n,n=u,u=y;break;case 63:y=o,o=d,d=y;break;case 79:y=a,a=m,m=y}}this.state.hash[0]=this.state.hash[0]+n|0,this.state.hash[1]=this.state.hash[1]+r|0,this.state.hash[2]=this.state.hash[2]+o|0,this.state.hash[3]=this.state.hash[3]+s|0,this.state.hash[4]=this.state.hash[4]+a|0,this.state.hash[5]=this.state.hash[5]+u|0,this.state.hash[6]=this.state.hash[6]+c|0,this.state.hash[7]=this.state.hash[7]+d|0,this.state.hash[8]=this.state.hash[8]+g|0,this.state.hash[9]=this.state.hash[9]+m|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}]),s(n,e),t}();e.a=n},function(t,e,n){"use strict";var r=n(3),i=n(0);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e,n){return(s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=a(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}for(var c=[10097,32533,76520,13586,34673,54876,80959,9117,39292,74945,37542,4805,64894,74296,24805,24037,20636,10402,822,91665,8422,68953,19645,9303,23209,2560,15953,34764,35080,33606,99019,2529,9376,70715,38311,31165,88676,74397,4436,27659,12807,99970,80157,36147,64032,36653,98951,16877,12171,76833,66065,74717,34072,76850,36697,36170,65813,39885,11199,29170,31060,10805,45571,82406,35303,42614,86799,7439,23403,9732,85269,77602,2051,65692,68665,74818,73053,85247,18623,88579,63573,32135,5325,47048,90553,57548,28468,28709,83491,25624,73796,45753,3529,64778,35808,34282,60935,20344,35273,88435,98520,17767,14905,68607,22109,40558,60970,93433,50500,73998,11805,5431,39808,27732,50725,68248,29405,24201,52775,67851,83452,99634,6288,98083,13746,70078,18475,40610,68711,77817,88685,40200,86507,58401,36766,67951,90364,76493,29609,11062,99594,67348,87517,64969,91826,8928,93785,61368,23478,34113,65481,17674,17468,50950,58047,76974,73039,57186,40218,16544,80124,35635,17727,8015,45318,22374,21115,78253,14385,53763,74350,99817,77402,77214,43236,210,45521,64237,96286,2655,69916,26803,66252,29148,36936,87203,76621,13990,94400,56418,9893,20505,14225,68514,46427,56788,96297,78822,54382,14598,91499,14523,68479,27686,46162,83554,94750,89923,37089,20048,80336,94598,26940,36858,70297,34135,53140,33340,42050,82341,44104,81949,85157,47954,32979,26575,57600,40881,22222,6413,12550,73742,11100,2040,12860,74697,96644,89439,28707,25815,63606,49329,16505,34484,40219,52563,43651,77082,7207,31790,61196,90446,26457,47774,51924,33729,65394,59593,42582,60527,15474,45266,95270,79953,59367,83848,82396,10118,33211,59466,94557,28573,67897,54387,54622,44431,91190,42592,92927,45973,42481,16213,97344,8721,16868,48767,3071,12059,25701,46670,23523,78317,73208,89837,68935,91416,26252,29663,5522,82562,4493,52494,75246,33824,45862,51025,61962,79335,65337,12472,549,97654,64051,88159,96119,63896,54692,82391,23287,29529,35963,15307,26898,9354,33351,35462,77974,50024,90103,39333,59808,8391,45427,26842,83609,49700,13021,24892,78565,20106,46058,85236,1390,92286,77281,44077,93910,83647,70617,42941,32179,597,87379,25241,5567,7007,86743,17157,85394,11838,69234,61406,20117,45204,15956,6e4,18743,92423,97118,96338,19565,41430,1758,75379,40419,21585,66674,36806,84962,85207,45155,14938,19476,7246,43667,94543,59047,90033,20826,69541,94864,31994,36168,10851,34888,81553,1540,35456,5014,51176,98086,24826,45240,28404,44999,8896,39094,73407,35441,31880,33185,16232,41941,50949,89435,48581,88695,41994,37548,73043,80951,406,96382,70774,20151,23387,25016,25298,94624,61171,79752,49140,71961,28296,69861,2591,74852,20539,387,59579,18633,32537,98145,6571,31010,24674,5455,61427,77938,91936,74029,43902,77557,32270,97790,17119,52527,58021,80814,51748,54178,45611,80993,37143,5335,12969,56127,19255,36040,90324,11664,49883,52079,84827,59381,71539,9973,33440,88461,23356,48324,77928,31249,64710,2295,36870,32307,57546,15020,9994,69074,94138,87637,91976,35584,4401,10518,21615,1848,76938,9188,20097,32825,39527,4220,86304,83389,87374,64278,58044,90045,85497,51981,50654,94938,81997,91870,76150,68476,64659,73189,50207,47677,26269,62290,64464,27124,67018,41361,82760,75768,76490,20971,87749,90429,12272,95375,5871,93823,43178,54016,44056,66281,31003,682,27398,20714,53295,7706,17813,8358,69910,78542,42785,13661,58873,4618,97553,31223,8420,28306,3264,81333,10591,40510,7893,32604,60475,94119,1840,53840,86233,81594,13628,51215,90290,28466,68795,77762,20791,91757,53741,61613,62269,50263,90212,55781,76514,83483,47055,89415,92694,397,58391,12607,17646,48949,72306,94541,37408,77513,3820,86864,29901,68414,82774,51908,13980,72893,55507,19502,37174,69979,20288,55210,29773,74287,75251,65344,67415,21818,59313,93278,81757,5686,73156,7082,85046,31853,38452,51474,66499,68107,23621,94049,91345,42836,9191,8007,45449,99559,68331,62535,24170,69777,12830,74819,78142,43860,72834,33713,48007,93584,72869,51926,64721,58303,29822,93174,93972,85274,86893,11303,22970,28834,34137,73515,90400,71148,43643,84133,89640,44035,52166,73852,70091,61222,60561,62327,18423,56732,16234,17395,96131,10123,91622,85496,57560,81604,18880,65138,56806,87648,85261,34313,65861,45875,21069,85644,47277,38001,2176,81719,11711,71602,92937,74219,64049,65584,49698,37402,96397,1304,77586,56271,10086,47324,62605,40030,37438,97125,40348,87083,31417,21815,39250,75237,62047,15501,29578,21826,41134,47143,34072,64638,85902,49139,6441,3856,54552,73135,42742,95719,9035,85794,74296,8789,88156,64691,19202,7638,77929,3061,18072,96207,44156,23821,99538,4713,66994,60528,83441,7954,19814,59175,20695,5533,52139,61212,6455,83596,35655,6958,92983,5128,9719,77433,53783,92301,50498,10850,62746,99599,10507,13499,6319,53075,71839,6410,19362,39820,98952,43622,63147,64421,80814,43800,9351,31024,73167,59580,6478,75569,78800,88835,54486,23768,6156,4111,8408,38508,7341,23793,48763,90822,97022,17719,4207,95954,49953,30692,70668,94688,16127,56196,80091,82067,63400,5462,69200,65443,95659,18288,27437,49632,24041,8337,65676,96299,90836,27267,50264,13192,72294,7477,44606,17985,48911,97341,30358,91307,6991,19072,24210,36699,53728,28825,35793,28976,66252,68434,94688,84473,13622,62126,98408,12843,82590,9815,93146,48908,15877,54745,24591,35700,4754,83824,52692,54130,55160,6913,45197,42672,78601,11883,9528,63011,98901,14974,40344,10455,16019,14210,33712,91342,37821,88325,80851,43667,70883,12883,97343,65027,61184,4285,1392,17974,15077,90712,26769,21778,30976,38807,36961,31649,42096,63281,2023,8816,47449,19523,59515,65122,59659,86283,68258,69572,13798,16435,91529,67245,52670,35583,16563,79246,86686,76463,34222,26655,90802,60584,47377,7500,37992,45134,26529,26760,83637,41326,44344,53853,41377,36066,94850,58838,73859,49364,73331,96240,43642,24637,38736,74384,89342,52623,7992,12369,18601,3742,83873,83080,12451,38992,22815,7759,51777,97377,27585,51972,37867,16444,24334,36151,99073,27493,70939,85130,32552,54846,54759,60790,18157,57178,65762,11161,78576,45819,52979,65130,4860,3991,10461,93716,16894,66083,24653,84609,58232,88618,19161,38555,95554,32886,59780,8355,60860,29735,47762,71299,23853,17546,73704,92052,46215,55121,29281,59076,7936,27954,58909,32643,52861,95819,6831,911,98936,76355,93779,80863,514,69572,68777,39510,35905,14060,40619,29549,69616,33564,60780,24122,66591,27699,6494,14845,46672,61958,77100,90899,75754,61196,30231,92962,61773,41839,55382,17267,70943,78038,70267,30532,21704,10274,12202,39685,23309,10061,68829,55986,66485,3788,97599,75867,20717,74416,53166,35208,33374,87539,8823,48228,63379,85783,47619,53152,67433,35663,52972,16818,60311,60365,94653,35075,33949,42614,29297,1918,28316,98953,73231,83799,42402,56623,34442,34994,41374,70071,14736,9958,18065,32960,7405,36409,83232,99385,41600,11133,7586,15917,6253,19322,53845,57620,52606,66497,68646,78138,66559,19640,99413,11220,94747,7399,37408,48509,23929,27482,45476,85244,35159,31751,57260,68980,5339,15470,48355,88651,22596,3152,19121,88492,99382,14454,4504,20094,98977,74843,93413,22109,78508,30934,47744,7481,83828,73788,6533,28597,20405,94205,20380,22888,48893,27499,98748,60530,45128,74022,84617,82037,10268,78212,16993,35902,91386,44372,15486,65741,14014,87481,37220,41849,84547,46850,52326,34677,58300,74910,64345,19325,81549,46352,33049,69248,93460,45305,7521,61318,31855,14413,70951,11087,96294,14013,31792,59747,67277,76503,34513,39663,77544,52701,8337,56303,87315,16520,69676,11654,99893,2181,68161,57275,36898,81304,48585,68652,27376,92852,55866,88448,3584,20857,73156,70284,24326,79375,95220,1159,63267,10622,48391,15633,84924,90415,93614,33521,26665,55823,47641,86225,31704,92694,48297,39904,2115,59589,49067,66821,41575,49767,4037,77613,19019,88152,80,20554,91409,96277,48257,50816,97616,38688,32486,45134,63545,59404,72059,43947,51680,43852,59693,25163,1889,70014,15021,41290,67312,71857,15957,68971,11403,65251,7629,37239,33295,5870,1119,92784,26340,18477,65622,36815,43625,18637,37509,82444,99005,4921,73701,14707,93997,64397,11692,5327,82162,20247,81759,45197,25332,83745,22567,4515,25624,95096,67946,48460,85558,15191,18782,16930,33361,83761,60873,43253,84145,60833,25983,1291,41349,20368,7126,14387,6345,80854,9279,43529,6318,38384,74761,41196,37480,51321,92246,80088,77074,88722,56736,66164,49431,66919,31678,72472,8,80890,18002,94813,31900,54155,83436,35352,54131,5466,55306,93128,18464,74457,90561,72848,11834,79982,68416,39528,72484,82474,25593,48545,35247,18619,13674,18611,19241,81616,18711,53342,44276,75122,11724,74627,73707,58319,15997,7586,16120,82641,22820,92904,13141,32392,19763,61199,67940,90767,4235,13574,17200,69902,63742,78464,22501,18627,90872,40188,28193,29593,88627,94972,11598,62095,36787,441,58997,34414,82157,86887,55087,19152,23,12302,80783,32624,68691,63439,75363,44989,16822,36024,867,76378,41605,65961,73488,67049,9070,93399,45547,94458,74284,5041,49807,20288,34060,79495,4146,52162,90286,54158,34243,46978,35482,59362,95938,91704,30552,4737,21031,75051,93029,47665,64382,99782,93478,94015,46874,32444,48277,59820,96163,64654,25843,41145,42820,74108,88222,88570,74015,25704,91035,1755,14750,48968,38603,62880,87873,95160,59221,22304,90314,72877,17334,39283,4149,11748,12102,80580,41867,17710,59621,6554,7850,73950,79552,17944,5600,60478,3343,25852,58905,57216,39618,49856,99326,66067,42792,95043,52680,46780,56487,9971,59481,37006,22186,54244,91030,45547,70818,59849,96169,61459,21647,87417,17198,30945,57589,31732,57260,47670,7654,46376,25366,94746,49580,69170,37403,86995,90307,94304,71803,26825,5511,12459,91314,8345,88975,35841,85771,8105,59987,87112,21476,14713,71181,27767,43584,85301,88977,29490,69714,73035,41207,74699,9310,13025,14338,54066,15243,47724,66733,47431,43905,31048,56699,80217,36292,98525,24335,24432,24896,43277,58874,11466,16082,10875,62004,90391,61105,57411,6368,53856,30743,8670,84741,54127,57326,26629,19087,24472,88779,30540,27886,61732,75454,60311,42824,37301,42678,45990,43242,17374,52003,70707,70214,49739,71484,92003,98086,76668,73209,59202,11973,2902,33250,78626,51594,16453,94614,39014,97066,83012,9832,25571,77628,66692,13986,99837,582,81232,44987,9504,96412,90193,79568,44071,28091,7362,97703,76447,42537,98524,97831,65704,9514,41468,85149,49554,17994,14924,39650,95294,556,70481,6905,94559,37559,49678,53119,70312,5682,66986,34099,74474,20740,41615,70360,64114,58660,90850,64618,80620,51790,11436,38072,50273,93113,41794,86861,24781,89683,55411,85667,77535,99892,41396,80504,90670,8289,40902,5069,95083,6783,28102,57816,25807,24260,71529,78920,72682,7385,90726,57166,98884,8583,6170,97965,88302,98041,21443,41808,68984,83620,89747,98882,60808,54444,74412,81105,1176,28838,36421,16489,18059,51061,80940,44893,10408,36222,80582,71944,92638,40333,67054,16067,19516,90120,46759,71643,13177,55292,21036,82808,77501,97427,49386,54480,23604,23554,21785,41101,91178,10174,29420,90438,6312,88940,15995,69321,47458,64809,98189,81851,29651,84215,60942,307,11897,92674,40405,68032,96717,54244,10701,41393,92329,98932,78284,46347,71209,92061,39448,93136,25722,8564,77936,63574,31384,51924,85561,29671,58137,17820,22751,36518,38101,77756,11657,13897,95889,57067,47648,13885,70669,93406,39641,69457,91339,22502,92613,89719,11947,56203,19324,20504,84054,40455,99396,63680,67667,60631,69181,96845,38525,11600,47468,3577,57649,63266,24700,71594,14004,23153,69249,5747,43321,31370,28977,23896,76479,68562,62342,7589,8899,5985,64281,61826,18555,64937,13173,33365,78851,16499,87064,13075,66847,70495,32350,2985,86716,38746,26313,77463,55387,72681,72461,33230,21529,53424,92581,2262,78438,66276,18396,73538,21032,91050,13058,16218,12470,56500,15292,76139,59526,52113,95362,67011,6651,16136,1016,857,55018,56374,35824,71708,49712,97380,10404,55452,34030,60726,75211,10271,36633,68424,58275,61764,97586,54716,50259,46345,87195,46092,26787,60939,89514,11788,68224,23417,73959,76145,30342,40277,11049,72049,15472,50669,48139,36732,46874,37088,73465,9819,58869,35220,12120,86124,51247,44302,60883,52109,21437,36786,49226,77837,19612,78430,11661,94770,77603,65669,86868,12665,30012,75989,39141,77400,28e3,64238,73258,71794,31340,26256,66453,37016,64756,80457,8747,12836,3469,50678,3274,43423,66677,82556,92901,51878,56441,22998,29718,38447,6453,25311,7565,53771,3551,90070,9483,94050,45938,18135,36908,43321,11073,51803,98884,66209,6830,53656,14663,56346,71430,4909,19818,5707,27369,86882,53473,7541,53633,70863,3748,12822,19360,49088,59066,75974,63335,20483,43514,37481,58278,26967,49325,43951,91647,93783,64169,49022,98588,9495,49829,59068,38831,4838,83605,92419,39542,7772,71568,75673,35185,89759,44901,74291,24895,88530,70774,35439,46758,70472,70207,92675,91623,61275,35720,26556,95596,20094,73750,85788,34264,1703,46833,65248,14141,53410,38649,6343,57256,61342,72709,75318,90379,37562,27416,75670,92176,72535,93119,56077,6886,18244,92344,31374,82071,7429,81007,47749,40744,56974,23336,88821,53841,10536,21445,82793,24831,93241,14199,76268,70883,68002,3829,17443,72513,76400,52225,92348,62308,98481,29744,33165,33141,61020,71479,45027,76160,57411,13780,13632,52308,77762,88874,33697,83210,51466,9088,50395,26743,5306,21706,70001,99439,80767,68749,95148,94897,78636,96750,9024,94538,91143,96693,61886,5184,75763,47075,88158,5313,53439,14908,8830,60096,21551,13651,62546,96892,25240,47511,58483,87342,78818,7855,39269,566,21220,292,24069,25072,29519,52548,54091,21282,21296,50958,17695,58072,68990,60329,95955,71586,63417,35947,67807,57621,64547,46850,37981,38527,9037,64756,3324,4986,83666,9282,25844,79139,78435,35428,43561,69799,63314,12991,93516,23394,94206,93432,37836,94919,26846,2555,74410,94915,48199,5280,37470,93622,4345,15092,19510,18094,16613,78234,50001,95491,97976,38306,32192,82639,54624,72434,92606,23191,74693,78521,104,18248,75583,90326,50785,54034,66251,35774,14692,96345,44579,85932,44053,75704,20840,86583,83944,52456,73766,77963,31151,32364,91691,47357,40338,23435,24065,8458,95366,7520,11294,23238,1748,41690,67328,54814,37777,10057,42332,38423,2309,70703,85736,46148,14258,29236,12152,5088,65825,2463,65533,21199,60555,33928,1817,7396,89215,30722,22102,15880,92261,17292,88190,61781,48898,92525,21283,88581,60098,71926,819,59144,224,30570,90194,18329,6999,26857,19238,64425,28108,16554,16016,42,83229,10333,36168,65617,94834,79782,23924,49440,30432,81077,31543,95216,64865,13658,51081,35337,74538,44553,64672,90960,41849,93865,44608,93176,34851,5249,29329,19715,94082,14738,86667,43708,66354,93692,25527,56463,99380,38793,85774,19056,13939,46062,27647,66146,63210,96296,33121,54196,34108,75814,85986,71171,15102,28992,63165,98380,36269,60014,7201,62448,46385,42175,88350,46182,49126,52567,64350,16315,53969,80395,81114,54358,64578,47269,15747,78498,90830,25955,99236,43286,91064,99969,95144,64424,77377,49553,24241,8150,89535,8703,91041,77323,81079,45127,93686,32151,7075,83155,10252,73100,88618,23891,87418,45417,20268,11314,50363,26860,27799,49416,83534,19187,8059,76677,2110,12364,71210,87052,50241,90785,97889,81399,58130,64439,5614,59467,58309,87834,57213,37510,33689,1259,62486,56320,46265,73452,17619,56421,40725,23439,41701,93223,41682,45026,47505,27635,56293,91700,4391,67317,89604,73020,69853,61517,51207,86040,2596,1655,9918,45161,222,54577,74821,47335,8582,52403,94255,26351,46527,68224,90183,85057,72310,34963,83462,49465,46581,61499,4844,94626,2963,41482,83879,44942,63915,94365,92560,12363,30246,2086,75036,88620,91088,67691,67762,34261,8769,91830,23313,18256,28850,37639,92748,57791,71328,37110,66538,39318,15626,44324,82827,8782,65960,58167,1305,83950,45424,72453,19444,68219,64733,94088,62006,89985,36936,61630,97966,76537,46467,30942,7479,67971,14558,22458,35148,1929,17165,12037,74558,16250,71750,55546,29693,94984,37782,41659,39098,23982,29899,71594,77979,54477,13764,17315,72893,32031,39608,75992,73445,1317,50525,87313,45191,30214,19769,90043,93478,58044,6949,31176,88370,50274,83987,45316,38551,79418,14322,91065,7841,36130,86602,10659,40859,964,71577,85447,61079,96910,72906,7361,84338,34114,52096,66715,51091,86219,81115,49625,48799,89485,24855,13684,68433,70595,70102,71712,88559,92476,32903,68009,58417,87962,11787,16644,72964,29776,63075,13270,84758,49560,10317,28778,23006,31036,84906,81488,17340,74154,42801,27917,89792,62604,62234,13124,76471,51667,37589,87147,24743,48023,6325,79794,35889,13255,4925,99004,70322,60832,76636,56907,56534,72615,46288,36788,93196,68656,66492,35933,52293,47953,95495,95304,50009,83464,28608,38074,74083,9337,7965,65047,36871,59015,21769,30398,44855,1020,80680,59328,8712,48190,45332,27284,31287,66011,9376,86379,74508,33579,77114,92955,23085,92824,3054,25242,16322,48498,9938,44420,13484,52319,58875,2012,88591,52500,95795,41800,95363,54142,17482,32705,60564,12505,40954,46174,64130,63026,96712,79883,39225,52653,69549,36693,59822,22684,31661,88298,15489,16030,42480,15372,38781,71995,77438,91161,10192,7839,62735,99218,25624,2547,27445,69187,55749,32322,15504,73298,51108,48717,92926,75705,89787,96114,99902,37749,96305,12829,70474,838,50385,91711,80370,56504,56857,80906,9018,76569,61072,48568,36491,22587,44363,39592,61546,90181,37348,41665,41339,62106,44203,6732,76111,79840,67999,32231,76869,58652,49983,1669,27464,79553,52855,25988,18087,38052,17529,13607,657,76173,43357,77334,24140,53860,2906,89863,44651,55715,26203,65933,51087,98234,40625,45545,63563,89148,82581,4110,66683,99001,9796,47349,65003,66524,81970,71262,14479,31300,8681,58068,44115,40064,77879,23965,69019,73985,19453,26225,97543,37044,7494,85778,35345,61115,92498,49737,64599,7158,82763,25072,38478,57782,75291,62155,52056,4786,11585,71251,25572,79771,93328,66927,54069,58752,26624,50463,77361,29991,96526,2820,91659,12818,96356,49499,1507,40223,9171,83642,21057,2677,9367,38097,16100,19355,6120,15378,56559,69167,30235,6767,66323,78294,14916,19124,88044,16673,66102,86018,29406,75415,22038,27056,26906,25867,14751,92380,30434,44114,6026,79553,55091,95385,41212,37882,46864,54717,97038,53805,64150,70915,63127,63695,41288,38192,72437,75075,18570,52065,8853,30104,79937,66913,53200,84570,78079,28970,53859,37632,80274,35240,32960,74859,7359,55176,3930,38984,35151,82576,82805,94031,12779,90879,24109,25367,77861,9541,85739,69023,64971,99321,7521,95909,43897,71724,92581,5471,64337,98949,3606,78236,78985,29212,57369,34857,67757,58019,58872,96526,28749,56592,37871,72905,70198,57319,54116,47014,18285,33692,72111,60958,96848,17893,40993,50445,14186,76877,87867,50335,9513,44346,26439,55293,6449,44301,63740,40158,72703,88321,85062,57345,66231,15409,3451,95261,43561,15673,28956,90303,62469,82517,43035,36850,15592,64098,59022,31752,4370,50486,11885,23085,41712,80692,48492,16495,99721,36912,28267,27882,16269,64483,11273,2680,1616,46138,54606,14761,5134,45144,63213,49666,27441,86989,29884,54334,6740,8368,80051,81020,17882,74973,74531,94994,24927,64894,22667,20466,82948,66831,47427,76033,31197,59817,20064,61135,28556,29695,80179,74058,18293,9963,35278,13062,83094,23373,90287,33477,48865,30348,70174,11468,25994,25343,22317,1587,30682,1,67814,59557,23362,13746,82244,42093,24671,79458,93730,45488,60234,67098,9899,25775,332,36636,57594,19958,85564,58977,12247,60774,66371,69442,20385,14486,91330,50332,46023,75768,59877,60081,92936,72302,75064,85727,52987,5750,19384,33684,78859,80458,69902,34870,88684,49762,40801,86291,18194,90366,82639,53844,96326,65728,48563,26027,52692,62406,76294,41848,63010,69841,29451,36170,21529,16525,64326,22086,24469,57407,96033,37771,31002,18311,93285,31948,14331,58335,15977,80336,81667,27286,24361,61638,57580,95270,46180,76990,53031,94366,2727,49944,19278,5756,51875,53445,33342,1965,7937,10054,97712,87693,58124,46064,39133,77385,9605,65359,70113,90563,86637,94282,12025,31926,24541,23854,58407,32131,92845,20714,27898,26917,50326,35145,50859,72119,95094,29441,42301,62460,75252,94267,38422,73047,24200,85349,72049,91723,97802,98496,12734,73432,10371,57213,53300,80847,46229,7099,72961,13767,65654,31102,82119,96946,65919,81083,3819,57888,57908,16849,77111,41429,92261,45263,1172,55926,78835,27697,48420,58865,41207,21406,8582,10785,36233,12237,7866,13706,92551,11021,63813,71512,65206,37768,94325,14721,20990,54235,71986,5345,56239,52028,1419,7215,55067,11669,21738,66605,69621,69827,8537,18638,60982,28151,98885,76431,25566,3085,23639,30849,63986,73287,26201,36174,14106,54102,57041,16141,64174,3591,90024,73332,31254,17288,59809,25061,51612,47951,16570,43330,79213,11354,55585,19646,99246,37564,32660,20632,21124,60597,69315,31312,57741,85108,21615,24365,27684,16124,33888,14966,35303,69921,15795,4020,67672,86816,63027,84470,45605,44887,26222,79888,58982,22466,98844,48353,60666,58256,31140,93507,69561,6256,88526,18655,865,75247,264,65957,98261,72706,36396,46065,85700,32121,99975,73627,78812,89638,86602,96758,65099,52777,46792,13790,55240,52002,10313,91933,71231,10053,78416,54563,96004,42215,30094,45958,48437,49591,50483,13422,69108,59952,27896,40450,79327,31962,46456,39260,51479,61882,48181,50691,64709,32902,10676,12083,35771,79656,56667,76783,3937,99859,10362,57411,40986,35045,2838,29255,64230,84418,34988,77644,39892,77327,74129,53444,35487,95803,38640,20383,55402,25793,14213,87082,42837,95030,97198,61608,97723,79390,35290,34683,81419,87133,70447,53127,97146,28299,56763,12868,1145,12147,58158,92124,60934,18414,97510,7056,54488,20719,53743,91037,44797,52110,8512,18991,20129,31441,51449,14661,71126,23180,68124,18807,70997,21913,19594,70355,73637,68266,60775,43164,52643,96363,77989,79332,39890,65379,20405,52935,43816,92740,95319,4538,60660,28982,15328,80475,34690,2293,19646,46524,96627,33159,42081,8816,74931,20674,8697,66169,46460,46326,39923,60625,28386,22919,19415,75766,43668,31626,70301,67053,3949,70082,2303,48642,38429,94053,38770,68137,68441,52928,70244,91954,17401,92693,98342,21451,84988,80487,33807,73797,49494,41878,76635,83227,76618,11946,13451,87591,78381,21407,90038,72638,69692,51599,86413,32019,64856,74730,41531,11064,1790,58817,86400,66213,92599,70905,78324,54326,43659,34206,63132,38837,40210,96346,16967,81619,96503,14881,89405,32205,49508,98425,2451,35423,56072,36810,30332,85998,49358,92748,84147,79835,94867,41224,61794,35066,82220,66684,20096,2754,41731,37068,32753,91059,13407,5607,69384,53329,95909,44968,11397,92973,50014,92997,80968,93761,57598,74703,7768,37978,73873,33475,9720,97852,98449,48722,84977,11271,11728,68318,22312,78792,87508,88466,72976,47099,84126,38595,85124,64405,90020,7492,52413,95111,34455,86311,68892,1074,60274,28136,19328,38161,57475,13771,63562,84207,94121,18901,52768,33801,82087,86091,59969,90398,56870,55756,78841,98450,54165,55106,50343,70519,14567,36780,55450,19606,83749,67562,64765,38543,16585,86841,73742,8766,39252,75678,75379,78760,37279,15280,13558,95916,89759,76686,76467,67147,63110,94008,8037,35263,53710,16667,79008,11231,29397,67136,18601,64502,90228,89109,72849,22711,65547,34542,26686,81678,87765,77654,23664,96352,14106,32938,28083,18633,80286,65507,46197,52722,75476,77816,47204,34876,45963,79262,90181,84041,3745,90041,30780,27226,92847,85572,15308,80688,5761,82638,13464,23683,81015,54214,64175,43701,86845,15569,50687,52679,87696,8285,97444,47599,94472,64150,87753,68652,60726,26213,17320,64553,81285,98126,12158,52095,64833,492,35817,55571,91300,97812,37507,4209,53515,64342,21223,16662,43265,68219,3529,43636,68417,53640,95326,93381,37113,80751,76469,96677,43054,22937,31954,13266,34140,27253,2734,99070,60077,57988,93211,92795,83795,57477,3941,39007,14619,38320,93449,31336,25279,97030,26245,47394,39475,90621,23820,29344,94859,91604,14033,41868,14816,4075,66644,87803,97815,99552,78666,3942,8175,22345,19983,76783,99044,20851,84981,59052,77178,72109,76475,21619,73017,6812,56633,50612,55289,4671,84419,94072,94446,80603,32188,93415,23464,43947,43728,74284,67177,57105,31059,10642,13803,69602,46961,66567,19359,84676,63918,40650,12923,15974,79732,20225,92525,71179,4859,91208,60430,5239,61458,24089,68852,60171,29603,42535,86365,93905,28237,45317,60718,82001,41679,20679,56304,70043,87568,21386,59049,78353,48696,77379,55309,23780,28391,5940,55583,81256,59418,97521,32846,70761,90115,45325,5490,65974,11186,15357,3568,450,96644,58976,36211,88240,92457,89200,94696,11370,91157,48487,59501,56983,89795,42789,69758,79701,29511,55968,41472,89474,84344,80517,7485,97523,17264,82840,59556,37119,30985,48866,60605,95719,70417,59083,95137,76538,44155,67286,57897,28262,4052,919,86207,79932,44236,10089,44373,65670,44285,6903,20834,49701,95735,21149,3425,17594,31427,14262,32252,68540,39427,44026,47257,45055,95091,8367,28381,57375,41562,83883,27715,10122,67745,46497,28626,87297,36568,39483,11385,63292,92305,78683,6146,81905,15038,38338,51206,65749,34119,71516,74068,51094,6665,91884,66762,11428,70908,21506,480,94183,78484,66507,75901,25728,52539,86806,69944,65036,27882,2530,4918,74351,65737,89178,8791,39342,94963,22581,56917,17541,83578,75376,65202,30935,79270,91986,99286,45236,44720,81915,70881,45886,43213,49789,97081,16075,20517,69980,25310,91953,1759,67635,88933,54558,18395,73375,62251,58871,9870,70538,48936,7757,90374,56631,88862,30487,38794,36079,32712,11130,55451,25137,38785,83558,31960,69473,45950,18225,9871,88502,75179,11551,75664,74321,67351,27703,83717,18913,42470,8816,37627,14288,62831,44047,67612,72738,26995,50933,63758,50003,43693,52661,55852,52372,59042,37595,4931,73622,68387,86478,40997,5245,75300,24902,59609,35653,15970,37681,69365,22236,86374,65550,343,98377,35354,65770,15365,41422,71356,16630,40044,19290,66449,53629,79452,71674,30260,97303,6487,62789,13005,70152,22501,49867,89294,59232,31776,54919,99851,5438,1096,72269,50486,16719,6144,82041,38332,64452,31840,99287,59928,25503,8407,46970,45907,99238,74547,19704,72035,26542,54600,79172,58779,35747,78956,11478,41195,58135,63856,33037,45753,60159,25193,71838,7526,7985,60714,88627,75790,38454,96110,39237,19792,34534,70169,24805,63215,38175,38784,38855,24826,50917,25147,17082,26997,32295,10894,21805,65245,85407,37926,69214,38579,84721,23544,88548,65626,75517,69737,55626,52175,21697,19453,16908,82841,24060,40285,19195,80281,89322,15232,70043,60691,86370,91949,19017,83846,77869,14321,95102,87073,71467,31305,64677,80358,52629,79419,22359,87867,48296,50141,46807,82184,95812,84665,74511,59914,4146,90417,58508,62875,17630,21868,9199,30322,33352,43374,25473,4119,63086,14147,14863,38020,44757,98628,57916,22199,11865,42911,62651,78290,9392,77294,63168,21043,17409,13786,27475,75979,89668,43596,74316,84489,54941,95992,45445,41059,55142,15214,42903,16799,88254,95984,48575,77822,21067,57238,35352,96779,89564,23797,99937,46379,27119,16060,30302,95327,12849,38111,97090,7598,78473,63079,18570,72803,70040,91385,96436,96263,17368,56188,85999,50026,36050,73736,13351,48321,28357,51718,65636,72903,21584,21060,39829,15564,4716,14594,22363,97639,65937,17802,31535,42767,98761,30987,57657,33398,63053,25926,20944,19306,81727,2695,97479,79172,72764,66446,78864,12698,15812,97209,38827,91016,91281,57875,45228,49211,69755,99224,43999,62879,8879,80015,74396,57146,64665,31159,6980,79069,37409,75037,69977,85919,42826,6974,61063,97640,13433,92528,91311,8440,38840,22362,93929,1836,36590,75052,89475,15437,65648,99012,70236,12307,83585,414,62851,48787,28447,21702,57033,29633,44760,34165,27548,37516,24343,63046,2081,20378,19510,42226,97134,68739,32982,56455,53129,77693,25022,55534,99375,30086,98001,7432,67126,76656,29347,28492,43108,64736,32278,84816,80440,30461,818,9136,1952,48442,91058,92590,10443,5195,34009,32141,62209,43740,54102,76895,98172,31583,4155,66492,58981,16591,11331,6838,3818,77063,12523,45570,68970,70055,77751,73743,71732,4704,61384,57343,66682,44500,89745,10436,67202,36455,42467,88801,91280,1056,27534,81619,79004,25824,66362,33280,20706,31929,57422,18730,96197,22101,47592,2180,18287,82310,60430,59627,26471,7794,60475,76713,45427,89654,14370,81674,41246,98416,8669,48883,77154,9806,94015,60347,20027,8405,33150,27368,53375,70171,59431,14534,34018,85665,77797,17944,49602,74391,48830,55029,10371,94261,16658,68400,44148,28150,40364,90913,73151,64463,50058,78191,84439,82478,62398,3113,17578,12830,6571,95934,9132,25287,78731,80683,67207,76597,42096,34934,76609,52553,47508,71561,8038,83011,72577,95790,40076,20292,32138,61197,95476,23123,26648,13611,48452,39963,85857,4855,27029,1542,72443,53688,82635,56264,7977,23090,93553,65434,12124,91087,87800,95675,99419,44659,30382,55263,82514,86800,16781,65977,65946,13033,93895,4056,75895,47878,91309,51233,81409,46773,69135,56906,84493,34530,84534,38312,54574,92933,77341,20839,36126,1143,35356,35459,7959,98335,53266,36146,78047,50607,22486,63308,8996,96056,39085,26567,6779,62663,30523,47881,41279,49864,82248,78333,29466,48151,41957,93235,53308,22682,90722,54478,7235,34306,15827,20121,96837,6283,80172,66109,92592,48238,76428,94546,45430,16288,74839,740,25553,83767,35900,5998,7493,46755,11449,88824,44906,33143,7454,56652,34755,63992,59674,65131,46358,12799,96988,51158,73176,1184,49925,63519,11785,29073,72850,47997,75172,55187,15313,40725,33225,56643,10465,38583,86440,97967,26401,17078,38765,33454,19136,57712,48446,98790,27315,71074,10157,57946,35582,49383,61324,26572,84503,3496,60449,17962,26017,65651,40400,83246,80056,75306,75147,41863,25581,87530,33193,43294,5065,99644,62771,75986,79005,44924,18703,40889,4403,5862,2571,82500,74200,36170,46836,74642,65471,26815,30937,64946,10160,15544,31962,54015,28853,66533,14573,79398,47391,73165,47805,77589,16881,13423,89452,76992,62509,9796,57540,13486,48855,25546,47589,21012,47388,78428,70196,84413,81026,87597,22445,83769,85937,38321,85485,87359,9839,67228,71179,94372,4446,62801,50775,96179,40646,44272,12417,47199,39701,30665,32775,66525,53558,78882,31939,67209,38906,34533,99914,27719,216,99225,96537,3843,90564,91110,51838,30300,9559,37795,94880,11325,44979,89696,28129,29931,89971,46292,92710,11036,74760,75307,12291,49618,16293,92408,67928,80823,32872,25460,66819,35374,4035,99087,61129,11341,39118,10891,37217,63638,75477,30068,42334,57570,6890,59353,89939,37692,15232,20033,32202,22348,2766,96791,58448,92248,5769,96684,67885,99295,47271,38655,59513,96960,31718,8974,16122,20535,52380,29769,70660,57425,50891,75044,84257,73315,38181,28673,93140,26307,82265,78382,19681,56585,8975,76764,39956,83450,84663,89963,71584,57696,30829,60527,64947,34899,28805,28397,91830,51842,99838,39839,66971,67177,74219,35637,35634,93581,81746,29991,81096,94279,2968,62561,2479,82126,25702,67953,88088,50293,83423,86206,39935,23253,43041,48941,85787,8388,6671,43574,84908,67295,33623,55060,28174,48415,2529,22009,24524,5283,30460,32399,80423,56929,40852,69969,88541,5979,91496,64730,57198,83145,39750,3568,54669,98679,4297,51047,31492,47734,31343,31180,232,19707,24823,75079,73943,17997,8446,91252,39879,58682,82972,18417,39203,36681,42895,8459,15618,17941,52594,43277,16530,40052,91100,87422,47230,95699,49794,50492,87439,86354,4546,65333,11057,77727,19748,38722,91821,18107,42125,89239,28847,54623,38783,47803,31414,38450,3697,89186,30579,44188,26532,8420,80723,48100,60748,76330,45832,8311,16051,4475,13400,48527,46073,17439,56498,94632,9021,16871,83366,14896,4219,38375,87890,90217,42370,61028,85101,76771,83715,94737,69973,74187,1958,59691,86712,86570,60984,76342,13648,85250,28323,48379,45141,36277,51845,29039,3553,5128,59866,51281,68124,17007,24729,29710,41439,40574,11774,86746,89698,56020,37810,88972,11361,95583,70786,589,74473,87513,17690,61427,72914,32517,1804,97910,6327,30246,33049,2622,41026,80875,41293,16752,84225,84414,37137,68956,8095,64981,28180,38629,76962,23840,17477,75268,48297,70340,57888,13938,38554,86836,2195,30270,55484,53364,54705,41380,56316,37723,234,21424,26664,63804,75139,36534,18579,9833,98849,72762,59767,52497,24227,83152,71794,21398,99456,89215,51632,54799,27973,68568,68465,98500,28681,18369,24279,96335,12874,82160,67202,85199,27908,67022,49810,77929,96212,81153,77884,7032,1671,53362,28119,56786,30883,28540,76029,3774,64611,19736,25589,46569,45206,48215,69523,17423,91807,90039,30393,58319,85098,66519,57571,24541,3562,14400,62731,82534,61477,89731,18421,29861,52829,838,78040,43350,74323,82892,84746,28302,13264,7595,134,12933,46831,24864,47275,20527,9110,28485,30326,99826,64005,99308,65779,42760,90066,3974,38688,39968,32604,11694,46262,73262,45405,43923,67397,88228,56405,17839,92073,57622,93328,15442,50186,7570,58001,31e3,8915,11467,14793,82691,51238,12485,51745,18192,5985,36826,89434,38669,91592,88799,65621,67237,59541,19657,93402,58705,73553,78280,69125,95591,81168,91927,25976,89077,71690,19404,64603,59752,74698,44233,67602,38615,31303,28650,53700,89819,7783,4351,77451,47350,21234,16016,41532,76508,23063,44993,43983,33356,61715,96485,22121,78004,6316,87896,99289,93981,37850,66128,92735,45064,50924,24204,58816,65290,34392,55567,66416,72353,45775,68590,85685,72683,60090,37149,85347,57414,72336,12979,5720,92754,76911,96883,74420,5220,85815,23557,80567,44365,70254,50864,36619,51479,23281,76428,18580,34240,59289,49076,18439,29522,42541,4024,84446,92434,90407,77241,19690,78143,65919,13699,91844,91241,38361,67171,90551,5709,3474,76025,97043,33834,44638,54040,82797,545,38159,16089,35870,89158,55864,98078,50563,36492,10994,85909,9018,19252,73887,67928,60045,70782,11937,4074,53814,46621,52577,94853,45968,73667,65062,73306,76045,78649,91654,53958,96537,95542,67622,54579,17279,67440,56441,20681,64011,52226,96618,32831,60664,67547,39523,2043,59748,1887,69229,94653,99271,98164,62155,9234,47367,13047,6364,35064,10073,6793,80248,29009,44969,11129,17139,79630,89772,26921,56949,23465,30036,17173,82459,96218,60768,76417,24405,18710,68887,82394,69729,82503,40873,41590,67255,30757,9657,91881,34578,9511,5417,58953,18532,10721,22029,48524,47778,881,83489,3464,57462,97459,86689,39755,39547,740,36666,7993,31671,86304,12970,73402,52849,31652,79655,11250,18463,57518,20306,25301,1374,51208,33298,87662,61849,60923,68685,69411,39266,80320,34844,89416,81569,83651,35795,40168,33501,1042,58931,3892,85188,74740,85476,23790,33842,89565,53359,25579,59049,62394,72435,12457,21904,18370,97035,57905,9581,91227,92754,37760,1411,7440,87175,88318,63242,85960,56690,12618,30493,11569,73723,7448,58830,157,65814,21118,22140,73793,57855,81830,6795,13183,12625,30635,56429,73216,12342,36722,83886,96828,82870,90954,97614,2370,42160,73370,11944,49067,59452,80495,43911,46712,17033,68037,41963,3874,44856,82985,57453,84358,16120,4454,76624,405,62369,55080,61880,51270,87807,10653,36894,70850,35660,234,14705,93418,94084,82856,25384,71555,56754,78315,18291,91656,98079,52384,43306,65205,75903,58701,99496,50048,33557,87793,90857,10143,46726,84284,43635,41213,83845,70986,91408,80220,5728,68890,46577,21152,43759,43301,93661,97252,50106,10099,13722,18572,44024,351,18173,23717,85114,85998,57782,63951,53723,86853,63851,79430,49181,46386,69666,55743,76162,71724,40028,94786,34457,16906,90040,30789,40281,94697,96584,81907,4055,53990,66397,80579,42517,78181,39251,9467,67097,95523,66568,63632,71048,15581,39904,75774,77495,75994,29911,65690,41178,47712,70355,16998,56025,5230,10093,71495,34784,70950,54680,57811,53782,39145,36829,85342,40406,35883,45668,3459,29870,78252,70088,70621,67153,5737,40933,91075,93335,86853,15860,81167,91259,16118,52401,83593,84474,2423,75608,39646,90871,70284,82100,96032,5115,63678,2225,88087,58581,44364,57468,21539,13042,64150,63754,5210,87644,54114,64013,63562,41388,32397,74152,23982,71982,71700,33026,66477,47838,46712,39848,35083,65927,97868,11067,76771,71799,43836,41014,97025,93225,8511,63096,26628,73012,12543,76269,99708,2629,49845,73677,19193,14924,57236,95564,15010,59667,73773,78515,2624,99744,13585,33746,58771,94785,62628,99585,11363,80832,59979,9444,78700,2596,85984,69438,16913,96475,93283,18625,77086,45911,39746,64722,39938,43930,54619,302,50384,2738,75714,75249,95439,80714,52555,47266,96190,78750,94973,83669,16479,53163,48071,28e3,45011,26733,67132,83362,84162,43028,8415,27236,52651,89059,64844,80910,1676,91752,57815,26264,3415,57532,29981,61200,96036,62600,20068,56530,38487,8432,89514,26883,69165,97237,22361,55276,39902,95927,82190,49269,27212,46095,37106,64254,27460,49572,51700,27679,12574,33891,3867,9925,6476,82018,45094,59014,67113,44192,75,23318,79895,70550,81717,28833,30271,15821,14999,88174,62617,57517,55256,50281,51583,96879,5225,42272,5339,20483,57596,41011,75937,22767,50120,95938,49753,63882,99616,69083,38721,73889,80236,99531,23053,71237,48861,59046,76283,60538,19732,93877,30345,64882,66660,17026,70364,45676,8039,96228,89936,59141,95585,89552,97247,59325,27848,80058,15950,61481,90906,40998,44137,16144,66300,44091,50018,81364,18211,60294,76559,20279,27414,10589,39860,23e3,31767,95618,56738,50332,16936,70342,92481,30702,76264,62619,68678,62284,83112,93032,55203,52614,36950,41796,45403,79262,2887,53596,61308,20738,34811,27099,90956,65448,3080,75795,29753,97699,80872,23830,85882,74427,99523,74904,28017,45898,57232,48525,7086,26805,74533,92470,18840,76011,93109,14344,55614,50284,15865,19458,35856,13464,53679,64603,51571,56124,79107,29596,89572,78198,57121,73649,8804,87977,87959,70859,40909,77295,87877,75158,62810,92074,23244,59516,50552,31602,41899,6347,27821,68370,48596,88577,30231,25267,84622,31449,12086,56461,22962,78213,62483,93966,60437,52239,58113,32526,38708,81607,57016,1695,90110,4649,59990,23979,3855,10297,46516,96092,82305,30760,78756,4967,82876,4773,86651,16648,53133,82439,78851,49766,24553,15273,36417,1901,33386,76979,25920,33372,2695,11982,40911,6230,91696,43907,17827,30332,89203,32215,91806,23080,49102,9174,11548,54590,75803,66108,73882,62324,26017,72716,33887,1285,31604,71039,24337,53514,58964,89901,22040,92751,12617,37007,5523,61672,62557,98540,26094,60284,19621,96230,38044,6545,9458,42988,2913,86345,67936,90174,40840,44991,24256,34989,74086,13652,68706,1363,4294,88008,78693,83068,94746,221,89299,53186,5930,61889,51341,45412,58860,72568,11381,59785,36887,10690,31347,93326,96267,86987,57565,86836,49071,90331,41248,34629,30240,27270,3864,84308,3035,61369,36902,51017,44409,17120,23823,36460,63359,8333,63173,19134,6493,303,18550,26191,19051,81502,66343,6737,90430,65478,58982,82484,16483,47704,44640,68322,44548,72787,2335,28749,39320,5436,98146,56596,812,51445,35533,35478,47573,38414,25542,38032,13442,42983,97207,77854,57806,81616,52828,79429,47389,96795,57764,19605,24767,63253,18809,65093,44449,22952,76872,30983,38948,9310,48336,87651,27110,84427,76209,56412,12760,16747,14551,82626,31224,98636,75100,84882,79479,83420,5347,6803,90063,4617,40257,79183,41766,71873,25242,12275,336,40798,42055,74066,69128,32547,76508,32530,42359,89207,49758,58984,92732,15779,7234,28884,28226,50011,35883,99606,45423,76224,75427,85747,33879,97978,57441,927,19164,74716,40702,19715,70917,60344,40236,9019,50577,15598,53136,57285,20536,7539,74832,89184,41501,39447,97422,97041,21913,40581,76081,13089,28776,54164,55736,36263,71841,34488,74988,55467,43322,9214,36746,67981,71877,81683,32461,84091,19422,88366,62054,85664,13409,8003,88276,6989,16607,33633,85349,5784,25950,97998,74110,16699,60184,92818,79705,10381,1474,18656,50434,18232,92132,66537,70141,42854,25120,39581,28249,14215,34810,19767,3409,11807,6566,66138,42997,41999,67504,87117,28961,5e3,29673,77726,73225,54753,69712,71576,92337,17713,63185,87923,91889,68351,17712,75532,93849,48280,62219,317,25290,29209,90927,92929,92762,60413,2018,31793,76290,73373,80777,60819,77375,57886,47291,99670,32605,29064,99476,80999,31217,35,91300,14892,73653,26593,25305,56797,12837,39560,27582,37253,38531,76489,49946,69108,58687,43092,73807,96282,6648,67431,87124,57694,21660,64002,6,33600,30245,60636,80164,9285,61426,4658,54130,14710,76553,1904,93668,63110,98618,5601,32199,74923,98049,49717,55539,35940,58545,43295,35810,45451,38735,42065,66769,69825,45461,83881,67372,67351,90612,79502,69460,23108,74421,82990,46821,40683,71603,55267,48192,50242,79738,96417,6664,19929,23644,41116,51056,219,45086,32747,49492,15399,24874,80825,95928,61457,45813,59037,16136,3953,83583,5910,12654,53630,92997,22168,93491,71897,74579,24022,6278,24049,71670,43044,8474,38572,77402,35800,7455,96177,41653,74493,20802,65843,73050,73349,2638,65813,96209,49196,45007,32207,14097,66059,46681,7534,71263,20582,10171,51514,52142,60961,57951,25637,37860,21683,86190,90434,94481,85697,95344,2606,74095,61133,7472,64777,94050,41482,975,23471,76052,82021,87676,91345,20196,2612,86299,44996,40312,65712,46079,88514,8610,3685,63197,9073,53105,86824,28112,99306,40706,66840,83003,51590,52755,32285,68454,85058,13645,23073,24724,52989,71880,21952,44144,74975,76715,7844,46447,86643,75579,29276,10864,83179,36721,19300,35066,29383,47478,56644,33354,31414,17643,92374,85085,88458,87191,85248,34963,76278,53230,13953,76985,70959,36663,5293,32658,56767,56997,76736,6558,64248,11907,29123,78458,17678,63805,89973,5076,39263,54404,4355,64957,74407,99838,18836,78098,6490,74888,73719,80675,86178,56283,33591,96957,38382,18772,74773,71229,2603,52673,44609,14843,58418,18060,95459,626,30914,13550,42195,44863,8871,89182,64446,78422,41140,15312,98274,48168,95651,35562,85386,56252,72136,85088,68761,78434,98143,61330,2446,64409,49406,99127,98626,55095,44808,13594,87370,89472,12833,98932,68064,58193,20225,5192,28425,23978,24542,80845,55858,4015,21454,37346,51007,17202,10242,12682,55933,96922,22280,75597,50227,70712,44236,20470,36320,49339,60536,80083,38880,93327,49522,93585,9918,55268,4671,57526,11457,48424,54610,7211,78610,9473,72923,27347,30057,76968,26177,59367,46172,88951,40229,34921,60405,88959,16779,29547,92231,61997,36002,21080,39795,77221,10012,49748,76900,15964,3803,40260,92351,92844,10288,57483,10881,70408,75688,16610,1638,93082,44282,66849,75702,69428,34047,84968,71281,72328,73143,88672,49802,50639,18129,93659,58389,49095,45971,34196,84609,59222,19332,17777,41004,47057,30688,16039,20906,41477,42915,60877,33864,75195,62294,3371,11672,1370,2486,35553,17907,90621,45136,9722,67635,12114,63055,16004,21625,24321,20491,26881,66259,94287,54751,36242,36557,5842,30687,65418,94608,24741,45887,78800,86912,42076,50287,9284,68891,76368,83094,96302,35997,30761,97081,9501,68887,32876,1705,34260,95065,45528,88241,30402,12318,52430,40139,96986,84900,72408,42027,31676,54382,73370,26184,14024,57444,57660,52173,30274,93448,63273,77681,74946,2099,69091,19372,66961,14595,58642,75760,52253,53148,26074,52293,65359,63971,4833,86492,1227,54505,19515,89889,46933,13364,33883,83389,36952,52505,67513,40071,31001,3105,87912,29610,75108,37363,28479,43546,89992,19550,54863,82633,19209,21548,35022,21960,57961,11815,95867,559,26428,69386,57453,70147,73538,49562,46806,64550,36653,25718,68792,31113,7607,48037,71020,22666,65957,11141,39227,7990,19849,65972,74528,40888,55386,95918,92088,91125,53648,66122,138,79933,71058,34826,97725,69513,22915,18246,52244,91161,40861,40374,13239,56162,4703,95851,22824,41271,28202,62852,84238,46625,20031,8524,20077,65817,21174,29279,57712,22401,67500,30980,74485,26480,21343,30031,61921,35744,57308,71196,1865,49234,62616,54021,29008,83672,85839,96836,45077,80900,66906,63526,93824,71820,11033,20183,85704,4683,63512,39144,56880,64424,95979,17709,94849,31771,5737,84286,16757,46256,24478,73180,59978,8254,78963,95437,86351,33824,32540,18357,2668,99260,21284,81351,70961,10255,6911,47394,72408,23827,59865,96395,30665,43699,3593,29165,23388,26628,92402,16731,86740,29493,9069,78653,90094,42735,33682,95041,89887,92369,57949,81585,50593,14698,4737,72551,57271,59433,156,33966,58773,59108,49578,18100,59836,73221,21110,1650,11058,47770,66141,84576,58388,40915,94507,32209,17272,65674,95552,25685,5345,36995,36302,7971,67001,62062,75939,36005,26739,56484,46885,66348,87666,78055,44485,82955,85936,9219,1847,92687,72579,45457,78252,98239,4e4,75563,92408,17175,78845,32638,26959,35406,59553,57852,7506,9,93172,77713,93880,40981,27924,9678,24538,52426,84852,83781,23712,82490,77890,22482,66668,55850,25644,44972,62275,78089,28894,98685,32998,98766,89119,34355,75127,69797,71419,62067,57990,96514,50603,79807,26135,29207,43632,32905,38513,18924,88872,20758,70232,60425,1116,24077,21369,93541,75329,78656,44251,42014,98154,42552,14575,30765,348,1134,71581,68420,78141,21105,63305,9718,54851,65867,8595,47390,39182,51174,41478,64433,59628,31945,87322,78667,95282,5622,26224,19972,97269,98376,14779,51138,49658,45345,4972,52794,15737,496,48939,63485,42780,16061,59631,37171,13483,56058,51093,62290,88227,17400,88433,67363,89507,26482,85964,71336,67799,28342,37747,61722,27180,78755,18603,42953,6606,23875,56766,1932,36113,62807,84012,21103,9685,69662,76755,13701,95168,13169,44726,15284,16702,89617,54397,52052,12835,37741,86434,22400,37947,95763,86337,35189,22756,47473,16618,42479,47405,14055,64262,66670,89692,54032,94591,44149,29854,76691,33263,62048,25116,88598,16119,62116,54517,31883,86707,18895,81790,71294,2684,15292,48107,14341,91416,75609,92564,39987,2283,89970,95855,80970,5432,89860,90293,99851,94648,5598,32171,28793,92305,64244,8277,93391,96717,34464,29838,10664,28050,60122,77934,10758,84922,92220,45071,97697,36368,17792,84792,76594,67319,51886,5665,45201,11348,9254,7510,51039,91683,84500,85338,5555,19633,3870,39576,41486,58524,54508,20707,58504,39642,22454,80069,83455,31043,90794,51934,3295,26582,16300,74990,22197,83310,69642,81113,58558,84833,17105,46659,25003,85749,44829,4103,67516,76458,52392,53546,70291,98846,67315,30686,18555,29755,5923,22732,19501,56181,85351,5023,4808,56911,16793,75336,49712,27723,96974,34321,5454,12862,71924,45928,95697,68664,58183,78104,42483,71204,99628,40642,56410,17350,13396,76724,87509,9158,83708,27298,92651,95086,38851,63558,89810,1580,32518,35795,26514,56322,78635,63731,91428,7247,66460,38671,26799,22549,47991,46064,80467,40083,17141,39152,99872,27561,75389,74778,94893,82935,99076,93419,10474,84436,47536,16719,60136,80566,28404,74525,74212,3704,65516,98197,34210,64140,22238,49939,99542,27481,21992,78181,90060,71365,66935,29578,14961,8569,9454,43308,66753,45972,93572,16382,87320,37183,25478,38164,31997,69856,60898,63968,62264,4799,17591,89937,73905,55890,88285,2448,40398,54180,65869,45155,43407,39105,339,51619,20203,21189,68245,76912,1222,76411,82679,7,66047,32043,42627,16638,27019,15248,66444,8249,18790,82150,54084,84469,3426,50226,99868,88894,43769,66384,8593,41414,2976,60053,51866,87904,74135,53842,59520,67482,16995,32328,29555,49067,2799,68851,41049,97190,53984,99755,46412,45885,64e3,21962,36438,71742,57223,66599,86071,31436,32667,98099,38399,47377,5171,2742,48803,17823,22093,9866,691,5514,25546,2114,5919,56181,96052,67211,61712,25995,3188,23833,38549,44775,55355,61548,55988,47309,23749,30667,70732,33299,16127,30842,78961,41072,9876,18903,30292,25275,61881,15939,72573,84502,92654,97226,53434,77025,63892,12421,33644,39445,30933,84218,13757,37719,84450,2697,60309,22402,80310,92771,45205,72792,95776,85945,74651,216,50842,47854,21916,61588,75405,10495,83083,60427,78495,99809,47890,22993,21508,9459,26845,98130,1184,46438,27698,40652,65654,98517,1096,6998,49133,57041,77983,58708,42176,67356,324,70063,10597,65205,25622,34336,16640,27896,26907,86760,48244,89650,44997,51609,28934,9171,97859,97213,19859,41037,64081,94781,27683,41521,52871,86935,26486,38744,25943,60617,6414,42292,46204,53262,30201,38776,88831,97253,67282,72860,18452,60927,81504,57713,30296,10896,39900,67135,42772,4631,55283,39253,25264,1809,12874,88035,88421,90491,83290,6884,15444,90113,20406,20796,40239,34431,15018,45600,17241,26611,9551,89126,65673,31708,91252,39647,63011,24193,58932,89326,33491,53217,27976,70151,37531,53576,23931,11789,73073,52171,89301,51718,15385,79487,66436,35771,34163,86540,42665,80748,77622,14679,40185,25030,42622,13162,17048,24243,59985,59807,60562,3595,10135,29199,69784,59796,38194,58432,50943,40422,63035,3208,81440,90749,88046,32218,88092,22224,2627,91576,16781,43948,57795,71073,27817,87077,82717,24473,42096,76920,88864,90537,14715,42551,45066,24316,37361,38582,21871,14672,93362,21727,57021,94313,39562,64985,94028,46094,43845,91838,79574,7597,3153,56783,18817,74711,6883,91061,31674,73729,99315,66183,57647,74484,68077,33224,397,56753,53158,71872,68153,9298,20961,49656,33407,95683,14328,44708,72952,27048,67887,28741,46752,88177,95894,40086,88534,87112,68614,83073,88794,96799,67588,75049,84603,83140,97347,87316,73087,77135,71883,98643,3808,8848,14133,60447,1366,72976,1868,51667,63279,60040,88264,79152,3474,61366,20523,21584,93712,83654,89761,90154,96345,37539,32556,74254,70603,97122,44978,78028,8943,13778,11080,34271,68276,85372,48410,94516,15427,75323,71685,70774,50342,33771,3678,42321,69788,41758,55004,30992,17402,63523,42328,87171,24751,15084,33884,83655,88345,69602,52606,57886,18034,3381,75796,35901,77480,28683,68324,66035,7223,14926,16128,13645,90370,31949,11057,98849,29499,21565,30786,83292,92392,37104,36899,49906,79368,43710,80365,88735,75275,21664,57965,19002,301,12658,94385,1717,96191,50404,80166,93965,24688,27839,10812,31715,92127,42588,93307,80834,11317,26583,25769,98227,14884,58462,29148,68662,26872,72927,79021,51622,29521,33355,45701,45996,33782,93424,16530,96086,17329,74020,11501,46660,5583,22277,77653,55430,84644,448,86828,58855,67451,95264,67386,82424,52611,60012,88620,72894,94716,22262,99813,69592,63464,33163,91857,47904,22209,78590,68615,52952,31441,41313,18550,72685,68825,4795,53971,14592,39634,23682,76630,2731,81481,86542,23727,54291,56045,61635,32186,9355,73416,63532,24340,18886,84832,30654,48543,18339,65024,91197,64624,74648,9660,27897,49771,11123,8732,49393,12911,72416,17834,18878,62754,85072,23727,56577,51257,83291,12329,16203,91681,68137,79959,43609,58987,2026,42969,59144,84349,75214,76972,22633,64104,53799,16851,99197,70476,77113,46320,88693,37711,96536,68156,7119,2104,49435,77706,18924,24957,92406,87148,70482,36491,42605,54440,7893,31618,35707,65130,30007,75706,77266,37100,9601,87681,42543,69847,81848,32034,49429,99434,62209,17125,55227,61634,52574,83649,28725,70119,62467,80676,21192,99584,21310,25292,72781,17186,10393,98390,19789,92931,36234,62627,23437,3885,58822,82941,43806,8172,23790,72295,36196,98200,2889,87619,13846,56197,27151,21238,48794,81100,62643,40001,39243,33213,78416,194,91369,79342,36404,52308,13741,24442,88610,12659,11574,70052,93039,79367,41792,61816,35435,47192,97596,28330,41145,16918,62865,9576,45857,68737,90124,16703,7071,48433,57222,34435,800,72496,16449,68187,28739,97672,86818,50768,40807,88681,64340,2224,19703,59245,90905,31239,84216,93942,97371,16842,92168,52692,16064,84686,89444,27938,98406,41365,4515,20494,18813,16242,10634,61566,18592,78057,8720,33739,78345,87693,30242,70545,55521,23687,9160,8655,38811,61768,7228,5567,5561,82071,85,50145,23113,97761,88441,14891,72188,85166,37189,75671,81377,92470,73645,93258,6610,12185,43065,26704,47922,56650,7527,18006,56948,51675,16658,66402,1047,81624,77395,62310,73262,66050,57275,32936,87641,51528,58183,21952,84098,28913,28622,18140,89796,41317,93954,67690,64667,57092,21315,4731,76115,77291,11204,8634,93034,27411,27149,13843,9817,9407,84492,28444,59901,14592,89654,66207,66232,80293,74502,36925,55515,10121,16768,4720,71502,40500,21406,571,87320,81683,42788,86367,44686,22159,67015,35892,49668,83991,72088,30210,74009,86370,97956,2132,93512,54819,26094,51409,21485,94764,85806,13393,48543,7042,76538,64224,47909,9994,23750,17351,52141,30486,60380,86546,66606,36913,58173,45709,83679,82617,23381,9603,61107,566,6572,64745,10614,86371,43244,97154,10397,50975,68006,20045,16942,25536,74031,31807,70133,78790,40341,68730,39635,39013,66841,44043,96215,21270,59427,25034,40645,84741,52083,54503,36861,27659,95463,53847,40921,70116,61536,56756,8967,31079,20097,76014,99818,16606,19713,66904,27106,24874,96701,73287,76772,6073,57343,51428,91171,28299,17520,64903,4177,36071,94952,59008,28543,11576,74547,13260,20688,41261,2780,6633,37536,8844,95774,49323,30448,14154,83379,71259,23302,68402,43750,88505,15575,44927,6584,29867,21541,65763,12154,86616,79877,73259,68626,98962,68548,86576,48046,51755,64995,3661,64585,81550,46798,49319,50206,22024,5175,12923,23427,55915,91723,55831,83784,81034,86779,34622,84570,18960,48798,42970,95789,39465,82353,68905,44234,18244,54345,5592,89361,14644,67924,66415,89349,88530,72096,44459,5258,48317,48866,56886,90458,75889,4514,37227,11302,4667,2129,80414,86289,15887,87380,50749,83220,50529,20619,11606,36531,23409,78122,19566,76564,33045,66703,30017,35347,35038,12952,13971,3922,98702,11786,38388,69556,76728,60535,59961,23634,42211,98387,34880,27755,93182,99040,96390,65989,38375,3652,59657,57431,24666,11061,64713,85185,72849,58611,31220,26657,77056,24553,24993,5210,89024,32054,46997,92652,28363,98992,22593,97710,47766,37646,93573,95502,33790,92973,27766,62671,89698,10877,73893,41004,96035,18795,48080,59666,30241,35233,87353,43647,13404,41982,19264,29229,61369,8309,39383,42305,25944,13577,51545,68990,69801,37145,79189,55897,57793,66816,21930,56771,79296,73793,21632,42301,23696,72641,56310,85576,3004,25669,69221,32996,23040,65782,23712,13414,10758,15590,97298,74246,51511,46900,36795,38292,3852,6384,84421,3446,91670,45312,27609,87034,6683,83891,88991,16533,9197,34427,60384,48525,90978,46107,21693,12956,21804,46558,37682,81207,85840,53238,35026,4835,53264,41376,17783,64756,39278,25403,33042,20954,31193,24247,45911,92453,25370,86602,48574,57865,26436,16122,76614,17028,21262,59718,77821,14036,31033,90563,45410,15158,90209,84089,38053,60780,54166,14255,33120,27171,71798,91214,80040,56699,12475,40193,59415,4769,75920,1036,2692,75862,16612,73670,61182,3305,90334,187,91659,28063,75684,50017,82643,9282,77376,85469,8164,5584,36623,82597,83859,3435,98460,70095,80257,4381,6501,8924,35514,14297,54373,71369,5172,15955,82441,4636,48215,6821,3385,17663,40107,55679,30366,42390,95895,16083,58499,17176,55993,51034,49296,4010,78974,35930,2019,96226,27167,68245,53109,59037,37843,79243,10262,58797,61490,82590,52411,54783,29447,94551,30026,97959,93939,73217,82573,62154,78291,33728,39102,11484,86210,43794,73553,87435,1110,77108,56521,78610,8254,1842,43068,70415,79195,26136,49786,47279,38471,20379,54704,86614,91138,51595,50818,80186,73087,17262,94735,4952,27935,4928,74862,51392,62388,9570,38485,30594,56278,47395,72762,94597,72279,16010,34697,54475,67874,78014,88381,4045,41494,55178,46054,24373,1824,55333,7525,97908,61178,84635,2199,35361,4803,21907,79414,66083,54782,58692,28332,41851,28198,55819,37313,67046,16147,90478,71230,34141,85002,44332,35906,429,39744,773,22909,19536,98986,90945,45209,85439,92265,25291,22775,60611,49159,95701,36113,53923,60824,84935,29656,50007,86624,61691,76150,32187,42765,60660,13859,10792,88210,29374,29563,45188,28811,19739,67649,73775,99247,48414,91067,68253,9452,90116,91737,73979,62370,69112,58791,20349,71480,56852,36919,87977,77609,68738,85159,4918,70076,46473,4122,57713,1426,50987,77910,66211,62546,77749,96462,34304,77441,12104,91805,10287,60943,49632,83116,25716,23113,22707,77770,31176,6759,46130,4739,55554,3843,31653,70834,72877,41561,36903,23010,6663,2266,16360,70118,91936,17098,77278,4880,23484,94970,41826,46733,93484,68350,38861,18134,32936,241,24803,13876,93278,5039,35873,44418,5305,28510,36115,46717,15238,78607,23464,68635,55712,55007,92411,65739,4858,67537,37041,67453,89801,45963,14800,14225,65655,80463,9716,77255,65136,11230,76323,81433,36445,86523,61058,59560,19380,40791,48073,29626,36661,87907,57369,41623,13705,3880,45088,55444,41003,27754,1450,75312,71801,99600,60719,54182,29245,63315,73758,42973,32702,10855,56363,14638,84424,27178,78195,3133,70865,48019,26117,7151,52107,85562,41347,50486,69457,86961,95482,11857,93587,45680,42145,13029,10043,5142,49213,54525,85761,42707,70754,33768,87671,85038,58900,88438,20004,63390,14815,38875,73417,82875,89481,55517,944,15773,61814,32915,27868,5510,21916,28426,89881,16680,88850,11056,51991,4230,39107,49216,40065,4523,75848,95349,56034,10724,9885,88232,42478,65702,95696,39746,66032,88082,86905,30007,75068,66629,7358,26706,90511,72843,67857,20061,98581,69682,38e3,14186,70,2290,17269,30909,69449,19997,13275,2444,84985,51290,97641,15092,69650,21920,19617,7418,49725,91090,20805,28627,80665,67192,34697,57667,99323,50101,40587,35081,14037,34414,19898,60779,83267,87499,29596,41852,15813,32419,72232,8322,39184,46525,13833,65743,94595,37363,4711,35386,96413,10627,62625,56555,12919,93218,25191,98380,51923,66181,5788,73491,1452,487,12277,45415,11884,61300,94528,9181,26616,11455,31514,63290,45035,42759,33804,85721,80979,46010,50975,72482,31231,3086,58941,46102,25773,89742,29788,96741,88523,14922,88262,76305,57676,93259,2396,69145,26074,30056,3853,75317,56639,66203,38923,48939,22813,91864,10934,6714,84099,25631,73223,95630,97552,45950,22197,42886,33764,1263,41856,82057,62349,94091,78028,62651,18911,5693,92561,97821,41994,92343,76785,22216,4203,5038,86151,23596,24338,77181,51761,97693,10955,98159,37568,58932,72128,27303,99608,31688,57557,91022,43036,93927,32869,53653,55205,33139,47271,31224,51650,36422,86857,73799,22068,43376,84760,44898,65776,42451,71480,38509,41673,44141,75918,95652,68981,83001,48815,98086,67950,27986,33175,43624,55274,71051,61124,51550,64967,31570,15748,19159,38174,51078,79811,39183,57527,96550,85168,28824,47466,56993,13151,96664,29735,70251,1079,4314,77714,11507,1440,48415,31984,99915,20282,26524,18057,4992,40521,98108,84045,91961,79256,72244,25788,5487,23595,73302,14205,8925,27625,64343,28821,37992,67156,83320,31106,10884,30735,15067,51091,15668,48777,50770,19169,76504,41165,29749,92812,8065,66782,26841,1411,95461,61134,18699,52261,60469,81373,44825,11448,73320,30151,56991,31372,6655,36472,86292,30247,30931,21029,53410,9859,37267,47514,3492,49008,94727,25234,40546,53417,36492,25723,76227,58456,15979,34876,9574,34392,3751,36933,83921,65108,63135,67572,40184,21098],l=new Array(16),h=[16,8,16,24],f=4,p=0,d=0;d<16;d++){l[d]=new Array(256);for(var g=0;g<256;g++)l[d][g]=g|g<<8|g<<16|g<<24;for(var m=3;0<=m;m--)for(var v=0;v<255;v++){var y=255<<(m<<3),b=0|l[d][v],w=function(t,e){var n=e-t+1,r=0,i=1;do{for(r=0,i=1;i<n;i*=10)r=10*r+(f<0&&(f=4,p++),c[p]%Math.pow(10,f+1)/Math.pow(10,f--)|0)}while((i/n|0)*n<=r);return t+r%n|0}(v,255);l[d][v]=l[d][v]&~y|l[d][w]&y,l[d][w]=l[d][w]&~y|b&y}}n=function(){function t(e){if(this instanceof t)return(e=e||{}).length=e.length||128,e.rounds=e.rounds||8,(e=function(t,e){if(!e||"object"!==o(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,a(t).call(this,e))).blockSize=16-e.state.hash.length,e.blockSizeInBytes=e.blockSize*e.unitSize,e.W=new Array(16),e;throw new TypeError("Cannot call a class as a function")}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&u(e,n);e=[{key:"reset",value:function(){s(a(t.prototype),"reset",this).call(this),this.state.hash=new Array(this.options.length/32|0);for(var e=0;e<this.state.hash.length;e++)this.state.hash[e]=0}},{key:"processBlock",value:function(t){for(var e=0;e<this.state.hash.length;e++)this.W[e]=0|this.state.hash[e];for(var n=this.state.hash.length;n<16;n++)this.W[n]=0|t[n-this.state.hash.length];for(var r=0;r<this.options.rounds<<1;r+=2)for(var o=0;o<4;o++){for(var s=0;s<16;s++){var a=0|l[r+(s/2|0)%2][255&this.W[s]];this.W[s-1>>>0&15]^=a,this.W[s+1&15]^=a}for(var u=0;u<16;u++)this.W[u]=Object(i.b)(this.W[u],h[o])}for(var c=0;c<this.state.hash.length;c++)this.state.hash[c]=this.state.hash[c]^this.W[15-c]}},{key:"finalize",value:function(){return 0<this.state.message.length&&this.addPaddingZero(this.blockSizeInBytes-this.state.message.length|0),this.addPaddingZero(this.blockSizeInBytes-8|0),this.addLengthBits(),this.process(),this.getStateHash()}}];for(var c=t.prototype,f=e,p=0;p<f.length;p++){var d=f[p];d.enumerable=d.enumerable||!1,d.configurable=!0,"value"in d&&(d.writable=!0),Object.defineProperty(c,d.key,d)}return t}(),e.a=n},function(t,e,n){"use strict";var r=n(5),i=n(0);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function a(t,e,n){return(a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=u(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function c(t,e){return(c=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}for(var l=[[7,12,17,22],[5,9,14,20],[4,11,16,23],[6,10,15,21]],h=new Array(64),f=0;f<64;f++)h[f]=4294967296*Math.abs(Math.sin(f+1))|0;n=function(){function t(){var e=this;if(!(e instanceof t))throw new TypeError("Cannot call a class as a function");e=this;var n=u(t).apply(this,arguments);if(!n||"object"!==o(n)&&"function"!=typeof n){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return n}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");return e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&c(e,n),e=[{key:"FF",value:function(t,e,n){return t&e|~t&n}},{key:"GG",value:function(t,e,n){return t&n|e&~n}},{key:"HH",value:function(t,e,n){return t^e^n}},{key:"II",value:function(t,e,n){return e^(t|~n)}},{key:"CC",value:function(t,e,n,r,o,s,a,u){return Object(i.a)(n+t(r,o,s)+a+e,u)+r|0}}],s((n=t).prototype,[{key:"reset",value:function(){a(u(t.prototype),"reset",this).call(this),this.state.hash=[1732584193,-271733879,-1732584194,271733878]}},{key:"processBlock",value:function(e){var n=0|this.state.hash[0],r=0|this.state.hash[1],i=0|this.state.hash[2],o=0|this.state.hash[3];n=t.CC(t.FF,h[0],n,r,i,o,e[0],l[0][0]),o=t.CC(t.FF,h[1],o,n,r,i,e[1],l[0][1]),i=t.CC(t.FF,h[2],i,o,n,r,e[2],l[0][2]),r=t.CC(t.FF,h[3],r,i,o,n,e[3],l[0][3]);n=t.CC(t.FF,h[4],n,r,i,o,e[4],l[0][0]),o=t.CC(t.FF,h[5],o,n,r,i,e[5],l[0][1]),i=t.CC(t.FF,h[6],i,o,n,r,e[6],l[0][2]),r=t.CC(t.FF,h[7],r,i,o,n,e[7],l[0][3]),n=t.CC(t.FF,h[8],n,r,i,o,e[8],l[0][0]),o=t.CC(t.FF,h[9],o,n,r,i,e[9],l[0][1]),i=t.CC(t.FF,h[10],i,o,n,r,e[10],l[0][2]),r=t.CC(t.FF,h[11],r,i,o,n,e[11],l[0][3]),n=t.CC(t.FF,h[12],n,r,i,o,e[12],l[0][0]),o=t.CC(t.FF,h[13],o,n,r,i,e[13],l[0][1]),i=t.CC(t.FF,h[14],i,o,n,r,e[14],l[0][2]),r=t.CC(t.FF,h[15],r,i,o,n,e[15],l[0][3]),n=t.CC(t.GG,h[16],n,r,i,o,e[1],l[1][0]),o=t.CC(t.GG,h[17],o,n,r,i,e[6],l[1][1]),i=t.CC(t.GG,h[18],i,o,n,r,e[11],l[1][2]),r=t.CC(t.GG,h[19],r,i,o,n,e[0],l[1][3]),n=t.CC(t.GG,h[20],n,r,i,o,e[5],l[1][0]),o=t.CC(t.GG,h[21],o,n,r,i,e[10],l[1][1]),i=t.CC(t.GG,h[22],i,o,n,r,e[15],l[1][2]),r=t.CC(t.GG,h[23],r,i,o,n,e[4],l[1][3]),n=t.CC(t.GG,h[24],n,r,i,o,e[9],l[1][0]),o=t.CC(t.GG,h[25],o,n,r,i,e[14],l[1][1]),i=t.CC(t.GG,h[26],i,o,n,r,e[3],l[1][2]),r=t.CC(t.GG,h[27],r,i,o,n,e[8],l[1][3]),n=t.CC(t.GG,h[28],n,r,i,o,e[13],l[1][0]),o=t.CC(t.GG,h[29],o,n,r,i,e[2],l[1][1]),i=t.CC(t.GG,h[30],i,o,n,r,e[7],l[1][2]),r=t.CC(t.GG,h[31],r,i,o,n,e[12],l[1][3]),n=t.CC(t.HH,h[32],n,r,i,o,e[5],l[2][0]),o=t.CC(t.HH,h[33],o,n,r,i,e[8],l[2][1]),i=t.CC(t.HH,h[34],i,o,n,r,e[11],l[2][2]),r=t.CC(t.HH,h[35],r,i,o,n,e[14],l[2][3]),n=t.CC(t.HH,h[36],n,r,i,o,e[1],l[2][0]),o=t.CC(t.HH,h[37],o,n,r,i,e[4],l[2][1]),i=t.CC(t.HH,h[38],i,o,n,r,e[7],l[2][2]),r=t.CC(t.HH,h[39],r,i,o,n,e[10],l[2][3]),n=t.CC(t.HH,h[40],n,r,i,o,e[13],l[2][0]),o=t.CC(t.HH,h[41],o,n,r,i,e[0],l[2][1]),i=t.CC(t.HH,h[42],i,o,n,r,e[3],l[2][2]),r=t.CC(t.HH,h[43],r,i,o,n,e[6],l[2][3]),n=t.CC(t.HH,h[44],n,r,i,o,e[9],l[2][0]),o=t.CC(t.HH,h[45],o,n,r,i,e[12],l[2][1]),i=t.CC(t.HH,h[46],i,o,n,r,e[15],l[2][2]),r=t.CC(t.HH,h[47],r,i,o,n,e[2],l[2][3]),n=t.CC(t.II,h[48],n,r,i,o,e[0],l[3][0]),o=t.CC(t.II,h[49],o,n,r,i,e[7],l[3][1]),i=t.CC(t.II,h[50],i,o,n,r,e[14],l[3][2]),r=t.CC(t.II,h[51],r,i,o,n,e[5],l[3][3]),n=t.CC(t.II,h[52],n,r,i,o,e[12],l[3][0]),o=t.CC(t.II,h[53],o,n,r,i,e[3],l[3][1]),i=t.CC(t.II,h[54],i,o,n,r,e[10],l[3][2]),r=t.CC(t.II,h[55],r,i,o,n,e[1],l[3][3]),n=t.CC(t.II,h[56],n,r,i,o,e[8],l[3][0]),o=t.CC(t.II,h[57],o,n,r,i,e[15],l[3][1]),i=t.CC(t.II,h[58],i,o,n,r,e[6],l[3][2]),r=t.CC(t.II,h[59],r,i,o,n,e[13],l[3][3]),n=t.CC(t.II,h[60],n,r,i,o,e[4],l[3][0]),o=t.CC(t.II,h[61],o,n,r,i,e[11],l[3][1]),i=t.CC(t.II,h[62],i,o,n,r,e[2],l[3][2]),r=t.CC(t.II,h[63],r,i,o,n,e[9],l[3][3]),this.state.hash[0]=this.state.hash[0]+n|0,this.state.hash[1]=this.state.hash[1]+r|0,this.state.hash[2]=this.state.hash[2]+i|0,this.state.hash[3]=this.state.hash[3]+o|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}]),s(n,e),t}(),e.a=n},function(t,e,n){"use strict";var r=n(3),i=n(0);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e,n){return(s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=a(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var c=[1518500249,1859775393,2400959708,3395469782];n=function(){function t(e){if(this instanceof t)return(e=function(t,e){if(!e||"object"!==o(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,a(t).call(this,e))).options.rounds=e.options.rounds||80,e.W=new Array(80),e;throw new TypeError("Cannot call a class as a function")}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&u(e,n);e=[{key:"reset",value:function(){s(a(t.prototype),"reset",this).call(this),this.state.hash=[1732584193,-271733879,-1732584194,271733878,-1009589776]}},{key:"processBlock",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],o=0|this.state.hash[3],s=0|this.state.hash[4],a=0;a<this.options.rounds;a++){this.W[a]=a<16?0|t[a]:0|Object(i.a)(this.W[a-3]^this.W[a-8]^this.W[a-14]^this.W[a-16],1);var u=Object(i.a)(e,5)+s+this.W[a]+c[a/20|0]|0;u=a<20?u+(n&r|~n&o)|0:!(a<40)&&a<60?u+(n&r|n&o|r&o)|0:u+(n^r^o)|0,s=o,o=r,r=0|Object(i.a)(n,30),n=e,e=u}this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+o|0,this.state.hash[4]=this.state.hash[4]+s|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}];for(var l=t.prototype,h=e,f=0;f<h.length;f++){var p=h[f];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(l,p.key,p)}return t}();e.a=n},function(t,e,n){"use strict";var r=n(0),i=n(3);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e,n){return(s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=a(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var c=new Array(256),l=(n=[104,208,235,43,72,157,106,228,227,163,86,129,125,241,133,158,44,142,120,202,23,169,97,213,93,11,140,60,119,81,34,66,63,84,65,128,204,134,179,24,46,87,6,98,244,54,209,107,27,101,117,16,218,73,38,249,203,102,231,186,174,80,82,171,5,240,13,115,59,4,32,254,221,245,180,95,10,181,192,160,113,165,45,96,114,147,57,8,131,33,92,135,177,224,0,195,18,145,138,2,28,230,69,194,196,253,191,68,161,76,51,197,132,35,124,176,37,21,53,105,255,148,77,112,162,175,205,214,108,183,248,9,243,103,164,234,236,182,212,210,20,30,225,36,56,198,219,75,122,58,222,94,223,149,252,170,215,206,7,15,61,88,154,152,156,242,167,17,126,139,67,3,226,220,229,178,78,199,109,233,39,64,216,55,146,143,1,29,83,62,89,193,79,50,22,250,116,251,99,159,52,26,42,90,141,201,207,246,144,40,136,155,49,14,189,74,232,150,166,12,200,121,188,190,239,110,70,151,91,237,25,217,172,153,168,41,100,31,173,85,19,187,247,111,185,71,47,238,184,123,137,48,211,127,118,130],[1,11,9,12,13,6,15,3,14,8,7,4,10,2,5,0]),h=[7,12,11,13,14,4,9,15,6,3,8,10,2,5,1,0],f=new Array(16),p=[1,1,4,1,8,5,2,9],d=[1,1,3,1,5,8,9,5],g=new Array(512),m=new Array(22),v=new Array(512),y=new Array(22),b=new Array(512),w=new Array(22);function _(t,e){for(var n=new Array(512),i=new Array(22),o=0;o<8;o++)n[o]=[];for(var s=0;s<256;s++){var a=new Array(10);a[1]=t[s],a[2]=a[1]<<1,256<=a[2]&&(a[2]^=285),a[3]=a[2]^a[1],a[4]=a[2]<<1,256<=a[4]&&(a[4]^=285),a[5]=a[4]^a[1],a[8]=a[4]<<1,256<=a[8]&&(a[8]^=285),a[9]=a[8]^a[1],n[0][2*s]=a[e[0]]<<24|a[e[1]]<<16|a[e[2]]<<8|a[e[3]],n[0][2*s+1]=a[e[4]]<<24|a[e[5]]<<16|a[e[6]]<<8|a[e[7]];for(var u=1;u<8;u++)n[u][2*s]=Object(r.d)(n[0][2*s+1],n[0][2*s],u<<3),n[u][2*s+1]=Object(r.c)(n[0][2*s+1],n[0][2*s],u<<3)}i[0]=0,i[1]=0;for(var c=1;c<=10;c++)i[2*c]=4278190080&n[0][16*c-16]^16711680&n[1][16*c-14]^65280&n[2][16*c-12]^255&n[3][16*c-10],i[2*c+1]=4278190080&n[4][16*c-7]^16711680&n[5][16*c-5]^65280&n[6][16*c-3]^255&n[7][16*c-1];return[n,i]}for(var x=0;x<16;x++)f[l[x]]=0|x;for(var C=0;C<256;C++){var k=l[C>>4],S=f[15&C],O=h[k^S];c[C]=l[k^O]<<4|f[S^O]}n=_(n,d),v=n[0],y=n[1],n=_(c,d),b=n[0],w=n[1],n=_(c,p),g=n[0],m=n[1],d=function(){function t(e){var n;switch(function(e){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this),(e=e||{}).type=e.type||"",e.rounds=e.rounds||10,(n=function(t,e){if(!e||"object"!==o(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,a(t).call(this,e))).options.type){case"0":case 0:n.C=v,n.RC=y;break;case"t":n.C=b,n.RC=w;break;default:n.C=g,n.RC=m}return n}var e=t,n=i.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&u(e,n);e=[{key:"reset",value:function(){s(a(t.prototype),"reset",this).call(this),this.state.hash=new Array(16);for(var e=0;e<16;e++)this.state.hash[e]=0}},{key:"processBlock",value:function(t){for(var e=new Array(16),n=[],r=0;r<16;r++)n[r]=t[r]^(e[r]=this.state.hash[r]);for(var i=[],o=1;o<=this.options.rounds;o++){for(var s=0;s<8;s++){i[2*s]=0;for(var a=i[2*s+1]=0,u=56,c=0;a<8;a++,c=(u-=8)<32?1:0)i[2*s]^=this.C[a][2*(e[2*(s-a&7)+c]>>>u%32&255)],i[2*s+1]^=this.C[a][2*(e[2*(s-a&7)+c]>>>u%32&255)+1]}for(var l=0;l<16;l++)e[l]=i[l];e[0]^=this.RC[2*o],e[1]^=this.RC[2*o+1];for(var h=0;h<8;h++){i[2*h]=e[2*h],i[2*h+1]=e[2*h+1];for(var f=0,p=56,d=0;f<8;f++,d=(p-=8)<32?1:0)i[2*h]^=this.C[f][2*(n[2*(h-f&7)+d]>>>p%32&255)],i[2*h+1]^=this.C[f][2*(n[2*(h-f&7)+d]>>>p%32&255)+1]}for(var g=0;g<16;g++)n[g]=i[g]}for(var m=0;m<16;m++)this.state.hash[m]^=n[m]^t[m]}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<32?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}];for(var r=t.prototype,c=e,l=0;l<c.length;l++){var h=c[l];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),Object.defineProperty(r,h.key,h)}return t}(),e.a=d},function(t,e,n){"use strict";var r=n(4);function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function o(t){return(o=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function s(t,e){return(s=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var a=function(){function t(e){if(this instanceof t)return(e=function(t,e){if(!e||"object"!==i(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,o(t).call(this,e))).unitSize=1,e.blockSizeInBytes=e.blockSize*e.unitSize,e.blockUnits=[],e;throw new TypeError("Cannot call a class as a function")}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&s(e,n);e=[{key:"process",value:function(){for(;this.state.message.length>=this.blockSizeInBytes;){this.blockUnits=new Array(this.blockSizeInBytes);for(var t=0;t<this.blockSizeInBytes;t++)this.blockUnits[t]=0|this.state.message.charCodeAt(t);this.state.message=this.state.message.substr(this.blockSizeInBytes),this.processBlock(this.blockUnits)}}},{key:"processBlock",value:function(t){}},{key:"getStateHash",value:function(t){t=t||this.state.hash.length;for(var e="",n=0;n<t;n++)e+=String.fromCharCode(255&this.state.hash[n]);return e}}];for(var a=t.prototype,u=e,c=0;c<u.length;c++){var l=u[c];l.enumerable=l.enumerable||!1,l.configurable=!0,"value"in l&&(l.writable=!0),Object.defineProperty(a,l.key,l)}return t}();function u(t){return(u="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function c(t,e,n){return(c="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=l(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function l(t){return(l=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function h(t,e){return(h=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var f=[41,46,67,201,162,216,124,1,61,54,84,161,236,240,6,19,98,167,5,243,192,199,115,140,152,147,43,217,188,76,130,202,30,155,87,60,253,212,224,22,103,66,111,24,138,23,229,18,190,78,196,214,218,158,222,73,160,251,245,142,187,47,238,122,169,104,121,145,21,178,7,63,148,194,16,137,11,34,95,33,128,127,93,154,90,144,50,39,53,62,204,231,191,247,151,3,255,25,48,179,72,165,181,209,215,94,146,42,172,86,170,198,79,184,56,210,150,164,125,182,118,252,107,226,156,116,4,241,69,157,112,89,100,113,135,32,134,91,207,101,230,45,168,2,27,96,37,173,174,176,185,246,28,70,97,105,52,64,126,15,85,71,163,35,221,81,175,58,195,92,249,206,186,197,234,38,44,83,13,110,133,40,132,9,211,223,205,244,65,129,77,82,106,220,55,200,108,193,171,250,36,225,123,8,12,189,177,74,120,136,149,139,227,99,232,109,233,203,213,254,59,0,29,57,242,239,183,14,102,88,208,228,166,119,114,248,235,117,75,10,49,68,80,180,143,237,31,26,219,153,141,51,159,17,131,20];n=function(){function t(e){if(this instanceof t)return(e=function(t,e){if(!e||"object"!==u(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,l(t).call(this,e))).options.rounds=e.options.rounds||18,e;throw new TypeError("Cannot call a class as a function")}var e=t,n=a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&h(e,n);e=[{key:"reset",value:function(){c(l(t.prototype),"reset",this).call(this),this.state.hash=new Array(48),this.state.checksum=new Array(16)}},{key:"processBlock",value:function(t){for(var e=0;e<16;e++)this.state.hash[16+e]=0|t[e],this.state.hash[32+e]=t[e]^this.state.hash[e];for(var n=0,r=0;r<this.options.rounds;r++){for(var i=0;i<48;i++)n=this.state.hash[i]^=f[n];n=n+r&255}n=255&this.state.checksum[15];for(var o=0;o<16;o++)n=this.state.checksum[o]^=f[t[o]^n]}},{key:"finalize",value:function(){this.addPaddingPKCS7(16-(15&this.state.message.length)|0),this.process();for(var t=0;t<16;t++)this.state.message+=String.fromCharCode(this.state.checksum[t]);return this.process(),this.getStateHash(16)}}];for(var r=t.prototype,i=e,o=0;o<i.length;o++){var s=i[o];s.enumerable=s.enumerable||!1,s.configurable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(r,s.key,s)}return t}();e.a=n},function(t,e,n){"use strict";var r=n(5),i=n(0);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e,n){return(s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=a(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var c=[0,1518500249,1859775393,2400959708],l=[5,11,7,15,6,13,8,14,7,12,9,11,8,15,6,12,9,14,5,13],h=[10,17,25,30],f=[18,0,1,2,3,19,4,5,6,7,16,8,9,10,11,17,12,13,14,15,22,3,6,9,12,23,15,2,5,8,20,11,14,1,4,21,7,10,13,0,26,12,5,14,7,27,0,9,2,11,24,4,13,6,15,25,8,1,10,3,30,7,2,13,8,31,3,14,9,4,28,15,10,5,0,29,11,6,1,12];n=function(){function t(e){if(this instanceof t)return(e=function(t,e){if(!e||"object"!==o(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,a(t).call(this,e))).options.rounds=e.options.rounds||80,e.W=new Array(32),e;throw new TypeError("Cannot call a class as a function")}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&u(e,n);e=[{key:"reset",value:function(){s(a(t.prototype),"reset",this).call(this),this.state.hash=[1732584193,4023233417,2562383102,271733878,3285377520]}},{key:"processBlock",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],o=0|this.state.hash[3],s=0|this.state.hash[4],a=0;a<16;a++)this.W[a]=0|t[a];this.W[16]=this.W[0]^this.W[1]^this.W[2]^this.W[3],this.W[17]=this.W[4]^this.W[5]^this.W[6]^this.W[7],this.W[18]=this.W[8]^this.W[9]^this.W[10]^this.W[11],this.W[19]=this.W[12]^this.W[13]^this.W[14]^this.W[15],this.W[20]=this.W[3]^this.W[6]^this.W[9]^this.W[12],this.W[21]=this.W[2]^this.W[5]^this.W[8]^this.W[15],this.W[22]=this.W[1]^this.W[4]^this.W[11]^this.W[14],this.W[23]=this.W[0]^this.W[7]^this.W[10]^this.W[13],this.W[24]=this.W[5]^this.W[7]^this.W[12]^this.W[14],this.W[25]=this.W[0]^this.W[2]^this.W[9]^this.W[11],this.W[26]=this.W[4]^this.W[6]^this.W[13]^this.W[15],this.W[27]=this.W[1]^this.W[3]^this.W[8]^this.W[10],this.W[28]=this.W[2]^this.W[7]^this.W[8]^this.W[13],this.W[29]=this.W[3]^this.W[4]^this.W[9]^this.W[14],this.W[30]=this.W[0]^this.W[5]^this.W[10]^this.W[15],this.W[31]=this.W[1]^this.W[6]^this.W[11]^this.W[12];for(var u=0;u<this.options.rounds;u++){var p=Object(i.a)(e,l[u%20])+s+this.W[f[u]]+c[u/20|0]|0;p=u<20?p+(n&r|~n&o)|0:!(u<40)&&u<60?p+(r^(n|~o))|0:p+(n^r^o)|0,s=o,o=r,r=0|Object(i.a)(n,h[u/20|0]),n=e,e=p}this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+o|0,this.state.hash[4]=this.state.hash[4]+s|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}];for(var p=t.prototype,d=e,g=0;g<d.length;g++){var m=d[g];m.enumerable=m.enumerable||!1,m.configurable=!0,"value"in m&&(m.writable=!0),Object.defineProperty(p,m.key,m)}return t}();e.a=n},function(t,e,n){"use strict";n.d(e,"a",(function(){return i}));var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function i(t){for(var e="",n=t.length-t.length%3|0,i=0,o=0;o<n;o+=3)i=t.charCodeAt(o)<<16|t.charCodeAt(o+1)<<8|t.charCodeAt(o+2),e+=r.charAt(i>>18)+r.charAt(i>>12&63)+r.charAt(i>>6&63)+r.charAt(63&i);return t.length-n==2?(i=t.charCodeAt(n)<<16|t.charCodeAt(1+n)<<8,e+=r.charAt(i>>18)+r.charAt(i>>12&63)+r.charAt(i>>6&63)+"="):t.length-n==1&&(i=t.charCodeAt(n)<<16,e+=r.charAt(i>>18)+r.charAt(i>>12&63)+"=="),e}},function(t,e,n){"use strict";var r=n(5),i=n(0);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function a(t,e,n){return(a="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=u(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function u(t){return(u=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function c(t,e){return(c=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var l=[[3,7,11,19],[3,5,9,13],[3,9,11,15]],h=1518500249,f=1859775393;n=function(){function t(){var e=this;if(!(e instanceof t))throw new TypeError("Cannot call a class as a function");e=this;var n=u(t).apply(this,arguments);if(!n||"object"!==o(n)&&"function"!=typeof n){if(void 0!==e)return e;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return n}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");return e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&c(e,n),e=[{key:"FF",value:function(t,e,n){return t&e|~t&n}},{key:"GG",value:function(t,e,n){return t&e|t&n|e&n}},{key:"HH",value:function(t,e,n){return t^e^n}},{key:"CC",value:function(t,e,n,r,o,s,a,u){return 0|Object(i.a)(n+t(r,o,s)+a+e,u)}}],s((n=t).prototype,[{key:"reset",value:function(){a(u(t.prototype),"reset",this).call(this),this.state.hash=[1732584193,-271733879,-1732584194,271733878]}},{key:"processBlock",value:function(e){var n=0|this.state.hash[0],r=0|this.state.hash[1],i=0|this.state.hash[2],o=0|this.state.hash[3];n=t.CC(t.FF,0,n,r,i,o,e[0],l[0][0]),o=t.CC(t.FF,0,o,n,r,i,e[1],l[0][1]),i=t.CC(t.FF,0,i,o,n,r,e[2],l[0][2]),r=t.CC(t.FF,0,r,i,o,n,e[3],l[0][3]);n=t.CC(t.FF,0,n,r,i,o,e[4],l[0][0]),o=t.CC(t.FF,0,o,n,r,i,e[5],l[0][1]),i=t.CC(t.FF,0,i,o,n,r,e[6],l[0][2]),r=t.CC(t.FF,0,r,i,o,n,e[7],l[0][3]),n=t.CC(t.FF,0,n,r,i,o,e[8],l[0][0]),o=t.CC(t.FF,0,o,n,r,i,e[9],l[0][1]),i=t.CC(t.FF,0,i,o,n,r,e[10],l[0][2]),r=t.CC(t.FF,0,r,i,o,n,e[11],l[0][3]),n=t.CC(t.FF,0,n,r,i,o,e[12],l[0][0]),o=t.CC(t.FF,0,o,n,r,i,e[13],l[0][1]),i=t.CC(t.FF,0,i,o,n,r,e[14],l[0][2]),r=t.CC(t.FF,0,r,i,o,n,e[15],l[0][3]),n=t.CC(t.GG,h,n,r,i,o,e[0],l[1][0]),o=t.CC(t.GG,h,o,n,r,i,e[4],l[1][1]),i=t.CC(t.GG,h,i,o,n,r,e[8],l[1][2]),r=t.CC(t.GG,h,r,i,o,n,e[12],l[1][3]),n=t.CC(t.GG,h,n,r,i,o,e[1],l[1][0]),o=t.CC(t.GG,h,o,n,r,i,e[5],l[1][1]),i=t.CC(t.GG,h,i,o,n,r,e[9],l[1][2]),r=t.CC(t.GG,h,r,i,o,n,e[13],l[1][3]),n=t.CC(t.GG,h,n,r,i,o,e[2],l[1][0]),o=t.CC(t.GG,h,o,n,r,i,e[6],l[1][1]),i=t.CC(t.GG,h,i,o,n,r,e[10],l[1][2]),r=t.CC(t.GG,h,r,i,o,n,e[14],l[1][3]),n=t.CC(t.GG,h,n,r,i,o,e[3],l[1][0]),o=t.CC(t.GG,h,o,n,r,i,e[7],l[1][1]),i=t.CC(t.GG,h,i,o,n,r,e[11],l[1][2]),r=t.CC(t.GG,h,r,i,o,n,e[15],l[1][3]),n=t.CC(t.HH,f,n,r,i,o,e[0],l[2][0]),o=t.CC(t.HH,f,o,n,r,i,e[8],l[2][1]),i=t.CC(t.HH,f,i,o,n,r,e[4],l[2][2]),r=t.CC(t.HH,f,r,i,o,n,e[12],l[2][3]),n=t.CC(t.HH,f,n,r,i,o,e[2],l[2][0]),o=t.CC(t.HH,f,o,n,r,i,e[10],l[2][1]),i=t.CC(t.HH,f,i,o,n,r,e[6],l[2][2]),r=t.CC(t.HH,f,r,i,o,n,e[14],l[2][3]),n=t.CC(t.HH,f,n,r,i,o,e[1],l[2][0]),o=t.CC(t.HH,f,o,n,r,i,e[9],l[2][1]),i=t.CC(t.HH,f,i,o,n,r,e[5],l[2][2]),r=t.CC(t.HH,f,r,i,o,n,e[13],l[2][3]),n=t.CC(t.HH,f,n,r,i,o,e[3],l[2][0]),o=t.CC(t.HH,f,o,n,r,i,e[11],l[2][1]),i=t.CC(t.HH,f,i,o,n,r,e[7],l[2][2]),r=t.CC(t.HH,f,r,i,o,n,e[15],l[2][3]),this.state.hash=[this.state.hash[0]+n|0,this.state.hash[1]+r|0,this.state.hash[2]+i|0,this.state.hash[3]+o|0]}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}]),s(n,e),t}();e.a=n},function(t,e,n){"use strict";var r=n(3),i=n(0);function o(t){return(o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e,n){return(s="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,n){t=function(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=a(t)););return t}(t,e);if(t)return(t=Object.getOwnPropertyDescriptor(t,e)).get?t.get.call(n):t.value})(t,e,n||t)}function a(t){return(a=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function u(t,e){return(u=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}var c=[1518500249,1859775393,2400959708,3395469782];n=function(){function t(e){if(this instanceof t)return(e=function(t,e){if(!e||"object"!==o(e)&&"function"!=typeof e){if(void 0!==t)return t;throw new ReferenceError("this hasn't been initialised - super() hasn't been called")}return e}(this,a(t).call(this,e))).options.rounds=e.options.rounds||80,e.W=new Array(80),e;throw new TypeError("Cannot call a class as a function")}var e=t,n=r.a;if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),n&&u(e,n);e=[{key:"reset",value:function(){s(a(t.prototype),"reset",this).call(this),this.state.hash=[1732584193,-271733879,-1732584194,271733878,-1009589776]}},{key:"processBlock",value:function(t){for(var e=0|this.state.hash[0],n=0|this.state.hash[1],r=0|this.state.hash[2],o=0|this.state.hash[3],s=0|this.state.hash[4],a=0;a<this.options.rounds;a++){this.W[a]=a<16?0|t[a]:this.W[a-3]^this.W[a-8]^this.W[a-14]^this.W[a-16];var u=Object(i.a)(e,5)+s+this.W[a]+c[a/20|0]|0;u=a<20?u+(n&r|~n&o)|0:!(a<40)&&a<60?u+(n&r|n&o|r&o)|0:u+(n^r^o)|0,s=o,o=r,r=0|Object(i.a)(n,30),n=e,e=u}this.state.hash[0]=this.state.hash[0]+e|0,this.state.hash[1]=this.state.hash[1]+n|0,this.state.hash[2]=this.state.hash[2]+r|0,this.state.hash[3]=this.state.hash[3]+o|0,this.state.hash[4]=this.state.hash[4]+s|0}},{key:"finalize",value:function(){return this.addPaddingISO7816(this.state.message.length<56?56-this.state.message.length|0:120-this.state.message.length|0),this.addLengthBits(),this.process(),this.getStateHash()}}];for(var l=t.prototype,h=e,f=0;f<h.length;f++){var p=h[f];p.enumerable=p.enumerable||!1,p.configurable=!0,"value"in p&&(p.writable=!0),Object.defineProperty(l,p.key,p)}return t}();e.a=n},function(t,e,n){"use strict";function r(t){for(var e="",n=new Uint8Array(t),r=0;r<n.length;r++)e+=String.fromCharCode(n[r]);return e}n.d(e,"a",(function(){return r}))},,function(t,e,n){"use strict";var r=function(){function t(e,n){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");e.length>n.blockSizeInBytes&&(n.update(e),e=n.finalize(),n.reset());for(var r=e.length;r<n.blockSizeInBytes;r++)e+="\0";this.oPad="";for(var i=0;i<e.length;i++)n.update(String.fromCharCode(54^e.charCodeAt(i))),this.oPad+=String.fromCharCode(92^e.charCodeAt(i));this.hasher=n}for(var e=[{key:"update",value:function(t){this.hasher.update(t)}},{key:"finalize",value:function(){var t=this.hasher.finalize();return this.hasher.reset(),this.hasher.update(this.oPad),this.hasher.update(t),this.hasher.finalize()}}],n=t.prototype,r=e,i=0;i<r.length;i++){var o=r[i];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(n,o.key,o)}return t}();e.a=r},,,,,,,,,function(t,e,n){"use strict";n.r(e);var r=n(14),i=n(13),o=n(16),s=n(10),a=n(8),u=n(17),c=n(11),l=n(7),h=n(6),f=n(9),p=n(12),d=n(1),g=n(18),m=n(2),v=n(15),y=n(20);n=new(n=function(){function t(){if(!(this instanceof t))throw new TypeError("Cannot call a class as a function");this.encoder={},this.encoder.fromUtf=d.a,this.encoder.fromArrayBuffer=g.a,this.encoder.toHex=m.a,this.encoder.toBase64=v.a}for(var e=[{key:"getHasher",value:function(t,e){switch(e=e||{},t){case"has160":return new r.a(e);case"md2":return new i.a(e);case"md4":return new o.a(e);case"md5":return new s.a(e);case"ripemd128":return e=Object.assign({},{length:128},e),new a.a(e);case"ripemd":case"ripemd160":return e=Object.assign({},{length:160},e),new a.a(e);case"ripemd256":return e=Object.assign({},{length:256},e),new a.a(e);case"ripemd320":return e=Object.assign({},{length:320},e),new a.a(e);case"sha0":return new u.a(e);case"sha1":return new c.a(e);case"sha224":return e=Object.assign({},{length:224},e),new l.a(e);case"sha256":return e=Object.assign({},{length:256},e),new l.a(e);case"sha384":return e=Object.assign({},{length:384},e),new h.a(e);case"sha512":return e=Object.assign({},{length:512},e),new h.a(e);case"sha512/224":return e=Object.assign({},{length:224},e),new h.a(e);case"sha512/256":return e=Object.assign({},{length:256},e),new h.a(e);case"snefru":case"snefru128":case"snefru128/8":return e=Object.assign({},{length:128},e),new f.a(e);case"snefru256":case"snefru256/8":return e=Object.assign({},{length:256},e),new f.a(e);case"snefru128/2":return e=Object.assign({},{length:128,rounds:2},e),new f.a(e);case"snefru256/4":return e=Object.assign({},{length:256,rounds:4},e),new f.a(e);case"whirlpool":return new p.a(e);case"whirlpool-0":return e=Object.assign({},{type:"0"},e),new p.a(e);case"whirlpool-t":return e=Object.assign({},{type:"t"},e),new p.a(e)}}},{key:"hash",value:function(t,e,n){return t=this.getHasher(t,n=n||{}),t.update(Object(d.a)(e)),Object(m.a)(t.finalize())}},{key:"getHmac",value:function(t,e){return new y.a(t,e)}},{key:"hmac",value:function(t,e,n){return t=this.getHmac(Object(d.a)(t),n),t.update(Object(d.a)(e)),Object(m.a)(t.finalize())}}],n=t.prototype,b=e,w=0;w<b.length;w++){var _=b[w];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Object.defineProperty(n,_.key,_)}return t}()),e.default=n}]).default;!function(t){"use strict";var e={page:1,pageSize:200,total:0,showTotal:!1,totalTxt:"共{total}条",noData:!1,showSkip:!1,showPN:!0,prevPage:"上一页",nextPage:"下一页",fastForward:0,selectOption:[],backFun:function(t){}};function n(n,r){this.element=t(n),this.settings=t.extend({},e,r),this.pageNum=1,this.pageList=[],this.pageTatol=0,this.init()}t.extend(n.prototype,{init:function(){this.element.empty(),this.viewHtml(),this.clickBtn()},creatHtml:function(t){t==this.settings.page?this.pageList.push('<button class="active" data-page='+t+">"+t+"</button>"):this.pageList.push("<button data-page="+t+">"+t+"</button>")},viewHtml:function(){var t=this.settings,e=0,n=[];if(0<t.total)e=Math.ceil(t.total/t.pageSize);else{if(!t.noData)return;t.page=e=1,t.total=0}if(this.pageTatol=e,this.pageNum=t.page,t.showTotal&&n.push('<div class="spage-total">'+t.totalTxt.replace(/\{(\w+)\}/gi,t.total)+"</div>"),n.push('<div class="spage-number">'),this.pageList=[],t.showPN&&(1==t.page?this.pageList.push('<button class="button-disabled" data-page="prev"><i class="prevBtn"></i></button>'):this.pageList.push('<button data-page="prev"><i class="prevBtn"></i></button>')),e<=6)for(var r=1;r<e+1;r++)this.creatHtml(r);else if(t.page<3){for(r=1;r<=3;r++)this.creatHtml(r);this.pageList.push('<button data-page="after" class="spage-after">...</button><button data-page='+e+">"+e+"</button>")}else if(t.page>e-3)for(this.pageList.push('<button data-page="1">1</button><button data-page="before" class="spage-before">...</button>'),r=e-3;r<=e;r++)this.creatHtml(r);else{for(this.pageList.push('<button data-page="1">1</button>'),3<t.page&&this.pageList.push('<button data-page="before" class="spage-before">...</button>'),r=t.page-1;r<=Number(t.page)+1;r++)this.creatHtml(r);t.page<=e-3&&this.pageList.push('<button data-page="after" class="spage-after">...</button>'),this.pageList.push("<button data-page="+e+">"+e+"</button>")}if(t.showPN&&(t.page==e?this.pageList.push('<button class="button-disabled" data-page="next"><i class="nextBtn"></i></button>'):this.pageList.push('<button data-page="next"><i class="nextBtn"></i></button>')),n.push(this.pageList.join("")),n.push("</div>"),0<t.selectOption.length){for(var i='<select class="selectNum" id="selectNum">',o=0;o<=t.selectOption.length-1;o++)i+="<option value="+t.selectOption[o]+" ",t.pageSize===t.selectOption[o]?i+="selected>"+t.selectOption[o]+"行/页</option>":i+=">"+t.selectOption[o]+"行/页</option>";n.push(i+="</select>")}t.showSkip&&n.push('<div class="spage-skip">跳至&nbsp;<input type="text" class="luckysheet-mousedown-cancel" value="'+t.page+'"/>&nbsp;页&nbsp;&nbsp;</div>'),this.element.html(n.join(""))},clickBtn:function(){var e=this,n=this.settings,r=this.element,i=this.pageTatol;this.element.on("change","select",(function(t){var r=parseInt(document.getElementById("selectNum").value);n.pageSize=r,n.page=1,e.element.empty(),e.viewHtml(),n.backFun(n)})),this.element.off("click","button"),this.element.on("click","button",(function(){var o=t(this).data("page");switch(o){case"prev":n.page=1<=n.page-1?n.page-1:1,o=n.page;break;case"next":n.page=Number(n.page)+1<=i?Number(n.page)+1:i,o=n.page;break;case"before":n.page=1<=n.page-n.fastForward?n.page-n.fastForward:1,o=n.page;break;case"after":n.page=Number(n.page)+Number(n.fastForward)<=i?Number(n.page)+Number(n.fastForward):i,o=n.page;break;case"go":var s=parseInt(r.find("input").val());if(!(/^[0-9]*$/.test(s)&&1<=s&&s<=i))return;o=n.page=s;break;default:n.page=o}o!=e.pageNum&&(e.pageNum=n.page,e.viewHtml(),n.backFun(n))})),this.element.off("keyup","input"),this.element.on("keyup","input",(function(t){13==t.keyCode&&(t=parseInt(r.find("input").val()),/^[0-9]*$/.test(t))&&1<=t&&t<=i&&t!=e.pageNum&&(n.page=t,e.pageNum=t,e.viewHtml(),n.backFun(n))})),0<n.fastForward&&(r.find(".spage-after").hover((function(){t(this).html("&raquo;")}),(function(){t(this).html("...")})),r.find(".spage-before").hover((function(){t(this).html("&laquo;")}),(function(){t(this).html("...")})))}}),t.fn.sPage=function(t){return this.each((function(){new n(this,t)}))}}(jQuery,(window,document));