<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>SSO</title>
</head>
<body>
<script>
     //当用户输入/sso时，并携带参数， 跳转至/#/sso并携带参数
    let currentUrl = window.location.href;
    let encodedFragment = encodeURIComponent("#/sso");
    let newUrl = currentUrl.replace(/^(.*)\/sso.*?(\?.*)?$/, "$1/" + encodedFragment + "$2");
    history.pushState(null, null, newUrl);
</script>
</body>
</html>
