### agnes-ui 运营平台前端
### 如当前版本未发布,在当前版本续写更新内容 如果已发布,则添加新的版本号更新内容 1.X.XX 未发布/发布时间
### 1.16.2 20250106
    F 升级 data-audit-ui/file-deal-ui/kms-ui包版本
### 1.16.2 20241230
    F 业务权限树定义-支持批量删除
### 1.16.2 20241227
    F 佣金席位模块支持配置显示首页
### 1.16.2 20241226
    B 账户管理模块sonar扫描修复完成
### 1.16.2 20241226
    F 回收账户管理模块
### 1.16.2 20241225
    B 缩进问题修复
### 1.16.2 20241219
    B Sonar扫描，本地修改vue-quill-editor组件代码降低代码复杂度
    B [平安] 升级kms-ui,知识库管理点击下载直接跳转到下载页面
### 1.15.1 20250102
    B step 配置添加计划开始、结束时间配置
### 1.15.1 20241218
    B 快捷新增-结束时间问题修复
### 1.15.1 20241218
    B 快捷新增-结束时间问题修复
### 1.15.1 20241218
    B 快捷新增-结束时间问题修复
### 1.15.1 20241218
    B 快捷新增-结束时间问题修复
### 1.15.1 20241218
    B 快捷新增-结束时间问题修复
### 1.15.1 20241218
    B 积木绑定绑定指标按钮切换时清空数据
    F 数据源管理驱动类修改为输入框并携带默认值
### 1.13.0 20241218
    B 表格筛选自动翻译

### 1.16.2 20241218
    B 升级file-deal-ui版本，修复文件服务器配置密码命名漏洞
    B 修复升级quill后导致vue-quill-editor组件不兼容适配
    B 任务制定页面，快捷新增时任务名称为空，保存时回显为default
    B 任务制定页面，执行开始时间大于结束时间
    B 任务通知方案页面，偏移、备注字段超长时报错
    B 场景构建，审核发布后定位到当前选择数据
### 1.16.2 20241217
    B 交接管理页面，交接开始时间大于结束时间
    B 审批管理-我的审批页面、我的发起页面，查询条件重置只清空条件列表数据未回显
    B 交接管理页面重置功能，只将重置的查询条件清空，未回显列表数据
### 1.16.2 20241216
    B Sonar扫描修复
### 1.16.2 20241213
    F 低码使用影像控件预览功能回收，使用x-doc-ui
    F 升级ares-ui，表格新增load-data-all事件，返回所有数据
### 1.16.2 20241209
    F [平安资管] 任务中心，场景开发，我的工作台模块支持低版本谷歌浏览器
    B 【OMS-Oracle-1.16】 产品分工表——重置查询条件，需点两次重置按钮才能清空
    B 任务制定页面- 任务列表，支持表格自适应
### 1.16.2 20241209
    B Sonar扫描修复
    F 升级版本1.16.5-beta.4
### 1.16.2 20241206
    F 集成席位佣金模块
    B Sonar扫描修复
### 1.16.2 20241204
    B sit测试 任务转办：保存的时候需要对转办人做必填校验
    B sit测试 交接管理页面，取消交接接口丢失问题，
### 1.16.2 20241128
    B [平安资管] 管理模式-业务管理-产品联系人管理里面新增-选择联系人表格不显示
### 1.16.2 20241128
    B [苏州银行] 修复频率点击问题
### 1.16.2 2024127
    F [平安资管] 引用kms样式修复
### 1.16.2 2024126
    F [平安资管] 权限控制问题修复
### 1.16.2 2024122
    F [平安资管] 左侧树页面适配66版浏览器
    F 指标明细展示，支持千分位格式化展示，支持小数
    F 升级kms支持通过字典配置控制知识库tab页的隐藏
### 1.16.2 2024122
    F [平安资管] 平安集成kms适配66版浏览器
### 1.16.2 20241119
    B [苏州银行] 修复频率点击问题
    B sonar扫描缺陷修复
### 1.16.2 20241118
    B [苏州银行] 修复指标统计和频率点击问题
    B 积木块-集成指标 钻取明细页面不能删除列表字段问题
    B [苏州银行] 支持用户自己设置登录背景
### 1.16.2 20241115
    B [苏州银行] 解决修改表格样式引起的操作列位置问题
### 1.16.2 20241031
    B [苏州银行] 首次登录强制修改密码
### 1.16.2 20241025
    B 【上海银行】任务中心-任务中心，查看指标类任务页面，点击刷新按钮无效（前端）
    B 【上海银行】任务中心-任务中心定义-关联任务中删除按钮无效（前端）
### 1.16.2 20241031
    B 首次登录强制修改密码
### 1.16.2 20241024
    B 快捷功能样式优化、消息提醒弹窗状态bug修复、指标查看明细状态修复、交接管理撤销
    B 任务中心-指标监控-明细查询调整
### 1.16.2 20241023
    F 【智能渠道模型】点击一条数据打开智能渠道模型配置页签，再选择一条数据后打开页签没有更新
    F 【智能渠道模型配置】通过查看按钮也能编辑保存数据
### 1.16.2 20241017
    F 任务中心-自定义卡片支持跳转指定页面
### 1.16.2 20241010
    B 漏洞修复
    B 任务中心Bug修改
### 1.16.1 20241010
    B 漏洞修复
### 1.16.1 20240930
    F 合并任务中心代码
### 1.16.1 20240926
    F 合并代码
    B 修复部分漏洞bug
### 1.13.1 20240802
    B 日常管理-查看明细 修复分组状态下勾选无法同时勾选分组下数据的问题
    B 修复我的日历点击关闭报错问题
### 1.13.1 20240724
    F 待办工作台、岗位工作台添加排序字段，展示顺序按配置展示
### 1.13.1 20240709
    B 合并华泰宝兴修复BUG
    B 积木配置中积木名称重复
### 1.13.1 20240625
    F 待办工作台功能开发
### 1.13.1 20240618
    B 修复需求模式不授权模式页签以及登录功能报错问题
### 1.13.1 20240604
    F 模版套打管理-添加签章类型单选框
### 1.13.1 20240530
    F word 支持多行套打，取数对象类型 增加默认按钮
### 1.13.1 20240522
    F 岗位工作台优化 配置方式：菜单路径.TA\JZ
### 1.13.1 20240522
    F 群组授权-添加知识库文件夹管理员授权功能
### 1.13.1 20240516
    F 选择用户组件新增我的常用
### 1.13.1 20240517
    F 账户管理-资料清单配置上传多份文件
### 1.13.1 20240515
    F 表单录入step-底码页面必填项校验功能开发
### 1.13.1 20240513
    F 岗位工作台添加查询接口，新增时，过滤已存在的记录
### 1.13.1 20240513
    B 底码表单提交step，对于没有配置xcp-form的，不进行内容提交
    B 用户组合授权-已有产品去除有问题，删除过程是奇删偶不删
### 1.13.1 20240509
    F 函数定义-SQL-添加使用说明
    B 修复绝对路径bug
### 1.13.1 20240507
    F 岗位工作台定义功能开发
### 1.14.1 20240507
    F 华泰柏瑞新资料清单多节点功能迁移
### 1.13.1 20240506
    F 日常作业工作台任务提交方法迁移到ac模块
### 1.13.1 20240506
    F 套打功能补充
### 1.13.1 20240430
    F 工作台角色切换；增加部门工作中心；估值看板增加tab切换；待办工作台增加基金代码筛选
### 1.13.1 20240428
    B 修改启动策略以及授权管理接口报错问题
### 1.13.1 20240427
    B 修复产品树结构无法刷新问题，产品树权限问题，指标积木块钻取明细列表删除问题
### 1.13.1 20240426
    F 【开发任务】套打生成文件时增加盖章功能
### 1.13.1 20240424
    F 迁移poc我的工作台以及华泰柏瑞待办事项功能
### 1.13.1 20240422
    B 修改情景知识标题以及按钮样式
    B 修改girid表格配置文件
### 1.13.1 20240419
    F grid保存列状态可以根据不同用户保存
### 1.13.1 20240418
    B 情景知识位置和样式优化
### 1.13.1 20240417
    F 场景step 消息异常通知 添加 通知模式，间隔时间等字段
    F 指标明细界面增加指标描述信息（业务逻辑）
    B 修改报表稽核el-tabs滚动以及箭头显示和积木定义以及场景构建样式
### 1.13.1 20240415
    B 组合授权页面及控件优化(中间空白行太长,添加移除控件优化)
    B 修改文件图片加载顺序
    B 修复运营日历工作台日期切换类型错误
### 1.13.1 20240412
    F 升级data-audit-ui 版本1.13.1
    F 任务明细支持展示图片形式
    F 套打功能回收
    F 工作台step上添加查看执行记录,异常明细 设置悬浮框位置和大小
### 1.13.0 20240409
    F 工作台step上添加查看执行记录功能按钮,可以查询step每次执行的结果记录
    B 修复产品机构台、机构机构台任务转交问题
    B 修复机构工作台打开明细值传递问题
    B 优化抽屉拖拽的位置
    F 看板定义以及自定义估值看板
### 1.13.0 20240408
    F 增加抽屉拖拽功能
### 1.3.0 20240403
    B 修复工作台表格无法分组问题
### 1.13.0 20240403
    B sql组件不支持检验动态变量（暂时去除sql校验）
### 1.13.6 20240920
    F 修改1.13.4-zxbc-19版本号为1.13.9
### 1.13.0 20240827
    B 修复需求模式不授权模式页签以及登录功能报错问题
### 1.13.0 20240403
    B sql组件不支持检验动态变量（暂时去除sql校验）
    B 修复工作台表格无法分组问题
### 1.13.0 20240402
    F audit-1.2.8
### 1.13.0 20240401
    B:修改场景超时字段OVERTIME为TIMEOUT
### 1.13.0-htbx.11 20240401
    F 升级data-pipe-ui版本 1.1.9 和 data-audit-ui 版本1.2.7
### 1.13.0 20240401
    B 个人工作台日维度 新增场景任务 按钮样式调整
### 1.13.0 20240329
    B 修改场景详情新增step、group按钮样式问题
    B 华泰保兴部门工作台流程完成情况不能跳转到指定的stage问题修复
    F 顶岗功能迁移
### 1.13.0 20240328
    B:日常工作台任务进度以及echart饼图遮挡,异常消息点击交互优化
### 1.13.0 20240327
    B 修改自动估值看板树与多选联动筛选失效问题
    B 添加默认tab模式VUE_APP_TAB配置，默认tab_v2
### 1.13.0 20240326
    B:修改左上角logo图标显示
    B:万家基金-积木复制问题
    F:新增机构工作台
### 1.13.0 20240326
    F 部门工作台新增组件今日任务概览、今日流程完成情况适配华泰保兴需求
    B 自动估值看板修改树默认展开层级、页面跳转、表格自适应问题
    B 自动估值看板估值看板状态去除小数
### 1.13.0 20240320
    F 指标明细界面调整支持用户认证：link:dict_id|url   字典中：dictId:loginUrl dictName:登陆url
    B 日常作业工作台将分组信息与操作分离
### 1.13.0 20240319
    F 影像空间组件升级和报表稽核组件升级为1.2.0
### 1.13.0 20240318
    B 场景构建-日常作业工作台-展示初始节点状态、修复新增任务stage消失问题
### 1.13.0 20240315
    F 场景构建-日常作业工作台-支持展示流程图
### 1.13.0 202403013
    F 场景详情中 场景配置-step-设置 优化交互方式，通过滚动右边表单进行切换，tab作为锚点做到快速定位
### 1.13.0 20240312
    F 个人工作台中日维度，日程提醒中增加用户名称显示
    F 积木块增加跳转到外部系统
### 1.13.0 20240311
    B 修复流程图拖拽至最边时再拖动空白问题
### 1.13.0 20240308
    F 隐藏文档管理中的权限按钮
### 1.13.0 20240307
    B 系统日切调度失败需要发送消息提醒业务日常异常
    F 文档管理中新增权限按钮控制权限，业务管理工作群组档案管理中目录名称调整
    F 【项目需求-百年保险资管】自定义查询支持多数据源
    F 积木定义中，名称列新增悬浮效果，展示描述详情中的信息
### 1.13.0 20240306
    B 刚进入首页网络接口中有报错问题，sit环境进入工作群组会弹出报错问题
    F icon通过系统设置进行配置动态展示
### 1.13.0 20240305
    B 文档管理修复无法展开对应层级节点
### 1.13.0 20240304
    B 优化流程图UI样式，控制流程图显隐
### 1.13.0 20240304
    F 运营日历工作台-添加协作人权限以及对应任务统计
### 1.12.9 20240301
    F 修改机构管理中弹窗会在鼠标滚动时抖动问题
### 1.12.9 20240228
    F 文档管理功能支持多文档上传
    F 文档管理功能文件夹支持权限控制
    F 文档管理功能pdf和图片格式支持预览
    B BUG修改：高亮提示tree节点、图片预览比例调整

### 1.13.0 20240304
    1.13.0版本开始
### 1.12.9 20240227
    F 机构管理中增加机构类型维护的按钮-可以打开机构类型维护页面
### 1.12.9 20240226
    F 积木块新增归属人
### 1.12.9 20240221
    B 修复 ETL采集-多JOB未选择ETL采集，可编辑界面内容，编辑后无法保存以及空指针问题
    B 更新版本号为1.1.7，修复 文件导入配置-接口种类字段表格和详情不一致; 修复 执行方式选择拆分后，文件类型无法选择excel
    B audit-ui 更新 1.1.26 优化指标取数可多选。
### 1.12.9 20240218
    B 任务转发、step协作人权限控制、日程调整、消息盒子调整代码合并。
### 1.12.9 20240204
    B data-pipe-ui-1.1.4,file-deal-ui-1.4.9 解决冲突。
### 1.12.9 20240131
    F audit更新1.1.24
### 1.12.9 20240129
    B 修改机构联系人邮箱的分隔方式为分号分隔
### 1.12.9 20240126
    F 积木块中接口采集-params-json格式框，响应报文json格式框和数据函数规则中-SQL语句。
    B 参数中心-图片引用方法bug修复
### 1.12.9 20240125
    F 消息通知支持钉钉群消息，个人消息推送
    F 增加系统用户扩展信息维护菜单是agnes.dop.gf.user.extend用户扩展信息维护
### 1.12.9 20240124
    F 机构联系人维护期望可以支持维护多个邮件地址
    F audit-ui 依赖1.1.23
### 1.12.8 20240122
    B 群组授权-流程、指标任务数据选择问题修复
### 1.12.8 20240119
    F 参数中心功能合并到研发以及对应依赖版本修改
### 1.12.7 20240119
    B:修改运营日历弹窗的显示位置
    B:修改监控指标配置中运行规则的跨日为不跨日的时候显示错误
### 1.12.7 20240117
    B.tab页签引起超过最大调用堆栈大小问题修复
### 1.12.7 20240115
    B.业务对象2.0-数据模型选择表，总是弹出提示问题修复；显示名称校验补充
    B 自定义查询请求参数放在body中
### 1.12.7 20240104
    F.渠道配置增加钉钉群（机器人）信息配置
    F.消息通知支持钉钉群消息推送
    B.修复triggerUpCol往宽表插很多\的问题，导致启动策略编辑保存失败
### 1.12.6
    20240102
    B 部门工作台 初次进入饼图不显示bug修复
    B 部门工作台 今日排班不自动滚动bug修复
    B 检查项任务看板 重新执行按钮全部可选
    B 个人工作台 修改接口参数和过滤问题
    20231227
    B 个人工作台 点击当天相应的业务要过滤显示；
    B 场景详情路径跳转
    B 积木参数过滤
    B 积木定义 ETL采集，传参组件光标定位错误
    B 指标看板 重新执行按钮显示调整
### 1.12.5 20231222
    F ETL采集支持动态参数开发(单JOB和多JOB)
### 1.12.4
    B 多积木配置bug修复
    F 报表稽核支持过滤条件函数传入
    F audit-ui升级1.1.19，报表稽核支持公式计算的配置
### 1.12.3
    20231226
    F 消息推送step支持发送群聊
    20231220
    B 策略配置-不同类型展示不同内容bug修复
    20231214
    B 菜单搜索优化、刷新跳转历史tab页、去除空菜单模块
    20231129
    B 发件箱-发信功能-发送群聊bug修复
    20231128
    智能渠道配置bug修复
    20231121
    B 策略穿梭框bug修复
    20231120
    B 工作台按钮展示bug修复
    20231116
    报表稽核1.0版本发布
### 1.12.2
    20231106
    1.12.2版本开始
### 1.12.0
    20231106
    F 净值日历功能开发
### 1.11.0-develop
    文档解析智能渠道规则UI适配
    文档解析支持多文件名正则匹配
### 1.11.0
    20231026
    F 部分功能适配兼容火狐浏览器
    20231012
    B 执行状态和节点生命周期状态相关逻辑调整
    B 产品信息维护参数维护BUG修复
    20231010
    F 邮件服务器配置新增“服务器类型”字段
    20230925
    F 参数中心-模版保存
    F 业务树权限功能
    20230915
    F 工作台中图片获取方式调整
    20230911
    F 报表稽核集成
    20230904
    F 场景导入导出
    20230901
    F 工作台代码集成
    B RPA明细界面查询BUG修复
    20230831
    F RPA功能代码合并、指标积木块添加’允许干预通过‘、’允许设置原因‘按钮、添加设置异常原因功能
    20230828
    F 文件处理工作台集成
### 1.10.0
    B 产品信息保存BUG修复
    F 积木块开发规范、工作台调整
    F 业务对象字段新增时，提供刷新按钮。
    B 启动策略-启动参数选不了业务字段BUG修复
    23.08.22
    B 积木定义-配置描述没有做校验问题修复
### 1.9.0
    B 排班明细，调班明细不分页问题
    20.08.18
    F 影像控件升级，解决execl无法打开问题
    F 文件解析，增加特殊字符转义标记
    23.08.09
    F 资料审阅-预览功能升级
    B 指标任务明细查询传参问题修复
    B 低码页面打开BUG修复、事件定义规则选不到对象BUG修复
    23.08.11
    F 场景管理-新增临时任务、场景跟踪功能按钮开发
    F 启动策略-数据现在穿梭框分页BUG修复
    B 日程提醒-修改日期选择框层级问题、产品信息查询数据加载缓慢问题
    23.08.17
    B 项目框架-覆盖消息提醒被全局css污染的样式
    23.08.21
    F 智能渠道模型增加开始标记、结束标记。 增加复制功能
### 1.8.0
    F agnes-common-xmage新增文件预览 该组件接收参数["bizId","fileName"] 且需配置字典XMAGE_CONFIG的commonUrl作为下载URL

### 1.8.0
    F RPA相关功能代码合并
    F agnes-common-xmage新增文件预览 该组件接收参数["bizId","fileName"] 且需配置字典XMAGE_CONFIG的commonUrl作为下载URL
### 1.8.x-0704
    F 文件处理积木中的页面都引用嵌套使用文件处理包中的页面
### 1.9.0
    F 函数API添加请求头配置
    B 优化发件箱的查询（列表查询sql去除大字段相关的字段查询，详情查看时单独查询对应字段）
    F 刷新数据源ui
    F 场景step支持执行方式选择 执行一次，按频率执行，手动执行。兼容老配置
### 1.8.0
    F 业务对象字段新增时，提供刷新按钮。
    F 接口采集积木块新增通用接口json数据处理。
    F 邮箱配置增加邮箱地址。
    F 文件扫描-支持上传ecm。
### 1.7.0
    F 日常作业工作台刷新场景功能，修复刷新遗留bug。
    F 作为制品库合并deal-file-ui功能，修复相关bug，新增部分功能。
    B 文件导出bug修复
### 1.6.03
    1、基金百宝箱右侧底码样式适配bug修复 表格项
    case发布，传给LC的数据在后台组装，并且更新规则中的SQL
    3.F 支持dbf，文本多文件拆分，合并的优化。 1.6版本暂不支持，待1.7版本
### 1.6.03
    1、B调整菜单使其可以在第一层级跳转页面
### 1.6.02
    1、基金百宝箱左侧树形结构样式调整
    1、个人工作台运营日历部分交互调整
### 1.6.01
    20230523
    1.F 业务树管理-高低码跳转功能（基金百宝箱）
    2.日程支持发送消息（微信、邮件）、支持查看待发送消息明细
    3. F datapipe多sheet导入
### 1.5.1
    20230524
    1.F 将个人工作台中的部分按钮从权限控制改为字典值控制 
    方案：在AGNES_LAYOUT_HIDDEN_BUTTON中 对应新增任务addCalenderTask 管控中心controlCenter 新增日程提醒addScheduleRemind 日月维度切换modeChange
### 1.5.05
    20230522
    1、发件箱查询添加密送人，抄送人
    2、解决step消息通知选择微信切换群聊无法保存问题
### 1.5.04
    20230522
    1、修复升级goframe引起的修改密码失败
    2、对项目暂不使用的字体包和图片进行优化删除
### 1.5.0
    20230518 
    B step选择的积木块在编辑时，未查询到积木块信息时，不将原有的积木块数据置空；
    B step启动、超时、异常、完成规则在发布时，time和date类型转成fn类型
    20230517
    B STEP消息通知兼容历史数据
    F ETL采集积木块支持兼容单job任务
    20230517
    1.F ares-ui版本升级5.14.8
    20230508
    1.B 修复单点登录跳转问题
    1.B 优化单点登录后退出登录问题
    20230505
    1.F STEP消息通知支持用户，群组，值班，微信支持发送群聊
    20230428
    B 修复step生命周期中，启动、结束选择规则时，step的开始时间和结束时间不给初始值，导致step启动之后，计划开始和计划结束时间计算不出来BUG
    20230427
    1.F ETL采集积木块支持执行job任务
### 1.3.26 20230426
    1.B 修复step生命周期中，启动、结束选择规则时，step的开始时间和结束时间不给初始值，导致step启动之后，计划开始和计划结束时间计算不出来BUG
### 1.3.26 20230425
    1.F 收件记录列表新增"邮件抓取状态"列
    2.F 发件记录列表新增"失败原因"列
    3.B 修复收件记录列表查询条件"渠道来源"无效BUG

### 1.3.25 未发布20230424
    1.B 查询任务时，未过滤状态BUG修复
### 1.3.24 未发布
    1.F 华泰费用支付外部文件解析添加展示字段
    2.F 华泰备付金保证金模块、费用模块界面产品树形结构调整
    3.B 修复【场景开发-场景管理-场景定义】编辑进入场景设置界面，选择运行参数的展示参数，点击保存报错 BUG2023042106936
    4.B 修复函数规则复制后保存会把原复制函数覆盖问题 BUG2023041906915
    5.B 修复场景定义-消息提醒-取消勾选保存无效问题 BUG2023041906916

### 1.3.23 已发布20230421
    1.B 修复【场景管理-场景开发2.0-场景构建】选择积木块下拉框不能输入，无法检索问题 BUG2023041806899
    2.B 修复【场景管理-场景开发2.0-场景构建】新增后再次编辑“消息推送积木块”，邮件主题及邮件正文为空，且执行异常问题 BUG2023040306775
    3.B 修复【场景管理-场景开发2.0-场景构建】在积木块设置的生命周期里自定义启动，没有条件的非（！）操作, BUG2023041906908
        功能描述：调整替换逻辑，匹配每个规则标签替换成描述文字 将&& || ！替换或与非文字
    4.F 积木快数据采集类（预先注释：渠道采集，rpa采集 功能没实现，不显示）
    5.B agnes-app 头寸管理页面添加产品树
    功能描述：头寸管理页面添加产品树
    6.B agnes-ac 运营日历
    功能描述：新增临时任务选择的事件是通用事件隐藏产品表单，全部维度日历任务已超时可点击完成
    7.B 【场景开发-场景管理-启动策略】相关bug修复,启动参数联动问题， 数据选择数据报错问题 BUG2023040406800
    8.B 登陆后首页报错修复 BUG2023042006922
    9.B 修复【日常作业监控】未授权场景，在个人工作台或部门工作台点击没有未授权提示且直接进入日常作业监控空界面 BUG2023042006921
    10.B etl调度手工执行失败时，返回异常错误信息 BUG2023041406878
### 1.3.22 20230418
    1.F 消息通知微信支持发送指标明细附件
        功能描述：消息通知微信支持发送指标明细附件
    2.B 场景构建中设置->发布参数默认选择日常作业
    3.B 修复积木配置->文件处理->文件导出切换导出规则问题
    4.B 部门工作台查看更多功能逻辑调整

### 1.3.21 20230414
    F agnes-app 华泰头寸管理代码迁移dev2.0
      功能描述：华泰头寸管理模块代码迁移 dev -> dev2.0

### 1.3.20 20230412
    1.F tab栏右键点击功能 全部关闭 关闭当前 关闭其他等
    2.F step重新执行，干预通过逻辑调整
    3.F 个人工作台日历显示当天
### 1.3.19 20230407
    1.F 优化运营日历适配华泰
        功能描述: (1) 个人工作台月维度新增运营日历卡片，并卡片展示每日日历详情，支持筛选数据
                 (2) 个人工作台全部维度页面日期选择框默认当天，将取消完成按钮更改为撤销，放开ObjCode字段
                 (3) 启动策略新增运用日历类型数据选择表格显示多选框
    2.F 点击机器人小智时，弹出快捷导航增加字典值配置 在已有字典值AGNES_LAYOUT_HIDDEN_BUTTON中加入字典项robotDialog
    3.F 单点登录因配置问题无法输入#，兼容/sso写法

#### 1.3.18 20230406
    1.B 小智机器人菜单路由跳转逻辑调整
    2.B 修复新增输入场景名按回车，界面刷新问题
    3.B 修复场景step自定义结束规则选择step无数据问题
    4.B 公共查询组件报错，代码回滚
    5.F 新增启动策略可刷新启动参数字段
    6.B 优化启动策略-日期规则-规则函数打开交互，若无数据提示未找到对应规则
    7.B 优化富文本框空格保存问题
    8.B 修复文件处理保存失败bug

#### 1.3.17 20230404
    1.B 数据函数规则（agnes.define.fun）参数代码进行去空格处理
    2.B 【场景管理-场景开发2.0-场景构建】新增输入场景名按回车阻止页面刷新
    3.B 【场景开发-场景管理-启动策略】启动策略新增，弹框标题名、提示文字做调整
    4.B 【场景开发-场景管理-启动策略】修复选择场景带出启动参数，再选择其他没有启动参数的场景，启动参数不变
    5.B 修复因未配置菜单或菜单配置错误引起的页面跳转报错

#### 1.3.16 20230403
    1.F 支持日程提醒维护批量删除逻辑，个人工作台日程提醒点击查看详情
        功能描述:支持日程提醒维护批量删除逻辑，个人工作台日程提醒点击查看详情
    2.F 优化日程提醒维护页面
    3.B 修复积木块->数据计算类->模型计算中设置运行参数目标值设置无法保存bug
#### 1.3.15 20230330
    1.F 华泰业务模块代码代码从DEV迁移到DEV2.0
        功能描述:华泰业务模块代码从DEV迁移到DEV2.0。
               涉及模块：另类分红、非标付息、费用支付、两金调整及结息
        注：DEV近期仍会有新增改动，后续再提交一次合并      

#### 1.3.14 20230330
    1.F 支持场景STEP消息通知发送指标明细附件或正文
        功能描述：支持场景STEP消息通知发送指标明细附件或正文
    2.F 个人工作台新增按钮增加权限
        功能描述：个人工作台“新增”按钮增加权限控制
        升级方案：在个人工作台中增加按钮权限 agnes.op.calendar.addCalenderTask
    3.F 帆软单点登录
        功能描述：帆软报表单点登录
        升级方案：配置菜单时 配置 link:/fanruan/index.html?url=跳转菜单地址
#### 1.3.13.2 20230329
    1.F 文件处理模块中添加脚本执行功能
        功能描述：在场景配置积木配置->文件处理类新增积木(文件类型:文件处理)->处理过程新增加点类型添加脚本执行按钮
        注意:对于配置字典值需要注意与系统中的枚举类是匹配的
#### 1.3.11.16 20230329
    1.F 运营日历展示日程提醒信息
        功能描述：支持在个人工作台运营日历显示与自己相关的日程提醒
    2.F agnes-app 日程提醒增加紧急度字段以及部分字段名称修改
        功能描述：记录事项调整为标题；提醒日期:调整为日期 ；日历类型:隐藏； 添加紧急度选择:一般，重要，紧急； 日程数据维护页面:用户可以操作自己创建的日程
        升级方案：若需要维护界面增加菜单：菜单地址：agnes.dop.memo.schedule请自行添加
#### 1.3.11.15 20230330
    1.B 修复指标重新执行前端显示问题
        功能描述：修复了指标重新执行后前端显示操作失败bug
#### 1.3.11.14 20230328
    1.F 场景配置中STEP增加消息配置
        功能描述：在场景配置STEP的"设置"->"消息提醒"中增加提前，完成，超时，异常的消息发送配置
        注意：邮件信息：需要手输邮箱；微信信息：只支持发送指定人；
    2、B step修改时间图标无法点击问题;指标重新执行无效问题
        1、优化step修改时间图标无法点击问题
        2、修复指标重新执行无效问题
    3. F 规则表单新增基准日期
       功能描述：规则表单新增基准日期，且规则列表中仅可存在一条基准日期规则和一条固定时间规则。 增加表单依赖关系说明字段，拼接规则表单的标题和函数。
       升级方案：无需升级。
    4. F 个人工作台跳转详情逻辑调整
       功能描述：通过defId判断跳转页面，2:日常作业监控 3:场景概览 4:业务时间工作台 默认跳转日常作业监控
       升级方案：无需升级。

#### 1.3.11.13 20230328
    1. B 场景构建配置step时首次保存频率无法反显问题
        功能描述：修复了此问题。
        升级方案：无需升级。
    2. B 单点登录功能：退出登录逻辑调整
        功能描述：修复了此问题。
        升级方案：无需升级。
    3. F bpnm-ui依赖升级
        功能描述：升级了bpnm-ui 0.0.18版本。
        升级方案：拉取代码后yarn install。
    4. F 选人组件新增全选功能
        功能描述：新增日程时选人组件仅显示用户，增加全选功能与快速过滤联动。
        升级方案：无需升级。
    5. F 工作群组授权-用户管理新增时表格快速过滤时全选功能优化
        功能描述：筛选时可全选并与快速过滤联动、表格分页调整。
        升级方案：无需升级。
    6. B 富文本输入丢失光标后无法输入问题修复
        功能描述：将blot的contenteditable属性设置为false的逻辑删除。
        升级方案：无需升级。

#### 1.3.11.12 20230324
    1. F 增加首页布局tab可配置性
        功能描述：首页布局tab可配置，默认只打开个人工作台。
        升级方案：配置字典值： HOMEPAGE_DEFAULT_TAB  首页默认打开菜单 字典项：agnes.op.calendar.wb 个人工作台、 datav.dep.view 部门工作台。
    2. F 个人工作台配置页面按钮权限
        功能描述：配置工作台菜单，添加(管控中心 日 月 全部)按钮权限控制。
        升级方案：配置菜单权限及按钮 参考dev环境工作台部分菜单内容。
    3. F 个人工作台右侧卡片可配置
        功能描述：个人工作台右侧卡片可通过字典配置，目前上下布局可配，如不配置另一卡片撑全部高度。
        升级方案：配置字典值：PERSONAL_WORKBENCH_LAYOUT_RIGHT_TOP 个人工作台右上卡片布局、PERSONAL_WORKBENCH_LAYOUT_RIGHT_BOTTOM 个人工作台右下卡片布局。
        字典项：operate我的日历、task场景任务、todo待办任务、schedule日程提醒。
    4. F 首页配置可隐藏项
        功能描述：首页布局右上方功能按钮以及点击用户名下拉菜单内容可配置隐藏项
        升级方案：需配置字典：AGNES_LAYOUT_HIDDEN_BUTTON 字典项：全部关闭:allClose 快捷导航 quickEntrance 搜索 search 全屏 fullScreen 换肤 changeSkin 消息提醒 message 面板配置 PanelSetting 意见反馈feedBack 帮助中心 helpCenter 修改密码 changePassword。
    5. F 首页布局右上方新增快捷导航功能
        功能描述：首页布局右上方新增快捷导航功能，可配置快捷导航菜单。
        升级方案：需配置icon和字典值，icon图片放置src/assets/quick-entrance-icon目录下，当作为制品库时需同时新建该目录。
        字典值配置 AGNES_QUICK_ENTRANCE 首页快捷入口 字典项 字典项值配置url携带http(s) 备用字段1配置label名称 备用字段2配置icon图片名称(带后缀)
