<template>
  
  <el-dropdown class="gf-export ml10" @command="commandExport">
    <gf-button :class="className" size="mini">
      导出
      <i class="el-icon-arrow-down el-icon--right"></i>
    </gf-button>
    <el-dropdown-menu slot="dropdown">
      <el-dropdown-item command="xlsx" v-if="exportType.includes('xlsx')"> XLSX格式 </el-dropdown-item>
      <el-dropdown-item command="txt" v-if="exportType.includes('txt')"> TXT格式 </el-dropdown-item>
    </el-dropdown-menu>
  </el-dropdown>
</template>

<script>
export default {
  name: "gf-export",
  props: {
    className: {
      type: String,
    },
    exportType: {
      type: String,
      default: "xlsx",
    },
  },
  data() {
    return {};
  },

  methods: {
    commandExport(fileType) {
      this.$emit("click", fileType);
    },
  },
};
</script>

<style lang="less" scoped>
.gf-export {
  ::v-deep {
    .el-icon-arrow-down {
      height: 13px;
    }
  }
}
</style>
