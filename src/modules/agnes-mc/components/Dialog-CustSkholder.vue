<template>
    <div class="CustSkholder">
        <el-form ref="formData" label-width="120px" style="padding-right: 20px;" :model="form" :rules="rules">
            <el-form-item label="股东名称" prop="holdName">
                <gf-input v-model="form.holdName" :max-byte-len="80" />
            </el-form-item>

            <el-form-item label="出资金额(万元)" prop="actualAmount">
                <gf-input-money v-model="form.actualAmount" :max-byte-len="19" />
            </el-form-item>

            <el-form-item label="持股比例(%)" prop="holdPct">
                <gf-input v-model="form.holdPct" v-rate="2">
                    <span slot="append">%</span>
                </gf-input>
            </el-form-item>

            <el-form-item label=" " prop="">当前所有股东持股:{{ holdPctTotal }}%</el-form-item>
        </el-form>
        <div class="center">
            <gf-button @click="handleSave">确定</gf-button>
            <gf-button @click="close">取消</gf-button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CustSkholder',
    props: {
        row: {
            type: Object
        },
        index: {},
        list: {}
    },
    data() {
        let form = this.row ? this.$lodash.cloneDeep(this.row) : {};

        return {
            tlist: this.$lodash.cloneDeep(this.list) || [],
            form,
            rules: {
                holdName: [{ required: true, message: '股东名称必填', trigger: 'change' }],
                actualAmount: [{ required: true, message: '出资金额必填', trigger: 'change' }],
                holdPct: [
                    { required: true, message: '持股比例必填', trigger: 'change' },
                    {
                        validator: (rule, value, callback) => {
                            if (value > 100) {
                                callback(new Error('持股比例大于100%'));
                            }

                            if (this.holdPctTotal > 100) {
                                callback(new Error(`所有股东持股大于100%,不能操作`));
                            }

                            callback();
                        },
                        trigger: 'change'
                    }
                ]
            }
        };
    },
    computed: {
        holdPctTotal() {
            let arr = this.$lodash.cloneDeep(this.list);
            if (this.index >= 0) {
                arr[this.index] = { ...this.form, holdPct: this.form.holdPct ? this.form.holdPct : 0 };
            } else {
                arr.push({ ...this.form, holdPct: this.form.holdPct ? this.form.holdPct : 0 });
            }
            let ttotal = 0;
            arr.map(m => (ttotal += Number(m.holdPct)));
            return Math.floor(ttotal * 100) / 100;
        }
    },

    methods: {
        /**
         * 确定
         */
        handleSave() {
            this.$refs['formData'].validate(valid => {
                if (!valid) {
                    return false;
                }
                let string = JSON.stringify({ btnType: 'ok', form: this.form, operate: '' });
                this.close(string);
            });
        },
        /**
         * 取消
         */
        close(action) {
            this.$dialog.close(this, action);
        }
    }
};
</script>
