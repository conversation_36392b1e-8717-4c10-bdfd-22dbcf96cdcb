<!--
 * @Des: 
 * @version: 
 * @Author: liu<PERSON>ifei
 * @Date: 2024-07-22 14:13:59
 * @LastEditors: liufeifei
 * @LastEditTime: 2024-07-30 15:32:45
-->
<template>
    <div class="Finreport">
        <el-form ref="formData" label-width="130px" style="padding-right: 20px;" :model="form" :rules="rules">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="会计期间" prop="reportYear">
                        <el-date-picker type="year" v-model="form.reportYear" value-format="yyyy" style="width: 98%;" />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="" label-width="0" prop="reportDateType">
                        <el-select v-model="form.reportDateType" style="width: 100%;">
                            <el-option label="一季报" value="1"></el-option>
                            <el-option label="半年报" value="2"></el-option>
                            <el-option label="三季报" value="3"></el-option>
                            <el-option label="年报" value="4"></el-option>
                        </el-select>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-form-item label="总资产(万元)" prop="totAsset">
                <gf-input-money v-model="form.totAsset" :max-byte-len="23" />
            </el-form-item>

            <el-form-item label="总负债(万元)" prop="totDebts">
                <gf-input-money v-model="form.totDebts" :max-byte-len="23" />
            </el-form-item>

            <el-form-item label="净资产(万元)" prop="netAsset">
                <gf-input-money v-model="form.netAsset" :max-byte-len="23" />
            </el-form-item>

            <el-form-item label="营业收入(万元)" prop="bizIncome">
                <gf-input-money v-model="form.bizIncome" :max-byte-len="23" />
            </el-form-item>

            <el-form-item label="净利润(万元)" prop="netProfit">
                <gf-input-money v-model="form.netProfit" :max-byte-len="23" />
            </el-form-item>
        </el-form>
        <div class="center">
            <gf-button @click="handleSave">确定</gf-button>
            <gf-button @click="close">取消</gf-button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'Finreport',
    props: {
        row: {
            type: Object
        },
        index: {}
    },
    data() {
        let form = this.row ? this.$lodash.cloneDeep(this.row) : {};
        return {
            form,

            rules: {
                reportYear: [{ required: true, message: '会计期间必填', trigger: 'change' }],
                reportDateType: [{ required: true, message: '会计期间必填', trigger: 'change' }],
                totAsset: [{ required: true, message: '总资产必填', trigger: 'change' }],
                totDebts: [{ required: true, message: '总负债必填', trigger: 'change' }],
                netAsset: [{ required: true, message: '净资产必填', trigger: 'change' }],
                bizIncome: [{ required: true, message: '营业收入必填', trigger: 'change' }],
                netProfit: [{ required: true, message: '净利润必填', trigger: 'change' }]
            }
        };
    },

    methods: {
        /**
         * 确定
         */
        handleSave() {
            this.$refs['formData'].validate(valid => {
                if (!valid) {
                    return false;
                }
                let string = JSON.stringify({ btnType: 'ok', form: this.form, operate: '' });
                this.close(string);
            });
        },
        /**
         * 取消
         */
        close(action) {
            this.$dialog.close(this, action);
        }
    }
};
</script>
