<template>
    <!-- 外部机构信息查询 -->
    <div class="page external" style="height: 100%">
        <gf-search-box ref="box"
                       title="筛选条件"
                       :is-show-select=false
        >
            <template slot="content">
                <gf-form ref="gfForm"
                         label-width="100px"
                         :form-items="formItems"
                         :mode="'search'"
                ></gf-form>
            </template>
        </gf-search-box>
        <gf-grid ref="grid" grid-no="ares-partnerManager-external" :query-args="queryArgs" @row-double-click="toDetails">
            <template slot="left">
                <gf-button class="action-btn" size="mini" v-if="$hasPermission('btn.partnerManager.external.sync')">同步外部机构</gf-button>
                <gf-export @click="exportList" v-if="$hasPermission('btn.partnerManager.external.export')" />
            </template>
        </gf-grid>
    </div>
</template>

<script>


export default {
    name: "external",
    data() {
        return {
            queryArgs: {
                compId: "",
                compType: "",
                entNature: ''
                },
            compList: [],
            keyWord: '',
            formItems: this.$formUtils.formatFormItems([
                {key: 'compId', type: 'select',name: '机构名称:',value: '',options: [],props: {clearable:true, filterable: true,remote: true, remoteMethod: this.loadData}},
                {key: 'compType', type: 'dict',name: '机构类型:',value: '', dict:'CUST_TYPE',props:{multiple: true, collapseTags: true, clearable: true}},
                {key: 'entNature', type: 'dict',name: '企业性质:',value: '', dict:'ENT_NATURE',props:{multiple: true, collapseTags: true, clearable: true}},
                {type: 'children', childItems: [
                        {type: 'button',action: 'reset',name: '重置',click:()=>{this.reset()}},
                        {type: 'button',action: 'open',},
                        {type: 'button',action: 'search',name: '查询',click:()=>{this.onSearch()}},
                    ]},
            ]),
        }
    },
    methods: {
        onSearch() {
            Object.assign(this.queryArgs, this.formItems.getValues())
            this.$refs.grid.reloadData(true);
        },
        toDetails(row) {
            let { compId } = row.data
            let pageView = this.$app.views.getView('gf.partnerManager.external.details');
            pageView.title = "外部机构详情"
            let tabView = Object.assign({ args: { compId }, id: 'details' + compId }, pageView);
            this.$nav.showView(tabView);
        },

        /**
         * 导出
         */
        exportList(fileType) {
            let params = {
                filename: '外部机构.' + fileType,
                fileType,
                ...this.queryArgs,
            }
            if (this.selRow && this.selRow.length > 0) {
                params.list = this.selRow
            }

            this.$api.GFPartnerManagerApi.exportCompExportList(params)
        },
        loadData(s) {
            this.keyWord = s
            this.$agnesUtils.debounce(this.loadCompList, 400)
        },
        loadCompList() {
            this.$api.GFPartnerManagerApi.compQueryList({
                compName: this.keyWord,
                pageNum: 1,
                pageSize: 100
            }).then(res => {
                this.formItems.getItem('compId').options = res.data.list.map(item => {
                    return {
                        id: item.compId,
                        label: item.compName,
                    }
                })
                this.compList = res.data.list

            })
        },
        reset() {
            this.formItems.resetValues()
        }
    },

}
</script>

<style lang="less" scoped>
.external {
    display: flex;
    flex-direction: column;


    ::v-deep {
        .ag-grid-box .grid-action-panel .toolbar-left {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            -webkit-box-pack: start;
            -ms-flex-pack: start;
            justify-content: flex-start;
            -webkit-box-align: center;
            -ms-flex-align: center;
            align-items: center;
            margin-top: 15px;
        }
    }
}
</style>
