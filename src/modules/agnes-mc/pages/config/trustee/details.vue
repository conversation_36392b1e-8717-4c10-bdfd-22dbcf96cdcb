<template>
    <div class="page trustee-details">
        <div class="page-title">境外托管人详情</div>
        <el-form ref="formData" class="page-form" label-width="140px" :model="form" :rules="rules">
            <el-row>
                <el-col :span="12">
                    <el-form-item label="境外托管人名称" prop="trusteeName">
                        <gf-input v-model="form.trusteeName" disabled />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="所在国家/地区" prop="country">
                        <gf-input v-model="form.country" disabled />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="12">
                    <el-form-item label="法定代表人">
                        <gf-input v-model="form.legrePerson" disabled />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实际控制人">
                        <gf-input v-model="form.actualPerson" disabled />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="12">
                    <el-form-item label="成立日期" prop="foundDate">
                        <el-date-picker v-model="form.foundDate" type="date" style="width: 100%;" value-format="yyyy-MM-dd" disabled />
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="人员数量">
                        <gf-input v-model="form.workforce" inputLang="num" disabled />
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="12">
                    <el-form-item label="注册资本(万元)">
                        <el-row>
                            <el-col :span="18">
                                <gf-input-money disabled v-model="form.regCaptial" :max-byte-len="19" />
                            </el-col>
                            <el-col :span="6">
                                <gf-dict style="width: 100%;" v-model="form.rcCur" dictType="ACCT_CURR" disabled />
                            </el-col>
                        </el-row>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="实收资本(万元)">
                        <el-row>
                            <el-col :span="18">
                                <gf-input-money disabled v-model="form.paidinCaptial" :max-byte-len="19" />
                            </el-col>
                            <el-col :span="6">
                                <gf-dict style="width: 100%;" v-model="form.pcCur" dictType="ACCT_CURR" disabled />
                            </el-col>
                        </el-row>
                    </el-form-item>
                </el-col>
            </el-row>

            <el-row>
                <el-col :span="12">
                    <el-form-item label="托管规模(亿元)">
                        <el-row>
                            <el-col :span="18">
                                <gf-input-money disabled v-model="form.trScale" :max-byte-len="19" />
                            </el-col>
                            <el-col :span="6">
                                <gf-dict style="width: 100%;" v-model="form.tsCur" dictType="ACCT_CURR" disabled />
                            </el-col>
                        </el-row>
                    </el-form-item>
                </el-col>
                <el-col :span="12">
                    <el-form-item label="托管收入(万元)">
                        <el-row>
                            <el-col :span="18">
                                <gf-input-money disabled v-model="form.trIncome" :max-byte-len="19" />
                            </el-col>
                            <el-col :span="6">
                                <gf-dict style="width: 100%;" v-model="form.tiCur" dictType="ACCT_CURR" disabled />
                            </el-col>
                        </el-row>
                    </el-form-item>
                </el-col>
            </el-row>

            <div class="v-section" v-if="isShowUpdate">
                <h3 class="sub-title">附件信息</h3>
                <div>
                    <gf-upload-ecm-file ref="upFile" :state="state"></gf-upload-ecm-file>
                </div>
            </div>
        </el-form>
        <div class="form-btn-group center mt20">
            <el-button @click="close">返回</el-button>
        </div>
    </div>
</template>

<script>
export default {
    name: 'trustee-details',
    props: {
        trusteeId: { type: String, require: false }
    },
    data() {
        return {
            form: {
                trusteeName: '',
                country: '',
                foundDate: '',
                workforce: '',
                regCaptial: '',
                rcCur: '01',
                paidinCaptial: '',
                pcCur: '01',
                legrePerson: '',
                actualPerson: '',
                trScale: '',
                tsCur: '01',
                trIncome: '',
                tiCur: '01'
            },

            rules: {
                trusteeName: [{ required: true, message: '请输入境外托管人名称', trigger: 'blur' }],
                country: [{ required: true, message: '请输入所在国家/地区', trigger: 'blur' }],
                foundDate: [{ required: true, message: '请输入成立日期', trigger: 'blur' }]
            },

            state: {
                groupId: '', // 业务id
                batchId: '', // 信雅达文件批次编号
                history: false, // 是否操作历史表, true: 查询历史表或向历史表插入数据
                handUp: false, // 是否手动上传, true: 选取文件后点击上传, false: 文件一旦被选择立即上传
                autoLoad: false, // 是否立即查询数据(例如: 修改时页面一旦被打开 根据 groupId、batchId、queryArgs 立即查询数据)
                limit: 999, // 文件限制上传数量
                forceLimit: '00', // 强制限制文件数量
                accept: '', // 文件限制上传类型
                toolBar: 'download,preview', // 该组件按钮选择
                procNode: 'Default', // 文件上传节点或文件标识(默认 Default)
                archiveJunior: 'Default', // 文件归档一级(类似菜单)(默认 Default)
                archiveMiddle: 'Default', // 文件归档二级(类似菜单)(默认 Default)
                archiveSenior: 'Default', // 文件归档三级(类似菜单)(默认 Default)
                queryArgs: {}, // 查询参数对象 若使用该对象 请了解使用规则; 禁止空value
                cells: {
                    type: {
                        vis: true, // 是否显示
                        dValue: '', // 字典默认值
                        mValue: '', // 字典必输值 必须至少一条数据含有该数据
                        dic: 'ECM_FILE_TYPE', // 字典名称
                        inc: ['15', '20'], // 该字典只显示的配置项
                        exc: [] // 该字典除配置项其余都显示
                    },
                    version: {
                        vis: false,
                        dValue: '',
                        mValue: '',
                        dic: 'ECM_FILE_VERSION',
                        inc: [],
                        exc: []
                    },
                    unit: {
                        vis: false
                    }
                },
                forceAssert: false,
                mFile: false
            }
        };
    },
    computed: {
        isShowUpdate() {
            return this.$store.getters.isShowUpdate;
        }
    },
    methods: {
        onTabActived() {
            this.trusteeId && this.getdetails();
        },
        /**
         * 获取详情
         */
        getdetails() {
            this.$api.GFPartnerManagerApi.trusteeDetails({ trusteeId: this.trusteeId }).then(res => {
                this.form = res.data;

                if (this.isShowUpdate) {
                    this.state.groupId = this.trusteeId || '';
                    this.state.groupId && this.$refs.upFile.loadEcmFiles(true);
                }
            });
        },

        /**
         * 返回
         */
        close() {
            this.$nav.closeCurrentTab({ closeCmd: 'gf.partnerManager.trustee' });
        }
    }
};
</script>

<style lang="less" scoped>
.trustee-details {
    ::v-deep {
        .el-form-item {
            width: 95%;
        }
    }

    .center {
        display: flex;
        justify-content: center;
        align-items: center;
    }
}
</style>
