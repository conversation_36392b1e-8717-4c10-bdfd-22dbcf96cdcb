<template>
    <!--暂存 管理人 记录修改   不走工作流-->
    <div class="page custodianNotes-edit" ref="custodianNotes-edit">
        <div class="page-title">管理人准入申请修改</div>
        <gf-right-nav pageName='custodianNotes-edit' />
        <el-form ref="formData" class="page-form" label-width="140px" style="padding-right:170px" :model="form" :rules="rules">
            <div class="v-section">
                <h3 class="sub-title">申请信息</h3>
                <div>
                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="申请人">
                                <gf-input disabled v-model="proposer.reqUserName" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="申请机构">
                                <gf-input disabled v-model="proposer.reqOrgName" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="申请日期">
                                <gf-input disabled v-model="proposer.reqTime" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <div class="v-section">
                <h3 class="sub-title">基本信息</h3>
                <div>
                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="管理人名称" prop="custName">
                                <gf-part-external v-model="form.custName" @change="changeCustName" ref="gfPartExternal" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="管理人类型" prop="custType">
                                <gf-dict v-model="form.custType" dictType="CUST_TYPE" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="成立日期" prop="foundDate">
                                <el-date-picker type="date" v-model="form.foundDate" style="width:100%" value-format="yyyy-MM-dd" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="注册资本(万元)" prop="regCaptial">
                                <gf-input-money v-model="form.regCaptial" :max-byte-len='19' />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="实收资本(万元)" prop="paidinCaptial">
                                <gf-input-money v-model="form.paidinCaptial" :max-byte-len='19' />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="法定代表人" prop="legrePerson">
                                <gf-input v-model="form.legrePerson" :max-byte-len='300' />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="实际控制人" prop="actualPerson">
                                <gf-input v-model="form.actualPerson" :max-byte-len='300' />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="统一社会信用代码" prop="creditCode">
                                <gf-input v-model="form.creditCode" clear-regex="[^\dA-Za-z]" :max-byte-len='18' @input="val=>form.creditCode=val.toLocaleUpperCase()" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="组织机构代码证" prop="organizationCode">
                                <gf-input v-model="form.organizationCode" :max-byte-len="9" clear-regex="[^\dA-Za-z]" @input="val=>form.organizationCode=val.toLocaleUpperCase()" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="人员数量" prop="workforce">
                                <gf-input v-model="form.workforce" inputLang="num" :max-byte-len='10' />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="管理规模(亿元)" prop="manageScale">
                                <gf-input-money v-model="form.manageScale" :max-byte-len='19' />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="监管评级">
                                <gf-input v-model="form.superviseGrade" inputLang="code" :max-byte-len='10' />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="规模是否排名同行业前50%" prop="tradeRank">
                                <gf-dict v-model="form.tradeRank" dictType="GF_BOOL_TYPE" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="企业性质" prop="entNature">
                                <gf-dict v-model="form.entNature" dictType="ENT_NATURE" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="关联方类型" prop="isSelfRelate">
                                <gf-dict v-model="form.isSelfRelate" dictType="CUST_RELATE_TYPE" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="是否为国有控股" prop="isStateOwn">
                                <gf-dict v-model="form.isStateOwn" dictType="GF_BOOL_TYPE" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="注册地址" prop="regAddr">
                                <gf-input type="textarea" resize="none" :autosize="{minRows: 2, maxRows: 6}" v-model="form.regAddr" :max-byte-len='600' />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="经营地址" prop="bizAddr">
                                <gf-input type="textarea" resize="none" :autosize="{minRows: 2, maxRows: 6}" v-model="form.bizAddr" :max-byte-len='600' />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="经营范围" prop="bizScope">
                                <gf-input type="textarea" resize="none" :autosize="{minRows: 2, maxRows: 6}" v-model="form.bizScope" :max-byte-len='3000' />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="经营状况" prop="bizSituation">
                                <gf-input type="textarea" resize="none" :autosize="{minRows: 2, maxRows: 6}" v-model="form.bizSituation" :max-byte-len='3000' />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="公司治理及内控情况" prop="compGvnCtrl">
                                <gf-input type="textarea" resize="none" :autosize="{minRows: 2, maxRows: 6}" v-model="form.compGvnCtrl" :max-byte-len='3000' />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <div class="v-section">
                <h3 class="sub-title">股东信息</h3>
                <div>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="股东列表" required>

                                <el-table border :data="form.stakeholderList">
                                    <el-table-column label="股东名称" prop="holdName" />
                                    <el-table-column label="出资金额(万元)" prop="actualAmount">
                                        <div slot-scope="slot">
                                            {{formatMoney(slot.row.actualAmount)}}
                                        </div>
                                    </el-table-column>
                                    <el-table-column label="持股比例(%)" prop="holdPct" />
                                    <el-table-column label="操作" align="center" width="150">
                                        <template slot-scope="slot">
                                            <i class="el-icon-edit mr10" style="color:red;cursor: pointer" @click="handleOpen('stakeholderList',slot)" />
                                            <i class="el-icon-delete" style="color:red;cursor: pointer" @click="form.stakeholderList.splice(slot.$index,1)" />
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <gf-button size="mini" @click="handleOpen('stakeholderList')">新增</gf-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <div class="v-section">
                <h3 class="sub-title">高管信息</h3>
                <div>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="高管信息列表" required>

                                <el-table border :data="form.seniorManagerList">
                                    <el-table-column label="姓名" prop="psname" />
                                    <el-table-column label="职务" prop="duty" />
                                    <el-table-column label="履历" prop="resume" />
                                    <el-table-column label="操作" align="center" width="150">
                                        <template slot-scope="slot">
                                            <i class="el-icon-edit mr10" style="color:red;cursor: pointer" @click="handleOpen('seniorManagerList',slot)" />
                                            <i class="el-icon-delete" style="color:red;cursor: pointer" @click="form.seniorManagerList.splice(slot.$index,1)" />
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <gf-button size="mini" @click="handleOpen('seniorManagerList')">新增</gf-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <div class="v-section">
                <h3 class="sub-title">资质情况</h3>
                <div>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="资质列表" required>

                                <el-table border :data="form.assetMgqualList">
                                    <el-table-column label="资质名称" prop="qualName" />
                                    <el-table-column label="批复机构" prop="approvalOrg" />
                                    <el-table-column label="获批日期" prop="beginDate" />
                                    <el-table-column label="操作" align="center" width="150">
                                        <template slot-scope="slot">
                                            <i class="el-icon-edit mr10" style="color:red;cursor: pointer" @click="handleOpen('assetMgqualList',slot)" />
                                            <i class="el-icon-delete" style="color:red;cursor: pointer" @click="form.assetMgqualList.splice(slot.$index,1)" />
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <gf-button size="mini" @click="handleOpen('assetMgqualList')">新增</gf-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <div class="v-section">
                <h3 class="sub-title">财务状况</h3>
                <div>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="财务信息列表" required>

                                <el-table border :data="form.financeReportList">
                                    <el-table-column label="会计期间" prop="reportYear">
                                        <div slot-scope="slot">
                                            {{slot.row.reportYear}}{{ {'1':'一季报','2':'半年报','3':'三季报','4':'年报'}[slot.row.reportDateType]}}
                                        </div>
                                    </el-table-column>
                                    <el-table-column label="总资产(万元)" prop="totAsset">
                                        <div slot-scope="slot">
                                            {{formatMoney(slot.row.totAsset)}}
                                        </div>
                                    </el-table-column>
                                    <el-table-column label="总负债(万元)" prop="totDebts">
                                        <div slot-scope="slot">
                                            {{formatMoney(slot.row.totDebts)}}
                                        </div>
                                    </el-table-column>
                                    <el-table-column label="净资产(万元)" prop="netAsset">
                                        <div slot-scope="slot">
                                            {{formatMoney(slot.row.netAsset)}}
                                        </div>
                                    </el-table-column>
                                    <el-table-column label="营业收入(万元)" prop="bizIncome">
                                        <div slot-scope="slot">
                                            {{formatMoney(slot.row.bizIncome)}}
                                        </div>
                                    </el-table-column>
                                    <el-table-column label="净利润(万元)" prop="netProfit">
                                        <div slot-scope="slot">
                                            {{formatMoney(slot.row.netProfit)}}
                                        </div>
                                    </el-table-column>
                                    <el-table-column label="操作" align="center" width="150">
                                        <template slot-scope="slot">
                                            <i class="el-icon-edit mr10" style="color:red;cursor: pointer" @click="handleOpen('financeReportList',slot)" />
                                            <i class="el-icon-delete" style="color:red;cursor: pointer" @click="form.financeReportList.splice(slot.$index,1)" />
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <gf-button size="mini" @click="handleOpen('financeReportList')">新增</gf-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <div class="v-section">
                <h3 class="sub-title">合法合规情况</h3>
                <div>
                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="洗钱评估结果" prop="amlAssess">
                                <gf-dict v-model="form.amlAssess" dictType="CUST_RISK_LEVEL" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="反洗钱风险管理制度和组织体系建设情况" prop="amlDevelop">
                                <gf-input type="textarea" resize="none" :autosize="{minRows: 2, maxRows: 6}" v-model="form.amlDevelop" :max-byte-len='3000' />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="管理人有无不良声誉或负面舆情" label-width="420px">
                                

                                <gf-dict-radio-group class="mr10" v-model="form.isBadFame" dictType='GF_BOOL_TYPE'></gf-dict-radio-group>
                                <el-link type="primary" :underline='false' v-if="form.creditCode" @click="showNews">查看舆情</el-link>
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="24">
                            <el-form-item label="近三年是否存在重大违规行为或被采取重大行政监管措施等" label-width="420px">
                                
                                <gf-dict-radio-group class="mr10" v-model="form.isIllegal" dictType='GF_BOOL_TYPE'></gf-dict-radio-group>
                                <el-link type="primary" :underline='false' v-if="form.creditCode" @click="showNews">查看舆情</el-link>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <div class="v-section">
                <h3 class="sub-title">调查人信息</h3>
                <div>
                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="调查日期" prop="investDate">
                                <el-date-picker type="date" v-model="form.investDate" style="width:100%" value-format="yyyy-MM-dd" :picker-options="pickerOptions" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="调查机构" prop="investOrg">
                                <gf-market-orgs v-model="form.investOrg" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="调查人1" prop="investigator1">
                                <gf-input v-model="form.investigator1" :max-byte-len='10' />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系方式" prop="contact1">
                                <gf-input v-model="form.contact1" oninput="value = value.replace(/[^0-9\-\+]/g, '')" :max-byte-len="17" />
                            </el-form-item>
                        </el-col>
                    </el-row>

                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="调查人2" prop="investigator2">
                                <gf-input v-model="form.investigator2" :max-byte-len='10' />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="联系方式" prop="contact2">
                                <gf-input v-model="form.contact2" oninput="value = value.replace(/[^0-9\-\+]/g, '')" :max-byte-len="17" />
                            </el-form-item>
                        </el-col>
                    </el-row>
                </div>
            </div>

            <div class="v-section">
                <h3 class="sub-title">附件信息</h3>
                <div>
                    <gf-upload-ecm-file ref="upFile" :state="state"></gf-upload-ecm-file>
                </div>
            </div>

        </el-form>
        <div class="form-btn-group center mt20">
            <gf-button @click="handleStorage">暂存</gf-button>
            <gf-button @click="handleSave">提交</gf-button>
            <gf-button @click="close">返回</gf-button>
            <gf-acct-show-bpmn :processKey="processKey" v-if="processKey" />
        </div>
    </div>
</template>

<script>

import CustSkholder from "../../../components/Dialog-CustSkholder";
import CustManager from "../../../components/Dialog-CustManager";
import Amqual from "../../../components/Dialog-Amqual";
import Finreport from "../../../components/Dialog-Finreport";
export default {
    name: "custodianNotes-edit",
    props: {
        recordId: {
            type: String
        },
    },
    data() {
        return {
            processKey: '',
            formatMoney: this.$fmt.formatMoney,
            form: {
                "reqUser": "",
                "reqTime": "",
                "custName": "",
                "custType": "",
                "compRegno": null,
                "foundDate": null,
                "regCaptial": null,
                "paidinCaptial": null,
                "legrePerson": null,
                "actualPerson": null,
                "creditCode": null,
                "organizationCode": null,
                "workforce": null,
                "manageScale": null,
                "superviseGrade": null,
                "tradeRank": null,
                "entNature": null,
                "isSelfRelate": null,
                "isStateOwn": null,
                "bizScope": null,
                "regAddr": null,
                "bizAddr": null,
                "bizSituation": null,
                "compGvnCtrl": null,
                "amlAssess": null,
                "amlDevelop": null,
                "isBadFame": null,
                "isIllegal": null,
                "investDate": null,
                "investOrg": null,
                "investigator1": null,
                "contact1": null,
                "investigator2": null,
                "contact2": null,
                "stakeholderList": [],     //股东列表 
                "seniorManagerList": [],          //高管列表
                "assetMgqualList": [],               //资质列表
                "financeReportList": []            //财务列表
            },

            proposer: {
                reqUser: '',
                reqUserName: '',
                reqOrg: '',
                reqOrgName: '',
                reqTime: '',
            },

            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now()
                }
            },

            rules: {
                custName: [
                    { required: true, message: '管理人名称必填', trigger: 'change' },
                ],
                custType: [
                    { required: true, message: '管理人类型必填', trigger: 'change' },
                ],
                foundDate: [
                    { required: true, message: '成立日期必填', trigger: 'change' },
                ],
                regCaptial: [
                    { required: true, message: '注册资本必填', trigger: 'change' },
                ],
                paidinCaptial: [
                    { required: true, message: '实收资本必填', trigger: 'change' },
                ],
                legrePerson: [
                    { required: true, message: '法定代表人必填', trigger: 'change' },
                ],
                actualPerson: [
                    { required: true, message: '实际控制人必填', trigger: 'change' },
                ],
                creditCode: [
                    { required: true, message: '统一社会信用代码必填', trigger: 'change' },
                    { pattern: /[\dA-Z]{18}/, message: '18位数字或大写字母', trigger: 'change' }
                ],
                organizationCode: [
                    { required: true, message: '组织机构代码证必填', trigger: 'change' },
                    { pattern: /[\dA-Z]{9}/, message: '10位数字或大写字母', trigger: 'change' }
                ],
                workforce: [
                    { required: true, message: '人员数量必填', trigger: 'change' },
                ],
                manageScale: [
                    { required: true, message: '管理规模必填', trigger: 'change' },
                ],
                tradeRank: [
                    { required: true, message: "规模是否排名同行业前50%", trigger: 'change' },
                ],
                entNature: [
                    { required: true, message: '企业性质必填', trigger: 'change' },
                ],
                isSelfRelate: [
                    { required: true, message: '关联方类型必填', trigger: 'change' },
                ],
                isStateOwn: [
                    { required: true, message: '是否为国有控股必填', trigger: 'change' },
                ],
                regAddr: [
                    { required: true, message: '注册地址必填', trigger: 'change' },
                ],
                bizAddr: [
                    { required: true, message: '经营地址必填', trigger: 'change' },
                ],
                bizScope: [
                    { required: true, message: '经营范围必填', trigger: 'change' },
                ],
                bizSituation: [
                    { required: true, message: '经营状况必填', trigger: 'change' },
                ],
                compGvnCtrl: [
                    { required: true, message: '公司治理及内控情况必填', trigger: 'change' },
                ],
                investDate: [
                    { required: true, message: '调查日期必填', trigger: 'change' },
                ],
                investOrg: [
                    { required: true, message: '调查机构必填', trigger: 'change' },
                ],
                investigator1: [
                    { required: true, message: '调查人必填', trigger: 'change' },
                ],
                contact1: [
                    { required: true, message: '联系方式必填', trigger: 'change' },

                ],
                investigator2: [
                    { required: true, message: '调查人必填', trigger: 'change' },
                ],
                contact2: [
                    { required: true, message: '联系方式必填', trigger: 'change' },

                ],
                // amlAssess: [
                //     { required: true, message: '洗钱评估结果必填', trigger: 'change' },
                // ],
                amlDevelop: [
                    { required: true, message: '反洗钱风险管理制度和组织体系建设情况必填', trigger: 'change' },
                ],
            },


            roles: this.$store.getters.roles,

            orgBizLevel: this.$store.getters.orgBizLevel,

            state: {
                groupId: "", // 业务id
                batchId: "", // 信雅达文件批次编号
                history: false, // 是否操作历史表, true: 查询历史表或向历史表插入数据
                handUp: false, // 是否手动上传, true: 选取文件后点击上传, false: 文件一旦被选择立即上传
                autoLoad: false, // 是否立即查询数据(例如: 修改时页面一旦被打开 根据 groupId、batchId、queryArgs 立即查询数据)
                limit: 999, // 文件限制上传数量
                forceLimit: "00", // 强制限制文件数量
                accept: "", // 文件限制上传类型
                toolBar: "find,edit,upFile,download,delete,preview", // 该组件按钮选择
                procNode: "Default", // 文件上传节点或文件标识(默认 Default)
                archiveJunior: "Default", // 文件归档一级(类似菜单)(默认 Default)
                archiveMiddle: "Default", // 文件归档二级(类似菜单)(默认 Default)
                archiveSenior: "Default", // 文件归档三级(类似菜单)(默认 Default)
                queryArgs: {}, // 查询参数对象 若使用该对象 请了解使用规则; 禁止空value
                cells: {
                    type: {
                        vis: true, // 是否显示
                        dValue: "", // 字典默认值
                        mValue: ["15"], // 字典必输值 必须至少一条数据含有该数据
                        dic: "ECM_FILE_TYPE", // 字典名称
                        inc: ["15", "20"], // 该字典只显示的配置项
                        exc: [], // 该字典除配置项其余都显示
                    },
                    version: {
                        vis: false,
                        dValue: "",
                        mValue: "",
                        dic: "ECM_FILE_VERSION",
                        inc: [],
                        exc: [],
                    },
                    unit: {
                        vis: false,
                    },
                },
                forceAssert: true,
                mFile: true,
            },

        }
    },
    computed: {

        processDefinitionKey() {
            let maps = {
                '3': 'Process_CustAdmit_SB',     //支行
                '2': 'Process_CustAdmit_B',      //分行
                '1': 'Process_CustAdmit'         //总行
            }
            return maps[this.orgBizLevel]
        },
    },

    mounted() {
        this.getDetails()
    },
    methods: {
        /**
        * 获取详情
        */
        getDetails() {

            this.$api.GFPartnerManagerApi.getAdmitView({ recordId: this.recordId }).then(res => {

                this.form = res.data && { ...this.form, ...res.data }
                this.form = {
                    ...this.form,
                    stakeholderList: this.form.stakeholderList ? this.form.stakeholderList.map(m => { return { ...m, vid: this.randomNum() } }) : [],
                    seniorManagerList: this.form.seniorManagerList ? this.form.seniorManagerList.map(m => { return { ...m, vid: this.randomNum() } }) : [],
                    assetMgqualList: this.form.assetMgqualList ? this.form.assetMgqualList.map(m => { return { ...m, vid: this.randomNum() } }) : [],
                    financeReportList: this.form.financeReportList ? this.form.financeReportList.map(m => { return { ...m, vid: this.randomNum() } }) : []
                }
                this.proposer = {
                    reqUser: this.form.reqUser || this.$app.session.data.user.reqUser,
                    reqUserName: this.form.reqUserName || this.$app.session.data.user.reqUserName,
                    reqOrg: this.form.reqOrg || this.$app.session.data.user.reqOrg,
                    reqOrgName: this.form.reqOrgName || this.$app.session.data.user.reqOrgName,
                    reqTime: this.form.reqTime || this.$app.session.data.user.reqTime,
                }

                this.$refs['gfPartExternal'].getlist(this.form.custName)

                this.state.groupId = this.form.recordDocId || ''
                this.state.groupId && this.$refs.upFile.loadEcmFiles(true)


                this.$nextTick(() => {
                    this.$refs['formData'].clearValidate()
                })

                this.getBizLevelByOrgId(this.form.reqOrg)

            })
        },

        getBizLevelByOrgId(orgId) {
            this.$api.GFAgrementApi.getBizLevelByOrgId({
                orgId
            }).then(res => {
                let maps = {
                    '1': 'Process_CustAdmit',
                    '2': 'Process_CustAdmit_B',
                    '3': 'Process_CustAdmit_SB',
                }
                let aory = res.data || '1'
                this.processKey = maps[aory]
            })
        },

        /**
         * 暂存
         */
        handleStorage() {

            let rows = this.$refs.upFile.getAllRows(true);
            if (rows.length > 0) {
                let ok = this.$refs.upFile.assertInputFile();
                if (!ok) {
                    return;
                }
            }

            let parms = {
                ...this.form,
                ecmFiles: rows
            }
            this.$api.GFPartnerManagerApi.saveAdmitReq(parms).then(res => {
                if (res) {
                    this.$message.success('操作成功')
                    this.$nav.closeCurrentTab({closeCmd: 'gf.partnerManager.custodian'})
                }
            })
        },

        /**
        * 提交
        */
        async handleSave() {
            if (!this.form.isBadFame) {
                this.$message.error('该机构存在不良声誉或负面舆情，不能提交')
                return false
            }
            if (!this.form.isIllegal) {
                this.$message.error('该机构近三年是否存在重大违规行为或被采取重大行政监管措施等，不能提交')
                return false
            }
            let rows = this.$refs.upFile.getAllRows(true);
            if (!rows.find(m => m.fileType == '15')) {
                this.$message.error('请上传尽调报告')
                return false;
            }

            let ok = this.$refs.upFile.assertInputFile();
            if (!ok) {
                return;
            }


            this.$refs["formData"].validate((valid) => {
                if (!this.alertError()) {
                    return false;
                }
                if (!valid) {
                    this.$message.error('表单页数据有误，请检查！！')
                    return false;
                }

                let data = {
                    ...this.form,
                    ecmFiles: rows
                }
                let { userId, userName} = this.$app.session.data.user

                let params = {
                    processDefinitionKey: this.processDefinitionKey,
                    businessTitle: '管理人准入申请_' + userName,
                    callBack: {
                        callService: 'custApplyProcessor',
                        dataStr: JSON.stringify(data),
                        dataMap: {}
                    },
                    variables: {
                        approveStatus: 1
                    },
                    businessKey: this.bizKey ? this.bizKey : '',
                    opinion: '发起管理人准入申请',
                    completeFirstTask: 1,
                    userId: userId
                }
                this.$api.ExecutionApi.processStart(params).then(res => {
                    if (res) {
                        this.$message.success('操作成功')
                        this.close()
                    }
                })
            })
        },


        alertError() {
            if (!this.processDefinitionKey) {
                this.$message.error('权限不足,请先配置权限')
                return false
            }
            if (this.form.stakeholderList && this.form.stakeholderList.length == 0) {
                this.$message.error('请填写股东信息')
                return false
            }
            if (this.form.seniorManagerList && this.form.seniorManagerList.length == 0) {
                this.$message.error('请填写高管信息')
                return false
            }
            if (this.form.assetMgqualList && this.form.assetMgqualList.length == 0) {
                this.$message.error('请填写资质')
                return false
            }
            if (this.form.financeReportList && this.form.financeReportList.length == 0) {
                this.$message.error('请填写财务信息')
                return false
            }
            return true
        },

        showNews() {
            this.$api.GFPartnerManagerApi.getCompIdByCreditCode({ creditCode: this.form.creditCode }).then(res => {
                if (res.data) {
                    let compId = res.data
                    let viewId = 'gf.partnerManager.external.details';
                    let pageView = this.$app.views.getView(viewId);
                    pageView.title = "外部机构详情"
                    let tabView = Object.assign({ args: { compId, anchor: '公司舆情信息' }, id: viewId }, pageView);
                    this.$nav.showView(tabView);
                } else {
                    this.$message.warning('统一社会信用代码查不出数据')
                }
            })
        },

        /**
         * 返回
         */
        close() {
            this.$nav.closeCurrentTab({ closeCmd: 'gf.partnerManager.custodian' })
        },


        handleOpen(comName, slow) {
            let that = this;
            let comMaps = {
                'stakeholderList': {
                    title: '新增股东',
                    coms: CustSkholder
                },
                'seniorManagerList': {
                    title: '新增高管',
                    coms: CustManager
                },
                'assetMgqualList': {
                    title: '新增资质',
                    coms: Amqual
                },
                'financeReportList': {
                    title: '新增财务',
                    coms: Finreport
                }

            }
            this.$dialog.create({
                title: comMaps[comName].title,
                component: comMaps[comName].coms,
                closeOnClickModal: false,
                width: '500px',
                args: {
                    row: slow ? slow.row : {},
                    index: slow ? slow.$index : -1,
                    list: that.form[comName]
                },
                beforeClose: function (ss, action) {
                    if (!action) {
                        return
                    }
                    let cbdata = JSON.parse(action)
                    let { btnType, form } = cbdata
                    if (btnType == 'ok') {
                        if (slow) {
                            that.form[comName][slow.$index] = form
                            that.form[comName] = [...that.form[comName]]
                            that.resetListId(that.form[comName])
                        } else {
                            that.form[comName].push(form)
                            that.resetListId(that.form[comName])
                        }
                    }
                }
            })
        },
        resetListId(arr) {
            arr.forEach(m => {
                m.vid = this.randomNum()
            });
        },
        randomNum() {
            const array = new Uint32Array(1);
            window.crypto.getRandomValues(array);
            return Date.now() + array[0].toString().slice(-6)
        },







        changeCustName(v) {
            this.getBase(v.compId)
        },

        /**
         *获取管理人 基本信息
         */
        getBase(v) {
            this.$api.GFPartnerManagerApi.compGetView({ compId: v }).then(res => {
                this.queryFinReportList(v)
                this.form = {
                    ...this.form,
                    ...res.data.compInfo,
                    custName: res.data.compInfo.compName,
                    custType: res.data.compInfo.compType,
                }
                this.form.seniorManagerList = res.data.managerList || []
                this.form.assetMgqualList = res.data.assetqualList || []
                this.form.assetMgqualList.map(m => {
                    m['approvalOrg'] = m.approveOrg
                    m['beginDate'] = m.beiginDate
                })

                this.form.stakeholderList = res.data.skholderList || []


            })
        },
        /**
         * 年报
         */
        queryFinReportList(v) {
            this.$api.GFPartnerManagerApi.queryFinReportList({
                "compId": v,
                "dateQryType": ""
            }).then(res => {
                this.form.financeReportList = res.data
            })
        },


    },

}
</script>

<style lang="less" scoped>
.custodianNotes-edit {
  padding: 10px 8px 40px;
  ::v-deep {
    .v-cust {
      .el-select__caret::before {
        content: "\e6e1";
      }
    }
    .el-form-item {
      //   width: 95%;
      //   display: flex;
    }
    // .el-form-item__content {
    //   flex: 1;
    // }
  }
}
</style>
