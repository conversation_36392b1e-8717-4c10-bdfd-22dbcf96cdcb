<template>
    <!-- 管理人详情 -->
    <div class="page custodian-details" ref="custodian-details">
        <div class="page-title">管理人详情</div>
        <el-tabs v-model="tabIndex" style="height: 100%;">
            <el-tab-pane label="基本信息" name="1">
                <el-form label-width="140px">
                    <div class="v-section">
                        <h3 class="sub-title">申请信息</h3>
                        <div>
                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="申请人">
                                        <gf-input disabled v-model="form.reqUserName" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="申请机构">
                                        <gf-input disabled v-model="form.reqOrgName" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="申请日期">
                                        <gf-input disabled v-model="form.reqTime" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="v-section">
                        <h3 class="sub-title">基本信息</h3>
                        <div>
                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="管理人名称">
                                        <gf-input disabled v-model="form.custName" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="管理人类型">
                                        <gf-dict disabled v-model="form.custType" dictType="CUST_TYPE" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="成立日期">
                                        <el-date-picker disabled type="date" v-model="form.foundDate" style="width: 100%;" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="注册资本(万元)">
                                        <gf-input-money disabled v-model="form.regCaptial" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="实收资本(万元)">
                                        <gf-input-money disabled v-model="form.paidinCaptial" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="法定代表人">
                                        <gf-input disabled v-model="form.legrePerson" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="实际控制人">
                                        <gf-input disabled v-model="form.actualPerson" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="统一社会信用代码">
                                        <gf-input disabled v-model="form.creditCode" clear-regex="[^\dA-Za-z]" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="组织机构代码证">
                                        <gf-input disabled v-model="form.organizationCode" clear-regex="[^\dA-Za-z]" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="人员数量">
                                        <gf-input disabled v-model="form.workforce" inputLang="num" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="管理规模(亿元)">
                                        <gf-input-money disabled v-model="form.manageScale" :max-byte-len="19" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="监管评级">
                                        <gf-input disabled v-model="form.superviseGrade" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="规模是否排名同行业前50%">
                                        <gf-dict v-model="form.tradeRank" dictType="GF_BOOL_TYPE" disabled />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="企业性质">
                                        <gf-dict disabled v-model="form.entNature" dictType="ENT_NATURE" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="关联方类型">
                                        <gf-dict disabled v-model="form.isSelfRelate" dictType="CUST_RELATE_TYPE" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="是否为国有控股">
                                        <gf-dict disabled v-model="form.isStateOwn" dictType="GF_BOOL_TYPE" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="注册地址">
                                        <gf-input disabled type="textarea" resize="none" :autosize="{ minRows: 2, maxRows: 6 }" v-model="form.regAddr" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="经营地址">
                                        <gf-input disabled type="textarea" resize="none" :autosize="{ minRows: 2, maxRows: 6 }" v-model="form.bizAddr" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="经营范围">
                                        <gf-input disabled type="textarea" resize="none" :autosize="{ minRows: 2, maxRows: 6 }" v-model="form.bizScope" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="经营状况">
                                        <gf-input disabled type="textarea" resize="none" :autosize="{ minRows: 2, maxRows: 6 }" v-model="form.bizSituation" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="公司治理及内控情况">
                                        <gf-input disabled type="textarea" resize="none" :autosize="{ minRows: 2, maxRows: 6 }" v-model="form.compGvnCtrl" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="v-section">
                        <h3 class="sub-title">股东信息</h3>
                        <div>
                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="股东列表">
                                        <el-table border :data="form.stakeholderList">
                                            <el-table-column label="股东名称" prop="holdName" />
                                            <el-table-column label="出资金额(万元)" prop="actualAmount" />
                                            <el-table-column label="持股比例(%)" prop="holdPct" />
                                        </el-table>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="v-section">
                        <h3 class="sub-title">高管信息</h3>
                        <div>
                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="高管信息列表">
                                        <el-table border :data="form.seniorManagerList">
                                            <el-table-column label="姓名" prop="psname" />
                                            <el-table-column label="职务" prop="duty" />
                                            <el-table-column label="履历" prop="resume" />
                                        </el-table>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="v-section">
                        <h3 class="sub-title">资质情况</h3>
                        <div>
                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="资质列表">
                                        <el-table border :data="form.assetMgqualList">
                                            <el-table-column label="资质名称" prop="qualName" />
                                            <el-table-column label="批复机构" prop="approvalOrg" />
                                            <el-table-column label="获批日期" prop="beginDate" />
                                        </el-table>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="v-section">
                        <h3 class="sub-title">财务状况</h3>
                        <div>
                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="财务信息列表">
                                        <el-table border :data="form.financeReportList">
                                            <el-table-column label="会计期间" prop="reportYear">
                                                <template slot-scope="scope">
                                                    <div>{{ scope.row.reportYear }}-{{ scope.row.reportDateType }}</div>
                                                </template>
                                            </el-table-column>
                                            <el-table-column label="总资产(万元)" prop="totAsset" />
                                            <el-table-column label="总负债(万元)" prop="totDebts" />
                                            <el-table-column label="净资产(万元)" prop="netAsset" />
                                            <el-table-column label="营业收入(万元)" prop="bizIncome" />
                                            <el-table-column label="净利润(万元)" prop="netProfit" />
                                        </el-table>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="v-section">
                        <h3 class="sub-title">合法合规情况</h3>
                        <div>
                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="洗钱评估结果">
                                        <gf-dict disabled v-model="form.amlAssess" dictType="CUST_RISK_LEVEL" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="反洗钱风险管理制度和组织体系建设情况">
                                        <gf-input disabled type="textarea" resize="none" :autosize="{ minRows: 2, maxRows: 6 }" v-model="form.amlDevelop" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="管理人有无不良声誉或负面舆情" label-width="420px">
                                        <gf-dict-radio-group class="mr10" disabled v-model="form.isBadFame" dictType="GF_BOOL_TYPE"></gf-dict-radio-group>
                                        <el-link type="primary" :underline="false" v-if="form.creditCode" @click="showNews">查看舆情</el-link>
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="24">
                                    <el-form-item label="近三年是否存在重大违规行为或被采取重大行政监管措施等" label-width="420px">
                                        <gf-dict-radio-group class="mr10" disabled v-model="form.isIllegal" dictType="GF_BOOL_TYPE"></gf-dict-radio-group>
                                        <el-link type="primary" :underline="false" v-if="form.creditCode" @click="showNews">查看舆情</el-link>
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="v-section">
                        <h3 class="sub-title">调查人信息</h3>
                        <div>
                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="调查日期">
                                        <el-date-picker disabled type="date" v-model="form.investDate" style="width: 100%;" :picker-options="pickerOptions" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="调查机构">
                                        <gf-market-orgs disabled v-model="form.investOrg" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="调查人1">
                                        <gf-input disabled v-model="form.investigator1" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="联系方式">
                                        <gf-input disabled v-model="form.contact1" oninput="value = value.replace(/[^0-9\-\+]/g, '')" :max-byte-len="17" />
                                    </el-form-item>
                                </el-col>
                            </el-row>

                            <el-row :gutter="12">
                                <el-col :span="12">
                                    <el-form-item label="调查人2">
                                        <gf-input disabled v-model="form.investigator2" />
                                    </el-form-item>
                                </el-col>
                                <el-col :span="12">
                                    <el-form-item label="联系方式">
                                        <gf-input disabled v-model="form.contact2" oninput="value = value.replace(/[^0-9\-\+]/g, '')" :max-byte-len="17" />
                                    </el-form-item>
                                </el-col>
                            </el-row>
                        </div>
                    </div>

                    <div class="v-section" v-if="isShowUpdate">
                        <h3 class="sub-title">附件信息</h3>
                        <div>
                            <gf-upload-ecm-file ref="upFile" :state="state"></gf-upload-ecm-file>
                        </div>
                    </div>
                </el-form>
            </el-tab-pane>
            <el-tab-pane label="产品信息" name="2">
                <gf-grid class="grid" ref="grid" grid-no="ares-partnerManager-custodianDetails" :query-args="queryArgs" toolbar=""></gf-grid>
            </el-tab-pane>
        </el-tabs>

        <div class="form-btn-group center mt20">
            <gf-button @click="close">关闭</gf-button>
        </div>
    </div>
</template>

<script>
import utils from "../../../../../utils/common"
export default {
    name: 'custodian-details',
    props: {
        custId: {
            type: String
        }
    },
    computed: {
        isShowUpdate() {
            return this.$store.getters.isShowUpdate;
        }
    },
    data() {
        return {
            tabIndex: '1',
            form: {
                stakeholderList: [], //股东列表
                seniorManagerList: [], //高管列表
                assetMgqualList: [], //资质列表
                financeReportList: [] //财务列表
            },

            queryArgs: {
                creditCode: ''
            },

            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },

            state: {
                groupId: '', // 业务id
                batchId: '', // 信雅达文件批次编号
                history: false, // 是否操作历史表, true: 查询历史表或向历史表插入数据
                handUp: false, // 是否手动上传, true: 选取文件后点击上传, false: 文件一旦被选择立即上传
                autoLoad: false, // 是否立即查询数据(例如: 修改时页面一旦被打开 根据 groupId、batchId、queryArgs 立即查询数据)
                limit: 999, // 文件限制上传数量
                forceLimit: '00', // 强制限制文件数量
                accept: '', // 文件限制上传类型
                toolBar: 'download,preview', // 该组件按钮选择
                procNode: 'Default', // 文件上传节点或文件标识(默认 Default)
                archiveJunior: 'Default', // 文件归档一级(类似菜单)(默认 Default)
                archiveMiddle: 'Default', // 文件归档二级(类似菜单)(默认 Default)
                archiveSenior: 'Default', // 文件归档三级(类似菜单)(默认 Default)
                queryArgs: {}, // 查询参数对象 若使用该对象 请了解使用规则; 禁止空value
                cells: {
                    type: {
                        vis: true, // 是否显示
                        dValue: '', // 字典默认值
                        mValue: '', // 字典必输值 必须至少一条数据含有该数据
                        dic: 'ECM_FILE_TYPE', // 字典名称
                        inc: ['15', '20'], // 该字典只显示的配置项
                        exc: [] // 该字典除配置项其余都显示
                    },
                    version: {
                        vis: false,
                        dValue: '',
                        mValue: '',
                        dic: 'ECM_FILE_VERSION',
                        inc: [],
                        exc: []
                    },
                    unit: {
                        vis: false
                    }
                },
                forceAssert: false,
                mFile: false
            }
        };
    },
    methods: {
        onTabActived() {
            this.getDetails();
        },
        /**
         * 获取详情
         */
        getDetails() {
            this.$api.GFPartnerManagerApi.getDetailView({ custId: this.custId }).then(res => {
                this.form = res.data && { ...this.form, ...res.data };
                this.form = {
                    ...this.form,
                    stakeholderList: this.form.stakeholderList
                        ? this.form.stakeholderList.map(m => {
                              return { ...m, vid: this.randomNum() };
                          })
                        : [],
                    seniorManagerList: this.form.seniorManagerList
                        ? this.form.seniorManagerList.map(m => {
                              return { ...m, vid: this.randomNum() };
                          })
                        : [],
                    assetMgqualList: this.form.assetMgqualList
                        ? this.form.assetMgqualList.map(m => {
                              return { ...m, vid: this.randomNum() };
                          })
                        : [],
                    financeReportList: this.form.financeReportList
                        ? this.form.financeReportList.map(m => {
                              return { ...m, vid: this.randomNum() };
                          })
                        : []
                };

                this.queryArgs.creditCode = this.form.creditCode;
                this.$refs.grid.reloadData(true);

                if (this.isShowUpdate) {
                    this.state.groupId = this.form.docId || '';
                    this.state.groupId && this.$refs.upFile.loadEcmFiles(true);
                }
            });
        },

        showNews() {
            this.$api.GFPartnerManagerApi.getCompIdByCreditCode({ creditCode: this.form.creditCode }).then(res => {
                if (res.data) {
                    let compId = res.data;
                    let viewId = 'gf.partnerManager.external.details';
                    let pageView = this.$app.views.getView(viewId);
                    pageView.title = '外部机构详情';
                    let tabView = Object.assign({ args: { compId, anchor: '公司舆情信息' }, id: viewId }, pageView);
                    this.$nav.showView(tabView);
                } else {
                    this.$message.warning('统一社会信用代码查不出数据');
                }
            });
        },

        /**
         * 返回
         */
        close() {
            this.$nav.closeCurrentTab();
        },

        randomNum() {
            return Date.now() + utils.randomNum(6);
        }




    },

}
</script>

<style lang="less" scoped>
.custodian-details {
    padding: 10px 8px 110px;
}
</style>
