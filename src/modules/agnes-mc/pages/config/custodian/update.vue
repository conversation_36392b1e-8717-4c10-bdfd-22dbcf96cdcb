<template>
    <!-- 管理人信息修改   不走工作流-->
    <div class="page custodian-update" ref="custodian-update">
        <div class="page-title">管理人修改</div>
        <gf-right-nav pageName='custodian-update' />
        <el-form ref="formData" class="page-form" label-width="140px" :model="form" style="padding-right:170px" :rules="rules">
            <div class="v-section">
                <h3 class="sub-title">申请信息</h3>
                <div>
                    <el-row :gutter="12">
                        <el-col :span="12">
                            <el-form-item label="申请人">
                                <gf-input disabled v-model="proposer.reqUserName" />
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="申请机构">
                                <gf-input disabled v-model="proposer.reqOrgName" />
                            </el-form-item>
                        </el-col>
                    </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="申请日期">
                <gf-input disabled v-model="proposer.reqTime" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="v-section">
        <h3 class="sub-title">基本信息</h3>
        <div>
          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="管理人名称" prop="custName">
                <gf-input disabled v-model="form.custName" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管理人类型" prop="custType">
                <gf-dict
                  disabled
                  v-model="form.custType"
                  dictType="CUST_TYPE"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="成立日期" prop="foundDate">
                <el-date-picker
                  disabled
                  type="date"
                  v-model="form.foundDate"
                  style="width: 100%"
                  value-format="yyyy-MM-dd"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注册资本(万元)" prop="regCaptial">
                <gf-input-money
                  disabled
                  v-model="form.regCaptial"
                  :max-byte-len="19"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="实收资本(万元)" prop="paidinCaptial">
                <gf-input-money
                  disabled
                  v-model="form.paidinCaptial"
                  :max-byte-len="19"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="法定代表人" prop="legrePerson">
                <gf-input
                  disabled
                  v-model="form.legrePerson"
                  :max-byte-len="300"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="实际控制人" prop="actualPerson">
                <gf-input
                  disabled
                  v-model="form.actualPerson"
                  :max-byte-len="300"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="统一社会信用代码" prop="creditCode">
                <gf-input
                  disabled
                  v-model="form.creditCode"
                  clear-regex="[^\dA-Za-z]"
                  :max-byte-len="18"
                  @input="(val) => (form.creditCode = val.toLocaleUpperCase())"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="组织机构代码证" prop="organizationCode">
                <gf-input
                  disabled
                  v-model="form.organizationCode"
                  :max-byte-len="9"
                  clear-regex="[^\dA-Za-z]"
                  @input="
                    (val) => (form.organizationCode = val.toLocaleUpperCase())
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="人员数量" prop="workforce">
                <gf-input
                  disabled
                  v-model="form.workforce"
                  inputLang="num"
                  :max-byte-len="10"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="管理规模(亿元)" prop="manageScale">
                <gf-input-money v-model="form.manageScale" :max-byte-len="19" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="监管评级">
                <gf-input
                  v-model="form.superviseGrade"
                  inputLang="code"
                  :max-byte-len="10"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="规模是否排名同行业前50%" prop="tradeRank">
                <gf-dict v-model="form.tradeRank" dictType="GF_BOOL_TYPE" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="企业性质" prop="entNature">
                <gf-dict
                  disabled
                  v-model="form.entNature"
                  dictType="ENT_NATURE"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="12">
              <el-form-item label="关联方类型" prop="isSelfRelate">
                <gf-dict
                  v-model="form.isSelfRelate"
                  dictType="CUST_RELATE_TYPE"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="是否为国有控股" prop="isStateOwn">
                <gf-dict v-model="form.isStateOwn" dictType="GF_BOOL_TYPE" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="注册地址" prop="regAddr">
                <gf-input
                  disabled
                  type="textarea"
                  resize="none"
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  v-model="form.regAddr"
                  :max-byte-len="600"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="经营地址" prop="bizAddr">
                <gf-input
                  disabled
                  type="textarea"
                  resize="none"
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  v-model="form.bizAddr"
                  :max-byte-len="600"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="经营范围" prop="bizScope">
                <gf-input
                  disabled
                  type="textarea"
                  resize="none"
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  v-model="form.bizScope"
                  :max-byte-len="3000"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="经营状况" prop="bizSituation">
                <gf-input
                  type="textarea"
                  resize="none"
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  v-model="form.bizSituation"
                  :max-byte-len="3000"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="公司治理及内控情况" prop="compGvnCtrl">
                <gf-input
                  type="textarea"
                  resize="none"
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  v-model="form.compGvnCtrl"
                  :max-byte-len="3000"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="v-section">
        <h3 class="sub-title">股东信息</h3>
        <div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="股东列表" required>
                <el-table border :data="form.stakeholderList">
                  <el-table-column label="股东名称" prop="holdName" />
                  <el-table-column label="出资金额(万元)" prop="actualAmount">
                    <div slot-scope="slot">
                      {{ formatMoney(slot.row.actualAmount) }}
                    </div>
                  </el-table-column>
                  <el-table-column label="持股比例(%)" prop="holdPct" />
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="v-section">
        <h3 class="sub-title">高管信息</h3>
        <div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="高管信息列表" required>
                <el-table border :data="form.seniorManagerList">
                  <el-table-column label="姓名" prop="psname" />
                  <el-table-column label="职务" prop="duty" />
                  <el-table-column label="履历" prop="resume" />
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="v-section">
        <h3 class="sub-title">资质情况</h3>
        <div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="资质列表" required>
                <el-table border :data="form.assetMgqualList">
                  <el-table-column label="资质名称" prop="qualName" />
                  <el-table-column label="批复机构" prop="approvalOrg" />
                  <el-table-column label="获批日期" prop="beginDate" />
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="v-section">
        <h3 class="sub-title">财务状况</h3>
        <div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="财务信息列表" required>
                <el-table border :data="form.financeReportList">
                  <el-table-column label="会计期间" prop="reportYear">
                    <div slot-scope="slot">
                      {{ slot.row.reportYear
                      }}{{
                        {
                          "1": "一季报",
                          "2": "半年报",
                          "3": "三季报",
                          "4": "年报",
                        }[slot.row.reportDateType]
                      }}
                    </div>
                  </el-table-column>
                  <el-table-column label="总资产(万元)" prop="totAsset">
                    <div slot-scope="slot">
                      {{ formatMoney(slot.row.totAsset) }}
                    </div>
                  </el-table-column>
                  <el-table-column label="总负债(万元)" prop="totDebts">
                    <div slot-scope="slot">
                      {{ formatMoney(slot.row.totDebts) }}
                    </div>
                  </el-table-column>
                  <el-table-column label="净资产(万元)" prop="netAsset">
                    <div slot-scope="slot">
                      {{ formatMoney(slot.row.netAsset) }}
                    </div>
                  </el-table-column>
                  <el-table-column label="营业收入(万元)" prop="bizIncome">
                    <div slot-scope="slot">
                      {{ formatMoney(slot.row.bizIncome) }}
                    </div>
                  </el-table-column>
                  <el-table-column label="净利润(万元)" prop="netProfit">
                    <div slot-scope="slot">
                      {{ formatMoney(slot.row.netProfit) }}
                    </div>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="v-section">
        <h3 class="sub-title">合法合规情况</h3>
        <div>
          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item label="洗钱评估结果" prop="amlAssess">
                <gf-dict
                  disabled
                  v-model="form.amlAssess"
                  dictType="CUST_RISK_LEVEL"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item
                label="反洗钱风险管理制度和组织体系建设情况"
                prop="amlDevelop"
              >
                <gf-input
                  type="textarea"
                  resize="none"
                  :autosize="{ minRows: 2, maxRows: 6 }"
                  v-model="form.amlDevelop"
                  :max-byte-len="3000"
                />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item
                label="管理人有无不良声誉或负面舆情"
                label-width="420px"
              >
                

                <gf-dict-radio-group
                  class="mr10"
                  v-model="form.isBadFame"
                  dictType="GF_BOOL_TYPE"
                ></gf-dict-radio-group>
                <el-link
                  type="primary"
                  :underline="false"
                  v-if="form.creditCode"
                  @click="showNews"
                  >查看舆情</el-link
                >
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="12">
            <el-col :span="24">
              <el-form-item
                label="近三年是否存在重大违规行为或被采取重大行政监管措施等"
                label-width="420px"
              >
                
                <gf-dict-radio-group
                  class="mr10"
                  v-model="form.isIllegal"
                  dictType="GF_BOOL_TYPE"
                ></gf-dict-radio-group>
                <el-link
                  type="primary"
                  :underline="false"
                  v-if="form.creditCode"
                  @click="showNews"
                  >查看舆情</el-link
                >
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </div>

      <div class="v-section">
        <h3 class="sub-title">附件信息</h3>
        <div>
          <gf-upload-ecm-file ref="upFile" :state="state"></gf-upload-ecm-file>
        </div>
      </div>
    </el-form>

    <div class="form-btn-group center mt20">
      <gf-button class="action-btn" @click="handleSave">保存</gf-button>
      <gf-button @click="close">返回</gf-button>
    </div>
  </div>
</template>

<script>
export default {
  name: "custodian-update",
  props: {
    custId: { type: String },
  },
  data() {
    return {
      formatMoney: this.$fmt.formatMoney,
      form: {
        reqUser: "",
        reqTime: "",
        custName: "",
        custType: "",
        compRegno: "",
        foundDate: "",
        regCaptial: "",
        paidinCaptial: "",
        legrePerson: "",
        actualPerson: "",
        creditCode: "",
        organizationCode: "",
        workforce: "",
        manageScale: "",
        superviseGrade: "",
        tradeRank: "",
        entNature: "",
        isSelfRelate: "",
        isStateOwn: "",
        bizScope: "",
        regAddr: "",
        bizAddr: "",
        bizSituation: "",
        compGvnCtrl: "",
        amlAssess: "",
        amlDevelop: "",
        isBadFame: "",
        isIllegal: "",
        investDate: "",
        investOrg: null,
        investigator1: "",
        contact1: "",
        investigator2: "",
        contact2: "",
        stakeholderList: [], //股东列表
        seniorManagerList: [], //高管列表
        assetMgqualList: [], //资质列表
        financeReportList: [], //财务列表
      },

      proposer: {
        reqUser: "",
        reqUserName: "",
        reqOrg: "",
        reqOrgName: "",
        reqTime: "",
      },

      state: {
        groupId: "", // 业务id
        batchId: "", // 信雅达文件批次编号
        history: false, // 是否操作历史表, true: 查询历史表或向历史表插入数据
        handUp: false, // 是否手动上传, true: 选取文件后点击上传, false: 文件一旦被选择立即上传
        autoLoad: false, // 是否立即查询数据(例如: 修改时页面一旦被打开 根据 groupId、batchId、queryArgs 立即查询数据)
        limit: 999, // 文件限制上传数量
        forceLimit: "00", // 强制限制文件数量
        accept: "", // 文件限制上传类型
        toolBar: "find,edit,upFile,download,delete,preview", // 该组件按钮选择
        procNode: "Default", // 文件上传节点或文件标识(默认 Default)
        archiveJunior: "Default", // 文件归档一级(类似菜单)(默认 Default)
        archiveMiddle: "Default", // 文件归档二级(类似菜单)(默认 Default)
        archiveSenior: "Default", // 文件归档三级(类似菜单)(默认 Default)
        queryArgs: {}, // 查询参数对象 若使用该对象 请了解使用规则; 禁止空value
        cells: {
          type: {
            vis: true, // 是否显示
            dValue: "", // 字典默认值
            mValue: ["15"], // 字典必输值 必须至少一条数据含有该数据
            dic: "ECM_FILE_TYPE", // 字典名称
            inc: ["15", "20"], // 该字典只显示的配置项
            exc: [], // 该字典除配置项其余都显示
          },
          version: {
            vis: false,
            dValue: "",
            mValue: "",
            dic: "ECM_FILE_VERSION",
            inc: [],
            exc: [],
          },
          unit: {
            vis: false,
          },
        },
        forceAssert: true,
        mFile: true,
      },

      compId: "",

      rules: {
        custName: [
          { required: true, message: "管理人名称必填", trigger: "change" },
        ],
        custType: [
          { required: true, message: "管理人类型必填", trigger: "change" },
        ],
        foundDate: [
          { required: true, message: "成立日期必填", trigger: "change" },
        ],
        regCaptial: [
          { required: true, message: "注册资本必填", trigger: "change" },
        ],
        paidinCaptial: [
          { required: true, message: "实收资本必填", trigger: "change" },
        ],
        legrePerson: [
          { required: true, message: "法定代表人必填", trigger: "change" },
        ],
        actualPerson: [
          { required: true, message: "实际控制人必填", trigger: "change" },
        ],
        creditCode: [
          {
            required: true,
            message: "统一社会信用代码必填",
            trigger: "change",
          },
          {
            pattern: /[\dA-Z]{18}/,
            message: "18位数字或大写字母",
            trigger: "change",
          },
        ],
        organizationCode: [
          { required: true, message: "组织机构代码证必填", trigger: "change" },
          {
            pattern: /[\dA-Z]{9}/,
            message: "9位数字或大写字母",
            trigger: "change",
          },
        ],
        workforce: [
          { required: true, message: "人员数量必填", trigger: "change" },
        ],
        manageScale: [
          { required: true, message: "管理规模必填", trigger: "change" },
        ],
        tradeRank: [
          {
            required: true,
            message: "规模是否排名同行业前50%必填",
            trigger: "change",
          },
        ],
        entNature: [
          { required: true, message: "企业性质必填", trigger: "change" },
        ],
        isSelfRelate: [
          { required: true, message: "关联方类型必填", trigger: "change" },
        ],
        isStateOwn: [
          { required: true, message: "是否为国有控股必填", trigger: "change" },
        ],
        regAddr: [
          { required: true, message: "注册地址必填", trigger: "change" },
        ],
        bizAddr: [
          { required: true, message: "经营地址必填", trigger: "change" },
        ],
        bizScope: [
          { required: true, message: "经营范围必填", trigger: "change" },
        ],
        bizSituation: [
          { required: true, message: "经营状况必填", trigger: "change" },
        ],
        compGvnCtrl: [
          {
            required: true,
            message: "公司治理及内控情况必填",
            trigger: "change",
          },
        ],
        investDate: [
          { required: true, message: "调查日期必填", trigger: "change" },
        ],
        investOrg: [
          { required: true, message: "调查机构必填", trigger: "change" },
        ],
        investigator1: [
          { required: true, message: "调查人必填", trigger: "change" },
        ],
        contact1: [
          { required: true, message: "联系方式必填", trigger: "change" },
        ],
        investigator2: [
          { required: true, message: "调查人必填", trigger: "change" },
        ],
        contact2: [
          { required: true, message: "联系方式必填", trigger: "change" },
        ],
        // amlAssess: [
        //     { required: true, message: '洗钱评估结果必填', trigger: 'change' },
        // ],
        amlDevelop: [
          {
            required: true,
            message: "反洗钱风险管理制度和组织体系建设情况必填",
            trigger: "change",
          },
        ],
      },
    };
  },

  mounted() {
    this.custId && this.getDetails();
  },
  methods: {
    /**
     * 获取详情
     */
    getDetails() {
      this.$api.GFPartnerManagerApi.getDetailView({ custId: this.custId }).then(
        (res) => {
          this.form = res.data && { ...this.form, ...res.data };
          this.form = {
            ...this.form,
            stakeholderList: this.form.stakeholderList
              ? this.form.stakeholderList.map((m) => {
                  return { ...m, vid: this.randomNum() };
                })
              : [],
            seniorManagerList: this.form.seniorManagerList
              ? this.form.seniorManagerList.map((m) => {
                  return { ...m, vid: this.randomNum() };
                })
              : [],
            assetMgqualList: this.form.assetMgqualList
              ? this.form.assetMgqualList.map((m) => {
                  return { ...m, vid: this.randomNum() };
                })
              : [],
            financeReportList: this.form.financeReportList
              ? this.form.financeReportList.map((m) => {
                  return { ...m, vid: this.randomNum() };
                })
              : [],
          };
          this.proposer = {
            reqUser: this.form.reqUser,
            reqUserName: this.form.reqUserName,
            reqOrg: this.form.reqOrg,
            reqOrgName: this.form.reqOrgName,
            reqTime: this.form.reqTime,
          };

          this.state.groupId = this.form.docId || "";
          this.state.groupId && this.$refs.upFile.loadEcmFiles(true);
        }
      );
    },

    /**
     * 提交
     */
    handleSave() {
      let rows = this.$refs.upFile.getAllRows(true);
      if (!rows.find((m) => m.fileType == "15")) {
        this.$message.error("请上传尽调报告");
        return false;
      }

      let ok = this.$refs.upFile.assertInputFile();
      if (!ok) {
        return;
      }

      this.$refs["formData"].validate((valid) => {
        if (!valid) {
          return false;
        }

        let parms = {
          ...this.form,
          ecmFiles: rows,
        };
        this.$api.GFPartnerManagerApi.updateInfo(parms).then((res) => {
          if (res) {
            this.$message.success("操作成功");
            this.$nav.closeCurrentTab({
              closeCmd: "gf.partnerManager.custodian",
            });
          }
        });
      });
    },

    showNews() {
      this.$api.GFPartnerManagerApi.getCompIdByCreditCode({
        creditCode: this.form.creditCode,
      }).then((res) => {
        if (res.data) {
          let compId = res.data;
          let viewId = "gf.partnerManager.external.details";
          let pageView = this.$app.views.getView(viewId);
          pageView.title = "外部机构详情";
          let tabView = Object.assign(
            { args: { compId, anchor: "公司舆情信息" }, id: viewId },
            pageView
          );
          this.$nav.showView(tabView);
        } else {
          this.$message.warning("统一社会信用代码查不出数据");
        }
      });
    },

    /**
     * 取消
     */
    close() {
      this.$nav.closeCurrentTab();
    },

        randomNum() {
            const array = new Uint32Array(1);
            window.crypto.getRandomValues(array);
            return Date.now() + array[0].toString().slice(-6)
        }


    },
  }
</script>

<style lang="less" scoped>
.custodian-update {
  padding: 10px 8px 40px;
  ::v-deep {
    .el-form-item {
      //   width: 95%;
      //   display: flex;
    }
    // .el-form-item__content {
    //   flex: 1;
    // }
  }
}
</style>
