<template>
    <div class="bottom-container">
        <gf-search-box ref="box"
                       :open="false"
        >
            <template slot="left-content">
                <gf-form ref="gfForm"
                         label-position="right"
                         label-width="150px"
                         :form-items="formItems"
                         @getParams="getParams"
                         :mode="'search'"
                ></gf-form>
            </template>
        </gf-search-box>
        <gf-grid :filterRemote="false" grid-no="agnes-biz-dirselsub" toolbar="find" ref="grid"
                 :query-args="queryArgs"
                 :options="gridOption(this)">
            <template slot="left">
                    <el-button class="action-btn" :class="{'cj-active': queryArgs.dataType === '01'}" @click="showInfoByType('01')">
                      明细信息展示</el-button>
                    <el-button class="action-btn" :class="{'cj-active': queryArgs.dataType === '00'}" @click="showInfoByType('00')">
                    汇总信息展示</el-button>

<!--                <el-button v-show="$hasPermission('agnes.biz.sel.sub.check')" class="action-btn" @click="confirmStatus">-->
<!--                    人工确认到账</el-button>-->
                <!-- <el-button class="action-btn" @click="exportAll">
                    <i class="fa fa-cloud-download" aria-hidden="true"></i>
                    导出</el-button> -->
<!--                <el-button class="action-btn" @click="setTops">-->
<!--                    置顶</el-button>-->
<!--                <el-button class="action-btn" @click="cancleTops">-->
<!--                    取消置顶</el-button>-->
<!--                <el-button class="action-btn" @click="syncTaShowZxgt" v-if="$hasPermission('agnes.biz.sell.rcp.zjqs.sync') && isToday()">-->
                <el-button class="action-btn" @click="syncTaShowZxgt" v-if="isToday()">
                  同步数据</el-button>
            </template>
            <template slot="right-before">
                <span class="normal-font">快速检索:</span>
            </template>
            <template slot="right-after">
                <el-button
                        icon="el-icon-download"
                        class="gf-grid-status-button"
                        @click="exportAll"
                ></el-button>
            </template>
        </gf-grid>
    </div>
</template>
<script>
import dateUtil from "@hex/gf-ui/src/util/date-utils";
import exportExcel from "../../../../../components/common/exportExcel";
export default {
  props: {
    reqData:{
      type:Object,
      request:true
    }
  },
  watch: {
    reqData:{
      handler() {
        this.queryArgs.bizDate = this.reqData.bizDate;
        this.search();
      },
      deep:true
    }
  },
    data() {
        return {
            gridOption: (_this)=>{
                return {
                    onCellClicked(params) {
                        // 定义可以被点击的单元格
                        const showDetailArr = ['indexNo','subPaymentStatus','productCode','productName','tranAmount','transAcc','customerName'];
                        // （!params.data.customerName）customerName为空，则表明该数据为汇总数据
                        if(showDetailArr.includes(params.colDef.field) && !params.data.customerName){
                            _this.cellClicked(params)
                        }
                    },
                }
            },
            searchOptions: [],
            formItems: [
                {type: 'select',name: '展开方式:',value: '01',options: [{id: 'dataType',label:''}],
                    props: { filterable:true,placeholder:'全部' }},
                {type: 'select',name: '申购款到账状态:', value: '',options: [{id: 'subPaymentStatus',label:''}],
                    props: { filterable:true,placeholder:'全部',multiple: true, collapseTags: true}},
                {type: 'children',
                    childItems: [
                        {type: 'button',action: 'search',name: '查询',click:()=>{this.search()}},
                        {type: 'button',action: 'reset',name: '重置',click:()=>{this.reset()}},
                    ]},
            ],
            queryArgs:{
                subPaymentStatus: '',
                dataType: '01',
                productCode:'',
                bizDate:window.bizDate,
            },
            paymentStatusList:[],
            total: 0,
            menuConfigInfoSumQL:{},
            menuConfigInfoDetailQL:{},
        }
    },
    mounted() {
        const p = this.initDictValue();
        this.$app.blockingApp(p);
        this.setDataTotal();
        this.initData();
        //使用公共树
        // this.$agnesUtils.usePublicTree(this.$route.path)
    },
    methods:{
        isToday(){
          return new Date().format("yyyy-MM-dd") === this.reqData.bizDate;
        },
        // 初始化字典值
        async initDictValue() {
            this.formItems[0].options = this.getDictValue("AGNES_SHOUW_WAY");
            this.formItems[1].options = this.getDictValue("AGNES_RCP_STATUS");
        },
        getDictValue(dictNo){
            return this.$app.dict
                        .getDictItems(dictNo)
                        .map((dictItem) => {
                        return {
                            id: dictItem.dictId,
                            label: dictItem.dictId + "-" + dictItem.dictName,
                        };
                    });
        },
        async initData(){
            const p = this.$api.fileScan.queryRuleConfigList();
            let res =  await this.$app.blockingApp(p);
            res.data.forEach(item=>{
                //直销柜台赎回款数据导出-全量-汇总
                if(item.ifCode === 'DIR_SEL_SUB_EXPOERT_SUM_SQL'){
                    this.menuConfigInfoSumQL.outputParam = item.pkId;
                    this.menuConfigInfoSumQL.resName = item.fileName;
                }
                //直销柜台赎回款数据导出-全量-明细
                if(item.ifCode === 'DIR_SEL_SUB_EXPOERT_DETAIL_SQL'){
                    this.menuConfigInfoDetailQL.outputParam = item.pkId;
                    this.menuConfigInfoDetailQL.resName = item.fileName;
                }
            });
         },
        async setDataTotal(){
            let productCode = this.$store.state.publicData.publicCheckProductStr;
            let bizDate = this.reqData.bizDate;
            let result = await this.$api.taVisualApi.queryDirSelSubInfo('','01',productCode,bizDate);
            this.total = result.data.total;
        },
        // 表格数据塞入
        setGridData(data) {
            this.$refs.grid.setRowData(data);
            this.$nextTick(()=>{
                this.$refs.grid.state.totalRowCount = this.total;
            })
        },
        // 重置表格列宽
        fitColumnsWidth() {
            setTimeout(()=>{
                this.$refs.grid.handleMoreCommand('autoSizeFitColumns')
            },500)
        },
        // 获取表单参数
        async getParams(params) {
            // 获取参数后，重新执行接口获取表格数据
            if(params[0].value !== ''){
                this.queryArgs.dataType = params[0].value;
            }
            this.paymentStatusList = params[1].value
            this.queryArgs.subPaymentStatus = params[1].value.join(',')
            this.$refs.grid.reloadData();
        },
        //不同数据类型切换表格数据
        showInfoByType(flag){
            this.$refs.box.searchValue = '';
            this.queryArgs.subPaymentStatus = '';
            this.formItems[0].value = flag;
            this.search();
            this.fitColumnsWidth();
        },
        //同步直销柜台客户申购款数据
        async syncTaShowZxgt() {
          // try {
          const ok = await this.$msg.ask('是否同步数据?');
          if (!ok) {
            return;
          }
          // 异步调用但不等待完成
          (async () => {
            await this.$api.taVisualApi.execTaKshZxgt({});
          })();
          this.$msg.success('信息同步中请稍后查看!');
          this.$refs.grid.reloadData();
        },

        //导出
      async exportAll() {
          if (this.menuConfigInfoSumQL == undefined || this.menuConfigInfoSumQL.outputParam == null || this.menuConfigInfoSumQL.outputParam == undefined) {
              this.$msg.error('请完善导出相关配置！');
              return;
          }
          let rows = this.$refs.grid.getSelectedRows();
          if (rows.length != 0) {
              this.$msg.warning('无需选中列表数据---可直接全部导出!');
              return;
          }else{

              this.paymentStatusList = this.formItems[1].value
              let subPaymentStatus = this.toPackageExportData(this.paymentStatusList)

              let products = this.$store.state.publicData.publicTreeCheckedKeys.data
              let productCodeStr = this.toPackageExportData(products);

              const map = {};
              map["bizDate"] = dateUtil.formatDate(this.reqData.bizDate,'yyyyMMdd');
              if(subPaymentStatus !== ''){
                  subPaymentStatus = " AND SUB_PAYMENT_STATUS in " + subPaymentStatus;
              }else {
                  subPaymentStatus = '';
              }
              map["subPaymentStatus"] = subPaymentStatus;

              if (productCodeStr !== ''){
                  productCodeStr = " AND PRODUCT_CODE in " + productCodeStr
              }
              map["productCodeStr"] = productCodeStr;

              if(this.queryArgs.dataType == '00'){
                  this.excelFile(this.menuConfigInfoSumQL,JSON.stringify(map));
              }else{
                  this.excelFile(this.menuConfigInfoDetailQL,JSON.stringify(map));
              }
          }
      },
        toPackageExportData(data){
            let str = ''
            if(data.length > 0){
                for(let i = 0; i<data.length; i++){
                    if(i===0){
                        str = '(' + str +"'"+ this.paymentStatusList[i]+"'"
                    }else{
                        str = str + ",'" + this.paymentStatusList[i] + "'"
                    }
                }
                str = str+')'
            }else{
                str = ''
            }
            return str
        },
      excelFile(menuConfigInfo,map) {
          let pkId = menuConfigInfo.outputParam;
          const basePath = window.location.href.split("#/")[0];
          let url = basePath + "api/data-pipe/v1/etl/file/exportFile"
          let params = {ifPkId: pkId, mapStr: map};
          exportExcel.postExcelFile(params,url);
      },
        //置顶
      async setTops() {
          let rows = this.$refs.grid.getSelectedRows();
          if(rows.length === 0){
              this.$msg.warning('请选中列表数据后再操作---置顶!');
              return
          }
          const ok = await this.$msg.ask(`是否置顶?`);
          if (!ok) {
              return
          }
          if (rows.length > 0) {
              try {
                  let p;
                  rows.forEach((item)=>{
                      if(item.customerName){
                          p = this.$api.taVisualApi.updateTopTime(item.productCode,'01',item.customerName,'1');
                      } else {
                          p = this.$api.taVisualApi.updateTopTime(item.productCode,'00','','1');
                      }
                  });
                  await this.$app.blockingApp(p);
                  this.$msg.success('置顶成功!');
                  this.$refs.grid.reloadData();
              } catch (reason) {
                  this.$msg.error(reason);
              }
          }
      },

      //取消置顶
      async cancleTops() {
          let rows = this.$refs.grid.getSelectedRows();
          if(rows.length === 0){
              this.$msg.warning('请选中列表数据后再操作---取消置顶!');
              return
          }
          const ok = await this.$msg.ask(`是否取消置顶?`);
          if (!ok) {
              return
          }
          if (rows.length > 0) {
              try {
                  for(let i=0; i<rows.length; i++){
                      if(rows[i].customerName){
                        let dYear = new Date(rows[i].detailsTopTime).getFullYear();
                        if(dYear === 1900 || (dYear !== dYear)){
                            this.$msg.warning('请选中已置顶的数据!');
                            return
                        }
                      }else{
                        let tYear = new Date(rows[i].topTime).getFullYear();
                        if(tYear === 1900 || (tYear !== tYear)){
                            this.$msg.warning('请选中已置顶的数据!');
                            return
                        }
                      }
                  }
                  let p ;
                  rows.forEach((item)=>{
                      if(item.customerName){
                          p = this.$api.taVisualApi.updateTopTime(item.productCode,'01',item.customerName,'');
                      } else {
                          p = this.$api.taVisualApi.updateTopTime(item.productCode,'00','','');
                      }
                  });
                  await this.$app.blockingApp(p);
                  this.$msg.success('取消置顶成功!');
                  this.$refs.grid.reloadData();
              } catch (reason) {
                  this.$msg.error(reason);
              }
          }
      },

        //人工确认到账
       async confirmStatus(){
            let data = this.$refs.grid.getSelectedRows();
            if(data.length === 0){
                this.$msg.warning('请选中列表数据后再操作---人工确认到账!');
                return
            }
            const ok = await this.$msg.ask(`是否人工确认到账?`);
            if (!ok) {
                return
            }
            try{
                let params = [];
                data.forEach(function(item) {
                params.push({productCode:item.productCode,customerName:item.customerName});
                })
                this.$api.taVisualApi.updateReceivedStatus(params);
                this.$msg.success('更新成功');
            }catch(exception){
                this.$msg.error('更新异常');
            }
            this.$refs.grid.reloadData();
        },
        //查询
        search(){
          this.queryArgs.bizDate = this.reqData.bizDate;
          this.queryArgs.productCode=this.$store.state.publicData.publicCheckProductStr
            // 获取参数后，重新执行接口获取表格数据
          this.$refs.gfForm.search();
        },
        // 重置表单数据
        reset() {
            this.formItems[0].value='';
            this.formItems[1].value='';
            this.paymentStatusList = [];
            this.queryArgs.subPaymentStatus = '';
            this.search();
        },
        // 置顶功能
        async setTop(data) {
            const {params,status} = data;
            // 获取当前表格数据
            if (status === 0) {
                this.$api.taVisualApi.updateTopTime(params.data.productCode,'00','','1');
            } else if (status === 1) {
                this.$api.taVisualApi.updateTopTime(params.data.productCode,'01',params.data.customerName,'1');
            }   
            this.$refs.grid.reloadData();
        },
        onExportWithFront() {
            let _this = this.$refs.grid;
            const params = {
                sheetName: "sheet1",
                onlySelected: true,
                fileName: (_this.ext.exportName || 'export-') + this.$dateUtils.getCurrentTime()
            };
            const cloneUtil = this.$utils.deepClone;
            const fieldArr = _this.ext.needChangeField;
            params.processCellCallback = function (e) {
                if (e.value === undefined) {
                    return ''
                }
                e.type = 'String'
                if(fieldArr.includes(e.column.getColDef().field)){
                    return `'${e.value}`
                }
                const colDef = cloneUtil(e.column.colDef);
                const cl = colDef.cellClass;
                colDef.cellClass = [cl, "boldBorders"];

                const valueFormatter = colDef.valueFormatter;
                if (valueFormatter) {
                    return valueFormatter(e);
                } else {
                    return e.value;
                }
            };
                _this.gridController.gridApi.exportDataAsExcel(params);
            },
        // 选中表格数据时，需获取当前数据的子数据并插入到表格中
         async cellClicked(params){
            let rowData = this.$refs.grid.getRowData()
            const selectedId = params.data.productCode;
            //根据id 拿到子元素数据
            let childrenData = params.data.dssList;
            if (params.data.selected === '已加载') {
                let data = rowData.filter(function (item) {
                    if (item.productCode === selectedId && !item.customerName){
                        item.selected = '未加载';
                        return item;
                    } else if(item.productCode !== selectedId) {
                        return item;
                    }
                });
                this.setGridData(data)
            } else {
                let data = [];
                rowData.forEach(item => {
                    if(item.productCode === selectedId){
                        item.selected = '已加载';
                        data.push(item);
                        data.push(childrenData);
                    }else {
                        data.push(item);
                    }
                });
                this.setGridData(data.flat(Infinity))
            
            }
            this.$refs.grid.gridController.gridApi.ensureIndexVisible(params.rowIndex, 'top'); 
        },
    }
}
</script>
<style scoped>
    .form {
        display: flex;
        padding: 0;
    }
    .form .el-form-item {
        width: 23%;
        margin: 0 3% 0 0;
    }
</style>