<template>
  <div class="bottom-container">
    <gf-search-box ref="box"
                   open="false">
      <template slot="content">
        <gf-form ref="gfForm"
                 label-position="right"
                 label-width="150px"
                 :form-items="formItems"
                 @getParams="getParams"
                 :mode="'search'"
        ></gf-form>
      </template>
    </gf-search-box>
    <gf-grid grid-no="agnes-biz-product-rcp" toolbar="find" ref="grid"
             :query-args="queryArgs"
             :options="gridOption(this)">
      <template slot="left">
        <el-button class="action-btn" :class="{'cj-active': queryArgs.flag === '00'}" @click="showGridData('00')">
            汇总信息展示</el-button>
        <el-button class="action-btn" :class="{'cj-active': queryArgs.flag === '01'}" @click="showGridData('01')">
            明细信息展示</el-button>
        <el-button class="action-btn" @click="checkFlag" v-if="$hasPermission('agnes.biz.product.rcp.check')">
          人工确认到账</el-button>
          <el-button class="action-btn" @click="checkTop" v-if="$hasPermission('agnes.biz.product.rcp.check')">
              置顶</el-button>
          <el-button class="action-btn" @click="cancleTop" v-if="$hasPermission('agnes.biz.product.rcp.check')">
              取消置顶</el-button>
          <el-button class="action-btn" @click="exportAll" v-if="$hasPermission('agnes.biz.product.rcp.check')">
              导出</el-button>
          <el-button class="action-btn" @click="syncZjqs" v-if="$hasPermission('agnes.biz.sell.rcp.zjqs.sync') && isToday()">
            同步数据</el-button>
      </template>
      <template slot="right-before">
        <span class="normal-font">快速检索:</span>
      </template>
    <template slot="right-after">
      <el-button
              icon="el-icon-download"
              class="gf-grid-status-button"
              @click="onExportWithFront"
      ></el-button>
    </template>
    </gf-grid>
  </div>
</template>
<script>
import queryScheme from "./query-scheme";
import exportExcel from "../../../../../components/common/exportExcel";
import dateUtil from "@hex/gf-ui/src/util/date-utils";

export default {
  props: {
    reqData:{
      type:Object,
      request:true
    }
  },
  watch: {
    reqData:{
      handler() {
        this.queryArgs.bizDate = this.reqData.bizDate;
        this.search();
      },
      deep:true
    }
  },
  data() {
    return {
      gridOption: (_this)=>{
        return {
          onCellClicked(params) {
            // 定义可以被点击的单元格
            const showDetailArr = ['indexNo','productCode','channelName'];
            // （!params.data.name）name为空，则表明该数据为汇总数据
            if(showDetailArr.includes(params.colDef.field) && !params.data.channelName){
              _this.cellClicked(params)
            }
          },
        }
      },
      searchOptions: [],
      queryName:'',
      queryOptions:[],
      formItems: [
        {type: 'select',name: '展开方式:',value: '00',options: [{id: 'showWay',label:''}],
          props: { filterable:true, placeholder:'全部'}},
        {type: 'select',name: '托管户余额是否充足:',value: '',options: [{id: 'cashPosition',label:''}],
          props: { filterable:true, placeholder:'全部', multiple: true, collapseTags: true}},
        {type: 'select',name: 'TA户划款状态:',value: '',options: [{id: 'transferMoneyStatus',label:''}],
          props: { filterable:true, placeholder:'全部', multiple: true, collapseTags: true}},
        {type: 'select',name: '托管户划拨状态:',value: '',options: [{id: 'transferStatus',label:''}],
          props: { filterable:true, placeholder:'全部', multiple: true, collapseTags: true}},
        {type: 'select',name: 'TA户到账状态:',value: '',options: [{id: 'taAccountStatus',label:''}],
          props: { filterable:true, placeholder:'全部', multiple: true, collapseTags: true}},
          {type: 'children',
              childItems: [
                  {type: 'button',action: 'search',name: '查询',click:()=>{this.search()}},
                  {type: 'button',action: 'reset',name: '重置',click:()=>{this.reset()}},
                  {type: 'button',action: 'open',}
              ]},
      ],
      rowData :[],
      queryArgs: {
        flag:'00',//默认初始数据是汇总数据
        custRealBalStatus:null,
        taAccTransStatus:null,
        custAccTransStatus:null,
        taAccRcpStatus:null,
        productCode:null,
        bizDate:window.bizDate,
      },
      queryForm:{
        queryName: '',
        queryFlag: '',
        queryResult: ''
      },
      total: 0,
        menuConfigInfoSumQL:{},
        menuConfigInfoDetailQL:{},
    }
  },
  mounted() {
    this.getSearchOptions();
    this.initSearchOption();
    this.setDataTotal();
    this.initData();
      //使用公共树
      // this.$agnesUtils.usePublicTree(this.$route.path)
  },
  methods:{
    isToday(){
      return new Date().format("yyyy-MM-dd") === this.reqData.bizDate;
    },
    onExportWithFront() {
      let _this = this.$refs.grid;
      const params = {
        sheetName: "sheet1",
        onlySelected: true,
        fileName: (_this.ext.exportName || 'export-') + this.$dateUtils.getCurrentTime()
      };
      const cloneUtil = this.$utils.deepClone;
      const fieldArr = _this.ext.needChangeField;
      params.processCellCallback = function (e) {
        if (e.value === undefined) {
          return ''
        }
        e.type = 'String'
        if(fieldArr.includes(e.column.getColDef().field)){
          return `'${e.value}`
        }
        const colDef = cloneUtil(e.column.colDef);
        const cl = colDef.cellClass;
        colDef.cellClass = [cl, "boldBorders"];

        const valueFormatter = colDef.valueFormatter;
        if (valueFormatter) {
          return valueFormatter(e);
        } else {
          return e.value;
        }
      };

      _this.gridController.gridApi.exportDataAsExcel(params);
    },
    // 获取查询方案字典值
    async getSearchOptions() {
      //1 产品赎回款
      this.formItems[0].options = this.selectByDict("AGNES_SHOUW_WAY");
      this.formItems[1].options = this.selectByDict("AGNES_ACC_POST");
      this.formItems[2].options = this.selectByDict("AGNES_TRANS_STATUS");
      this.formItems[3].options = this.selectByDict("AGNES_ACC_TRANS_STATUS");
      this.formItems[4].options = this.selectByDict("AGNES_RCP_STATUS");
    },
    selectByDict(param) {
      return this.$app.dict
          .getDictItems(param)
          .map((dictItem) => {
            return {
              id: dictItem.dictId,
              label: dictItem.dictId + "-" + dictItem.dictName,
            };
          });
    },
    async setDataTotal(){
      const bizDate = this.reqData.bizDate;
      var result = await this.$api.productRcpApi.getProductRCPInfo('00',bizDate);
      this.total = result.data.total;

    },
      async initData(){
          const p = this.$api.fileScan.queryRuleConfigList();
          let res =  await this.$app.blockingApp(p);
          res.data.forEach(item=>{
              //直销柜台赎回款数据导出-全量-汇总
              if(item.ifCode === 'PRODUCT_RCP_EXPOERT_SUM_QL'){
                  this.menuConfigInfoSumQL.outputParam = item.pkId;
                  this.menuConfigInfoSumQL.resName = item.fileName;
              }
              //直销柜台赎回款数据导出-全量-明细
              if(item.ifCode === 'PRODUCT_RCP_EXPOERT_DETAIL_QL'){
                  this.menuConfigInfoDetailQL.outputParam = item.pkId;
                  this.menuConfigInfoDetailQL.resName = item.fileName;
              }
          });
      },
    //初始新增方案数据
    async initSearchOption(mode = "1"){
      let query = await this.$api.productRcpApi.selectQueryScheme(mode);
      const dataFomat = [];
      query.data.forEach(function(item, index){
        dataFomat.push({
          id: index+1,
          label: item.label,
          pkId:  item.id,
          formItem: eval("("+item.formItem+")")
        })
      })
      this.searchOptions = dataFomat;
    },
    // 表格数据塞入
    async setGridData(data) {
      this.$refs.grid.setRowData(data);
      this.$nextTick(()=>{
        this.$refs.grid.state.totalRowCount = this.total;
      })
      setTimeout(()=>{
        this.$refs.grid.handleMoreCommand('autoSizeFitColumns')
      },100)
    },
    // 重置表格列宽
    fitColumnsWidth() {
      setTimeout(()=>{
        this.$refs.grid.handleMoreCommand('autoSizeFitColumns')
      },500)
    },
     showGridData(param) {
         this.queryArgs.flag = param;
        this.reset();
       this.fitColumnsWidth();
      },

    //人工确认到账
    async checkFlag() {
      let rows = this.$refs.grid.getSelectedRows();
      if(rows.length === 0){
        this.$msg.warning('请选中列表数据后再操作---人工确认到账!');
        return
      }
      const ok = await this.$msg.ask(`是否人工确认到账?`);
      if (!ok) {
        return
      }
      if (rows.length > 0) {
        try {
          const p = this.$api.productRcpApi.checkFlag(rows);
          await this.$app.blockingApp(p);
          this.$msg.success('人工确认成功!');
          this.$refs.grid.reloadData();
        } catch (reason) {
          this.$msg.error(reason);
        }
      }
    },


      //置顶
      async checkTop() {
          let rows = this.$refs.grid.getSelectedRows();
          if(rows.length === 0){
              this.$msg.warning('请选中列表数据后再操作---置顶!');
              return
          }
          const ok = await this.$msg.ask(`是否置顶?`);
          if (!ok) {
              return
          }
          if (rows.length > 0) {
              try {
                  let p;
                  rows.forEach((item)=>{
                      if(item.channelName){
                          p = this.$api.productRcpApi.updateTopTime(item.productCode,'01',item.channelName,'');
                      } else {
                          p = this.$api.productRcpApi.updateTopTime(item.productCode,'00','','');
                      }
                  });
                  await this.$app.blockingApp(p);
                  this.$msg.success('置顶成功!');
                  this.$refs.grid.reloadData();
              } catch (reason) {
                  this.$msg.error(reason);
              }
          }
      },


      //取消置顶
      async cancleTop() {
          let rows = this.$refs.grid.getSelectedRows();
          if(rows.length === 0){
              this.$msg.warning('请选中列表数据后再操作---取消置顶!');
              return
          }
          const ok = await this.$msg.ask(`是否取消置顶?`);
          if (!ok) {
              return
          }
          if (rows.length > 0) {
              try {
                  let cancle = false;
                  rows.forEach((item) => {
                      if(item.channelName){
                          if(!item.detailsTopTime){
                              cancle = true;
                          }
                      } else {
                          if(!item.topTime){
                              cancle = true;
                          }
                      }
                  });
                  if (cancle) {
                      this.$msg.warning('请选中已置顶的数据!');
                      return
                  }

                  let p ;
                  rows.forEach((item)=>{
                      if(item.channelName){
                          p = this.$api.productRcpApi.updateTopTime(item.productCode,'01',item.channelName,'1');
                      } else {
                          p = this.$api.productRcpApi.updateTopTime(item.productCode,'00','','1');
                      }
                  });
                  await this.$app.blockingApp(p);
                  this.$msg.success('取消置顶成功!');
                  this.$refs.grid.reloadData();
              } catch (reason) {
                  this.$msg.error(reason);
              }
          }
      },

      //同步资金清算接口的数据
      async syncZjqs() {
        let rows = this.$refs.grid.getSelectedRows();
        if(rows.length === 0){
          this.$msg.warning('请选中列表数据后再操作---同步数据!');
          return
        }
        const ok = await this.$msg.ask(`是否同步数据?`);
        if (!ok) {
          return
        }
        // TA户
        const transAccArray = [];
        // rows.forEach((item)=>{
        //   if(item.taAccount){
        //     transAccArray.add(item.taAccount)
        //   }
        // });
        // 获取选中记录的组合信息
        const productArray = [];
        rows.forEach((item)=>{
          if(item.productCode){
            productArray.add(item.productCode)
          }
        });
        // 对数组进行判空操作
        if (productArray.length === 0 ){
          this.$msg.warning('未获取到选中数据的基础信息，请联系管理员!');
          return
        }
        // 对数组进行去重复
        const productResult = Array.from(new Set(productArray))

        try {
          let p = this.$api.dirSellRcpApi.syncZjqs({"transAccList":transAccArray,"productCodeList":productResult,"bizDate":dateUtil.formatDate(this.reqData.bizDate,'yyyyMMdd')})
          await this.$app.blockingApp(p);
          this.$msg.success('同步资金清算数据成功!');
          this.$refs.grid.reloadData();
        } catch (reason) {
          this.$msg.error(reason);
        }
      },

      //导出
      async exportAll() {
          if (this.menuConfigInfoSumQL == undefined || this.menuConfigInfoSumQL.outputParam == null || this.menuConfigInfoSumQL.outputParam == undefined) {
              this.$msg.error('请完善导出相关配置！');
              return;
          }
          let rows = this.$refs.grid.getSelectedRows();
          if (rows.length != 0) {
              this.$msg.warning('无需选中列表数据---可直接导出!');
              return;
          }else{
              this.search();

              const map = {};
              map["today"] = dateUtil.formatDate(this.reqData.bizDate,'yyyyMMdd');

              var custRealBalStatus;
              if(this.queryArgs.custRealBalStatus){
                  custRealBalStatus = " AND A.CUST_REAL_BAL_STATUS in " + this.splitOutParams(this.queryArgs.custRealBalStatus);
              }else {
                  custRealBalStatus = '';
              }
              map["custRealBalStatus"] = custRealBalStatus;

              var taAccTransStatus;
              if(this.queryArgs.taAccTransStatus){
                  taAccTransStatus = " AND A.TA_ACC_TRANS_STATUS in " + this.splitOutParams(this.queryArgs.taAccTransStatus);
              }else {
                  taAccTransStatus = '';
              }
              map["taAccTransStatus"] = taAccTransStatus;

              var custAccTransStatus;
              if(this.queryArgs.custAccTransStatus){
                  custAccTransStatus = " AND A.CUST_ACC_TRANS_STATUS in " + this.splitOutParams(this.queryArgs.custAccTransStatus);
              }else {
                  custAccTransStatus = '';
              }
              map["custAccTransStatus"] = custAccTransStatus;

              var taAccRcpStatus;
              if(this.queryArgs.taAccRcpStatus){
                  taAccRcpStatus = " AND A.TA_ACC_RCP_STATUS in " + this.splitOutParams(this.queryArgs.taAccRcpStatus);
              }else {
                  taAccRcpStatus = '';
              }
              map["taAccRcpStatus"] = taAccRcpStatus;

              let products = this.$store.state.publicData.publicTreeCheckedKeys.data
              let productCodeStr = this.toPackageExportData(products);
              if(this.queryArgs.productCode !== ''){
                  productCodeStr = " AND A.PRODUCT_CODE in " + productCodeStr
              }else {
                  productCodeStr = '';
              }
              map["productCodeStr"] = productCodeStr;

              if(this.queryArgs.flag == '00'){
                  this.excelFile(this.menuConfigInfoSumQL,JSON.stringify(map));
              }else{
                  this.excelFile(this.menuConfigInfoDetailQL,JSON.stringify(map));
              }
          }
      },
      toPackageExportData(data){
          let str = ''
          if(data.length > 0){
              for(let i = 0; i<data.length; i++){
                  if(i===0){
                      str = '(' + str +"'"+ this.paymentStatusList[i]+"'"
                  }else{
                      str = str + ",'" + this.paymentStatusList[i] + "'"
                  }
              }
              str = str+')'
          }else{
              str = ''
          }
          return str
      },
      excelFile(menuConfigInfo,map) {
          let pkId = menuConfigInfo.outputParam;
          const basePath = window.location.href.split("#/")[0];
          let url = basePath + "api/data-pipe/v1/etl/file/exportFile"
          let params = {ifPkId: pkId, mapStr: map};
          exportExcel.postExcelFile(params,url);
      },
    // 查询方案切换时触发
    changeFormItem(searchOptions){
      let temp = this.formItems[this.formItems.length-1];
      let formItem = [];
      searchOptions.formItem.forEach(item=>{
        formItem.push(item);
      })
      formItem.push(temp)
      this.formItems = this.$lodash.cloneDeep(formItem);
      this.queryName = searchOptions.label;
      this.queryOptions = searchOptions;
    },
    // 获取表单参数
    async getParams(params) {
      if(params[0].value !== ''){
        this.queryArgs.flag = params[0].value;
      }
      this.queryArgs.custRealBalStatus = params[1].value === "" || params[1].value === [] ? null : this.splitParams(params[1].value);
      this.queryArgs.taAccTransStatus = params[2].value === "" || params[2].value === [] ? null : this.splitParams(params[2].value);
      this.queryArgs.custAccTransStatus = params[3].value === "" || params[3].value === [] ? null : this.splitParams(params[3].value);
      this.queryArgs.taAccRcpStatus = params[4].value === "" || params[4].value === [] ? null : this.splitParams(params[4].value);
      this.$refs.grid.reloadData();
    },
      splitParams(param) {
          let value = "a";
          param.forEach((item) => {
              value = value + "b" + item + "bc";
          });
          value = value.substring(0, value.lastIndexOf("c")) + "d";
          if(value == "d"){
              value = null;
          }
          return value;
      },
      splitOutParams(param) {
          let value = param;
          value = value.replaceAll("a", "(")
              .replaceAll("b", "'")
              .replaceAll("c", ",")
              .replaceAll("d", ")");
          return value;
      },
    // 查询
    search() {
      this.queryArgs.bizDate = this.reqData.bizDate;
      this.queryArgs.productCode = this.$store.state.publicData.publicCheckProductStr
      this.$refs.gfForm.search();
    },
    // 重置表单数据
    async reset() {
      this.$refs.gfForm.reset();
      this.formItems[0].value = this.queryArgs.flag
      this.$refs.box.searchValue = null;
      this.queryArgs.custRealBalStatus = null;
      this.queryArgs.taAccTransStatus = null;
      this.queryArgs.custAccTransStatus = null;
      this.queryArgs.taAccRcpStatus = null;
      await this.$refs.grid.reloadData();
      // this.$nextTick(()=>{
      //   this.$refs.grid.handleMoreCommand('autoSizeFitColumns')
      // })
      this.queryName = '';
    },
    // 置顶功能
    async setTop(data) {
        const {params,status} = data;
        // 获取当前表格数据
        let rowData = this.$refs.grid.getRowData();
        let selectedRowData = rowData.filter(item => {
            // 判断是否是汇总数据，并且item.selected 是否等于 '已加载'
            return item.channelName === '' && item.selected === '已加载'
        })
        console.log(params,selectedRowData,data)
        if (status === 0) {
            this.$api.productRcpApi.updateTopTime(params.data.productCode,'00','','');
        } else if (status === 1) {
            this.$api.productRcpApi.updateTopTime(params.data.productCode,'01',params.data.channelName,'');
        }
        this.$refs.grid.reloadData();
    },
    // 选中表格数据时，需获取当前数据的子数据并插入到表格中
    async cellClicked(params){
      let rowData = this.$refs.grid.getRowData()
      const selectedId = params.data.productCode;
      let childrenData = params.data.dssList;
      if (params.data.selected === '已加载') {
        let data = [...rowData].filter(x => [...childrenData].every(y => y !== x));
        data.forEach(item => {
          if (item.productCode === selectedId && !item.channelName){
            item.selected = '未加载';
          }
        });
        await this.setGridData(data)
      } else {
        let data = [];
        rowData.forEach(item => {
          if(item.productCode === selectedId && childrenData){
            item.selected = '已加载';
            data.push(item);
            data.push(childrenData);
          }else {
            data.push(item);
          }
        });
        await this.setGridData(data.flat(Infinity))
      }
      this.$refs.grid.gridController.gridApi.ensureIndexVisible(params.rowIndex, 'top'); 
    },
    // 新增查询方案
    addQueryScheme() {
      this.showSchemeDlg();
    },
    showSchemeDlg() {
      this.$nav.showDialog(
          queryScheme,
          {
            width: '600px',
            title: '新增查询方案',
            closeOnClickModal: false,
            args: {
              actionOk: (val) => {
                let data = this.$lodash.cloneDeep(this.$refs.gfForm.formItems)
                // this.$refs.box.addSearchOption(data,val,'1')//'1'标识当前模块的新增方案数据
                this.addSearchOption(data,val,'1')//'1'标识当前模块的新增方案数据
              },
            }
          }
      );
    },
    async addSearchOption(data,val,mode) {
      let formItem = [];
      if(data.length>1){
        data.forEach(item=>{
          if(item.type !== 'children'){
            formItem.push(item)
          }
        })
      }
      this.searchValue = val;
      this.queryForm.queryName = val;
      this.queryForm.queryResult = formItem;
      this.queryForm.queryFlag = mode;
      const p = this.$api.productRcpApi.saveQueryScheme(this.queryForm);
      await this.$app.blockingApp(p);
      //刷新查询方案数据
      this.initSearchOption(mode)
      this.reset()
    },
      // 保存查询方案
      async editQueryScheme() {
          if(this.queryName){
              const ok = await this.$msg.ask(`确认修改该查询方案吗, 是否继续?`);
              if (!ok) {
                  return
              }
              var queryForm = {};
              queryForm.queryName = this.queryName;
              queryForm.pkId = this.queryOptions.pkId;
              queryForm.queryResult = this.formItems;
              this.$api.productRcpApi.saveQueryScheme(queryForm);
              this.$msg.success('修改成功');

              //刷新查询方案数据
            this.initSearchOption()
            this.reset();
        }
      },
      // 删除查询方案
      async deleteQueryScheme() {
          if(this.queryName){
              const ok = await this.$msg.ask(`确认删除该查询方案吗, 是否继续?`);
              if (!ok) {
                  return
              }
              try {
                  var queryForm = {};
                  queryForm.pkId = this.queryOptions.pkId;
                  await this.$api.productRcpApi.removeQueryScheme(queryForm);
                  this.$msg.success('删除成功');

                  //刷新查询方案数据
                this.initSearchOption()
                this.reset();
              } catch (reason) {
                  this.$msg.error(reason);
              }
          }
      }
  }
}
</script>