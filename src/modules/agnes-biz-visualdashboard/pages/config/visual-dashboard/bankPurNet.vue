<template>
    <div class="bottom-container">
        <gf-search-box ref="cjForm"
                       :open="false"
        >
            <template slot="left-content">
              <gf-form ref="gfForm"
                       label-position="right"
                       label-width="150px"
                       :form-items="formItems"
                       @getParams="getParams"
                       :mode="'search'"
              ></gf-form>
            </template>
        </gf-search-box>
        <!--agnes-biz-bank-net-pur-list-->
        <gf-grid grid-no="agnes-biz-bank-net-pur-list" toolbar="find" ref="grid"
                 :query-args="queryArgs">
            <template slot="left">
                <el-button class="action-btn" @click="setTops">
                    置顶</el-button>
                <el-button class="action-btn" @click="cancelTops">
                    取消置顶</el-button>
                <el-button class="action-btn" @click="syncZjqs" v-if="$hasPermission('agnes.biz.sell.rcp.zjqs.sync') && isToday()">
                  同步数据</el-button>
<!--                <el-button class="action-btn" @click="exportExcel">-->
<!--                    导出</el-button>-->
            </template>
            <template slot="right-before">
                <span class="normal-font">快速检索:</span>
            </template>
            <template slot="right-after">
                <el-button
                        icon="el-icon-download"
                        class="gf-grid-status-button"
                        @click="exportExcel"
                ></el-button>
            </template>
        </gf-grid>
    </div>
</template>
<script>
import dateUtil from "@hex/gf-ui/src/util/date-utils";
    export default {
      props: {
        reqData:{
          type:Object,
          request:true
        }
      },
      watch: {
        reqData:{
          handler() {
            this.queryArgs.bizDate = this.reqData.bizDate;
            this.search();
          },
          deep:true
        }
      },
        data() {
            return {
                formItemsList:[],
                queryArgs: {
                    'taActive':'',
                    productCode:'',
                    bizDate:window.bizDate,
                },
                showSearchBox: false,
                searchOptions: [],
                formItems: [
                {   type: 'select', name: 'Ta账户到账状态:', value: '', options: [],
                    props: { filterable:true,multiple: true, collapseTags: true }},
                    {type: 'children',
                        childItems: [
                            {type: 'button',action: 'search',name: '查询',click:()=>{this.search()}},
                            {type: 'button',action: 'reset',name: '重置',click:()=>{this.reset()}},
                        ]},
                ],
            }
        },
        computed: {
            word: function() {
                if (this.showSearchBox == false) {
                    return "展开";
                } else {
                    return "收起";
                }
            }
        },
        beforeMount() {
            const p = this.getOptionData();
            this.$app.blockingApp(p);
        },
        mounted() {
            // 收起搜索
            this.$nextTick(function() {
                this.closeSearch();
            });
            //使用公共树
            // this.$agnesUtils.usePublicTree(this.$route.path)
        },
        methods:{
            isToday(){
              return new Date().format("yyyy-MM-dd") === this.reqData.bizDate;
            },
            async exportExcel() {
                const ok = await this.$msg.ask(`是否导出文件`);
                if (!ok) {
                    return
                }
                let formItemsListString ='';
                formItemsListString = this.formItems[0].value.join(',')
                let productCode = this.queryArgs.productCode
                try {
                    const p =  this.$api.bankNetPurApi.exportExcel(formItemsListString,productCode);
                    const submit =  await this.$app.blockingApp(p);
                    let docId = submit.data;
                    let fileId = '';
                    const doc = this.$api.ecmUploadApi.getOisFileList(docId);
                    const res = await this.$app.blockingApp(doc);
                    if (res.status === '0001') {
                        this.$msg.warning('下载失败');
                        return
                    }
                    if (res.files[0]) {
                        fileId = res.files[0].objectId;
                        const basePath = window.location.href.split("#/")[0];
                        window.open(basePath + 'api/ecm-server/ecm/file/download/' + fileId);
                    } else {
                        this.$msg.warning('找不到文件');
                    }
                } catch (reason) {
                    this.$msg.error(reason);
                }
            },
            //同步资金清算接口的数据
            async syncZjqs() {
              let rows = this.$refs.grid.getSelectedRows();
              if(rows.length === 0){
                this.$msg.warning('请选中列表数据后再操作---同步数据!');
                return
              }
              const ok = await this.$msg.ask(`是否同步数据?`);
              if (!ok) {
                return
              }
              const transAccArray = [];
              // rows.forEach((item)=>{
              //   if(item.taAccount){
              //     transAccArray.add(item.taAccount)
              //   }
              // });
              // 获取选中记录的组合信息
              const productArray = [];
              rows.forEach((item)=>{
                if(item.productCode){
                  productArray.add(item.productCode)
                }
              });
              // 对数组进行判空操作
              if (productArray.length === 0 ){
                this.$msg.warning('未获取到选中数据的基础信息，请联系管理员!');
                return
              }
              // 对数组进行去重复
              const productResult = Array.from(new Set(productArray))

              try {
                let p = this.$api.dirSellRcpApi.syncZjqs({"transAccList":transAccArray,"productCodeList":productResult,"bizDate":dateUtil.formatDate(this.reqData.bizDate,'yyyyMMdd')})
                await this.$app.blockingApp(p);
                this.$msg.success('同步资金清算数据成功!');
                this.$refs.grid.reloadData();
              } catch (reason) {
                this.$msg.error(reason);
              }
            },
            async setTops() {
                let rows = this.$refs.grid.getSelectedRows();

                if(rows.length === 0){
                    this.$msg.warning('请选中列表数据后再操作');
                    return
                }
                const ok = await this.$msg.ask(`是否置顶?`);
                if (!ok) {
                    return
                }
                try {
                    const p = this.$api.bankNetPurApi.setTopTime(rows);
                    await this.$app.blockingApp(p);
                    this.$refs.grid.reloadData();
                    this.$msg.success('置顶成功!');
                } catch (reason) {
                    this.$msg.error(reason);
                }

            },
            async cancelTops() {
                let rows = this.$refs.grid.getSelectedRows();
                console.log(rows);

                if(rows.length === 0){
                    this.$msg.warning('请选中列表数据后再操作');
                    return
                }
                if (rows.length > 0) {
                    let cancle = false;
                    rows.forEach((item) => {
                        if (item.topTime) {
                            if (!item.topTime) {
                                cancle = true;
                            }
                        } else {
                            if (!item.topTime) {
                                cancle = true;
                            }
                        }
                    });
                    if (cancle) {
                        this.$msg.warning('请选中已置顶的数据!');
                        return
                    }
                }
                const ok = await this.$msg.ask(`是否取消置顶?`);
                if (!ok) {
                    return
                }
                try {
                    const p = this.$api.bankNetPurApi.cancelSetTopTime(rows);
                    await this.$app.blockingApp(p);
                    this.$refs.grid.reloadData();
                    this.$msg.success('取消置顶成功!');
                } catch (reason) {
                    this.$msg.error(reason);
                }
            },
            // 获取申购款到账状态字典值
            async getOptionData() {
                this.formItems[0].options = this.$app.dict
                    .getDictItems("AGNES_RCP_STATUS")
                    .map((dictItem) => {
                        return {
                            id: dictItem.dictId,
                            label: dictItem.dictId + "-" + dictItem.dictName,
                        };
                    });
            },
            closeSearch() {
                this.showSearchBox = !this.showSearchBox;
                const dom = document.getElementById("searchBox4");
                if (!this.showSearchBox) {
                    dom.style.height = 41 + "px";
                } else {
                    dom.style.height = "auto";
                }
            },
            getParams(params) {
                this.queryArgs.taActive = params[0].value.join(',');
                this.$refs.grid.reloadData();
            },
            search() {
                this.$parent.fatherMethod();
              this.queryArgs.bizDate = this.reqData.bizDate;
              this.queryArgs.productCode = this.$store.state.publicData.publicCheckProductStr
                this.$refs.gfForm.search();
            },
            reset() {
                this.formItemsList=[];
                this.formItems[0].value='';
                this.queryArgs.taActive='';
                this.search();
            },
            // 置顶方法
            async setTop(params) {
                const id  = params.params.data.fundNo;
                const p = this.$api.bankNetPurApi.setTop(id);
                await this.$app.blockingApp(p);
                this.$refs.grid.reloadData();
            },
            onExportWithFront() {
                let _this = this.$refs.grid;
                const params = {
                    sheetName: "sheet1",
                    onlySelected: true,
                    fileName: (_this.ext.exportName || 'export-') + this.$dateUtils.getCurrentTime()
                };
                const cloneUtil = this.$utils.deepClone;
                const fieldArr = _this.ext.needChangeField;
                params.processCellCallback = function (e) {
                    if (e.value === undefined) {
                        return ''
                    }
                    e.type = 'String'
                    if(fieldArr.includes(e.column.getColDef().field)){
                        return `'${e.value}`
                    }
                    const colDef = cloneUtil(e.column.colDef);
                    const cl = colDef.cellClass;
                    colDef.cellClass = [cl, "boldBorders"];

                    const valueFormatter = colDef.valueFormatter;
                    if (valueFormatter) {
                        return valueFormatter(e);
                    } else {
                        return e.value;
                    }
                };

                _this.gridController.gridApi.exportDataAsExcel(params);
            },
        }
    }
</script>

<style scoped>
    .form {
        display: flex;
        padding: 0;
    }
    .form .el-form-item {
        width: 23%;
        margin: 0 3% 0 0;
    }
</style>