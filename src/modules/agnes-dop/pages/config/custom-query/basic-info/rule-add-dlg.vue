<template>
    <div class="rule-add-dlg">
        <el-form :model="form" label-width="90px" ref="form" style="height: 100%;" :rules="rules">
            <div class="line">
                <el-form-item label="规则名称" prop="customQueryName" style="width: 100%;">
                    <gf-input v-model="form.customQueryName" :max-byte-len="50"></gf-input>
                </el-form-item>
                <el-form-item label="规则编号" prop="customQueryCode" style="width: 100%;">
                    <el-input v-model="form.customQueryCode" disabled></el-input>
                </el-form-item>
            </div>
            <div class="line">
                <el-form-item label="业务标签" prop="bizType" style="width: 50%;">
                    <el-input v-model="form.bizType"></el-input>
                </el-form-item>
                <el-row :gutter="50">
                    <el-col :span="16">
                        <el-form-item label="数据源" prop="bizParamDb" style="width: 120%">
                            <el-select v-model="form.bizParamDb" @change="handleDbChange">
                              <el-option v-for="(item, index) in dataSourceList" :key="index" :label="item.dsName" :value="item.id"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <span class="common-error">{{sourceText}}</span>
                    </el-col>
                </el-row>
            </div>
            <div class="line" v-if="showMode">
                <el-form-item>
                    <el-radio-group v-model="form.queryMode">
                        <el-radio label="table">目标表</el-radio>
                        <el-radio label="sql">执行脚本</el-radio>
                    </el-radio-group>
                </el-form-item>
            </div>
            <el-form-item label="目标表" v-if="form.queryMode === 'table'" prop="targetTableId" style="width: 50%;">
                <el-select v-model="form.targetTableId" @change="targetTableChange" clearable>
                    <el-option v-for="item in formatTargetTableList" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <el-button @click="initFields">加载列表字段</el-button>
            </el-form-item>
            <el-form-item label="执行脚本" v-if="form.queryMode === 'sql'" prop="scriptSql" class="mar10">
                <gf-input type="textarea" v-model="form.scriptSql" resize="none" :autosize="{ minRows: 5, maxRows: 20 }" style="width: 98%;"></gf-input>
                <el-button type="primary" @click="initFields">加载列表字段</el-button>
                <span v-if="checkResult.flag === false" class="common-error">{{ checkResult.info }}</span>
            </el-form-item>
            <el-form-item label="加载字段" prop="fields" style="width: 100%;">
                <div class="optionButtonGroup">
                    <gf-button @click="removeRow" class="action-btn" size="mini">删除</gf-button>
                    <gf-button @click="move('up')" class="action-btn" size="mini">上移</gf-button>
                    <gf-button @click="move('down')" class="action-btn" size="mini">下移</gf-button>
                </div>
                <el-table :data="this.form.dopReCustomQueryFields" max-height="580" style="width: 100%;" ref="table" key="customQueryTable">
                    <el-table-column type="selection" width="50"></el-table-column>
                    <el-table-column prop="indexNo" label="序号" width="50"></el-table-column>
                    <el-table-column prop="queryFieldKey" label="SQL字段" min-width="100">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.queryFieldKey" disabled></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="fieldKey" label="字段编码" min-width="100">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.fieldKey"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="queryFieldName" label="列名" min-width="140">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.queryFieldName" :style="!scope.row.queryFieldName ? 'border:1px solid #f00;border-radius:5px;' : ''"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="headerGroup" label="列头分组" min-width="100">
                        <template slot-scope="scope">
                            <el-input v-model="scope.row.headerGroup"></el-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="dictTypeId" label="字典定义" min-width="100">
                        <template slot-scope="scope">
                            <el-select v-model="scope.row.dictTypeId" filterable clearable placeholder="请选择">
                                <el-option v-for="dict in dictData" :key="dict.dictTypeId" :label="dict.dictTypeName" :value="dict.dictTypeId"></el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column prop="isDisplay" label="是否显示" width="50">
                        <template slot-scope="scope">
                            <el-checkbox v-model="scope.row.isDisplay" true-label="1" false-label="0"></el-checkbox>
                        </template>
                    </el-table-column>
                    <el-table-column prop="alignStyle" label="对齐方式" width="100">
                        <template slot-scope="scope">
                            <gf-dict-select dict-type="OPDS_KPI_ALIGN" v-model="scope.row.alignStyle" :clearable="false" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="queryFieldType" label="数据类型" width="100">
                        <template slot-scope="scope">
                            <gf-dict-select dict-type="TASK_DEF_DATATYPE" v-model="scope.row.queryFieldType" :clearable="false" />
                        </template>
                    </el-table-column>
                    <el-table-column prop="stringFormat" label="格式化" min-width="100">
                        <template slot-scope="scope">
                            <gf-dict-select dict-type="OPDS_KPI_FORMATTER" v-model="scope.row.stringFormat" />
                        </template>
                    </el-table-column>
                    <el-table-column label="查询条件" align="center">
                        <el-table-column label="是否查询条件" width="100" prop="isQuery">
                            <template slot-scope="scope">
                                <el-checkbox v-model="scope.row.isQuery" true-label="1" false-label="0" @change="changeIsQuery(scope.row)"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column label="默认值" min-width="100" prop="queryDefaultValue">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.queryDefaultValue" :disabled="scope.row.isQuery === '1' ? false : true"></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="是否组合" width="70" prop="isGroup">
                            <template slot-scope="scope">
                                <el-checkbox v-model="scope.row.isGroup" true-label="1" false-label="0" :disabled="scope.row.isQuery === '1' && scope.row.queryFieldType === 'date' ? false : true"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column label="是否必填" width="75" prop="isRequired">
                            <template slot-scope="scope">
                                <el-checkbox v-model="scope.row.isRequired" true-label="1" false-label="0" :disabled="scope.row.isQuery === '1' ? false : true"></el-checkbox>
                            </template>
                        </el-table-column>
                        <el-table-column label="条件排序" width="80" prop="queryOrder">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.queryOrder" :disabled="scope.row.isQuery === '0'" placeholder="请输入"></el-input>
                            </template>
                        </el-table-column>
                    </el-table-column>
                </el-table>
            </el-form-item>
            <el-form-item label="拓展操作" style="width: 100%;">
                <el-button type="text" @click="showExpansion = !showExpansion">{{ showExpansion ? '折叠' : '展开' }}</el-button>
                <el-table :data="form.dopReCustomQueryOperations" key="customOptionTable" v-if="showExpansion" border>
                    <el-table-column label="操作" width="100px">
                        <template slot-scope="scope">
                            <el-button type="text" @click="deleteOption(scope.$index)">删除</el-button>
                        </template>
                    </el-table-column>
                    <el-table-column width="200px">
                        <template slot="header">
                            <p><span style="color: red;">*</span> 名称</p>
                        </template>
                        <template slot-scope="scope">
                            <el-form-item class="option-item" :prop="`dopReCustomQueryOperations.${scope.$index}.buttonName`" :rules="rules.buttonName">
                                <gf-input v-model="scope.row.buttonName" placeholder="请输入" :disabled="mode === 'view'"></gf-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column min-width="200px">
                        <template slot="header">
                            <p><span style="color: red;">*</span> 请求地址</p>
                        </template>
                        <template slot-scope="scope">
                            <el-form-item class="option-item" :prop="`dopReCustomQueryOperations.${scope.$index}.requestUrl`" :rules="rules.requestUrl">
                                <gf-input v-model="scope.row.requestUrl" placeholder="请输入" :disabled="mode === 'view'"></gf-input>
                            </el-form-item>
                        </template>
                    </el-table-column>
                    <el-table-column label="查看角色" min-width="200px">
                        <template slot-scope="scope">
                            <select-picker
                              filterable
                              collapse-tags
                              v-model="scope.row.roleIdList" 
                              :options="roleList.map(_=>({value:_.roleId, label:_.roleName}))"
                            />
                        </template>
                    </el-table-column>
                    <el-table-column label="说明" min-width="200px">
                        <template slot-scope="scope">
                            <gf-input v-model="scope.row.remark" placeholder="请输入" :disabled="mode === 'view'"></gf-input>
                        </template>
                    </el-table-column>
                </el-table>
                <el-button v-if="showExpansion" @click="addOptions()" style="margin-top: 6px;">新增</el-button>
            </el-form-item>
        </el-form>
    </div>
</template>

<script>
import lodash from 'lodash';
import GFUserApi from '../../../../../../api/gfApi/GFUserApi'
export default {
    name: 'rule-add-dlg',
    props: {
        mode: {
            type: String,
            default: 'add'
        },
        row: Object,
        treeNode: Object,
        actionOk: Function
    },
    data() {
        return {
            dictData: [],
            dataSourceList:[], //数据源列表
            sourceText:'',//源文字
            loadListParam:[], //加载列参数
            checkResult:{},
            form: {
                pkId: '',
                customQueryKey: '',
                customQueryName: '',
                customQueryCode: '',
                bizType: '',
                targetTableId: '',
                targetTableName: '',
                scriptSql: '',
                dopReCustomQueryFields: [],
                bizParamDb: '',
                dopReCustomQueryOperations: [],
                queryMode: 'table',
            },
            currentDataSource:{
                id:'',
                dsType:'' //1 单源，2 多源
            },
            rules: {
                customQueryName: [{ required: true, message: '规则名称必填', trigger: 'blur' }],
                scriptSql: [{ required: true, message: '执行脚本必填', trigger: 'blur'}],
                buttonName:[{ required: true, message: '名称必填', trigger: 'blur'}],
                requestUrl:[{ required: true, message: '请求地址必填', trigger: 'blur'}],
                targetTableId:[{ required: true, message: '目标表必选', trigger: 'change'}],
            },
            showExpansion: true,
            roleList: [],
            checkRoles: [],
            showMode: true,
            targetTableList: []
        };
    },
    computed: {
        formatTargetTableList() {
            if(this.targetTableList?.length) {
                return this.targetTableList.map(item => ({ value: item.filter1, label: `${item.dictName}(${item.filter1})` }));
            }else {
                return []
            }
        }
    },
    beforeMount() {
        this.getMode()
        this.initDict();
        this.getAllRoles()
        this.targetTableList = this.$app.dict.getDictItems('CUSTOM_QUERY_TABLE') || []
        Object.assign(this.form, (({seqNo, treeCode, treeName}) => ({seqNo, treeCode, treeName}))(this.treeNode))
        console.log(this.targetTableList, 'CUST');
        
    },
    async mounted() {
        if (this.mode !== 'add') {
            Object.assign(this.form, this.row);
            this.queryFields(this.form.pkId);
            this.queryExtedList(this.form.pkId)
        } else {
            this.getRuleCode();
        }
        await this.queryDataSource();
        this.handleDbChange(this.form.bizParamDb);
    },
    methods: {
        async getMode() {
            try {
                const resp = await this.$api.customQueryApi.getMode()
                if(resp?.data === 'tableMode') {
                    this.showMode = false
                }else {
                    this.showMode = true
                }
            } catch (error) {
                this.showMode = true
            }
        },
        async getRuleCode() {
            const resp = await this.$api.codeGeneratorApi.getCode('zdy');
            if (resp) {
                this.form.customQueryCode = resp.data;
            }
        },
        async getAllRoles() {
            let params = {
                pageIndex: 0,
                pageSize: 1000,
            }
            const resp = await GFUserApi.getAllRoles(params)
            if(resp.ok && resp.data?.data) {
                this.roleList = resp.data.data || []
                this.checkRoles = this.roleList.map(item => item.roleId)
            }else {
                this.roleList = []
            }
            
        },
        targetTableChange() {
            let data = this.targetTableList.find(item => item.dictId === this.form.targetTableId)
            if(data) {
                this.form.targetTableName = data.dictName;
            }
        },
        async initFields() {
        this.checkResult ={
          flag:'',
          info:''
        }
        if(this.form.bizParamDb == '' || this.form.bizParamDb == undefined){
          return this.$message.error("请先选择数据源");
        }
        if(this.form.scriptSql == '' || this.form.scriptSql == undefined){
          return this.$message.error("请先输入SQL语句")
        }else{
          let temp = this.form.scriptSql.replace(/^\s+/, '').split(' ');
          if(temp[0].toLowerCase() !== 'select'){
            this.checkResult.flag = false;
            this.checkResult.info = '只允许输入查询语句';
            return;
          }
        }
        // 获取当前选择的数据源：1单源，2多源
        let tempData = this.dataSourceList.filter((item)=>{
          return this.form.bizParamDb == item.id;
        })
        this.currentDataSource = tempData[0];
        // 提取表名并放入数组
        let result = [];

        // 提取 FROM 后的表名（支持多个）
        const fromRegex = /FROM\s+([a-zA-Z0-9_]+(\.[a-zA-Z0-9_]+)*)/gi;
        let fromMatch;
        while ((fromMatch = fromRegex.exec(this.form.scriptSql)) !== null) {
          result.push(fromMatch[1]);
        }
        // 提取 JOIN 后的表名（支持多个）
        const joinRegex = /JOIN\s+([a-zA-Z0-9_]+(\.[a-zA-Z0-9_]+)*)/gi;
        let joinMatch;
        while ((joinMatch = joinRegex.exec(this.form.scriptSql)) !== null) {
          result.push(joinMatch[1]);
        }

        this.loadListParam = [];
        // 单源校验
        if( this.currentDataSource.dsType == '1') {
          for (let i = 0; i < result.length; i++) {
            let item = result[i];
            let tempStr = result[i].split('.');
            if (tempStr.length === 3) {
              this.checkResult.flag = false;
              this.checkResult.info = `${item} 不是合法的表名，请检查`;
              return;
            }
          }
        } else { // 多源校验
          for (let i = 0; i < result.length; i++) {
            let item = result[i];
            let tempStr = result[i].split('.');
            if (tempStr.length !== 3) {
              this.checkResult.flag = false;
              this.checkResult.info = `${item} 不是合法的表名，请检查`;
              return;
            }
          }
        }
        if (this.form.scriptSql !== null) {
          let params = {
            customQueryId: this.form.pkId,
            sql: this.form.scriptSql,
            id: this.form.bizParamDb
          }
          const p = this.$api.customQueryApi.initFields(params);
          const resp = await this.$app.blockingApp(p);
          if (resp && resp.data) {
            let fields = this.form.dopReCustomQueryFields;
            this.form.dopReCustomQueryFields = resp.data.map((item, index) => {
              let newRow = {
                queryFieldKey: item.queryFieldKey || '',
                fieldKey: item.fieldKey || '',
                queryFieldName: item.queryFieldName || '',
                queryFieldType: item.queryFieldType || 'string',
                indexNo: item.indexNo || index + 1,
                dictTypeId: item.dictTypeId || '',
                isDisplay: item.isDisplay || '1',
                alignStyle: item.alignStyle || 'left',
                stringFormat: item.stringFormat || '',
                isQuery: item.isQuery || '0',
                queryDefaultValue: item.queryDefaultValue || '',
                isGroup: item.isGroup || '0',
                headerGroup: item.headerGroup || '',
                queryOrder: item.queryOrder || '',
                isRequired: item.isRequired || '0'
              };
              let hasRow = fields.find(field => field.fieldKey === item);
              return hasRow ? hasRow : newRow;
            });
          }
        }
      },
        async initDict() {
            this.dictData = [];
            const p = this.$api.customQueryApi.queryDictList();
            const resp = await this.$app.blockingApp(p);
            if (resp && resp.data) {
                this.dictData = lodash.clone(resp.data);
            }
        },
        changeIsQuery(row) {
            if (row.queryDefaultValue) {
                row.queryDefaultValue = '';
            }
            row.isGroup = '0';
        },
        // 获取数据源
        async queryDataSource(){
          try {
            let res = this.$api.dataSourceApi.queryDataSourceList();
            let resp = await this.$app.blockingApp(res)
            if (resp.success) {
              this.dataSourceList = resp.data;
            }
          }catch (e) {
            this.$msg.error(e);
          }
        },
        handleDbChange(value){
          let dataSource = this.dataSourceList.filter(item=>{
            return item.id === value
          })
          if(dataSource[0]?.dsType === '1'){
            this.sourceText = '单源'
          }else if (dataSource[0]?.dsType === '2'){
            this.sourceText = '多源'
          }
        },
        async queryFields(pkId) {
            if (pkId !== null && pkId !== '') {
                this.form.dopReCustomQueryFields = [];
                const p = this.$api.customQueryApi.queryFieldList(pkId);
                const resp = await this.$app.blockingApp(p);
                if (resp && resp.data) {
                    this.form.dopReCustomQueryFields = lodash.clone(resp.data);
                }
            }
        },
        async queryExtedList(pkId) {
            if (pkId !== null && pkId !== '') {
                this.form.dopReCustomQueryOperations = [];
                const p = this.$api.customQueryApi.queryExtedList(pkId);
                const resp = await this.$app.blockingApp(p);
                if (resp && resp.data) {
                    this.form.dopReCustomQueryOperations = lodash.clone(resp.data);
                }
            }
        },
        // 拓展操作
        addOptions() {
            this.form.dopReCustomQueryOperations.push({
                buttonName: '',
                requestUrl: '',
                roleIdList: this.checkRoles,
                remark: '',
            })
        },
        async deleteOption(index) {
            const ok = await this.$msg.ask('是否删除选中行')
            if(ok) {
                this.form.dopReCustomQueryOperations.splice(index, 1);
            }
        },
        async onSave() {
            const ok = await this.$refs['form'].validate();
            if (!ok) {
                return;
            }
            let validate = true;
            if (this.form.dopReCustomQueryFields) {
                for (let i = 0; i < this.form.dopReCustomQueryFields.length; i++) {
                    if (this.form.dopReCustomQueryFields[i].queryFieldName === '') {
                        validate = false;
                        break;
                    }
                }
            }
            if (!validate) {
                this.$msg.warning('请补充完整参数表格!');
                return;
            }
            try {
                const p = this.$api.customQueryApi.save(this.form);
                await this.$app.blockingApp(p);
                if (this.actionOk) {
                    await this.actionOk(this.form);
                }
                this.$emit('onClose');
            } catch (e) {
                this.$msg.error(e);
            }
        },
        move(direction) {
            let items = this.$refs.table.selection;
            if (items.length > 1 || items.length === 0) {
                this.$msg.warning('请选择一条记录进行操作!');
                return;
            }
            const item = items[0];
            let index = lodash.clone(item.indexNo);
            if (direction === 'up' && index > 1) {
                const row = this.form.dopReCustomQueryFields[index - 2];
                item.indexNo = index - 1;
                row.indexNo = index;
                this.form.dopReCustomQueryFields[index - 2] = item;
                this.form.dopReCustomQueryFields[index - 1] = row;
            } else if (direction === 'down' && index < this.form.dopReCustomQueryFields.length) {
                const row = this.form.dopReCustomQueryFields[index];
                item.indexNo = index + 1;
                row.indexNo = index;
                this.form.dopReCustomQueryFields[index] = item;
                this.form.dopReCustomQueryFields[index - 1] = row;
            }
        },
        async removeRow() {
            let items = this.$refs.table.selection;
            if (items.length > 1 || items.length === 0) {
                this.$msg.warning('请选择一条记录进行删除!');
                return;
            }
            let index = lodash.clone(items[0].indexNo);
            this.form.dopReCustomQueryFields.splice(index - 1, 1);
            for (let i = lodash.clone(index); i <= this.form.dopReCustomQueryFields.length; i++) {
                this.form.dopReCustomQueryFields[i - 1].indexNo = i;
            }
        },
        async onCancel() {
            this.$emit('onClose');
        }
    }
};
</script>
<style scoped lang="less">
.rule-add-dlg {
    .el-form {
        .el-form-item.option-item {
            margin: 14px 0px;
        }
    }
}
.add-block-target{
  height: 800px !important;
  overflow: auto !important;
}
.form-item-group{
  display: flex;
  justify-content: space-around;
}
.common-error{
  display: inline-block;
  margin-left: 10px;
  margin-top: 5px;
  color: #D90923;
}
.mar10{
  margin-top:-10px;
}
.custom-table ::v-deep  .el-table__header-wrapper{
  height: 40px;
  line-height: 40px;
  display: flex;
  align-items: center;
}
</style>
