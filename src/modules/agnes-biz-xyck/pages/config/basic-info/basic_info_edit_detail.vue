<template>
  <div>
    <el-form ref="form"
      :model="form"
      :rules="rules"
      label-width="100px"
      style="padding: 10px">
      <div class="line">
        <el-form-item label="产品名称" prop="combinationCode">
          <el-select v-model="form.combinationCode" filterable clearable placeholder="请选择" @change="prdCodeOnChange" :disabled="disabled">
            <gf-filter-option v-for="item in productLists" :key="item.productCode"
              :label="`${item.productCode} - ${item.productName}`" :value="item.productCode">
            </gf-filter-option>
          </el-select>
        </el-form-item>
        <el-form-item label="存款类型" prop="depositType">
          <gf-dict-select v-model="form.depositType" dict-type="XYCK_DEPOSIT_TYPE" :disabled="disabled"></gf-dict-select>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="存款行" prop="depositBank">
          <el-input v-model="form.depositBank" maxlength="30" clearable :disabled="disabled"/>
        </el-form-item>
        <el-form-item label="本金(元)" prop="principal">
          <gf-input-money v-model="form.principal" :max-byte-len="16" :readonly="disabled" />
        </el-form-item>
      </div>


      <div class="line">
        <el-form-item label="利率(%)" prop="rate">
          <div style="display: flex;">
          <el-input v-model.trim="form.rate" :disabled="disabled" style="margin-right: 8px;" @input="value => handleInput(value)" clearable></el-input>
          <el-checkbox v-model.trim="isRateCheck" :disabled="disabled" @change="form.depositRates = []">分段</el-checkbox>
        </div>
        </el-form-item>
        <el-form-item label=""></el-form-item>
      </div>

      <div class="line" v-if="isRateCheck">
        <el-form-item label="利息分段" prop="depositRates">
          <el-table :data="form.depositRates"
            header-row-class-name="rule-header-row"
            header-cell-class-name="rule-header-cell"
            row-class-name="rule-row"
            cell-class-name="rule-cell"
            :cell-style="{ 'padding-top': '4px', 'padding-bottom': '4px' }"
            :header-cell-style="{ 'background': '#EFF3FC', 'padding-top': '2px', 'padding-bottom': '2px', color: '#666', 'font-size': '12px', 'font-weight': 'normal' }"
            border>
            <template v-if="!disabled">
              <el-table-column min-width="25%" label="操作">
                <template slot-scope="scope">
                  <el-button type="text" @click="rateOnEdit(scope)" style="color: #0f5eff;">编辑</el-button>
                  <el-button type="text" @click="rateOnDel(scope)" style="color: #0f5eff;">删除</el-button>
                </template>
              </el-table-column>
            </template>
            <el-table-column min-width="25%" prop="effectiveDate" label="生效时间"></el-table-column>
            <el-table-column min-width="25%" prop="expirationDate" label="失效时间"></el-table-column>
            <el-table-column min-width="25%" prop="rateValue" label="利率(%)"></el-table-column>
          </el-table>
          <el-button v-if="!disabled" @click="rateOnAdd" style="margin-top: 5px; padding: 6px 12px;">新增</el-button>
        </el-form-item>
      </div>

      <div class="line">
        <el-form-item label="起息日期" prop="valueDate">
          <el-date-picker v-model="form.valueDate" type="date" value-format="yyyy-MM-dd" :picker-options="startOpts" :disabled="disabled"></el-date-picker>
        </el-form-item>

        <el-form-item label="到期日期" prop="expirationDate">
          <el-date-picker v-model="form.expirationDate" type="date" value-format="yyyy-MM-dd" :picker-options="endOpts" :disabled="disabled"></el-date-picker>
        </el-form-item>
      </div>

      <div class="line">
        <el-form-item label="付息频率" prop="paymentFrequency">
          <gf-dict-select v-model="form.paymentFrequency" dict-type="AGNES_XYCK_FXPL" :disabled="disabled"></gf-dict-select>
        </el-form-item>

        <el-form-item label="首次结息日" prop="firstInterestDate">
          <el-date-picker v-model="form.firstInterestDate" type="date" value-format="yyyy-MM-dd" :disabled="disabled"></el-date-picker>
        </el-form-item>
      </div>

      <el-row>
        <el-col :span="12">
          <el-form-item label="是否提前支取" prop="isAdvanceWithdrawal">
            <gf-dict-select v-model="form.isAdvanceWithdrawal" dict-type="AGNES_XYCK_SFTQZQ" :disabled="disabled"
              @change="() => form.advanceWithdrawalDate = ''"/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="提前支取日期" v-if="form.isAdvanceWithdrawal === '1'" prop="advanceWithdrawalDate">
            <el-date-picker v-model="form.advanceWithdrawalDate" type="date" value-format="yyyy-MM-dd" :disabled="disabled"></el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>

      <div class="line" v-if="form.isAdvanceWithdrawal === '1'">
        <el-form-item label="提前支取说明" prop="advanceWithdrawalDesc">
          <el-input v-model="form.advanceWithdrawalDesc" clearable maxlength="50" :disabled="disabled"/>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" clearable maxlength="200" :disabled="disabled"></el-input>
        </el-form-item>
      </div>

      <div class="line">
        <el-form-item label="存单保管行" prop="depositoryBank">
          <el-select v-model="form.depositoryBank" filterable clearable :disabled="disabled">
            <gf-filter-option v-for="item in depBanks" :key="item.extOrgName" :label="item.extOrgName" :value="item.extOrgName">
            </gf-filter-option>
          </el-select>
        </el-form-item>
        <el-form-item label="存单编号" prop="depositNumber">
          <el-input v-model="form.depositNumber" oninput="value=value.replace(/[^0-9]/g, '');" clearable maxlength="30" :disabled="disabled"/>
        </el-form-item>
      </div>

      <div class="line">
        <el-form-item label="存款账号" prop="depositAccount">
          <el-input v-model="form.depositAccount" clearable maxlength="30" :disabled="disabled"/>
        </el-form-item>
        <el-form-item label="存款账户名称" prop="depositAccountName" :rules="[{ required: true, message: '存款账户名称必填', trigger: 'blur' }]">
          <el-input v-model="form.depositAccountName" clearable maxlength="30" :disabled="disabled"/>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="计息天数" prop="jxgs">
          <gf-dict-select v-model="form.jxgs" dict-type="XYCK_JXTS" :disabled="disabled"/>
        </el-form-item>
        <el-form-item label="预留印鉴(支取)" prop="reserveSealZq">
          <gf-dict-select v-model="form.reserveSealZq" dict-type="XYCK_YLYJ" :disabled="disabled"/>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="预留印鉴(管理)" prop="reserveSealGl">
          <gf-dict-select v-model="form.reserveSealGl" dict-type="XYCK_YLYJ" :disabled="disabled"/>
        </el-form-item>
        <el-form-item label="预留印鉴(对账)" prop="reserveSealDz">
          <gf-dict-select v-model="form.reserveSealDz" dict-type="XYCK_YLYJ" :disabled="disabled"/>
        </el-form-item>
      </div>

      <div class="line">
        <el-form-item label="协议上传" prop="docId">
          <ecm-upload style="width: 94%;"
            :disabled="disabled"
            :applyType="'2'"
            :fileSize="25"
            :src-doc-id="form.docId"
            :showRemove="!disabled"
            :file-list="this.fjList"
            :drag="false"
            :multiple="false">
          </ecm-upload>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="印鉴卡上传" prop="sealCardId">
          <ecm-upload style="width: 94%;"
            :disabled="disabled"
            :applyType="'2'"
            :fileSize="25"
            :src-doc-id="form.sealCardId"
            :showRemove="!disabled"
            :file-list="this.sealCardList"
            :drag="false"
            :multiple="false">
          </ecm-upload>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="存款证实书上传" prop="depositCertificateId">
          <ecm-upload style="width: 94%;"
            :disabled="disabled"
            :applyType="'2'"
            :fileSize="25"
            :src-doc-id="form.depositCertificateId"
            :showRemove="!disabled"
            :file-list="this.depositCertificateList"
            :drag="false"
            :multiple="false">
          </ecm-upload>
        </el-form-item>
      </div>
      <template v-if="mode === 'approval'">
        <div class="line">
          <el-divider></el-divider>
        </div>
        <div class="line">
          <el-form-item label="审核结果" prop="isReviewResults" >
            <el-radio v-model="form.isReviewResults" label="1">通过</el-radio>
            <el-radio v-model="form.isReviewResults" label="0">不通过</el-radio>
          </el-form-item>
        </div>

        <div class="line">
          <el-form-item label="复核意见" prop="reviewOpinions">
            <el-input v-model="form.reviewOpinions"
              type="textarea"
              :maxlength="500"
              resize="none"
              :autosize="{minRows: 2, maxRows: 6}" />
          </el-form-item>
        </div>
      </template>
    </el-form>
  </div>
</template>

<script>
import interestRateDlg from './interest-rate-dlg'
export default {
  props: {
    mode: { type: String, default: 'add' },
    row: { type: Object, default: null },
    actionOk: Function
  },
  data() {
    return {
      productLists: [],
      depBanks: [],
      form: {
        combinationCode: '',
        combinationName: '',
        depositType: '',
        depositBank: '',
        principal: '',
        depositRates: [],

        valueDate: '',
        expirationDate: '',
        paymentFrequency: '',
        firstInterestDate: '', // 首次结息日
        isAdvanceWithdrawal: '', // 是否提前支取
        advanceWithdrawalDate: '' , // 提前支取日期
        advanceWithdrawalDesc: '', // 提前支取说明
        remark: '', // 备注

        depositoryBank: '', // 存单保管行
        depositNumber:'', // 存单编号
        depositAccount:'', // 存款账号
        depositAccountName: '', // 存款账户名称

        jxgs: '', // 计息天数
        reserveSealZq: '', // 预留印鉴(支取)
        reserveSealGl: '', // 预留印鉴(管理)
        reserveSealDz: '', // 预留印鉴(对账)

        docId: '', // 协议上传编号
        sealCardId:'', // 印鉴卡上传编号
        depositCertificateId:'', // 存款证实书上传编号

        isExamine: '', // 审核状态 是否审核
        isReviewResults: '', // 复核结果
        reviewOpinions: '' // 复核意见
      },
      fjList: [],
      sealCardList:[], // 印鉴卡文件列表
      depositCertificateList:[], // 存款证实书列表

      rules: {
        combinationCode: [{ required: true, message: '产品名称必填', trigger: 'change' }],
        depositType: [{ required: true, message: '存款类型必填', trigger: 'change' }],
        depositBank: [{ required: true, message: '存款行必填', trigger: 'blur' }],
        principal: [{required: true, message: '本金必填', trigger: 'blur' }],
        depositRates: [{ required: true, validator: this.rateValidate }],
        valueDate: [{ required: true, message: '起息日期必填', trigger: 'change' }],
        expirationDate: [{ required: true, message: '到期日期必填', trigger: 'change' }],
        paymentFrequency: [{ required: true, message: '付息频率必填', trigger: 'change' }],
        firstInterestDate: [{ required: true, message: '首次结息日必填', trigger: 'change' }],
        isReviewResults:[{ required: true, message: '审核结果必填', trigger: 'change' }],
        rate:[{ required: true, message: '利率必填', trigger: 'change' }]
      },


      isRateCheck: false
    }
  },

  computed: {
    startOpts() {
      return {
        disabledDate: time => {
          if (this.form.expirationDate) {
            return time.getTime() > new Date(this.form.expirationDate.replace(/[-]/g, '/')).getTime() - 86400000
          }
          return false
        }
      }
    },
    endOpts() {
      return {
        disabledDate: time => {
          if (this.form.valueDate) {
            return time.getTime() < new Date(this.form.valueDate.replace(/[-]/g, '/')).getTime() + 86400000
          }
          return false
        }
      }
    },
    disabled() {
      return ['view', 'approval'].includes(this.mode)
    }
  },
  async beforeMount() {
    if (this.mode !== 'add') {
      await this.getDepositRatesList({pkId: this.row.pkId});
      this.isRateCheck= this.form.depositRates&&this.form.depositRates.length>0 ?true:false;
    }
    await this.getProductList();
    if (this.row) {
      this.form = {
        ...this.form,
        ...this.row
      }
    }
    const resp = await this.$api.basicInfoApi.getOrgByOrgType();
    if (resp.success) {
      if (Array.isArray(resp.data) && resp.data.length >0) {
        this.depBanks = resp.data;
      }
    }
    if (this.mode === 'approval') {
      this.form = {
        ...this.form,
        isReviewResults: '1'
      }
    }
    console.log(this.gird)
    console.log(this.gird2)
    this.getProductList();
    this.getCklx();
  },

  // 监听事件 获取docId
  watch: {
    'fjList': {
      handler(o, n) {
        if (n[0]) {
          this.form.docId = n[0].docId;
        }
      }
    },
    'sealCardList': {
      handler(o, n) {
        if (n[0]) {
          this.form.sealCardId = n[0].docId;
        }
      }
    },
    'depositCertificateList': {
      handler(o, n) {
        if (n[0]) {
          this.form.depositCertificateId = n[0].docId;
        }
      }
    }
  },

  methods: {
    rateValidate(rule, value, callback) {
      if (Array.isArray(value) && value.length >0) {
        callback();
        return;
      }
      callback(new Error('利率必填'));
    },

    handleInput (val) {
      let value = val + '';

      value = value.replace(/[^\d.]/g, '');
      value = value.replace(/\.{2,}/g, '.');
      value = value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');

      let reg = new RegExp('^(\\d+)\\.(\\d{6}).*$')
      value = value.replace(reg, '$1.$2');
      if (value.indexOf('.') < 0 && value !== '' && value !== null) {
        value = parseFloat(value)
      }

      if (!isNaN(value) && value !== '' && value !== null) {
        if (parseFloat(value) > 100) {
          value = 100
        }
      }
      this.$set(this.form, 'rate', value);
    },


    async getProductList(){
      this.productLists = [];
      const resp = await this.$api.productInfoApi.getProductListForSelect();
      if (Array.isArray(resp) && resp.length >0) {
        this.productLists = resp.map(item => ({ productCode: item.id, productName: item.label }))
      }
    },
    prdCodeOnChange(val) {
      if (val) {
        let c = this.productLists.find(item => item.productCode === val);
        this.form = {
          ...this.form,
          combinationName: c ? c.productName : ''
        }
      } else {
        this.form = {
          ...this.form,
          combinationName: ''
        }
      }
    },

    async getDepositRatesList({pkId}){
      const resp = await this.$api.basicInfoApi.getRates({pkId});
      if (resp.success && resp.data.length >0) {
       this.form.depositRates = resp.data;
      }
    },

    rateOnAdd() {
      this.$nav.showDialog(interestRateDlg, {
        args: {},
        width: '600px',
        title: `利率新增`,
        beforeClose: (args, action) => {
          if (action) {
            this.form.depositRates.push(JSON.parse(action));
          }
        }
      })
    },

    rateOnEdit (scope) {
      this.$nav.showDialog(interestRateDlg, {
        args: { row: scope.row },
        width: '600px',
        title: `利率修改`,
        beforeClose: (args, action) => {
          if (action) {
            this.form.depositRates.splice(scope.$index, 1, JSON.parse(action));
          }
        }
      })
    },
    rateOnDel (scope) {
      this.form.depositRates.splice(scope.$index, 1);
    },

    checkFormData(form) {
      let firstInterestDate = new Date(form.firstInterestDate).getTime();
      let valueDate = new Date(form.valueDate).getTime();
      let expirationDate = new Date(form.expirationDate).getTime();
      let check = true;
      if(firstInterestDate){
        if ( firstInterestDate < valueDate) {
          this.$msg.error('首次结息日不能小于起息日期');
          check = false;
          return check;
        }
      }

      if (valueDate > expirationDate) {
        this.$msg.error('起息日期不能大于到息日期');
        check = false;
        return check;
      }
      if (firstInterestDate > expirationDate) {
        this.$msg.error('首次结息日不能大于到息日期');
        check = false;
        return check;
      }
      if (firstInterestDate === expirationDate && expirationDate === valueDate) {
        this.$msg.error('起息日期、首次结息日、到息日期不能相等');
        check = false;
        return check;
      }
      return check;
    },
    // 取消onCancel事件，触发抽屉关闭事件this.$emit("onClose");
    async onCancel() {
      this.$emit("onClose");
    },
    async onSave() {
      try {
        const ok = await this.$refs['form'].validate();
        if (!ok) {
          return;
        }
        if (this.mode === 'edit'){
          const p1 = this.$api.basicInfoApi.saveBasicInfo(this.form);
          this.$api.basicInfoApi.regeneration(this.form);
          let resp1 = await this.$app.blockingApp(p1);
          if (resp1.code === 'error') {
            this.$msg.warning(resp1.message);
            return;
          }
        }
        if (this.mode === 'approval' && this.form.isReviewResults === '1') {
          const resp2 = this.$api.basicInfoApi.regeneration(this.form);
          await this.$app.blockingApp(resp2);
          if(this.gird2){
            this.gird2.reloadData();
          }
          if (resp2.code === 'error') {
            this.$msg.warning(resp2.message);
            return;
          }
        }
        this.$msg.success('保存成功');
        this.$emit("onClose");
        if(this.gird){
          this.gird.reloadData();
        }
      } catch (reason) {
        this.$msg.error(reason);
      }
    },
    //是否移交
    isDisplay() {
      this.isShow = this.form.transferPurchase === '移交';
      return this.isShow;
    },


    getCklx() {
      this.cklx =  this.selectByDict("XYCK_DEPOSIT_TYPE")
    },

    selectByDict(param) {
      return this.$app.dict
          .getDictItems(param)
          .map((dictItem) => {
            return {
              id: dictItem.dictId,
              label: dictItem.dictName,
            };
          });
    },
  }

}
</script>