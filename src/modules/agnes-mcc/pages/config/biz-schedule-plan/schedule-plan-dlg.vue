<template>
  <div v-loading="loading">
    
    <span v-if="mode === 'audit'" v-html="text"></span>
    <el-form class="fit-box" :disabled="mode==='audit' || mode === 'view'" :model="form" ref="form" :rules="rules" label-width="70px" style="padding: 10px;">
      <el-row>
        <el-col :span="6">
          <el-form-item label="方案名称" prop="planName" style="width: 100%">
            <el-input type="text" placeholder="请输入方案名称" v-model="form.planName"/>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="固定循环天数" prop="cycleDays" style="width: 100%" label-width="90px">
            <gf-input clear-regex="[^0-9]" v-model="form.cycleDays" placeholder="请输入固定循环天数"></gf-input>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="生效时间" prop="startDate" style="width: 100%">
            <el-date-picker
              v-model="form.startDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :picker-options="startDateOptions"
              placeholder="请选择生效时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="结束时间" prop="endDate" style="width: 100%">
            <el-date-picker
              v-model="form.endDate"
              type="date"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              :picker-options="endDateOptions"
              placeholder="请选择结束时间">
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
      <div style="max-height: 600px;overflow-y: auto">
        <div class="cycle-box" v-for="(item,index) in form.detailList" :key="index">
          <div class="cycle-title">
            <el-row style="padding-top: 5px;">
                  <el-col :span="$hasPermission('agnes.biz.schdule.plan.quickly') ? 8 : 12">
                    <el-form-item label="岗位名称" :prop="'detailList.' + index + '.postId'" style="width: 100%" :rules="getValidationRules()">
                        <el-select v-model="item.postId" clearable filterable placeholder="请选择" @change="handlePostIdChange(index)">
                          <gf-filter-option v-for="item in gwName" :key="item.userGroupId"
                              :label="item.userGroupName" :value="item.userGroupId">
                          </gf-filter-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="$hasPermission('agnes.biz.schdule.plan.quickly') ? 10 : 12">
                    <el-form-item label="日期类型" :prop="'detailList.' + index + '.workDay'" style="width: 100%" :rules="getValidationRules()">
                      <el-radio-group v-model="item.workDay">
                        <el-radio :label="'1'">工作日</el-radio>
                        <el-radio :label="'0'">自然日</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
            </el-row>
            <div>
              <span class="add-span" size="mini" @click="onPreview(index)">预览</span>
              <span v-if="index > 0" class="add-span" @click="delCycle(index)" size="mini">删除</span>
            </div>
          </div>
          <div class="table-box">
              <div class="big-cycle">
                  <span class="cycle-day">&nbsp;&nbsp;周期共 {{getNum(index)}} 天</span>
                  <el-table :data="item.cycleInfoRes" :size="'mini'" border :header-cell-style="headerCellStyle">
                      <el-table-column label="" prop="" width="40">
                          <template v-slot:default="{ $index }">
                            <i v-if="$index > 2" class="el-icon-remove-outline" style="font-size: 16px;cursor: pointer;" @click="deleteRow('big',index, $index)"></i>
                          </template>
                      </el-table-column>
                      <el-table-column label="班次" prop="schedule" width="110">
                          <template v-slot:default="{row,$index}">
                              <template v-if="$index < 2">
                                  <span>{{ $index === 0 ? '值班天数' : '值班顺序' }}</span>
                              </template>
                              <template v-else>
                                  <el-select clearable v-model="row.schedule" @change="validateSchedule('big',index,$index,row.schedule)">
                                      <el-option v-for="item in scheduleList" :key="item.dictId" :label="item.dictName" :value="item.dictId">
                                      </el-option>
                                  </el-select>
                              </template>
                          </template>
                      </el-table-column>
                      <el-table-column v-for="(perItem, perIndex) in item.columns" :key="perIndex" :prop="perItem.value" :label="perItem.name" width="170">
                        <template #header="{ $index }">
                          <div class="person-header">
                            <span>人员</span>
                            <i class="el-icon-remove-outline" style="font-size: 16px;cursor: pointer;" @click="delColumn('big',index, $index)"></i>
                          </div>     
                        </template>
                        <template #default="{ row, $index }">
                          <template v-if="$index === 0">
                            <gf-input v-model="row.person[perIndex]" clear-regex="[^0-9]" :max-byte-len="2" :class="{ 'person-num': getRowStyle(row.person[perIndex]) }"></gf-input>
                          </template>
                          <template v-else-if="$index === 1">
                            {{ perIndex + 1 }}
                          </template>
                          <template v-else>
                                  <el-select clearable filterable multiple collapse-tags v-model="row.person[perIndex]">
                                      <el-option v-for="item in item.perList" :key="item.userId"
                                          :label="item.userName" :value="item.userId">
                                      </el-option>
                                  </el-select>
                          </template>
                        </template>
                      </el-table-column>

                  </el-table>
                  <gf-button class="add-btn" @click="addRowlist('big',index)" size="mini">+ 新增行</gf-button>
                  <gf-button class="add-btn" @click="addColumnlist('big',index)" size="mini">+ 新增列</gf-button>
              </div>
              <div class="small-cycle" v-if="hasReturnInPerList(index)">
                <span class="cycle-day">&nbsp;&nbsp;小周期共 {{ item.childColumns.length }} 天</span>
                <span style="margin-left: 20px">小周期设置</span>
                    <el-table :data="item.childCycleInfoRes" :size="'mini'" border :header-cell-style="headerCellStyle">
                      <el-table-column label="" prop="" width="40">
                          <template v-slot:default="{ $index }">
                            <i v-if="$index > 1" class="el-icon-remove-outline" style="font-size: 16px;cursor: pointer;" @click="deleteRow('small',index, $index)"></i>
                          </template>
                      </el-table-column>
                      <el-table-column label="班次" prop="schedule" width="110">
                          <template v-slot:default="{row,$index}">
                              <template v-if="$index === 0">
                                  <span>轮班顺序</span>
                              </template>
                              <template v-else>
                                  <el-select v-model="row.schedule" @change="validateSchedule('small',index,$index,row.schedule)">
                                      <el-option v-for="item in scheduleList" :key="item.dictId"
                                          :label="item.dictName" :value="item.dictId">
                                      </el-option>
                                  </el-select>
                              </template>
                          </template>
                      </el-table-column>
                      <el-table-column v-for="(perItem, perIndex) in item.childColumns" :key="perIndex" :prop="perItem.value" :label="perItem.name" width="170">
                        <template #header="{ $index }">
                          <div class="person-header">
                            <span>人员</span>
                            <i class="el-icon-remove-outline" style="font-size: 16px;cursor: pointer;" @click="delColumn('small',index, $index)"></i>
                          </div>     
                        </template>
                        <template #default="{ row, $index }">
                          <template v-if="$index === 0">
                            {{ perIndex + 1 }}
                          </template>
                          <template v-else>
                                  <el-select clearable filterable multiple collapse-tags v-model="row.person[perIndex]">
                                      <el-option v-for="item in item.smallPerList" :key="item.userId"
                                          :label="item.userName" :value="item.userId">
                                      </el-option>
                                  </el-select>
                          </template>
                        </template>
                      </el-table-column>

                  </el-table>
                  <gf-button class="add-btn" @click="addRowlist('small',index)" size="mini">+ 新增行</gf-button>
                  <gf-button class="add-btn" @click="addColumnlist('small',index)" size="mini">+ 新增列</gf-button>
              </div>
          </div>
        </div>
      </div>
    </el-form>
    <gf-button v-if="mode !== 'audit' && mode != 'view'" type="primary" @click="addSchedulePlan(index)" style="width:100px">+ 新增</gf-button>
    <dialog-footer :ok-button-visible="mode !== 'view'" :on-save="onSave" :ok-button-title="mode==='audit'? '审核': '保存'"></dialog-footer>
  </div>
</template>

<script>
    import PreviewDlg from "./preview-dlg.vue";
    import lodash from 'lodash'
    export default {
        name: "schedule-plan-dlg",
        props: {
            mode: {
                type: String,
                default: 'add'
            },
            row: Object,
            actionOk: Function
        },
        data() {
            return {
              text: '',
              loading: false,
              header: '',
              typeArr: [
                {id: '1', label: '人员'},
                {id: '2', label: '轮值'},
              ],
              scheduleList: this.$app.dict.getDictItems("AGNES_ROSTER_TYPE"),
              gwName:[], //岗位名称
              form: {
                id: '',
                planName: '',
                cycleDays: '',
                startDate: window.bizDate,
                endDate: '',
                detailList: [ //表示排班方案，对应每个卡片
                    {
                      disabled: true, //快速生成晚班兜底班按钮禁用状态，默认禁用
                      postId:'',  //岗位ID
                      perList: [], //人员列表
                      smallPerList: [], //人员列表
                      workDay:'1', //日期类型，0-自然日，1-工作日
                      columns: [],  //人员列 （大周期）
                      cycleInfoRes: [
                        {person: [],schedule: '',},
                        {person: [],schedule: '',},
                        {person: [],schedule: '',},
                      ],  //排班信息 （大周期）
                      childColumns:[], //人员列 （小周期）
                      childCycleInfoRes:[
                        {person: [],schedule: '',},
                        {person: [],schedule: '',},
                      ], //小周期
                    },
                ],
              },
              allUserMap: new Map(),
              memberRefList: [],
              rosterDate: '',
              rules: {
                    'planName': [{required: true, message: "请输入方案名称", trigger: 'blur'}],
                    'startDate': [{required: true, message: "请选择生效时间", trigger: 'blur'}],
                    'endDate': [{required: true, message: "请选择结束时间", trigger: 'blur'}],
                },
              startDateOptions: {
                disabledDate: time => {
                  if (this.form.endDate) {
                    return time.getTime() > new Date(this.form.endDate).getTime();
                  } else {
                    return false;
                  }
                }
              },
              endDateOptions: {
                disabledDate: time => {
                  if (this.form.startDate) {
                    return time.getTime() < new Date(this.form.startDate).getTime();
                  } else {
                    return false;
                  }
                }
              },
            }
        },
        async mounted() {
          this.loading = true;
          await this.getAllUserMap();
          if(this.mode !== 'add'){
            const p = await this.$api.BizSchedulePlanApi.getById(this.row.id);
            p.data = JSON.parse(p.data)
            if (this.mode === 'copy') {
              p.data.id = '';
              p.data.planName = p.data.planName + '_2';
              
            }
            for (let item  of p.data.detailList) {
              let postId = item.postId;
              const userRes = await this.$api.bizPostConfigApi.listUserByPostId({userGroupId: postId});
              
              let perList = userRes.data?.rows;
              item.smallPerList = [];
              item.smallPerList.push(...userRes.data?.rows);
              item.perList = [];
              item.perList.push(...perList);
              item.perList.splice(0, 0, {userId: "ROTATE", userName: "轮值"});
              this.loadUser(item)
            }
            this.form = p.data;
            if (this.mode ==='audit') {
              this.handleModeAduit();
            }
          }
          const post = await this.getPostList();
          this.gwName = post.data?.rows;
          this.loading = false;
        },
        methods: {
          // mode为aduit时，显示操作信息
          handleModeAduit() {
            let title
            if (this.row.flag === '1') {
              title = '新增';
            } else if (this.row.flag === '2') {
              title = '修改';
            } else if (this.row.flag === '3') {
              title = '删除';
            } else {
              title = '日期调整';
            }
            this.text = `&nbsp;&nbsp;操作行为：<strong>${title}</strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;操作员：<strong>${this.row.updateUser}</strong>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;操作时间：</strong>${this.row.updateTs}<strong>`;
          },
          loadUser(detail) {
            let userIdList = detail.perList.map(p => p.userId);
            for(let i = 2; i < detail.cycleInfoRes?.length; i++) {
              let columnList = detail.cycleInfoRes[i].person;
              columnList.forEach((column, cIdx) => {
                let idxArr = [];
                let filteredColumn = column.filter((value, j) => {
                   return this.handleFilter(userIdList,value,column,j,idxArr);
                });
                if(idxArr.length > 0){
                  columnList[cIdx] = filteredColumn;
                }
              
              });
            }
          },
          // 处理过滤逻辑
          handleFilter(userIdList,value,column,j,idxArr){
              if (userIdList.includes(value)) {
                return true;
              }
              //复制、编辑、审核操作清除掉已不在岗位下的用户
              let modeList = ['copy', 'edit', 'audit'];
              if (modeList.includes(this.mode)) {
                idxArr.push(j);
                return false;
              }

              if (this.allUserMap.size > 0 && this.allUserMap.has(value)) {
                column[j] = this.allUserMap.get(value);
                return true;
              }

              return false;
          },
          async getAllUserMap() {
            const resp = await this.$api.bizOrgFrameworkApi.getAllUser({});
            if (resp.success) {
              resp.data?.forEach(user => {
                this.allUserMap.set(user.userId, user.userName);
              })
            }
          },
          async createData(index) {
            //校验
            let data = this.form.detailList[index];
            if (data.cycleInfoRes.length < 3 || data.cycleInfoRes[2].schedule != 'ZB' || !data.columns) {
              this.$message.error("当前没有维护早班排班，请先维护！");
              return;
            }
            if (data.cycleInfoRes[0].person.some(i => i != 1)) {
              this.$message.error("值班天数必须为1，请检查！");
              return;
            }
            let zb = data.cycleInfoRes[2].person;
            if (zb.some(p => p.length != 1)) {
              this.$message.error("早班每列必须有且只有一个值班人数据，请检查！");
              return;
            }
            if (data.cycleInfoRes.length > 3) {
              const ok = await this.$msg.ask("是否覆盖已经存在的排班数据？");
              if (!ok) return;
            }
            //早班往左移一位生成早走班
            let zzb = zb.filter(i => i.indexOf("ROTATE") < 0).slice(1, zb.length);
            let rotateIdxArr = [];
            for (let i=0; i<zb.length; i++) {
              if (zb[i].indexOf("ROTATE") >= 0) {
                rotateIdxArr.push(i);
              }
            }
            zzb.push(zb[0]);
            //早班值班人去重
            let zbPerson = []; 
            zzb.forEach(arr => {
              if (zbPerson.indexOf(arr[0]) < 0) {
                zbPerson.push(arr[0]);
              }
            })
            //当前岗位下所有人，剔除'轮值'
            let perList = data.perList.slice(1, data.perList.length).map(i => i.userId);
            let wb = [];
            let ddb = [];
            zzb.forEach(p => {
              let userId = p[0];
              //生成晚班：岗位下除了当天值早班的所有人
              let w = perList.filter(i => i != userId);
              wb.push(w);
              //生成兜底，早走班除了当天值班人的下一值班人
              let dd = [zbPerson[(zbPerson.indexOf(userId) + 1) % zbPerson.length]]
              ddb.push(dd);
            });
            let newCycle = data.cycleInfoRes.slice(0, 3);
            rotateIdxArr.forEach(idx => {
              wb.splice(idx, 0, [])
              ddb.splice(idx, 0, [])
            })
            newCycle.push({
              person: wb,
              schedule: 'WB',
            });
            newCycle.push({
              person: ddb,
              schedule: 'DD',
            });
            data.cycleInfoRes = newCycle;
          },
          async getPostList() {
            let result;
            if (this.mode === 'view') {
              const res = await this.$api.bizPostConfigApi.listAll({status: '1'});
              result = res;
            } else {
              const orgRes = await this.$api.bizOrgFrameworkApi.listOrgByUserId({userId: this.$app.session.data.user.userId});
              let orgList = orgRes.data.map(i => i.orgId);
              const centerOrg = await this.$api.bizOrgFrameworkApi.selectParentOrgByType(orgList);
              let bizCenterArr = centerOrg.data?.map(org => {
                return {id: org.id, label: org.orgName};
              }) || [];
              
              const postRes = await this.$api.bizPostConfigApi.getPostByCenterOrgId({orgIdList: bizCenterArr.map(org => org.id), status: '1'})
              result = postRes;
            }
            return result;
          },
          async handlePostIdChange(index) {
            let postId = this.form.detailList[index].postId;
            let currentPost = this.gwName.find(p1 => p1.userGroupId === postId);
            this.form.detailList[index].disabled = !(currentPost && currentPost.isProductTask === '1');
            const p = await this.$api.bizPostConfigApi.listUserByPostId({userGroupId: postId});
            let perList = p.data.rows;
            this.form.detailList[index].smallPerList = [];
            this.form.detailList[index].smallPerList.push(...p.data.rows);
            perList.splice(0, 0, {userId: "ROTATE", userName: "轮值"});
            this.form.detailList[index].perList = perList;
          },
          async onPreview(index) {
            let data = lodash.clone(this.form);
            data.detailList = data.detailList[index];
            const param = {
              previewData: data
            };
            this.$nav.showDialog(
              PreviewDlg,
              {
                  width: '70%',
                  title: "排班预览",
                  closeOnClickModal: false,
                  args: param,
                  className:'none-paddingTop'
              }
            );
          },
          getValidationRules() {
              return [
                {
                  required: true,
                  message: '该字段为必填项',
                  trigger: ['blur', 'change'],
                },
              ];
          },
            validateSchedule(type,index,schIndex,schedule) {
              if(schedule !== ""){
                let cycleInfoRes =  this.handleSchedule(type,index,schIndex,schedule);
                let hasDuplicate = false;
                for (const res of cycleInfoRes) {
                  if (res.schedule === schedule) {
                    hasDuplicate = true;
                    break;
                  }
                }
                if (hasDuplicate) {
                  this.$message.warning('该轮班已存在!')
                } else {
                  cycleInfoRes[schIndex].schedule = schedule;
                }
              }
            },
            // 处理卡片和岗位的关系
            handleSchedule(type,index,schIndex,schedule){
              const cycleInfoRes = type === 'big' ? this.form.detailList[index].cycleInfoRes : this.form.detailList[index].childCycleInfoRes;
                cycleInfoRes[schIndex].schedule = "";
                let postId = this.form.detailList[index].postId
                for (let i=0; i<this.form.detailList.length; i++) {
                  if (i == index) continue;
                  let cardData = this.form.detailList[i];
                  if (postId === cardData.postId) { //判断不同卡片相同岗位不能有重复排班
                     for(const item of cardData.cycleInfoRes) {
                        if (item.schedule === schedule) {
                          this.$message.warning('该轮班已存在!')
                          return;
                        }
                     }
                  }
                }
                return cycleInfoRes
            },
            getNum(index) {
              const person = this.form.detailList[index].cycleInfoRes[0].person;
              // 过滤掉空字符串并将元素转换为整数
              const filteredPerson = person.filter(item => item !== '').map(item => parseInt(item));
              // 如果过滤后的数组为空，返回默认值 0
              if (filteredPerson.length === 0) {
                return 0;
              }
              return filteredPerson.reduce((acc, curr) => acc + curr, 0);
            },
            // 判断是否存在（轮班）
            hasReturnInPerList(index) {
              const item = this.form.detailList[index];
              if (!item || !item.cycleInfoRes) return false;

              // 使用 flatMap 展平 person 数组，然后使用 some 检查是否包含 'ROTATE'
              return item.cycleInfoRes.some(res => 
                res.person.flatMap(personArray => Array.isArray(personArray) ? personArray : []).includes('ROTATE')
              );
            },
            getRowStyle(value){
                return value >= 10
           
            },
            headerCellStyle() {
              return{ 
                background: '#F0F1F2',
                borderBottom: '1px dashed #D9D9D9'
              }
            },
            // 添加新的排班方案
            addSchedulePlan(){
              const newDetail = {
                disabled: true,
                postId:'',  //岗位ID
                workDay:'1', //日期类型，0-自然日，1-工作日
                columns: [],
                cycleInfoRes: [
                    {person: [],schedule: '',},
                    {person: [],schedule: '',},
                    {person: [],schedule: '',},
                ],
                childColumns:[], //人员列 （小周期）
                childCycleInfoRes:[
                  {person: [],schedule: '',},
                  {person: [],schedule: '',},
                ], //小周期
              }
              this.form.detailList.push(newDetail);
            },
            // 删除新的排班方案
            delCycle(index){
              this.form.detailList.splice(index, 1);
            },
            // 新增行
            addRowlist(type,index){
              const newTask = { schedule: '', person: []};
              if(type === 'big'){
                this.form.detailList[index].cycleInfoRes.push(newTask);
              }else{
                this.form.detailList[index].childCycleInfoRes.push(newTask);
              }
            },
            // 删除行
            deleteRow(type,listIndex,index){
              if(type === 'big'){
                this.form.detailList[listIndex].cycleInfoRes.splice(index, 1);
              }else{
                this.form.detailList[listIndex].childCycleInfoRes.splice(index, 1);
              }  
            },
            // 添加列
            addColumnlist(type,index) {
              let newColumn = {
                value: `user`,
                name: `人员`
              };
              if(type === 'big'){
                const newColumns = [...this.form.detailList[index].columns, newColumn];
                this.form.detailList[index].columns = newColumns;
                this.form.detailList[index].cycleInfoRes[0].person.push(1);
              }else{
                const newColumns = [...this.form.detailList[index].childColumns, newColumn];
                this.form.detailList[index].childColumns = newColumns;
              }

            },
            // 删除列
            delColumn(type, listIndex, index) {
              const deleteIndex = index - 2;
              const columns = type === 'big' ? this.form.detailList[listIndex].columns : this.form.detailList[listIndex].childColumns;
              const cycleInfoRes = type === 'big' ? this.form.detailList[listIndex].cycleInfoRes : this.form.detailList[listIndex].childCycleInfoRes;
              if (columns.length > deleteIndex) {
                columns.splice(deleteIndex, 1);
              }
              cycleInfoRes.forEach(item => {
                if (item.person.length > deleteIndex) {
                  item.person.splice(deleteIndex, 1);
                }
              });
            },
          // 校验数据
          async validateData() {
            const ok = await this.$refs['form'].validate();
            if (!ok) return false;

            try {
              // 循环表格数据检验是否存在必填未填写
              this.form.detailList.forEach((item) => {
                let hasRotate = false;
                //选了轮值的班次
                let rosterSet = new Set();
                if (item.columns.length === 0) {
                  throw new Error('请至少选择一列人员');
                } else {
                  hasRotate =this.validateColumns(item,rosterSet,hasRotate);
                }
                 // 检验周期数据
                 this.validateCycleInfoRes(hasRotate,item, rosterSet)
              });
              return true
            } catch (error) {
              this.$msg.error(error.message);
              throw new Error(error.message);
            }
          },
          // 检验周期数据
          validateCycleInfoRes(hasRotate,item, rosterSet){
            if (!hasRotate) { //不包含轮值，直接把小周期数据置空
                item.childColumns = [];
                item.childCycleInfoRes = [
                  { person: [], schedule: '', },
                  { person: [], schedule: '', },
                ];
              } else { //校验小周期
                  this.validateChildCycleInfoRes(item,rosterSet);
              }
          },
          // 校验小周期数据
          validateChildCycleInfoRes(item,rosterSet){
            if (item.childColumns.length === 0) {
              throw new Error(`请先完善小周期设置！`);
            } else {
              if (item.childCycleInfoRes[1].person.length < 2) {
                throw new Error(`小周期大于2列人员才有轮值意义！`);
              }
              let rosterTypeSet = new Set(); //小周期班次
              for (let i = 1; i < item.childCycleInfoRes.length; i++) {
                rosterTypeSet.add(item.childCycleInfoRes[i].schedule);
                if (item.childCycleInfoRes[i].person.some(personArray => personArray.length === 0) ||
                  item.childCycleInfoRes[i].schedule == "") {
                  throw new Error(`小周期第 ${i + 1} 行存在必填项未填写`);
                }
              }
              //找出小周期中未维护的需要轮值的班次
               this.handleSmallStand(rosterSet,rosterTypeSet);
            }
          },
             //找出小周期中未维护的需要轮值的班次
          handleSmallStand(rosterSet,rosterTypeSet){
            let arr = [...rosterSet].filter(r => !rosterTypeSet.has(r));
              if (arr.length > 0) {
                let msgList = [];
                this.scheduleList.forEach(dict => {
                  if (arr.indexOf(dict.dictId) >= 0) {
                    msgList.add(dict.dictName);
                  }
                });
                throw new Error(`${msgList.join(",")}需要维护小周期数据！`);
              }
          },
          // 校验列数据
          validateColumns(item,rosterSet,hasRotate){
            let flag = hasRotate;
            if (item.cycleInfoRes[0].person.length !== item.columns.length) {
              throw new Error('存在值班天数未填写');
            }
            for (let i = 2; i < item.cycleInfoRes.length; i++) {
              if (item.cycleInfoRes[i].person.some(personArray => personArray.length === 0) ||
                item.cycleInfoRes[i].schedule == "") {
                throw new Error(`第 ${i + 1} 行存在必填项未填写`);
              }
              if (item.cycleInfoRes[i].person.some(p => p.indexOf("ROTATE") >= 0)) {
                rosterSet.add(item.cycleInfoRes[i].schedule);
                flag = true;
              }
            }
            return flag;
          },
            async onSave() {
              let ok = await this.validateData();
              console.log(ok, 'ok');
              
              if (!ok) {
                  return;
              }
              try {
                this.form.flag = this.row?.flag || '1';
                let p;
                switch (this.mode) {
                  case 'add':
                  case 'copy':
                    p = this.$api.BizSchedulePlanApi.add(this.form);
                    break;
                  case 'edit':
                    p = this.$api.BizSchedulePlanApi.edit(this.form);
                    break;
                  default:
                    p = this.$api.BizSchedulePlanApi.audit(this.form);
                    break;
                }
                let res = await this.$app.blockingApp(p);
                if (res.code === "00000000") {
                  if (res.data) {
                    this.$msg.warning(res.data);
                  } else {
                    let msg
                    if(this.mode === 'add'){
                      msg = '保存成功'
                    } else if(this.mode === 'audit'){
                      msg = '审核成功'
                    } else{
                      msg = '编辑成功'
                    }
                    this.$msg.success(msg);
                  }
                } else {
                  this.$msg.error(res.message);
                  return;
                }
                if (this.actionOk) {
                  await this.actionOk();
                }
                this.$dialog.close(this);
              } catch (reason) {
                  this.$msg.error(reason);
              }
            },
        }
    }
</script>

<style scoped>
.add-btn {
    color: #0f5eff;
    font-size: 12px;
    margin: 20px 10px;
    border: 1px solid #0f5eff;
    border-radius: 4px;
    background: transparent;
    padding: 6px 10px;
}
  
.add-span {
    position: relative;
    color: #0f5eff;
    font-size: 12px;
    margin: 20px 10px;
    padding: 6px 10px;
    cursor: pointer;
    top: -8px;
}

.cycle-box{
  width: 100%;
  border-radius: 4px;
  overflow-y: auto;
  margin-bottom: 20px;
  border: 1px solid #cccccc;
}

.cycle-title{
    display: flex;
    height: 65px;
    justify-content: space-between;
    align-items: center;
    margin-bottom: -15px;
}

.person-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.del-row{
  color: #f84646;
  cursor: pointer;
}

.person-num ::v-deep .el-input__inner{
  color: #ff0000 !important;
}

.form-item {
  display: flex;
  align-items: center;
}

.cycle-day{
  color: #ccc;
  font-size: 12px;
}
</style>
