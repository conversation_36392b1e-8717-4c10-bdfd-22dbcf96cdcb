<template>
  <div class="flex-column">
    <div class="task-query">
      <el-form
          ref="form"
          label-position="right"
          :model="queryArgs"
          label-width="100px"
          size="mini">
        <el-row :gutter="24">
          <el-col :span="6">
            <el-form-item label="产品名称:">
              <virtual-select 
                placeholder="请选择产品名称"
                collapse-tags
                clearable
                searchable
                filterable
                v-model="queryArgs.productName" 
                :options="prdtCode.map(_=>({value:_.productCode, label:_.productName+'-'+_.productCode}))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="主岗人:">
              <select-picker 
                placeholder="请选择主岗人"
                collapse-tags
                clearable
                searchable
                filterable
                v-model="queryArgs.zhugangren" 
                :options="attUser.map(_=>({value:_.userId, label:_.userName}))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="备岗人:">
              <select-picker 
                placeholder="请选择备岗人"
                collapse-tags
                clearable
                searchable
                filterable
                v-model="queryArgs.beigangren" 
                :options="attUser.map(_=>({value:_.userId, label:_.userName}))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="!collapsed">
            <el-form-item label="分工方案:">
              <select-picker 
                placeholder="请选择分工方案"
                collapse-tags
                clearable
                searchable
                filterable
                v-model="queryArgs.divideWork" 
                :options="DivideDate"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="24">
          <el-col :span="6" v-if="!collapsed">
            <el-form-item label="产品状态:">
              <select-picker 
                placeholder="请选择产品状态"
                collapse-tags
                clearable
                searchable
                filterable
                v-model="queryArgs.productStatus" 
                :options="dictProductStatus.map(_=>({value:_.dictId, label:_.dictName}))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="!collapsed">
            <el-form-item label="产品种类:">
              <select-picker 
                placeholder="请选择产品种类"
                collapse-tags
                clearable
                searchable
                filterable
                v-model="queryArgs.assetTypeList" 
                :options="assetTypeDict.map(_=>({value:_.dictId, label:_.dictName}))"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6" v-if="!collapsed">
            <el-form-item label=" ">
              <el-checkbox v-model="queryArgs.allParam">全部</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="collapsed ? 6 : 5" class="button-col">
            <div :class="['button-container', collapsed ? 'button-fold' : 'button-not-fold']">
              <gf-button @click="getQueryData()" class="option-btn" type="primary">查询</gf-button>
              <gf-button @click="reSetSearch()" class="option-btn">重置</gf-button>
              <gf-button @click="hideQueryParam()" class="option-btn">{{ collapsed ? '展开条件' : '收起条件' }} <i
                  :class="[
                    collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up',
                  ]"></i></gf-button>
            </div>
          </el-col>
        </el-row>

      </el-form>
    </div>
    <div class="bottom" style="margin-top: 30px;">
      <gf-grid :filterRemote="false"
              grid-no="agnes-biz-product-divideWork"
              toolbar="find,refresh,more"
              :query-args="queryArgs"
              ref="taskGrid"
              @selected-changed="selectedChanged"
              height="100%">
        <template slot="left">
          <el-button class="action-btn" v-if="$hasPermission('agnes.biz.product.divide.work.divideAdd')" @click="genDivideAddDlg('add')">
            新增
          </el-button>
          <el-button class="action-btn" v-if="$hasPermission('agnes.biz.product.divide.work.divideEdit')" @click="() => genDivideAddDlg('edit')" :disabled="selectNum !== 1">
            修改
          </el-button>
          <el-button class="action-btn" v-if="$hasPermission('agnes.biz.product.divide.work.divideDel')" @click="() => deleteByPrdtCode()">
            删除
          </el-button>
          <el-button class="action-btn" v-if="$hasPermission('agnes.biz.product.divide.work.import')" @click="() => uploadFile()">
            导入
          </el-button>
          <el-button class="action-btn" v-if="$hasPermission('agnes.biz.product.divide.work.export')" @click="() => exportDivide()">
            导出
          </el-button>
          <el-button class="action-btn" v-if="$hasPermission('agnes.biz.product.divide.work.divideTemp')" @click="downloadFile()">
            模板
          </el-button>
          <el-button class="action-btn" v-if="$hasPermission('agnes.biz.divide.work.viewProduct')" @click="viewProduct()">
            未有方案产品
          </el-button>
            <el-checkbox style="margin-right: 10px;" @change="reloadData" v-model="queryArgs.allParam" label="全部" :true-label="true" :false-label="false" />
        </template>
      </gf-grid>
    </div>
    <el-dialog
        title="产品分工方案"
        :visible.sync="dialogVisible"
        width="470px">
      <DivideAddDlg :type="type" :currentSelectObj='currentSelectObj' :selectObjId='selectObjId' :prdtCode='prdtCode' :attUser='attUser'
                    :bakProductCode='bakProductCode'
                    :dialogVisible='dialogVisible' @cancel-test="onCancelTest"/>
    </el-dialog>
    <el-dialog
        title="产品分工方案导入"
        :visible.sync="fileVisible"
        width="350px"
    >
      <fileUploadDlg @file-upload-cancel="onCancelFile" v-if="fileVisible"/>
    </el-dialog>
  </div>
</template>
<script>
import DivideAddDlg from './DivideAddDlg.vue'
import axios from "axios";
import fileUploadDlg from "./file-upload-dlg.vue";
import SelectPicker from '../../../../datav/components/common/select-picker.vue';
import ProductInfoDlg from "./product-info-dlg.vue";

export default {

  components: {
    DivideAddDlg,fileUploadDlg,
    SelectPicker
  },
  watch: {
    type: function (type) {
      if (type === 'edit') {
        console.log(type);        
      } else if (type === 'add') {
        this.currentSelectObj = null;
      }
    }
  },
  data() {
    return {
      dictProductStatus: [],
      assetTypeDict: this.$app.dict.getDictItems("AGNES_PRODUCT_CLASS"),
      selected: null,
      bakProductCode: '',
      selectNum: 0,
      prdtCode: [],
      selectObj: [],
      attUser: [],
      divideWork: [],
      dialogVisible: false,
      fileVisible: false,
      type: '',
      currentSelectObj: {},
      DivideDate:[],
      selectObjId:'',
      queryArgs: {
        allParam: false,
        productName: [],
        zhugangren: [],
        beigangren: [],
        divideWork: [],
        productStatus: [],
        custodianBank: '',
        assetTypeList: [],
      },
      collapsed: true,
    }
  },

  mounted() {
    this.dictProductStatus = this.$app.dict.getDictItems("AGNES_PRODUCT_STAGE")
    this.getAttUser();
    this.getPrdt();
    this.getDivideWork();
  },

  methods: {
    //查看未配置产品分工方案的产品信息
    viewProduct() {
      this.$nav.showDialog(
        ProductInfoDlg,
        {
          width: '50%',
          title: this.$dialog.formatTitle('产品信息', 'view'),
          closeOnClickModal: false,
        }
      );
    },
    hideQueryParam() {
      this.collapsed = !this.collapsed
    },
    uploadFile() {
      this.fileVisible = true;
    },
    async exportDivide() {
      let rows = this.$refs.taskGrid.getSelectedRows();
      if (rows.length == 0) {
        this.$msg.error('请选择需要导出的数据！');
        return
      }
      const ok = await this.$msg.ask(
          `确定要导出这些产品分工信息吗?, 是否继续?`
      );
      if (!ok) {
        return;
      }
      const ids = [];
      rows.forEach((item) => {
        ids.push(item.id);
      });
      axios({
        method: "post",
        url: "/api/agnes-app/v1/dop/prdt-divide-work/exportDivide",
        data: {pkIds: ids},
        responseType: "blob"
      }).then(res => {
        const link = document.createElement("a");
        let blob = new Blob([res.data], {type: "multipary/form-data"});
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        link.setAttribute("download", decodeURI(res.headers['content-disposition'].split('=')[1]));
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }).catch(error => {
        this.$Notice.error({
          title: "错误",
          desc: "系统数据错误"
        });
        console.log(error);
      });
    },
    async deleteByPrdtCode() {
      const ask = await this.$msg.ask("确定要删除这些产品分工信息吗?");
      if (!ask) {
        return;
      }
      let selectedRows = this.$refs.taskGrid.getSelectedRows();
      let productCodes = selectedRows.map(row => row.id);
      console.log(productCodes)
      try {
        let resp = await this.$api.DivideWorkApi.deleteDivide(productCodes);
        if (resp.success) {
          this.$msg.success('删除成功');
          this.getDivideWork();

        } else {
          this.$msg.error(resp.message || '删除失败');
        }
      } catch (e) {
        this.$msg.error(e);
      } finally {
        this.$refs.taskGrid.reloadData();
      }
    },

    async selectedChanged() {
      this.selectNum = this.$refs.taskGrid.getSelectedRows().length;
      this.selectObj = this.$refs.taskGrid.getSelectedRows();
      this.currentSelectObj = this.selectObj[0];
      this.selectObjId = this.currentSelectObj&&this.currentSelectObj.id||'';
      this.bakProductCode = this.currentSelectObj&&this.currentSelectObj.productCode||'';
    },
    async getDivideWork() {
      try {
        let resp = await this.$api.DivideWorkApi.getDivideWork();
        if (resp.success) {
          this.DivideDate = resp.data;
        } else {
          this.$msg.error(resp.message || '获取分工方案列表失败');
        }
      } catch (e) {
        this.$msg.error(e);
      }
    },
    async getPrdt() {
      try {
        let resp = await this.$api.DivideWorkApi.getPrdt();
        if (resp.success) {
          this.prdtCode = resp.data;
          
        } else {
          this.$msg.error(resp.message || '获取产品代码失败');
        }
      } catch (e) {
        this.$msg.error(e);
      }
    },
    async getAttUser() {
      try {
        let resp = await this.$api.DivideWorkApi.getAttUser();
        if (resp.success) {
          this.attUser = resp.data;
        } else {
          this.$msg.error(resp.message || '获取责任人列表失败');
        }
      } catch (e) {
        this.$msg.error(e);
      }
    },
    async reSetSearch() {
      this.queryArgs = {
        allParam: false,
        productName: [],
        zhugangren: [],
        beigangren: [],
        divideWork: [],
        productStatus: [],
        productType: [],
        custodianBank: '',
      };
      this.$nextTick(() => {
        this.reloadData();
      });
    },
    getQueryData() {
      this.reloadData();
    },
    reloadData(){
      this.$refs.taskGrid.reloadData(true);
    },
    async downloadFile() {
      axios({
        method: "post",
        url: "/api/agnes-app/v1/dop/prdt-divide-work/dowmloadTemplate",
        responseType: "blob"
      }).then(resp => {
        const link1 = document.createElement("a");
        let blob = new Blob([resp.data], {type: "multipary/form-data"});
        link1.style.display = "none";
        link1.href = URL.createObjectURL(blob);
        link1.setAttribute("download", decodeURI(resp.headers['content-disposition'].split('=')[1]));
        document.body.appendChild(link1);
        link1.click();
        document.body.removeChild(link1);
      }).catch(error => {
        this.$Notice.error({
          title: "错误",
          desc: "模板下载错误"
        });
        console.log(error);
      });
    },

    genDivideAddDlg(type) {
      if (type === 'edit') {
        this.currentSelectObj = this.$refs.taskGrid.getSelectedRows()[0];
      }
      this.dialogVisible = true;
      this.type = type;
    },

    onCancelTest() {
      this.dialogVisible = false;
      this.type = '';
      this.$refs.taskGrid.reloadData();
      this.getDivideWork();
    },
    async onCancelFile() {
      this.fileVisible = false;
      this.getDivideWork();
      this.$refs.taskGrid.reloadData();
      this.getDivideWork();
    },
  }
}

</script>
<style lang="less" scoped>
.bottom {
  position: relative;
  height: 100%;
  flex: 1;
}
.bottom ::v-deep .ag-grid-box{
  position: absolute;
}

.button-col {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}


.option-btn {
  margin-left: 10px;
}
.button-container {
  display: flex;
  justify-content: flex-end;
  gap: 8px; /* 按钮之间的间距 */
  position: absolute;
  &.button-fold{
    right: 0;
    top: -50px;
  }
  &.button-not-fold{
    right: 12px;
    top: 5px;
  }
}
</style>