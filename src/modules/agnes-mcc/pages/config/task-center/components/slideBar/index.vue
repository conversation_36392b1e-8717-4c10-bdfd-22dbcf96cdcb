<!--
 * @Des:
 * @version:
 * @Author: liufeifei
 * @Date: 2024-08-12 10:39:55
 * @LastEditors: yz.jiang
 * @LastEditTime: 2025-03-19 17:57:43
-->
<template>
  <div class="slide-bar-view elec-process">
    <div class="filter-view">
      <div class="filter-radio">
        <el-radio-group v-model="tempForm.taskCls">
          <el-radio
            v-for="item in options"
            :key="item.value"
            :label="item.value"
            @click.native.prevent="taskClsClickHandler"
          >
            <div
              :class="{
                active: tempForm.taskCls == item.value,
                taskSelectBtn: true,
              }"
              tabindex="1"
            >
              <span>{{ item.label }}</span>
              <img
                class="select-icon"
                src="../../assets/img/task-select.png"
                alt=""
              />
            </div>
          </el-radio>
        </el-radio-group>
        <el-dropdown @command="changeFilter">
          <img class="filter-icon" src="../../assets/img/filter.png" alt="" />
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="it in filters"
              :command="it.command"
              :key="it.command"
              >{{ it.label }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <div class="query-area">
        <el-date-picker
          class="date-picker"
          v-model="taskDateRange"
          type="daterange"
          align="right"
          unlink-panels
          :clearable="false"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          :picker-options="pickerOptions"
          @change="queryChange"
        />
        <img
          class="refresh-icon"
          @click="refreshCardTask"
          src="../../assets/img/refresh.png"
          alt=""
        />
        <div class="interval-ctrl">
            <span v-html="svgImg.startInterval"
                 v-show="ifIntervalStart"
                  @contextmenu.prevent="intervalListShow = true"
                  @click="clearFreshInterval(true)"
                  title="定时刷新已开启"

            ></span>
            <span v-html="svgImg.stopInterval"
                  v-show="!ifIntervalStart"
                   @contextmenu.prevent="intervalListShow = true"
                    @click="startFreshInterval(true)"
                  title="定时刷新已暂停"

            ></span>
            <ul class="interval-list" v-show="intervalListShow" v-clickoutside="outsideClick">
                <li v-for="inter in intervalList"
                    :class="intervalMin === inter.value ? 'is-select' : ''"
                    :key="inter.value"
                    @click="chooseIntervalMin(inter.value)"
                >
                    {{inter.label}}
            </li>
            </ul>
        </div>
      </div>
      <div class="add-query">
        <el-dropdown @command="selectTaskType">
          <el-button type="primary" plain class="add-btn" icon="el-icon-plus" @dblclick.native="quickAadTask"
            >新增任务</el-button
          >
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item
              v-for="it in addTaskTypes"
              :command="it.command"
              :key="it.command"
              >{{ it.label }}</el-dropdown-item
            >
          </el-dropdown-menu>
        </el-dropdown>

        <el-input
          placeholder="请输入"
          prefix-icon="el-icon-search"
          v-model="filterText"
        >
        </el-input>
      </div>
    </div>
    <div class="list-view" v-loading="loading">
      <el-tree
        indent="0"
        lazy
        node-key="pkId"
        ref="tree"
        :load="loadNode"
        :data="cardData"
        :props="defaultProps"
        :filter-node-method="filterNode"
        :default-expanded-keys="defaultExpandedKeys"
        :default-checked-keys="defaultCheckedKeys"
        @node-click="nodeClick"
        @node-expand="nodeClick"
      >
        <div
          :class="[
            'card-tree-node',
            node.level === 1 ? 'first-level' : '',
            'last-level',
          ]"
          slot-scope="{ node, data }"
        >
          <div class="node-label">
            <span>{{ data.themeName || data.taskName }}</span>
            <!-- 超时和异常都有时显示超时数，有一种显示一种-->
            <span class="err-count" v-if="data.timeOutCount > 0"
              >超时: {{ data.timeOutCount }}</span
            >
            <span class="err-count" v-else-if="data.errCount > 0"
              >异常: {{ data.errCount }}</span
            >
          </div>
          <div class="card-footer" v-if="!data.isInstant">
            <!--cardType-01 其他任务-不显示计数   cardType-02 临时任务-不显示计数  -->
            <template v-if="data.type == '00' || (data.type == '01' && data.countFun) || data.type == '02'  || data.type == '05'  ">
              <div class="type-tit">
                总数:
                <span class="num all">{{ data.totalCount }}</span>
              </div>
              <div class="type-tit">
                已完成:
                <span class="num done">{{ data.completeCount }}</span>
              </div>
              <div class="type-tit">
                未完成:
                <span class="num todo">{{ data.noCompleteCount }}</span>
              </div>
            </template>
            <template v-if="data.type == '04' || data.type == '03'">
              <div class="type-tit1">
                总数:
                <span class="num all">{{ data.totalCount }}</span>
              </div>
              <div class="type-tit1">
                异常:
                <span class="num error">{{ data.errCount }}</span>
              </div>
              <div class="type-tit1">
                干预通过:
                <span class="num confirm">{{ data.gytgCount }}</span>
              </div>
              <div class="type-tit1">
                正常:
                <span class="num common">{{ data.completeCount }}</span>
              </div>
            </template>
          </div>
          <div class="card-footer" v-else>
            <div class="card-status">
              <span
                class="status-icon"
                :style="{
                  backgroundColor: getCardStatus(data.taskStatus).color,
                }"
              ></span>
              {{ getCardStatus(data.taskStatus).label }}
            </div>
            <div class="card-date" v-if="data.startDate">
              <i class="el-icon-date"></i>
              {{ $dateUtils.formatDate(data.startDate, 'yyyy-MM-dd') }}至{{
                $dateUtils.formatDate(data.endDate, 'yyyy-MM-dd')
              }}
            </div>
          </div>
        </div>
      </el-tree>
    </div>
  </div>
</template>

<script>
import { CARD_STATUS } from '../../assets/js/constant'
import { EventBus } from '../../assets/js/eventBus'
import addTempTask from './add-temp-task'
import addSceneTask from './add-scene-task'
import { TreePerformanceMixin } from '../../../../../../../utils/performance/mixins.js'
import { debounce, globalPerformanceMonitor } from '../../utils/performance.js'
export default {
  components: {},
  mixins: [TreePerformanceMixin],
  props: {
    showType: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      taskDateRange: [
       window.bizDate,
       window.bizDate,
      ],
      tempForm: {
        basePath: '********',
        startDate:  window.bizDate, //开始日期
        endDate:  window.bizDate, //结束日期
        taskCls: '01', //	任务分类
      },
      tempTaskCls: '01',
      options: [
        {
          label: '仅待办',
          value: '01',
        },
        {
          label: '仅转交',
          value: '02',
        },
        {
          label: '仅协作',
          value: '03',
        },
      ],
      filters: [
        {
          label: '一级标题',
          command: 'first',
        },
        {
          label: '二级标题',
          command: 'second',
        },
      ],
      addTaskTypes: [
        {
          label: '临时任务',
          command: 'tempTask',
        },
        {
          label: '场景任务',
          command: 'sceneTask',
        },
      ],
      pickerOptions: {
        shortcuts: [
          {
            text: '最近三天',
            onClick(picker) {
              const end =  new Date(window.bizDate)
              const start =  new Date(window.bizDate)
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 3)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近一周',
            onClick(picker) {
              const end =  new Date(window.bizDate)
              const start =  new Date(window.bizDate)
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近一个月',
            onClick(picker) {
              const end =  new Date(window.bizDate)
              const start =  new Date(window.bizDate)
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              const end =  new Date(window.bizDate)
              const start =  new Date(window.bizDate)
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
      cardData: [],
      tempChildren: [],
      defaultProps: {
        children: 'childList',
        isLeaf: 'isLeaf',
        label: 'themeName',
        taskCls: 'taskCls',
      },
      filterText: '', //过滤树节点
      intervalList: [{label: '1分钟', value: 1}, {label: '3分钟', value: 3}, {label: '5分钟', value: 5}],
      intervalMin: 1,
      svgImg: this.$lcImg,
      freshInterval: null,
      intervalListShow: false,
      loading:false,
      ifIntervalStart:sessionStorage.getItem('ifIntervalStart') || false,
      currentClikNode: {},
      firstLoad: true,
      node: {},
      resolve: null,
      timeId: null,
      reFreshFlag: false,
      clickNodeList: [],

      defaultExpandedKeys:[],
      defaultCheckedKeys:[],
      tempNode:{},

      // 新增性能优化相关变量
      getCardListTimer: null,
      cardListCache: null,
      timeout: null,
    }
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val)
    },
      // 监听,当路由发生变化的时候执行
      $route(to, from) {
        if (this.path === from.path) {
            this.clearFreshInterval();
        }
        if (this.path === to.path) {
           this.ifIntervalStart = sessionStorage.getItem('ifIntervalStart') || false;
            if(this.ifIntervalStart) {
                this.startFreshInterval();
            }
        }
    }
  },
  async mounted() {
    // 使用requestAnimationFrame优化初始加载
    this.$nextTick(() => {
      requestAnimationFrame(() => {
        this.getCardList(false)
      })
    })
  },
  beforeDestroy() {
      // 清理所有定时器和事件监听器
      this.clearFreshInterval();
      if (this.timeId) {
        clearTimeout(this.timeId);
        this.timeId = null;
      }
      if (this.timeout) {
        clearTimeout(this.timeout);
        this.timeout = null;
      }
      if (this.getCardListTimer) {
        clearTimeout(this.getCardListTimer);
        this.getCardListTimer = null;
      }
      // 清理EventBus监听器
      EventBus.$off('updateCard');
      EventBus.$off('nodeTaskMonitor');
      EventBus.$off('nodeTaskInfo');
      // 清理缓存
      this.cardListCache = null;
  },
  methods: {
    filterNode(value, data) {
      if (!value) return true
      return data.themeName.indexOf(value) !== -1
    },
    async loadeChildNode(node, resolve){
      this.tempForm.basePath = node.data.basePath
      //获取子节点
      if (!node.data.leaf) {
        await this.getCardList(true)
        resolve(this.tempChildren)
        return;
      }
      let params = this.tempForm;
      if (node.data.themeName === '其他任务') {
          params.cardType = '01';
      } else if (node.data.themeName === '临时任务') {
          params.cardType = '02';
      } else {
          params.cardType = '';
      }
      //获取实例
      try {
        let resp = await this.$api.MccTaskCenterApi.todoTaskQuery({
          ...params,
          showType: this.showType,
        })
        if (resp.success) {
          //taskType 0场景任务 1流程任务 2单节点任务
          // 过滤单节点任务
          let resultData = resp.data.filter((item) => item.taskType !== '2')
          resultData.map((item) => {
            item.isInstant = true
            item.isLeaf = true
          })
          resolve(resultData)
        } else {
          this.$msg.error(resp.message || '获取实例失败')
        }
      } catch (e) {
        this.$msg.error(e)
      }
    },
    async loadNode(node, resolve) {
      this.node = node
      this.resolve = resolve

      // 如果是根节点，则直接解析已有的数据
      if (node.level === 0) {
        resolve(this.cardData)
      } else {
        this.loadeChildNode(node, resolve)
      }
    },
    /**
     * 获取卡片列表 - 优化版本，添加防抖和缓存
     */
    async getCardList(childFlag,type) {
      // 防抖处理，避免频繁请求
      if (this.getCardListTimer) {
        clearTimeout(this.getCardListTimer);
      }

      this.getCardListTimer = setTimeout(async () => {
        this.loading = true;
        try {
          //type为1表示刷新根节点，去查左侧所有卡片
          if(type === '1'){
            this.tempForm.basePath = '********'
          }else{
            this.tempForm.basePath = this.tempForm.basePath ?? '********'
          }

          // 生成缓存key
          const cacheKey = JSON.stringify({
            ...this.tempForm,
            showType: this.showType,
            childFlag
          });

          // 检查缓存（5秒内的数据直接使用）
          if (this.cardListCache && this.cardListCache.key === cacheKey &&
              Date.now() - this.cardListCache.timestamp < 5000) {
            this.getSuccess(this.cardListCache.data, childFlag);
            this.loading = false;
            return;
          }

          let resp = await this.$api.MccTaskCenterApi.queryForTask({
            ...this.tempForm,
            showType: this.showType,
          })
          if (resp.success) {
            // 缓存结果
            this.cardListCache = {
              key: cacheKey,
              data: resp,
              timestamp: Date.now()
            };
            this.getSuccess(resp,childFlag);
          } else {
            this.$msg.error(resp.message || '分组卡片查询失败')
          }
        } catch (e) {
          this.$msg.error(e)
        } finally {
          this.loading = false;
        }
      }, 200); // 200ms防抖
    },
    getSuccess(resp,childFlag){
      this.loading = false
      resp.data.forEach((item) => {
          // 指标、自定义卡片下无实例
          if (item.totalCount === 0 || item.type === '04' || (item.type === '01' && item.countFun)) {
              item.isLeaf = true;
          } else {
              item.isLeaf = false;
          }
          item.isInstant = false;
      });
      if (childFlag) {
        this.tempChildren = resp.data;
      } else {
          this.cardData = resp.data;
      }
    },
     // 刷新卡片
    refreshClickNode(updateCardNotState){
        if(this.clickNodeList.length > 0){
          if(this.clickNodeList.length === 1){
            this.tempNode = this.clickNodeList[0];
          }
            this.timeId = setTimeout(()=>{
              this.nodeClick(this.clickNodeList[0],updateCardNotState);
              if(this.clickNodeList[0]?.cardType !== '01' && this.clickNodeList[0]?.cardType !== '02'){
                this.$refs.tree.setCurrentNode(this.clickNodeList[0]);
                this.defaultExpandedKeys.push(this.clickNodeList[0].pkId);
                this.defaultCheckedKeys[0] = this.clickNodeList[0].pkId;
              }
              this.clickNodeList.splice(0,1);
              this.refreshClickNode(updateCardNotState);
            },800)
        }else {
          this.reFreshFlag = false;
          // 连续刷新设置选中样式
          this.timeId = setTimeout(()=>{
              this.nodeClick(this.tempNode,updateCardNotState);
              if(this.tempNode?.cardType !== '01' && this.tempNode?.cardType !== '02'){
                this.$refs.tree.setCurrentNode(this.tempNode);
                this.defaultExpandedKeys.push(this.tempNode.pkId);
                this.defaultCheckedKeys[0] = this.tempNode.pkId;
              }
          },800)
        }
    },
    /**
     * 任务分类/日期改变
     */
    queryChange() {
      this.resetTempForm()
      this.getCardList(false)
    },
    /**
     * 点击任务分类
     */
    taskClsClickHandler(event) {
      let clickTask = this.options.filter(
        (item) => item.label == event.target.innerText
      )
      this.tempForm.taskCls =
        this.tempTaskCls === clickTask[0].value ? '' : clickTask[0].value
      this.tempTaskCls = this.tempForm.taskCls
      this.queryChange()
    },
    /**
     * 左侧过滤修改
     */
    async changeFilter(command) {
      console.log(command)
    },

    /**
     * 刷新卡片任务列表
     */
    async refreshCardTask() {

      this.reFreshFlag = true;
      this.resetTempForm()
      this.getCardList(false)
      EventBus.$emit('updateCard')

      if(this.currentClikNode.type === '03' || this.currentClikNode.type === '04'){
        EventBus.$emit('nodeTaskMonitor', JSON.stringify(this.currentClikNode), true)
      }else{
        EventBus.$emit('nodeTaskInfo', JSON.stringify({...this.currentClikNode,updateCardNotState:true }), true)

      }
      this.$nextTick(()=>{
        this.refreshClickNode(true);
      })

    },
    /**
     * 重置tempForm
     */
    resetTempForm() {
      this.tempForm = {
        ...this.tempForm,
        basePath: '********',
        startDate: this.taskDateRange[0],
        endDate: this.taskDateRange[1],
      }
    },
    /**
     * 获取流程任务状态
     */
    getCardStatus(taskStatus) {
      let temp = CARD_STATUS.filter((item) => item.value === taskStatus)
      return temp.length > 0 ? temp[0] : {}
    },
    /**
     * 点击节点
     */
    nodeClick(data,updateCardNotState) {
      if(!this.reFreshFlag){
          this.reFreshClick(data);
          // 临时任务以及其他任务获取点击数据
          if(['01','02'].indexOf(data?.cardType) >=0){
          this.clickNodeList = [];
              this.clickNodeList.push(data);
          }
      }
      const elements = document.querySelectorAll('.last-level')
      elements.forEach((element) => {
        const parentElement = element.parentElement
        if (parentElement) {
          parentElement.classList.add('is-current');
          parentElement.classList.add('click-node');
        }
      })
      let tempData = {
        ...data,
        taskCls: this.tempForm.taskCls,
        startDate: this.taskDateRange[0],
        endDate: this.taskDateRange[1],
      }
      this.currentClikNode = tempData
      if(tempData.type === '03' || tempData.type === '04'){
        EventBus.$emit('nodeTaskMonitor', JSON.stringify(tempData))
      }else{
        //有可能是节点，并且要排除单独点击节点元素=》例如左侧单独单机临时任务
        const state= typeof updateCardNotState == 'boolean'
        //记录下nodeTaskInfo 刷新的参数，如果一致的话，就不再刷线了，
        if(this.checkoutTempData(tempData) && state) {
            return
        }
        this.nodeClicknodeTaskInfoParams = {...tempData}
        tempData.updateCardNotState = state ? updateCardNotState: false
        const tmp = JSON.stringify(this.nodeClicknodeTaskInfoParams)
        EventBus.$emit('nodeTaskInfo', tmp, updateCardNotState)
      }
    },
    reFreshClick(data){
      if(this.clickNodeList.length > 0){
        let index = this.clickNodeList.length - 1;
        // 当点击的节点basePath大于列表节点说明是子节点，则加入记录列表
        if(data?.basePath?.split('/').length  > this.clickNodeList[index]?.basePath?.split('/').length){
          this.clickNodeList.push(data);
        }else if(data?.basePath?.split('/').length  == this.clickNodeList[index]?.basePath?.split('/').length){
          this.clickNodeList = [];
          this.clickNodeList.push(data);
        }
      }else{
        this.clickNodeList = [];
        this.clickNodeList.push(data);
      }
    },
    /**
     * Author: yuelanfenghua
     * Date: 2025-01-10 18:02:27
     * version:
     * Des: 点击刷新按钮，接口不重复发送请求
     * param {*} tempData
     * return {*}
     */
    checkoutTempData(tempData) {
        this.nodeClicknodeTaskInfoParams = this.nodeClicknodeTaskInfoParams ||{}
      let oldParams = Object.keys(this.nodeClicknodeTaskInfoParams )
      let newParams = Object.keys(tempData)
      if(oldParams.length != newParams.length) {
        return false
      }
      return oldParams.filter(key => this.nodeClicknodeTaskInfoParams[key] != tempData[key] )==0
    },
    /**
     * 选中新增任务类型
     */
    selectTaskType(taskType) {
      if (taskType === 'tempTask') {
        this.$dialog.create({
          title: '新增临时任务',
          width: '700px',
          component: addTempTask,
          closeOnClickModal: false,
          args: {
            actionOk: this.addTempTaskFun.bind(this),
          },
        })
      } else {
        this.$dialog.create({
          title: '新增场景任务',
          width: '700px',
          component: addSceneTask,
          closeOnClickModal: false,
          args: {
            actionOk: this.addSceneTaskFun.bind(this),
          },
        })
      }
    },
    /**
     * 新增临时任务
     */
    addTempTaskFun(data) {
      this.addData('tempTaskAdd', data)
    },
    /**
     * 双击快捷新增临时任务
     */
    quickAadTask(){
      this.$dialog.create({
        title: '新增临时任务',
        width: '700px',
        component: addTempTask,
        closeOnClickModal: false,
        args: {
          actionOk: this.addTempTaskFun.bind(this),
        },
      })
    },
    /**
     * 新增场景任务
     */
    addSceneTaskFun(data) {
      this.addData('sceneTaskAdd', data)
    },
    async addData(fun, data) {
      try {
        let resp = await this.$api.MccTaskCenterApi[fun](data)
        if (resp.code === '00000000') {
          this.getCardList(false,'1')
          this.$msg.success('保存成功')
        } else {
          this.$msg.error(resp.message || '保存失败')
        }
      } catch (e) {
        this.$msg.error(e)
      }
    },
     // 关闭刷新定时器
     async clearFreshInterval(ifAsk){
          if(ifAsk){
              const ok = await this.$msg.ask('是否关闭定时刷新？');
              if (!ok) {
                  return
              }
          }
          this.ifIntervalStart = false;
          if(ifAsk){
            sessionStorage.setItem('ifIntervalStart', this.ifIntervalStart);
          }
          this.$forceUpdate();
          clearInterval(this.freshInterval);
      },
      // 开启刷新定时器
      async startFreshInterval(ifAsk){
          if(ifAsk){
              const ok = await this.$msg.ask('是否开启定时刷新？');
              if (!ok) {
                  return
              }
          }
          this.ifIntervalStart = true;
          sessionStorage.setItem('ifIntervalStart', this.ifIntervalStart);
          this.$forceUpdate();
          const intervalMin = this.intervalMin*60*1000;
          this.freshInterval = setInterval(() => {
              if (!this.loading) {
                // this.queryChange()
                this.refreshCardTask();
              }
          }, intervalMin);
      },
      outsideClick(){
          this.intervalListShow = false;
          this.$forceUpdate()
      },
      // 定时频率选择
      async chooseIntervalMin(chooseMin){
          const ok = await this.$msg.ask(`是否切换刷新频率为${chooseMin}分钟？`);
          if (!ok) {
              return
          }
          this.clearFreshInterval();
          this.intervalMin = chooseMin;
          if(this.ifIntervalStart){
              this.startFreshInterval();
          }
          this.$msg.success('刷新频率设置成功！');
      },
  },
}
</script>

<style scoped lang="less">
.slide-bar-view {
  display: flex;
  height: 100%;
  overflow: auto;
  flex-direction: column;
  border-right: 1px solid #d7dfea;
  .filter-view ::v-deep {
    height: 130px;
    .filter-radio {
      height: 35px;
      border-bottom: 1px solid #d7dfea;
      .el-radio {
        margin-right: 13px !important;
        &:last-child {
          margin-right: 8px !important;
        }
      }
      .el-radio__input {
        display: none;
      }
      .el-radio__label {
        height: 26px;
        display: inline-block;
        position: relative;
        padding-left: 0 !important;
        .taskSelectBtn {
          width: 74px;
          height: 26px;
          line-height: 23px;
          border-radius: 4px;
          opacity: 1;
          background: #fff;
          color: #24272e;
          text-align: center;
          border: 1px solid #ebeced;
          span {
            width: 36px;
            opacity: 1;
            font-size: 12px;
            font-weight: 400;
            font-family: 'Roboto', 'Helvetica Neue', Arial, sans-serif;
          }
          /* 点击选择样式改变 */
          &.active {
            color: #3387f3;
            background: #e0edfd;
            border: 1px solid #3387f3;
            &::before {
              content: '';
              position: absolute;
              right: 0;
              bottom: 0;
              width: 12px;
              height: 12px;
              background-color: #3387f3;
              border-radius: 4px 0;
            }
          }
          .select-icon {
            position: absolute;
            right: 4px;
            bottom: 4px;
          }
        }
      }
      .filter-icon {
        cursor: pointer;
      }
    }
    .query-area {
      margin-bottom: 6px;
      display: flex;
      .date-picker {
        width: 79%;
      }
      .refresh-icon {
        cursor: pointer;
        margin: 18px 4px;
        width: 20px;
        height: 20px;
      }
    }
    .add-query {
      display: flex;
      justify-content: space-between;
      padding-right: 12px;
      .el-button--primary.is-plain {
        background: #fff;
        margin-right: 6px;
        border-radius: 4px;
        border-color: #3387f3;
        color: #3387f3;
        .el-icon-plus {
          color: #3387f3;
        }
        &:hover {
          border-color: #5C9FF5;
          background: #5C9FF5;
          color: #fff;
          .el-icon-plus {
            color: #fff;
          }
        }
        &:active {
          border-color: #3387f3;
          background: #3387f3;
          color: #fff;
          .el-icon-plus {
            color: #fff;
          }
        }
      }
    }
  }
  .list-view {
    flex: 1;
    overflow: auto;
    &::-webkit-scrollbar {
      width: 2px; /* 滚动条宽度 */
    }
    .el-tree::v-deep {
      width: 265px;
      flex-shrink: 0;
      > .el-tree-node {
        margin-bottom: 7px;
        border: 1px solid #eee;
        border-radius: 2px;
        > .el-tree-node__content > .card-tree-node {
          line-height: 25px;
          // 一级标题
          .node-label {
            font-weight: 600;
          }
        }
        .el-tree-node__content > .el-tree-node__expand-icon {
          position: relative;
          top: -13px;
          color: #000;
          &.is-leaf {
            color: transparent !important;
          }
        }
        .el-tree-node__content {
          width: 265px;
          height: 63px;
          background: #e6e7eb;
        }
        > .el-tree-node__content {
          border-radius: 2px;
        }

        &.is-expanded > .el-tree-node__children {
          overflow: visible;
          .el-tree-node__content {
            border-bottom: 1px solid #e4e7ee;
            background: #f1f2f6b3;
          }
          .el-tree-node__children {
            overflow: hidden;
            .el-tree-node__content {
              border-bottom: 1px solid #e4e7ee;
              background: #fff;
            }
          }
          .is-current {
            > .el-tree-node__content.click-node {
              background: #3387f3 !important;
              .node-label {
                color: #fff;
              }
              .card-footer {
                .card-status,
                .card-date {
                  color: #fff;
                  .status-icon {
                    background-color: #fff !important;
                  }
                }
              }
            }
          }
        }
      }
      .card-tree-node {
        width: 85%;
        .node-label {
          color: #333333;
          font-size: 14px;
          font-weight: 500;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          .err-count {
            position: absolute;
            right: -4px;
            height: 24px;
            line-height: 24px;
            padding: 0 8px;
            border-radius: 4px 4px 0 4px;
            font-size: 12px;
            font-weight: 400;
            background: #ed5858;
            color: #fff;
            box-shadow: 0 2px 3px 0 #ed585833;
            &::after {
              content: '';
              position: absolute;
              top: calc(100% - 0.5px);
              right: 0px;
              border-left: 4px solid transparent;
              border-right: 4px solid transparent;
              border-bottom: 4px solid #993939;
              transform: rotate(-45deg);
            }
          }
        }
        .card-footer {
          display: flex;
          justify-content: flex-start;
          margin-top: 2px;
          .type-tit {
            color: #9ea7b5;
            font-family: 'Source Han Sans CN', sans-serif;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            margin-right: 30px;
            .num {
              font-weight: 600;
              &.all {
                color: #556377;
              }
              &.done {
                color: #20c781;
              }
              &.todo {
                color: #faa125;
              }
            }
          }
          .type-tit1 {
            color: #9ea7b5;
            font-family: 'Source Han Sans CN', sans-serif;
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            margin-right: 15px;
            .num {
              font-weight: 600;
              &.all {
                color: #556377;
              }
              &.error{
                color: #FA6A6A;
              }
              &.confirm{
                color: #bba350;
              }
              &.common{
                color:green;
              }
            }
          }
          .card-status {
            .status-icon {
              display: inline-block;
              width: 6px;
              height: 6px;
              border-radius: 50%;
              position: relative;
              top: -1px;
            }
          }
          .card-date {
            color: #9ea7b5;
            font-size: 12px;
            margin-left: 7px;
          }
        }
      }
    }
  }
}

.interval-ctrl {
    line-height: 22px;
    width: 16px;
    height: 16px;
    cursor: pointer;
    margin: 20px 4px;
}

</style>
