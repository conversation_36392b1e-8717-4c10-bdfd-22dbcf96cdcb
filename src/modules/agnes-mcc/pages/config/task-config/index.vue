<template>
<div>
    <div class="return-button" v-if="isShow &&$app.dict.getDictName('AGNES_CJHX_BUTTON', 'cancelBtn') == 'true'">
        <gf-button class="add-btn" size="mini" @click="jumpPage">返回</gf-button>
    </div>
    <div class="case-scene" :style="{ height: (isShow && $app.dict.getDictName('AGNES_CJHX_BUTTON', 'cancelBtn') == 'true') ? 'calc(100% - 40px)' : '100%' }">
        <task-complex-widget class="box" id="taskTreeDragContainer" v-dragx="dragColumn" @bindUpdate="dragColumnUpdate"  ref="complexWidget" compTitle="任务主题"
        :fileOption="fileOption" :defaultExpandAll="false" :defalutMenuFold="false" :compType="['task']" @getMoreData="getMoreSetting"
        :menu="menu">
        </task-complex-widget>
        <div class="case-scene-right">
            <div class="case-scene-right-top">
                <el-form ref="form" label-position="right" :model="queryArgs" label-width="100px" size="mini">
                    <el-row :gutter="20">
                        <el-col :span="6">
                            <el-form-item label="任务名称">
                                <el-input v-model="queryArgs.taskName" :max-byte-len="50"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="配置方式">
                                <select-picker
                                    placeholder="请选择配置方式"
                                    collapse-tags
                                    clearable
                                    v-model="queryArgs.disTypes"
                                    :options="collocOptions?.map(_=>({value:_.dictId, label:_.dictName}))"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <el-form-item label="状态">
                                <select-picker
                                    placeholder="请选择状态"
                                    collapse-tags
                                    clearable
                                    v-model="queryArgs.taskStatusArr"
                                    :options="stateOptions?.map(_=>({value:_.dictId, label:_.dictName}))"
                                />
                            </el-form-item>
                        </el-col>
                        <el-col :span="6">
                            <gf-button @click="reSetSearch()" class="option-btn" style="margin-left: 16px;">重置</gf-button>
                            <gf-button  @click="getQueryData()" class="option-btn" type="primary" >查询</gf-button>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <gf-grid :filterRemote="false" grid-no="data-task-config" @row-double-click="dbClickData" @selected-changed="selectedChanged" ref="taskGrid" height="100%" :query-args="queryArgs">
                <template slot="left">
                    <gf-button class="action-btn" v-if="$app.dict.getDictName('AGNES_CJHX_BUTTON', 'quickAdd') == 'false' && $hasPermission('agnes.task.config.quick.add')"
                        :disabled="this.canSet === '0'" size="mini" @click="addCaseScene(quickAddData,'quickAdd')">
                        快捷新增
                    </gf-button>
                    <gf-button class="action-btn" :disabled="this.canSet === '0'"  @click="addChecklist('regularAdd')" size="mini"
                        v-if="$hasPermission('agnes.task.config.add')">
                        新增
                    </gf-button>
                    <gf-button class="action-btn" :disabled="this.canSet === '0'" @click="taskCopy()" size="mini"
                        v-if="$hasPermission('agnes.task.config.copy')">
                        复制
                    </gf-button>
                    <gf-button class="action-btn" :disabled="this.canSh === '0' || this.rowCanSh === '0'"  @click="taskExamine('examine')" size="mini"
                        v-if="$hasPermission('agnes.task.config.audit')">
                        审核
                    </gf-button>
                    <gf-button class="action-btn" :disabled="this.canYs === '0' || this.rowCanYs === '0'"  @click="taskCheck('check')" size="mini"
                        v-if="$hasPermission('agnes.task.config.acceptance')">
                        验收
                    </gf-button>
                    <gf-button class="action-btn" :disabled="this.canFb === '0' || this.rowCanFb === '0'"  @click="taskRelease('release')" size="mini"
                        v-if="$hasPermission('agnes.task.config.publish')">
                        发布
                    </gf-button>
                    <gf-button class="action-btn" :disabled="this.canHt === '0' || this.rowCanHt === '0'"  @click="taskReturn('return')" size="mini"
                        v-if="$hasPermission('agnes.task.config.back')">
                        回退
                    </gf-button>
                    <gf-button class="action-btn" :disabled="this.canTy === '0' || this.rowCanTy === '0'"  @click="taskStop('stop')" size="mini"
                        v-if="$hasPermission('agnes.task.config.stop')">
                        停用
                    </gf-button>
                    <gf-button class="action-btn" :disabled="this.canSc === '0' || this.rowCanSc === '0'"  @click="taskDelete('delete')" size="mini"
                        v-if="$hasPermission('agnes.task.config.delete')">
                        删除
                    </gf-button>
                    <gf-button class="action-btn" :disabled="selectRows" @click="exportData" size="mini">
                        导出
                    </gf-button>
                    <gf-button class="action-btn" @click="importData" size="mini">
                        导入
                    </gf-button>
                </template>
            </gf-grid>
        </div>
    </div>
</div>
</template>

<script>
import moveFloderDlg from './move-floder-dlg.vue';
import taskSetting from './task-setting.vue';
import addCaseDialog from "./add-case-dialog.vue";
import taskAdding from "./task-adding.vue";
import axios from "axios";
import leadIntoList from "./lead-into-list.vue";
export default {
    name: "index",
    props:{
        isCjhxFirst:{
            type:Boolean,
            default:true
        },
        isShow:{
            default:false,
            type:Boolean
        }
    },
    data() {
        return {
            selectRows: true,
            isFirstClick:true,
            queryArgs: {
                'themeId':'',
                'taskName':'',
                'disTypes':[],
                'taskStatusArr':[],
            },
            menu: [
                {
                    title: "添加",
                    childer: [
                        {
                        title: "添加子节点",
                        value: "1",
                        },
                        {
                        title: "添加同级节点",
                        value: "2",
                        },
                    ],
                },
                {
                    title: "移动",
                    value: "4",
                },
                {
                    title: "删除节点",
                    value: "5",
                },
                {
                    title: "权限设置",
                    value: "6",
                },
            ],
            canSet: '0',
            canSh: '0',
            canFz: '0',
            canYs: '0',
            canFb: '0',
            canTy: '0',
            canHt: '0',
            canSc: '0',
            rowCanSh:'0',
            rowCanYs:'0',
            rowCanFb:'0',
            rowCanHt:'0',
            rowCanTy:'0',
            rowCanSc:'0',
            currentTreeNode:{},
            dragColumn: {dragContainerId: "taskTreeDragContainer", dragDirection: 'e'},
            dictParentId: 'R0000001', //根目录
            collocOptions:[],
            stateOptions:[],
            coverTree: [],
            agentData: [],
            fileOption: [{
                treeData: [],
                defaultActions: {
                    'node-click': this.nodeClick,
                }
            }],
            taskTitle:'',
            powerData:{},
            themeName: '',
            type:'',
            addType:'',
            quickAddData:{
                disType:2, //单节点任务
                name:'default',
            }
        }
    },
    computed: {
        iconTypeArr() {
            return this.$app.$root.$store.state.publicData.iconTypeArr
        },
    },
    mounted() {
        if(this.isCjhxFirst){
            this.isFirstClick = true;
            this.initTreeData();
        }else{
           let tempData = JSON.parse(sessionStorage.getItem("currentTreeNode"));
           this.initTreeData(tempData)
        }
        this.collocOptions = this.$app.dict.getDictItems('OMS_TASK_MODE');
        this.stateOptions = this.$app.dict.getDictItems('AGNES_TASK_STATUS');

    },
    methods: {
        jumpPage(){
            let viewId = 'agnes.task.config';
            this.$agnesUtils.closeTab(viewId);
        },
        // 处理按钮操作
        handleButtonClick(rows) {
              // 审核
            this.rowCanSh = rows.every(item => item.reTaskDef.taskStatus === '01') ? '1' : '0';

            // 验收
            this.rowCanYs = rows.every(item => item.reTaskDef.taskStatus === '08') ? '1' : '0'
            // 发布
            this.rowCanFb = rows.every(item => item.reTaskDef.taskStatus === '09' || item.reTaskDef.taskStatus === '02') ? '1' : '0'
            // 回退
            this.rowCanHt = rows.every(item => item.reTaskDef.taskStatus === '09' || item.reTaskDef.taskStatus === '02' ||
                item.reTaskDef.taskStatus === '08') ? '1' : '0'
            // 停用
            this.rowCanTy = rows.every(item => item.reTaskDef.taskStatus === '03') ? '1' : '0'
            //删除
            this.rowCanSc = rows.every(item => item.reTaskDef.taskStatus === '09' || item.reTaskDef.taskStatus === '01') ? '1' : '0'
        },
        selectedChanged() {
            let rows = this.$refs.taskGrid.getSelectedRows();
            this.selectRows = rows.length <= 0;
            if(rows && rows.length > 0){
              this.handleButtonClick(rows);
            }

        },
        //树点击事件
        nodeClick(data) {
            this.canSet = data.canSet || '0';
            this.canSh = data.canSh || '0';
            this.canYs = data.canYs || '0';
            this.canFz = data.canFz || '0';
            this.canFb = data.canFb || '0';
            this.canTy = data.canTy || '0';
            this.canHt = data.canHt || '0';
            this.canSc = data.canSc || '0';
            this.rowCanSh = '0';
            this.rowCanYs = '0';
            this.rowCanFb = '0';
            this.rowCanHt = '0';
            this.rowCanTy = '0';
            this.taskName = data.themeName;
            //初始化进来的时候默认选中第一行目录
            console.log("this.isFirstClick",this.isFirstClick);
            this.currentTreeNode = data;
            if(this.isFirstClick === true){
              this.currentTreeNode = this.agentData.find(item => {
                return item.pkId === data.pkId
              })
              console.log("currentTreeNode",this.currentTreeNode);

              if(this.currentTreeNode) {
                this.$nextTick(() => {
                  this.$refs.complexWidget?.$refs.taskTreeComp.isSelected(this.currentTreeNode)
                })
              }
              this.isFirstClick = false;
            }
            if(!this.isCjhxFirst){
                if(data) {
                    this.$nextTick(() => {
                        this.$refs.complexWidget?.$refs.taskTreeComp.isSelected(data)
                    })
                }
            }
            sessionStorage.setItem('currentTreeNode', JSON.stringify(this.currentTreeNode))
            this.queryArgs.themeId = data.pkId;
            this.reloadData();
        },
        reloadData(){
            this.$refs.taskGrid.reloadData(true);
            setTimeout(() => {
                this.$refs.taskGrid.gridController.gridApi.sizeColumnsToFit(); //调整表格大小自适应
                this.$refs.taskGrid.gridController.autoFitColumns(1); //自适应列宽
            }, 600);
        },
        reloadTree(){
            this.$refs.taskGrid.reloadData(true);
            this.initTreeData('1');
        },
        // 查询
        getQueryData(){
            this.reloadData();
        },
        // 重置
        reSetSearch(){
            this.queryArgs.taskName = '';
            this.queryArgs.disTypes = [];
            this.queryArgs.taskStatusArr = [];
            this.reloadData();
        },
        async addChecklist(addType) {
            this.addType = addType;
            if (!this.taskName) {
                this.$msg.msg("请在左侧选任务主题!", 'warning');
                return;
            }
            this.$nav.showDialog(
                addCaseDialog,
                {
                    args: {actionOk: this.addCaseScene.bind(this)},
                    width: '530px',
                    title: '创建任务',
                }
            );
        },
      async editData(params) {
        let resp = await this.$api.xcpInfoApi.executeApi('a087fff826cd334cc3006c6105ee70ea', {});
        params.data.reTaskDef.tableInfos = resp.data;
        let option = resp.data;
        let hasDataModel = false;
        if (option) {
          let code = params.data.reTaskDef.caseKey;
          option.forEach((item => {
            if (item.datatype == code) {
              hasDataModel = true;
            }
          }))
          if (!hasDataModel) {
            //保存低码信息
            let xcpData = {
              "xcpInfo": {
                "apiCode": "bb823a2b6ff58df9e5d90d6b593be22b",
                "body": {
                  "dsId": "default",
                  "datatype": params.data.reTaskDef.caseKey,
                  "datatypeName": params.data.reTaskDef.taskName,
                  "fieldList": [
                    {
                      "fieldLength": 32,
                      "fieldType": "text",
                      "isRequire": "1",
                      "orderNum": 0,
                      "tableField": "PK_ID",
                      "field": "pkId",
                      "fileDataType": "01",
                      "linkId": "1",
                      "component": "input-text",
                      "formatType": "",
                      "format": "",
                      "fieldName": "pkId",
                      "isBizKey": "1",
                      "isPk": "1",
                      "ext": "{\"multiple\":\"0\"}",
                      "fillRule": "ATOMIC_ID"
                    },
                    {
                      "fieldLength": 32,
                      "fieldType": "text",
                      "isRequire": "0",
                      "orderNum": 1,
                      "tableField": "CASE_ID",
                      "field": "caseId",
                      "fileDataType": "01",
                      "linkId": "1",
                      "component": "input-text",
                      "formatType": "",
                      "format": "",
                      "fieldName": "caseId"
                    },
                    {
                      "fieldLength": 32,
                      "fieldType": "text",
                      "isRequire": "0",
                      "orderNum": 1,
                      "tableField": "EXEC_DATE",
                      "field": "execDate",
                      "fileDataType": "01",
                      "linkId": "1",
                      "component": "input-text",
                      "formatType": "",
                      "format": "",
                      "fieldName": "执行日期"
                    },
                    {
                      "fieldLength": 32,
                      "fieldType": "text",
                      "isRequire": "0",
                      "orderNum": 1,
                      "tableField": "START_DATE",
                      "field": "startDate",
                      "fileDataType": "01",
                      "linkId": "1",
                      "component": "input-text",
                      "formatType": "",
                      "format": "",
                      "fieldName": "业务开始日期"
                    },
                    {
                      "fieldLength": 32,
                      "fieldType": "text",
                      "isRequire": "0",
                      "orderNum": 1,
                      "tableField": "END_DATE",
                      "field": "endDate",
                      "fileDataType": "01",
                      "linkId": "1",
                      "component": "input-text",
                      "formatType": "",
                      "format": "",
                      "fieldName": "业务结束日期"
                    },
                  ],
                  "linkList": [],
                  "tableName": "AC_RU_CASE_MODEL_DEF"
                }
              }
            }
            const p2 = this.$api.businessObjectApi.saveXcpModel({xcpInfo: xcpData.xcpInfo});
            let resp2 = await this.$app.blockingApp(p2);
          }
        }
        this.addType = params.data.reTaskDef.addType;
        this.pageJump('agnes.work.flow', params.data, this.reloadData.bind(this), '', 'edit', this.addType)
      },
        /**
         * 复制
         */
        async taskCopy() {
            let selctRows = await this.$refs.taskGrid.getSelectedRows();
            if (selctRows.length !== 1) {
              this.$message.warning('请选择一条记录!')
              return;
            }
            let data = selctRows[0];
            this.addType = data.reTaskDef.addType;
            this.pageJump('agnes.work.flow', data, this.reloadData.bind(this), '','copy',this.addType)
        },
        async initCode() {
            const resp = await this.$api.codeGeneratorApi.getCode('case');
            let caseKey = ''
            if (resp && resp.data) {
                caseKey = resp.data;
            }
            return caseKey
        },
        //新建
        async addCaseScene(data,type) {
            this.addType = type;
            let caseKey = await this.initCode()
            const req = {
                caseKey,
                taskName: data.name,
                bizType: this.taskType,
                configDef: 'caseV3',
                disType: data.disType,
            }
            let res = '';
            if(this.addType !== 'quickAdd'){
                res = await this.$api.motConfigApi.preSaveTask({reTaskDef: req, isCheckCode: true,});
            }else{
                res= {'data':{'reTaskDef':{'caseKey':caseKey,'taskName':'default',configDef:'caseV3'}}}
            }
            if (res.data ) {
                //保存低码信息
                let xcpData = {
                    "xcpInfo": {
                        "apiCode": "bb823a2b6ff58df9e5d90d6b593be22b",
                        "body": {
                          "dsId": "default",
                          "datatype": "case-00000681",
                          "datatypeName": "测试",
                          "fieldList": [
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "1",
                              "orderNum": 0,
                              "tableField": "PK_ID",
                              "field": "pkId",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "pkId",
                              "isBizKey": "1",
                              "isPk": "1",
                              "ext": "{\"multiple\":\"0\"}",
                              "fillRule": "ATOMIC_ID"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "CASE_ID",
                              "field": "caseId",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "caseId"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "EXEC_DATE",
                              "field": "execDate",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "执行日期"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "START_DATE",
                              "field": "startDate",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "业务开始日期"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "END_DATE",
                              "field": "endDate",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "业务结束日期"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "PLAN_START_TS",
                              "field": "planStartTs",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "计划开始日期"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "PLAN_END_TS",
                              "field": "planEndTs",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "计划结束日期"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "PRDT_CODE",
                              "field": "prdtCode",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "产品代码"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "PRDT_NAME",
                              "field": "prdtName",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "产品名称"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "FIELD1",
                              "field": "field1",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "备用1"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "FIELD2",
                              "field": "field2",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "备用2"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "FIELD3",
                              "field": "field3",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "备用3"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "FIELD4",
                              "field": "field4",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "备用4"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "FIELD5",
                              "field": "field5",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "备用5"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "FIELD6",
                              "field": "field6",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "备用6"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "TASK_NAME",
                              "field": "taskName",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "任务名称"
                            },
                            {
                              "fieldLength": 32,
                              "fieldType": "text",
                              "isRequire": "0",
                              "orderNum": 1,
                              "tableField": "BIZ_ID",
                              "field": "bizId",
                              "fileDataType": "01",
                              "linkId": "1",
                              "component": "input-text",
                              "formatType": "",
                              "format": "",
                              "fieldName": "业务主键"
                            }
                          ],
                          "linkList": [],
                          "tableName": "AC_RU_CASE_MODEL_DEF"
                        }
                    }
                }
                xcpData.xcpInfo.body.datatypeName = data.name;
                xcpData.xcpInfo.body.datatype = caseKey;
                const p2 = this.$api.businessObjectApi.saveXcpModel({xcpInfo: xcpData.xcpInfo});
                let resp2 = await this.$app.blockingApp(p2);
                if (resp2.msgType && resp2.msgType == 'success') {
                    let row = this.$utils.deepClone(res.data)
                    row.reTaskDef.disType = data.disType
                    row.reTaskDef.themeId = this.queryArgs.themeId
                    this.pageJump('agnes.work.flow',row, this.reloadData.bind(this), '','add',this.addType)

                }
            }
        },

        pageJump(url,row, actionOk,title,mode,addType){
            let tabMap = this.$app.nav?.tabBar?.tabMap || new Map();
            if(tabMap && tabMap.get(url) && url === 'agnes.work.flow') {
                this.$confirm("是否放弃正在编辑中的数据?", "提示", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning",
                })
                .then(() => {
                    this.toPage(url,row, actionOk,title, mode,addType)
                })
                .catch(() => {
                    this.$message({
                        type: 'info',
                        message: '已取消'
                    })
                });
            }else {
                this.toPage(url,row, actionOk,title, mode,addType)
            }

        },
        toPage(url,row, actionOk,title, mode,addType) {
            let viewId = url;
            this.$agnesUtils.closeTab(viewId);
            let bindingMode = 'BLOCK_MODE';
            let dicts = this.$app.dict.getDictItems("TASK_BINDING_MODE");
            let dict = dicts?.find(m => m.filter1 === '1');
            bindingMode = dict?.dictId;
            this.$nextTick(() => {
                let pageView = this.$app.views.getView(viewId);
                let tabView = Object.assign(
                    {
                        args: {
                            row: row,
                            mode: mode,
                            title,
                            actionOk: actionOk,
                            addType: addType,
                            bindingMode: bindingMode,
                        },
                        id: viewId
                    }, pageView);

                this.$nav.showView(tabView);
            })
        },
        // 复制
        async copyCaseScene(){
            console.log('copyCaseScene');
        },
        editRoback(){
            this.reloadData();
        },
        // 查询左侧数据
        async initTreeData(flag) {
            try {
                const p = this.$api.MccTaskConfigApi.getQuery({userId: window.$gfui.$app.session.data.user.userId});
                const resp = await this.$app.blockingApp(p);
                if(resp.success){
                    this.coverTree = JSON.parse(JSON.stringify(resp.data));
                    // 找到 pkId 为 99999999 的对象所在的索引
                    const specialIndex = this.coverTree.findIndex(item => item.pkId === '99999999');
                    // 如果找到 pkId 为 99999999 的对象，将其从数组中取出并添加到数组的末尾
                    if (specialIndex !== -1) {
                        const specialItem = this.coverTree[specialIndex];
                        this.coverTree.splice(specialIndex, 1); // 移除特殊项
                        this.coverTree.push(specialItem); // 添加到数组末尾
                    }
                    this.agentData = this.formatTree(this.coverTree)
                    this.fileOption[0].treeData = this.agentData;
                        // 默认展开三级
                        this.$nextTick(() => {
                            this.$refs.complexWidget.changeExpandedLevel(3)
                        })
                }
                    if(flag === '1') {
                        console.log("nodeClick",this.currentTreeNode);
                        this.nodeClick(this.currentTreeNode)
                    }else if(typeof flag === 'object'){
                        setTimeout(() => {
                          this.nodeClick(flag);
                        },200)
                    }else{
                        setTimeout(() => {
                          this.nodeClick(this.agentData[0]);
                        },200)
                    }
            } catch (e) {
                this.$message.error(e);
            }
            this.$nextTick(()=>{
                this.$refs.complexWidget.changeExpandedLevel(3);
            })
        },
        formatTree(data) {
            let formatTree = JSON.parse(JSON.stringify(data));
            formatTree.forEach(item => {
                item.type = 'folder'
                item.id = item.pkId
                item.isHovered = 'false'
                item.isEdit = false
            })
            return this.transformTree(formatTree, this.dictParentId)
        },
        //平级转换树结构
        transformTree(treeList, parentId) {
            let treeArr = [];
            treeList.forEach(item => {
                if (item.parentThemeId == parentId) {
                    const childList = this.transformTree(treeList, item.pkId)
                    if (childList.length) {
                        item.children = childList
                    }
                    treeArr.push(item);
                }
            })
            return treeArr;
        },
        // 2：添加同级节点操作，1：添加子节点操作
        addNode(data, type) {
            this.type = type;
            this.$nav.showDialog(
                taskAdding,
                {
                    width: '400px',
                    title: '新增主题',
                    args: {
                        data: data,
                        actionOk: this.addTask.bind(this)
                    }
                })
        },
        // 移动节点
        moveNode(data) {
            this.$nav.showDialog(
                moveFloderDlg,
                {
                    args: {
                        treeData: this.agentData,
                        data: data,
                        actionOk: this.reloadTree.bind(this),
                    },
                    width: '600px',
                    title: '移动主题级别',
                }
            );
        },
        // 删除节点
        async deleteNode(data) {
            if (data.pkId === "99999999") {
                return this.$message.warning('无归属主题不能删除')
            }
            //删除节点
            const ok = await this.$msg.ask(`确认删除吗, 是否继续?`);
            if (ok) {
                try {
                    const p = this.$api.MccTaskConfigApi.deleteQuery(data);
                    const resp = await this.$app.blockingApp(p);
                    if (resp.success) {
                        this.$message.success('删除成功！');
                        // 回到初始第一个状态
                        this.isCjhxFirst = true;
                        this.isFirstClick = true;
                        this.initTreeData();
                    } else {
                        this.$message.error(resp.message || '删除失败');
                    }
                } catch (e) {
                    this.$message.error('删除失败！');
                }
            }
        },
        // 权限设置
        async permissionSetting(data) {
            let obj = {
                themeId: data.pkId,
                basePath: data.basePath,
            }
            this.taskTitle = `权限设置-${data.themeName}`;
            let p = this.$api.MccTaskConfigApi.powerQuery(obj);
            let resp = await this.$app.blockingApp(p);
            this.powerData = resp.data;
            this.$nav.showDialog(
                taskSetting,
                {
                    width: '900px',
                    title: this.taskTitle,
                    args: {
                        treeData: data,
                        data: this.powerData,
                        actionOk: this.reloadTree.bind(this)
                    }
                })
        },
        // 拖拽节点操作
        async dragNode(data) {
            const p = this.$api.MccTaskConfigApi.treeMove(data);
            const resp = await this.$app.blockingApp(p);
            if (resp.success) {
                this.$message.success('移动成功！');
            } else {
                this.$message.error(resp.message || '移动失败');
            }
        },


        async getMoreSetting(type, data) {
            switch (type) {
                case '1':
                    // 添加子节点操作
                    this.addNode(data, type);
                    break;
                case '2':
                    // 2：添加同级节点操作
                    this.addNode(data, type);
                    break;
                case '4':
                    // 4：移动节点操作
                    this.moveNode(data);
                    break;
                case '5':
                    // 删除节点
                    this.deleteNode(data);
                    break;
                case '6':
                    // 权限设置
                    this.permissionSetting(data);
                    break;
                case 'drag':
                    // 拖拽节点操作
                    this.dragNode(data);
                    break;
            }
            // 刷新左侧树，选中第一个数据
            if (type !== '5') this.initTreeData('1');
        },
        async addTask(data,name){
                let obj ={
                    themeName: name,
                    parentThemeId: ''
                }
                if (this.type === '1') {
                    obj.parentThemeId = data.pkId;
                } else {
                    obj.parentThemeId = data.parentThemeId;
                }
                try {
                    const p = this.$api.MccTaskConfigApi.infoSave(obj);
                    const resp = await this.$app.blockingApp(p);
                    if(resp.success){
                        this.$message.success('新增成功！');
                        this.reloadTree();
                    }else {
                        this.$message.error(resp.message || '新增失败！');
                    }
                } catch (e) {
                    this.$message.error('新增失败！');
                }
        },
        dragColumnUpdate() {
            this.$refs.complexWidget.style.height = '100%';
        },
        // 表格审核操作
        async taskExamine(params){
            let data = [];
            if (params === 'examine') {
                data = await this.$refs.taskGrid.getSelectedRows();
            } else {
                data = [params.data];
            }
            if (data.length === 0) {
                this.$message.warning('请选择一条可审核的任务')
                return;
            }
            // 可审核任务
            let canCheckTaskList = data.filter(item => item.reTaskDef.taskStatus === '01')
            if(canCheckTaskList.length === 0){
                this.$message.warning('无可审核任务')
                return;
            }
            const ok = await this.$msg.ask(`确认审核选中记录?`);
            if (!ok) {
                return
            }
            try {
                let obj = canCheckTaskList.map(row => ({ taskId: row.reTaskDef.taskId,taskStatus: '08'}));
               this.checkReuslt(obj);
            } catch (e) {
                this.$message.error('审核失败');
            }
        },
        // 审核接口
       async checkReuslt(obj){
            const ok = await this.$msg.ask(`确认审核选中记录?`);
                if (!ok) {
                    return
                }
                try {
                    const p = this.$api.MccTaskConfigApi.taskChenk({taskDefs:obj});
                    const resp = await this.$app.blockingApp(p);
                    if(resp.success){
                        this.$message.success('审核成功！');
                        this.reloadData();
                    }else{
                        this.$message.error(resp.message || '审核失败');
                    }
                } catch (e) {
                    this.$message.error('审核失败');
                }
        },
        // 表格验收操作
        async taskCheck(params){
            let data = [];
            if (params === 'check') {
                data = await this.$refs.taskGrid.getSelectedRows();
            } else {
                data = [params.data];
            }
            if (data.length === 0) {
                this.$message.warning('请选择一条可验收的任务')
                return;
            }
            // 可验收任务
            let canAcceptTaskList = data.filter(item => item.reTaskDef.taskStatus === '08')
            if(canAcceptTaskList.length === 0){
                this.$message.warning('无可验收任务')
                return;
            }
            const ok = await this.$msg.ask(`确认验收选中记录?`);
            if (!ok) {
                return
            }
            try {
                let obj = canAcceptTaskList.map(row => ({ taskId: row.reTaskDef.taskId,taskStatus: '02'}));
                const p = this.$api.MccTaskConfigApi.taskAcceptance({taskDefs:obj});
                const resp = await this.$app.blockingApp(p);
                if(resp.success){
                    this.$message.success('验收成功！');
                    this.reloadData();
                }else{
                    this.$message.error(resp.message || '验收失败');
                }
            } catch (e) {
                this.$message.error('验收失败');
            }
        },
        // 发布可审核任务
        async taskCannelByPer(obj){
            const r = this.$api.MccTaskConfigApi.existTodayTask(obj);
            let res = await this.$app.blockingApp(r);
            if (res.data) {
                let flag = '-1';
                await this.$msgbox({
                    distinguishCancelAndClose: true,
                    title: '提示',
                    message: '当天任务已经生成，是否进行作废，并重新生成？',
                    showCancelButton: true,
                    confirmButtonText: '作废',
                    cancelButtonText: '不作废',
                    beforeClose: (action, instance, done) => {
                        if(action === 'confirm'){
                            flag = '1'
                        } else if(action === 'cancel'){
                            flag = '0'
                        }
                        done();
                    }
                }).then(() => {
                    if (flag === '-1') {
                        return;
                    }
                    obj[0].isCancelTodayTask = flag;
                })
            } else {
                const ok = await this.$msg.ask(`确认发布选中记录?`);
                if (!ok) {
                    return
                }
            }
            try {
                const p1 = this.$api.MccTaskConfigApi.taskPublishce(obj);
                const resp = await this.$app.blockingApp(p1);
                if(resp.success){
                    this.$message.success('发布成功！');
                    this.reloadData();
                }else{
                    this.$message.error(resp.message || '发布失败');
                }
            } catch (e) {
                this.$message.error('发布失败');
            }
        },
        // 表格发布操作（只支持单个发布）
        async taskRelease(params){
            let data = [];
            if (params === 'release') {
                data = await this.$refs.taskGrid.getSelectedRows();
            } else {
                data = [params.data];
            }
            if (data.length !== 1) {
                this.$message.warning('请选择一条可发布的任务')
                return;
            }
            // 可审核任务
            let canPublishTaskList = data.filter(item => item.reTaskDef.taskStatus === '09' || item.reTaskDef.taskStatus === '02')
            if (canPublishTaskList.length === 0) {
                this.$message.warning('无可发布任务');
                return;
            }
            const p = this.$api.MccTaskConfigApi.selectTaskCaseBody(canPublishTaskList[0].caseDefId)
            let rep = await this.$app.blockingApp(p);
            let caseDefBody = rep.data.caseDefBody;
            let obj = [
                {
                    reTaskDef: canPublishTaskList[0].reTaskDef,
                    caseDefId: canPublishTaskList[0].caseDefId,
                    caseDefBody: caseDefBody,
                }
            ]
            //今日任务已实例化，让用户确认是否需要作废当天的任务
           this.taskCannelByPer(obj);
        },
        // 表格回退操作
        async taskReturn(params){
            let data = [];
            if (params === 'return') {
                data = await this.$refs.taskGrid.getSelectedRows();
            } else {
                data = [params.data];
            }
            if (data.length === 0) {
                this.$message.warning('请至少选择一条可回退的任务')
                return;
            }
            // 可回退任务
            let canReturnTaskList = data.filter(item => item.reTaskDef.taskStatus === '09' || item.reTaskDef.taskStatus === '02' || item.reTaskDef.taskStatus === '08')
            if(canReturnTaskList.length === 0){
                this.$message.warning('无可回退任务')
                return;
            }
            const ok = await this.$msg.ask(`确认回退选中记录?`);
            if (!ok) {
                return
            }
            try {
                let obj = canReturnTaskList.map(row => ({ taskId: row.reTaskDef.taskId,taskStatus: '01'}));
                const p = this.$api.MccTaskConfigApi.taskBack({taskDefs:obj});
                const resp = await this.$app.blockingApp(p);
                if(resp.success){
                    this.$message.success('回退成功！');
                    this.reloadData();
                }else{
                    this.$message.error(resp.message || '回退失败');
                }
            } catch (e) {
                this.$message.error('回退失败');
            }
        },
        // 表格停用操作
        async taskStop(params){
            let data = [];
            if (params === 'stop') {
                data = await this.$refs.taskGrid.getSelectedRows();
            } else {
                data = [params.data];
            }
            if (data.length === 0) {
                this.$message.warning('请至少选择一条可停用的任务')
                return;
            }
            // 可审核任务
            let canStopTaskList = data.filter(item => item.reTaskDef.taskStatus === '03')
            if(canStopTaskList.length === 0){
                this.$message.warning('无可停用任务')
                return;
            }
            const ok = await this.$msg.ask(`确认停用选中记录?`);
            if (!ok) {
                return
            }
            try {
                let obj = canStopTaskList.map(row => ({ taskId: row.reTaskDef.taskId}));
                const p = this.$api.MccTaskConfigApi.taskStop({taskDefs:obj});
                const resp = await this.$app.blockingApp(p);
                if(resp.success){
                    this.$message.success('停用成功！');
                    this.reloadData();
                }else{
                    this.$message.error(resp.message || '停用失败');
                }
            } catch (e) {
                this.$message.error('停用失败');
            }
        },
        // 表格删除操作
        async taskDelete(params){
            let data = [];
            if (params === 'delete') {
                data = await this.$refs.taskGrid.getSelectedRows();
            } else {
                data = [params.data];
            }

            if (data.length === 0) {
                this.$message.warning('请至少选择一条可删除的任务')
                return;
            }
            // 可删除任务
            let canDtlTaskList = data.filter(item => item.reTaskDef.taskStatus === '01' || item.reTaskDef.taskStatus === '09')
            if(canDtlTaskList.length === 0){
                this.$message.warning('无可删除任务')
                return;
            }
            const ok = await this.$msg.ask(`确认删除选中记录?`);
            if (!ok) {
                return
            }
            try {
                let obj = canDtlTaskList.map(row => ({ taskId: row.reTaskDef.taskId,isDel:'1'}));
                const p = this.$api.MccTaskConfigApi.taskDel({taskDefs:obj});
                const resp = await this.$app.blockingApp(p);
                if(resp.success){
                    this.$message.success('删除成功！');
                    this.reloadData();
                }else{
                    this.$message.error(resp.message || '删除失败');
                }
            } catch (e) {
                this.$message.error('删除失败');
            }
        },
        // 表格积木绑定操作
        async buildBind(params){
            this.pageJump('agnes.block.binding',params.data, this.reloadData(),'', 'view')
        },
        dbClickData(params){
            this.pageJump('agnes.work.flow.details',params.data, this.reloadData.bind(this),'', 'details', params.data.reTaskDef.addType)
        },
        async exportData(){
          let rows = this.$refs.taskGrid.getSelectedRows();
          let caseKeys = rows.map(row => row.reTaskDef.caseKey);
          const ok = await this.$msg.ask(`确认导出吗, 是否继续?`);
          if (ok) {
            axios({
              method: "post",
              url: "api/agnes-ac/v1/ac/taskdef/taskExport",
              data: caseKeys,
              responseType: "blob" // 确保返回的是二进制数据
            }).then(res => {
              // 创建 Blob 对象，指定 MIME 类型为 application/zip
              let blob = new Blob([res.data], { type: "application/zip" });
              // 提取文件名
              let fileName = 'exported_file.zip'; // 默认文件名
              if (res.headers['content-disposition']) {
                const match = res.headers['content-disposition'].match(/filename="(.+)"/);
                if (match && match[1]) {
                  fileName = decodeURI(match[1]); // 提取后端返回的文件名
                }
              }
              // 创建 <a> 标签并触发下载
              const link = document.createElement("a");
              link.style.display = "none";
              link.href = URL.createObjectURL(blob); // 创建临时 URL
              link.setAttribute("download", fileName); // 设置下载文件名
              document.body.appendChild(link);
              link.click();
              // 清理
              document.body.removeChild(link);
              setTimeout(() => window.URL.revokeObjectURL(link.href), 200); // 释放临时 URL
            }).catch(() => {
              this.$message.error("导出失败")
            });
          }
        },
        importData(){
          if(this.queryArgs.themeId === ""){
            this.$message.warning('请在左侧选择任务主题!')
            return;
          }
          this.$nav.showDialog(
              leadIntoList,
              {
                width: '30%',
                title: '场景导入',
                args: {
                  themeId:this.queryArgs.themeId,
                  actionOk: this.reloadData.bind(this)
                }
              }
          )
        }
    }
}
</script>

<style scoped>

.return-button {
    display: flex;
    justify-content: flex-end;
    top: 10px;
    right: 10px;
    height: 40px;
}

.case-scene {
    display: flex;
    flex-direction: row !important;
}

.case-scene .ag-grid-box {
    padding-left: 8px;
}

.case-scene-right{
    flex: 1;
    display: flex;
    flex-direction: column;
}

.case-scene-right-top{
    width: 100%;
    height: 44px;
    border-bottom: 1px solid #DAE0E9;
}

.complex-widget {
    width: 300px;
}

.complex-widget .content-container .container {
    height: calc(100% - 50px);
    margin-left: 10px;
    color: #0f5eff;
}

.new-special-drawer>.el-drawer__container>.el-drawer>#el-drawer__title .drawer-title {
    display: block;
}

.inputDeep>>>.el-input__inner {
    border: 0;
}

.view-permission{
    margin-left: 8px;
    width: 300px;
    height: 100%;
    padding: 10px 20px ;
    border: 1px solid #EAEBED;
}

.add-btn{
    color: #0f5eff;
    font-size: 12px;
    border: 1px solid #0f5eff;
    border-radius: 4px;
    background: transparent;
    padding: 6px 10px;
    margin-bottom: 10px;
}
/* 表格样式 竖向滚动条*/
.case-scene-right /deep/ .ag-body-viewport.ag-layout-normal::-webkit-scrollbar{
  display: block !important;
}
</style>
