<template>
  <div>
      <el-form class="fit-box" :model="form" ref="form" :rules="rules" label-width="80px" style="padding: 10px;">
        <el-form-item label="方案名称" prop="planName" style="width: 100%">
          <gf-input type="text" :max-byte-len="80" v-model="form.planName"/>
        </el-form-item>
        <el-form-item label="任务类型" prop="taskType">
          <el-select class="multiple-select" v-model="form.taskType" @change="initBlockData(form)" filterable clearable placeholder="请选择">
              <gf-filter-option
                  v-for="item in planList1"
                  :key="item.dictId"
                  :label="item.dictName"
                  :value="item.dictId">
              </gf-filter-option>
          </el-select>
        </el-form-item>
        <el-form-item label="所属岗位" prop="jobBlon" style="width: 100%">
          <el-select class="treeSelect" v-model="form.jobBlon" filterable clearable :disabled="form.taskType === '1'"
            @change="handleSelectChange" placeholder="请选择">
              <gf-filter-option
                  v-for="item in planList2"
                  :key="item.userGroupId"
                  :label="item.userGroupName"
                  :value="item.userGroupId">
              </gf-filter-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否默认" prop="isDefault" style="width: 100%">
          <el-select class="multiple-select" v-model="form.isDefault" clearable placeholder="请选择">
            <gf-filter-option
                v-for="item in planList3"
                :key="item.dictId"
                :label="item.dictName"
                :value="item.dictId">
            </gf-filter-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" style="width: 100%">
          <gf-input v-model="form.remark" :max-byte-len="150" placeholder="请输入备注"></gf-input>
        </el-form-item>
      </el-form>
      <dialog-footer :ok-button="mode !== 'view'" :on-save="onSave" ok-button-title="保存"></dialog-footer>
  </div>
</template>

<script>
export default {
  props: {
      mode: {
          type: String,
          default: 'add'
      },
      row: Object,
      actionOk: Function
  },
  data() {
      return {
        form: {
          'userGroupId': '',
          'planName': '',
          'taskType': '',
          'jobBlon': '',
          'isDefault': '0',
          'userGroupName': '',
          'remark': '',
        },
        rules: {
          'planName': [{required: true, message: "请输入方案名称", trigger: 'blur'}],
          'taskType': [{required: true, message: "请选择任务类型", trigger: 'blur'}],
          'isDefault': [{required: true, message: "请选择是否默认", trigger: 'blur'}],
        },
        planList1:[],
        planList2:[],
        planList3:[],
        tag: false,
      }
  },
  mounted() {
      this.planList1 = this.$app.dict.getDictItems('OMS_PLAN_TYPE');
      this.planList3 = this.$app.dict.getDictItems('OMS_DEF_TYPE');
      if(this.mode === 'edit'){
          this.form = this.row;
          this.initBlockData(this.form)
          this.tag = true;
      }
  },
  methods: {
      handleSelectChange(value){
          const selectedItem = this.planList2.find(item => item.userGroupId === value);
          this.form.userGroupName = selectedItem ? selectedItem.userGroupName : '';
      },
      async initBlockData(item) {
              if(this.tag){
                  this.form.jobBlon = '';
                  this.planList2 = [];
              }
              if (item.taskType === '1') { //产品任务不允许选岗位
                this.form.jobBlon = '';
                this.form.userGroupName = '';
                return;
              }
              let obj = {
                  'isProductTask': item.taskType === '1' ? '1' : '',
                  'status': '1',
              };
              // getJobList
              const p = this.$api.MccTaskConfigApi.getJobList(obj);
              const resp = await this.$app.blockingApp(p);
              if (resp && resp.data) {
                  this.planList2 = resp.data;
              }
      },
      async onSave() {
              const ok = await this.$refs['form'].validate();
              if (!ok) {
                  return;
              }
              try {
                  const p = this.$api.taskNoticeApi.saveList(this.form);
                  const resp = await this.$app.blockingApp(p);
                  if(resp.success){
                      if (this.actionOk) {
                          await this.actionOk();
                      }
                      this.$msg.success(this.mode === 'add' ? '保存成功' : '编辑成功');
                      this.$dialog.close(this);
                  }else{
                      this.$message.error(resp.message || '保存失败');
                  }
              } catch (reason) {
                  this.$msg.error(reason);
              }
      },
  }
}
</script>
<style lang="less" scoped>
/deep/.el-tree .el-tree-node__label {
font-weight: lighter;
color: #606266;
}
/deep/.treeSelect.el-select-dropdown__item {
padding: 0px;
}
</style>
