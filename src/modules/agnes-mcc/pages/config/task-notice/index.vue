<template>
    <div class="notice-programme">
        <div class="return-button" v-if="isShow &&$app.dict.getDictName('AGNES_CJHX_BUTTON', 'cancelBtn') == 'true'">
            <gf-button class="add-btn" size="mini" @click="jumpPage">返回</gf-button>
        </div>
        <p>任务通知方案</p>
        <div class="notice-center">
            <gf-button class="add-btn" size="mini" v-if="$hasPermission('agnes.task.notice.planAdd')" @click="notificationPlan">新增</gf-button>
            <div class="notice-table" style="overflow-x: auto;">
                <el-table ref="singleTable" border :data="form.programmeList" highlight-current-row @current-change="handleCurrentChange" :header-cell-style="headerCellStyle" :row-style="rowStyle" height="300px" style="width: 100%;">
                    <el-table-column label="操作" width="100" fixed>
                        <template v-slot:default="{ $index,row }">
                            <el-button class="option-cell" type="text" v-if="$hasPermission('agnes.task.notice.planDel')" @click.stop="delTableItem('notice',$index,row)">
                                <span>删除</span>
                            </el-button>   
                            <el-button class="option-cell" type="text" v-if="$hasPermission('agnes.task.notice.planEdit')" @click.stop="editNotPlan($index)">
                                <span>编辑</span>
                            </el-button>   
                        </template>
                    </el-table-column>
                    <el-table-column prop="planName" width="240">
                        <template slot="header">
                            方案名称<span class="required">*</span>
                        </template>
                        <template v-slot:default="{ row }">
                            {{row.planName}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="taskType" width="190">
                        <template slot="header">
                            任务类型<span class="required">*</span>
                        </template>
                        <template v-slot:default="{row}">
                            {{getType(row.taskType)}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="jobBlon" width="190">
                        <template slot="header">
                            所属岗位
                        </template>
                        <template v-slot:default="{row}">
                            {{row.userGroupName}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="isDefault" width="100">
                        <template slot="header">
                            是否默认<span class="required">*</span>
                        </template>
                        <template v-slot:default="{row}">
                            {{getDefault(row.isDefault)}}
                        </template>
                    </el-table-column>
                    <el-table-column prop="remark">
                        <template slot="header">
                            备注
                        </template>
                        <template v-slot:default="{row}">
                            {{row.remark}}
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
        <div class="notice-line"></div>
        <p>通知规则</p>
        <div class="notice-center">
            <gf-button class="add-btn" size="mini" v-if="$hasPermission('agnes.task.notice.ruleAdd')" @click="addTasklist">新增</gf-button>
            <div class="notice-table" style="overflow-x: auto;">
                <el-table :data="form.ruleList" border :header-cell-style="headerCellStyle" height="300px" style="width: 100%;">
                    <el-table-column label="操作" width="190" fixed>
                        <template v-slot:default="{ $index,row }">
                            <el-button class="option-cell" type="text" v-if="$hasPermission('agnes.task.notice.ruleDel')" @click="delTableItem('rule',$index,row)">
                                <span>删除</span>
                            </el-button>   
                            <el-button class="option-cell" type="text" v-if="$hasPermission('agnes.task.notice.ruleEdit')" @click="editOrSave($index)">
                                <span>{{ isEditing($index) ? '保存' : '编辑' }}</span>
                            </el-button>   
                            <el-button class="option-cell" :class="{ 'is-disabled': row.editable }" :disabled="row.editable" type="text" v-if="$hasPermission('agnes.task.notice.ruleNoticeType')" @click="noticeMethod($index)">
                                <span>通知方式</span>
                            </el-button>
                        </template>
                    </el-table-column>
                    <el-table-column  prop="notifyRule" width="190">
                        <template slot="header">
                            通知对象<span class="required">*</span>
                        </template>
                        <template v-slot:default="{row}">
                            <el-select clearable v-model="row.notifyRule" :disabled="!row.editable">
                                <el-option v-for="item in planList4" :key="item.fnId"
                                    :label="item.fnName" :value="item.fnId">
                                </el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column prop="standard" width="190">
                        <template slot="header">
                            基准<span class="required">*</span>
                        </template>
                        <template v-slot:default="{row}">
                            <el-select clearable v-model="row.standard" :disabled="!row.editable" @change="ruleCondition(row)">
                                <el-option v-for="item in planList5" :key="item.dictId"
                                    :label="item.dictName" :value="item.dictId">
                                </el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column prop="conditionT" width="190">
                        <template slot="header">
                            条件<span class="required">*</span>
                        </template>
                        <template v-slot:default="{row}">
                            <el-select clearable v-model="row.conditionT" :disabled="!row.editable">
                                <el-option v-for="item in row.taskConfig" :key="item.dictId"
                                    :label="item.dictName" :value="item.dictId">
                                </el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column prop="offsetT" width="190">
                        <template slot="header">
                            偏移<span class="required">*</span>
                        </template>
                        <template v-slot:default="{row}">
                            <gf-input :disabled="!row.editable" :max-byte-len="2" v-model="row.offsetT" placeholder="请输入偏移量"></gf-input>
                        </template>
                    </el-table-column>
                    <el-table-column prop="unit" width="190">
                        <template slot="header">
                            单位<span class="required">*</span>
                        </template>
                        <template v-slot:default="{row}">
                            <el-select clearable v-model="row.unit" :disabled="!row.editable">
                                <el-option v-for="item in planList7" :key="item.dictId"
                                    :label="item.dictName" :value="item.dictId">
                                </el-option>
                            </el-select>
                        </template>
                    </el-table-column>
                    <el-table-column prop="remark">
                        <template slot="header">
                            说明
                        </template>
                        <template v-slot:default="{row}">
                            <gf-input :disabled="!row.editable" :max-byte-len="150" v-model="row.remark" placeholder="请输入说明"></gf-input>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
        </div>
    </div>
</template>
<script>
import addEditDig from "./add-edit-dlg.vue";
export default {
    props:{
        isShow:{
            default:false,
            type:Boolean
        }
    },
    data() {
        return {
            form: {
                programmeList:[],
                ruleList:[],
            },
            planList1:[],
            planList3:[],
            planList4:[],
            planList5:[],
            planList7:[],
            configList:{},
            pkId:'',
        }
    },
    beforeMount() {
        this.getFnDefList();
    },
    async mounted() {
        // 获取列表字典值
        this.planList1 = this.$app.dict.getDictItems('OMS_PLAN_TYPE');
        this.planList3 = this.$app.dict.getDictItems('OMS_DEF_TYPE');
        this.planList5 = this.$app.dict.getDictItems('OMS_BENCHMARK');
        this.planList7 = this.$app.dict.getDictItems('OMS_COMPANY');
        await this.reloadData();
        if(this.form.programmeList.length==0) {
            return
        }
        if(this.form.programmeList[0].configId){
            this.setCurrent(this.form.programmeList[0])
        }
    },
    methods: {
        jumpPage(){
            let viewId = 'agnes.task.notice';
            this.$agnesUtils.closeTab(viewId);
        },
        setCurrent(row) {
            this.$refs.singleTable.setCurrentRow(row);
        },
        handleCurrentChange(val) {
            if(val.configId){
                this.configList = val;
                this.reloadRuleData(val.configId);
            }
        },
        // 新增
        notificationPlan(){
            this.showDlg('add',{},'任务通知方案-新增',this.reloadData);
        },
        // 编辑
        editNotPlan(index){
            let row = JSON.parse(JSON.stringify(this.form.programmeList[index]));
            this.showDlg('edit',row,'任务通知方案-编辑',this.reloadData);
        },
        getType(type){
            if(type === '1'){
                return '产品任务'
            }else{
                return '岗位任务'
            }
        },
        getDefault(type){
            if(type === '0'){
                return '否'
            }else{
                return '是'
            }
        },
        showDlg(mode, row, title, actionOk) {
            this.$nav.showDialog(
                addEditDig,
                {
                    args: {row, mode, actionOk},
                    width: '40%',
                    title: title
                }
            );
        },
        async getFnDefList(){
            try {
                const p = this.$api.MccTaskAllocationApi.getFnNotList();
                const resp = await this.$app.blockingApp(p);
                if (resp.data) {
                this.planList4 = resp.data;      
                }
            }catch (resp){
                this.$message.error(resp)
            }
        },
        async ruleCondition(item) {
            if(item.standard === ''){
                this.form.ruleList.conditionT = '';
            }else if(item.standard === '2'){
            item.taskConfig =  [{dictId:'1',dictName:'提前'},{dictId:'2',dictName:'延后'}];
            }else{
               item.taskConfig = [{dictId:'2',dictName:'延后'}];
            }
        },
        rowStyle() {
            return 'border-bottom: 1px dashed #ccc;';
        },
        //任务通知方案刷新表格数据
        async reloadData(){           
            try {
                const p = this.$api.taskNoticeApi.getProgramme();
                const resp = await this.$app.blockingApp(p);
                if(resp.success){
                    this.form.programmeList = resp.data;
                    this.setCurrent(this.form.programmeList[0]);
                    this.handleCurrentChange(this.form.programmeList[0]);
                }
            } catch (e) {
                this.$message.error(e)
            }
            return Promise.resolve()
        },
        // 刷新通知规则表格数据
        async reloadRuleData(data){
            let obj = {
                configId: data,
            };
            try {
                const p = this.$api.taskNoticeApi.getRule(obj);
                const resp = await this.$app.blockingApp(p);
                if(resp.success){
                    this.form.ruleList = resp.data.map(item => ({
                        ...item,
                        taskConfig:JSON.parse(item.taskConfig),
                        editable: false,
                    }))
                }
            }catch (e) {
                this.$message.error(e)
            }
        },
        headerCellStyle() {
          return{ 
            background: '#F5F9FE',
            borderBottom: '1px dashed #D9DFEA'
          }
        },
        // 新增操作
        async addTasklist(){
            const newTask = {notifyRule:'',standard:'2',conditionT:'2',offsetT:'0',unit:'1',remark:'',editable: true,taskConfig:[{dictId:'1',dictName:'提前'},{dictId:'2',dictName:'延后'}]};
            this.form.ruleList.push(newTask);
        },
        // 删除不存在pkId的规则
        delTableNoPkId(type,index){
            if(type === 'notice'){
                this.form.programmeList.splice(index, 1);
                this.reloadData();
            }else{
                this.form.ruleList.splice(index, 1);
            }
            this.$message.success('删除成功！');
        },
        // 删除存在pkId的规则
        async delTablePkId(type,row){
            try {
                let p;
                let successMessage;
                let reloadFunction;
                if (type === 'notice') {
                p = this.$api.taskNoticeApi.delList({ pkId: row.pkId });
                successMessage = '删除成功！';
                reloadFunction = this.reloadData;
                } else {
                p = this.$api.taskNoticeApi.delRule({ pkId: row.pkId });
                successMessage = '删除成功！';
                reloadFunction = () => {
                    this.setCurrent(this.configList);
                    this.handleCurrentChange(this.configList);
                };
                }
                
                const resp = await this.$app.blockingApp(p);
                if (resp.success) {
                this.$message.success(resp.message || successMessage);
                reloadFunction();
                } else {
                this.$message.error(resp.message || '删除失败');
                }
            } catch (e) {
                this.$message.error(e);
            }
        },
        // 删除操作
        async delTableItem(type,index,row) {
            const ok = await this.$msg.ask(`确认删除选中记录?`);
            if (!ok) {
                return;
            }
            if(!row.pkId){
               this.delTableNoPkId(type,index);
            }else{
              this.delTablePkId(type,row);
            }
        },
        // 保存操作
        async saveTableItem(index){
                let row = {
                    configId: this.configList.configId || '',
                    ...this.form.ruleList[index]
                }
                if (!row.notifyRule || !row.standard || !row.conditionT || !row.offsetT || !row.unit) {
                this.$message.warning('存在必填项未填写！');
                    return;
                }
                try {
                    const p = this.$api.taskNoticeApi.saveRule(row);
                    const resp = await this.$app.blockingApp(p);
                    if(resp.success){
                        this.$message.success(resp.message ||'保存成功！');
                        this.form.ruleList[index].editable = false;
                        this.setCurrent(this.configList);
                        this.handleCurrentChange(this.configList);
                    }else{
                        this.$message.error(resp.message || '保存失败');
                    }
                } catch (e) {
                    this.$message.error(e)
                } 
        },
        editTableItem(index){
            this.form.ruleList[index].editable = !this.form.ruleList[index].editable;
        },
        editOrSave(index) {
            if (this.isEditing(index)) {
                this.saveTableItem(index);
            } else {
                this.editTableItem(index);
            }
        },
        isEditing(index) {
            return this.form.ruleList[index].editable;
        },
        async noticeMethod(index){
            let remindProp = []
            this.pkId = this.form.ruleList[index].pkId;
            try {
                let obj = {
                    pkId: this.pkId,
                }
                const p = this.$api.taskNoticeApi.getEmailInfo(obj);
                const resp = await this.$app.blockingApp(p);
                if(resp.success){
                    remindProp = JSON.parse(resp.data.email).remindProp;
                }
            } catch (e) {
                    this.$message.error(e)
            }
            this.$nav.showDialog(
                'remind-rule',
                {
                    args: {remindProp:remindProp,actionOk:this.showRemind.bind(this)},
                    width: '600px',
                    title: "通知方式",
                }
            );
        },
        async showRemind(remindProp){
           try {
            let obj = {
                pkId: this.pkId,
                notifyContent:{remindProp},
            }
                const p = this.$api.taskNoticeApi.saveEmailInfo(obj);
                const resp = await this.$app.blockingApp(p);
                if(resp.success){
                    this.$message.success(resp.message ||'保存成功！');
                }else{
                    this.$message.error(resp.message || '保存失败');
                }
           } catch (e) {
                this.$message.error(e)
           }
        },
    }
}
</script>
<style lang="less" scoped>
.notice-programme {
    p {
        position: relative;
        height: 40px;
        display: flex;
        align-items: center;
        color: #000000;
        font-family: "Source Han Sans CN", sans-serif;
        font-size: 14px;
        font-style: normal;
        font-weight: 500;
        line-height: normal;
        padding-left: 10px;
        &::before {
            position: absolute;
            content: '';
            display: block;
            width: 2px;
            height: 14px;
            background: #3580F8;
            left: 0;
            top: calc(50% - 6px);
        }
    }
}

.return-button {
    display: flex;
    justify-content: flex-end;
    top: 10px;
    right: 10px;
    height: 40px;
}

.notice-center{
    position: relative;
    height: 350px;
    margin-top: 5px;
}

.notice-table ::v-deep .el-table thead {
    color: #5e5e5e;
    font-family: "Source Han Sans CN", sans-serif;
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.required {
    color: red;
}

.el-button--text{
    color: #3387f3;
    font-family: "Source Han Sans CN", sans-serif;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
}

.flex-qz{
    display: flex;
    align-items: center;
}

.notice-line{
    margin:10px 0;
    border: 1px dashed #E6EBF5;
}
.add-btn{
    color: #0f5eff;
    font-size: 12px;
    border: 1px solid #0f5eff;
    border-radius: 4px;
    background: transparent;
    padding: 6px 10px;
    margin-bottom: 10px;
}
</style>