<template>
  <div style="padding: 18px;">
    <el-form :model="form" ref="form" label-width="130px" size="mini">
      <el-form-item label="选择维度" prop="dimId" :rules="[{ required: true, message: '维度必填', trigger: 'change' }]">
        <el-select v-model="form.dimId" clearable filterable style="width: 480px;" @change="dimOnChange">
          <el-option v-for="item in dimOpts" :key="item.dimId" :label="item.dimName" :value="item.dimId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择考核周期" prop="checkCycle" :rules="[{ required: true, message: '考核周期必填', trigger: 'change' }]">
        <el-select v-model="form.checkCycle" clearable filterable style="width: 480px;">
          <el-option v-for="item in opts(cycleType)" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="选择考核方式" prop="checkType" :rules="[{ required: true, message: '考核方式必填', trigger: 'change' }]">
        <el-radio-group v-model="form.checkType" @change="typeOnChange">
          <el-radio label="1">按佣金比例</el-radio>
          <el-radio label="2">按佣金量</el-radio>
          <el-radio label="3">按佣金比例区间</el-radio>
          <el-radio label="4">按佣金量区间</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="设置考核目标">
        <el-table header-row-class-name="rule-header-row"
          header-cell-class-name="rule-header-cell"
          row-class-name="rule-row"
          cell-class-name="rule-cell"
          :data="form.targetList"
          border stripe
          style="width: 100%">
          <el-table-column prop="qsPk" label="券商名称">
            <template slot-scope="scope">
              <el-select v-model="scope.row.qsPk" clearable filterable style="width: 100%;">
                <el-option v-for="item in fmtTargets(scope)" :key="item.qsPk" :label="item.qsName" :value="item.qsPk"></el-option>
              </el-select>
            </template>
          </el-table-column>
          <template v-if="form.checkType === '1'">
            <el-table-column prop="targetLowerLimit" label="佣金比例(%)">
              <template slot-scope="scope">
                <el-input v-model="scope.row.targetLowerLimit" @input="value => handleInput(value, scope.$index, 'targetLowerLimit')"></el-input>
              </template>
            </el-table-column>
          </template>
          <template v-if="form.checkType === '2'">
            <el-table-column prop="targetLowerLimit" label="佣金量">
              <template slot-scope="scope">
                <gf-input-money v-model="scope.row.targetLowerLimit" :max-byte-len="10" />
              </template>
            </el-table-column>
          </template>
          <template v-if="form.checkType === '3'">
            <el-table-column prop="targetLowerLimit" label="佣金比例下限(%)">
              <template slot-scope="scope">
                <el-input v-model="scope.row.targetLowerLimit" @input="value => handleInput(value, scope.$index, 'targetLowerLimit')"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="targetUpperLimit" label="佣金比例上限(%)">
              <template slot-scope="scope">
                <el-input v-model="scope.row.targetUpperLimit" @input="value => handleInput(value, scope.$index, 'targetUpperLimit')"></el-input>
              </template>
            </el-table-column>
          </template>
          <template v-if="form.checkType === '4'">
            <el-table-column prop="targetLowerLimit" label="佣金量下限">
              <template slot-scope="scope">
                <gf-input-money v-model="scope.row.targetLowerLimit" :max-byte-len="10" />
              </template>
            </el-table-column>
            <el-table-column prop="targetUpperLimit" label="佣金量上限">
              <template slot-scope="scope">
                <gf-input-money v-model="scope.row.targetUpperLimit" :max-byte-len="10" />
              </template>
            </el-table-column>
          </template>
          <el-table-column prop="remark" label="备注">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="60">
            <template slot-scope="scope">
              <el-button type="text" @click="onTagetDel(scope.$index)">
                <span style="color: #FA6A6A;">删除</span>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="target-table-btn">
          <gf-button class="action-btn" @click="targetOnAdd">新增</gf-button>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  props: {
    row: { type: Object, default: null },
    actionOk: Function
  },
  data () {
    return {
      form: {
        checkType: '1',
        dimId: '',
        checkCycle: '',
        targetList: []
      },
      cycleType: '',
      dimOpts: [],
      targets: [],
      allTargets: []
    }
  },
  computed: {
    opts () {
      return function (type) {
        if (type) {
          if (type === '2') {
            let result = [];
            let curYear = new Date().getFullYear();
            for (let i = 0; i < 10; i++) {
              result.push(`${curYear + i}年`);
            }
            return result;
          } else if (type === '1') {
            let result = [];
            let t = new Date();
            for (let i = 0; i < 10; i++) {
              let fDate = this.$dateUtils.addSeason(t, i);
              result.push(`${fDate[0].getFullYear()}年${this.$dateUtils.getSeason(fDate[0])}季度`);
            }
            return result;
          } else {
            return []
          }
        }
        return []
      }
    },
    fmtTargets () {
      let _t = this;
      return function (scope) {
        let qsPks = [];
        _t.form.targetList.forEach(item => {
          if (item.qsPk) { qsPks.push(item.qsPk) }
        });

        if (scope.row.qsPk) {
          let i = qsPks.findIndex(item => item == scope.row.qsPk)
          qsPks.splice(i, 1);
        }
        return _t.allTargets.filter(item => !qsPks.includes(item.qsPk))
      }
    }
  },
  async mounted() {
    try {
      // 获取所有的券商
      await this.fetchAllTargets();
      // 获取维度
      await this.fetchDimSelections();
      // 修改 获取当前维度下的券商
      if (this.row && this.row.pkId) {
        const res2 = await this.$api.objectivesApi.queryDimQs(this.row.dimId);
        if (res2.data && res2.data.length) {
          this.targets = res2.data;
        }
        // 获取维度下的考核方式 按年 按季
        let c = this.dimOpts.find(v => v.dimId === this.row.dimId);
        this.cycleType = c ? c.cycleType : '';
        // 获取详情
        const res = await this.$api.objectivesApi.queryDetailById(this.row.pkId);
        if (res.data) {
          this.form = res.data;
        }
      }
    } catch (error) {
      Promise.reject(error);
    }
  },
  methods: {
    async fetchAllTargets() {
      const res11 = await this.$api.TransactionUnitApi.queryBank({ extOrgType: '2700' });
      if (res11.code === '********' && Array.isArray(res11.data) && res11.data.length) {
        this.allTargets = res11.data.map(v => ({ qsName: v.extOrgName, qsPk: v.extOrgId }));
      }
    },
    async fetchDimSelections() {
      const resp = await this.$api.objectivesApi.queryDimSelections({});
      if (resp.data && resp.data.length) {
        this.dimOpts = resp.data.map(item => ({ dimName: item.dimName, dimId: item.pkId, cycleType: item.cycleType }));
      }
    },
    handleInput (val, index, key) {
      let value = val + '';
      value = value.replace(/[^\d.]/g, '');
      value = value.replace(/\.{2,}/g, '.');
      value = value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');

      let reg = new RegExp('^(\\d+)\\.(\\d{2}).*$')
      value = value.replace(reg, '$1.$2');
      if (value.indexOf('.') < 0 && value !== '' && value !== null) {
        value = parseFloat(value)
      }

      if (!isNaN(value) && value !== '' && value !== null) {
        if (parseFloat(value) > 100) {
          value = 100
        }
      }
      this.$set(this.form.targetList[index], key, value);
    },
    async dimOnChange(val) {
      // 置空考核周期
      this.$set(this.form, 'checkCycle', '');
      if (val) {
        try {
          // 获取维度下的考核方式 按年 按季
          let c = this.dimOpts.find(v => v.dimId === val);
          this.cycleType = c ? c.cycleType : '';
          // 获取当前维度下的券商
          const resp = await this.$api.objectivesApi.queryDimQs(val);
          if(resp.data && resp.data.length) {
            this.targets = resp.data;
            this.$set(this.form, 'targetList', resp.data.map(v => ({ qsPk: v.qsPk })))
          } else {
            this.targets = []
            this.$set(this.form, 'targetList', [])
          }
        } catch (error) {
          Promise.reject(error);
        }
      } else {
        this.targets = []
        this.$set(this.form, 'targetList', [])
      }
    },

    typeOnChange () {
      this.$set(this.form, 'targetList', this.targets.map(v => ({ qsPk: v.qsPk })))
    },

    targetOnAdd() {
      if (!this.form.dimId) {
        this.$message.error('请选择维度！！');
        return;
      }
      this.form.targetList.push({})
    },

    onTagetDel (i) {
      this.form.targetList.splice(i, 1);
    },

    onCancel() {
      this.$emit('onClose');
    },

    targetListOnValidate () {
      if (this.form.targetList.length === 0) {
        this.$message.error('考核目标必填');
        return false;
      }
      if (this.form.targetList.some(v => !v.qsPk)) {
        this.$message.error('考核目标券商名称必填');
        return false;
      }
      if (this.form.checkType === '1') {
        if (this.form.targetList.some(v => !v.targetLowerLimit)) {
          this.$message.error('考核目标佣金比例必填');
          return false;
        }
      }
      if (this.form.checkType === '2') {
        if (this.form.targetList.some(v => !v.targetLowerLimit)) {
          this.$message.error('考核目标佣金量必填');
          return false;
        }
      }
      if (this.form.checkType === '3') {
        this.thirdOnChange();
      }
      if (this.form.checkType === '4') {
        this.fourthOnChange();
      }
      return true;
    },
    thirdOnChange(){
      if (this.form.targetList.some(v => !v.targetLowerLimit)) {
        this.$message.error('考核目标佣金比例下限必填');
        return false;
      }
      if (this.form.targetList.some(v => !v.targetUpperLimit)) {
        this.$message.error('考核目标佣金比例上限必填');
        return false;
      }
      if (this.form.targetList.some(v => parseFloat(v.targetLowerLimit) > parseFloat(v.targetUpperLimit))) {
        this.$message.error('考核目标佣金比例上限需大于佣金比例下限');
        return false;
      }
    },
    fourthOnChange(){
        if (this.form.targetList.some(v => !v.targetLowerLimit)) {
          this.$message.error('考核目标佣金量下限必填');
          return false;
        }
        if (this.form.targetList.some(v => !v.targetUpperLimit)) {
          this.$message.error('考核目标佣金量上限必填');
          return false;
        }
        if (this.form.targetList.some(v => parseFloat(v.targetLowerLimit) > parseFloat(v.targetUpperLimit))) {
          this.$message.error('考核目标佣金量上限需大于佣金量下限');
          return false;
        }
    },

    async onSave() {
      try {
        const valid = await this.$refs.form.validate();
        if (valid && this.targetListOnValidate()) {
          const res = await this.$api.objectivesApi.checkExist(this.form);
          if (res.code === 'error') {
            this.$message.error(res.message);
            return;
          }
          if (res.code === '********') {
            this.successData(res);
          }
        }
      } catch (error) {
        Promise.reject(error);
      }
    },
    async successData(res){
      if (res.data) {
        await this.$msgbox({
          title: '确认消息',
          message: res.data,
          showCancelButton: true,
          confirmButtonText: '确认',
          cancelButtonText: '取消',
          customClass: 'return-visit-message-box'
        })
      }
      const p = this.$api.objectivesApi.objectivesSave({ ...this.form, cycleType: this.cycleType });
      const resp = await this.$app.blockingApp(p);
      if (resp.code === '********') {
        this.$message.success('操作成功');
        if (this.actionOk) {
          await this.actionOk();
        }
        this.$emit('onClose');
      }
    },
  }
}
</script>

<style lang="less" scoped>
.target-table-btn {
  margin-top: 8px;
  .action-btn {
    color: #0f5eff !important;
    font-size: 12px;
    border: 1px solid #0f5eff;
    border-radius: 4px;
    background: transparent;
    padding: 7px 10px !important;
  }
}
</style>
