<template>
  <module-card title="基础信息" shadow="never">
    <template slot="content">
      <div class="line">

        <el-form-item label="产品名称" prop="prdCode" :rules="[{ required: true, message: '产品名称必填', trigger: 'change' }]">
          <el-select v-model="form.prdCode" filterable clearable @change="prdOnChange" :disabled="isDisabled">
            <gf-filter-option
                    v-for="item in productList"
                    :key="item.productCode"
                    :label="`${item.productCode} - ${item.productName}`"
                    :value="item.productCode">
            </gf-filter-option>
          </el-select>
        </el-form-item>
        <el-form-item label="产品代码" prop="prdCode" :rules="[{ required: true, message: '产品代码必填', trigger: 'change' }]">
          <gf-input v-model="form.prdCode" :disabled="true" />
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="申请人" prop="applyUser" :rules="[{ required: true, message: '申请人必填', trigger: 'blur' }]">
          <gf-input v-model="form.applyUser" :disabled="isDisabled" />
        </el-form-item>
        <el-form-item label="业务发起部门" prop="baseStartDept" :rules="[{ required: true, message: '业务发起部门必填', trigger: 'change' }]">
          <gf-dict v-model="form.baseStartDept" dict-type="ACCT_BASE_START_DEPT" filterable clearable :disabled="isDisabled" />
        </el-form-item>
      </div>

      <div class="line">
        <el-form-item v-if="isShowUrgency" label="紧急度" prop="urgency" :rules="[{ required: true, message: '紧急度必填', trigger: 'change' }]">
          <el-radio-group v-model="form.urgency" :disabled=" mode ==='details'|| ['02', '03', '04', '05', '06', '07'].includes(form.acctStatus)">
            <el-radio v-for="item in urgencyList" :key="item.dictId" :label="item.dictId">{{
                                item.dictName }}</el-radio>
          </el-radio-group>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="业务描述" prop="baseDesc">
          <gf-input v-model="form.baseDesc" type="textarea" resize="none" :autosize="{minRows: 2, maxRows: 10}" :max-byte-len="200" :disabled="mode ==='details' || ['02', '03', '04', '05'].includes(form.acctStatus)" />
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="账户类型" prop="acctType" :rules="[{ required: true, message: '账户类型必填', trigger: 'change' }]">
          <el-select v-model="form.acctType" :disabled="isDisabled" @change="acctTypeOnChange">
            <el-option v-for="item in acntTypeOption" :key="item.typeCode" :label="item.typeName" :value="item.typeCode">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="开户机构" prop="openBankId" :rules="[{ required: true, message: '开户机构必填', trigger: 'change' }]">
          <el-cascader ref="cas" v-model="form.openBankId" :disabled="isDisabled"
                       :props="{checkStrictly: false, emitPath:false, value: 'extOrgId', label: 'extOrgName', children: 'children'}"
                       :options="openBankOptions"
                       @change="openOrgOnChange"
                       style="width: 100%">
          </el-cascader>
        </el-form-item>
      </div>
    </template>
  </module-card>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => ({})
    },
    isDisabled: { type: Boolean, default: false },
    isShowUrgency: { type: Boolean, default: false },
  },
  data () {
    return {
      productList: [],
      acntTypeOption: [],
      openBankOptions: [],
      urgencyList: [], //紧急度
    }
  },
  computed: {
    curAcctOpts () {
      if (this.form.acctType) {
        let cur = this.acntTypeOption.find(item => item.typeCode == this.form.acctType);
        return cur || {}
      }
      return {}
    }
  },
  watch: {
    isShowUrgency(){
      console.log('切换页面！')
    },
    curAcctOpts: {
      handler (val) {
        if (val && val.relatedDept) {
          this.$api.AcntInfoManageApi.selectOpenOrgTree(val.relatedDept).then(resp => {
            if (resp.success) {
              this.openBankOptions = resp.data;
            } else {
              this.openBankOptions = []
            }
          })
        }
      },
      deep: true
    }
  },
  async created () {
    this.urgencyList = this.$app.dict.getDictItems('ACCT_BASE_URGENCY');
    await this.getPrdList();
    try {
      const resp = await this.$api.AcntTypeMangeApi.getTypeList();
      if (resp.success) {
        if (Array.isArray(resp.data) && resp.data.length) {
          this.acntTypeOption = resp.data.map(item => {
            return {
              typeCode: item.typeCode,
              typeName: item.typeName,
              relatedForm: item.relatedForm,
              relatedDept: JSON.parse(item.relatedDept)
            }
          })
        } else {
          this.acntTypeOption = [];
        }
      } else {
        this.acntTypeOption = [];
      }
    } catch (err) {
      Promise.reject(err);
    }
    if(!this.form.urgency|| this.form.urgency === ''){
      this.form.urgency='01'
    }
  },
  methods: {
    prdOnChange (value) {
      let cur = this.productList.find(item => item.productCode === value);
      if (cur && cur.productName) {
        this.$set(this.form, 'prdName', cur.productName);
      }
    },
    acctTypeOnChange (value) {
      let cur = this.acntTypeOption.find(item => item.typeCode == value);
      this.$emit('acctTypeOnChange', cur || {})
    },
    openOrgOnChange () {
      this.$nextTick(() => {
        const [node] = this.$refs.cas.getCheckedNodes();
        this.$emit('openOrgOnChange', node);
      })
    },
    async getPrdList () {
      try {
        const resp = await this.$api.AcntApplyApi.getProductCodeList()
        if (resp.code === '00000000') {
          this.productList = resp.data
        }
      } catch (err) {
        Promise.reject(err);
      }
    },
  }
}
</script>
