<template>
  <el-form ref="form" :model="form" label-width="100px" style="padding-top: 18px;">
    <el-row>
      <el-col :span="12">
        <el-form-item label="产品名称" prop="prdCode" :rules="[{ required: true, message: '产品名称必填', trigger: 'change' }]">
          <el-select v-model="form.prdCode" filterable clearable placeholder="请选择" :disabled="sub">
            <gf-filter-option
                v-for="item in productList"
                :key="item.productCode"
                :label="`${item.productCode} - ${item.productName}`"
                :value="item.productCode">
            </gf-filter-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="产品代码">
          <el-input v-model="form.prdCode" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="账户名称" prop="acctNo" :rules="[{ required: true, message: '账户名称必填', trigger: 'change' }]">
          <el-select class="multiple-select" v-model="form.acctNo" filterable clearable placeholder="请选择" :disabled="sub">
            <el-option v-for="item in acctList" :key="item.acctNo + Math.random().toString(36)" :label="item.acctName" :value="item.acctNo"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="账号">
          <el-input v-model="form.acctNo" disabled />
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="账户类型" prop="acctType" :rules="[{ required: true, message: '账户类型必填', trigger: 'change' }]">
          <el-select class="multiple-select" v-model="form.acctType" filterable clearable placeholder="请选择" @change="onTypeCodeChange" :disabled="sub">
            <el-option v-for="item in acctTypeList" :key="item.typeCode" :label="item.typeName" :value="item.typeCode"></el-option>
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :span="12">
        <el-form-item label="开户机构" prop="acctOrg" :rules="[{ required: true, message: '开户机构必填', trigger: 'change' }]">
          <el-cascader :disabled="sub"
              style="width: 100%"
              v-model="form.acctOrg"
              :options="openBankOptions"
              :props="{ checkStrictly: false, value: 'extOrgId', emitPath: false, label: 'extOrgName', children: 'children' }"
              clearable
              filterable>
          </el-cascader>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="利率" prop="acctRateRules" :rules="[{ required: true, validator: rateRuleValidate }]">
          <gf-grid ref="grid" toolbar="" :filterRemote="false" @grid-ready="onGridReady" grid-no="acnt-rate-rule" height="25vh"></gf-grid>
          <el-button style="margin-top: 5px;" @click="ruleDlgOnAdd" size="small">新增</el-button>
        </el-form-item>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <el-form-item label="利率备注">
          <gf-input v-model="form.remark" type="textarea" resize="none" :autosize="{minRows: 2, maxRows: 10}" :max-byte-len="200" :disabled="sub" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import RuleForm from './rule-form.vue'
export default {
  props: {
    row: { type: Object, default: null },
    sub: { type: Boolean, default: false },
    actionOk: Function
  },
  data () {
    return {
      productList: [],
      acctTypeList: [],
      acctList: [],
      openBankOptions: [],
      form: {
        prdCode: '',
        prdName: '',
        acctType: '',
        acctOrg: '',
        acctNo: '',
        remark: '',
        acctRateRules: []
      }
    }
  },
  async mounted () {
    let p = Promise.all([this.getPrdList(), this.getAcctList()]);
    await this.$app.blockingApp(p);
  },
  methods: {
    async onGridReady () {
      await this.getAcctTypeList();
      if (this.row && this.row.pkId) {
        await this.querySubData({ rateId: this.row.pkId });
        if (this.form.acctType) {
          let c = this.acctTypeList.find(item => item.typeCode === this.form.acctType);
          if (c && c.relatedDept) {
            await this.getOpenOrgList(JSON.parse(c.relatedDept));
          }
        }
      }
    },
    async querySubData ({ rateId }) {
      try {
        const resp = await this.$api.AcntRateRuleApi.queryRateDetail({ rateId });
        if (resp.code === '********') {
          this.form = { ...resp.data };
          if (Array.isArray(resp.data.acctRateRules) && resp.data.acctRateRules.length > 0) {
            let r = resp.data.acctRateRules.map(item => {
              return {
                ...item,
                effectiveDate: this.$dateUtils.formatDate(item.effectiveDate, 'yyyy-MM-dd'),
                expirationDate: this.$dateUtils.formatDate(item.expirationDate, 'yyyy-MM-dd')
              }
            })
            this.form = { ...this.form, acctRateRules: r };
          } else {
            this.form = { ...this.form, acctRateRules: [] };
          }

          this.$nextTick(() => {
            this.$refs.grid && this.$refs.grid.setRowData(this.form.acctRateRules);
          })
        } else {
          this.$message({ type: 'error', message: resp.message || 'Fail！！！' });
        }
      } catch (error) {
        Promise.reject(error);
      }
    },

    rateRuleValidate (rule, value, callback) {
      if (Array.isArray(value) && value.length > 0) {
        callback()
      } else {
        callback(new Error('利率必填'))
      }
    },
    onCancel () {
      this.$emit("onClose");
    },
    async getPrdList () {
      try {
        const resp = await this.$api.AcntApplyApi.getProductCodeList()
        if (resp.code === '********') {
          this.productList = resp.data
        }
      } catch (err) {
        Promise.reject(err);
      }
    },
    async onTypeCodeChange (val) {
      this.openBankOptions = [];
      this.$set(this.form, 'acctOrg', '')
      if (val) {
        let c = this.acctTypeList.find(item => item.typeCode === val);
        if (c && c.relatedDept) {
          await this.getOpenOrgList(JSON.parse(c.relatedDept));
        }
      }
    },
    async getAcctTypeList () {
      try {
        const resp = await this.$api.AcntTypeMangeApi.getTypeList();
        if (resp.success) {
          this.acctTypeList = resp.data
        }
      } catch (err) {
        Promise.reject(err);
      }
    },
    async getAcctList () {
      try {
        const resp = await this.$api.CommonApi.queryAcctList('1iz3muldtsys1');
        if (resp.code === '********') {
          if (Array.isArray(resp.data.rows) && resp.data.rows.length > 0) {
            this.acctList = resp.data.rows.map(item => ({ acctNo: item.ACCT_NO, acctName: item.ACCT_NAME }));
          } else {
            this.acctList = [];
          }
        }
      } catch (err) {
        Promise.reject(err);
      }
    },
    async getOpenOrgList (relatedDept) {
      try {
        const resp = await this.$api.AcntInfoManageApi.selectOpenOrgTree(relatedDept);
        if (resp.success) {
          this.openBankOptions = resp.data
        }
      } catch (err) {
        Promise.reject(err);
      }
    },

    ruleDlgOnAdd () {
      this.openRateRulesDlg({ mode: '利率规则新增', row: null })
    },

    // 利率新增弹框
    openRateRulesDlg ({ mode, row, index = -1 }) {
      let _this = this;
      this.$dialog.create({
        title: mode,
        component: RuleForm,
        closeOnClickModal: false,
        width: '50%',
        args: { row },
        beforeClose: function (args, action) {
          if (action) {
            const { btnType, rows } = JSON.parse(action);
            if (btnType === 'ok') {
              _this.updateRuleData(rows, index);
            }
          }
        }
      })
    },
    updateRuleData (rows, index) {
      if (index === -1) {
        this.form.acctRateRules.push({ ...rows });
      } else {
        this.form.acctRateRules.splice(index, 1, { ...rows });
      }
      this.$nextTick(() => {
        this.$refs.grid.setRowData(this.form.acctRateRules);
        // 清除利率红色校验
        this.$refs.form.clearValidate(['acctRateRules'])
      })
    },
    onEdit (params) {
      let c = this.form.acctRateRules[params.rowIndex];
      if (c) {
        this.openRateRulesDlg({ mode: '利率规则修改', row: params.data, index: params.rowIndex })
      }
    },
    onDel (params) {
      this.form.acctRateRules.splice(params.rowIndex, 1);
      this.$nextTick(() => {
        this.$refs.grid.setRowData(this.form.acctRateRules);
      })
    },
    async onSave () {
      try {
        const valid = await this.$refs.form.validate();
        if (valid) {
          let url = this.row ? 'acntRateRuleUpdate' : 'acntRateRuleAdd';
          const p = this.$api.AcntRateRuleApi[url]({ ...this.form });
          const resp = await this.$app.blockingApp(p);
          if (resp.success) {
            this.$message({ type: 'success', message: '操作成功' });
            if (this.actionOk) {
              await this.actionOk();
            }
            this.$emit("onClose");
          } else {
            this.$message({ type: 'error', message: resp.message || resp.data.message || 'Fail！！！' });
          }
        }
      } catch(err) {
        Promise.reject(err);
      }
    }
  }
}
</script>
