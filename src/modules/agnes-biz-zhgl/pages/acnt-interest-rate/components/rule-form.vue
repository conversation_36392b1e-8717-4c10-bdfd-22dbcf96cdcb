<!--
 * @Author: <EMAIL>
 * @Date: 2024-12-26 13:46:27
 * @LastEditors: <EMAIL>
 * @LastEditTime: 2024-12-26 17:39:39
 * @Description: 
-->
<template>
  <div>
    <el-form ref="form" :model="form" label-width="100px">
      <el-form-item label="类型" prop="ruleType" :rules="[{ required: true, message: '类型必填', trigger: 'change' }]">
        <gf-dict v-model="form.ruleType" dict-type="ACCT_RATE_TYPE" filterable clearable />
      </el-form-item>
      <el-form-item label="下限(万)" prop="lowerLimit" :rules="[{ required: true, message: '下限(万)必填', trigger: 'blur' }]">
        <gf-input-money v-model="form.lowerLimit" :max-byte-len="15" />
      </el-form-item>
      <el-form-item label="上限(万)" prop="higherLimit" :rules="[{ required: true, message: '上限(万)必填', trigger: 'blur' }]">
        <gf-input-money v-model="form.higherLimit" :max-byte-len="15" />
      </el-form-item>
      <el-form-item label="利率(%)" prop="rate" :rules="[{ required: true, message: '利率必填', trigger: 'blur' }]">
        <gf-input v-model="form.rate" v-rate="2" />
      </el-form-item>

      <el-form-item label="生效时间" prop="effectiveDate" :rules="[{ required: true, message: '生效时间必填', trigger: 'change' }]">
        <el-date-picker v-model="form.effectiveDate" type="date" style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :picker-options="startPickerOpts"></el-date-picker>
      </el-form-item>
      <el-form-item label="失效时间" prop="expirationDate" :rules="[{ required: true, message: '失效时间必填', trigger: 'change' }]">
        <el-date-picker v-model="form.expirationDate" type="date" style="width: 100%" format="yyyy-MM-dd" value-format="yyyy-MM-dd" :picker-options="endPickerOpts"></el-date-picker>
      </el-form-item>
    </el-form>
    <div style="text-align: center;">
      <el-button type="primary" @click="handleSave">确定</el-button>
      <el-button @click="close">取消</el-button>
    </div>
  </div>
</template>
<script>
export default {
  props: {
    row: { type: Object, default: null },
    index: { type: Number, default: -1 }
  },
  data () {
    return {
      form: {
        ruleType: '',
        lowerLimit: '',
        higherLimit: '',
        rate: '',
        expirationDate: '',
        effectiveDate: ''
      }
    }
  },
  computed: {
    startPickerOpts() {
      let psDate = this.form.expirationDate;
      return {
        disabledDate: time => {
          if (!psDate) return false;
          return time.getTime() > new Date(psDate.replace(/[-]/g, '/')).getTime()
        }
      }
    },
    endPickerOpts() {
      let efDate = this.form.effectiveDate;
      return {
        disabledDate: time => {
          if (!efDate) return false;
          return time.getTime() < new Date(efDate.replace(/[-]/g, '/')).getTime()
        }
      }
    }
  },
  mounted () {
    if (this.row) {
      this.form = {
        ...this.row,
        rate: this.row.rate ? parseFloat(this.row.rate).toFixed(2) : ''
      }
    }
  },
  methods: {
    close(action) {
      this.$dialog.close(this, action);
    },
    handleSave () {
      this.$refs.form.validate((valid) => {
        if (!valid) {
          return false;
        }
        let string = JSON.stringify({ btnType: 'ok', rows: this.form })
        this.close(string);
      })
    }
  }
}
</script>
