<template>
    <div class="flex-row">
        <div class="right flex-column" style="flex-grow: 1;">
            <div class="top" style="margin-bottom: 15px; border-bottom: 1px solid #eeeeee;">
                <gf-search-box ref="box" title="筛选条件" :is-show-select="false">
                    <template slot="content">
                        <gf-form ref="gfForm" label-position="right" label-width="120px" :form-items="formItems" @getParams="getParams" :mode="'search'"></gf-form>
                    </template>
                </gf-search-box>
            </div>
            <div class="bottom">
                <gf-grid :filterRemote="false" :query-args="queryArgs" grid-no="agnes-biz-provisions-adjust-blacklist" toolbar="refresh,find,more" ref="grid" height="100%">
                    <template slot="left">
                        <el-button class="action-btn" @click="add" v-if="$hasPermission('agnes.biz.bfj.blacklist.add')">
                            新增
                        </el-button>
                        <el-button class="action-btn" @click="del" v-if="$hasPermission('agnes.biz.bfj.blacklist.del')">
                            删除
                        </el-button>
                    </template>
                    <template slot="right-before">
                        <span class="normal-font">快速检索:</span>
                    </template>
                </gf-grid>
            </div>
        </div>
    </div>
</template>
<script>
import payFeeBaseInfo from '../pay-fee-cal/pay-fee-base-info';
import dateUtil from '@hex/gf-ui/src/util/date-utils';

export default {
    data() {
        return {
            filterValue: '1',
            svgImg: this.$lcImg,
            formItems: [
                { type: 'input', name: '产品名称:', value: '', props: { clearable: true } },
                {
                    type: 'select',
                    name: '费用类型:',
                    value: '',
                    options: [{ id: '2', label: '' }],
                    props: { filterable: true, clearable: true, multiple: true, collapseTags: true }
                },
                {
                    type: 'date',
                    name: '开始日期:',
                    value: new Date().format('yyyy-MM-dd'),
                    props: { dateType: 'date', clearable: true, format: 'yyyy-MM-dd' }
                },
                {
                    type: 'date',
                    name: '结束日期:',
                    value: new Date().format('yyyy-MM-dd'),
                    props: { dateType: 'date', clearable: true, format: 'yyyy-MM-dd' }
                },
                {
                    type: 'children',
                    childItems: [
                        {
                            type: 'button',
                            action: 'reset',
                            name: '重置',
                            click: () => {
                                this.reset();
                            }
                        },
                        { type: 'button', action: 'open' },
                        {
                            type: 'button',
                            action: 'search',
                            name: '查询',
                            click: () => {
                                this.search();
                            }
                        },
                    ]
                }
            ],
            isChecked: false,
            total: 0,
            queryArgs: {
                productName: '', //产品名称
                productCode: '', //产品代码
                feeType: '', //费用类型
                // bizDate:new Date().format("yyyy-MM-dd"), //业务日期
                bizDate: '', //业务日期
                startDate: new Date().format('yyyy-MM-dd'), //开始日期
                endDate: new Date().format('yyyy-MM-dd') //结束日期
                //approveStatus: "01", //默认查询状态为未审核，状态，04：已审核 01：未审核
            }
        };
    },
    mounted() {
        this.getSearchOptions();
        this.$agnesUtils.usePublicTree(this.$route.path);
    },
    methods: {
        search() {
            this.queryArgs.productCode = this.$store.state.publicData.publicCheckProductStr;
            this.$refs.gfForm.search();
        },
        async add() {
            let productCode = this.$store.state.publicData.publicCheckProductStr;
            if (productCode.length == 0) {
                this.$msg.error('请选择需要添加的数据！');
                return;
            }
            const p = await this.$api.provisionsAdjustBlanklistApi.add(productCode);
            if (p.code == '200') {
                this.$msg.success(p.message);
            } else {
                this.$msg.error('添加失败');
            }
            this.$refs.grid.reloadData();
        },
        async del() {
            let rows = this.$refs.grid.getSelectedRows();
            if (rows.length == 0) {
                this.$msg.error('请选择需要删除的数据！');
                return;
            }
            const ok = await this.$msg.ask(`确认删除信息记录吗, 是否继续?`);
            if (!ok) {
                return;
            }
            const pkIds = [];
            rows.forEach(item => {
                pkIds.push(item.pkId);
            });
            const p = await this.$api.provisionsAdjustBlanklistApi.del(pkIds);
            if (p.code == '200') {
                this.$msg.success(p.message);
            } else {
                this.$msg.error('删除失败！');
            }
            this.$refs.grid.reloadData();
        },
        onExportWithFront() {
            let _this = this.$refs.grid;
            const params = {
                sheetName: 'sheet1',
                onlySelected: true,
                fileName: (_this.ext.exportName || 'export-') + this.$dateUtils.getCurrentTime()
            };
            const cloneUtil = this.$utils.deepClone;
            const fieldArr = _this.ext.needChangeField;
            params.processCellCallback = function (e) {
                if (e.value === undefined) {
                    return '';
                }
                e.type = 'String';
                if (fieldArr.includes(e.column.getColDef().field)) {
                    return `'${e.value}`;
                }
                const colDef = cloneUtil(e.column.colDef);
                const cl = colDef.cellClass;
                colDef.cellClass = [cl, 'boldBorders'];

                const valueFormatter = colDef.valueFormatter;
                if (valueFormatter) {
                    return valueFormatter(e);
                } else {
                    return e.value;
                }
            };
            _this.gridController.gridApi.exportDataAsExcel(params);
        },
        // 获取日期
        getDate() {
            this.getChangeData();
        },
        async getChangeData() {
            this.formItems[2].value = new Date().format('yyyy-MM-dd');
            this.formItems[3].value = new Date().format('yyyy-MM-dd');
        },
        // 获取查询方案字典值
        async getSearchOptions() {
            this.queryArgs.productCode = this.$store.state.publicData.publicCheckProductStr;
            this.formItems[1].options = this.selectByDict('PAY_CONF_FEE_TYPE');
        },
        selectByDict(param) {
            return this.$app.dict.getDictItems(param).map(dictItem => {
                return {
                    id: dictItem.dictId,
                    label: dictItem.dictName
                };
            });
        },
        // 获取表单参数
        async getParams(params) {
            this.queryArgs.productName = params[0].value === '' ? null : params[0].value;
            this.queryArgs.feeType = params[1].value === '' ? null : params[1].value.join(',');
            this.queryArgs.startDate = params[2].value === '' ? null : params[2].value;
            this.queryArgs.endDate = params[3].value === '' ? null : params[3].value;
            setTimeout(() => {
                this.$refs.grid.reloadData();
            }, 0);
        },
        async reset() {
            this.$refs.gfForm.reset();
            this.queryArgs.productName = '';
            this.queryArgs.productCode = '';
            this.queryArgs.feeType = '';
            this.getDate();
            this.queryArgs.startDate = new Date().format('yyyy-MM-dd');
            this.queryArgs.endDate = new Date().format('yyyy-MM-dd');
            await this.$refs.grid.reloadData();
        },
        dateChange(val) {
            this.queryArgs.bizDate = val ? dateUtil.formatDate(val, 'yyyy-MM-dd') : '';
            this.search();
        },
        //点击切换审核状态
        changeStatus(params) {
            this.queryArgs.approveStatus = params;
            this.search();
        },
        //删除列表数据
        async logicDelete(params) {
            const row = params.data;
            const ok = await this.$msg.ask(`确认删除:[${row.productName}]信息记录吗, 是否继续?`);
            if (!ok) {
                return;
            }
            try {
                const pkId = this.$api.payFeeCalApi.logicDelete(row.pkId);
                await this.$app.blockingApp(pkId);
                this.$msg.success('删除成功!');
                this.search();
            } catch (reason) {
                this.$msg.error(reason);
            }
        },
        async onAddPFCInfo() {
            this.search();
        },
        async onEditPFCInfo() {
            this.search();
        },

        //同步
        async sync() {
            const ok = await this.$msg.ask(`是否需要手工同步?`);
            if (!ok) {
                return;
            }
            await this.$api.payFeeCalApi.syncData(this.queryArgs.bizDate);
            await this.$refs.grid.reloadData();
            this.$msg.success('同步成功');
        },
        //复制费用支付日历信息
        copyPFCInfo() {
            let rows = this.$refs.grid.getSelectedRows();
            let row = [];
            if (rows.length > 0) {
                row = rows[0];
            } else {
                this.$msg.warning('请选中一条记录!');
                return;
            }
            this.showPFCInfo('copy', row, this.onEditPFCInfo.bind(this));
        },
        //编辑费用支付日历信息
        editPFCInfo(params) {
            this.showPFCInfo('edit', params.data, this.onEditPFCInfo.bind(this));
        },
        showDetail(params) {
            this.showPFCInfo('view', params.data, this.onEditPFCInfo.bind(this));
        },
        checked(params) {
            this.showPFCInfo('check', params.data, this.onEditPFCInfo.bind(this));
        },
        //展示费用支付日历数据编辑面板
        showPFCInfo(mode, row, actionOk) {
            if (mode !== 'add' && !row) {
                this.$msg.warning('请选中一条记录!');
                return;
            }
            row.isCheck = false;
            let title = this.$dialog.formatTitle('费用支付日历', mode);
            if (mode === 'copy') {
                title = '费用支付日历 - 复制';
            }

            if (mode == 'edit') {
                title = '费用支付日历 - 编辑';
            } else if (mode == 'view') {
                title = '查看';
            }

            if (mode == 'check') {
                row.isCheck = true;
                title = '费用支付日历 - 审核';
            }
            this.$nav.showDialog(payFeeBaseInfo, {
                args: { row, mode, actionOk },
                width: '40%',
                title: title
            });
        },

        async batchDel() {
            let rows = this.$refs.grid.getSelectedRows();
            if (rows.length == 0) {
                this.$msg.error('请选择需要删除的数据！');
                return;
            }
            const pkIds = [];
            rows.forEach(item => {
                pkIds.push(item.pkId);
            });
            const p = await this.$api.payFeeCalApi.batchDel(pkIds);
            if (p.code == '200') {
                this.$msg.success('删除成功!');
            } else {
                this.$msg.error('删除失败!');
            }
            this.$refs.grid.reloadData();
        }
    }
};
</script>
<style scoped>
.bottom {
    flex: 1;
}
</style>
