<template>
    <div class="flex-column">
        <div class="top" style="margin-bottom: 15px; border-bottom: 1px solid #eeeeee;">
            <gf-search-box ref="box" title="筛选条件" :is-show-select="false">
                <template slot="content">
                    <gf-form ref="gfForm" defalut-expand label-position="right" label-width="120px" :form-items="formItems" @getParams="getParams" :mode="'search'"></gf-form>
                </template>
            </gf-search-box>
        </div>
        <div class="bottom">
            <gf-grid grid-no="agnes-biz-pay-fee-external-file-parsing" toolbar="find,more" :query-args="queryArgs" ref="grid" height="100%" :filterRemote="false">
                <template slot="left">
                    <el-button class="action-btn" @click="handleFileParsing">
                        <i class="fa fa-plus" aria-hidden="true"></i>
                        手动同步外部文件解析
                    </el-button>
                    <gf-button @click="batchDel" class="action-btn" style="margin-left: 4px;" v-if="$hasPermission('agnes.biz.pay.fee.external.file.parsing.batchDel')">
                        删除
                    </gf-button>
                </template>
                <template slot="right-before">
                    <span class="normal-font">快速检索:</span>
                </template>
                <template slot="right-after">
                    <el-button icon="el-icon-download" class="gf-grid-status-button" @click="onExportWithFront"></el-button>
                </template>
            </gf-grid>
        </div>
    </div>
</template>
<script>

import detail from "../pay-fee-external-file-parsing/detail";
import extFileParse from "../pay-flow-mag/ext-file-parse.vue";
export default {
   data() {
       return {
           svgImg: this.$lcImg,
           formItems: [
             {type: 'select',name: '产品名称:',value: '',options: [],props: { clearable:true, filterable:true,multiple:true, collapseTags:true }},
             {type: 'select', name: '费用类型:', value: '', options: [],props: { filterable:true,clearable:true,multiple:true,collapseTags:true}},
               {type: 'date',name: '开始日期:',value: new Date().format("yyyy-MM-dd"),props: { dateType: 'date',clearable:true,format:'yyyy-MM-dd'}},
               {type: 'date',name: '结束日期:',value: new Date().format("yyyy-MM-dd"),props: { dateType: 'date',clearable:true,format:'yyyy-MM-dd'}},
             {type: 'children',
                  childItems: [
                   {type: 'button',action: 'search',name: '查询',click:()=>{this.search()}},
                   {type: 'button',action: 'reset',name: '重置',click:()=>{this.reset()}}
               ]},
           ],
           queryArgs: {
               productCode:null,
               productName:null,
               feeType:null,
               businessStartTs: new Date().format("yyyyMMdd"),
               businessEndTs:new Date().format("yyyyMMdd"),
               stratDate:null
           }
       }
   },
   mounted() {
     this.getSearchOptions();
   },
   methods: {
       search() {
           this.$refs.gfForm.search();
       },
       onExportWithFront() {
         let _this = this.$refs.grid;
         const params = {
           sheetName: "sheet1",
           onlySelected: true,
           fileName: (_this.ext.exportName || 'export-') + this.$dateUtils.getCurrentTime()
         };
         const cloneUtil = this.$utils.deepClone;
         const fieldArr = _this.ext.needChangeField;
         params.processCellCallback = function (e) {
           if (e.value === undefined) {
             return ''
           }
           e.type = 'String'
           if(fieldArr.includes(e.column.getColDef().field)){
             return `'${e.value}`
           }
           const colDef = cloneUtil(e.column.colDef);
           const cl = colDef.cellClass;
           colDef.cellClass = [cl, "boldBorders"];

                const valueFormatter = colDef.valueFormatter;
                if (valueFormatter) {
                    return valueFormatter(e);
                } else {
                    return e.value;
                }
            };

         _this.gridController.gridApi.exportDataAsExcel(params);
       },
       // 获取查询方案字典值
       async getSearchOptions() {
          const resp = await this.$api.productInfoApi.getProductListForSelect();
          if (Array.isArray(resp) && resp.length >0) {
            this.formItems[0].options = resp.map(item => ({ id: item.id, label: item.id + '-' + item.label }))
          }
          this.formItems[1].options = this.selectByDict("PAY_EXTERNAL_FILE_FEE_TYPE");
       },
       // 外部文件解析
       externalFileParsing() {
         const title = '外部文件解析'
         const component = extFileParse;
         this.showSchemeDlg(title,component);
       },

       showSchemeDlg(title,component) {
         this.$nav.showDialog(
             component,
             {
               width: '600px',
               title: title,
               closeOnClickModal: true
             }
         )
       },
       selectByDict(param) {
         return this.$app.dict
             .getDictItems(param)
             .map((dictItem) => {
               return {
                 id: dictItem.dictId,
                 label: dictItem.dictName,
               };
             });
       },
       // 获取表单参数
       getParams(params) {
           this.queryArgs.productCode = params[0].value === "" ? null : params[0].value.join(",");
           this.queryArgs.feeType = params[1].value === "" ? null : params[1].value.join(",");
           this.queryArgs.businessStartTs = params[2].value === "" ? null : params[2].value;
           this.queryArgs.businessEndTs = params[3].value === "" ? null : params[3].value;
           this.$refs.grid.reloadData();
       },
       // 重置表单数据
       async reset() {
         this.$refs.gfForm.reset();
         this.queryArgs.productCode = null;
         this.queryArgs.productName = null;
         this.queryArgs.feeType = null;
         this.queryArgs.date = null;
         this.queryArgs.businessStartTs = new Date().format("yyyy-MM-dd");
         this.queryArgs.businessEndTs = new Date().format("yyyy-MM-dd");
         this.formItems[2].value = new Date().format("yyyy-MM-dd");
         this.formItems[3].value = new Date().format("yyyy-MM-dd");
         await this.$refs.grid.reloadData();
       },
       // 重置表格列宽
       fitColumnsWidth() {
           setTimeout(() => {
               this.$refs.grid.handleMoreCommand('autoSizeFitColumns')
           }, 500)
       },
       reloadData() {
           this.$refs.grid.reloadData();
       },
       //手动同步外部文件解析
       async handleFileParsing() {
           const ok = await this.$msg.ask(`是否需要手动同步外部文件解析?`);
           if (!ok) {
               return
           }
           try {
               const p = await this.$api.payFlowMagApi.handleFileParsing();
               if (p.code == '500') {
                   this.$msg.error('处理外部文件失败!');
               } else {
                   this.$msg.success('处理外部文件成功!');
               }
               this.reloadData();
           } catch (reason) {
               this.$msg.error(reason);
           }
       },
       async reload() {
           this.reloadData();
       },
       async edit(params) {
           this.showDetail(params.data, 'edit', this.reload.bind(this));
       },
       async remove(params) {
           const row = params.data;
           const ok = await this.$msg.ask(`确认删除吗?`);
           if (!ok) {
               return
           }
           try {
               const p = this.$api.payFeeExternalFileParsingApi.remove(row.pkId);
               await this.$app.blockingApp(p);
               this.reloadData();
         } catch (reason) {
           this.$msg.error(reason);
         }
       },
       showDetail(row, mode, actionOk) {
           let title = mode;
           let cancelTitle = '取消';
           if (mode === 'edit') {
               title = '编辑';
           }
           let isShow = true;
           // 抽屉创建
           this.$drawerPage.create({
               width: 'calc(100% - 20px)',
               title: [title],
               component: detail,
               modal: true,
               args: {row, mode, actionOk},
               okButtonVisible: isShow,
               okButtonTitle: '保存',
               cancelButtonTitle: cancelTitle,
               pageEl: this.$el
           })
       },
     async batchDel() {
       let rows = this.$refs.grid.getSelectedRows();
       if (rows.length == 0) {
         this.$msg.error('请选择需要删除的数据！');
         return
       }
       this.$confirm('确定要删除数据吗?', '提示', {
         confirmButtonText: '确定',
         cancelButtonText: '取消',
         type: 'warning'
       }).then(async () => {
         const pkIds = [];
         rows.forEach((item) => {
           pkIds.push(item.pkId);
         });
         const p = await this.$api.payFeeExternalFileParsingApi.batchRemove(pkIds);
         if (p.code == '200') {
           this.$msg.success('删除成功!');
         } else {
           this.$msg.error('删除失败!');
         }
         this.reloadData();
       }).catch(() => {
         this.$message({
           type: 'info',
           message: '已取消'
         });
       });
     }

   }
}
</script>
<style scoped>
.bottom {
    flex: 1;
}
</style>
