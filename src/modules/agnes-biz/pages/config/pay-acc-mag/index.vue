<template>
    <div class="flex-column">
        <div class="top" style="margin-bottom: 15px; border-bottom: 1px solid #eeeeee;">
            <gf-search-box ref="box" title="筛选条件" :is-show-select="false">
                <template slot="content">
                    <gf-form ref="gfForm" label-position="right" label-width="120px" :form-items="formItems" @getParams="getParams" :mode="'search'"></gf-form>
                </template>
            </gf-search-box>
        </div>
        <div class="bottom">
            <gf-grid :filterRemote="false" :query-args="queryArgs" grid-no="agnes-biz-pay-acc-mag" toolbar="find,more" ref="grid" height="100%">
                <template slot="left">
                    <gf-button @click="add" class="action-btn" v-if="$hasPermission('agnes.biz.pay.acc.msg.add')">新增</gf-button>
                    <el-upload class="upload" :show-file-list="false" style="margin-left: 4px;" action="action" :http-request="uploadSectionFile" :on-success="onSuccess" accept=".xls,.xlsx" :on-error="onError">
                        <gf-button class="action-btn" slot="trigger" size="mini" v-if="$hasPermission('agnes.biz.pay.acc.msg.addBatch')">导入</gf-button>
                    </el-upload>
                    <gf-button @click="synchronization" class="action-btn"> 立即采集</gf-button>
                    <gf-button @click="syncData" class="action-btn" style="margin-left: 4px;" v-if="$hasPermission('agnes.biz.pay.acc.msg.sync')">同步</gf-button>
                    <gf-button @click="batchDel" class="action-btn" style="margin-left: 4px;" v-if="$hasPermission('agnes.biz.pay.acc.msg.batchDel')">删除</gf-button>
                </template>
                <template slot="right-before">
                    <span class="normal-font">快速检索:</span>
                </template>
                <template slot="right-after">
                    <el-button icon="el-icon-download" class="gf-grid-status-button" @click="onExportWithFront"></el-button>
                </template>
            </gf-grid>
        </div>
    </div>
</template>
<script>
import AccMagDlg from '../pay-acc-mag/pay-acc-mag';
import {request} from '@hexinfo/gf-ui';
export default {
    data() {
        return {
            svgImg: this.$lcImg,
            formItems: [
                { type: 'select', name: '费用类型:', value: '', options: [{ id: '2', label: '' }], props: { filterable: true, clearable: true, multiple: true, collapseTags: true } },
                { type: 'select', name: '账户类型:', value: '', options: [{ id: '2', label: '' }], props: { filterable:true}},
                // {type: 'input',name: '机构名称:',value: '',props: {clearable:true}},
                {
                    type: 'children',
                    childItems: [
                        {
                            type: 'button',
                            action: 'reset',
                            name: '重置',
                            click: () => {
                                this.reset();
                            }
                        },
                        {
                            type: 'button',
                            action: 'search',
                            name: '查询',
                            click: () => {
                                this.search();
                            }
                        },
                    ]
                }
            ],
            queryArgs: {
                productCode: null,
                feeType: null
                // orgName:null
            }
        };
    },
    mounted() {
        this.getSearchOptions();
        this.$agnesUtils.usePublicTree(this.$route.path);
    },
    methods: {
        search() {
            this.queryArgs.productCode = this.$store.state.publicData.publicCheckProductStr;
            this.$refs.gfForm.search();
        },
        onExportWithFront() {
            let _this = this.$refs.grid;
            const params = {
                sheetName: 'sheet1',
                onlySelected: true,
                fileName: (_this.ext.exportName || 'export-') + this.$dateUtils.getCurrentTime()
            };
            const cloneUtil = this.$utils.deepClone;
            const fieldArr = _this.ext.needChangeField;
            params.processCellCallback = function (e) {
                if (e.value === undefined) {
                    return '';
                }
                e.type = 'String';
                if (fieldArr.includes(e.column.getColDef().field)) {
                    return `'${e.value}`;
                }
                const colDef = cloneUtil(e.column.colDef);
                const cl = colDef.cellClass;
                colDef.cellClass = [cl, 'boldBorders'];

                const valueFormatter = colDef.valueFormatter;
                if (valueFormatter) {
                    return valueFormatter(e);
                } else {
                    return e.value;
                }
            };
            _this.gridController.gridApi.exportDataAsExcel(params);
        },
        // 获取查询方案字典值
        async getSearchOptions() {
            this.formItems[0].options = this.selectByDict('PAY_CONF_FEE_TYPE');
        },
        selectByDict(param) {
            return this.$app.dict.getDictItems(param).map(dictItem => {
                return {
                    id: dictItem.dictId,
                    label: dictItem.dictName
                };
            });
        },
        // 获取表单参数
        getParams(params) {
            this.queryArgs.feeType = params[0].value === '' ? null : params[0].value.join(',');

            this.$refs.grid.reloadData();
        },
        // 重置表单数据
        async reset() {
            this.$refs.gfForm.reset();
            this.queryArgs.feeType = null;

            await this.$refs.grid.reloadData();
        },
        reloadData() {
            this.$refs.grid.reloadData();
        },
        async syncData() {
            const ok = await this.$msg.ask(`是否需要手工同步?`);
            if (!ok) {
                return;
            }
            await this.$api.payAccMagApi.syncData();
            await this.$refs.grid.reloadData();
            this.$msg.success('同步成功');
        },
        async onAddModel() {
            this.reloadData();
        },
        async onEditModel() {
            this.reloadData();
        },
        add() {
            this.showDialog('add', {}, this.onAddModel.bind(this));
        },
        async synchronization() {
            const ok = await this.$msg.ask(`根据COP系统账户、O3场外对手方账户刷入最新数据`);
            if (!ok) {
            return;
            }
            try {
            let p = this.$api.tradeFlowApi.sjtb("ZHXX_CJTB");
            const respCheck = await this.$app.blockingApp(p);
            if (respCheck.success) {
                if (this.actionOk) {
                await this.actionOk();
                }
                this.$msg.success(respCheck.data);
            }else {
                this.$msg.error(respCheck.message);
            }

            } catch (reason) {
            this.$msg.error(reason);
            }
            this.reloadData();
        },
        copy(params) {
            this.showDialog('copy', params.data, this.onEditModel.bind(this));
        },
        edit(params) {
            this.showDialog('edit', params.data, this.onEditModel.bind(this));
        },
        showDetail(params) {
            this.showDialog('view', params.data, this.onEditModel.bind(this));
        },
        approve() {
            let rows = this.$refs.grid.getSelectedRows();
            let row = [];
            if (rows.length == 1) {
                row = rows[0];
            } else {
                this.$msg.warning('请选中一条记录!');
                return;
            }
            this.showDialog('approve', row, this.onEditModel.bind(this));
        },
        showDialog(mode, row, actionOk) {
            if (mode !== 'add' && !row) {
                this.$msg.warning('请选中一条记录!');
                return;
            }
            row.auditStatus = '04';
            row.dataType = '00';
            let title = '添加';
            if (mode == 'copy') {
                title = '复制';
            } else if (mode == 'edit') {
                title = '编辑';
            } else if (mode == 'approve') {
                row.auditStatus = '04';
                title = '审核';
            } else if (mode == 'view') {
                title = '查看';
            }
            this.$nav.showDialog(AccMagDlg, {
                args: { row, mode, actionOk },
                width: '60%',
                title: title
            });
        },
        async remove(params) {
            const row = params.data;
            const ok = await this.$msg.ask(`确认删除吗?`);
            if (!ok) {
                return;
            }
            try {
                const p = this.$api.payAccMagApi.removePayAccMag(row.pkId);
                await this.$app.blockingApp(p);
                this.reloadData();
            } catch (reason) {
                this.$msg.error(reason);
            }
        },

        async uploadSectionFile(params) {
            const file = params.file;
            const form = new FormData();
            form.append('file', file);
            await request.post('/agnes-app/v1/pay/fee/account/batchSave', form).then(res => {
                if (res.code === '200') {
                    //处理成功的逻辑
                    this.$message({
                        dangerouslyUseHTMLString: true,
                        message: '保存成功' + res.message,
                        type: 'success'
                    });
                    this.reloadData();
                }
            });
        },
        onSuccess(response, file, fileList) {
            console.log(response, file, fileList);
        },
        onError(err, file, fileList) {
            console.log(err, file, fileList);
        },
        async batchDel() {
            let rows = this.$refs.grid.getSelectedRows();
            if (rows.length == 0) {
                this.$msg.error('请选择需要删除的数据！');
                return;
            }
            const pkIds = [];
            rows.forEach(item => {
                pkIds.push(item.pkId);
            });
            const p = await this.$api.payAccMagApi.batchRemove(pkIds);
            if (p.code == '200') {
                this.$msg.success('删除成功!');
            } else {
                this.$msg.error('删除失败!');
            }
            this.reloadData();
        }
    }
};
</script>
<style scoped>
.bottom {
    flex: 1;
}
</style>
