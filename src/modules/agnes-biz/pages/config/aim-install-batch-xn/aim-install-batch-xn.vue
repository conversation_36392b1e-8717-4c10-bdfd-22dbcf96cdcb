<template>
    <div style="height: 95%; width: 98%;">
        <gf-grid :filterRemote="false" class="acnt-apply-grid" height="100%" ref="batchGrid" toolbar="find,refresh,more"
            grid-no="install-batch-xn" :query-args="installQueryArgs">
            <template slot="left">
                <gf-button v-show="!disabled" class="action-btn" @click="saveTable" :disabled="disabled"
                    v-if="$hasPermission('agnes.biz.install.batch.xn.save')">保存</gf-button>
            </template>
            <template slot="right-after">
                <el-button icon="el-icon-download" class="gf-grid-status-button"
                    @click="onExportWithFront('batch')"></el-button>
            </template>
        </gf-grid>
    </div>
</template>

<script>
import aimInstallResult from '../aim-install-batch/aim-install-result-dlg';
export default {
    name: 'aim-install-batch-dlg',
    props: {
        row: Object,
        mode: String,
        gridData: {
            type: Array,
            default: () => {
                return [];
            }
        }
    },
    data() {
        return {
            disabled: true,
            installQueryArgs: {
                pkId: '',
                payBatchNo: ''
            },
            queryArgs: {
                pkId: ''
            },
            selectRows: {},
            batchGz: {},
            batchLl: {},
            gridOption: _this => {
                return {
                    onCellClicked(params) {
                        const showDetailArr=['checkStatus'];
                        if(showDetailArr.includes(params.colDef.field)) {
                            _this.showDetail(params.data);
                        }
                    },
                    onFirstDataRendered(params) {
                        params.columnApi.columnController.autoSizeFitColumns();
                    }
                };
            }
        };
    },
    mounted() {
        setTimeout(() => {
            this.selectRows=this.row;
            this.selectDate();
        }, 0);
    },
    methods: {
        onExportWithFront() {
            let _this=this.$refs.batchGrid;
            const params={
                sheetName: 'sheet1',
                onlySelected: true,
                fileName: (_this.ext.exportName||'export-')+this.$dateUtils.getCurrentTime()
            };
            const cloneUtil=this.$utils.deepClone;
            const fieldArr=_this.ext.needChangeField;
            params.processCellCallback=function(e) {
                if(e.value===undefined) {
                    return '';
                }
                e.type='String';
                if(fieldArr.includes(e.column.getColDef().field)) {
                    return `'${e.value}`;
                }
                const colDef=cloneUtil(e.column.colDef);
                const cl=colDef.cellClass;
                colDef.cellClass=[cl, 'boldBorders'];

                const valueFormatter=colDef.valueFormatter;
                if(valueFormatter) {
                    return valueFormatter(e);
                } else {
                    return e.value;
                }
            };
            _this.gridController.gridApi.exportDataAsExcel(params);
        },
        // 点击状态打开弹窗
        showDetail(row) {
            let customOpBtn=[{ title: '关闭', action: 'onCancel' }];
            this.$drawerPage.create({
                width: 'calc(95% - 215px)',
                title: ['携宁核对'],
                component: aimInstallResult,
                args: { row },
                customOpBtn: customOpBtn,
                pageEl: this.$el
            });
        },

        selectDate() {
            if(this.selectRows.taskNode==='03'&&this.selectRows.taskStatus==='02') {
                this.disabled=false
            } else {
                this.disabled=true
            }
            this.initPinnedBottomRowData();
        },
        cellValueChangedXn(params) {
            let flag=false;
            let oldValue=params.oldValue;
            let newValue=params.newValue;
            let column=params.colDef.field;
            let regPos=/^\d+(\.\d+)?$/;
            let regNeg=/^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/;
            if(!(regPos.test(newValue)||regNeg.test(newValue))) {
                this.$message.warning("请输入数字")
                params.data[column]=oldValue
                newValue=oldValue;
                flag=true
            }
            let value=parseFloat(newValue).sub(parseFloat(oldValue));
            //活期存款分摊、应收利息
            let column1=['demandDepositShare', 'interestReceivable'];
            //应付受托费 汇划费 受益人大会费 评级费 律师费 审计费 应付托管费 监督费 资金监督费
            let column2=['payableTrusteeFee', 'hhf', 'syrdhf', 'pjf', 'lsf', 'sjf', 'payableCustodyFee', 'superviseFee', 'moneySupervisionFee'];
            //应付受托费 应付托管费  监督费 账户监管费 汇划费 受益人大会费 评级费 律师费 审计费  资金监督费 增值税 附加税
            let column3=['payableTrusteeFee', 'payableCustodyFee', 'superviseFee', 'accountSupervisionFee', 'hhf', 'syrdhf', 'pjf', 'lsf', 'sjf', 'moneySupervisionFee',
                'vat', 'surtax'];
            //本期资金流入 = 活期存款分摊+应收利息
            if(!flag&&column1.includes(column)) {
                params.data.currentCapitalInflow=parseFloat(params.data.currentCapitalInflow).add(value);
                params.data.currentIstributableIncome=parseFloat(params.data.currentIstributableIncome).add(value)
            }
            let payableTrusteeFeeOldValue=params.data.payableTrusteeFee;
            //应付受托费=应付受托费-托管费 -监督费 - 资金监督费 - 其他费用(汇划费，受益人大会费，评级费，律师费，审计费)
            if(!flag&&column2.includes(column)) {
                if(column==='payableTrusteeFee') {
                    params.data.payableTrusteeFee=newValue
                } else {
                    params.data.payableTrusteeFee=parseFloat(params.data.payableTrusteeFee).sub(value)
                }
            }
            let payableTrusteeFeeNewValue=params.data.payableTrusteeFee;
            let payableTrusteeFee=parseFloat(payableTrusteeFeeNewValue).sub(parseFloat(payableTrusteeFeeOldValue));
            //本期支出 = 应付受托费+应付托管费+监督费+资金监督费+增值税+附加税+备用金(分摊备用金)+账户监管费+律师费+审计费+受益人大会费+汇划费
            if(!flag&&column3.includes(column)) {
                let oldCurrentExpenditures=params.data.currentExpenditures;
                params.data.currentExpenditures=parseFloat(params.data.currentExpenditures).add(payableTrusteeFee)
                    .add(value);
                let diff=parseFloat(params.data.currentExpenditures).sub(parseFloat(oldCurrentExpenditures));
                params.data.currentIstributableIncome=parseFloat(params.data.currentIstributableIncome)
                    .sub(parseFloat(diff));
            }
            //本期可分配收益 = 本期资金流入-本期支出 + 偿还本金
            if(!flag&&column==='repayPrincipal') {
                params.data.currentIstributableIncome=parseFloat(params.data.currentIstributableIncome).add(value);
            }
            this.$refs.batchGrid.refreshRows([params.node]);
            this.resetTable('edit');
        },
        /**
         * Author: yuelanfenghua
         * Date: 2025-01-02 19:47:37
         * version: 
         * Des: 合计
         * return {*}
         */
        getTotal(fieldArr, totalData, checkData, baseData, res) {
            fieldArr.forEach(val => {
                //合计
                totalData[val]=res[val]? Number(res[val]):'0';
            })
            fieldArr.forEach(val => {
                //合计
                totalData[val]=res[val]? Number(res[val]):'0';
                //核验
                if(val==='demandDepositAmount'||val==='repayPrincipal') {
                    baseData[val]='--';
                    //核验
                    checkData[val]='--';
                } else {
                    baseData[val]=baseData[val]||0;
                    //核验
                    checkData[val]=totalData[val]? this.$agnesUtils.sub(totalData[val], baseData[val]):'0';
                }
            })
            return {
                totalData, baseData, checkData
            }
        },
        async resetTable(type) {
            if(type=='edit') {
                this.loadZmxx(this.batchGz);
            } else {
                try {
                    let resp=await this.$api.aimProductApi.installBatchResetGz(this.selectRows);
                    if(resp.code&&resp.code=='00000000') {
                        let data=Object.assign(resp.data)
                        this.loadZmxx(data);
                        this.$msg.success('数据校验成功');
                        this.$refs.batchGrid.reloadData(true);
                    } else {
                        this.$msg.success('数据校验失败');
                    }
                } catch(e) {
                    this.$msg.error(e);
                }
            }
        },
        loadZmxx() {
            let baseData={ demandDepositAmount: '携宁数据' };
            this.pinnedBottomRowData=[]
            //需要计算的列
            let fieldArr=[
                'shareProvisions',
                'interestReceivable',
                'currentCapitalInflow',
                'demandDepositShare',
                'payableTrusteeFee',
                'payableCustodyFee',
                'superviseFee',
                'vat',
                'surtax',
                'accountSupervisionFee',
                'moneySupervisionFee',
                'pjf',
                'lsf',
                'sjf',
                'syrdhf',
                'hhf',
                'currentExpenditures',
                'currentIstributableIncome'
            ]

            let rowData=this.$refs.batchGrid.getRowData()
            if(rowData.length==0) {
                return;
            }
            let data=this.$lodash.cloneDeep(rowData)
            Object.assign(baseData, this.batchLl)
            // 合计
            let res=data.reduce((result, next) => {
                if(!result) result={}
                Object.keys(next).forEach((key) => {
                    if(fieldArr.includes(key)) {
                        result[key]=this.$agnesUtils.add(result[key], next[key])||0
                    }
                })
                return result
            })
            let totalData={ demandDepositAmount: '明细合计' }
            let checkData={ demandDepositAmount: '核验' }
            const obj=this.getTotal(fieldArr, totalData, checkData, baseData, res)
            totalData=obj.totalData
            baseData=obj.baseData
            checkData=obj.checkData

            this.pinnedBottomRowData=[totalData, baseData, checkData];
            this.setPinnedBottomRowData(this.pinnedBottomRowData)
        },
        /**
         * Author: yuelanfenghua
         * Date: 2025-01-02 19:52:23
         * version: 
         * Des: 合计
         * return {*}
         */
        getinitPinnedBottomRowDataTotal(data,fieldArr ,baseData) {
            // 合计
            let res=data.reduce((result1, next1) => {
                if(!result1) result1={}
                Object.keys(next1).forEach((key) => {
                    if(fieldArr.includes(key)) {
                        result1[key]=this.$agnesUtils.add(result1[key], next1[key])||0
                    }
                })
                return result1
            })
            let totalData={ demandDepositAmount: '明细合计' }
            let checkData={ demandDepositAmount: '核验' }
            fieldArr.forEach(val1 => {
                //合计
                totalData[val1]=res[val1]? Number(res[val1]):'0';
            })
            fieldArr.forEach(val1 => {
                //合计
                totalData[val1]=res[val1]? Number(res[val1]):'0';
                //核验
                if(val1==='demandDepositAmount'||val1==='repayPrincipal') {
                    baseData[val1]='--';
                    //核验
                    checkData[val1]='--';
                } else {
                    baseData[val1]=baseData[val1]||0;
                    //核验
                    checkData[val1]=totalData[val1]? this.$agnesUtils.sub(totalData[val1], baseData[val1]):'0';
                }
            })
            return {baseData,totalData,checkData}

        },
        async initPinnedBottomRowData(selectRows=this.row) {
            let baseData={ demandDepositAmount: '携宁数据' };
            this.pinnedBottomRowData=[]
            //需要计算的列
            let fieldArr=[
                'shareProvisions',
                'interestReceivable',
                'demandDepositShare',
                'currentCapitalInflow',
                'payableTrusteeFee',
                'payableCustodyFee',
                'superviseFee',
                'vat',
                'surtax',
                'accountSupervisionFee',
                'moneySupervisionFee',
                'pjf',
                'lsf',
                'sjf',
                'syrdhf',
                'hhf',
                'currentExpenditures',
                'currentIstributableIncome'
            ]
            try {
                let resp=await this.$api.aimProductApi.installBatchQueryInfo(selectRows);
                if(resp.data) {
                    Object.assign(this.batchGz, resp.data.batchGz)
                    if(resp.data.bizRuAimInstallBatch) {
                        Object.assign(this.batchLl, resp.data.bizRuAimInstallBatch)
                    } else {
                        if(this.batchLl) {
                            Object.keys(this.batchLl).forEach(item => {
                                this.batchLl[item]=null
                            })
                        }
                    }
                    Object.assign(baseData, this.batchLl)
                    this.$refs.batchGrid.setRowData(resp.data.batchList);
                    let data=this.$lodash.cloneDeep(resp.data.batchList)
                     let obj = this.getinitPinnedBottomRowDataTotal(data,fieldArr ,baseData)
                     let totalData = obj.totalData
                     baseData = obj.baseData
                     let checkData = obj.checkData
                    this.pinnedBottomRowData=[totalData, baseData, checkData];
                    this.setPinnedBottomRowData(this.pinnedBottomRowData)
                }
            } catch(e) {
                this.$message.error(e);
            }
        },
        setPinnedBottomRowData(data) {
            this.$refs.batchGrid.gridController.gridApi.setPinnedBottomRowData(data)
        },

        async saveTable() {
            try {
                let rows=this.$refs.batchGrid.getRowData();
                let resp=await this.$api.aimProductApi.installBatchSaveInfo(rows);
                if(resp.code&&resp.code=='00000000') {
                    this.$msg.success('保存成功');
                    this.$refs.batchGrid.reloadData(true);
                } else {
                    this.$msg.success('保存失败');
                }
            } catch(e) {
                this.$msg.error(e);
            }
        },

        async onCancel() {
            this.$emit('onClose');
        }
    }
};
</script>
