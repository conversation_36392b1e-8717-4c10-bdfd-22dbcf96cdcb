<template>
    <div class="flex-column">
        <div class="top" style="margin-bottom: 15px; border-bottom: 1px solid #eeeeee;">
            <gf-search-box ref="box" title="筛选条件" :is-show-select="false">
                <template slot="content">
                    <gf-form ref="gfForm" label-position="right" label-width="120px" :form-items="formItems" @getParams="getParams" :mode="'search'"></gf-form>
                </template>
            </gf-search-box>
        </div>
        <div class="bottom">
            <gf-grid :filterRemote="false" :query-args="queryArgs" class="payDedAuth" grid-no="agnes-biz-pay-ded-auth" toolbar="find,more" ref="grid" height="100%">
                <template slot="left">
                    <el-button class="action-btn" @click="add">
                        新增
                    </el-button>

                    <el-button class="action-btn" @click="batchGen">
                        授权文件生成
                    </el-button>
                    <el-button class="action-btn" @click="batchEmailSend">
                        邮件推送
                    </el-button>
                </template>
                <template slot="right-before">
                    <span class="normal-font">快速检索:</span>
                </template>
                <template slot="right-after">
                    <el-button icon="el-icon-download" class="gf-grid-status-button" @click="onExportFromServer"></el-button>
                </template>
            </gf-grid>
        </div>
    </div>
</template>
<script>
import DedMagDlg from './ded-mag-dlg';
import ChangeDataDlg from './change-data-dlg';
import ChangeRuleDlg from './change-rule-dlg';
import FileTemplate from './file-template-dlg';

export default {
    data() {
        return {
            genId: '',
            form: {
                req: null
            },
            svgImg: this.$lcImg,
            formItems: [
                { type: 'input', name: '产品名称:', value: '', props: { clearable: true } },
                {
                    type: 'select',
                    name: '授权类型:',
                    value: '',
                    options: [{ id: '2', label: '' }],
                    props: { filterable: true, clearable: true, multiple: true, collapseTags: true }
                },
                {
                    type: 'select',
                    name: '托管分行简称:',
                    value: '',
                    options: [{ id: '2', label: '' }],
                    props: { filterable: true, clearable: true, multiple: true, collapseTags: true }
                },
                {
                    type: 'date',
                    name: '授权日期:',
                    value: this.dealDate(new Date(window.bizDate)),
                    props: { dateType: 'date', format: 'yyyy年MM月dd日' }
                },
                {
                    type: 'select',
                    name: '邮件发送结果:',
                    value: '',
                    options: [{ id: '2', label: '' }],
                    props: { filterable: true, clearable: true, multiple: true, collapseTags: true }
                },
                {
                    type: 'select',
                    name: '费用类型:',
                    value: '',
                    options: [{ id: '2', label: '' }],
                    props: { filterable: true, clearable: true, multiple: true, collapseTags: true }
                },
                {
                    type: 'children',
                    childItems: [
                        {
                            type: 'button',
                            action: 'reset',
                            name: '重置',
                            click: () => {
                                this.reset();
                            }
                        },
                        { type: 'button', action: 'open' },
                        {
                            type: 'button',
                            action: 'search',
                            name: '查询',
                            click: () => {
                                this.search();
                            }
                        },
                    ]
                }
            ],
            total: 0,
            queryArgs: {
                productCode: null,
                productName: null,
                authType: null,
                tgfhBh: null,
                authDate: this.dealDate(new Date(window.bizDate)),
                emailPushStatus: null,
                feeType: null
            },
            autoSend: '00'
        };
    },
    mounted() {
        this.getSearchOptions();

        //使用公共树
        this.$agnesUtils.usePublicTree(this.$route.path);
    },
    methods: {
        reView(params) {
            this.$nav.showDialog(FileTemplate, {
                width: '1300px',
                title: '预览',
                closeOnClickModal: true,
                args: {
                    docId: params.data.docId
                }
            });
        },

        async handTask() {
            const ok = await this.$msg.ask(`确认手工同步?`);
            if (!ok) {
                return;
            }
            await this.$api.payFeeDedMagApi.handTask();
            await this.$refs.grid.reloadData();
            this.$msg.success('同步成功');
        },
        // 下载
        download(param) {
            const basePath = window.location.href.split('#/')[0];
            window.open(basePath + 'api/ecm-server/ecm/doc/download/' + param.data.docId);
        },
        // 生成授权成功
        async generate(params) {
            const ok = await this.$msg.ask(`确认生成文件吗?`);
            if (!ok) {
                return;
            }
            let form = {
                productCode: params.data.productCode,
                productName: params.data.productName,
                authDate: params.data.authDate,
                authType: params.data.authType,
                emailPushStatus: params.data.emailPushStatus,
                feeRemark: params.data.feeRemark,
                custodianName: params.data.tgfhQc
            };
            await this.$api.payFeeDedMagApi.genFile(form);
            await this.$refs.grid.reloadData();
            this.$msg.success('授权文件生成成功');
        },
        // 批量生成授权文件
        async batchGen() {
            let rows = this.$refs.grid.getSelectedRows();
            if (rows.length === 0) {
                this.$msg.warning('请勾选数据');
                return;
            }
            let remindMsg = '';
            let datas = [];
            for (let ritem of rows) {
                if (ritem.feeType != '-') {
                    continue;
                }
                if (ritem.emailPushStatus == '01') {
                    this.$msg.warning(ritem.productCode + '已发送邮件');
                    return;
                }
                if (ritem.fileCreatStatus == '01') {
                    this.$msg.warning(ritem.productCode + '已生成授权文件');
                    return;
                }
                if (!ritem.tgfhQc) {
                    remindMsg = '勾选数据有托管行信息缺失，';
                    continue;
                }

                let dd = {
                    productCode: ritem.productCode,
                    productName: ritem.productName,
                    authDate: ritem.authDate,
                    authType: ritem.authType,
                    emailPushStatus: ritem.emailPushStatus,
                    feeRemark: ritem.feeRemark,
                    custodianName: ritem.tgfhQc
                };
                datas.push(dd);
            }

            const ok = await this.$msg.ask(remindMsg + `确认要生成授权文件吗?`);
            if (!ok) {
                return;
            }
            let form = {
                data: datas
            };
            await this.$api.payFeeDedMagApi.batchGenAuth(form, { timeout: 60000 });
            await this.$refs.grid.reloadData();
            this.$msg.success('授权文件生成成功');
        },
        async onAddChangeData() {
            await this.loadChangeData();
        },
        addChangeRule() {
            this.showDlg(this.onAddChangeData.bind(this), 'rule', null);
        },
        showDlg(actionOk, type, execScheduler) {
            if (type === 'data') {
                this.$nav.showDialog(ChangeDataDlg, {
                    args: { actionOk },
                    width: '576px',
                    title: this.$dialog.formatTitle('发送频率')
                });
            } else if (type === 'rule') {
                this.$nav.showDialog(ChangeRuleDlg, {
                    args: { actionOk, execScheduler },
                    width: '600px',
                    title: this.$dialog.formatTitle('发送频率', 'edit')
                });
            }
        },

        search() {
            this.queryArgs.productCode = this.$store.state.publicData.publicCheckProductStr;
            this.$refs.gfForm.search();
        },
        onExportWithFront() {
            let _this = this.$refs.grid;
            const params = {
                sheetName: 'sheet1',
                onlySelected: true,
                fileName: (_this.ext.exportName || 'export-') + this.$dateUtils.getCurrentTime()
            };
            const cloneUtil = this.$utils.deepClone;

            params.processCellCallback = function (e) {
                if (e.value === undefined) {
                    return '';
                }
                e.type = 'String';
                const colDef = cloneUtil(e.column.colDef);
                const cl = colDef.cellClass;
                colDef.cellClass = [cl, 'boldBorders'];

                const valueFormatter = colDef.valueFormatter;
                if (valueFormatter) {
                    return valueFormatter(e);
                } else {
                    return e.value;
                }
            };
            _this.gridController.gridApi.exportDataAsExcel(params);
        },
        async onExportFromServer() {
            let _this = this.$refs.grid;
            let _url = 'agnes-biz/v1/biz/payFeeDedMag/exportData';

            let fileName = (_this.ext.exportName || 'export-') + this.$dateUtils.getCurrentTime() + '.xlsx';

            _this.gridController.$inst
                .$req({
                    method: 'post',
                    url: _url,
                    data: this.queryArgs,
                    responseType: 'blob'
                })
                .then(response => {
                    _this.gridController.gridApi.excelCreator.downloader.download(fileName, response);
                });
        },
        // 获取查询方案字典值
        async getSearchOptions() {
            this.formItems[1].options = this.selectByDict('AUTHORIZE_TYPE');
            this.formItems[2].options = await this.getBankList();
            this.formItems[4].options = this.selectByDict('EMAIL_PUSH_STATUS');
            this.formItems[5].options = this.selectByDict('DED_AUTH_FEE_TYPE');
        },
        async getSwitchStatus() {
            const res = await this.$api.payFeeDedMagApi.getSwitchStatus();
            if (res.data) {
                this.autoSend = res.data;
            }
        },
        changeSwitchStatus(params) {
            this.$api.payFeeDedMagApi.changeSwitchStatus(params);
        },
        selectByDict(param) {
            return this.$app.dict.getDictItems(param).map(dictItem => {
                return {
                    id: dictItem.dictId,
                    label: dictItem.dictName
                };
            });
        },
        // 托管行列表
        async getBankList() {
            let bankLists = [];
            const banRes = await this.$api.bizFeeDedRuleAddApi.getBankList();
            if (banRes.data.rows) {
                banRes.data.rows.forEach(dataItem => {
                    bankLists.push({ id: dataItem.id, label: dataItem.bankName });
                });
            }
            return bankLists;
        },
        // 获取表单参数
        getParams(params) {
            this.queryArgs.productName = params[0].value === '' ? null : params[0].value;
            this.queryArgs.authType = params[1].value === '' ? null : params[1].value;
            this.queryArgs.tgfhBh = params[2].value === '' ? null : params[2].value;
            this.queryArgs.authDate = params[3].value === '' ? null : params[3].value;
            this.queryArgs.emailPushStatus = params[4].value === '' ? null : params[4].value;
            this.queryArgs.feeType = params[5].value === '' ? null : params[5].value;
            this.$refs.grid.reloadData();
        },
        // 重置表单数据
        async reset() {
            this.$refs.gfForm.reset();
            this.queryArgs.productCode = null;
            this.queryArgs.productName = null;
            this.queryArgs.authType = null;
            this.queryArgs.tgfhBh = null;
            this.queryArgs.emailPushStatus = null;
            this.queryArgs.feeType = null;
            let today = this.dealDate(new Date(window.bizDate));
            this.queryArgs.authDate = today;
            this.formItems[3].value = today;
            await this.$refs.grid.reloadData();
        },
        reloadData() {
            this.$refs.grid.reloadData();
        },
        async onAddModel() {
            this.reloadData();
        },
        async onEditModel() {
            this.reloadData();
        },
        add() {
            this.showDialog('add', {}, this.onAddModel.bind(this));
        },
        async emailsend() {
            let rows = this.$refs.grid.getSelectedRows();
            if (rows.length === 0 || rows.length > 1) {
                this.$msg.warning('请选中列表一条数据后再操作---邮件推送!');
                return;
            }
            if (rows[0].fileCreatStatus === '00') {
                this.$msg.warning('自动扣收文件文件尚未生成，无法发送邮件');
                return;
            }
            const ok = await this.$msg.ask(`请确认是否需要邮件推送?`);
            if (!ok) {
                return;
            }

            if (rows.length === 1) {
                try {
                    const form = rows[0];
                    const p = await this.$api.payFeeDedMagApi.emailSend(form);
                    if (p.code == '500') {
                        this.$msg.error('邮件推送失败!');
                    } else {
                        this.$msg.success('邮件推送成功!');
                    }
                    this.$refs.grid.reloadData();
                } catch (reason) {
                    this.$msg.error(reason);
                }
            }
        },
        getbatchEmailSendPAram(rows) {
            let datas = [];
            let remindMsg = '';
            let success = true;
            for (let ritem of rows) {
                if (ritem.feeType != '-') {
                    continue;
                }
                if (ritem.emailPushStatus == '01') {
                    this.$msg.warning(ritem.productCode + '已发送邮件');
                    success = false;
                    break;
                }
                if (ritem.fileCreatStatus == '00') {
                    this.$msg.warning(ritem.productCode + '未生成授权文件');
                    success = false;
                    break;
                }
                if (ritem.flowSeq == '02') {
                    this.$msg.warning(ritem.productCode + '有数据缺失');
                    success = false;
                    break;
                }
                if (ritem.flowSeq == '01') {
                    remindMsg = '勾选数据有重复条目，';
                }
                datas.push(ritem);
            }

            return {
                success: success,
                datas: datas,
                remindMsg: remindMsg
            };
        },
        async batchEmailSend() {
            let rows = this.$refs.grid.getSelectedRows();
            if (rows.length === 0) {
                this.$msg.warning('请勾选数据');
                return;
            }
            let obj = this.getbatchEmailSendPAram(rows)
            if(!obj.success) {
                return
            }
            let {datas,remindMsg} = obj

            const ok = await this.$msg.ask(remindMsg + `确认要推送邮件吗?`);
            if (!ok) {
                return;
            }

            try {
                let form = {
                    data: datas
                };
                const p = await this.$api.payFeeDedMagApi.batchEmailSend(form);
                if (p.code == '500') {
                    this.$msg.error('邮件推送失败!');
                } else {
                    this.$msg.success('邮件推送成功!');
                }
                this.$refs.grid.reloadData();
            } catch (reason) {
                this.$msg.error(reason);
            }
        },
        copy(params) {
            this.showDialog('copy', params.data, this.onEditModel.bind(this));
        },
        edit(params) {
            this.showDialog('edit', params.data, this.onEditModel.bind(this));
        },
        showDetail(params) {
            this.showDialog('view', params.data, this.onEditModel.bind(this));
        },
        approve() {
            let rows = this.$refs.grid.getSelectedRows();
            let row = [];
            if (rows.length == 1) {
                row = rows[0];
            } else {
                this.$msg.warning('请选中一条记录!');
                return;
            }
            this.showDialog('approve', row, this.onEditModel.bind(this));
        },
        showDialog(mode, row, actionOk) {
            if (mode !== 'add' && !row) {
                this.$msg.warning('请选中一条记录!');
                return;
            }
            row.approveStatus = '01';
            let title = '新增';
            if (mode == 'copy') {
                title = '复制';
            } else if (mode == 'edit') {
                title = '编辑';
            } else if (mode == 'approve') {
                row.approveStatus = '04';
                title = '审核';
            } else if (mode == 'view') {
                title = '查看';
            }
            this.$nav.showDialog(DedMagDlg, {
                args: { row, mode, actionOk },
                width: '60%',
                title: title
            });
        },
        async remove(params) {
            const row = params.data;
            const ok = await this.$msg.ask(`确认删除吗?`);
            if (!ok) {
                return;
            }
            try {
                const p = this.$api.payFeeDedMagApi.removePayFeeDedMag(row.pkId);
                await this.$app.blockingApp(p);
                this.reloadData();
            } catch (reason) {
                this.$msg.error(reason);
            }
        },
        // 重置表格列宽
        fitColumnsWidth() {
            setTimeout(() => {
                this.$refs.grid.handleMoreCommand('autoSizeFitColumns');
            }, 500);
        },

        dealDate(date) {
            let Y = date.getFullYear();
            let M = date.getMonth() + 1 - 0 >= 10 ? Number(date.getMonth()) + 1 : '0' + (Number(date.getMonth()) + 1);
            let D = date.getDate() >= 10 ? Number(date.getDate()) : '0' + Number(date.getDate());
            return Y + '年' + M + '月' + D + '日';
        }
    }
};
</script>
<style scoped>
    .bottom {
        flex: 1;
    }
    .payDedAuth ::v-deep .gf-flex-parent .agnes-biz-pay-ded-auth .ag-root-wrapper .ag-status-bar {
        display: flex !important;
    }
</style>
