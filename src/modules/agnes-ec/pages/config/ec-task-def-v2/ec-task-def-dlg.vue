<template>
    <div style="display: flex; height: 96%;">
        <div style="height: 100%;" class="event-tree" v-if="this.mode !== 'edit'">
            <ec-task-obj-dlg ref="eventTree" :row="objRow" :mode="this.mode" :selectData="row" @setBizObjDtos="setBizObjDtos" @getSelectedNodes="getSelectedNodes" @planEdit="planEdit"></ec-task-obj-dlg>
        </div>
        <div class="event-form">
            <svg-icon v-if="!taskForm.ecReTaskDefs.length" name="empty-interface" height="45%" style="margin-top: 10%;" />
            <el-form v-show="taskForm.ecReTaskDefs.length && taskForm.ecReTaskDefs.length > 0" ref="form" :model="taskForm" :rules="rules" label-width="140px">
                <template v-for="(event, index) in taskForm.ecReTaskDefs">
                    <module-card :title="event.eventName" shadow="never" :key="index">
                        <template slot="content">
                            <el-form-item label="备注:" :prop="'ecReTaskDefs.' + index + '.remark'">
                                <gf-input placeholder="备注" v-model.trim="event.remark" clearable :max-byte-len="150"/>
                            </el-form-item>

                            <el-form-item label="逢节假日处理规则:" :prop="'ecReTaskDefs.' + index + '.holidayRule'" :rules="rules.holidayRule">
                                <gf-dict v-model="event.holidayRule" dict-type="HOLIDAY_DEALTYPE" />
                            </el-form-item>

                            <el-form-item label="事件基准日:" :prop="'ecReTaskDefs.' + index + '.baseId'" :rules="rules.baseId">
                                <el-select v-model="event.baseId" filterable clearable :disabled="event.isEditBaseId || false" placeholder="请选择">
                                    <gf-filter-option v-for="item in baseDateList" :key="item.baseId" :label="item.baseName" :value="item.baseId"></gf-filter-option>
                                </el-select>
                            </el-form-item>

                            <el-form-item label="事件规则:" :prop="'ecReTaskDefs.' + index + '.ruleId'" :rules="rules.ruleId">
                                <el-select v-model="event.ruleId" filterable clearable :disabled="event.isEditRuleId || false" placeholder="请选择" @change="refreshEventRule(event, event.ruleId)">
                                    <gf-filter-option v-for="item in eventRuleList" :key="item.ruleId" :label="item.ruleName" :value="item.ruleId"></gf-filter-option>
                                </el-select>
                            </el-form-item>
                            <el-form-item :prop="'ecReTaskDefs.' + index + '.eventRuletableData'" :rules="rules.eventRuletableData" v-if="isShowRuleParamTable[index]">
                                <div class="rule-table">
                                    <el-table :data="event.eventRuletableData" border stripe header-row-class-name="rule-header-row" header-cell-class-name="rule-header-cell" row-class-name="rule-row" cell-class-name="rule-cell">
                                        <template v-for="(col, i) in event.eventRuleParamInData[0]">
                                            <el-table-column prop="argsDisc" min-width="150" :key="i">
                                                <template slot="header">
                                                    <span v-if="col.mustFill === '1'" style="color: red;">*</span>
                                                    <span>{{ col.argsDisc }}</span>
                                                </template>
                                                <template slot-scope="scope">
                                                    <el-input v-model="scope.row[col.fieldKey]" v-if="col.fieldType === 'string'"></el-input>
                                                    <el-input type="number" v-model="scope.row[col.fieldKey]" v-else-if="col.fieldType === 'number'"></el-input>
                                                    <el-date-picker v-model="scope.row[col.fieldKey]" type="date" placeholder="选择日期" v-else-if="col.fieldType === 'date'" value-format="yyyy-MM-dd"></el-date-picker>
                                                    <gf-dict v-model="scope.row[col.fieldKey]" v-else-if="col.fieldType === 'boolean'" dict-type="GF_BOOL_TYPE"></gf-dict>
                                                    <gf-dict v-model="scope.row[col.fieldKey]" dict-type="GF_BOOL_TYPE" v-else-if="col.fieldType === 'boolean'"></gf-dict>
                                                    <el-select v-model="scope.row[col.fieldKey]" filterable clearable placeholder="请选择" v-else-if="col.fieldType === 'list'">
                                                        <gf-filter-option v-for="item in getLists(scope.row[i].fieldValueRange)" :key="item.value" :label="item.name" :value="item.value"></gf-filter-option>
                                                    </el-select>
                                                    <gf-dict v-model="scope.row[col.fieldKey]" v-else-if="col.fieldType === 'dict'" :dict-type="scope.row[i].fieldValueRange"></gf-dict>
                                                </template>
                                            </el-table-column>
                                        </template>
                                        <el-table-column v-if="event.eventRuleParamInData[0]" prop="option" label="操作" width="52" align="center">
                                            <template slot-scope="scope">
                                                <span class="option-span" @click="deleteRuleRow(index, scope.$index)">删除</span>
                                            </template>
                                        </el-table-column>
                                    </el-table>
                                    <gf-button v-if="event.eventRuleParamInData[0]" @click="addRule(index)" class="rule-add-btn" size="small">新增</gf-button>
                                </div>
                            </el-form-item>
                        </template>
                    </module-card>
                </template>
            </el-form>
            <div v-show="this.taskForm.ecReTaskDefs.length > 0 && this.isShowPlan" class="plan">
                <div style="display: flex; justify-content: flex-start; align-items: center; flex: 1;" v-show="switchVal">
                    <span style="min-width: 70px; margin-left: 90px;">方案名称:</span>
                    <gf-input width="50%" v-model="reTaskPlanDef.planName" placeholder="请输入方案名称" clearable :disabled="!isEditPlan" :max-byte-len="50"></gf-input>
                </div>
                <div>
                    <span style="min-width: 110px;">是否保存为方案:</span>
                    <el-switch v-model="switchVal" :disabled="!isEditPlan" active-color="#13ce66" inactive-color="#ff4949"></el-switch>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import ecTaskObjDlg from './ec-task-obj-dlg';

export default {
    props: {
        row: Object,
        mode: {
            type: String,
            default: 'add'
        },
        actionOk: Function
    },
    components: {
        'ec-task-obj-dlg': ecTaskObjDlg
    },
    data() {
        const checkEventRuletableData = (rule, value, callback) => {
            if (this.taskForm) {
                const ruleParamInData = this.taskForm.ecReTaskDefs[rule.field.split('.')[1]].eventRuleParamInData;
                //无参数||无必填参数 通过校验
                if (!ruleParamInData.length || !ruleParamInData[0].some(item => item.mustFill === '1')) return callback();
                //筛选必填项的key
                const mustFillKeys = ruleParamInData[0].filter(item => item.mustFill === '1').map(item => item.fieldKey);
                //将value中必填项的值转化为数组
                let valArr = mustFillKeys.map(item => {
                    return value.map(item2 => {
                        return item2[item];
                    });
                });
                //判断必填值的数组中是否有非空值
                if (valArr.some(item => !item.some(item2 => item2))) return callback('必填项中至少需要一个参数');
            }
            callback();
        };
        return {
            taskForm: {
                ecReTaskDefs: []
            },
            //基准日list
            baseDateList: [],
            //事件规则
            eventRuleList: [],

            rules: {
                holidayRule: [{ required: true, message: '逢节假日处理规则必填', trigger: 'change' }],
                baseId: [{ required: true, message: '事件基准日必填', trigger: 'change' }],
                ruleId: [{ required: true, message: '事件规则必填', trigger: 'change' }],
                eventRuletableData: [{ required: true, validator: checkEventRuletableData, trigger: 'blur' }]
            },
            reTaskPlanDef: {
                planId: '',
                planName: '',
                bizObjType: ''
            },
            bizObjDtos: [],

            objRow: {
                bizObjType: '',
                planId: '',
                objCode: ''
            },
            switchVal: false,
            isShowPlan: true,
            isEditPlan: true
        };
    },
    computed: {
        isShowRuleParamTable() {
            let arr = [];
            this.taskForm.ecReTaskDefs.forEach(item => {
                if (item.ruleId === undefined || item.ruleId === '') {
                    arr.push(false);
                } else {
                    let filter = [];
                    filter = this.eventRuleList.filter(v => {
                        return v.ruleId === item.ruleId;
                    });
                    if (filter && filter.length > 0) {
                        if (filter[0].ruleFnType === '1') {
                            arr.push(true);
                        } else {
                            arr.push(false);
                        }
                    } else {
                        arr.push(false);
                    }
                }
            });
            return arr;
        }
    },
    created() {
        this.initEventDlg();
        if (this.mode === 'planEdit') {
            if (this.row.planId) {
                this.switchVal = true;
                this.isEditPlan = false;
                this.reTaskPlanDef = {
                    planId: this.row.planId,
                    planName: this.row.planName,
                    bizObjType: this.row.bizObjType
                };
                this.planEdit();
                this.objRow.bizObjType = this.row.bizObjType;
                this.objRow.planId = this.row.planId;
            }
        }
        if (this.mode === 'edit') {
            this.initTaskForm(this.row.forms);
            this.isShowPlan = false;
            this.isEditPlan = false;
        }
    },

    methods: {
        async initTaskForm(forms) {
            this.taskForm.ecReTaskDefs = [];
            forms.forEach(row => {
                let defs = {
                    isEditBaseId: false,
                    isEditRuleId: false,
                    ruleId: '',
                    ruleParamIn: '',
                    eventRuleParamInData: [[]]
                };
                if (row.baseId && row.baseId !== '') {
                    defs.isEditBaseId = true;
                }
                if (row.ruleId && row.ruleId !== '') {
                    defs.ruleId = row.ruleId;
                    defs.isEditRuleId = true;
                    if (row.ruleParamIn && row.ruleParamIn !== '[]' && row.ruleParamIn !== '') {
                        defs.ruleParamIn = row.ruleParamIn;
                    }
                } else {
                    defs.isEditRuleId = false;
                }
                Object.assign(row, defs);
                this.taskForm.ecReTaskDefs.push(JSON.parse(JSON.stringify(row)));
                this.taskForm.ecReTaskDefs.forEach(item => {
                    this.refreshEventRule(item, item.ruleId);
                });
            });
        },

        removeTask(index) {
            if (this.taskForm.ecReTaskDefs[index].taskId && this.taskForm.ecReTaskDefs[index].taskId !== '') {
                this.taskForm.ecReTaskDefs[index].isDel = '1';
            } else {
                this.taskForm.ecReTaskDefs.splice(index, 1);
            }
        },
        async planEdit() {
            if (this.reTaskPlanDef.planId !== '' && this.reTaskPlanDef.planId !== undefined) {
                const p = this.$api.ecTaskDefApi.listTaskByPlanId(this.reTaskPlanDef.planId);
                let resp = await this.$app.blockingApp(p);
                if (resp.data) {
                    this.row.forms = resp.data;
                    this.initTaskForm(resp.data);
                }
            }
        },

        async initEventDlg() {
            //初始化基准日list
            const baseData = await this.$api.baseDateDefApi.getBaseDateList();
            if (baseData && baseData.data) {
                this.baseDateList = baseData.data;
            }
            //初始化事件规则list
            const ruleData = await this.$api.eventRuleDefApi.getRuleList();
            if (ruleData && ruleData.data) {
                this.eventRuleList = ruleData.data;
            }
        },
        async refreshEventRule(row, ruleId) {
            if (ruleId) {
                let data = {
                    params: {
                        ruleId: ruleId
                    }
                };
                row.eventRuleParamInData = [];
                row.eventRuletableData = [];
                //事件规则入参
                const eventRuleParamInData = await this.$api.eventRuleDefApi.getRuleParamIn(data);
                if (eventRuleParamInData.data && eventRuleParamInData.data.length > 0) {
                    row.eventRuleParamInData = [eventRuleParamInData.data];
                    //填入表格默认内容
                    let defalutData = {};
                    eventRuleParamInData.data.forEach(item => {
                        defalutData[item.fieldKey] = item.defaultValue || '';
                    });
                    if (row.ruleParamIn && row.ruleParamIn !== '[]') {
                        row.eventRuletableData = JSON.parse(row.ruleParamIn);
                    } else {
                        this.$set(row.eventRuletableData, 0, defalutData);
                    }
                }
            }
        },
        getLists(fieldValueRange) {
            let lists = [];
            const fieldRange = fieldValueRange.split(';');
            if (fieldRange) {
                fieldRange.forEach(item => {
                    const values = item.split(':');
                    if (values.length > 1) {
                        lists.push({
                            name: values[1],
                            value: values[0]
                        });
                    } else {
                        lists.push({
                            name: values[0],
                            value: values[0]
                        });
                    }
                });
            }
            return lists;
        },
        async onCancel() {
            this.$emit('onClose');
        },
        //保存
        async onSave() {
            if (!await this.validateForm()) return;
            try {
                const params = this.prepareParams();
                if (this.switchVal && !this.reTaskPlanDef.planName) {
                    this.$msg.warning('保存方案时，请填写方案名称');
                    return;
                }
                if (!this.validateBizObjDtos(params)) return;
                params.version = 'ectg2';
                const p = this.$api.ecTaskDefApi.saveV2Task(params);
                await this.$app.blockingView(p);
                if (this.actionOk) {
                    this.$msg.success('保存成功');
                    await this.actionOk();
                }
                this.$emit('onClose');
            } catch (reason) {
                this.$msg.error(reason);
            }
        },

        async validateForm() {
            const ok = await this.$refs['form'].validate();
            return !(!ok);
        },

        prepareParams() {
            const params = {
                bizObjDtos: [],
                reTaskPlanDef: {},
                mode: this.mode
            };
            if (this.mode === 'add' || this.mode === 'planEdit') {
                params.ecReTaskDefs = this.taskForm.ecReTaskDefs.map(def => ({
                    ...def,
                    ruleParamIn: JSON.stringify(def.eventRuletableData)
                }));
            } else if (this.mode === 'edit') {
                params.defDetail = this.taskForm.ecReTaskDefs[0];
            }
            if (this.switchVal) {
                params.reTaskPlanDef = this.reTaskPlanDef;
            }
            if (this.objRow.bizObjType && this.objRow.bizObjType !== '') {
                if (this.objRow.bizObjType === '-') {
                    params.bizObjDtos = [{ objCode: '-', objName: '-', objTypeCode: '-', objTypeName: '-' }];
                } else {
                    params.bizObjDtos = this.bizObjDtos;
                }
            }

            return params;
        },

        validateBizObjDtos(params) {
            if (this.mode !== 'edit') {
                if (this.mode === 'add' && (!params.ecReTaskDefs || params.ecReTaskDefs.length === 0)) {
                    this.$msg.warning('请选择场景事件');
                    return false;
                }
                if (!params.bizObjDtos || params.bizObjDtos.length === 0) {
                    this.$msg.warning('请选择业务对象数据');
                    return false;
                }
            }
            return true;
        },
        // 数组转对象
        arrayToObject(arr) {
            let obj = {};
            let keys = Array.from(arr, ({ eventId }) => eventId);
            keys = [...new Set(keys)];
            keys.forEach(key => {
                obj[`${key}`] = arr.filter(item => item.eventId === key);
            });
            return obj;
        },
        setBizObjDtos(params) {
            this.bizObjDtos = this.$lodash.cloneDeep(params);
        },
        getSelectedNodes(nodes) {
            let arr = [];
            let defs = this.taskForm.ecReTaskDefs;
            if (defs.length >= nodes.length) {
                arr = this.handleRemoveSelectedNodes(defs, nodes);
            } else {
                arr = this.handleAddNewNodes(defs, nodes);
            }
            this.taskForm.ecReTaskDefs = JSON.parse(JSON.stringify(arr));
            this.taskForm.ecReTaskDefs.forEach(item => {
                this.refreshEventRule(item, item.ruleId);
            });
            this.updateEventTree(arr);
        },

        handleRemoveSelectedNodes(defs, nodes) {
            if (nodes.length === 0) {
                return [];
            }
            // 求交集
            const arr = defs.filter(v => nodes.some(node => node.eventCode === v.eventId));
            this.reTaskPlanDef.bizObjType = arr[0].bizObjType;
            return arr;
        },

        handleAddNewNodes(defs, nodes) {
            const nodesArr = nodes.map((node, index) => {
                const obj = {
                    taskId: '',
                    planId: '',
                    eventName: node.eventName,
                    eventId: node.eventCode,
                    remark: '',
                    holidayRule: '',
                    baseId: node.baseId || '',
                    ruleId: node.ruleId || '',
                    ruleParamIn: node.ruleParamIn || '',
                    eventRuleParamInData: node.eventRuleParamInData || [],
                    eventRuletableData: node.eventRuletableData || [],
                    isEditBaseId: !!node.baseId,
                    isEditRuleId: !!node.ruleId,
                    bizObjType: node.bizObjType
                };
                if (index === 0) this.reTaskPlanDef.bizObjType = node.bizObjType;
                return obj;
            });
            // 在不改变左侧事件顺序的情况下新增
            const defsObj = this.arrayToObject(defs);
            const ecReTaskDefsObj = this.arrayToObject(nodesArr);
            Object.assign(ecReTaskDefsObj, defsObj);
            return Object.values(ecReTaskDefsObj).map(items => items[0]);
        },

        updateEventTree(arr) {
            if (this.mode !== 'planEdit') {
                if (arr.length === 0) {
                    this.$refs.eventTree.resetDisabled();
                    this.$refs.eventTree.setCheckedKeys([]);
                } else {
                    this.objRow.bizObjType = arr[0].bizObjType;
                    if (this.reTaskPlanDef.bizObjType) {
                        this.$refs.eventTree.setDisabledByBizObjType(this.reTaskPlanDef.bizObjType);
                    }
                    const keys = arr.filter(v => v.bizObjType === this.reTaskPlanDef.bizObjType).map(v => v.eventId);
                    this.$refs.eventTree.setCheckedKeys(keys);
                }
            }
        },
        addRule(index) {
            if (this.taskForm.ecReTaskDefs[index].eventRuleParamInData.length > 10) {
                return this.$msg.msg('最多只能新增10个参数', 'warning');
            }
            this.taskForm.ecReTaskDefs[index].eventRuletableData.push({});
        },
        deleteRuleRow(index, rowIndex) {
            this.taskForm.ecReTaskDefs[index].eventRuletableData.splice(rowIndex, 1);
        }
    }
};
</script>

<style scoped>
.event-form {
    width: 100%;
    height: 100%;
    overflow-y: scroll;
}

.event-form >>> .el-form .el-form-item:not(.is-error) .el-form-item__content input {
    font-size: 14px;
    border-color: #eaeaea;
    background: transparent;
}

.plan {
    display: flex;
    flex-direction: row;
    padding: 20px 30px;
    justify-content: flex-end;
    align-items: center;
    font-size: 14px;
}

.plan >>> .el-input {
    width: 50%;
}

.add-btn {
    width: 100px;
    height: 40px;
    margin-top: 10px;
    margin-left: 27px;
}

.event-tree >>> .move-widget:not(.move) {
    width: 300px !important;
}
</style>
