<template>
    <el-form class="event-cfg-form" :model="form" :disabled="mode === 'view'" ref="form" :rules="rules" label-width="120px" style="margin-right: 150px;">
        <module-card title="基本信息" id="nav-1" shadow="never">
            <template slot="content">
                <el-form-item label="事件名称" prop="eventCfg.eventName">
                    <gf-input type="text" v-model="form.eventCfg.eventName" />
                </el-form-item>
                <div style="width: 50%;">
                    <el-form-item label="业务场景" prop="eventCfg.bizType">
                        <gf-dict v-model="form.eventCfg.bizType" dictType="AGNES_BIZ_CASE"></gf-dict>
                    </el-form-item>

                    <el-form-item label="业务标签:" prop="eventCfg.bizTag">
                        <el-select class="multiple-select" v-model="form.bizTagArr" multiple filterable clearable collapse-tags default-first-option placeholder="请选择">
                            <gf-filter-option v-for="item in bizTagOption" :key="item.dictId" :label="item.dictName" :value="item.dictId"></gf-filter-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="场景图标:">
                        <gf-input v-model.trim="form.eventCfg.bizIcon" placeholder="场景图标" style="margin-right: 10px;" />
                        <el-popover placement="bottom" title="图标库使用" width="220" trigger="click" popper-class="question-popover">
                            <el-button slot="reference" icon="fa fa-question-circle-o" style="border: none;padding: 0;font-size: 20px;vertical-align: middle;position: absolute;left: 102%;top: 18%;}"></el-button>
                            <p>图标库中选择需要的图标对应名称，前面加上【fa fa-】前缀即可。</p>
                            <p>
                                如：
                                <span>
                                    <em class="fa fa-bus"></em>
                                    bus
                                </span>
                                填写为【fa fa-bus】
                            </p>
                            <p>
                                图标库链接地址为：
                                <a href="https://fontawesome.dashgame.com/" target="_blank" rel="noopener noreferrer" style="color: #4183c4;">点我跳转</a>
                            </p>
                        </el-popover>
                    </el-form-item>

                    <el-form-item label="事件等级:">
                        <el-rate v-model="form.eventCfg.eventLevel" :max="max" show-text :texts="texts" :colors="rateColor" style="display: inline-block;"></el-rate>
                        <em class="el-icon-refresh-left" @click="form.eventCfg.eventLevel = 0" v-show="mode !== 'view'"></em>
                    </el-form-item>
                </div>

                <el-form-item label="基准日:" prop="eventCfg.baseId">
                    <el-select v-model="form.eventCfg.baseId" filterable clearable placeholder="请选择">
                        <gf-filter-option v-for="item in baseDateList" :key="item.baseId" :label="item.baseName" :value="item.baseId"></gf-filter-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="事件规则:" prop="eventCfg.ruleId">
                    <el-select v-model="form.eventCfg.ruleId" filterable clearable placeholder="请选择" @change="refreshEventRule(form.eventCfg.ruleId)">
                        <gf-filter-option v-for="item in eventRuleList" :key="item.ruleId" :label="item.ruleName" :value="item.ruleId"></gf-filter-option>
                    </el-select>
                </el-form-item>

                <el-form-item prop="eventCfg.ruleParamInData" v-if="isShowRuleParamTable">
                    <div class="rule-table">
                        <el-table :data="form.eventRuleParamInData" border stripe header-row-class-name="rule-header-row" header-cell-class-name="rule-header-cell" row-class-name="rule-row" cell-class-name="rule-cell">
                            <template v-for="(col, i) in form.eventRuleParamInData[0]">
                                <el-table-column prop="argsDisc" min-width="150" :key="i">
                                    <template slot="header">
                                        <span v-if="col.mustFill === '1'" style="color: red;">*</span>
                                        <span>{{ col.argsDisc }}</span>
                                    </template>
                                    <template slot-scope="scope">
                                        <el-input v-model="scope.row[i].defaultValue" v-if="scope.row[i].fieldType === 'string'"></el-input>
                                        <el-input type="number" v-model="scope.row[i].defaultValue" v-else-if="scope.row[i].fieldType === 'number'"></el-input>
                                        <el-date-picker v-model="scope.row[i].defaultValue" type="date" placeholder="选择日期" v-else-if="scope.row[i].fieldType === 'date'" value-format="yyyy-MM-dd"></el-date-picker>
                                        <gf-dict v-model="scope.row[i].defaultValue" v-else-if="scope.row[i].fieldType === 'boolean'" dict-type="GF_BOOL_TYPE"></gf-dict>
                                        <gf-dict v-model="scope.row[i].defaultValue" dict-type="GF_BOOL_TYPE" v-else-if="scope.row[i].fieldType === 'boolean'"></gf-dict>
                                        <el-select v-model="scope.row[i].defaultValue" filterable clearable placeholder="请选择" v-else-if="scope.row[i].fieldType === 'list'">
                                            <el-option v-for="item in getLists(scope.row[i].fieldValueRange)" :key="item.value" :label="item.name" :value="item.value"></el-option>
                                        </el-select>
                                        <gf-dict v-model="scope.row[i].defaultValue" v-else-if="scope.row[i].fieldType === 'dict'" :dict-type="scope.row[i].fieldValueRange"></gf-dict>
                                    </template>
                                </el-table-column>
                            </template>
                        </el-table>
                    </div>
                </el-form-item>

                <el-form-item label="事件策略:" prop="eventCfg.strategyId">
                    <el-select v-model="form.eventCfg.strategyId" filterable clearable placeholder="请选择">
                        <gf-filter-option v-for="item in eventStrategyList" :key="item.strategyId" :label="item.strategyName" :value="item.strategyId"></gf-filter-option>
                    </el-select>
                </el-form-item>

                <el-form-item label="事件描述:" prop="eventCfg.eventDescribe">
                    <gf-input v-model="form.eventCfg.eventDescribe" type="textarea" :max-byte-len="100"></gf-input>
                </el-form-item>
            </template>
        </module-card>

        <module-card title="触发配置" id="nav-2" shadow="never">
            <template slot="content">
                <el-form-item label="触发周期区间:" required>
                    <div class="line none-shrink">
                        <el-form-item prop="eventCfg.startTime">
                            <el-date-picker v-model="form.eventCfg.startTime" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptionsStart" placeholder="开始日期"></el-date-picker>
                        </el-form-item>
                        <span style="margin: 0 10px;">~</span>
                        <el-form-item prop="eventCfg.endTime">
                            <el-date-picker v-model="form.eventCfg.endTime" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptionsEnd" placeholder="结束日期"></el-date-picker>
                        </el-form-item>
                        <el-checkbox v-model="form.startAllTime" style="margin-left: 10px;">永久有效</el-checkbox>
                    </div>
                </el-form-item>

                <el-form-item label="是否周期执行:" prop="eventCfg.isCycle">
                    <el-radio-group v-model="form.eventCfg.isCycle">
                        <el-radio label="1">是</el-radio>
                        <el-radio label="0">否</el-radio>
                    </el-radio-group>
                </el-form-item>

                <template v-if="form.eventCfg.isCycle === '1'">
                    <el-form-item label="选择设置类型:" prop="eventCfg.chooseSetType">
                        <el-radio-group v-model="form.eventCfg.chooseSetType">
                            <el-radio label="1">普通配置</el-radio>
                            <el-radio label="0">高级配置</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="" prop="eventCfg.cycleCron" v-if="form.eventCfg.chooseSetType === '1'">
                        <agnes-cron ref="agnesCronRef" :showRequireStar="false" :corn-obj="form.eventCfg.cycleCron"></agnes-cron>
                    </el-form-item>
                    <el-form-item label="" prop="eventCfg.cycleCron" v-if="form.eventCfg.chooseSetType === '0'">
                        <el-button type="text" @click="editExecTime(form.eventCfg.cycleCron)">
                            高级设置
                        </el-button>
                        <div>{{ form.eventCfg.cycleCron }}</div>
                    </el-form-item>
                </template>
            </template>
        </module-card>

        <module-card title="条件过滤" id="nav-3" shadow="never">
            <template slot="content">
                <el-form-item label="匹配规则:">
                    <rule-table :isDisable="mode === 'view'" ref="ruleTable" :confType="confType" :ruleTableData="form.ruleTableData" :ruleTargetOp="ruleTargetOp" :bizObjType="this.form.eventCfg.bizObjType" tableHeight="200" tableMaxHeight="300"></rule-table>
                </el-form-item>
            </template>
        </module-card>

        <gf-scroll-navigator :navigator="scrollNavigator"></gf-scroll-navigator>
    </el-form>
</template>

<script>
import fecha from 'element-ui/src/utils/date';

export default {
    props: {
        row: Object,
        mode: {
            type: String,
            default: 'add'
        },
        actionOk: Function
    },
    data() {
        const checkExecScheduler = async (rule, value, callback) => {
            if (this.form.eventCfg.isCycle === '1' && !this.form.eventCfg.cycleCron) {
                callback(new Error('请任选一种，配置触发频率！'));
            }
        };

        return {
            scrollNavigator: [
                { id: 'nav-1', value: '基本信息' },
                { id: 'nav-2', value: '触发配置' },
                { id: 'nav-3', value: '条件过滤' }
            ],
            //业务类型下拉
            bizTagOption: [],
            //任务等级
            max: 3,
            texts: ['普通', '重要', '非常重要'],
            rateColor: { 1: { value: '#409EFF' }, 2: { value: '#E6A23C' }, 3: { value: '#F00' } },
            //基准日list
            baseDateList: [],
            //事件规则
            eventRuleList: [],
            eventRuleParam: {},
            //事件策略
            eventStrategyList: [],

            form: {
                startAllTime: false, // 是否永久有效
                eventCfg: {
                    bizObjType: '',
                    eventId: '',
                    eventName: '',
                    crtObjCycle: '',
                    eventKey: '',
                    bizType: '',
                    bizTag: '',
                    bizIcon: '', //场景图标 10位数
                    eventLevel: 1, //事件等级
                    baseId: '',
                    ruleId: '',
                    ruleParamIn: '',
                    strategyId: '',
                    isCycle: '1',
                    chooseSetType: '1',
                    startTime: '',
                    endTime: '',
                    cycleCron: '',
                    matchId: '',
                    status: '',
                    eventDescribe: '', //事件描述
                    dateRange: '',
                    jobId: '',
                    noEnd: ''
                },
                eventRuleParamInData: [],
                bizTagArr: [],

                ruleTableData: {
                    ruleList: [],
                    judgeScript: '',
                    ruleBody: '',
                    ruleId: ''
                }
            },
            funModelData: [],
            rules: {
                'eventCfg.eventName': [{ required: true, message: '事件名称必填' }],
                'eventCfg.bizType': [{ required: true, message: '业务场景必填' }],
                'eventCfg.strategyId': [{ required: true, message: '事件策略必填' }],
                'eventCfg.startTime': [{ required: true, message: '触发周期开始时间必填', trigger: 'blur' }],
                'eventCfg.endTime': [{ required: true, message: '触发周期结束时间必填', trigger: 'change' }],
                'eventCfg.isCycle': [{ required: true, message: '是否周期执行必填' }],
                'eventCfg.cycleCron': [{ validator: checkExecScheduler, trigger: 'blur' }]

                // 'ruleTableData': [{ validator: checkRuleTableData, trigger: 'blur' }]
            },
            pickerOptionsEnd: {
                disabledDate: time => {
                    let beginDateVal = this.form.eventCfg.startTime;
                    if (beginDateVal) {
                        return time.getTime() < new Date(beginDateVal).getTime() - 1 * 24 * 60 * 60 * 1000;
                    }
                }
            },
            pickerOptionsStart: {
                disabledDate: time => {
                    let endDateVal = this.form.eventCfg.endTime;
                    if (endDateVal) {
                        return time.getTime() > new Date(endDateVal).getTime();
                    }
                }
            },
            funDefList: {},
            ruleTargetOp: {
                object: [{}]
            },

            modelTypeList: {},
            funcList: {}
        };
    },
    beforeMount() {
        Object.assign(this.form.eventCfg, this.row);
        this.initEventDlg();
    },
    mounted() {
        this.bizTagOption = this.$app.dict.getDictItems('AGNES_BIZ_TAG');
    },
    watch: {
        'form.startAllTime'(val) {
            if (val) {
                this.form.eventCfg.endTime = '9999-12-31';
            } else {
                this.form.eventCfg.endTime = '';
            }
        }
    },
    computed: {
        confType() {
            if (this.form.eventCfg.bizObjType === 'prdt' || this.form.eventCfg.bizObjType === 'account') {
                return 'fn,bizObj';
            } else {
                return 'fn';
            }
        },
        isShowRuleParamTable() {
            let isShow = false;
            this.eventRuleList.forEach(v => {
                if (v.ruleId === this.form.eventCfg.ruleId) {
                    if (v.ruleFnType === '1') {
                        isShow = true;
                    }
                }
            });
            return isShow;
        }
    },
    methods: {
        async initEventDlg() {
            if (this.mode && this.mode !== 'add') {
                this.$nextTick(async () => {
                    await this.fetchMatchRuleDef();
                    await this.fetchMatchRuleDetail();
                });
            }
            //初始化基准日list
            const baseData = await this.$api.baseDateDefApi.getBaseDateList();
            if (baseData && baseData.data) {
                this.baseDateList = baseData.data;
            }
            //初始化事件规则list
            const ruleData = await this.$api.eventRuleDefApi.getRuleList();
            if (ruleData && ruleData.data) {
                this.eventRuleList = ruleData.data;
            }
            //初始化事件策略list
            const strategyData = await this.$api.eventStrategyApi.getStrategyList();
            if (strategyData && strategyData.data) {
                this.eventStrategyList = strategyData.data;
            }
            if (this.form.eventCfg.ruleParamIn) {
                this.form.eventRuleParamInData = [JSON.parse(this.form.eventCfg.ruleParamIn)];
            }
            if (this.form.eventCfg.bizTag) {
                this.form.bizTagArr = this.form.eventCfg.bizTag.split(',');
            }

            //触发区间 是否永久有效判断
            if (this.mode && this.mode !== 'add') {
                if (this.form.eventCfg.endTime && this.form.eventCfg.endTime.toString().startsWith('9999-12-31')) {
                    this.form.startAllTime = true;
                }
            }
            this.clearForCopy();
        },
        /**
         * 刷新事件规则参数
         * @param ruleId
         * @returns {Promise<void>}
         */
        async refreshEventRule(ruleId) {
            if (ruleId) {
                let data = {
                    params: {
                        ruleId: ruleId
                    }
                };

                let q = this.eventRuleList.filter(item => {
                    return item.ruleId === ruleId;
                });
                if (q && q.length === 1) {
                    if (q[0].ruleFnType === '1') {
                        this.form.eventCfg.isCycle = '1';
                    } else {
                        this.form.eventCfg.isCycle = '0';
                    }
                }

                this.form.eventRuleParamInData = [];

                //事件规则入参
                const eventRuleParamInData = await this.$api.eventRuleDefApi.getRuleParamIn(data);
                if (eventRuleParamInData.data && eventRuleParamInData.data.length > 0) {
                    this.form.eventRuleParamInData = [eventRuleParamInData.data];
                }
            } else {
                this.form.eventRuleParamInData = [];
                this.form.eventCfg.isCycle = '1';
            }
        },

        getLists(fieldValueRange) {
            let lists = [];
            const fieldRange = fieldValueRange.split(';');
            if (fieldRange) {
                fieldRange.forEach(item => {
                    const values = item.split(':');
                    if (values.length > 1) {
                        lists.push({
                            name: values[1],
                            value: values[0]
                        });
                    } else {
                        lists.push({
                            name: values[0],
                            value: values[0]
                        });
                    }
                });
            }
            return lists;
        },

        async fetchMatchRuleDef() {
            try {
                if (this.form.eventCfg.matchId) {
                    const resp = await this.$api.ruleConfigApi.getRuleDef(this.form.eventCfg.matchId);
                    this.form.ruleTableData.judgeScript = resp.data.judgeScript;
                    this.form.ruleTableData.ruleBody = resp.data.ruleBody;
                    this.form.ruleTableData.ruleId = resp.data.ruleId;
                }
            } catch (reason) {
                this.$msg.error(reason);
            }
        },
        async fetchMatchRuleDetail() {
            try {
                if (this.form.eventCfg.matchId) {
                    const resp = await this.$api.ruleConfigApi.getRuleDetailList(this.form.eventCfg.matchId);
                    this.form.ruleTableData.ruleList = [];
                    if (resp && resp.data) {
                        resp.data.forEach(item => {
                            item.ruleParamKey = item.ruleTarget.split('_')[0];
                            this.form.ruleTableData.ruleList.push(item);
                        });
                    }
                    if (this.$refs.ruleTable) {
                        this.$refs.ruleTable.reloadInitDate();
                    }
                }
            } catch (reason) {
                this.$msg.error(reason);
            }
        },

        async onSave() {
            // 获取cron表达式返回值
            this.getCron();
            const ok = await this.$refs['form'].validate();
            if (!ok) {
                return;
            }

            try {
                let msg = '';
                //如果是审核
                if (this.row.isCheck) {
                    const p = this.$api.eventCfgApi.approveEventCfg(this.form.eventCfg.eventId);
                    await this.$app.blockingApp(p);
                    msg = '审核成功';
                } else {
                    // 定义需要校验的列数组，数组内容为列filed值
                    const validatefieldArr = ['ruleParamKey', 'ruleParam', 'ruleKey', 'ruleSign', 'ruleValue'];
                    const validator = this.$refs.ruleTable.validator(validatefieldArr);
                    if (!validator) {
                        // 验证失败
                        return;
                    }

                    if (this.form.bizTagArr && this.form.bizTagArr.length > 0) {
                        this.form.eventCfg.bizTag = this.form.bizTagArr.join(',');
                    }
                    if (this.form.eventRuleParamInData && this.form.eventRuleParamInData.length > 0) {
                        this.validatorData();
                    }
                    // 获取规则表格整理后的json对象串
                    const outputJson = this.$refs.ruleTable.jsonFormatter();
                    this.form.ruleTableData.ruleBody = outputJson;

                    this.clearForCopy();
                    const p = this.$api.eventCfgApi.saveEventCfg(this.form);
                    await this.$app.blockingApp(p);
                    msg = '保存成功';
                }

                if (this.actionOk) {
                    await this.actionOk(this.form, this.row);
                }
                this.$msg.success(msg);
                this.$emit('onClose');
            } catch (reason) {
                this.$msg.error(reason);
            }
        },
        getCron(){
            if (this.form.eventCfg.isCycle === '1') {
                if (this.$refs.agnesCronRef) {
                    this.form.eventCfg.cycleCron = this.$refs.agnesCronRef.getValue();
                }
                if (!this.form.eventCfg.cycleCron || this.form.eventCfg.cycleCron === '') {
                    this.$msg.error('周期触发时，触发配置必选');
                    return;
                }
            }
        },
        validatorData(){
            let flag = false;
            this.form.eventRuleParamInData[0].forEach(item => {
                if (item && item.mustFill && item.mustFill === '1' && (!item.defaultValue || item.defaultValue === '')) {
                    flag = true;
                }
            });
            if (flag) {
                this.$msg.error('参数必填项不能为空');
                return;
            }
            this.form.eventCfg.ruleParamIn = JSON.stringify(this.form.eventRuleParamInData[0]);
        },
        async onCancel() {
            this.$emit('onClose');
        },
        showDlg(data, action) {
            this.$nav.showDialog('gf-cron-modal', {
                showType: 'minute,hour,day,week,month,year,extSetting',
                args: { cornObj: data, action },
                width: '530px',
                title: this.$dialog.formatTitle('编辑频率', 'edit')
            });
        },
        setExecScheduler(cron) {
            this.form.eventCfg.cycleCron = cron;
        },
        editExecTime(cycleCron) {
            this.showDlg(cycleCron, this.setExecScheduler.bind(this));
        },
        onNoEndChange() {
            var startDate = this.form.eventCfg.dateRange[0];
            if (!startDate) {
                startDate = fecha.format(new Date(), 'yyyy-MM-dd');
            }
            if (this.form.eventCfg.noEnd === true) {
                this.$set(this.form.eventCfg, 'dateRange', [startDate, '9999-12-31']);
            } else {
                this.$set(this.form.eventCfg, 'dateRange', [startDate, startDate]);
            }

            if (this.form.eventCfg.dateRange[0]) {
                this.form.eventCfg.startTime = this.form.eventCfg.dateRange[0];
            }
            if (this.form.eventCfg.dateRange[1]) {
                this.form.eventCfg.endTime = this.form.eventCfg.dateRange[1];
            }
        },
        onDateRangeChange() {
            if (this.form.eventCfg.dateRange[0]) {
                this.form.eventCfg.startTime = this.form.eventCfg.dateRange[0];
            }
            if (this.form.eventCfg.dateRange[1]) {
                this.form.eventCfg.endTime = this.form.eventCfg.dateRange[1];
            }
        },

        clearForCopy() {
            if (this.mode && this.mode === 'copy') {
                this.form.eventCfg.eventId = '';
                this.form.eventCfg.matchId = '';
                this.form.eventCfg.jobId = '';
                this.form.ruleTableData.ruleId = '';
                this.form.ruleTableData.ruleList.forEach(item => {
                    item.ruleDetailId = '';
                });
            }
        }
    }
};
</script>

<style scoped>
.event-cfg-form {
    width: 80%;
    padding: 0px !important;
}

.el-icon-refresh-left {
    color: #0f5eff;
    margin-left: 10px;
    vertical-align: text-top;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
}
</style>
