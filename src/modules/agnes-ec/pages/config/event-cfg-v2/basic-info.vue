<!--
 * @Des: 
 * @version: 
 * @Author: liufeifei
 * @Date: 2024-07-22 14:13:59
 * LastEditors: yuelanfenghua
 * LastEditTime: 2025-01-04 20:02:52
-->
<template>
    <el-form ref="form" class="event-def-basic-info-form" :model="form" :rules="rules" label-width="100px" :disabled="this.mode === 'view' || this.mode === 'check'">
        <div class="line">
            <el-form-item label="策略名称:" prop="eventName">
                <gf-input type="text" v-model="form.eventName" />
            </el-form-item>
            <el-form-item label="策略分类:" prop="eventType">
                <gf-dict v-model="form.eventType" dictType="AGNES_EC_EVEN_TYPE" :disabled="true"></gf-dict>
            </el-form-item>
        </div>
        <div class="line">
            <el-form-item class="encoding-rules" label="编码规则:" prop="encodingRule">
                <gf-input v-model="form.encodingRule" :max-byte-len="8" placeholder="编码规则为八位以内的数字" :disabled="true">
                    <template slot="prepend">{{ this.modelPrefix }}-</template>
                </gf-input>
            </el-form-item>
            <el-form-item label="名称显示:" prop="nameDisplay">
                <gf-input v-model="nameDisplay" placeholder="名称(规则)" disabled></gf-input>
            </el-form-item>
        </div>
        <div class="line">
            <el-form-item label="业务分类:" prop="bizType">
                <gf-dict v-model="form.bizType" dictType="AGNES_BIZ_CASE" filterable clearable></gf-dict>
            </el-form-item>
            <el-form-item label="策略等级:" prop="eventLevel">
                <el-rate v-model="form.eventLevel" :max="max" show-text :texts="texts" :colors="rateColor" style="display: inline-block;"></el-rate>
                <em class="el-icon-refresh-left" @click="form.eventLFevel = 0" v-show="true"></em>
            </el-form-item>
        </div>
        <div class="line">
            <el-form-item label="业务对象:" prop="bizObjType" v-if="eventType != 'webHookStrategy'" style="width: 50%;">
                <el-select v-model="form.bizObjType" @change="setBizObjType" filterable clearable placeholder="请选择">
                    <gf-filter-option v-for="item in bizObjTypeOptions" :key="item.datatype" :label="item.datatypeName" :value="item.datatype"></gf-filter-option>
                </el-select>
            </el-form-item>
        </div>
        <div class="line">
            <el-form-item label="启动场景:" prop="caseKey" style="width: 50%;">
                <el-select v-model="form.caseKey" @change="caseChange" filterable clearable placeholder="请选择">
                    <gf-filter-option v-for="item in caseList" :key="item.taskId" :label="item.taskName" :value="item.taskId"></gf-filter-option>
                </el-select>
            </el-form-item>
        </div>
        <div class="line">
            <el-form-item label="启动参数:" prop="caseModelParams" v-if="form.caseModelParams || hasParam" style="width: 60%;">
                <div class="rule-table">
                    <el-table header-row-class-name="rule-header-row" header-cell-class-name="rule-header-cell" row-class-name="rule-row" cell-class-name="rule-cell" :data="caseModelParams" border stripe :header-cell-style="{ 'text-align': 'center' }">
                        style="width: 100%">
                        <el-table-column label="启动参数字段:">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.field" disabled></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column label="启动参数名称:">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.fieldName" disabled></el-input>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="eventType != 'webHookStrategy'" label="业务对象关系字段:">
                            <template slot-scope="scope">
                                <el-select v-model="scope.row.bizObjFieldCode" filterable clearable>
                                    <gf-filter-option v-for="item in xcpOptions" :key="item.field" :label="item.fieldName" :value="item.field"></gf-filter-option>
                                </el-select>
                            </template>
                        </el-table-column>
                        <el-table-column v-if="eventType === 'webHookStrategy'" label="参数对象关系字段:">
                            <template slot-scope="scope">
                                <el-input v-model="scope.row.fieldValue"></el-input>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </el-form-item>
        </div>
        <div class="line">
            <el-form-item label="策略描述:" prop="eventDescribe">
                <gf-input v-model="form.eventDescribe" type="textarea" :rows="6" :max-byte-len="100"></gf-input>
            </el-form-item>
        </div>
    </el-form>
</template>

<script>
export default {
    name: 'basic-info',
    props: {
        basicInfo: {
            type: Object
        },
        mode: {
            type: String,
            default: 'add'
        },
        eventType: {
            type: String
        }
    },

    data() {
        const checkencodingRule = (rule, value, callback) => {
            let numberReg = /^\d+(\.\d+)?$/;
            if (value === '') {
                callback(new Error('编码规则不能为空'));
            } else if (!numberReg.test(value)) {
                callback(new Error('编码规则位数字'));
            } else if (value.length !== 8) {
                callback(new Error('编码规则为8位'));
            } else {
                callback();
            }
        };
        return {
            rules: {
                eventName: [{ required: true, message: '请填写策略名称', trigger: 'blur' }],
                eventType: [{ required: true, message: '请选择策略类别', trigger: 'blur' }],
                bizType: [{ required: true, message: '请选择业务分类', trigger: 'blur' }],
                eventLevel: [{ required: true, message: '请选择策略级别', trigger: 'blur' }],
                caseKey: [{ required: false, message: '请选择case场景', trigger: 'blur' }],
                bizObjType: [{ required: this.eventType !== 'frequencyStrategy', message: '请选择业务对象', trigger: 'blur' }],
                encodingRule: [{ required: true, validator: checkencodingRule, trigger: 'blur' }]
            },
            form: {
                eventId: '',
                eventName: '',
                eventType: '',
                encodingRule: '',
                bizObjType: '-',
                eventKey: '',
                eventCode: '',
                bizType: '',
                eventLevel: 1, //策略等级
                eventDescribe: '', //策略描述
                caseKey: '', //case场景
                caseModelCode: '',
                caseModelParams: ''
            },
            eventCategoryOptions: [{ dictName: '产品策略类', dictId: '1' }],
            bizObjTypeOptions: [{ name: '', code: '' }],
            caseList: [],
            eventRuleParamInData: [],
            //业务类型下拉
            bizTagOption: [],
            //任务等级
            max: 3,
            texts: ['普通', '重要', '非常重要'],
            rateColor: { 1: { value: '#409EFF' }, 2: { value: '#E6A23C' }, 3: { value: '#F00' } },
            //基准日list
            baseDateList: [],
            //策略规则
            eventRuleList: [],
            eventRuleParam: {},
            model: 'event', //类型
            modelPrefix: 'event', //编码前缀
            xcpOptions: [], //xcp参数字段
            hasParam: false, //场景
            caseModelFieldOption: [],
            caseModelParams: []
        };
    },
    computed: {
        //名称规则显示
        nameDisplay() {
            if (this.form.eventName && this.form.encodingRule) {
                return `${this.form.eventName}(${this.modelPrefix}-${this.form.encodingRule})`;
            } else {
                return '';
            }
        }
    },
    created() {
        this.$app.registerCmd('eventdlg.basic.v2.initCode', async () => {
            const  res = await this.initCode()
            return res
        });
    },
        async mounted() {
            await this.initBizObjData();
            await this.initCase("");
            if (this.basicInfo) {
                const {eventId, eventName, eventType, encodingRule,
                    eventKey, eventCode, eventLevel,bizType,
                    eventDescribe, caseKey,bizObjType,caseModelCode,
                    caseModelParams
                } = this.basicInfo
                this.form = {...this.form,eventId, eventName, eventType, encodingRule,
                    eventKey, eventCode,  eventLevel,bizType,
                    eventDescribe, caseKey,bizObjType,caseModelCode,
                    caseModelParams
                }
                if (this.basicInfo.caseKey){
                    if (this.basicInfo.caseModelParams){
                        this.caseModelParams = JSON.parse(this.basicInfo.caseModelParams)
                    }else{
                        await this.caseChange(this.form.caseKey)
                    }
                }
            }
            if (this.mode === 'add' || this.mode === 'copy'){
                await this.initCode();
                if (this.eventType){
                    this.form.eventType = this.eventType
                }
            }else{
                this.modelPrefix =  this.basicInfo.modelPrefix || this.modelPrefix
            }
            if (this.mode !== 'add' ){
                await this.setBizObjType(this.basicInfo.bizObjType)

            }
        },
        methods: {

            //返回表单内容 先进行校验 如若不符 返回false
            getData() {
                let data = null;
                this.$refs['form'].validate(validate => {
                    if (!validate) {
                        data = false;
                    } else {
                        data = {
                            ...this.form,
                            eventCode: `${this.modelPrefix}-${this.form.encodingRule}`,
                        }
                        if (this.caseModelParams && this.caseModelParams.length > 0){
                            data.caseModelParams = JSON.stringify(this.caseModelParams)
                        }
                    }
                });
                return data;
            },

        /**
         * 初始化业务对象
         */
        async initBizObjData() {
            let params = {
                apiCode: this.$api.XcpApiCode.queryListDatatype()
            };
            let res = await this.$api.XcpModelApi.execute(params);
            this.bizObjTypeOptions = res.data;
        },

        /**
         * event-code 编码
         * @returns {Promise<void>}
         */
        async initCode() {
            const resp = await this.$api.codeGeneratorApi.getCode(this.model);
            if (resp && resp.data) {
                this.modelPrefix = resp.data.split('-')[0];
                this.form.encodingRule = resp.data.split('-')[1];
            }
        },

        /**
         * 初始化场景
         * @param val
         * @returns {Promise<void>}
         */
        async initCase(val) {
            const caseData = await this.$api.caseSceneApi.getCaseSceneByObj(val);
            if (caseData && caseData.data) {
                this.caseList = caseData.data;
            }
        },

        /**
         * 联动赋值
         * @param val
         * @returns {Promise<void>}
         */
        async setBizObjType(val) {
            const bizObj = this.bizObjTypeOptions.find(item => item.datatype === val);
            if (bizObj) {
                await this.initXcpObj(bizObj);
                if (this.eventType === 'bizObjStrategy') {
                    this.$app.runCmd('eventdlg.bizObj.v2.setBizObj', { bizObjType: val, xcpOptions: this.xcpOptions });
                } else if (this.eventType === 'frequencyStrategy') {
                    this.$app.runCmd('eventdlg.frequency.v2.setBizObjType', val);
                }
                this.$app.runCmd('eventdlg.filter.v2.setBizObjType', val);
                this.$app.runCmd('eventdlg.taskMsg.v2.setBizObjType', val);
            } else {
                this.$app.runCmd('eventdlg.filter.v2.setBizObjType', '');
                this.$app.runCmd('eventdlg.taskMsg.v2.setBizObjType', '');
            }
        },
        /**
         * 业务对象字段信息
         * @param bizObj
         * @returns {Promise<void>}
         */
        async initXcpObj(bizObj) {
            this.xcpOptions = [];
            const xcp = await this.$api.xcpInfoApi.getXcpModelFiledList({ dataType: bizObj.datatype });
            if (xcp && xcp.main && xcp.main.fieldList && xcp.main.fieldList.length > 0) {
                xcp.main.fieldList.forEach(item => {
                    this.xcpOptions.push({ field: item.field, fieldName: item.fieldName });
                });
            }
        },
        /**
         * case关联启动参数-这里做关系映射
         * @param taskId
         * @returns {Promise<void>}
         */
        async caseChange(taskId) {
            this.form.caseModelCode = '';
            this.hasParam = false;
            let taskInfo = this.caseList.find(item => item.taskId === taskId);
            if (taskInfo) {
                this.form.caseModelCode = taskInfo.caseKey;
                if (taskInfo.actionParam) {
                    if (JSON.parse(taskInfo.actionParam).length && JSON.parse(taskInfo.actionParam).length > 0) {
                        this.caseModelParams = JSON.parse(taskInfo.actionParam);
                        this.hasParam = true;
                    }
                }
            }
        }
    }
};
</script>

<style scoped>
.event-def-basic-info-form {
    padding: 0;
}

.event-def-basic-info-form >>> .el-tabs__content {
    overflow-y: scroll;
}

.event-def-basic-info-form >>> .encoding-rules.el-form-item .el-form-item__content input {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.event-def-basic-info-form >>> .el-form-item .el-form-item__content .el-input-group__prepend {
    border-color: #a8aed3;
}
</style>
