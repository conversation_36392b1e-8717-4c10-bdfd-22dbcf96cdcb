<template>
  <module-card title="账户信息" shadow="never" style="position: relative;">
    <template slot="content">
      <div class="receipt-upload">
        <el-upload
          v-if="!isDisabled"
          :data="{ folderTag: '2', docId: form.receiptDocId }"
          :on-success="handleSuccess"
          :show-file-list="false"
          :action="uploadUrl"
          :disabled="true"
          ref="upload"
          multiple
          :on-error="uploadError"
          :before-upload="beforeUpload"
          v-loading.fullscreen.lock="uploadFileLoading"
        >
          <el-button type="primary" icon="el-icon-upload" @click="handleUpload"
            >上传回执</el-button
          >
        </el-upload>
      </div>
      <template v-if="form.receiptDoc.length > 0">
        <el-form-item label="回执材料">
          <div
            class="receipt-file-container"
            :style="{ height: 28 * form.receiptDoc.length + 100 + 'px' }"
            style="margin-bottom: 18px"
          >
            <gf-grid
              ref="grid"
              toolbar=" "
              @grid-ready="onGridReady"
              :filterRemote="false"
              grid-no="acnt-receipt-file"
              height="100%"
            ></gf-grid>
          </div>
        </el-form-item>
      </template>
      <el-form-item label="银行账户" prop="acctBankInfo">
        <div class="rule-table">
          <el-table
            header-row-class-name="rule-header-row"
            header-cell-class-name="rule-header-cell"
            row-class-name="rule-row"
            cell-class-name="rule-cell"
            :data="form.acctBankInfo"
            border
            stripe
          >
            <el-table-column prop="acctName" label="账户名称">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.acctName"
                  :disabled="isDisabled"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="acctNo" label="账号">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.acctNo"
                  :disabled="isDisabled"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="openBankName" label="开户行名称">
              <template slot-scope="scope">
                <el-select
                  v-model="scope.row.openBankName"
                  :disabled="isDisabled"
                  filterable
                  @change="(e) => openBankNameChange(e, scope.row)"
                >
                  <el-option
                    v-for="item in openBankNameList"
                    :key="item.extOrgId"
                    :label="item.extOrgName"
                    :value="item.extOrgName"
                  >
                  </el-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="bigPayNo" label="大额支付号">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.bigPayNo"
                  :disabled="true"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="currency" label="币种">
              <template slot-scope="scope">
                <gf-dict
                  v-model="scope.row.currency"
                  dict-type="AGNES_ACNT_CURRENCY_TYPE"
                  filterable
                  clearable
                  :disabled="isDisabled"
                />
              </template>
            </el-table-column>
            <el-table-column prop="openDt" label="开户日期">
              <template slot-scope="scope">
                <el-date-picker
                  v-model="scope.row.openDt"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="开户日期"
                  :disabled="isDisabled"
                >
                </el-date-picker>
              </template>
            </el-table-column>
            <el-table-column prop="valid" label="账户有效期">
              <template slot-scope="scope">
                <el-date-picker
                  v-model="scope.row.valid"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="账户有效期"
                  :disabled="isDisabled"
                >
                </el-date-picker>
              </template>
            </el-table-column>
            <el-table-column prop="cancelDt" label="销户日期">
              <template slot-scope="scope">
                <el-date-picker
                  v-model="scope.row.cancelDt"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="销户日期"
                  :disabled="isDisabled"
                >
                </el-date-picker>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.remark"
                  :disabled="isDisabled"
                  :maxlength="50"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!isDisabled"
              prop="option"
              label="操作"
              align="center"
            >
              <template slot-scope="scope">
                <span
                  class="option-span"
                  @click="deleteBankAccRuleRow(scope.$index)"
                  >删除</span
                >
              </template>
            </el-table-column>
          </el-table>
          <el-button
            v-if="!isDisabled"
            @click="addBankAccRule"
            class="rule-add-btn"
            size="small"
            >新增</el-button
          >
        </div>
      </el-form-item>
      <el-form-item label="交易账户" prop="acctTradeInfo">
        <div class="rule-table">
          <el-table
            header-row-class-name="rule-header-row"
            header-cell-class-name="rule-header-cell"
            row-class-name="rule-row"
            cell-class-name="rule-cell"
            :data="form.acctTradeInfo"
            border
            stripe
          >
            <el-table-column prop="acctType" label="账户类型">
              <template slot-scope="scope">
                <gf-dict
                  v-model="scope.row.acctType"
                  dict-type="ACCT_TRADE_TYPE"
                  filterable
                  clearable
                  :disabled="isDisabled"
                />
              </template>
            </el-table-column>
            <el-table-column prop="acctName" label="账户名称">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.acctName"
                  :disabled="isDisabled"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="acctNo" label="账号">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.acctNo"
                  :disabled="isDisabled"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="dealMarket" label="交易市场">
              <template slot-scope="scope">
                <gf-dict
                  v-model="scope.row.dealMarket"
                  dict-type="TRADE_MARKET"
                  filterable
                  clearable
                  :disabled="isDisabled"
                />
              </template>
            </el-table-column>
            <el-table-column prop="openDt" label="开户日期">
              <template slot-scope="scope">
                <el-date-picker
                  v-model="scope.row.openDt"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="开户日期"
                  :disabled="isDisabled"
                >
                </el-date-picker>
              </template>
            </el-table-column>
            <el-table-column prop="valid" label="账户有效期">
              <template slot-scope="scope">
                <el-date-picker
                  v-model="scope.row.valid"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="账户有效期"
                  :disabled="isDisabled"
                >
                </el-date-picker>
              </template>
            </el-table-column>
            <el-table-column prop="cancelDt" label="销户日期">
              <template slot-scope="scope">
                <el-date-picker
                  v-model="scope.row.cancelDt"
                  type="date"
                  format="yyyy-MM-dd"
                  value-format="yyyy-MM-dd"
                  placeholder="销户日期"
                  :disabled="isDisabled"
                >
                </el-date-picker>
              </template>
            </el-table-column>
            <el-table-column prop="other" label="其他">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.other"
                  :disabled="isDisabled"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注">
              <template slot-scope="scope">
                <el-input
                  v-model="scope.row.remark"
                  :disabled="isDisabled"
                  :maxlength="50"
                ></el-input>
              </template>
            </el-table-column>
            <el-table-column
              v-if="!isDisabled"
              prop="option"
              label="操作"
              width="52"
              align="center"
            >
              <template slot-scope="scope">
                <span
                  class="option-span"
                  @click="deleteTradAccRuleRow(scope.$index)"
                  >删除</span
                >
              </template>
            </el-table-column>
          </el-table>
          <el-button
            v-if="!isDisabled"
            @click="addTradAccRule"
            class="rule-add-btn"
            size="small"
            >新增</el-button
          >
        </div>
      </el-form-item>
    </template>
  </module-card>
</template>

<script>
export default {
  props: {
    form: {
      type: Object,
      default: () => ({ acctBankInfo: [], acctTradeInfo: [] }),
    },
    isDisabled: { type: Boolean, default: false }
  },
  data() {
    return {
      uploadUrl: `${process.env.VUE_APP_BASE_API}/ecm-server/ecm/doc/upload`,
      openBankNameList: [],
      uploadFileLoading: false
    }
  },
  mounted() {
    this.getOpenBankNameList()
  },
  methods: {
    async getOpenBankNameList() {
      try {
        const resp = await this.$api.AcntApplyApi.getAllOrg()
        if (resp.data) {
          this.openBankNameList = resp.data
        }
      } catch (err) {
        Promise.reject(err)
      }
    },
    openBankNameChange(openBankName, row) {
      let temp = this.openBankNameList.filter(
        (item) => item.extOrgName === openBankName
      );
      if (temp.length > 0) {
        row.openBank = temp[0].extOrgId;
        row.bigPayNo = temp[0].extOrgRemark;
      }
    },
    onGridReady() {
      this.$nextTick(() => {
        const resp = this.form.receiptDoc.map((item) => ({
          ...item,
          acctStatus: this.form.acctStatus,
        }))
        this.$refs.grid.setRowData(resp)
      })
    },
    onDownload(params) {
      if (params.data && params.data.objectId) {
        const downUrl =
          'api/ecm-server/ecm/file/download/' + params.data.objectId
        window.open(encodeURI(downUrl))
      }
    },
    onDel(params) {
      this.form.receiptDoc.splice(params.rowIndex, 1)
      this.$nextTick(() => {
        this.$refs.grid.setRowData(this.form.receiptDoc)
      })
    },
    async handleUpload() {
      if (!this.form.receiptDocId) {
        try {
          const resp = await this.$api.acntApplyApi.createDocAndGetDocId('2')
          if (resp.data) {
            this.$set(this.form, 'receiptDocId', resp.data.objectId)
          }
        } catch (err) {
          Promise.reject(err)
        }
      }
      // 手动触发文件选择窗口
      this.$refs.upload.$el.querySelector('input').click()
    },
    beforeUpload(){
      this.uploadFileLoading = true
    },
    uploadError(){
      this.uploadFileLoading = false
    },
    handleSuccess(resp) {
      if (resp.status && resp.data) {
        this.form.receiptDoc.push({
          ...resp.data.files[0],
          docId: this.form.receiptDocId,
          acctStatus: '04',
        })
        this.uploadFileLoading = false
        this.$message({ type: 'success', message: '文件上传成功' })
        this.$nextTick(() => {
          this.$refs.grid && this.$refs.grid.setRowData(this.form.receiptDoc)
        })
      }
    },
    async deleteBankAccRuleRow(i) {
      const ok = await this.$msg.ask(`是否删除该文件?`);
      if (ok) {
        this.form.acctBankInfo.splice(i, 1)
      }
    },
    addBankAccRule() {
      console.log(1)
      this.form.acctBankInfo.push({
        acctName: '',
        acctNo: '',
        openBank: '',
        openBankName: '',
        bigPayNo: '',
        currency: '',
        openDt: '',
        valid: '9999-12-31',
        remark: '',
      })
    },
    async deleteTradAccRuleRow(i) {
      const ok = await this.$msg.ask(`是否删除该文件?`);
      if (ok) {
        this.form.acctTradeInfo.splice(i, 1)
      }
    },
    addTradAccRule() {
      this.form.acctTradeInfo.push({
        acctType: '',
        acctName: '',
        acctNo: '',
        dealMarket: '',
        openDt: '',
        valid: '9999-12-31',
        other: '',
        remark: '',
      })
    },
  },
}
</script>

<style scoped>
.receipt-upload {
  position: absolute;
  top: 5px;
  left: 140px;
}
</style>
