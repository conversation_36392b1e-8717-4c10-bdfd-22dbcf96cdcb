<template>
    <el-form ref="taskDefForm" class="task-def-form" :model="detailForm" :disabled="mode === 'view'" :rules="detailFormRules" label-width="110px">
        <module-card title="基本信息" shadow="never">
            <template slot="content">
                <el-form-item label="任务名称:" prop="taskName">
                    <gf-input v-model.trim="detailForm.taskName" placeholder="任务名称" :max-byte-len="120" />
                </el-form-item>
                <el-form-item label="任务等级:">
                    <el-rate v-model="detailForm.stepLevel" show-text :max="3" :low-threshold="1" :high-threshold="3" :texts="['一般', '重要', '紧急']" :colors="detailForm.rateColor"></el-rate>
                    <em class="el-icon-refresh-left" @click="detailForm.stepLevel = 0"></em>
                </el-form-item>
                <el-form-item label="任务编号:" prop="caseKey">
                    <gf-input v-model.trim="detailForm.caseKey" clear-regex="[^0-9]" placeholder="任务编号" :max-byte-len="8" />
                </el-form-item>
                <el-form-item label="业务场景:" prop="bizType">
                    <gf-dict filterable clearable v-model="detailForm.bizType" dict-type="AGNES_BIZ_CASE" />
                </el-form-item>
                <el-form-item label="业务标签:" prop="bizTag">
                    <el-select style="width: 100%;" v-model="detailForm.bizTagArr" placeholder="请选择" filterable clearable multiple>
                        <gf-filter-option v-for="item in bizTagOption" :key="'biz' + item.dictId" :label="item.dictName" :value="item.dictId"></gf-filter-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="数据采集方案:" prop="stepActParam1">
                    <el-select style="width: 100%;" v-model="detailForm.stepActParam1" placeholder="请选择" filterable clearable>
                        <gf-filter-option v-for="item in dataAcqOptions" :key="'dataAcq' + item.pkId" :label="item.dataAcqName" :value="item.pkId"></gf-filter-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="任务说明:" prop="stepRemark">
                    <gf-input type="textarea" v-model.trim="detailForm.stepRemark" placeholder="任务说明" />
                </el-form-item>
            </template>
        </module-card>

        <module-card title="运行规则" shadow="never">
            <template slot="content">
                <el-form-item label="运行周期配置:" required>
                    <div class="line none-shrink">
                        <el-form-item prop="task_startTime">
                            <el-date-picker v-model="detailForm.task_startTime" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptionsStart" placeholder="开始日期"></el-date-picker>
                        </el-form-item>
                        <span style="margin: 0 10px;">~</span>
                        <el-form-item prop="task_endTime">
                            <el-date-picker v-model="detailForm.task_endTime" type="date" value-format="yyyy-MM-dd" :picker-options="pickerOptionsEnd" placeholder="结束日期" :disabled="startAllTime === '1'"></el-date-picker>
                        </el-form-item>
                        <gf-strbool-checkbox v-model="startAllTime" style="margin-left: 10px;">永久有效</gf-strbool-checkbox>
                    </div>
                </el-form-item>
                <el-form-item label="创建方式选择:" prop="task_execMode">
                    <el-radio-group v-model="detailForm.task_execMode">
                        <el-radio label="1">按运行周期创建一次</el-radio>
                        <el-radio label="2">按自定义频率创建</el-radio>
                        <el-radio label="3">按外部事件触发时创建</el-radio>
                    </el-radio-group>
                </el-form-item>
                <template v-if="detailForm.task_execMode == 2">
                    <el-form-item label="创建频率配置:" prop="step_execScheduler">
                        <el-button type="text" @click="editExecTime('task_execScheduler', detailForm.task_execScheduler, '创建频率配置')">{{ detailForm.task_execScheduler }}点击配置</el-button>
                    </el-form-item>
                </template>
                <el-form-item label="外部事件选择:" v-if="detailForm.task_execMode == 3">
                    <el-select v-model="eventKey" placeholder="请选择" filterable clearable style="width: 64%;">
                        <gf-filter-option v-for="item in eventOptions" :key="'event' + item.value" :label="item.label" :value="item.value"></gf-filter-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="取值参数列表:" v-if="hasEventParam">
                    <div class="rule-table">
                        <el-table :data="eventParam" :header-cell-style="{ 'text-align': 'center' }" border cell-class-name="rule-cell" header-cell-class-name="rule-header-cell" header-row-class-name="rule-header-row" row-class-name="rule-row" stripe style="width: 100%">
                            <el-table-column label="参数代码:">
                                <template slot-scope="scope">
                                    <el-input :disabled="true" v-model="scope.row.fieldKey"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="参数名称:">
                                <template slot-scope="scope">
                                    <el-input :disabled="true" v-model="scope.row.fieldName"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column label="参数类型:">
                                <template slot-scope="scope">
                                    <gf-dict filterable clearable v-model="scope.row.paramType" dict-type="TASK_DEF_DATATYPE" />
                                </template>
                            </el-table-column>
                            <el-table-column label="参数值:">
                                <template slot-scope="scope">
                                    <el-select v-model="scope.row.paramValueRange" filterable clearable placeholder="请选择" v-if="scope.row.paramType === 'dict'">
                                        <el-option v-for="dict in dictData" :key="dict.dictTypeId" :label="dict.dictTypeName" :value="dict.dictTypeId"></el-option>
                                    </el-select>
                                    <el-input v-model="scope.row.paramValueRange" :disabled="!(scope.row.paramType === 'list')" v-if="scope.row.paramType == 'list'"></el-input>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-form-item>
                <el-form-item label="执行开始时间:" prop="step_startTime" style="width: 90%;">
                    <div class="line none-shrink">
                        <gf-dict clearable v-model="detailForm.startDayType" dict-type="AGNES_ACROS_DAY_TYPE" v-show="startDayChecked === '1'" style="width: 22%;" />
                        <el-input style="width: 13%; margin-right: 10px; margin-left: 10px;" v-model.number="detailForm.startDay" v-show="startDayChecked === '1'"></el-input>
                        <span v-show="startDayChecked === '1'" style="margin-right: 10px;">天</span>
                        <el-time-picker v-model="detailForm.step_startTime" :picker-options="startTimeForDay" placeholder="执行开始时间" value-format="HH:mm" @change="timeChange"></el-time-picker>
                        <gf-strbool-checkbox v-model="startDayChecked" style="margin-left: 10px;" @change="startDayChange">跨日</gf-strbool-checkbox>
                    </div>
                    <gf-strbool-checkbox v-model="startStepRuleChecked">自定义激活规则</gf-strbool-checkbox>
                </el-form-item>
                <el-form-item v-if="startStepRuleChecked == '1'">
                    <rule-table :isDisable="mode == 'view'" ref="activeRuleTable" confType="fn,event" :ruleTableData="detailForm.activeRuleTableData"></rule-table>
                </el-form-item>
                <el-form-item label="执行结束时间:" prop="step_endTime" style="width: 90%;">
                    <div class="line none-shrink">
                        <gf-dict clearable v-model="detailForm.endDayType" dict-type="AGNES_ACROS_DAY_TYPE" v-show="endDayChecked === '1'" style="width: 22%;" />
                        <el-input style="width: 13%; margin-right: 10px; margin-left: 10px;" v-model.number="detailForm.endDay" v-show="endDayChecked === '1'"></el-input>
                        <span v-show="endDayChecked === '1'" style="margin-right: 10px;">天</span>
                        <el-time-picker v-model="detailForm.step_endTime" :picker-options="endTimeForDay" placeholder="执行结束时间" value-format="HH:mm" @change="timeChange"></el-time-picker>
                        <gf-strbool-checkbox v-model="endDayChecked" style="margin-left: 10px;" @change="endDayChange">跨日</gf-strbool-checkbox>
                    </div>
                    <gf-strbool-checkbox v-model="timeoutRuleChecked">自定义超时规则</gf-strbool-checkbox>
                </el-form-item>
                <el-form-item v-if="timeoutRuleChecked == '1'">
                    <rule-table :isDisable="mode == 'view'" ref="timeoutRuleTable" confType="fn,event" :ruleTableData="detailForm.timeoutRuleTableData"></rule-table>
                </el-form-item>
                <el-form-item label="执行逻辑选择:">
                    <el-select v-model="detailForm.stepActKey" placeholder="请选择" filterable clearable style="width: 64%;">
                        <gf-filter-option v-for="item in kpiOptions" :key="item.value" :label="item.label" :value="item.value"></gf-filter-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="执行频率配置:">
                    <el-button type="text" @click="editExecTime('step_execScheduler', detailForm.step_execScheduler, '执行频率配置')">{{ detailForm.step_execScheduler }}点击配置</el-button>
                </el-form-item>

                <el-form-item label="执行人:" prop="stepActOwner">
                    <gf-person-chosen ref="memberRef" :disabled="mode === 'view'" :memberRefList="this.memberRefList" chosenType="user, group, roster" :rosterDate="this.rosterDate" @getMemberList="getMemberList"></gf-person-chosen>
                </el-form-item>
                <el-form-item label="协作人:" prop="stepCollaborateOwner">
                    <gf-person-chosen ref="collaborateMemberRef" :disabled="mode === 'view'" :memberRefList="this.collaborateRefList" chosenType="user, group" @getMemberList="getCollaborateMemberList"></gf-person-chosen>
                </el-form-item>

                <el-form-item label="完成规则:">
                    <el-radio-group v-model="succeedRule">
                        <el-radio v-for="ruleType in ruleTypeOp" :key="ruleType.value" :label="ruleType.value">
                            {{ ruleType.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="succeedRule == '1'">
                    <rule-table ref="successRuleTable" confType="fn" :ruleTableData="detailForm.successRuleTableData"></rule-table>
                </el-form-item>
                <el-form-item label="异常规则:">
                    <el-radio-group v-model="abnormalRule">
                        <el-radio v-for="ruleType in ruleErrorTypeOp" :key="ruleType.value" :label="ruleType.value">
                            {{ ruleType.label }}
                        </el-radio>
                    </el-radio-group>
                </el-form-item>
                <el-form-item v-if="abnormalRule == '1'">
                    <rule-table ref="failRuleTable" confType="fn" :ruleTableData="detailForm.failRuleTableData"></rule-table>
                </el-form-item>
                <el-form-item label="任务控制参数:">
                    <gf-strbool-checkbox v-model="detailForm.needApprove">是否需要复核1</gf-strbool-checkbox>
                    <gf-strbool-checkbox v-model="detailForm.isTodo">是否进入待办</gf-strbool-checkbox>
                    <gf-strbool-checkbox v-model="detailForm.allowManualConfirm">是否允许人工干预通过</gf-strbool-checkbox>
                    <gf-strbool-checkbox v-model="detailForm.notDependTarget">不依赖于目标值</gf-strbool-checkbox>
                </el-form-item>
            </template>
        </module-card>
        <module-card title="任务展示" shadow="never">
            <template slot="content"></template>
        </module-card>

        <module-card title="消息通知" shadow="never">
            <template slot="content">
                <el-form-item label="消息通知参数:">
                    <span class="default-checked">系统内部消息</span>
                    <el-checkbox-group v-model="msgInformParam">
                        <el-checkbox v-for="msgInform in msgInformOp" :key="msgInform.value" :label="msgInform.value">
                            {{ msgInform.label }}
                        </el-checkbox>
                    </el-checkbox-group>
                    <gf-strbool-checkbox v-model="detailForm.isNotSendErrRunning">指标监控超时前不发送异常通知</gf-strbool-checkbox>
                </el-form-item>
                <el-form-item v-if="msgInformParam.length > 0">
                    <el-tabs class="scroll-content-tab" type="card" style="height: 290px;">
                        <el-tab-pane v-for="(msgInformItem, msgInformIndex) in msgInformParam" :key="msgInformIndex" :name="msgInformIndex + ''">
                            <span class="tab-label" slot="label">
                                <span>{{ msgInformOp[msgInformItem].label }}</span>
                            </span>
                            <el-form size="small" label-width="100px" v-show="msgInformItem == '0'">
                                <el-form-item label="提前通知配置">
                                    <el-button type="text" @click="openRemindDlg(detailForm.warningRemind, 'warningRemind')">
                                        点击配置通知方式
                                    </el-button>
                                </el-form-item>
                                <el-form-item label="预警时间">
                                    提前
                                    <gf-input v-model="detailForm.warningMintues" style="width: 30%;"></gf-input>
                                    <el-select v-model="detailForm.warningTimeType" placeholder="请选择">
                                        <el-option v-for="item in detailForm.timeTypeData" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-form>
                            <el-form size="small" label-width="100px" v-show="msgInformItem == '1'">
                                <el-form-item label="完成通知配置">
                                    <el-button type="text" @click="openRemindDlg(detailForm.finishRemind, 'finishRemind')">点击配置通知方式</el-button>
                                </el-form-item>
                            </el-form>
                            <el-form size="small" label-width="100px" v-show="msgInformItem == '2'">
                                <el-form-item label="超时通知配置">
                                    <el-button type="text" @click="openRemindDlg(detailForm.timeoutRemind, 'timeoutRemind')">点击配置通知方式</el-button>
                                </el-form-item>
                                <el-form-item label="服务水平承诺">
                                    <el-select v-model="detailForm.serviceResponseId" placeholder="请选择" @change="serviceResChange">
                                        <el-option v-for="item in serviceRes" :key="item.value" :label="item.label" :value="item.value"></el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item>
                                    按照每隔
                                    <gf-input :value="repeatMinutes" style="width: 20%;" :disabled="true"></gf-input>
                                    分钟，执行
                                    <gf-input :value="maxRepeatCount" style="width: 20%;" :disabled="true"></gf-input>
                                    次后退出
                                </el-form-item>
                                <el-form-item label="异常记录">
                                    <gf-strbool-checkbox v-model="detailForm.isRecordTimeoutError">记入异常</gf-strbool-checkbox>
                                </el-form-item>
                                <el-form-item v-if="detailForm.isRecordTimeoutError === '1'">
                                    <el-form-item label="异常类型">
                                        <el-select v-model="detailForm.timeoutErrorType" placeholder="请选择">
                                            <gf-filter-option v-for="item in errorTypeData" :key="'error' + item.dictId" :label="item.dictName" :value="item.dictId"></gf-filter-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="异常内容">
                                        <gf-input v-model="detailForm.timeoutErrorContent" type="textarea" :rows="1" :max-byte-len="100"></gf-input>
                                    </el-form-item>
                                </el-form-item>
                            </el-form>
                            <el-form size="small" label-width="100px" v-show="msgInformItem == '3'">
                                <el-form-item label="异常通知配置">
                                    <el-button type="text" @click="openRemindDlg(detailForm.exceptionRemind, 'exceptionRemind')">点击配置通知方式</el-button>
                                </el-form-item>
                                <el-form-item label="异常记录">
                                    <gf-strbool-checkbox v-model="detailForm.isRecordError">记录异常</gf-strbool-checkbox>
                                </el-form-item>
                                <el-form-item v-if="detailForm.isRecordError === '1'">
                                    <el-form-item label="异常类型">
                                        <el-select v-model="detailForm.errorType" placeholder="请选择">
                                            <gf-filter-option v-for="item in errorTypeData" :key="'error1' + item.dictId" :label="item.dictName" :value="item.dictId"></gf-filter-option>
                                        </el-select>
                                    </el-form-item>
                                    <el-form-item label="异常内容">
                                        <gf-input v-model="detailForm.errorContent" type="textarea" :rows="1" :max-byte-len="100"></gf-input>
                                    </el-form-item>
                                </el-form-item>
                            </el-form>
                        </el-tab-pane>
                    </el-tabs>
                </el-form-item>
            </template>
        </module-card>
    </el-form>
</template>

<script>
import loadsh from 'lodash';
import staticData from '../../../util/dataFormat';
import initData from '../../../util/initData';

export default {
    name: 'task-define',
    props: {
        mode: {
            type: String,
            default: 'add'
        },
        row: Object,
        actionOk: Function,
        slotBtn: {
            type: Object
        }
    },
    data() {
        const checkActOwner = (rule, value, callback) => {
            if (value == '[]') {
                callback(new Error('执行人必填'));
            } else {
                callback();
            }
        };
        return {
            isCheckCode: false,
            rosterDate: '',
            versionId: '',
            eventKey: '',
            memberRefList: [],
            collaborateRefList: [], // 协作人列表
            serviceRes: [],
            staticData: staticData(),
            detailForm: initData(),
            dayChecked: '0', // 跨日
            endTimeForDay: null,
            startTimeForDay: null,
            succeedRule: '0',
            abnormalRule: '0',
            startDayChecked: '0', // 执行开始时间跨日
            endDayChecked: '0', // 执行结束时间跨日
            timeoutRuleChecked: '0', // 超时规则确认框
            startStepRuleChecked: '0', // 激活规则确认框
            repeatMinutes: '',
            maxRepeatCount: '',
            curExecScheduler: '', // 当前频率对象字段
            msgInformParam: [], // 消息通知参数类型数组
            startAllTime: '0', // 是否永久有效
            bizTagOption: [], // 业务类型下拉
            eventOptions: [], //
            kpiOptions: [], //
            errorTypeData: [],
            dataAcqOptions: [],
            hasEventParam: false,
            eventParam: [],
            msgInfoStr: ['warningRemind', 'finishRemind', 'timeoutRemind', 'exceptionRemind'],
            // 规则选择类型选项
            ruleTypeOp: [
                { label: '默认完成规则', value: '0' },
                { label: '自定义完成规则', value: '1' }
            ],
            ruleErrorTypeOp: [
                { label: '默认异常规则', value: '0' },
                { label: '自定义异常规则', value: '1' }
            ],
            // 消息配置类型类型选项
            msgInformOp: [
                { label: '提前通知', value: '0' },
                { label: '完成通知', value: '1' },
                { label: '超时通知', value: '2' },
                { label: '异常通知', value: '3' }
            ],
            detailFormRules: {
                taskName: [{ required: true, message: '任务名称必填', trigger: 'blur' }],
                stepLevel: [{ required: true, message: '任务等级必填', trigger: 'blur' }],
                caseKey: [{ validator: this.hasRepetCode, required: true, trigger: 'change' }],
                stepRemark: [{ required: true, message: '任务说明必填', trigger: 'blur' }],
                task_startTime: [{ required: true, message: '运行周期开始时间必填', trigger: 'blur' }],
                task_endTime: [{ required: true, message: '运行周期结束时间必填', trigger: 'blur' }],
                // dayendDefId: [
                //     {required: true, message: '基准日期必填', trigger: 'change'},
                // ],
                task_execMode: [{ required: true, message: '启动方式必填', trigger: 'blur' }],
                task_execScheduler: [{ required: true, message: '任务创建频率必填', trigger: 'blur' }],
                step_startTime: [{ required: true, message: '执行开始时间必填', trigger: 'change' }],
                step_endTime: [{ required: true, message: '执行结束时间必填', trigger: 'change' }],
                stepActOwner: [{ required: true, validator: checkActOwner, trigger: 'blur' }]
            },

            pickerOptionsStart: {
                disabledDate: time => {
                    let endDateVal = this.detailForm.task_endTime;
                    if (endDateVal) {
                        return time.getTime() > new Date(endDateVal).getTime();
                    }
                }
            },
            pickerOptionsEnd: {
                disabledDate: time => {
                    let beginDateVal = this.detailForm.task_startTime;
                    if (beginDateVal) {
                        return time.getTime() < new Date(beginDateVal).getTime() - 1 * 24 * 60 * 60 * 1000;
                    }
                }
            }
        };
    },
    beforeMount() {
        this.errorTypeData = this.$app.dict.getDictItems('AGNES_DOP_ERR_TYPE');
        this.reDataTransfer();
        this.getOptions();
        this.getServiceResponse();
        this.startTimeForDay = { selectableRange: `00:00:00-${this.detailForm.step_endTime ? this.detailForm.step_endTime + ':00' : '23:59:59'}` };
        this.endTimeForDay = { selectableRange: `${this.detailForm.step_startTime ? this.detailForm.step_startTime + ':00' : '00:00:00'}-23:59:59` };
        this.initFilesOption();
    },
    methods: {
        hasRepetCode(rule, value, callback) {
            if (!value) {
                callback(new Error('任务编号必填'));
            } else if (value.length !== 8) {
                callback(new Error('任务编号需为8位数字'));
            } else {
                callback();
            }
        },
        async initFilesOption() {
            const c = this.$api.dataAcqApi.getDataAcqList();
            const resp = await this.$app.blockingApp(c);
            if (resp && resp.data) {
                this.dataAcqOptions = resp.data;
            }
        },
        async serviceResChange(param) {
            this.serviceRes.forEach(item => {
                if (item.value === param) {
                    this.repeatMinutes = item.repeatMinutes;
                    this.maxRepeatCount = item.maxRepeatCount;
                }
            });
        },
        async getServiceResponse() {
            const serviceRes = this.$api.kpiTaskApi.getServiceResponse();
            const serviceResData = await this.$app.blockingApp(serviceRes);
            if (serviceResData.data) {
                const serviceResList = serviceResData.data;
                serviceResList.forEach(item => {
                    this.serviceRes.push({
                        label: item.serviceResponseName,
                        value: item.serviceResponseId,
                        repeatMinutes: item.repeatMinutes,
                        maxRepeatCount: item.maxRepeatCount
                    });
                });
            }
        },
        async getOptions() {
            if (this.detailForm.eventId) {
                this.eventKey = this.detailForm.eventId.split('_')[0];
            }
            this.bizTagOption = this.$app.dict.getDictItems('AGNES_BIZ_TAG');
            const e = this.$api.eventlDefConfigApi.getEventDefList();
            const eventR = await this.$app.blockingApp(e);
            if (eventR.data) {
                const eventList = eventR.data;
                eventList.forEach(item => {
                    this.eventOptions.push({ label: item.eventName, value: item.eventKey, eventId: item.eventId });
                });
            }

            const kpi = this.$api.kpiTaskApi.getAllKpiList();
            const kpiData = await this.$app.blockingApp(kpi);
            if (kpiData.data) {
                const kpiList = kpiData.data;
                kpiList.forEach(item => {
                    let kpiName = '(' + item.kpiCode + ')' + item.kpiName;
                    this.kpiOptions.push({ label: kpiName, value: item.kpiCode });
                });
            }
        },
        getMemberList(val) {
            this.memberRefList = val;
            this.detailForm.stepActOwner = JSON.stringify(val);
        },
        getCollaborateMemberList(val) {
            this.collaborateRefList = val;
            this.detailForm.stepCollaborateOwner = JSON.stringify(val);
        },
        editExecTime(curObj, execScheduler, title) {
            this.curExecScheduler = curObj;
            let flag = false;
            if (curObj === 'step_execScheduler') {
                flag = true;
            }
            this.showDlg(flag, execScheduler, title, this.setExecScheduler.bind(this));
        },
        showDlg(flag, data, title, action) {
            let defShowType = 'second,minute,hour,day,month,extSetting';
            if (this.mode === 'view') {
                return;
            }
            if (flag) {
                defShowType = 'second,minute,hour,extSetting';
            }
            this.$nav.showDialog('gf-cron-modal', {
                args: {
                    cornObj: data,
                    action,
                    showType: defShowType
                },
                width: '530px',
                title: this.$dialog.formatTitle(title, 'edit')
            });
        },
        setExecScheduler(cron) {
            this.detailForm[this.curExecScheduler] = cron;
        },
        // 取消onCancel事件，触发抽屉关闭事件this.$emit("onClose");
        async onCancel() {
            if (this.row.isCheck) {
                let resData = this.dataTransfer();
                resData.isPass = '0';
                const p = this.$api.kpiTaskApi.checkTask(resData);
                await this.$app.blockingApp(p);
                if (this.actionOk) {
                    await this.actionOk();
                }
            }
            this.$emit('onClose');
        },

        // 保存onSave事件，保存操作完成后触发抽屉关闭事件this.$emit("onClose");
        async onSave() {
            const ok = await this.$refs['taskDefForm'].validate();
            if (!ok) {
                return;
            }
            try {
                let msg = this.requireVerify()
                if(msg) {
                    this.$message.warning(msg);
                    return;
                }
                let resData = this.dataTransfer();
                resData.taskDeploys = [];
                if (this.row.isCheck) {
                    await this.checkTask(resData)
                    return
                } 
                //校验该明细是否被修改
                if (this.row.caseUpdateTs) {
                    const checkP = this.$api.caseConfigApi.checkCaseIsUpdate({ caseDefId: this.row.caseDefId, caseUpdateTs: this.row.caseUpdateTs });
                    let checkResp = await this.$app.blockingApp(checkP);
                    if (checkResp?.code == '000001') {
                        const ok1 = await this.$msg.ask('该明细数据已被其他地方修改，是否继续保存?');
                        if (!ok1) {
                            return;
                        }
                    }
                }
                if (this.detailForm.task_execMode == '3') {
                    let row = this.eventOptions.find(item => item.value == this.eventKey);
                    if(row) {
                        this.detailForm.eventId = row.eventId;
                    }
                }
                const p = this.$api.kpiTaskApi.saveTask(resData);
                const resp = await this.$app.blockingApp(p);
                if (resp?.code == 'rwbhycz') {
                    this.$msg.error(resp.message);
                    return
                } 
                this.$msg.success('保存成功');
                if (this.actionOk) {
                    await this.actionOk();
                }
                this.$emit('onClose');
            } catch (reason) {
                this.$msg.error(reason);
            }
        },
        /**
         *  校验必填
         */
        requireVerify() {
            if (this.detailForm.stepActOwner == '[]') {
                return '请选择通知人员！';
            }
            if (this.detailForm.task_execMode == '3' && this.detailForm.eventId == '') {
                return '请选择触发事件！';
            }
            if (this.detailForm.step_execScheduler == '') {
                return '请选择执行频率！';
            }
            if (this.startDayChecked == '1' && !this.detailForm.startDay) {
                return '请输入执行开始时间的跨日天数！';
            }
            if (this.endDayChecked == '1' && !this.detailForm.endDay) {
                return '请输入执行结束时间的跨日天数！';
            }
            return ''
        },
        /**
         * 审核任务
         * @param resData 
         */
        async checkTask(resData) {
            resData.isPass = '1';
            const p = this.$api.kpiTaskApi.checkTask(resData);
            await this.$app.blockingApp(p);
            this.$msg.success('审核成功');
            if (this.actionOk) {
                await this.actionOk();
            }
            this.$emit('onClose');
        },
        async showRemind(remindProp, remindSort) {
            this.detailForm[remindSort] = remindProp;
        },
        // 告警方式配置，打开弹框
        openRemindDlg(remindProp, remindSort) {
            this.showRemindDlg(remindProp, remindSort, this.showRemind.bind(this));
        },
        showRemindDlg(remindProp, remindSort, actionOk) {
            this.$nav.showDialog('remind-def', {
                args: { remindProp, remindSort, actionOk },
                width: '530px',
                title: this.$dialog.formatTitle('通知方式配置', 'edit')
            });
        },
        // 数据结构转换
        dataTransfer() {
            if (this.detailForm.warningTimeType === '2') {
                this.detailForm.warningMintues = this.detailForm.warningMintues * 60;
            } else if (this.detailForm.warningTimeType === '3') {
                this.detailForm.warningMintues = this.detailForm.warningMintues * 60 * 24;
            }
            this.setTableData()
            this.setRuleBody()
            //消息通知参数判断是否勾选
            this.setRemind()
            let kpiTaskDef = this.$utils.deepClone(this.staticData.kpiTaskDef);
            this.detailForm.bizTag = this.detailForm.bizTagArr.join(',');
            this.detailForm.stepCode = this.detailForm.caseKey;
            this.detailForm.caseDefKey = this.detailForm.caseKey;
            this.keyToValue(kpiTaskDef, 'task_');
            let caseDef = this.$utils.deepClone(this.staticData.caseDef);
            let defId = this.$agnesUtils.randomString(32);
            let defName = this.detailForm.taskName;
            this.detailForm.stepName = defName;
            caseDef.stages[0].defId = defId;
            caseDef.stages[0].children[0].stepId = defId;
            caseDef.stages[0].defName = defName;
            caseDef.stages[0].children[0].stepName = defName;
            let stepFormInfo = this.$utils.deepClone(this.staticData.caseDef.stages[0].children[0].stepFormInfo);
            Object.keys(stepFormInfo).forEach(key => {
                if (key === 'caseStepDef') {
                    this.keyToValue(stepFormInfo.caseStepDef, 'step_');
                } else {
                    stepFormInfo[key] = this.detailForm[key] || stepFormInfo[key];
                }
            });
            caseDef.stages[0].children[0].stepFormInfo = stepFormInfo;
            if (this.mode === 'add' || this.row.reTaskDef.caseKey != kpiTaskDef.caseKey) {
                this.isCheckCode = true;
            }
            return { reTaskDef: kpiTaskDef, caseDefId: this.row.caseDefId, caseDefBody: JSON.stringify(caseDef), versionId: this.versionId, isCheckCode: this.isCheckCode };
        },
        /**
         * 设置tableData
         */
        setTableData() {
            if (this.succeedRule === '0') {
                this.detailForm.successRuleTableData = {};
            }
            if (this.abnormalRule === '0') {
                this.detailForm.failRuleTableData = {};
            }
            if (this.startStepRuleChecked === '0') {
                this.detailForm.activeRuleTableData = {};
            }
            if (this.timeoutRuleChecked === '0') {
                this.detailForm.timeoutRuleTableData = {};
            }
        },
        /**
         * 设置ruleBody
         */
        setRuleBody() {
            if (this.detailForm.activeRuleTableData && this.detailForm.activeRuleTableData.ruleList) {
                const activeRuleJson = this.$refs.activeRuleTable.jsonFormatter();
                this.detailForm.activeRuleTableData.ruleBody = activeRuleJson;
            }
            if (this.detailForm.successRuleTableData && this.detailForm.successRuleTableData.ruleList) {
                const successRuleJson = this.$refs.successRuleTable.jsonFormatter();
                this.detailForm.successRuleTableData.ruleBody = successRuleJson;
            }
            if (this.detailForm.timeoutRuleTableData && this.detailForm.timeoutRuleTableData.ruleList) {
                const timeoutRuleJson = this.$refs.timeoutRuleTable.jsonFormatter();
                this.detailForm.timeoutRuleTableData.ruleBody = timeoutRuleJson;
            }
            if (this.detailForm.failRuleTableData && this.detailForm.failRuleTableData.ruleList) {
                const failRuleJson = this.$refs.failRuleTable.jsonFormatter();
                this.detailForm.failRuleTableData.ruleBody = failRuleJson;
            }
        },
        /**
         * 设置remind
         */
        setRemind() {
            if (this.msgInformParam.indexOf('0') === -1) {
                this.detailForm.warningRemind = [];
            }
            if (this.msgInformParam.indexOf('1') === -1) {
                this.detailForm.finishRemind = [];
            }
            if (this.msgInformParam.indexOf('2') === -1) {
                this.detailForm.timeoutRemind = [];
            }
            if (this.msgInformParam.indexOf('3') === -1) {
                this.detailForm.exceptionRemind = [];
            }
        },
        async reDataTransfer() {
            this.rosterDate = window.bizDate;
            if (this.mode === 'add') {
                return
            }
            let kpiTaskDef = this.$utils.deepClone(this.row.reTaskDef);
            this.reKeyToValue(kpiTaskDef, 'task_');
            this.versionId = this.row.versionId;
            if (this.row.caseDefId) {
                const p = this.$api.caseConfigApi.selectTaskCaseBody(this.row.caseDefId);
                let rep = await this.$app.blockingApp(p);
                if (rep.data.caseDefBody) {
                    this.row.caseDefBody = rep.data.caseDefBody;
                    this.row.caseUpdateTs = rep.data.updateTs;
                }
            }
            let caseDefBody = JSON.parse(this.row.caseDefBody);
            let stepFormInfo = this.$utils.deepClone(caseDefBody.stages[0].children[0].stepFormInfo);
            this.setStepFormInfo(stepFormInfo)
            this.setParams()
            const startDay = this.detailForm.startDay;
            const endDay = this.detailForm.endDay;
            if (startDay) {
                this.startDayChecked = '1';
            }
            if (endDay) {
                this.endDayChecked = '1';
            }
            this.dayChecked = startDay || endDay ? '1' : '0';
            //消息通知参数回显
            this.msgInfoStr.forEach((strItem, index) => {
                if (this.detailForm[strItem] && this.detailForm[strItem].length > 0) {
                    this.msgInformParam.push(index + '');
                }
            });
        },
        /**
         * 设置stepFormInfo
         * @param stepFormInfo 
         */
        setStepFormInfo(stepFormInfo) {
            Object.keys(stepFormInfo).forEach(key => {
                if (key === 'caseStepDef') {
                    this.reKeyToValue(stepFormInfo.caseStepDef, 'step_');
                } else {
                    this.detailForm[key] = stepFormInfo[key] || this.detailForm[key];
                }
            });
        },
        /**
         * 设置部分参数
         */
        setParams() {
            if (this.detailForm.task_endTime === '9999-12-31') {
                this.startAllTime = true;
            }
            if (this.detailForm.bizTag) {
                this.detailForm.bizTagArr = this.detailForm.bizTag.split(',');
            }
            if (this.detailForm.stepActOwner) {
                this.memberRefList = JSON.parse(this.detailForm.stepActOwner);
            }
            if (this.detailForm.stepCollaborateOwner) {
                this.collaborateRefList = JSON.parse(this.detailForm.stepCollaborateOwner);
            }
            if (!loadsh.isEmpty(this.detailForm.successRuleTableData)) {
                this.succeedRule = '1';
            }
            if (!loadsh.isEmpty(this.detailForm.failRuleTableData)) {
                this.abnormalRule = '1';
            }
            if (this.detailForm.activeRuleTableData) {
                const activeRuleTableData = this.detailForm.activeRuleTableData.ruleList || [];
                this.startStepRuleChecked = activeRuleTableData.length <= 0 ? '0' : '1';
            }
            if (this.detailForm.timeoutRuleTableData) {
                const timeoutRuleTableData = this.detailForm.timeoutRuleTableData.ruleList || [];
                this.timeoutRuleChecked = timeoutRuleTableData.length <= 0 ? '0' : '1';
            }
        },
        timeChange() {
            if (this.endDayChecked == '1' || this.startDayChecked == '1') {
                this.startTimeForDay = { selectableRange: '00:00:00-23:59:59' };
                this.endTimeForDay = { selectableRange: '00:00:00-23:59:59' };
            } else {
                this.startTimeForDay = { selectableRange: `00:00:00-${this.detailForm.step_endTime ? this.detailForm.step_endTime + ':00' : '23:59:59'}` };
                this.endTimeForDay = { selectableRange: `${this.detailForm.step_startTime ? this.detailForm.step_startTime + ':00' : '00:00:00'}-23:59:59` };
            }
        },

        keyToValue(obj, type) {
            Object.keys(obj).forEach(key => {
                const extraKeyArr = ['startTime', 'endTime', 'execMode', 'execScheduler'];
                if (extraKeyArr.indexOf(key) === -1) {
                    obj[key] = this.detailForm[key] || obj[key];
                } else {
                    obj[key] = this.detailForm[type + key] || obj[key];
                }
            });
        },

        reKeyToValue(obj, type) {
            Object.keys(obj).forEach(key => {
                const extraKeyArr = ['startTime', 'endTime', 'execMode', 'execScheduler'];
                if (extraKeyArr.indexOf(key) === -1) {
                    this.detailForm[key] = obj[key] || this.detailForm[key];
                } else {
                    this.detailForm[type + key] = obj[key] || this.detailForm[key];
                }
            });
        },
        endTimeChange() {
            if (this.dayChecked == '1') {
                this.startTimeForDay = { selectableRange: '00:00:00-23:59:59' };
            } else {
                this.startTimeForDay = { selectableRange: `00:00:00-${this.detailForm.step_endTime ? this.detailForm.step_endTime + ':00' : '23:59:59'}` };
            }
        },
        startTimeChange() {
            if (this.dayChecked == '1') {
                this.endTimeForDay = { selectableRange: '00:00:00-23:59:59' };
            } else {
                this.endTimeForDay = { selectableRange: `${this.detailForm.step_startTime ? this.detailForm.step_startTime + ':00' : '00:00:00'}-23:59:59` };
            }
        },
        async getEventParam() {
            this.eventOptions.forEach(item1 => {
                if (item1.value == this.eventKey) {
                    this.detailForm.eventId = item1.eventId;
                }
            });
            if (this.detailForm.eventId) {
                const e = this.$api.modelConfigApi.getFieldByEventId(this.detailForm.eventId);
                const eventR = await this.$app.blockingApp(e);
                if (eventR.data && eventR.data.length > 0) {
                    this.paramRefList = [];
                    this.detailForm.bizParam = '';
                    this.hasEventParam = true;
                    this.eventParam = eventR.data;
                }
            }
        },
        startDayChange() {
            if (this.startDayChecked !== '1') {
                this.detailForm.startDay = '';
            }
        },
        endDayChange() {
            if (this.endDayChecked !== '1') {
                this.detailForm.endDay = '';
            }
        }
    },

    watch: {
        startAllTime(val) {
            if (val) {
                this.detailForm.task_endTime = '9999-12-31';
            } else {
                this.detailForm.task_endTime = '';
            }
        },
        'detailForm.task_execMode'(val) {
            this.hasEventParam = false;
            this.eventParam = [];
            if (val === '2') {
                this.detailForm.eventId = '';
            } else if (val === '3') {
                this.detailForm.task_execScheduler = '';
            } else {
                this.detailForm.eventId = '';
                this.detailForm.task_execScheduler = '';
            }
        },
        dayChecked(val) {
            if (val === '1') {
                this.endTimeForDay = { selectableRange: '00:00:00-23:59:59' };
                this.startTimeForDay = { selectableRange: '00:00:00-23:59:59' };
                if (!this.detailForm.endDay) {
                    this.detailForm.endDay = '1';
                }
                if (!this.detailForm.startDay) {
                    this.detailForm.startDay = '0';
                }
            } else {
                this.endTimeForDay = { selectableRange: `${this.detailForm.step_startTime ? this.detailForm.step_startTime + ':00' : '00:00:00'}-23:59:59` };
                this.startTimeForDay = { selectableRange: `00:00:00-${this.detailForm.step_endTime ? this.detailForm.step_endTime + ':00' : '23:59:59'}` };
                this.detailForm.endDay = '';
                this.detailForm.startDay = '';
                this.detailForm.step_endTime = '';
            }
        },
        eventKey(val) {
            if (val) {
                this.getEventParam();
            }
        }
    }
};
</script>

<style scoped>
.el-icon-refresh-left {
    color: #0f5eff;
    margin-left: 10px;
    vertical-align: text-top;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
}
</style>

<style>
.task-def-form .el-tabs.scroll-content-tab .el-tab-pane .el-form {
    height: 100%;
    overflow: auto;
}
</style>

<style scoped>
.task-def-form {
    max-width: 850px;
    padding: 0px !important;
}
.task-def-form ::v-deep .el-tabs.scroll-content-tab .el-tab-pane .el-form{
    height: 100%;
    overflow: auto;
}
.el-form .el-card >>> .el-card__body{
  padding: 20px 32px 20px 10px;
  border-bottom: 1px dashed #E7E9EB;
}
.el-form-item {
    margin-bottom: 16px;
}
.basicInfo {
    width: 76%;
    min-width: 300px;
}
.basicInfo .el-form-item >>> .el-form-item__label {
    width: 81px !important;
}
.basicInfo .el-form-item >>> .el-form-item__content {
    margin-left: 81px !important;
}
.basicInfo .el-form-item >>> .el-form-item__content .el-input {
    width: 100%;
}
.el-form .el-card {
    -webkit-box-shadow: 0 0 0 0;
    box-shadow: 0 0 0 0;
}
</style>
