<template>
  <div class="optional-cell" >
      <el-popover ref="popover"
                  placement="top-start"
                  :title="title"
                  width="400"
                  trigger="manual"
                  v-model="popoverVisible"
      >
          <el-form v-if="actionType=='actionConfirm'  || actionType=='forcePass'  || actionType=='finishDetailShow' || actionType=='reloadParam' || actionType=='editEndTime' " ref="form" :model="form"
                   label-width="80px" :rules="rules">


              <el-form-item v-for="item in form.paramList"
                            :label="item.paramName"
                            :key="item.paramKey"
                            :prop="item.paramKey">
                  <el-input type="textarea" v-model="item.paramString" :disabled="isShowFinishDetail" v-if="item.paramType=='string'"></el-input>
                  <el-input v-model="item.paramNumber" :disabled="isShowFinishDetail" oninput="value=value.replace(/[^\d]/g,'')"
                            maxLength='9' placeholder="请输入数字" v-if="item.paramType=='number'"/>
                  <el-input v-model.number="item.paramAmount" :disabled="isShowFinishDetail" placeholder="请输入金额" v-if="item.paramType=='amount'"/>
                  <el-date-picker
                          v-model="item.paramDate"
                          type="date"
                          placeholder="选择日期" :disabled="isShowFinishDetail" v-if="item.paramType=='date'">
                  </el-date-picker>
                  <gf-dict v-model="item.paramString" v-if="item.paramType === 'boolean'"
                           dict-type="GF_BOOL_TYPE"></gf-dict>

                  <el-select v-model="item.paramString" filterable clearable placeholder="请选择"
                             v-if="item.paramType === 'list'">
                      <el-option
                              v-for="item in getLists(item.paramValueRange)"
                              :key="item.value"
                              :label="item.name"
                              :value="item.value">
                      </el-option>
                  </el-select>
                  <gf-dict v-model="item.paramString" v-if="item.paramType === 'dict'"
                           :dict-type="item.paramValueRange"></gf-dict>
              </el-form-item>
              <el-form-item label="备注" v-show="isShowRemark">
                  <el-input type="textarea" :rows="1" placeholder="请输入备注内容"
                            v-model="form.remark" :maxlength="50">
                  </el-input>
              </el-form-item>
              <el-form-item label="结束时间" v-show="isShowEndTime">
                  <el-date-picker
                          v-model="form.planEndTime"
                          type="datetime"
                          value-format="yyyy-MM-dd HH:mm:ss"
                          placeholder="选择日期时间">
                  </el-date-picker>
              </el-form-item>
          </el-form>
          <el-table header-row-class-name="rule-header-row"
                    header-cell-class-name="rule-header-cell"
                    row-class-name="rule-row"
                    cell-class-name="rule-cell"
                    v-if="actionType=='autoTaskActionConfirm'"
                    :data="autoData"
                    border stripe
                    style="width: 100%"
                    :header-cell-style="{'text-align':'center'}">
              <el-table-column prop="stepCode" label="返回参数名">
                  <template slot-scope="scope">
                      <span>{{scope.row.fieldKey}}</span>
                  </template>
              </el-table-column>
              <el-table-column prop="stepCode" label="返回参数值">
                  <template slot-scope="scope">
                      <span>{{scope.row.fieldValue}}</span>
                  </template>
              </el-table-column>
          </el-table>
          <div style="text-align: right; margin-top: 10px">
              <el-button class="op-btn" size="mini" type="text" @click="closeRemake">取消</el-button>
              <el-button class="op-btn primary"
                         v-if="actionType=='actionConfirm'  || actionType=='forcePass'   || actionType=='reloadParam' || actionType=='editEndTime' "
                         size="mini" @click="confirmRemark">保存
              </el-button>
          </div>
      </el-popover>
        <el-button size="mini" type="text"
                   v-popover:popover
                   @click="popoverClick('isFormAction')"
                   title="表单录入"
                   v-if="isFormAction"
                   :disabled="isDisabled || !isCanExec || isUnDoing"
        >
          <span class="svgSpan" v-html="svgImg.actionConfirm"></span>
        </el-button>
        <el-button size="mini" type="text"
                   v-popover:popover v-if="actionShow"
                   @click="popoverClick('actionConfirm')"
                   title="手工确认"
                   :disabled="isDisabled || !isCanExec || isUnDoing"
        >
            <span class="svgSpan" v-html="svgImg.actionConfirm"></span>
        </el-button>
        <el-button size="mini" type="text"
                   v-popover:popover
                   v-if="forcePass"
                   @click="popoverClick('forcePass')"
                   title="干预通过"
                   :disabled="isDisabled || !isCanExec || isUnDoing"
        >
          <span class="svgSpan" v-html="svgImg.forcePass1"></span>
        </el-button>
        <el-button size="mini" type="text"
                   v-popover:popover
                   v-if="fileForcePass"
                   class="show"
                   @click="popoverClick('fileForcePass')"
                   title="干预通过"
                   :disabled="!isCanExec || isUnDoing"
        >
          <span class="svgSpan" v-html="svgImg.forcePass1"></span>
        </el-button>
        <el-button size="mini" type="text"
                   class="detail-btn"
                   v-if="reExecuteShow"
                   @click="reExecute('reExecute')"
                   title="重新执行"
                   :disabled="!isCanExec || isUnDoing"
        >
          <span class="svgSpan" v-html="svgImg.reExecute"></span>
        </el-button>

    <el-button  type="text"
                :disabled="isFinished || !isCanExec || isUnDoing"
                @click="taskChangeMethod('taskChange')"
                title="任务转交"
    >
      <span class="svgSpan" v-html="svgImg.taskChange"></span>
    </el-button>
    <el-button size="mini" type="text"
                   v-popover:popover
                   v-if="queryExecRecords"
                   class="show"
                   @click="popoverClick('queryExecRecords')"
                   title="查询执行记录"
                   :disabled="!isCanExec || isUnDoing"
        >
          <span class="svgSpan" v-html="svgImg.queryExecRec"></span>
      </el-button>
  </div>
</template>

<script>
import Permission from "../../../../../utils/hasPermission";
import flowChartDlg from "../../../components/biz/flow-chart/flow-chart-dlg.vue";
import TaskChangeDlg from "../task-change-info/task-change-dlg.vue";
  export default {
      data() {
          return {
              prdtInfo: {},
              form: {
                  paramList: [],
                  remark: '',
                  planEndTime: '',
              },
              rules:{},
              autoData:[],
              isShowFinishDetail:false,
              isReloadParam:false,
              remark: '',
              title: '手工确认',
              popoverVisible: false,
              isShowEndTime: false,
              isShowRemark:true,
              actionType: '',
              svgImg: this.$lcImg,
          }
      },
      props:{
        currentNode:{
          type: String,
          default: ''
        },
        bizDate:{
          type: String,
          default: ''
        },

        actionOk: Function
      },
      beforeMount() {
          if (this.currentNode) {
              this.remark = this.currentNode.remark;
              this.form.planEndTime = this.currentNode.planEndTime;
              if (!this.currentNode.stepActType) {
                  this.currentNode.stepActType = ''
              }
              if (!this.currentNode.stepStatus) {
                  this.currentNode.stepStatus = ''
              }
          }
      },
      computed: {
        //任务完成返回false
        isdown() {
          return this.currentNode.stepStatus !='06' && this.currentNode.stepStatus !='07';
        },
        //任务完成返回TRUE
            isDisabled() {
              return !this.currentNode.stepStatus || this.currentNode.stepStatus =='01'  ||  this.currentNode.stepStatus == '06' || this.currentNode.stepStatus == '07';
            },
        //是否有操作权限
            isCanExec() {
              return  true;
            },
            //任务未开始返回TRUE
            isUnDoing() {
              return !this.currentNode.stepStatus || this.currentNode.stepStatus =='01';
            },
        //表单录入
          isFormAction() {
                return (this.currentNode.allowFormAction === '1' || this.currentNode.dealType?.match(/01|12|20/) )  && Permission.hasPermission('agnes.elec.task.formAction') ;
            },
        //手工确认
            actionShow() {
                return (this.currentNode.allowActionConfirm === '1'  || this.currentNode.stepActType.match(/6/) || this.currentNode.dealType?.match(/21/))  && Permission.hasPermission('agnes.elec.task.actionShow');
            },
        //文件干预通过
        fileForcePass() {
                return (this.currentNode.stepActType.match(/9/) || this.currentNode.dealType?.match(/07/))  &&  Permission.hasPermission('agnes.elec.task.confirm')
            },
          // 查询执行记录
          queryExecRecords(){
              return (this.currentNode.dealType !== undefined && this.currentNode.dealType !== null && this.currentNode.dealType !== '')
          },
            forcePass() {
              //allowManualConfirm 为场景配置中 1:允许干预通过 0：不允许干预通过
                return !(this.currentNode.stepActType.match(/9|6|01|05|07/) || this.currentNode.dealType?.match(/07/)) && (this.currentNode.allowManualConfirm === '1' || this.currentNode.stepActType.match(/1|3|7|10|02|04|06/) || this.currentNode.stepActType==='01' || this.currentNode.dealType?.match(/23/))
            },
        //重新执行
            reExecuteShow() {
              console.log(this.currentNode)
                return (this.currentNode.isAllowExecAgain === '1'  || this.currentNode.stepActType.match(/9|02|06/) || this.currentNode.stepActType==='01' || this.currentNode.dealType?.match(/07/))  && Permission.hasPermission('agnes.elec.task.exec');
            },
        //查看回填参数
          finishDetailShow() {
                return this.currentNode.stepActType === '6' && (this.currentNode.stepStatus === '06' || this.currentNode.stepStatus === '07') && this.currentNode.hasTaskParam != '0' && Permission.hasPermission('agnes.elec.task.actionShow');
            },
          manualStartStepShow(){
            return this.currentNode.taskStatus ==='01' && Permission.hasPermission('agnes.elec.step.manual.start') && new Date>this.currentNode.planStartTime;
          },
          indexEditShow() {
            return Permission.hasPermission('agnes.elec.task.edit') && this.currentNode.allowEditEndTime === '1';
          },
          isFinished(){
            return this.currentNode.stepStatus?.match(/06|07/)
          },
          isEditDisabled() {
              let isEditDisabledStatus = !(this.currentNode.stepStatus === '02' || this.currentNode.stepStatus ==='03');
              if (this.currentNode.planEndTime) {
                let bizDate = window.bizDate+' '+new Date().getHours()+':'+new Date().getMinutes()+':'+new Date().getSeconds();
                if(new Date(bizDate)>new Date(this.currentNode.planEndTime)){
                      isEditDisabledStatus = true;
                  }
              }
              return isEditDisabledStatus
            }
      },
      methods: {
          async popoverClick(actionType) {
              if (this.$app.optionsBtnPopoverRref) {
                this.$app.optionsBtnPopoverRref.value = this.$app.optionsBtnPopoverRref !== this.$refs.popover ? false : true;
              }
              this.$app.optionsBtnPopoverRref=this.$refs.popover
              this.rules = {};
              this.form.paramList = [];
              this.isShowFinishDetail = false;
              this.isReloadParam = false;
              this.isShowRemark = true
              this.actionType = actionType;
              if(actionType=='actionConfirm'){
                //手工确认，判断是否有回填参数
                  const p = this.$api.taskTodoApi.selectRollBackTaskParams({stepExecId:this.currentNode.stepExecId})
                  const resp = await this.$app.blockingApp(p);
                  this.form.paramList=resp.data;
                  this.form.paramList.forEach(item=>{
                      if(item.isRequire == '1'){
                          let ruleItem = item.paramKey;
                          this.rules[ruleItem] = [{validator: this.checkRule,required: true, trigger: 'change'}];
                      }
                  })
                  this.title= '手工确认';
                  this.form.remark = this.currentNode.remark;
                  this.popoverVisible = true;
              }else if(actionType=='autoTaskActionConfirm'){
                //待确定
                  this.autoData=[];
                  const p = this.$api.taskTodoApi.getAutoTaskParam({stepExecId:this.currentNode.stepExecId})
                  const resp = await this.$app.blockingApp(p);
                  if(resp.data){
                      this.autoData=resp.data;
                  }
                  this.title= '返回值';
                  this.popoverVisible = true;
              }else if(actionType == 'forcePass'){
                this.title= '干预通过';
                this.popoverVisible = true;
              }else if(actionType == 'fileForcePass'){
                this.fileForcePassMethod();
              }else if(actionType == 'queryExecRecords'){
                this.queryExecRecordsMethod();
              }else if(['finishDetailShow', 'reloadParam'].includes(actionType)){
                this.getCaseStepParam(actionType)

              }else if(actionType == 'isFormAction'){
                  //表单录入
                  this.isFormActionMethod({'data':this.currentNode});
              }
          },
          async getCaseStepParam (actionType){
            if(this.currentNode.stepExecId && this.currentNode.caseId){
                let taskCommit = {
                    caseId:this.currentNode.caseId,
                    stepExecId:this.currentNode.stepExecId,
                }
                const s = this.$api.taskDefineApi.getCaseStepParam(taskCommit)
                let resp = await this.$app.blockingApp(s);
                if(resp.data){
                    this.form.paramList=resp.data;
                }
            }
            this.title= '查看回填参数';
            this.isShowFinishDetail = true;
            if(actionType == 'reloadParam'){
                this.title= '回填参数重新录入';
                this.isShowFinishDetail = false;
                this.isReloadParam = true;
            }
            this.isShowRemark = false;
            this.popoverVisible = true;
          },
          async taskChangeMethod(){
            this.taskChange();
          },
          getLists(fieldValueRange) {
              let lists = [];
              const fieldRange = fieldValueRange.split(';')
              if (fieldRange) {
                  fieldRange.forEach(item => {
                      const values = item.split(':');
                      if(!values){
                          return;
                      }
                      if (values.length > 1) {
                          lists.push({
                              name: values[1],
                              value: values[0],
                          })
                      } else {
                          lists.push({
                              name: values[0],
                              value: values[0],
                          })
                      }
                  })
              }
              return lists;

          },

          checkRule(rule, value, callback) {
              callback();
          },
          async openKjd(actionType) {
              this.handleCmd(actionType);
          },

          indexDetail(actionType) {
              this.handleCmd(actionType);
          },
          // 备注确定 -- 保存
          async confirmRemark() {
              let isReturn = false;
              if(this.form?.paramList?.length>0){
                  this.form.paramList.forEach(item=>{
                      if(this.verifRuquire(item)){
                          this.$msg.warning('请补充完整必填项！');
                          isReturn = true;
                          return;
                      }
                      if(item.paramType=='date'){
                          this.currentNode.isHasdate = '1';
                      }
                  })
              }
              if(isReturn){
                  return;
              }
              this.popoverVisible = false;
              this.isShowEndTime = false;
              this.isShowRemark = true;
              this.currentNode.prdtInfo = this.prdtInfo;
              this.currentNode.remark = this.form.remark;
              this.currentNode.paramList = this.form.paramList;
              this.currentNode.planEndTime = this.form.planEndTime;
              let type = this.actionType;
              if(type=='actionV2Mot'){
                  type = 'actionConfirm';
              }
              if(type=='actionConfirm'){
                this.actionConfirm({'data':this.currentNode})
              }else if(type=='forcePass'){
                this.forcePassMethod({'data':this.currentNode})
              }else if(type=='fileForcePass'){
                this.fileForcePassMethod({'data':this.currentNode})
              }else if(type=='reExecute'){
                this.reExecute()
              }

              this.form.paramList = [];
          },
          /**
           * 校验必填项
           * @param item
           */
          verifRuquire(item) {
            let v = item.isRequire == '1'
            let v1 = item.paramType == 'string' && !item.paramString
            let v2 = item.paramType == 'number' && !item.paramNumber
            let v3 = item.paramType == 'amount' && !item.paramAmount
            let v4 = item.paramType == 'date' && !item.paramDate
            return v && (v1 || v2 || v3 || v4)
          },
          closeRemake() {
              this.popoverVisible = false
              this.isShowEndTime = false;
              this.isShowRemark = true;
              this.form.paramList = [];
          },

        //调整后的表单录入
        isFormActionMethod(params){
          this.actionV2Mot(params);
        },
        //调整后的手工确认
        async actionConfirm(params) {
          if(params.data.dealType&&this.$refs[params.data.dealType]&&this.$refs[params.data.dealType][0]  && this.$refs[params.data.dealType][0].actionConfirm){
            this.$refs[params.data.dealType][0].actionConfirm(params);
          }else {
            let taskCommit = {
              stepInfo: {},
              inst: {
                taskId: "",
              },
              paramListStr:'',
            };
            taskCommit.stepInfo.remark = params.data.remark;
            taskCommit.stepInfo.stepStatus = "06";
            if(params.data.stepActType === '7'){
              taskCommit.stepInfo.stepStatus = "07";
            }
            taskCommit.stepInfo.jobId = params.data.jobId;
            taskCommit.inst.taskId = params.data.taskId;
            taskCommit.stepInfo.stepCode = params.data.stepCode;
            taskCommit.stepInfo.bizDate = this.bizDate;
            taskCommit.stepInfo.execBizDate = this.bizDate;
            taskCommit.stepInfo.caseId = params.data.caseId;
            taskCommit.caseId = params.data.caseId;
            taskCommit.stepExecId = params.data.stepExecId;
            try {
              if(params.data?.paramList?.length>0){
                taskCommit.paramListStr = JSON.stringify(params.data.paramList);
                const s = this.$api.taskDefineApi.saveCaseStepParam(taskCommit)
                await this.$app.blockingApp(s);
                if(params.data.isHasdate && params.data.isHasdate==='1'){
                  const up = this.$api.taskConfigApi.updatePlanEndTimeByBaseDate({'caseId':taskCommit.caseId})
                  await this.$app.blockingApp(up);
                }
              }
              await this.$api.taskTodoApi.updateStepStatus(taskCommit.stepInfo);
              const p = this.$api.taskTodoApi.confirmKpiTask(taskCommit)
              await this.$app.blockingApp(p);
              if (this.actionOk) {
                await this.actionOk();
              }
              this.$msg.success('提交成功');
              this.$emit("onClose");
            } catch (e) {
              this.$msg.error(e);
            }
          }
        },
        //调整后的干预通过
        async forcePassMethod(params) {
          if(params.data.dealType){
            if(this.$refs[params.data.dealType] && this.$refs[params.data.dealType][0]  && this.$refs[params.data.dealType][0].forcePass){
              this.$refs[params.data.dealType][0].forcePass(params);
            }else {
              this.forcePassV2(params);
            }
          }else {
            let taskCommit = {
              stepInfo: {},
              inst: {
                taskId: "",
              },
            };

            taskCommit.stepInfo.remark = params.data.remark;
            taskCommit.stepInfo.stepStatus = "07";
            taskCommit.stepInfo.jobId = params.data.jobId;
            taskCommit.inst.taskId = params.data.taskId;
            taskCommit.stepInfo.stepCode = params.data.stepCode;
            taskCommit.stepInfo.bizDate = this.bizDate;
            taskCommit.stepInfo.execBizDate = this.bizDate;
            taskCommit.stepInfo.caseId = params.data.caseId;
            taskCommit.stepInfo.stepActType = params.data.stepActType;
            taskCommit.stepInfo.stepActKey = params.data.stepActKey;
            taskCommit.stepInfo.execStartTime = params.data.execStartTime;
            try {
              await this.$api.taskTodoApi.updateStepStatus(taskCommit.stepInfo);
              const p =  this.$api.taskTodoApi.confirmKpiTask(taskCommit);
              await this.$app.blockingApp(p);
              if (this.actionOk) {
                await this.actionOk();
              }
              this.$msg.success('提交成功');
              this.$emit("onClose");
            } catch (e) {
              this.$msg.error(e);
            }
          }
        },
        //调整后的查干预通过打开文件
        fileForcePassMethod(){
            let actionOk = this.actionOk;
          const row = this.currentNode;
          this.$drawerPage.create({
            className: 'elec-dashboard-drawer',
            width: 'calc(100% - 250px)',
            title: [row.stepName],
            component: 'file-step-info-page',
            args: {stepExecId: row.stepExecId,stepStatus:row.stepStatus,actionOk},
            cancelButtonTitle: '返回',
            okButtonVisible: false
          });
        },
        //查询执行记录
        queryExecRecordsMethod(){
          let actionOk = this.actionOk;
          const row = this.currentNode;
          this.$drawerPage.create({
            className: 'elec-dashboard-drawer',
            width: 'calc(100% - 250px)',
            title: [row.stepName],
            component: 'task-step-info-page',
            args: {caseId: row.caseId,stepCode:row.stepCode,actionOk},
            cancelButtonTitle: '返回',
            okButtonVisible: false
          });
        },
        async commonShowDetailPage(params){
          let url = params.data.detailUrl;
          let componentList =Object.keys(this.$root.constructor.options.components);
          if(componentList.includes(url)){
            this.hasDetailUrl = true;
          }else{
            this.hasDetailUrl = false;
            return;
          }
          const action = this.actionOk;
          const row = params.data;
          if(row.dealType == '05'){
            row.stepActKey = row.dealId;
          }
          this.$drawerPage.create({
            width: 'calc(100% - 250px)',
            title: [row.stepName],
            component: url,
            args: {stepExecId: row.stepExecId,action,stepCode: row.stepCode,stepActKey: row.stepActKey, caseId: row.caseId,  bizDate: this.bizDate, status: 1},
            cancelButtonTitle: '返回',
            okButtonVisible: false
          });
        },
        // 查询V2Mot
        async reviewV2MotShow(params) {
          let pageUrl = params.data.pageCode;
          if(!pageUrl){
            this.$msg.warning('该step未绑定页面！');
            return ;
          }
          //根据objCode 和 caseId 从case和业务对象关联表中查询出参数
          const p = this.$api.taskTodoApi.selectMotDataV2({objCode:params.data.objCode,caseId:params.data.caseId})
          const resp = await this.$app.blockingApp(p);
          if(!resp || !resp.data){
            this.$msg.warning('暂不支持新增业务对象数据！');
            return ;
          }
          let paramInfo = JSON.parse(resp.data.businessKey);
          paramInfo.disabled="1";
          this.$drawerPage.create({
            width: 'calc(80% - 250px)',
            title: [params.data.stepName],
            component: 'xcp-component-drawer',
            args: {pageCode: pageUrl, data: paramInfo,actionOk:this.actionOk.bind(this)},
            okButtonVisible: false,
            cancelButtonVisible: false,
          })
        },
        // 查看指标详情
        showIndexDetail(params){
          this.showIndexDetailInfo(params, this.showInfo.bind(this));
        },
        showIndexDetailInfo(params,actionOk) {
          const row = params.data;
          if(row.dealType == '05'){
            row.stepActKey = row.dealId;
          }
          this.$drawerPage.create({
            className: 'elec-dashboard-drawer',
            width: 'calc(100% - 300px)',
            title: [row.stepName],
            component: 'monitor-detail-page',
            args: {stepCode: row.stepCode,stepActKey: row.stepActKey, caseId: row.caseId,  bizDate: this.bizDate, status: 1
              ,actionOk},
            cancelButtonTitle: '返回',
            okButtonVisible: false,
            customButtonVisible:true,
            customButtonTitle:'指标逻辑'

          });
        },

        // 查看金智维接口详情
        showJzwDetail(params){
          this.showJzwDetailInfo(params, this.showInfo.bind(this));
        },
        showJzwDetailInfo(params,actionOk) {
          const row = params.data;
          this.$drawerPage.create({
            className: 'elec-dashboard-drawer',
            width: 'calc(100% - 250px)',
            title: [row.stepName],
            component: 'jzw-info',
            args: {stepCode: row.stepCode,caseId: row.caseId,actionOk},
            cancelButtonTitle: '返回',
            okButtonVisible: false
          });
        },

        //自动化接口任务查看明细
        openTradeDetailShow(){
          const menuId = 'agnes.app.ta.kjqr'
          this.$agnesUtils.closeTab(menuId);
          this.$nextTick(() => {
            if (menuId) {
              let clientView = this.$app.views.getView(menuId);
              let clientTabView = Object.assign({args: {bizDate:this.bizDate}, id: menuId}, clientView);
              this.$nav.showView(clientTabView);
            }
          })
        },

        //修改结束时间
        async editEndTime(params) {
          let form = {
            stepExecId: '',
            planEndTime: ''
          };
          form.stepExecId = params.data.stepExecId;
          form.planEndTime = params.data.planEndTime;
          try {
            const p = this.$api.elecProcessApi.editStepPlanEndTime(form)
            await this.$app.blockingApp(p);
            if (this.actionOk) {
              await this.actionOk();
            }
            this.$msg.success('修改成功');
            this.freshFlowData(false); // 刷新页面数据
            this.$emit("onClose");
          } catch (e) {
            this.$msg.error(e);
          }
        },

        // 手动启动step
        manualStartStep(params) {
          const rowData = params.data;
          let stepData = {}
          stepData.caseId = rowData.caseId;
          stepData.stepCode = rowData.stepCode;
          stepData.taskId = rowData.taskId;
          this.$api.caseConfigApi.manualStartStep(stepData).then((resp) => {
            if (resp.data) {
              this.$msg.success("重新执行成功");
              this.freshFlowData(false);
            } else {
              this.$msg.error("操作失败");
            }
          });
        },

        //重新录入
        async reloadParam(params){
          try {
            if(params.data.paramList && params.data.paramList.length>0){
              let taskCommit = {};
              taskCommit.paramList  = params.data.paramList;
              taskCommit.caseId = params.data.caseId;
              taskCommit.stepExecId = params.data.stepExecId;
              if(params.data.isHasdate && params.data.isHasdate ==='1'){
                taskCommit.isHasdate = true;
              }else {
                taskCommit.isHasdate = false;
              }
              const s = this.$api.taskConfigApi.reloadCaseStepParam(taskCommit)
              await this.$app.blockingApp(s);
              this.$msg.success('提交成功');
              this.freshFlowData(false); // 刷新页面数据
              this.$emit("onClose");
            }
            this.$emit("onClose");
          } catch (e) {
            this.$msg.error(e);
          }
        },



        // 重新执行
        async reExecute() {
          const ok = await this.$msg.ask(`是否确认重新执行`);
          if(!ok){
            return;
          }
          const rowData = this.currentNode;
          let kpiTaskReq = {}
          kpiTaskReq.caseId = rowData.caseId;
          kpiTaskReq.stepCode = rowData.stepCode;
          kpiTaskReq.bizDate = this.bizDate;
          kpiTaskReq.taskId = rowData.taskId;
          this.$api.kpiDefineApi.execTask(kpiTaskReq).then((resp) => {
            if (resp.data.status) {
              this.$msg.success("重新执行成功");
              if(this.actionOk){
                this.actionOk();
              }
            } else {
              this.$msg.error(resp.data.message || "操作失败");
            }
          });
        },

        async actionV2Mot(params) {
          let step = params.data;
          if(step.stepStatus=='01'){
            this.$msg.warning("任务还未启动!");
            return;
          }
          let stepDetail = this.$lodash.cloneDeep(step);
          stepDetail.taskName = stepDetail.stepName
          let isView = step.stepStatus.match(/06|07/) ? '1' : '0'
          let actionOk = this.actionOk;
          if(stepDetail.pageCode != '' && !stepDetail.xcpInfoKey){
            if(stepDetail.objCode){
              const p = this.$api.taskTodoApi.selectMotDataV2({objCode:stepDetail.objCode,caseId:stepDetail.caseId})
              const resp = await this.$app.blockingApp(p);
              if(resp.data){
                stepDetail.xcpInfoKey = resp.data.businessKey;
              }
            }
          }

          this.$nav.showDialog(
              'agnes-task-exec-deal',
              {
                args: {stepInfo: stepDetail, isView: isView,actionOk:actionOk},
                width: '98%',
                className: 'large-dialog larger'
              }
          )
        },
        showDialog() {
          this.$nav.showDialog(
              {
                component: flowChartDlg,
                args: {  },
                width: '98%',
                title: '13',
                className: 'large-dialog larger'
              }
          )
        },
        //干预通过 V2
        async forcePassV2(params){
          const rowData = params.data;
          let taskReq = {}
          taskReq.caseId = rowData.caseId;
          taskReq.stepCode = rowData.stepCode;
          taskReq.bizDate = this.bizDate;
          taskReq.taskId = rowData.taskId;
          taskReq.remark = rowData.remark; //备注
          this.$api.kpiDefineApi.forcePassV2(taskReq).then((resp) => {
            if (resp.data.status) {
              this.$msg.success("干预通过成功");
              if(this.actionOk){
                this.actionOk();
              }
            } else {
              this.$msg.error("操作失败");
            }
          });
        },
        taskChange(){
          this.showDlg('add', this.currentNode, this.actionOk.bind(this));
        },

        showDlg(mode, row, actionOk) {
          if (mode !== 'add' && !row ) {
            this.$msg.warning("请选中一条记录!");
            return;
          }
          let title = this.$dialog.formatTitle('转交申请', mode);
          this.$nav.showDialog(
              TaskChangeDlg,
              {
                args: {row, mode, actionOk},
                width: '50%',
                title: title,
              }
          );
        },
      }
  }
</script>


<style scoped>
  .optional-cell {
      width: 120px;
      height: 46px;
      line-height: 46px;
  }

  .optional-cell .svgSpan {
      display: inline-block;
      width: 16px;
      height: 16px;
      cursor: pointer;
  }
  .optional-cell .svgSpan >>> svg {
      width: 100%;
      height: 100%;
  }

  .optional-cell > span + span {
      margin-left: 10px;
  }

  .optional-cell .svgSpan > svg {
      width: 100%;
      height: 100%;
  }

  .op-btn {
      height: 25px;
      padding: 5px 19px;
      border-color: #E1E4E8;
      color: #333;
      background: transparent;
      font-size: 14px;
  }

  .op-btn.primary {
      color: #fff;
      background: #0f5eff;
      border-color: #0f5eff;
  }

  .detail-btn {
      padding: 7px 0;
  }

  .detail-btn>>>.fa.fa-eye{
      font-size: 15px;
      vertical-align: text-bottom;
      color: #0f5eff;
      line-height: 17px;
  }

  .detail-btn.is-disabled>>>.fa.fa-eye {
      color: #ccc;
  }
  .repeat {
    font-size: 15px;
    vertical-align: text-bottom;
    color: #0f5eff;
    line-height: 17px;
  }
  .el-form{
    padding:0 20px 0 0;
  }
  .el-form >>> .el-form-item {
    margin-bottom: 10px;
  }
  .el-form >>> .el-form-item .el-form-item__label {
    width: 95px !important;
  }
  .el-form >>> .el-form-item .el-form-item__content {
    margin-left: 95px !important;
  }
</style>
