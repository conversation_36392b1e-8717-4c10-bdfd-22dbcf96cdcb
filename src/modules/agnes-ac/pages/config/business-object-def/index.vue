<template>
  <div v-if="businessDate.length === 0">
    <div style="display: flex;justify-content: center;align-items: center;height: 100%">
      <svg-icon name="empty-interface" height="50%"/>
    </div>
  </div>
  <div v-else style="height: 100%;">
    <el-form style="padding: 0;height: 100%;display: flex;flex-direction: column">
      <div class="object-tab" style="height: 40px">
        <el-radio-group v-model="objCode" size="small">
          <el-radio-button v-for="(item,index) in businessDate"
                           :key="index"
                           :label="item.dictId"
                           border>
            {{item.dictName}}对象
          </el-radio-button>
        </el-radio-group>
      </div>
        <module-card title="基本信息" shadow="never" v-if="objCode" style="flex: 1;overflow-y: scroll">
          <div slot="headOption">
            <el-button type="primary" v-if="edit" @click="edit = !edit" size="mini">编辑</el-button>
            <el-button v-else class="primary" @click="canle" size="mini">取消</el-button>
            <el-button type="primary" v-if="edit"  @click="preview" size="mini" style="margin-right: 20px">预览</el-button>
            <el-button type="primary" v-else class="primary" @click="saveObjInfo" size="mini" style="margin-right: 20px">保存</el-button>
          </div>
          <div slot="content">
            <el-form ref="businessObj" class="business-obj-form" label-width="100px" :model="businessObj"
                     :disabled="edit" :rules="objRules" style="padding: 0;"
                     v-loading="loading"
                     element-loading-text="数据加载中..."
                     element-loading-background="rgba(255, 255, 255, 0.6)">
              <el-form-item label="对象名称:" prop="objName">
                <gf-input v-model="businessObj.objName" placeholder="请输入对象名称" :max-byte-len="120"/>
              </el-form-item>
              <el-form-item label="查询sql:" prop="execScript">
                <gf-input type="textarea" v-model="businessObj.execScript" :autosize="{minRows: 2, maxRows: 20}"
                          placeholder="请输入查询sql"
                          :max-byte-len="2000" style="width: 100%"></gf-input>
              </el-form-item>
              <el-form-item label="属性配置:" prop="params">
                <div class="attribute-table">
                  <div class="option-button">
                    <gf-button type="text" class="headOption" @click="initParams">初始化</gf-button>
                    <gf-button type="text" class="headOption" @click="addColumn">增加</gf-button>
                    <em class="question el-icon-question" style="margin-left: 18px;font-size: 16px;color: #0f5eff;cursor: pointer;"
                        title="操作说明" @click="openHelpFile"></em>
                  </div>
                  <div class="rule-table">
                    <el-table header-row-class-name="required"
                              row-class-name="rule-row"
                              cell-class-name="rule-cell"
                              :header-cell-style="{textAlign:'center',padding:'0px',backgroundColor:'#E8EFFF'}"
                              :data="businessObj.params"
                              v-loading="tableLoading"
                              element-loading-text="正在初始化字段列表..."
                              element-loading-background="rgba(255, 255, 255, 0.6)"
                              border stripe
                              style="width: 100%;">
                      <el-table-column label="操作" width="50">
                        <template slot-scope="scope">
                          <gf-button @click="reMoveColumn(scope.$index)" type="text">删除</gf-button>
                        </template>
                      </el-table-column>
                      <el-table-column>
                        <template slot="header">
                          <span v-if="businessObj.params.length !== 0" class="label-require">*</span>
                          <span>属性名称</span>
                        </template>
                        <template slot-scope="scope">
                          <el-form-item :rules="objRules.paramName"
                                        :prop="'params.'+scope.$index+'.paramName'">
                            <gf-input v-model="scope.row.paramName"
                                      :style="!scope.row.paramName ? 'border:1px solid #f00':''">
                            </gf-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column>
                        <template slot="header">
                          <span v-if="businessObj.params.length !== 0" class="label-require">*</span>
                          <span>SQL字段</span>
                        </template>
                        <template slot-scope="scope">
                          <el-form-item :rules="objRules.tableField"
                                        :prop="'params.'+scope.$index+'.tableField'">
                            <gf-input :style="!scope.row.tableField ? 'border:1px solid #f00':''"
                                      v-model="scope.row.tableField">
                            </gf-input>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column>
                        <template slot="header">
                          <span v-if="businessObj.params.length !== 0" class="label-require">*</span>
                          <span>数据类型</span>
                        </template>
                        <template slot-scope="scope">
                          <el-form-item :rules="objRules.dataType"
                                        :prop="'params.'+scope.$index+'.dataType'">
                            <gf-dict-select dict-type="AGNES_FIELD_TYPE"
                                            :style="!scope.row.dataType ? 'border:1px solid #f00':''"
                                            v-model="scope.row.dataType"/>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="数据格式">
                        <template slot-scope="scope">
                          <gf-input v-model="scope.row.dataFormat" :max-byte-len="50"></gf-input>
                        </template>
                      </el-table-column>
                      <el-table-column label="字典类型">
                        <template slot-scope="scope">
                          <el-form-item :rules="scope.row.dataType==='dict'?objRules.dataType:null"
                                        :prop="scope.row.dataType==='dict'?('params.'+scope.$index+'.dataType'):''">
                            <el-select v-model="scope.row.dictTypeId" filterable clearable placeholder="请选择"
                                       v-if="scope.row.dataType==='dict'"
                                       :style="(scope.row.dataType==='dict' && !scope.row.dictTypeId) ? 'border:1px solid #f00':''" >
                              <el-option
                                  v-for="dict in dictData"
                                  :key="dict.dictTypeId"
                                  :label="dict.dictTypeName"
                                  :value="dict.dictTypeId">
                              </el-option>
                            </el-select>
                          </el-form-item>
                        </template>
                      </el-table-column>
                      <el-table-column label="对象通用属性">
                        <template slot-scope="scope">
                          <el-form-item>
                            <gf-dict-select dict-type="BUSINESS_OBJECT_GENERAL_FIELD"
                                            v-model="scope.row.generalField"/>
                          </el-form-item>
                        </template>
                      </el-table-column>
                    </el-table>
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </module-card>
    </el-form>
  </div>
</template>

<script>
import loadsh from "lodash";
import configHelpStr from "./configHelpStr";
import businessObjData from "./business-objDate-dlg";

export default {
  name: "index",
  data(){
    const checkParams  = (rule, value, callback) => {
      if(JSON.stringify(value) === '[]'){
        callback(new Error('属性不能为空'));
      }else{
        callback();
      }
    }
    const checkSqlParams  = (rule, value, callback) => {
      if(value === ''){
        callback(new Error('属性不能为空'));
      }else if(!this.initFlag) {
        callback(new Error('sql初始化失败，请检查sql编写是否正确'));
      }else{
        callback();
      }
    }
    return{
      initFlag:true,
      loading:false,
      tableLoading:false,
      objCode:'',
      edit:false,
      businessDate:[],
      dictData:[],
      businessObj:{
        objCode:'',
        objName:'',
        execScript:'',
        params:[]
      },
      paramsTemp:[],
      tableFieldTemp:[],
      businessObjCopy:{},
      objRules:{
        'objName':[
          {required: true, message:'对象名称不能为空',trigger:['blur']},
          {max: 30, message: "提醒标题最多30字", trigger: "change"},
        ],
        'execScript':[{required: true, validator:checkSqlParams,trigger:['blur']}],
        'params':[{required: true,validator:checkParams,trigger:['blur']}],
        'paramName':[{required: true,message:'属性名称不能为空',trigger:['blur']}],
        'tableField':[{required: true,message:'SQL字段不能为空',trigger:['blur']}],
        'dataType':[{required: true,message:'数据类型不能为空',trigger:['blur']}],
      }
    }
  },
  watch:{
    'objCode':function (val){
      this.getObjectInfo(val)
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init(){
      let dictList = this.$app.dict.getDictItems("AGNES_PRDT_CALENDAR_OBJ");
      if(dictList){
        let arr = [];
        dictList.forEach(item=>{
          if(item.dictId !== 'common' || item.dictName !== '通用'){
            arr.push(item)
          }
        });
        this.businessDate = arr;
        this.objCode = this.businessDate[0].dictId;
        this.getObjectInfo(this.objCode)
        this.initDict();
      }else{
        this.$message.error("业务对象字典（AGNES_PRDT_CALENDAR_OBJ）未维护，请及时维护！")
      }
    },
    //初始化字典
    async initDict() {
      const resp = await this.$api.customQueryApi.queryDictList();
      if (resp && resp.data) {
        this.dictData = loadsh.clone(resp.data)
      }
    },
    //获取对象信息，根据对象code
    async getObjectInfo(objCode) {
      this.loading = true
      try {
        const resp = await this.$api.businessObjectApi.getBusinessObjList(objCode);
        if (resp) {
          if(JSON.stringify(resp.data) === '{}'){
            this.businessObj = {
              objCode:'',
              objName:'',
              execScript:'',
              params:[]
            }
          }else{
            this.businessObj = resp.data;
          }
        }
        Object.assign(this.businessObjCopy,this.businessObj)
      } catch (resp) {
        this.$message.error(resp)
      }
      this.loading = false
      this.edit = true
      this.$refs['businessObj'].clearValidate();
    },
    //初始化sql字段
    async initParams() {
      if (!this.businessObj.execScript) {
        this.$message.warning("请输入查询sql");
        return;
      }
      this.tableLoading = true;
      try {
        let params = {
          sql: this.businessObj.execScript
        }
        const resp = await this.$api.customQueryApi.initFields(params);
        if (resp && resp.data) {
          this.tableFieldTemp = []
          this.paramsTemp = [];
          let arr =[]
          this.businessObj.params.forEach(item1=>{
            arr.push(item1.tableField)
          })
          Object.assign(this.tableFieldTemp , arr);
          this.businessObj.params.forEach(item2=>{
            this.paramsTemp[item2.tableField] = item2;
          })
          this.businessObj.params =[]
          resp.data.forEach(item => {
            this.businessObj.params.push({
              paramName: this.tableFieldTemp.includes(item)?this.paramsTemp[item].paramName:'',
              tableField: item,
              dataType: this.tableFieldTemp.includes(item)?this.paramsTemp[item].dataType:'',
              dataFormat:this.tableFieldTemp.includes(item)?this.paramsTemp[item].dataFormat:'',
              dictTypeId: this.tableFieldTemp.includes(item)?this.paramsTemp[item].dictTypeId:'',
              generalField:this.tableFieldTemp.includes(item)?this.paramsTemp[item].generalField:''
            })
          })
        }
        this.initFlag =true;
      } catch (resp) {
        this.initFlag = false
      }
      this.tableLoading = false;
      this.$refs['businessObj'].validate()
    },
    //新增行数据
    addColumn() {
      this.businessObj.params.push({
        paramName: '',
        tableField: '',
        dataType: '',
        dataFormat: '',
        dictTypeId: '',
        generalField:''
      });
      this.$refs['businessObj'].validate()
    },
    //删除行数据
    reMoveColumn(index) {
      this.businessObj.params.splice(index, 1);
      this.$refs['businessObj'].validate()
    },
    //保存
    async saveObjInfo() {
      const ok = await this.$refs['businessObj'].validate();
      if (!ok) {
        return;
      }
      try{
        this.businessObj.objCode = this.objCode
        await this.$api.businessObjectApi.saveObjInfo(this.businessObj);
        await this.getObjectInfo(this.objCode);
      }catch (resp){
        this.$message.error(resp);
      }
    },
    //预览
    preview(){
      if(!this.businessObj.execScript){
        this.$message.warning("未进行相关配置，请先配置保存再进行预览")
        return
      }
      let objCode = this.businessObj.objCode;
      this.$drawerPage.create({
        width: 'calc(80% - 165px)',
        title: [this.businessObj.objName+"-数据预览"],
        component: businessObjData,
        okButtonVisible:false,
        args: {objCode},
        cancelButtonTitle:"关闭",
      });
    },
    canle(){
      this.edit = !this.edit
      this.$refs['businessObj'].clearValidate();
      this.getObjectInfo(this.objCode)
    },
    // 打开帮助文档
    openHelpFile(){
      if(this.helpNotify && this.helpNotify.close){
        this.helpNotify.close();
      }
      this.helpNotify = this.$notify({
        width: 400,
        title: '业务对象参数配置说明文档',
        customClass: 'cronHelpNotify',
        dangerouslyUseHTMLString: true,
        duration: 0,
        message: configHelpStr()
      });
    },
  }
}
</script>

<style scoped>
.headOption{
  color: #0f5eff !important;
  border: 1px solid #0f5eff !important;
  height: 25px;
  padding: 0px 5px !important;
}
.label-require{
  color: red;
  margin-right: 5px;
}
/deep/ .el-card .el-card__header .clearfix {
  align-items: center;
}
</style>
