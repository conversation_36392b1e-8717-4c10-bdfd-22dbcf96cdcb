<template>
  <div>
    <el-form ref="form" class="case-scene-form" :model="form" :rules="rules" label-width="60px" :disabled="mode == 'approve'">
      <div class="line">
        <el-form-item class="encoding-rules" label="编码:" prop="code">
          <gf-input v-model="form.code" :max-byte-len="8" :disabled="true" @change="changeObjCode">
            <template slot="prepend">obj-</template>
          </gf-input>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="分类:" prop="type">
          <gf-dict-select dict-type="AGNES_OBJ_TYPE"  v-model="form.objType" />
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="描述:" prop="comment">
          <gf-input type="textarea" rows="6" v-model="form.objDesc" :max-byte-len="100"></gf-input>
        </el-form-item>
      </div>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "basic-info",
  props: {
    basicInfo: {
      type: Object,
    },
    mode: {
      type: String,
    },
  },
  computed: {
    nameDisplay() {
      if (this.form.name && this.form.code) {
        return this.form.name + "（obj-" + this.form.code + ")";
      } else if (this.form.name && !this.form.code) {
        return this.form.name;
      } else if (!this.form.name && this.form.code) {
        return "（obj-" + this.form.code + ")";
      } else {
        return "";
      }
    },
  },
  data() {
    const checkencodingRule = (rule, value, callback) => {
      let numberReg = /^\d+(\.\d+)?$/;
      if (value === "") {
        callback(new Error("编码规则不能为空"));
      } else if (!numberReg.test(value)) {
        callback(new Error("编码规则位数字"));
      } else if (value.length != 8) {
        callback(new Error("编码规则为8位"));
      } else {
        callback();
      }
    };
    return {
      objOption: [],
      objPageOption: [],
      form: {
        optAttributesArr: [],
        optAttributes: "",
        name: "",
        code: "",
        opInfosInterface: "",
        objType: "",
        objDesc: "",
      },
      nameDisplayOption: [{ dictId: "name", dictName: "名称(编码)" }],
      rules: {
        code: [{ required: true, validator: checkencodingRule, trigger: "blur" }],
      },
    };
  },

  mounted() {
    this.initData();
    this.$nextTick(() => {
      if (this.basicInfo) {
        this.form = this.$lodash.cloneDeep(Object.assign(this.form, this.basicInfo));
        if (this.basicInfo.optAttributes) {
          this.form.optAttributesArr = this.basicInfo.optAttributes.split(";");
        }
      }
    });
  },
  methods: {
    async initData() {
      try {
        const p = this.$api.blockApi.queryObj();
        let resp = await this.$app.blockingApp(p);
        this.objOption = resp.data;
      } catch (e) {
        this.$msg.error(e);
      }
    },
    async getObjPage() {
      const p2 = this.$api.blockApi.queryObjPageInfo(this.form.objId);
      let resp2 = await this.$app.blockingApp(p2);
      this.objPageOption = resp2.data;
    },
    //保存时拿数据
    getData() {
      let data = null;
      this.$refs["form"].validate((validate) => {
        if (!validate) {
          data = false;
        } else {
          if (this.form.optAttributesArr) {
            this.form.optAttributes = this.form.optAttributesArr.join(";");
          }
          data = this.form;
        }
      });
      return data;
    },
    changeObjCode(value) {
      this.$app.runCmd("object.change.objCode", value);
    },
    showView(menuId) {
      if (menuId) {
        let clientView = this.$app.views.getView(menuId);
        let clientTabView = Object.assign({ args: {}, id: menuId }, clientView);
        this.$nav.showView(clientTabView);
      }
    },
  },
};
</script>

<style scoped>
.case-scene-form {
  padding: 0;
  margin: 0;
  width: 80%;
}

>>> .el-form .encoding-rules.el-form-item .el-form-item__content input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

>>> .el-form .el-form-item .el-form-item__content .el-input-group__prepend {
  border-color: #a8aed3;
}
</style>
