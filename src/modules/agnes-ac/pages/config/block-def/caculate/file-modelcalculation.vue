<template>
  <div>
    <el-form
      ref="form"
      :model="form"
      :rules="rules"
      :disabled="dealInfo.caseView"
      label-width="80px"
    >
      <div class="line">
        <el-form-item label="执行方式:" prop="execType">
          <el-select
            v-model="form.execType"
            clearable
            :disabled="mode !== 'add' && !!execType"
            @change="hanleSelectChange"
          >
            <gf-filter-option
              v-for="item in modelList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </gf-filter-option>
          </el-select>
        </el-form-item>
        <el-form-item label="保存方式:" prop="paramsOption">
          <el-select
            style="width: 100%"
            class="multiple-select"
            v-model="form.paramsOption"
            clearable
            filterable
            placeholder="请选择"
          >
            <gf-filter-option
              v-for="item in paramsOptions"
              :key="item.dictId"
              :label="item.dictName"
              :value="item.dictId"
            ></gf-filter-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="执行函数:" v-if="form.execType == '0'">
          <el-select v-model="form.execFunc" clearable>
            <gf-filter-option
              v-for="item in FunArr"
              :key="item.fnKey"
              :label="item.fnName"
              :value="item.fnKey"
            >
            </gf-filter-option>
          </el-select>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item label="上传文件:" v-if="form.execType == '2'">
          <el-upload
            class="upload-demo"
            action="/api/agnes-ac/v2/block/parse-file"
            :on-remove="handleRemove"
            accept=".py"
            :before-remove="beforeRemove"
            :file-list="fileList"
            :limit="1"
            :on-success="handleSuccess"
          >
            <el-button size="small" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item
          label="脚本名称:"
          v-if="form.execType == '1' || form.execType == '2'"
        >
          <gf-input v-model="form.procName" :max-byte-len="50"></gf-input>
        </el-form-item>
      </div>
      <div class="line">
        <el-form-item
          label="脚本代码:"
          v-if="form.execType == '1' || form.execType == '2'"
        >
          <gf-input v-model="form.procCode"></gf-input>
        </el-form-item>
      </div>
      <el-form-item
        label="执行脚本:"
        v-if="form.execType == '1' || form.execType == '2'"
      >
        <monacoEditor
          :flag="false"
          :options="editorOptions02"
          :height="editorHeight02"
          :code="form.proc"
          @getValue="handleValue"
        >
        </monacoEditor>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import monacoEditor from "../../../../../../components/common/monaco-editor/index.vue";
export default {
  name: "file-model",
  props: {
    mode: {
      type: String,
      default: "add",
    },
    blockCode: {
      type: String,
    },
    dealInfo: {
      type: Object,
    },
  },
  components: {
    monacoEditor,
  },
  data() {
    return {
      FunArr: [],
      paramsOptions: [],
      form: {
        pkId: "",
        execType: "",
        execFunc: "",
        procCode: "",
        procName: "",
        paramsOption: "1", //默认选中“只保留最新
        proc: "",
        file: "",
      },
      execType: "",
      rules: {
        execType: [{ required: true, message: "请选择模型", trigger: "blur" }],
      },
      modelList: [
        { label: "函数", value: "0" },
        { label: "脚本", value: "1" },
        { label: "python", value: "2" },
      ],
      editorOptions02: {
        language: "json",
        readOnly: false,
      },
      editorHeight02: "600px",
      fileList: [],
    };
  },
  beforeMount() {
    if (this.mode != "add") {
      Object.assign(this.form, this.dealInfo);
      this.getKpiInfo(
        this.blockCode.includes("block-")
          ? this.blockCode
          : "block-" + this.blockCode
      );
    }
    this.getFuncOption();
    this.paramsOptions = this.$app.dict.getDictItems("AGNES_PARAMS_OPTIONS");
  },
  methods: {
    async getFuncOption() {
      const c = this.$api.ruleTableApi.getFnAndModelfields();
      const resp = await this.$app.blockingApp(c);
      if (resp.data) {
        this.FunArr = resp.data;
      }
    },
    getData() {
      if (this.form.execType === "") {
        this.$msg.warning("请选择执行方式！");
        return;
      }

      if (this.form.execType == "1" || this.form.execType == "2") {
        if (
          this.form.procCode == "" ||
          this.form.procName == "" ||
          this.form.proc == ""
        ) {
          this.$msg.warning("请将脚本信息补充完整！");
          return;
        }
      } else {
        if (this.form.execFunc == "") {
          this.$msg.warning("请选择执行函数！");
          return;
        }
      }
      //组合基本信息中的getData中的数据
      let basicInfo = this.$app.runCmd("get-blockdef-basicInfo-data");
      let kpiList = { kpiCode: "block-" + basicInfo.blockCode };
      if (this.form.execType == "0") {
        this.form.procCode = "";
        this.form.procName = "";
        this.form.proc = "";
      } else {
        this.form.execFunc = "";
        kpiList.kpiProcInfo = this.form;
      }
      kpiList.paramsOption = this.form.paramsOption;
      kpiList.dopOPDSReKpiParams = { noManualRefresh: "0" };
      kpiList.kpiAuthInfo = { kpiCode: "block-" + basicInfo.blockCode };
      kpiList.dopOPDSReKpi = {
        pkId: this.form.pkId,
        kpiCode: "block-" + basicInfo.blockCode,
        style: "01",
        kpiName: basicInfo.blockName,
        execFunc: this.form.execFunc,
        execType: this.form.execType,
        paramsOption: this.form.paramsOption
      };
      return { kpiList: kpiList, dealType: "12" };
    },
    async getKpiInfo(kpiCode) {
      const p1 = this.$api.kpiDefineApi.queryKpiInfoMation({
        kpiCode: kpiCode,
      });
      const resp1 = await this.$app.blockingApp(p1);
      const p = this.$api.kpiDefineApi.getKpiScript({
        kpiCode: kpiCode,
        execType: resp1.data?.execType,
      });
      const resp = await this.$app.blockingApp(p);
      let data = {
        ...this.form,
        ...resp1.data,
        ...resp.data,
      };
      this.form = data;
      this.form.pkId = resp1.data.pkId;
      this.form.execType = resp1.data?.execType;
      this.execType = resp1.data?.execType;
      let fileTemp = {
        name: this.form.procName,
        url: "",
      };
      this.fileList.push(fileTemp);
    },
    // changeCode(val) {
    //     this.form.proc = val
    // },
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    handleSuccess(data) {
      console.log("data", data);
      this.form.procName = data.data.procName;
      this.form.procCode = data.data.procCode;
      this.form.proc = data.data?.proc;
      console.log("this.form.proc", this.form.proc);
    },
    handleValue(value) {
      this.form.proc = value;
    },
    handleRemove() {
      this.form.procCode = "";
      this.form.procName = "";
      this.form.proc = "";
    },
    hanleSelectChange() {
      this.form.procCode = "";
      this.form.procName = "";
      this.form.proc = "";
    },
  },
};
</script>
