<template>
  <div>
    <el-form ref="form" :disabled="disabled" :model="stepData" :rules="rules" class="etl-form" label-width="100px">
      <el-form-item label="配置描述:" prop="confName">
        <gf-input v-model="stepData.confName" :max-byte-len="50"></gf-input>
      </el-form-item>
      <el-form-item label="源数据源:" prop="originDs">
        <gf-ds v-model="stepData.originDs"></gf-ds>
      </el-form-item>
      <el-form-item label="SELECT分句:" prop="selectClause">
        <el-input v-model="stepData.selectClause"></el-input>
      </el-form-item>
      <el-form-item label="FROM分句:" prop="fromClause">
        <el-input v-model="stepData.fromClause"></el-input>
      </el-form-item>
      <el-form-item label="WHERE分句:" prop="whereClause">
        <el-input v-model="stepData.whereClause"></el-input>
      </el-form-item>
      <el-form-item label="GROUP分句:" prop="groupClause">
        <el-input v-model="stepData.groupClause"></el-input>
      </el-form-item>
      <el-form-item label="排序字段:" prop="orderKeys">
        <el-input v-model="stepData.orderKeys"></el-input>
      </el-form-item>
      <el-form-item label="目标数据源:" prop="destDs">
        <gf-ds v-model="stepData.destDs"></gf-ds>
      </el-form-item>
      <el-form-item label="目标表:" prop="destTable">
        <el-input v-model="stepData.destTable"></el-input>
      </el-form-item>
      <el-form-item label="目标去重表:" prop="currentTable">
        <el-input v-model="stepData.currentTable"></el-input>
      </el-form-item>
      <el-form-item label="删除目标表数据:" prop="delDest">
        <gf-dict-select dict-type="GF_BOOL_TYPE" v-model="stepData.delDest" />
      </el-form-item>

      <el-form-item label="动态参数添加:" style="width: 800px;">
        <div class="rule-table">
          <el-table header-row-class-name="rule-header-row" header-cell-class-name="rule-header-cell" row-class-name="rule-row" cell-class-name="rule-cell" :data="paramList" border stripe style="width: 100%;">
            <el-table-column prop="fieldKey" label="字段名称" width="150px">
              <template slot-scope="scope">
                <el-input v-model="scope.row.fieldName"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="fieldCode" label="字段编码" width="250px">
              <template slot-scope="scope">
                <tag-textarea-pave v-model="scope.row.fieldCode"></tag-textarea-pave>
              </template>
            </el-table-column>
            <el-table-column prop="example" label="示例值">
              <template slot-scope="scope">
                <el-input v-model="scope.row.example"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="option" label="操作" align="center" width="100px">
              <template slot-scope="scope">
                <span class="option-span" @click="deleteParamRuleRow(scope.$index)">删除</span>
              </template>
            </el-table-column>
          </el-table>
          <el-button @click="addParamRule()" class="rule-add-btn" size="small">新增</el-button>
        </div>
      </el-form-item>

      <el-form-item label-width="0">
        <div class="line" style="margin-bottom: 10px;">
          <gf-button @click="loadFields()" class="action-btn" size="mini">映射字段</gf-button>
          <gf-button @click="deleteRow()" class="action-btn" size="mini">删除</gf-button>
        </div>
        <div class="rule-table">
          <el-table header-row-class-name="rule-header-row" header-cell-class-name="rule-header-cell" row-class-name="rule-row" cell-class-name="rule-cell" ref="multipleTable" :data="stepData.dopETLReConfDBFields" border stripe style="width: 100%;">
            <el-table-column type="selection" width="55"></el-table-column>
            <el-table-column label="序号">
              <template slot-scope="scope">
                <span>{{ scope.$index + 1 }}</span>
              </template>
            </el-table-column>
            <el-table-column prop="originField" label="源字段">
              <template slot-scope="scope">
                <el-input :disabled="true" v-model="scope.row.originField"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="originFieldType" label="字段类型">
              <template slot-scope="scope">
                <el-input :disabled="true" v-model="scope.row.originFieldType"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="destTable" label="目标表">
              <template slot-scope="scope">
                <el-input :disabled="true" v-model="scope.row.destTable"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="destField" label="目标字段">
              <template slot-scope="scope">
                <el-select v-model="scope.row.destField" @change="destFieldChange(scope.row.destField, scope.$index)" clearable>
                  <gf-filter-option v-for="item in stepData.fieldMap" :key="item.fieldName" :label="item.fieldName" :value="item.fieldName"></gf-filter-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="destFieldType" label="目标字段类型">
              <template slot-scope="scope">
                <el-input :disabled="true" v-model="scope.row.destFieldType"></el-input>
              </template>
            </el-table-column>
            <el-table-column prop="nullAble" label="是否必填">
              <template slot-scope="scope">
                <el-select v-model="scope.row.nullAble" :disabled="true" clearable>
                  <gf-filter-option v-for="item in BooleanArr" :key="item.index" :label="item.value" :value="item.index"></gf-filter-option>
                </el-select>
              </template>
            </el-table-column>
            <el-table-column prop="isPk" label="是否pk">
              <template slot-scope="scope">
                <el-checkbox v-model="scope.row.isPk" true-label="1" false-label="0"></el-checkbox>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'exeScript',
  props: {
    stepData: {
      type: Object
    },
    etlConfVos: {
      type: Array
    },
    disabled: Boolean
  },
  data() {
    return {
      paramList: [],
      BooleanArr: [
        { value: '否', index: '0' },
        { value: '是', index: '1' }
      ],
      rules: {
        destDs: [{ required: true, message: '请选择目标数据源！', trigger: 'blur' }],
        originDs: [{ required: true, message: '请选择源数据源！', trigger: 'blur' }],
        selectClause: [{ required: true, message: '请输入SELECT分句！', trigger: 'blur' }],
        fromClause: [{ required: true, message: '请输入FROM分句！', trigger: 'blur' }],
        orderKeys: [{ required: true, message: '请输入排序字段！', trigger: 'blur' }],
        destTable: [{ required: true, message: '请输入目标表！', trigger: 'blur' }]
      }
    };
  },

  watch: {
    'stepData.paramFields': {
      handler(value) {
        if (this.paramList) {
          this.paramList.removeRange(0, this.paramList.length);
        }
        if (value) {
          value.forEach(item => {
            if (item.confId === this.stepData.confId) {
              this.paramList.push(item);
            }
          });
        }
      },
      deep: true,
      immediate: true
    }
  },

  methods: {
    async loadFields() {
      let p = this.$api.dataPipeApi.onLoadInfos(this.stepData);
      let rep = await this.$app.blockingApp(p);
      this.stepData.fieldMap = rep.data.destFieldsMap[this.stepData.destTable];
      this.$set(this.stepData, 'dopETLReConfDBFields', rep.data.dopETLReConfDBFieldList || []);
    },
    destFieldChange(value, index) {
      let itemInfo = {};
      this.stepData.fieldMap.forEach(item => {
        if (item.fieldName == value) {
          itemInfo = item;
        }
      });
      this.stepData.dopETLReConfDBFields[index].destFieldType = itemInfo.fieldType;
      this.stepData.dopETLReConfDBFields[index].nullAble = itemInfo.nullable + '';
    },

    deleteRow() {
      let items = this.$refs.multipleTable.selection;
      if (items.length === 0) {
        this.$msg.warning('请选择一条记录进行操作!');
        return;
      }
      items.forEach(item => {
        const index = this.stepData.dopETLReConfDBFields.findIndex(it => it.pkId === item.pkId);
        if (index > -1) this.stepData.dopETLReConfDBFields.splice(index, 1);
      });
    },

    // 删除行
    deleteParamRuleRow(rowIndex) {
      this.stepData.paramFields.splice(rowIndex, 1);
    },
    addParamRule() {
      const newFileTableObj = {
        fieldName: '',
        fieldCode: '',
        example: 'example',
        confId: this.stepData.confId
      };
      if (this.stepData.paramFields === undefined || this.stepData.paramFields === null) {
        this.stepData.paramFields = [];
      }
      this.stepData.paramFields.push(newFileTableObj);
    }
  }
};
</script>

<style scoped>
.gf-select-user {
  width: 100%;
  height: calc(100% - 42px);
}

.exeScript {
  width: 100%;
  height: 100px;
  border-radius: 5px;
  border-color: #999;
}
.block-def-dialog .etl-form >>> .el-form-item:not(:last-child) {
  width: 50%;
}
</style>
