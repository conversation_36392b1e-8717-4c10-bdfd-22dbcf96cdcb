<template>
    <div>
        <el-form :model="form" ref="form" :rules="rules" label-width="80px" class="file-generate">
            <div>
                <el-form-item label="文件模版:" prop="templateId">
                    <el-select v-model="form.templateId" clearable @change="templateChange" filterable>
                        <gf-filter-option v-for="item in fileTemplateList" :key="item.pkId" :label="item.modelName" :value="item.pkId"></gf-filter-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="文件名称:" prop="fileName">
                    <gf-input v-model="form.fileName" :max-byte-len="50"></gf-input>
                </el-form-item>
                <el-form-item label="文件变量:" prop="inParamFields">
                    <div class="rule-table">
                        <el-table header-row-class-name="rule-header-row" header-cell-class-name="rule-header-cell" row-class-name="rule-row" cell-class-name="rule-cell" :data="form.inParamFields" border stripe style="width: 100%;">
                            <el-table-column prop="fieldKey" label="参数名" width="200px">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.fileName" :disabled="true"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="fieldCode" label="参数值">
                                <template slot-scope="scope">
                                    <tag-textarea-pave v-model="scope.row.fieldCode" :disabled="true"></tag-textarea-pave>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-form-item>
                <el-form-item label="文件生成:" prop="outParamFields">
                    <div class="rule-table">
                        <el-table header-row-class-name="rule-header-row" header-cell-class-name="rule-header-cell" row-class-name="rule-row" cell-class-name="rule-cell" :data="form.outParamFields" border stripe style="width: 100%;">
                            <el-table-column prop="fieldKey" label="参数名" width="200px">
                                <template slot-scope="scope">
                                    <el-input v-model="scope.row.fileName" :disabled="true"></el-input>
                                </template>
                            </el-table-column>
                            <el-table-column prop="fieldCode" label="参数值">
                                <template slot-scope="scope">
                                    <tag-textarea-pave v-model="scope.row.docId" :disabled="true"></tag-textarea-pave>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-form-item>
                <el-form-item label="模版预览:">
                    <template-style-adjust :parentData="parentData"></template-style-adjust>
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>

<script>
import TextareaDialog from '../../../../../../components/biz/tag-textarea/textarea-dialog';
import templateStyleAdjust from './file-template';
export default {
    name: 'file-generate',
    props: {
        dealInfo: {
            type: Object
        },
        mode: {
            type: String
        }
    },
    components: {
        'template-style-adjust': templateStyleAdjust
    },

    data() {
        return {
            form: {
                templateId: '',
                templateCtPkId: '',
                fileName: '',
                // fieldPairDocid: {html:'',res:''},
                // tableId:'',
                outParamFields: [{ fileName: '文件生成', docId: 'docId' }],
                inParamFields: []
            },
            parentData: {},
            fileTemplateList: [],
            fileModelVariables: [],
            fieldMap: [],
            rules: {
                templateId: [{ required: true, message: '文件模版不能为空!' }],
                fileName: [{ required: true, message: '文件名不能为空!' }],
                outParamFields: [{ required: true, message: '文件生成不能为空!' }]
            }
        };
    },
    mounted() {
        if (this.mode !== 'add') {
            if (this.dealInfo.dealConfJson) {
                this.form = JSON.parse(this.dealInfo.dealConfJson);
            }
        }
        this.initData();
        this.getFileTemplate();
    },
    methods: {
        async openDialog(id, content) {
            let dataType = this.$app.runCmd('get-blockdef-basicInfo-data').objId;
            this.id = `${id}-dlg`;
            this.$dialog.create({
                title: '属性字段',
                width: '900px',
                component: TextareaDialog,
                closeOnClickModal: false,
                args: { dataType: dataType, id: this.id, innerHtml: content.html, actionOk: this.setContent.bind(this) }
            });
        },
        async initData() {
            let resp = await this.$api.xcpInfoApi.executeApi('a087fff826cd334cc3006c6105ee70ea', {});
            let option = resp.data;
            if (option) {
                let code = this.$app.runCmd('get-blockdef-basicInfo-data').objId;
                option.forEach(item => {
                    if (item.datatype == code) {
                        this.modelId = item.id;
                        this.loadDetail(item.id);
                    }
                });
            }
        },
        async loadDetail(id) {
            let params = {
                apiCode: this.$api.XcpApiCode.queryDatatype(),
                body: {
                    id: id
                }
            };
            let resp = await this.$api.XcpModelApi.execute(params);
            if (resp.success && resp.data) {
                let linkList = resp.data.linkList;
                let fieldList = resp.data.fieldList;
                fieldList
                    .filter(item => item.isLink !== '1')
                    .map(v => {
                        let obj = {};
                        //寻找具有关联关系的节点
                        let link = linkList && linkList.length && linkList.find(e => e.srcField === v.field);
                        if (!link) {
                            obj.fieldName = v.fieldName;
                            obj.field = v.field;
                            obj.id = resp.data.id;
                            this.fieldMap.push(obj);
                        }
                    });
            }
        },

        async getFileTemplate() {
            let resp = await this.$api.blockApi.getFileTemplate();
            if (resp) {
                this.fileTemplateList = resp.data;
            }
            if (this.form.templateId) {
                let fileTemplateData = this.fileTemplateList.find(item => item.pkId === this.form.templateId);
                this.$set(this.parentData, 'clauseTypeLsPkId', fileTemplateData.ctPkId);
                this.$set(this.parentData, 'pkId', fileTemplateData.pkId);
                this.$set(this.parentData, 'modelName', fileTemplateData.modelName);
            }
        },
        templateChange() {
            this.form.templateCtPkId = this.fileTemplateList.find(item => item.pkId === this.form.templateId)?.ctPkId;
            let fileTemplateData = this.fileTemplateList.find(item => item.pkId === this.form.templateId);
            this.$set(this.parentData, 'clauseTypeLsPkId', fileTemplateData.ctPkId);
            this.$set(this.parentData, 'pkId', fileTemplateData.pkId);
            this.$set(this.parentData, 'modelName', fileTemplateData.modelName);
            this.getModelVariables(this.form.templateId);
        },
        async getModelVariables(templateId) {
            let resp = await this.$api.blockApi.getFileModelVariables(templateId);
            if (resp) {
                this.fileModelVariables = resp.data;
                if (this.fileModelVariables) {
                    const inParamFields = [];
                    this.fileModelVariables.forEach(item => {
                        inParamFields.push({ fileName: item.variableMsg, fieldCode: item.variableName });
                    });
                    this.form.inParamFields = inParamFields;
                }
            }
        },
        getData() {
            //组合基本信息中的getData中的数据
            let basicInfo = this.$app.runCmd('get-blockdef-basicInfo-data');
            let data = null;
            if (!this.form.templateId || !this.form.fileName || this.form.outParamFields.length == 0) {
                this.$msg.warning('请完善基础信息！');
                return;
            }
            data = { dealType: basicInfo.dealType, dealConfJson: JSON.stringify(this.form) };
            let blockParamInfos = [];
            this.form.outParamFields.forEach(row => {
                let item = row.docId;
                blockParamInfos.push({ fieldCode: item, fieldName: item, paramType: 'out', fieldType: 'String' });
            });
            this.form.inParamFields.forEach(row => {
                let item = row.fieldCode;
                blockParamInfos.push({ fieldCode: item, fieldName: item, paramType: 'in', fieldType: 'String' });
            });
            return { ...data, blockParamInfos: blockParamInfos };
        }
    }
};
</script>
<style scoped>
.block-def-dialog .file-generate >>> .el-form-item:not(:last-child) {
    width: 60%;
}
</style>
