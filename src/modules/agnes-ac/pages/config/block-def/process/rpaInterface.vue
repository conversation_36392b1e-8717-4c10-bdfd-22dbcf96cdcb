<template>
    <div>
        <el-form :model="form" ref="form" :rules="rules" label-width="150px">
            <el-form-item label="服务器及端口" class="content-flex post-url" prop="url">
                <gf-input v-model="form.url" :max-byte-len="1024" style="width: calc(42% - 30px);"></gf-input>
            </el-form-item>
            <gf-strbool-checkbox style="margin-left: 50px; margin-bottom: 10px;" v-model="form.startType">开启流程</gf-strbool-checkbox>
            <gf-strbool-checkbox style="margin-left: 50px; margin-bottom: 10px;" v-model="form.statusType">开启状态监控</gf-strbool-checkbox>
            <gf-strbool-checkbox @change="form.nodeParam = ''" style="margin-left: 50px; margin-bottom: 10px;" v-model="form.nodeType">开启节点监控</gf-strbool-checkbox>

            <div v-if="form.startType == '1'">
                <el-form-item label="开启流程请求参数" prop="startParam">
                    <el-input style="width: 500px;" type="textarea" v-model="form.startParam" :rows="5"></el-input>
                </el-form-item>
            </div>
            <div v-if="form.statusType == '1'">
                <el-form-item label="流程状态监控请求参数" prop="statusParam">
                    <el-input :rows="5" style="width: calc(39.5%); margin-right: 10px;" type="textarea" v-model="form.statusParam" :maxlength="128"></el-input>
                    <em class="question el-icon-question" style="top: 15px; right: 165px; font-size: 20px; color: #0f5eff; cursor: pointer;" title="操作文档" @click="openHelpFile"></em>
                </el-form-item>
            </div>
            <div v-if="form.nodeType == '1'">
                <el-form-item label="节点状态监控请求参数" prop="nodeParam">
                    <el-input style="width: 500px" type="textarea" v-model="form.nodeParam" :rows="5"></el-input>
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>
<script>
import readMeStr from './readMeStr.js';
export default {
  name: "rpaInterface",
  props: {
    mode: {
      type: String,
      default: 'add'
    },
    dealInfo: {
      type: Object,
    },
  },
  data() {
    //判断是否为Json格式且不为空 {}
    const checkJson = (rule, value, callback) => {
      let isJson = true;
      let obj = null;
      try {
        obj = JSON.parse(value);
      } catch (e) {
        isJson = false;
      }
      let toString = Object.prototype.toString;
      let isArray = isJson && toString.call(obj) === '[object Array]'
      if (!value) {
        callback(new Error("输入参数不为空"));
      } else if(!isArray){
        callback(new Error("输入参数格式不符合要求"));
      } else {
        callback();
      }
    };
    return {
      objInfo: [],
      form: {
        param1: '',
        url: '',
        startType: '',
        statusType: '',
        nodeType: '',
        startParam: '',
        statusParam: '',
        nodeParam: ''

      },
      isNoEdit: false,
      okTitle: '保存',
      rules: {
        'url': [{required: true, message: "请填写服务器和端口地址!"}],
        'startParam': [{required: true, validator: checkJson, trigger: 'blur'}],
        'statusParam': [{required: true, validator: checkJson, trigger: 'blur'}],
        'nodeParam': [{required: true, validator: checkJson, trigger: 'blur'}],
      },
      index: null,
    }
  },
  computed: {
    blockObjId() {
      return this.$app.$root.$store.state.publicData.blockObjId
    }
  },
  mounted() {
    if(this.mode!=='add') {
      if (this.dealInfo.dealConfJson) {
        this.form = JSON.parse(this.dealInfo.dealConfJson);
      }
    }


  },
  methods: {
    openHelpFile() {
      if (this.helpNotify && this.helpNotify.close) {
        this.helpNotify.close();
      }
      this.helpNotify = this.$notify({
        width: 400,
        title: '流程状态监控请求参数说明',
        customClass: 'cronHelpNotify',
        dangerouslyUseHTMLString: true,
        duration: 0,
        message: readMeStr()
      });
    },
    async getData() {
      let ok = await this.$refs.form.validate();
      let basicInfo = this.$app.runCmd("get-blockdef-basicInfo-data")
      if(ok) {
        //组装数据
        if(this.form.startType === '0') this.form.startParam = ''
        if(this.form.statusType === '0') this.form.statusParam = ''
        if(this.form.nodeType === '0') this.form.nodeParam = ''
        return {dealType:basicInfo.dealType,dealConfJson:JSON.stringify(this.form)}
      }
    },
  }
}

</script>
