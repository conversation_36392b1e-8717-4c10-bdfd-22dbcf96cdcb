<template>
    <div class="case-scene">
        <new-complex-widget ref="complexWidget" compTitle="积木管理" :fileOption="fileOption" :defalutMenuFold="false" :compType="['file']"></new-complex-widget>
        <div class="case-scene-right">
            <gf-search-box ref="box" title="筛选条件" :is-show-select="false" v-show="false">
                <template slot="content">
                    <gf-form ref="gfForm" label-position="right" label-width="100px" :form-items="formItems" :mode="'search'"></gf-form>
                </template>
            </gf-search-box>
            <gf-grid :filterRemote="false" grid-no="agnes-block-def" ref="grid" height="100%" @selected-changed="selectedChanged" :query-args="queryArgs">
                <template slot="left">
                    <gf-button class="action-btn" @click="preAddBlock" size="mini">
                        新增
                    </gf-button>
                    <gf-button class="action-btn" :disabled="!selectOne" @click="copyBlock" size="mini">
                        复制
                    </gf-button>
                    <gf-button class="action-btn" @click="deleteBlock" size="mini">
                        删除
                    </gf-button>
                    <gf-button class="action-btn" @click="approveBlock" size="mini">
                        审核
                    </gf-button>
                    <gf-button class="action-btn" @click="publishBlock" size="mini">
                        发布
                    </gf-button>
                    <gf-button class="action-btn" :disabled="selectRows" @click="exportData" size="mini">
                        导出
                    </gf-button>
                    <gf-button class="action-btn" @click="importData" size="mini">
                        导入
                    </gf-button>
                </template>
            </gf-grid>
        </div>
    </div>
</template>

<script>
import caseSceneDrawer from './block-drawer'
import addBlockDialog from './add-block-dialog'
import addBlockTarget from './add-block-target'
import testDialog from './test-dialog.vue';
import leadIntoList from "./lead-into-list.vue";
import axios from "axios";

export default {
    name: 'index',
    data() {
        return {
            selectRows: true,
            blockType: '',
            queryArgs: {
                blockName: '',
                blockType: '',
                status: ''
            },
            selectOne: false,
            fileOption: [
                {
                    treeData: [],
                    defaultActions: {
                        'node-click': this.nodeClick
                    }
                }
            ],
            formItems: this.$formUtils.formatFormItems([
                { key: 'blockName', type: 'input', name: '名称:', value: '', props: { clearable: true } },
                { key: 'blockType', type: 'dict', name: '类型:', value: '', dict: 'AGNES_BLOCK_TYPE', props: { clearable: true } },
                { key: 'status', type: 'dict', name: '状态:', value: '', dict: 'AGNES_RELEASE_STATUS', props: { clearable: true } },
                {
                    type: 'children',
                    childItems: [
                        {
                            type: 'button',
                            action: 'reset',
                            name: '重置',
                            click: () => {
                                this.reSetSearch();
                            }
                        },
                        { type: 'button', action: 'open' },
                        {
                            type: 'button',
                            action: 'search',
                            name: '查询',
                            click: () => {
                                this.reloadData();
                            }
                        },
                    ]
                }
            ]),
            maintainFileData: {
                dictTypeId: 'AGNES_BLOCK_TYPE',
                title: '积木'
            }
        };
    },
    beforeMount() {
        this.initTreeData();
    },
    computed: {
        iconTypeArr() {
            return this.$app.$root.$store.state.publicData.iconTypeArr;
        },
        stepTypeOption() {
            return this.$app.$root.$store.state.publicData.stepTypeOption;
        }
    },
    methods: {
        //树点击事件
        nodeClick(data) {
            this.blockType = '';
            this.queryArgs.blockType = '';
            this.formItems.setValue('blockType', '');
            if (data.id != 'root') {
                this.queryArgs.blockType = data.id;
                this.formItems.setValue('blockType', data.id);
                this.blockType = data.id;
            }
            this.queryArgs.pkId = '';
            this.$refs.grid.reloadData(true);
        },
        reloadData() {
            //取搜索条件的值
            this.queryArgs = this.formItems.getValues();
            this.$nextTick(() => {
                this.$refs.grid.reloadData(true);
            });
        },
        reSetSearch() {
            this.formItems.resetValues();
            this.reloadData();
        },
        editRoback() {
            this.reloadData();
            this.initTreeData();
        },
        //修改
        edit(param) {
            this.showDrawer('edit', param.data, this.editRoback.bind(this));
        },
        preAddBlock() {
            if (!this.blockType) {
                this.$msg.msg('请在左侧选择积木块类型!', 'warning');
                return;
            }
            if(this.blockType === '09'){
                this.$nav.showDialog(
                    addBlockTarget,
                    {
                        args: {actionOk: this.reloadData.bind(this),blockType: this.blockType,mode:'add'},
                        width: '80%',
                        title: this.$dialog.formatTitle(this.$app.dict.getDictName('AGNES_BLOCK_TYPE', this.blockType),"add"),
                    }
                )
            }else{
                this.$nav.showDialog(
                    addBlockDialog,
                    {
                        args: {actionOk: this.addBlock.bind(this),blockType: this.blockType},
                        width: '530px',
                        title: this.$dialog.formatTitle(this.$app.dict.getDictName('AGNES_BLOCK_TYPE', this.blockType),"add"),
                    }
                );
            }
        },
        async addBlock(data) {
            let blockCode = await this.getBLockCode();
            let req = {
                ...data,
                blockCode,
                blockType: this.blockType
            };
            req.status = '00';
            let pkId = '';
            const res = await this.$api.blockApi.preSaveBlock(req);
            if (res.data) {
                pkId = res.data.pkId;
            }
            this.showDrawer('add', { blockType: this.blockType, ...data, pkId, blockCode }, this.editRoback.bind(this));
        },
        async getBLockCode() {
            let code = '';
            const resp = await this.$api.codeGeneratorApi.getCode('block');
            if (resp && resp.data) {
                code = resp.data;
            }
            return code || '';
        },
        async initTreeData() {
            try {
                const p = this.$api.blockApi.getTreeNode({});
                let resp = await this.$app.blockingApp(p);
                if (resp.data.children && resp.data.children.length) {
                    resp.data.type = '';
                    resp.data.children.forEach(item => {
                        item.children = [];
                        item.type = '';
                        item.icon = this.stepTypeOption.find(items => items.id === item.id)?.icon;
                    });
                }
                this.fileOption[0].treeData = [resp.data];
                this.$nextTick(() => {
                    this.$refs.complexWidget.changeExpandedLevel(1);
                });
            } catch (e) {
                this.$message.error(e.data || 'Error');
            }
        },
        async deleteBlock() {
            let rows = this.$refs.grid.getSelectedRows();
            if (rows.length < 1) {
                this.$msg.warning('请选择一条记录!');
                return;
            }
            const ok = await this.$msg.ask(`确认删除吗, 是否继续?`);
            if (!ok) {
                return;
            }
            let ids = [];
            rows.forEach(item => {
                ids.push(item.pkId);
            });
            try {
                const p = this.$api.blockApi.deleteBlock({'ids':ids});
                let resp = await this.$app.blockingApp(p);
                if(resp && resp.code && resp.code=='error'){
                  this.$msg.error(resp.message)
                  this.reloadData();
                  return ;
                }
                this.$msg.success('删除成功！');
                this.editRoback();
            } catch (e) {
                this.$msg.error(e);
            }
        },
        async approveBlock() {
            let rows = this.$refs.grid.getSelectedRows();
            if (rows.length < 1) {
                this.$msg.warning('请选择一条记录!');
                return;
            }
            let checkInfo = {};
            let checkNum = 0;
            let ids = [];
            let id_s = [];
            rows.forEach(item => {
                if (item.status == '01') {
                    ids.push(item.pkId);
                    checkInfo = item;
                    checkNum = checkNum + 1;
                }
                if (item.status != '01') {
                    id_s.push(item.pkId);
                }
            });
            if (id_s.length > 0) {
            this.$msg.warning('请仅选择待复核的记录!');
            return;
            }
            if (checkNum == 1) {
                let customOpBtn = [
                    { title: '取消', action: 'onCancel' },
                    { title: '审核', className: 'primary', action: 'onApprove' },
                ];
                this.openDrawer('approve', checkInfo, this.editRoback.bind(this), customOpBtn);
            } else {
                if (ids.length < 1) {
                    this.$msg.warning('请选择需要审核的数据!');
                    return;
                }
                try {
                    const p = this.$api.blockApi.approveBlock({ ids: ids, status: '02' });
                    let resp = await this.$app.blockingApp(p);
                    if (resp && resp.code && resp.code == 'error') {
                        this.$msg.error(resp.message);
                        return;
                    }
                    this.$msg.success('批量审核成功！');
                    this.editRoback();
                } catch (e) {
                    this.$msg.error(e);
                }
            }
        },
        async publishBlock() {
            let rows = this.$refs.grid.getSelectedRows();
            if (rows.length < 1) {
                this.$msg.warning('请选择一条记录!');
                return;
            }
            let ids = [];
            let id_s = [];
            rows.forEach(item => {
                if (item.status == '02') {
                    ids.push(item.pkId);
                }
                if (item.status != '02') {
                    id_s.push(item.pkId);
                }
            });
            if (id_s.length > 0) {
                this.$msg.warning('请仅选择待发布的记录!');
                return;
            }
            const ok = await this.$msg.ask(`确认发布吗, 是否继续?`);
            if (!ok) {
                return;
            }
            try {
                const p = this.$api.blockApi.publishBlock({ ids: ids, status: '03' });
                await this.$app.blockingApp(p);
                this.$msg.success('发布成功！');
                this.reloadData();
            } catch (e) {
                this.$msg.error(e);
            }
        },
        selectedChanged() {
            let rows = this.$refs.grid.getSelectedRows();
            this.selectOne = rows.length === 1;
            this.selectRows = rows.length <= 0;
        },
        copyBlock() {
            let rows = this.$lodash.cloneDeep(this.$refs.grid.getSelectedRows());
            if (rows.length != 1) {
                this.$msg.warning('请选择一条记录!');
                return;
            }
            let data = rows[0];
            data.blockName = data.blockName + '(copy)';
            this.showDrawer('copy', data, this.editRoback.bind(this));
        },
        showDrawer(mode, row, actionOk) {
          console.log("-----"+JSON.stringify(row))
            let customOpBtn = [
                { title: '取消', action: 'onCancel' },
                { title: '保存', className: 'primary', action: 'onSaveAndAdd' }
            ];
            this.openDrawer(mode, row, actionOk, customOpBtn);
        },
        openDrawer(mode, row, actionOk,customOpBtn){
            let data = this.$lodash.cloneDeep(row);
            let title = this.formatTitle(mode)
            if(data.blockType === '09' ){
                this.$nav.showDialog(
                    addBlockTarget,
                    {
                        args: {actionOk: this.reloadData.bind(this),blockType: data.blockType,mode:mode,row:data},
                        width: '80%',
                        title: `${this.$app.dict.getDictName('AGNES_BLOCK_TYPE', data.blockType)}-${title}`,
                    }
                )
            }else{
                this.$nav.showDialog(
                    caseSceneDrawer,
                    {
                        args: {row: data, mode, actionOk, customOpBtn},
                        width: '95%',
                        className: 'large-dialog'
                    }
                )
            }

        },
        formatTitle(mode){
            if(mode === 'add'){
                return '新增'
            }else if(mode === 'edit'){
                return '编辑'
            }else if(mode === 'copy'){
                return '复制'
            }else if(mode === 'approve'){
                return '审核'
            }
        },
        async test(params){
            let result = {
                blockCode: params.data.blockCode
            }
            let resp = await this.$api.dataSourceApi.querySqlBlock(result);
            let form,ruleData;
            let testData = [];
            if(resp.success){
                form = {...resp.data};
                testData = [];
                form.intPutBlockParamInfos.forEach((item)=>{
                    let tempData = {
                    fieldCode: item.fieldCode,
                    fieldName: item.fieldName,
                    fieldType: item.fieldType ,
                    inSrc: item.inSrc,
                    linkItem: item.linkItem,
                    dictList: item.linkItem ? this.$app.dict.getDictItems(item.linkItem) : [],
                    inputValue:'',
                    }
                    testData.push(tempData)
                })
                ruleData = {
                    exceptionReBlockResultConfVos:form.exceptionConf,
                    warnReBlockResultConfVos:form.warnConf
                }

            }
            this.$nav.showDialog(
                testDialog,
                {
                    args: {
                        form:form,
                        testData:testData,
                        loadColumsList:form.outPutBlockParamInfos,
                        ruleData:ruleData,
                        outPutBlockParamInfos:form.outPutBlockParamInfos,
                        intPutBlockParamInfos:form.intPutBlockParamInfos
                    },
                    width: '60%',
                    title: `${this.$app.dict.getDictName('AGNES_BLOCK_TYPE', this.blockType)}-测试`,
                }
            )

        },
        async exportData(){
          let rows = this.$refs.grid.getSelectedRows();
          let blockCodes = rows.map(row => row.blockCode);
          const ok = await this.$msg.ask(`确认导出吗, 是否继续?`);
          if (ok) {
            axios({
              method: "post",
              url: "api/agnes-ac/v1/ac/taskdef/blockExport",
              data: blockCodes,
              responseType: "blob" // 确保返回的是二进制数据
            }).then(res => {
              // 创建 Blob 对象，指定 MIME 类型为 application/zip
              let blob = new Blob([res.data], { type: "application/zip" });
              // 提取文件名
              let fileName = 'exported_file.zip'; // 默认文件名
              if (res.headers['content-disposition']) {
                const match = res.headers['content-disposition'].match(/filename="(.+)"/);
                if (match && match[1]) {
                  fileName = decodeURI(match[1]); // 提取后端返回的文件名
                }
              }
              // 创建 <a> 标签并触发下载
              const link = document.createElement("a");
              link.style.display = "none";
              link.href = URL.createObjectURL(blob); // 创建临时 URL
              link.setAttribute("download", fileName); // 设置下载文件名
              document.body.appendChild(link);
              link.click();
              // 清理
              document.body.removeChild(link);
              setTimeout(() => window.URL.revokeObjectURL(link.href), 200); // 释放临时 URL
            }).catch(() => {
              this.$message.error("导出失败")
            });
          }
        },
        importData(){
          this.$nav.showDialog(
              leadIntoList,
              {
                width: '40%',
                title: '积木导入',
                args: {
                  actionOk: this.reloadData.bind(this)
                }
              }
          )
        }
    }
};
</script>

<style scoped>
.case-scene {
    display: flex;
    flex-direction: row !important;
}

.case-scene .ag-grid-box {
    padding-left: 8px;
}
.case-scene-right {
    flex: 1;
    display: flex;
    flex-direction: column;
}
.complex-widget {
    width: auto;
}
</style>
