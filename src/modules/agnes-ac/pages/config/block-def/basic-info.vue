<template>
    <div>
        <el-form ref="form"
                 class="case-scene-form"
                 :model="form"
                 :rules="rules"
                 label-width="80px"
                 :disabled="mode=='approve' || mode=='view'">
            <div class="line">
                <el-form-item label="积木类型:" prop="blockType">
                    <gf-dict-select dict-type="AGNES_BLOCK_TYPE"   :disabled=true
                                    v-model="form.blockType" />
                </el-form-item>
            </div>
            <div class="line">
                <el-form-item label="处理类型:" prop="dealType">
                    <el-select style="width: 100%" class="multiple-select" v-model="form.dealType"  @change="dealTyepChange"
                               clearable filterable
                               placeholder="请选择">
                        <gf-filter-option
                            v-for="item in dealTypeOption"
                            :key="item.dictId"
                            :label="item.dictName"
                            :value="item.dictId">
                        </gf-filter-option>
                    </el-select>
                </el-form-item>
            </div>
            <div class="line">
                <el-form-item class="encoding-rules" label="编码规则:" prop="blockCode">
                    <gf-input v-model="form.blockCode" :max-byte-len="8"  :disabled="!form.isEditCode">
                        <template slot="prepend">block-</template>
                    </gf-input>
                    <el-checkbox class="show-selected"  v-model="form.isEditCode" style="margin-left: 15px">是否自定义</el-checkbox>
                </el-form-item>
            </div>
            <div class="line" >
            <el-form-item label="执行人:" >
                  <gf-person-chosen ref="memberRef"
                                    :disabled="mode==='view'"
                                    :memberRefList="this.memberRefList"
                                    chosenType="user, group"
                                    @getMemberList="getMemberList"
                                    style="width: 100%">
                  </gf-person-chosen>
            </el-form-item>
            </div>

            <div class="line">
                <el-form-item label="描述:" prop="blockDesc">
                    <gf-input type="textarea" rows="6" v-model="form.blockDesc" :max-byte-len="100"></gf-input>
                </el-form-item>
            </div>
        </el-form>
    </div>
</template>

<script>
export default {
  name: "basic-info",
  props: {
    basicInfo: {
      type: Object,
    },
    mode: {
      type: String,
    },
  },
  computed: {
    nameDisplay() {
      if (this.form.blockName && this.form.blockCode) {
        return this.form.blockName + "（block-" + this.form.blockCode + ")";
      } else if (this.form.blockName && !this.form.blockCode) {
        return this.form.blockName;
      } else if (!this.form.blockName && this.form.blockCode) {
        return "（block-" + this.form.blockCode + ")";
      } else {
        return "";
      }
    },
  },
  data() {
    const checkencodingRule = (rule, value, callback) => {
      let numberReg = /^\d+(\.\d+)?$/;
      if (value === "") {
        callback(new Error("编码规则不能为空"));
      } else if (!numberReg.test(value)) {
        callback(new Error("编码规则位数字"));
      } else if (value.length != 8) {
        callback(new Error("编码规则为8位"));
      } else {
        callback();
      }
    };
    return {
      blockTypeOps: [],
      objOption: [],
      dealTypeOption: [],
      objPageOption: [],
      adqOption: [
        { value: "01", label: "接口采集" },
        { value: "02", label: "etl采集" },
        { value: "03", label: "渠道采集" },
        { value: "04", label: "rpa采集" },
      ],
      dataCacuOption: [
        { value: "05", label: "高级计算" },
        { value: "06", label: "简单计算" },
      ],
      fileOption: [{ value: "07", label: "文件处理" }],
      followOption: [{ value: "08", label: "流程处理" }],
      usitOption: [
        { value: "09", label: "接口推送" },
        { value: "10", label: "消息推送" },
        { value: "11", label: "RPA任务推送" },
      ],
      motOption: [{ value: "12", label: "人工任务" }],
      memberRefList: [], // 执行人列表
      form: {
        blockName: "",
        blockType: "",
        objId: "",
        pageId: "",
        isEditCode: "",
        blockCode: "",
        blockOwner: "", //执行人
        blockDesc: "",
      },
      nameDisplayOption: [{ dictId: "name", dictName: "名称(编码)" }],
      rules: {
        blockCode: [{ required: true, validator: checkencodingRule, trigger: "blur" }],
        dealType: [{ required: true, message: "请选择执行类型!" }],
      },
    };
  },

  beforeMount() {
    this.blockTypeOps = this.$app.dict.getDictItems("AGNES_BLOCK_TYPE");
  },
  mounted() {
    this.initData();
    this.initCode();
    this.$nextTick(() => {
      if (this.basicInfo) {
        this.form = this.$lodash.cloneDeep(Object.assign(this.form, this.basicInfo));
        this.dealTyepChange(this.form.dealType);
        if (this.form.objId) {
          this.getObjPage();
        }
      }
      if (this.form.blockType == "01") {
        this.dealTypeOption = this.$app.dict.getDictItems("AGNES_BLOCK_ADQ_TYPE");
      } else if (this.form.blockType == "02") {
        this.dealTypeOption = this.$app.dict.getDictItems("AGNES_BLOCK_DATA_CACU_TYPE");
      } else if (this.form.blockType == "03") {
        this.dealTypeOption = this.$app.dict.getDictItems("AGNES_BLOCK_FILE_TYPE");
      } else if (this.form.blockType == "04") {
        this.dealTypeOption = this.$app.dict.getDictItems("AGNES_BLOCK_FOLLOW_TYPE");
      } else if (this.form.blockType == "06") {
        this.dealTypeOption = this.$app.dict.getDictItems("AGNES_BLOCK_USIT_TYPE");
      } else if (this.form.blockType == "07") {
        this.dealTypeOption = this.$app.dict.getDictItems("AGNES_BLOCK_CHACK_LIST_TYPE");
      }
      if (this.form.blockOwner) {
        this.memberRefList = JSON.parse(this.form.blockOwner);
        this.$refs.memberRef.initChosenData(this.memberRefList);
      }
    });
    //使积木块组件保存时能够实时组合封装基本信息数据
    this.$app.registerCmd("get-blockdef-basicInfo-data", () => {
      return this.getData();
    });
  },
  methods: {
    async initData() {
      try {
        const p = this.$api.blockApi.queryObj();
        let resp = await this.$app.blockingApp(p);
        this.objOption = resp.data;
      } catch (e) {
        this.$msg.error(e);
      }
    },
    getMemberList(val) {
      this.memberRefList = val;
      this.form.blockOwner = JSON.stringify(val);
    },
    async initCode() {
      if (this.mode == "copy") {
        const resp = await this.$api.codeGeneratorApi.getCode("block");
        if (resp && resp.data) {
          this.form.blockCode = resp.data.split("-")[1];
        }
      }
    },
    async getObjPage() {
      try {
        const p = this.$api.xcpInfoApi.getXcpModelPageList({});
        let resp = await this.$app.blockingApp(p);
        this.objPageOption = resp.data;
      } catch (e) {
        this.$msg.error(e);
      }
    },
    dealTyepChange(value) {
      try {
        this.$emit("change-deal-type", value);
      } catch (e) {
        return true;
      }
    },
    //保存时拿数据
    getData() {
      let data = null;
      this.$refs["form"].validate((validate) => {
        if (!validate) {
          data = false;
        } else {
          data = this.form;
        }
      });
      return data;
    },
    changeObjId(value) {
      this.$app.$root.$store.commit("changeBlockObjId", value);
      this.$app.$emit("changeObjId", value);
    },
    showView(menuId) {
      if (menuId) {
        let clientView = this.$app.views.getView(menuId);
        let clientTabView = Object.assign({ args: {}, id: menuId }, clientView);
        this.$nav.showView(clientTabView);
      }
    },
  },
};
</script>

<style scoped>
.case-scene-form {
  padding: 0;
  width: 100%;
}

>>> .case-scene-form .el-form-item__content {
  display: flex;
  align-items: center;
  justify-content: center;
}

>>> .case-scene-form .el-form-item__content .form-icon-edit {
  margin-left: 10px;
  font-size: 20px;
  cursor: pointer;
  color: #9090ff;
}

>>> .el-form .encoding-rules.el-form-item .el-form-item__content input {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

>>> .el-form .el-form-item .el-form-item__content .el-input-group__prepend {
  border-color: #a8aed3;
}
.line >>> .el-form-item {
  width: 80% !important;
}
</style>
