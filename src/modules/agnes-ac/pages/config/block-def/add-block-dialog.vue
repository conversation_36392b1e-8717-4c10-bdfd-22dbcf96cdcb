<template>
  <div>
    <el-form ref="form"
             :model="form"
             class="pre-add-block"
             :rules="rules"
             label-width="90px">
      <el-form-item label="积木名称:" prop="blockName">
        <gf-input v-model="form.blockName" :max-byte-len="10"></gf-input>
      </el-form-item>
      <el-form-item label="处理类型:" prop="dealType">
        <el-select style="width: 100%" class="multiple-select" v-model="form.dealType"
                   clearable filterable
                   placeholder="请选择">
          <gf-filter-option
              v-for="item in dealTypeOption"
              :key="item.dictId"
              :label="item.dictName"
              :value="item.dictId">
          </gf-filter-option>
        </el-select>
      </el-form-item>
    </el-form>
    <dialog-footer :on-cancel="onCancel" :on-save="onSave" okButtonTitle="下一步"></dialog-footer>
  </div>
</template>

<script>
export default {
  name: "add-obj-dialog",
  props: {
    actionOk: Function,
    blockType: String,
  },
  data() {
    return {
      form: {
        blockName: '',
        dealType: '',
      },
      rules: {
        blockName: {required: true, message: '请输入名称', trigger: 'change'},
        dealType: {required: true, message: '请选择处理类型', trigger: 'blur'},
      },
      dealTypeOption: [],
    }
  },
  mounted() {
    if (this.blockType === '01') {
      this.dealTypeOption = this.$app.dict.getDictItems('AGNES_BLOCK_ADQ_TYPE');
    } else if (this.blockType === '02') {
      this.dealTypeOption = this.$app.dict.getDictItems('AGNES_BLOCK_DATA_CACU_TYPE');
    } else if (this.blockType === '03') {
      this.dealTypeOption = this.$app.dict.getDictItems('AGNES_BLOCK_FILE_TYPE');
    } else if (this.blockType === '04') {
      this.dealTypeOption = this.$app.dict.getDictItems('AGNES_BLOCK_FOLLOW_TYPE');
    } else if (this.blockType === '06') {
      this.dealTypeOption = this.$app.dict.getDictItems('AGNES_BLOCK_USIT_TYPE');
    } else if (this.blockType === '07') {
      this.dealTypeOption = this.$app.dict.getDictItems('AGNES_BLOCK_CHACK_LIST_TYPE');
    }
  },
  methods: {
    onCancel() {
      this.$dialog.close(this, 'cancel');
    },
    async onSave() {
        const ok = await this.$refs.form.validate();
        if (ok) {
          this.onCancel()
          await this.actionOk(this.form)
        }
    },
  }
}
</script>

<style scoped>
.dialog-footer-bar >>> .el-button {
  width: 80px;
}
.pre-add-block {
  padding: 0 5px;
}
</style>
