<template>
    <el-form
        ref="form"
        class="work-process-operating-params-form"
        :model="caseStepDef"
        :rules="rules"
        :disabled="optionType==='view'"
        label-width="100px">
        <el-container>
            <el-aside class="tab-left" width="100px" height = "100%">
                <el-tabs ref="tabs" :tab-position="'left'" class="describe-tabs" @tab-click="handleTabClick">
                    <el-tab-pane label="常规设置"></el-tab-pane>
                    <el-tab-pane label="生命周期"></el-tab-pane>
                    <el-tab-pane label="参数控制"></el-tab-pane>
                    <el-tab-pane label="出参配置"></el-tab-pane>
                    <el-tab-pane label="消息提醒"></el-tab-pane>
                    <el-tab-pane label="情景知识"></el-tab-pane>
                </el-tabs>
            </el-aside>
            <el-main>
                <div class="box-right" ref="boxRight">       
                    <div class="main-right" index="0">
                        <h3>常规设置</h3>
                        <div class="line">
                            <el-form-item label="步骤编码:" prop="encodingRule" class="content-flex encoding-rules">
                                <gf-input v-model="caseStepDef.encodingRule" clear-regex="[^0-9]" :max-byte-len="8" placeholder="请填写编码" :disabled="!isShowCode">
                                    <template slot="prepend">step-</template>
                                </gf-input>
                                <el-checkbox style="margin-left: 10px;" v-model="isShowCode">是否自定义编码</el-checkbox>
                            </el-form-item>
                        </div>
                        <div class="line">
                            <el-form-item label="任务等级:" class="task-rate">
                                <el-rate v-model="caseStepDef.stepLevel"
                                        :max="3"
                                        show-text
                                        :texts="['普通', '重要', '非常重要']"
                                        :colors="{1: {value: '#409EFF'}, 2: {value: '#E6A23C'}, 3: {value: '#F00'}}"
                                >
                                </el-rate>
                                <em class="el-icon-refresh-left" @click="caseStepDef.stepLevel = 0" v-show="true"></em>
                            </el-form-item>
                        </div>
                        <div class="line">
                            <el-form-item label="业务标签:" prop="stepTag">
                                <el-select class="multiple-select" v-model="caseStepDef.stepTag"
                                        multiple filterable clearable
                                        default-first-option placeholder="请选择">
                                    <gf-filter-option
                                        v-for="item in bizTagOption"
                                        :key="item.dictId"
                                        :label="item.dictName"
                                        :value="item.dictId">
                                    </gf-filter-option>
                                </el-select>
                            </el-form-item>
                        </div>
                        <el-form-item label="执行人:" prop="stepActOwner">
                            <gf-person-chosen ref="memberRef"
                                            :disabled="false"
                                            :memberRefList="this.memberRefList"
                                            chosenType="user, group, roster"
                                            :rosterDate="this.rosterDate"
                                            @getMemberList="getMemberList">
                            </gf-person-chosen>
                        </el-form-item>
                        <el-form-item label="协作人:" prop="stepCollaborateOwner">
                            <gf-person-chosen ref="collaborateMemberRef"
                                            :disabled="false"
                                            :memberRefList="this.collaborateRefList"
                                            chosenType="user, group"
                                            @getMemberList="getCollaborateMemberList">
                            </gf-person-chosen>
                        </el-form-item>
                        <div class="line">
                            <el-form-item label="任务说明:" prop="stepRemark">
                                <gf-input :rows="6" v-model="caseStepDef.stepRemark" type="textarea" :max-byte-len="50"></gf-input>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="main-right" index="1">
                        <h3>生命周期</h3>
                        <el-form-item label="基准日类型:">
                            <el-radio-group v-model="stepFormInfo.caseStepDef.baseDateType">
                                <el-radio v-for="baseDateType in baseDateTypeOp"
                                        :key="baseDateType.value"
                                        :label="baseDateType.value">
                                    {{ baseDateType.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="启动:" prop="startStepRuleChecked" style="width: 100%">
                            <el-radio-group v-model="startStepRuleChecked">
                                <el-radio v-for="startType in startTypeOp"
                                        :key="startType.value"
                                        :label="startType.value">
                                    {{ startType.label }}
                                </el-radio>
                            </el-radio-group>
                            <div class="line none-shrink">
                                <el-time-picker
                                    v-model="caseStepDef.startTime"
                                    :picker-options=startTimeForDay
                                    placeholder="执行开始时间"
                                    value-format="HH:mm" @change="timeChange" style="width: 90%">
                                </el-time-picker>
                                <gf-strbool-checkbox v-model="startDayChecked" style="margin-left: 6px;">高级
                                </gf-strbool-checkbox>
                            </div>
                            <div class="line none-shrink" v-show="startDayChecked === '1'" style="width: 90%">
                                <el-select v-model="caseStepDef.startBaseDay" style="width: 35%">
                                    <gf-filter-option
                                        v-for="item in baseDateParamList"
                                        :key="item.paramKey"
                                        :label="item.paramName"
                                        :value="item.paramKey">
                                    </gf-filter-option>
                                </el-select>
                                <el-select v-model="caseStepDef.startDayType" style="width: 30%">
                                    <gf-filter-option
                                        v-for="item in acrosDayTypes"
                                        :key="item.paramKey"
                                        :label="item.paramName"
                                        :value="item.paramKey">
                                    </gf-filter-option>
                                </el-select>
                                <el-select v-model="caseStepDef.startCrossDayType" style="width: 30%">
                                    <gf-filter-option
                                        v-for="item in crossDayTypes"
                                        :key="item.paramKey"
                                        :label="item.paramName"
                                        :value="item.paramKey">
                                    </gf-filter-option>
                                </el-select>
                                <el-input v-model.number="caseStepDef.startDay"
                                          style="width: 20%;" :readonly="startDayDisabled" placeholder="请输入天数"></el-input>
                                <span style="margin-right: 2px">天</span>


                            </div>
                        </el-form-item>
                        <el-form-item v-if="startStepRuleChecked === '1'" label-width="0">
                            <rule-table tableHeight="100" :isDisable="false" ref="activeRuleTable" confType="fn,step,event,time,date"
                                        :ruleTableData="stepFormInfo.activeRuleTableData"
                                        :ruleTargetOp="ruleTargetOp"></rule-table>
                        </el-form-item>
                        <el-form-item label="结束:" prop="timeoutRuleChecked" style="width: 100%">
                            <el-radio-group v-model="timeoutRuleChecked">
                                <el-radio v-for="endType in endTypeOp"
                                        :key="endType.value"
                                        :label="endType.value">
                                    {{ endType.label }}
                                </el-radio>
                            </el-radio-group>
                        <div class="line none-shrink">
                            <el-time-picker
                                v-model="caseStepDef.endTime"
                                :picker-options=endTimeForDay
                                placeholder="执行结束时间"
                                value-format="HH:mm" @change="timeChange" style="width: 90%">
                            </el-time-picker>
                            <gf-strbool-checkbox v-model="endDayChecked" style="margin-left: 6px;">高级</gf-strbool-checkbox>
                        </div>
                        <div class="line none-shrink" v-show="endDayChecked === '1'" style="width: 90%">
                            <el-select v-model="caseStepDef.endBaseDay" style="width: 35%">
                                <gf-filter-option
                                    v-for="item in baseDateParamList"
                                    :key="item.paramKey"
                                    :label="item.paramName"
                                    :value="item.paramKey">
                                </gf-filter-option>
                            </el-select>
                            <el-select v-model="caseStepDef.endDayType" style="width: 30%">
                                <gf-filter-option
                                    v-for="item in acrosDayTypes"
                                    :key="item.paramKey"
                                    :label="item.paramName"
                                    :value="item.paramKey">
                                </gf-filter-option>
                            </el-select>
                            <el-select v-model="caseStepDef.endCrossDayType" style="width: 30%">
                                <gf-filter-option
                                    v-for="item in crossDayTypes"
                                    :key="item.paramKey"
                                    :label="item.paramName"
                                    :value="item.paramKey">
                                </gf-filter-option>
                            </el-select>
                            <el-input v-model.number="caseStepDef.endDay"
                                      style="width: 20%" :readonly="endDayDisabled" placeholder="请输入天数"></el-input>
                            <span style="margin-right: 2px">天</span>

                        </div>
                        </el-form-item>
                        <el-form-item v-if="timeoutRuleChecked === '1'" label-width="0">
                            <rule-table tableHeight="100" :isDisable="false" ref="timeoutRuleTable" confType="fn,step,event,time,date"
                                        :ruleTableData="stepFormInfo.timeoutRuleTableData" :ruleTargetOp="ruleTargetOp"></rule-table>
                        </el-form-item>
                        <el-form-item label="完成:">
                            <el-radio-group v-model="succeedRule">
                                <el-radio v-for="ruleType in ruleTypeOp"
                                        :key="ruleType.value"
                                        :label="ruleType.value">
                                    {{ ruleType.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="succeedRule === '1'" label-width="0">
                            <rule-table tableHeight="100" :isDisable="false" ref="successRuleTable" confType="fn,step,event"
                                        :ruleTableData="stepFormInfo.successRuleTableData"
                                        :ruleTargetOp="ruleTargetOp"></rule-table>
                        </el-form-item>
                        <el-form-item label="异常:">
                            <el-radio-group v-model="abnormalRule">
                                <el-radio v-for="ruleType in ruleTypeOp"
                                        :key="ruleType.value"
                                        :label="ruleType.value">
                                    {{ ruleType.label }}
                                </el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="abnormalRule === '1'" label-width="0">
                            <rule-table tableHeight="100" :isDisable="false" ref="failRuleTable"
                                        confType="fn,step,event" :ruleTableData="stepFormInfo.failRuleTableData"
                                        :ruleTargetOp="ruleTargetOp"></rule-table>
                        </el-form-item>
                    </div>
                    <div class="main-right" index="2">
                        <h3>参数控制</h3>
                        <el-form-item label="任务控制参数:" label-width="100px">
                            <gf-strbool-checkbox v-model="caseStepDef.isTodo">是否进入待办</gf-strbool-checkbox>
                        </el-form-item>
                        <el-form-item label="" label-width="100px">
                            <gf-strbool-checkbox v-model="caseStepDef.allowManualConfirm">是否允许人工干预通过</gf-strbool-checkbox>
                        </el-form-item>
                        <el-form-item label="" label-width="100px">
                            <gf-strbool-checkbox v-model="caseStepDef.allowEditEndTime">允许修改计划结束时间</gf-strbool-checkbox>
                        </el-form-item>
                        <el-form-item label="" label-width="100px">
                        <gf-strbool-checkbox v-model="caseStepDef.isAllowExecAgain">是否允许重新执行</gf-strbool-checkbox>
                        </el-form-item>
                        <el-form-item label="" label-width="100px">
                        <gf-strbool-checkbox v-model="caseStepDef.allowShowDetail">是否可以查看明细</gf-strbool-checkbox>
                        </el-form-item>
                        <el-form-item label="" v-if="caseStepDef.allowShowDetail==='1'" label-width="100px" style="margin-left: 30px;margin-top: -10px">
                        <gf-strbool-checkbox v-model="caseStepDef.allowZdyDetailUrl">自定义明细页面</gf-strbool-checkbox>
                          <gf-input :rows="6" v-if="caseStepDef.allowZdyDetailUrl==='1'" v-model="caseStepDef.detailUrl"  style="width: 35%;margin-left: 5px"></gf-input>
                        </el-form-item>
                        <el-form-item label="" label-width="100px">
                            <gf-strbool-checkbox v-model="caseStepDef.allowActionConfirm">是否允许人工确认</gf-strbool-checkbox>
                        </el-form-item>
                        <el-form-item label="执行方式:" label-width="100px">
                            <el-radio-group v-model="caseStepDef.stepExecMode">
                                <el-radio label="FIRST">启动执行一次</el-radio>
                                <el-radio label="FREQUENCY">按自定义频率执行</el-radio>
                                <el-radio label="MANUALLY">手动执行</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="执行频率配置:" label-width="100px" v-if="caseStepDef.stepExecMode === 'FREQUENCY'" prop="execScheduler">
                            <el-button type="text" @click="editExecTime(caseStepDef.execScheduler,'执行频率配置')">
                                {{caseStepDef.execScheduler}}点击配置
                            </el-button>
                        </el-form-item>
                    </div>
                    <div class="main-right" index="3">
                        <h3>出参配置</h3>
                        <div class="line rule-table" v-if="this.caseStepDef.blockCode">
                            <el-form-item label="" label-width="0" prop="paramsOutData">
                                <el-table
                                    :data="caseStepDef.paramsOutData"
                                    border
                                    stripe
                                    header-row-class-name="rule-header-row"
                                    header-cell-class-name="rule-header-cell"
                                    row-class-name="rule-row"
                                    cell-class-name="rule-cell">
                                    <el-table-column  v-for="item in paramsInRows" :prop="item.index" :key="item.index" :label="item.name" :width="item.width || 'auto'">
                                        <template slot-scope="scope">
                                            <template v-if="item.index === 'fieldName'">
                                                <el-select v-model="scope.row[item.index]">
                                                    <gf-filter-option
                                                        v-for="item in stepOutParams"
                                                        :key="item.fieldCode"
                                                        :label="item.fieldName"
                                                        :value="item.fieldCode">
                                                    </gf-filter-option>
                                                </el-select>
                                            </template>
                                            <template v-if="item.index === 'fieldValue'">
                                                <tag-textarea :id="'paramsOut'"
                                                    class="ellipsis"
                                                    v-model="scope.row[item.index].html"
                                                    @click.native="openDialog('paramsOut', scope.row[item.index], scope.$index)"
                                                    style="height: 35px">
                                                </tag-textarea>
                                            </template>
                                            <template v-if="item.index === 'option'">
                                                <el-button type="text" @click="deleteParamsOut(scope.$index)">删除</el-button>
                                            </template>
                                        </template>
                                    </el-table-column>
                                </el-table>
                                <el-button @click="addparamsOutData" class="rule-add-btn" size="small">新增</el-button>
                            </el-form-item>
                        </div>
                    </div>
                    <div class="main-right" index="4">
                        <h3>消息提醒</h3>
                        <el-form-item label="消息通知参数:">
                            <el-checkbox-group v-model="msgInformParam" @change="changeMsgInformParam">
                                <el-checkbox v-for="msgInform in msgInformOp"
                                            :key="msgInform.value"
                                            :label="msgInform.value">
                                    {{msgInform.label}}
                                </el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                        <el-form-item label-width="0" v-if="msgInformParam.length>0">
                            <el-tabs type="card" style="height: 330px;">
                                <el-tab-pane v-for="(msgInformItem, msgInformIndex) in msgInformParam" :key="msgInformIndex"
                                            :name="msgInformIndex+''">
                                <span class="tab-label" slot="label">
                                    <span>{{msgInformOp[msgInformItem].label}}</span>
                                </span>
                                    <el-form size="small" label-width="100px" v-show="msgInformItem === '0'" :disabled="optionType=='view'">
                                        <el-form-item label="提前通知配置:">
                                            <el-button type="text" @click="openRemindDlg(stepFormInfo.warningRemind,'warningRemind')">
                                                点击配置通知方式
                                            </el-button>
                                        </el-form-item>
                                        <el-form-item label="预警时间:">提前
                                            <gf-input v-model="caseStepDef.warningMintues" style="width: 40%"></gf-input>
                                            <el-select style="width: 20%" v-model="timeType" placeholder="请选择">
                                                <el-option
                                                    v-for="item in timeTypeData"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                                </el-option>
                                            </el-select>
                                        </el-form-item>
                                    </el-form>
                                    <el-form size="small" label-width="100px" :disabled="optionType=='view'" v-show="msgInformItem === '1'">
                                        <el-form-item label="完成通知配置:">
                                            <el-button type="text" @click="openRemindDlg(stepFormInfo.finishRemind,'finishRemind')">
                                                点击配置通知方式
                                            </el-button>
                                        </el-form-item>
                                    </el-form>
                                    <el-form size="small" label-width="100px" :disabled="optionType=='view'" v-show="msgInformItem === '2'">
                                        <el-form-item label="超时通知配置:">
                                            <el-button type="text" @click="openRemindDlg(stepFormInfo.timeoutRemind,'timeoutRemind')">
                                                点击配置通知方式
                                            </el-button>
                                        </el-form-item>
                                        <el-form-item label-width="113px"  label="服务水平承诺:">
                                            <el-select style="width: 30%" v-model="stepFormInfo.serviceResponseId" placeholder="请选择" @change="serviceResChange">
                                                <el-option
                                                    v-for="item in serviceRes"
                                                    :key="item.value"
                                                    :label="item.label"
                                                    :value="item.value">
                                                </el-option>
                                            </el-select>按照每隔
                                            <gf-input :value= "repeatMinutes" style="width: 10%" :disabled="true"></gf-input>分钟，执行
                                            <gf-input :value= "maxRepeatCount" style="width: 15%" :disabled="true"></gf-input>次后退出
                                        </el-form-item>
                                        <el-form-item label="异常记录:">
                                            <gf-strbool-checkbox v-model="caseStepDef.isRecordTimeoutError" @change="caseStepDef.timeoutErrorType='';caseStepDef.timeoutErrorContent=''">记入异常</gf-strbool-checkbox>
                                        </el-form-item>
                                        <template v-if="caseStepDef.isRecordTimeoutError === '1'">
                                            <el-form-item label="异常类型:">
                                                <el-select v-model="caseStepDef.timeoutErrorType" placeholder="请选择">
                                                    <gf-filter-option
                                                        v-for="item in errorTypeData"
                                                        :key="item.dictId"
                                                        :label="item.dictName"
                                                        :value="item.dictId">
                                                    </gf-filter-option>
                                                </el-select>
                                            </el-form-item>
                                            <el-form-item label="异常内容:">
                                                <gf-input v-model="caseStepDef.timeoutErrorContent"  type="textarea" :max-byte-len="100"></gf-input>
                                            </el-form-item>
                                        </template>
                                    </el-form>
                                    <el-form size="small" label-width="100px" :disabled="optionType=='view'" v-show="msgInformItem === '3'">
                                        <el-form-item label="异常通知配置:">
                                            <el-button type="text" @click="openRemindDlg(stepFormInfo.exceptionRemind,'exceptionRemind')">
                                                点击配置通知方式
                                            </el-button>
                                        </el-form-item>
                                        <el-form-item label="异常记录:">
                                            <gf-strbool-checkbox v-model="caseStepDef.isRecordError" @change="caseStepDef.errorType='';caseStepDef.errorContent=''">记入异常</gf-strbool-checkbox>
                                        </el-form-item>
                                        <template v-if="caseStepDef.isRecordError === '1'">
                                            <el-form-item label="异常类型:">
                                                <el-select v-model="caseStepDef.errorType" placeholder="请选择">
                                                    <gf-filter-option
                                                        v-for="item in errorTypeData"
                                                        :key="item.dictId"
                                                        :label="item.dictName"
                                                        :value="item.dictId">
                                                    </gf-filter-option>
                                                </el-select>
                                            </el-form-item>
                                            <el-form-item label="异常内容:">
                                                <gf-input v-model="caseStepDef.errorContent"  type="textarea" :max-byte-len="100"></gf-input>
                                            </el-form-item>
                                        </template>
                                        <el-form-item label="通知模式:" >
                                            <el-radio-group v-model="caseStepDef.sendMode" @change="sendModeAndJgChange(caseStepDef.sendMode)">
                                                <el-radio  label="1">每次异常时通知</el-radio>
                                                <el-radio  label="2">自定义时间间隔通知</el-radio>
                                            </el-radio-group>
                                            </el-form-item>
                                            <el-form-item label="每隔" v-if="caseStepDef.sendMode=='2'">
                                            <el-input-number v-model="caseStepDef.jgTime"  :min="1" :max="60" @change="sendModeAndJgChange(caseStepDef.sendMode)"></el-input-number>分钟   
                                        </el-form-item>
                                    </el-form>
                                </el-tab-pane>
                            </el-tabs>
                        </el-form-item>
                    </div>
                    <div class="main-right" index="5">
                        <h3>情景知识</h3>
                        <div class="line">
                            <el-form-item label="操作规程:"  class="content-flex encoding-rules">
                                <quill-editor class="gf-quill-editor"
                                            ref="myTextEditor"
                                            :options="editorOption"
                                            v-model="stepSituKnow.content"
                                            :config="editorOption"
                                            style="margin-top: 5px"
                                ></quill-editor>
                            </el-form-item>
                        </div>
                        <div class="line">
                            <el-form-item>
                                <el-checkbox style="margin-left: 10px;" v-model="stepSituKnow.isAutoCollect" :true-label="'1'"
                                            :false-label="'0'">自动收集同类任务异常处理经验</el-checkbox>
                            </el-form-item>
                        </div>
                    </div>
                </div>
            </el-main>
        </el-container>
    </el-form>
</template>

<script>
import TextareaDialog from "../../../../../../components/biz/tag-textarea/textarea-dialog";
export default {
    name: "operating-params",
    props: {
        operatingParams: {
            type: Object,
        },
        optionType: {
            type:String,
            required: true
        },
        stepCodeArr: {
            type: Object
        },
        reTaskDef: {
            type: Object
        },
    },
    data() {
        const checkActOwner = (rule, value, callback) => {
            if (value === '' || value === '[]') {
                callback(new Error('执行人必填'));
            } else {
                callback();
            }
        }
        const checkencodingRule = (rule, value, callback) => {
            let numberReg = /^\d+(\.\d+)?$/;
            if (value === '') {
                callback(new Error('编码规则不能为空'));
            } else if (!numberReg.test(value)) {
                callback(new Error('编码规则为数字'));
            } else if (value.length > 8) {
                callback(new Error('编码规则不能超过8位'));
            } else {
                callback();
            }
        };
        const checkParamsOutData = (rule, value, callback) => {
            if (value && value.length > 0) {
                let flag = true;
                value.forEach(item => {
                    if (!item.fieldName || !item.fieldValue.html) {
                        flag = false;
                    }
                });
                if (!flag) {
                    callback(new Error('出参值不能为空'));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };
        return {
            blockInfo:{},
            stepOutParams: [],
            editorOption: {
            },
            paramsInRows:[
                {
                    name: '参数名',
                    index: 'fieldName',
                    width: '130px'
                },
                {
                    name: '参数值',
                    index: 'fieldValue',
                },
                {
                    name: '操作',
                    index: 'option',
                    width: '50px'
                },
            ],
            rules: {
                startTime: [
                    {required: true, message: '执行开始时间必填', trigger: 'blur'},
                ],
                endTime: [
                    {required: true, message: '执行结束时间必填', trigger: 'blur'},
                ],
                stepActOwner: [
                    {required: true, validator: checkActOwner, trigger: 'blur'}
                ],
                encodingRule: [
                    {required: true, validator: checkencodingRule, trigger: 'blur'}
                ],
                paramsOutData: [
                    {required: true, validator: checkParamsOutData, trigger: 'blur'}
                ]

            },
            stepSituKnow:{
                isAutoCollect:'0',
                pkId:'',
                content:'',
            },
            stepFormInfo: {
                caseStepDef: {
                    sendMode:'0',
                    jgTime:0,
                    isNotSendErrRunning:'',
                    stepLevel: 0,
                    encodingRule:'',
                    stepTag: [],
                    stepRemark: '',
                    baseDateType: '0',  //基准日类型
                    startTime: '',  //执行开始时间
                    startBaseDay: '',  //执行开始时间基准日
                    startDayType: '00',  //执行开始时间基准日类型
                    startCrossDayType: '0',  //执行开始时间跨天类型
                    startDay: '',  //执行开始时间基准日天数
                    endTime: '',  //执行结束时间
                    endBaseDay: '',  //执行结束时间基准日
                    endDayType: '00',  //执行结束时间基准日类型
                    endCrossDayType: '0',  //执行结束时间跨天类型
                    endDay: '',  //执行结束时间基准日天数
                    isTodo: '0',  //是否进入待办
                    isNotCrtJob: '0',  //是否注册调度
                    allowManualConfirm: '1',  //是否允许人工干预通过
                    isAllowExecAgain: '0',//重新执行
                    allowActionConfirm: '0',//手工确认
                    allowShowDetail: '0',//查看明细
                    allowZdyDetailUrl: '0',
                    detailUrl: '',//自定义明细页面地址
                    allowFormAction: '0',//表单录入
                    allowEditEndTime: '0',  //允许修改计划结束时间
                    stepActOwner:'',  //执行人
                    stepCollaborateOwner:'',  //协作人
                    paramsOutData: [],
                    warningMintues:'',
                    execScheduler: '00#01#01#0 0/10 * * * ?',
                    stepExecMode:'FREQUENCY'
                },
                failRuleTableData: {judgeScript: ''},// 异常规则表格数据
                successRuleTableData: {judgeScript: ''}, // 完成规则表格数据
                activeRuleTableData: {judgeScript: ''},// 激活规则表格数据
                timeoutRuleTableData: {judgeScript: ''}, // 超时规则表格数据
                exceptionRemind: [],
                finishRemind: [],
                timeoutRemind: [],
                serviceResponseId: '',
                isRecordTimeoutError: '0',
                warningRemind: [],
            },
            modelPrefix: 'step',
            isShowCode:false,
            rosterDate: '',
            baseDateTypeOp: [{label: '业务日期', value: '0'}, {label: '系统日期', value: '1'}], //基准日类型下拉框数据
            ruleTypeOp: [{label: '默认规则', value: '0'}, {label: '自定义规则', value: '1'}],  // 规则选择类型选项
            startTypeOp: [{label: '随场景启动', value: '0'}, {label: '自定义启动', value: '1'}],
            endTypeOp: [{label: '默认规则', value: '0'}, {label: '自定义结束', value: '1'}],
            baseDateParamList: [], //基准日类型下拉框数据
            acrosDayTypes: [], //跨天类型下拉框数据
            crossDayTypes: [], //跨天类型下拉框数据
            startTimeForDay: null, //执行开始时间（天）
            endTimeForDay: null, //执行结束时间（天）
            startDayChecked: '0',  // 执行开始时间选择高级
            endDayChecked: '0',  // 执行结束时间选择高级
            startStepRuleChecked: '0',  // 激活规则确认框
            timeoutRuleChecked: '0',  //超时规则是否选中
            succeedRule: '0',  //完成规则
            abnormalRule: '0',  //异常规则
            ruleTargetOp: {
                step: this.stepCodeArr
            },
            memberRefList: [],  // 执行人列表
          collaborateRefList: [],  // 协作人列表
            respParam:'',
            bizTagOption: [], //业务标签下拉框数据           
            msgInformParam: [],// 消息配置类型类型选项
            msgInformOp: [{label: '提前通知', value: '0'}, {label: '完成通知', value: '1'}, {label: '超时通知', value: '2'},
                {label: '异常通知', value: '3'}],
            msgInfoStr: ['warningRemind', 'finishRemind', 'timeoutRemind', 'exceptionRemind'],
            serviceRes: [],
            repeatMinutes: '',
            maxRepeatCount: '',
            errorTypeData: [],
            timeTypeData: [{value: '1', label: '分钟'}, {value: '2', label: '小时'}, {value: '3', label: '天'}],
            timeType: '1',
        }
    },
    computed: {
        caseObjId(){
            return this.$app.$root.$store.state.publicData.caseObjData.objCode;
        },
        caseObjName(){
            return this.$app.$root.$store.state.publicData.caseObjData.objName;
        },
        poCode(){
            return this.$app.$root.$store.state.publicData.caseObjData.poCode;
        },
        caseStepDef(){
            return this.stepFormInfo.caseStepDef;
        },
        startDayDisabled() {
            return this.stepFormInfo.caseStepDef.startCrossDayType !== '0';
        },
        endDayDisabled() {
            return this.stepFormInfo.caseStepDef.endCrossDayType !== '0';
        },
    },
    watch: {
        'blockInfo': "initData",
        'endDayChecked'(val) {
            if (val === '0') {
                this.stepFormInfo.caseStepDef.endDay = '';
            }
            this.timeChange();
        },
        'startDayChecked'(val) {
            if (val === '0') {
                this.stepFormInfo.caseStepDef.startDay = '';
            }
            this.timeChange();
        }
    },
    beforeMount() {
        this.rosterDate = window.bizDate
        if(this.optionType !== 'add'){
            this.operatingParamsGetData();
            this.getStepKnowInfo();
        }
        if(this.optionType === 'add' || this.optionType === 'copy'){
            this.initCode();
            this.memberRefList.push({
                refType: '1',
                memberId: this.$app.session.data.user.userId,
                memberDesc: this.$app.session.data.user.userName
            })
        }else{
            this.stepFormInfo.caseStepDef.encodingRule = this.stepFormInfo.caseStepDef.stepCode.split('-')[1]
            this.modelPrefix =  this.stepFormInfo.caseStepDef.stepCode.split('-')[0] || this.modelPrefix
        }
        this.initDict();
        this.initParamsOut();
        this.startTimeForDay = {selectableRange: `00:00:00-${this.caseStepDef.endTime ? this.caseStepDef.endTime + ':00' : '23:59:59'}`};
        this.endTimeForDay = {selectableRange: `${this.caseStepDef.startTime ? this.caseStepDef.startTime + ':00' : '00:00:00'}-23:59:59`};
    },
    mounted() {
        //    业务标签下拉数据
        this.bizTagOption = this.$app.dict.getDictItems("AGNES_BIZ_TAG");
        this.initData();
        this.$nextTick(() => {
            this.msgInfoStr.forEach((strItem, index)=>{
                if(this.stepFormInfo[strItem] && this.stepFormInfo[strItem].length>0){
                    this.msgInformParam.push(index+'');
                }
            });
        });
        this.getServiceResponse();
        this.errorTypeData = this.$app.dict.getDictItems("AGNES_DOP_ERR_TYPE");
        const paneElement = document.getElementById('pane-operatingParams');
        paneElement.addEventListener('scroll', this.handleScroll);
    },
    beforeDestroy() {
    const paneElement = document.getElementById('pane-operatingParams');
    paneElement.removeEventListener('scroll', this.handleScroll);
    },
    methods: {
        sendModeAndJgChange(val){
            if(val){
                if(val ==='1') {
                    this.stepFormInfo.caseStepDef.jgTime = 0;
                }
            }
        },
        // 监听滚轮滚动事件
        handleScroll(){
            let index = null;
            const container = document.querySelector('.describe-tabs');
            const tabs = container.querySelectorAll('.el-tabs__item.is-left');
            const activeBar = container.querySelector('.el-tabs__active-bar.is-left');
            const elements = document.querySelectorAll('.main-right');
            let selectedTab = null;
            for (let i = 0; i < tabs.length; i++) {
            const list = elements[i];
            const rect = list.getBoundingClientRect();
                if (rect.bottom > 388) {
                    selectedTab = tabs[i];
                    index = i;
                    break;
                }
            }
            if(selectedTab){
                tabs.forEach((tab) => {
                tab.classlist?.remove('is-active');
                });
                activeBar.style.transform = `translateY(${34*index}px)`;
                selectedTab.classlist?.add('is-active');
            }           
        },
        // 左侧tab点击事件
        handleTabClick(label){
            const paneLabel = label.index;
            const targetElement = document.querySelector(`.main-right[index="${paneLabel}"]`);
            if (targetElement) {
                targetElement.scrollIntoView({ behavior: 'smooth' });
            }
        },
        initData(){

          this.$app.registerCmd('case-step-change-block',(params) => {
            this.blockInfo = params[0];
            this.caseStepDef.blockCode = this.blockInfo.blockCode;
            //清空参数
            this.caseStepDef.paramsOutData = [];
            if(this.blockInfo.allowManualConfirm === '1'){
              this.caseStepDef.allowManualConfirm = '1';
            }
            if(this.blockInfo.isAllowEdit === '1'){
              this.caseStepDef.allowEditEndTime = '1';
            }
            if(this.blockInfo.isTodo === '1'){
              this.caseStepDef.isTodo = '1';
            }
            if(this.blockInfo.isAllowExecAgain === '1'){
              this.caseStepDef.isAllowExecAgain = '1';
            }
            if(this.blockInfo.allowShowDetail === '1'){
              this.caseStepDef.allowShowDetail = '1';
            }
            if(this.blockInfo.allowZdyDetailUrl === '1'){
              this.caseStepDef.allowZdyDetailUrl = '1';
            }
            if(this.blockInfo.allowActionConfirm === '1'){
              this.caseStepDef.allowActionConfirm = '1';
            }
            if(this.blockInfo.allowFormAction === '1'){
              this.caseStepDef.allowFormAction = '1';
            }
            this.initParamsOut();
            //获取积木块信息
          })
        },
        async getStepKnowInfo(){
            let resp = await this.$api.motConfigApi.queryCaseStepKnow({caseKey:this.reTaskDef.caseKey,stepCode:this.stepFormInfo.caseStepDef.stepCode,type:'01'});
            if(resp.data && resp.data.length>0){
                this.stepSituKnow = resp.data[0];
            }
        },
        operatingParamsGetData(){
            const {baseDateType,isAllowExecAgain,allowShowDetail,allowZdyDetailUrl,detailUrl,allowFormAction,allowActionConfirm,startTime,startBaseDay,startDayType,startCrossDayType,dealType,execScheduler,
                startDay,endTime,endBaseDay,endDayType,endCrossDayType,endDay,isTodo,isNotCrtJob,allowManualConfirm,
                allowEditEndTime,stepActOwner,stepCollaborateOwner,encodingRule,stepLevel,stepRemark,stepTag,paramsOutData,stepCode,blockCode,
                warningMintues,isRecordTimeoutError,timeoutErrorType,timeoutErrorContent,isRecordError,errorType,errorContent,stepExecMode,sendMode,jgTime,isNotSendErrRunning}=this.operatingParams.caseStepDef;
            const {failRuleTableData,successRuleTableData,activeRuleTableData,timeoutRuleTableData,exceptionRemind,
                finishRemind,timeoutRemind,serviceResponseId,warningRemind,} = this.operatingParams;
            this.stepFormInfo =  {failRuleTableData,successRuleTableData,activeRuleTableData,timeoutRuleTableData,exceptionRemind,finishRemind,
                timeoutRemind,serviceResponseId,warningRemind,};
            let data = {baseDateType,isAllowExecAgain,allowShowDetail,allowZdyDetailUrl,detailUrl,allowFormAction,allowActionConfirm,startTime,startBaseDay,startDayType,startCrossDayType,dealType,execScheduler,
                startDay,endTime,endBaseDay,endDayType,endCrossDayType,endDay,isTodo,isNotCrtJob,allowManualConfirm,
                allowEditEndTime,stepActOwner,stepCollaborateOwner,encodingRule,stepLevel,stepRemark,stepTag,paramsOutData,stepCode,blockCode,warningMintues,
                isRecordTimeoutError,timeoutErrorType,timeoutErrorContent,isRecordError,errorType,errorContent,stepExecMode,sendMode,jgTime,isNotSendErrRunning};
            this.$set(this.stepFormInfo, 'caseStepDef',data)
        },
        async saveparamsOutData(stepCode, stepName){
            // 保存出参 在父元素保存step时使用 如无积木块则不保存
            if(!this.caseStepDef.blockCode) return false;
            let data = {
                caseKey: this.reTaskDef.caseKey,
                stepCode: stepCode,
                stepName: stepName,
                caseDefaultObjs: this.stepOutParams,
            }
            try {
                await this.$api.blockApi.saveCaseDefaultObject(data)
            } catch (e){
                this.$message.error(e.message)
            }
        },
        async saveStepSituKnow(stepCode){
            // 保存场景知识
            this.stepSituKnow.stepCode = stepCode;
            this.stepSituKnow.caseKey = this.reTaskDef.caseKey;
            this.stepSituKnow.type = '01';
            try {
                await this.$api.motConfigApi.saveCaseStepKnow(this.stepSituKnow)
            } catch (e){
                this.$message.error(e.message)
            }
        },
        //验证表单规则
        validateForm(){
            let ok = null
            this.$refs['form'].validate(validate => {
                ok = validate
            })
            return ok
        },
        getData() {
            let data = null;
            const ok = this.$refs['form'].validate()
            if(!ok) {
                return
            }
            if (this.timeType === '2') {
                this.stepFormInfo.caseStepDef.warningMintues = this.stepFormInfo.caseStepDef.warningMintues * 60
            } else if (this.timeType === '3') {
                this.stepFormInfo.caseStepDef.warningMintues = this.stepFormInfo.caseStepDef.warningMintues * 60 * 24
            }
            this.stepFormInfo.caseStepDef.stepCode = `${this.modelPrefix}-${this.stepFormInfo.caseStepDef.encodingRule}`
            if (this.succeedRule === '0') {
                this.stepFormInfo.successRuleTableData = {}
            }
            if (this.abnormalRule === '0') {
                this.stepFormInfo.failRuleTableData = {}
            }
            this.stepFormInfo.caseStepDef.startTime = '00:00:00'
            if (this.startStepRuleChecked === '0') {
                this.stepFormInfo.activeRuleTableData = {}
            }
            this.stepFormInfo.caseStepDef.endTime = '23:59:59'
            if (this.timeoutRuleChecked === '0') {
                this.stepFormInfo.timeoutRuleTableData = {}
            }
            this.setRuleBody()
            if(!this.caseStepDef.allowShowDetail || this.caseStepDef.allowShowDetail === '0'){
                this.caseStepDef.allowZdyDetailUrl='';
                this.caseStepDef.detailUrl='';
            }
            if(!this.caseStepDef.allowZdyDetailUrl || this.caseStepDef.allowZdyDetailUrl === '0'){
                this.caseStepDef.detailUrl='';
            }
            //依赖关系文字拼接
            this.stepFormInfo.caseStepDef.dependDesc = this.connectActiveAndTimeoutRules()
            this.caseStepDef.stepActOwner = JSON.stringify(this.memberRefList);
            this.caseStepDef.stepCollaborateOwner = JSON.stringify(this.collaborateRefList);
            data = {stepFormInfo:this.stepFormInfo};

                
            return data

        },
        /**
         * 设置规则参数
         */
        setRuleBody() {
            if(this.stepFormInfo?.activeRuleTableData?.ruleList){
                const activeRuleJson = this.$refs.activeRuleTable.jsonFormatter();
                this.stepFormInfo.activeRuleTableData.ruleBody = activeRuleJson;
                //如果启动规则列表中存在time的 将固定时间的值赋给 startTimeRuleValue 不存在则将其赋空值
                let timeRule = this.stepFormInfo.activeRuleTableData.ruleList.find(item => item.ruleType === 'time')
                this.stepFormInfo.caseStepDef.startTimeRuleValue = timeRule ? timeRule.ruleValue : ''
                //如果启动规则列表中存在date的 将ruleTarget拼接ruleKey赋给 startDateRuleValue 不存在则将其赋空值
                let dateRule = this.stepFormInfo.activeRuleTableData.ruleList.find(item => item.ruleType === 'date')
                this.stepFormInfo.caseStepDef.startDateRuleValue = dateRule ? `${dateRule.ruleTarget || ''};${dateRule.ruleKey}` : '';
            }
            if(this.stepFormInfo?.successRuleTableData?.ruleList){
                const successRuleJson = this.$refs.successRuleTable.jsonFormatter();
                this.stepFormInfo.successRuleTableData.ruleBody = successRuleJson;
            }
            if(this.stepFormInfo?.timeoutRuleTableData?.ruleList){
                const timeoutRuleJson = this.$refs.timeoutRuleTable.jsonFormatter();
                this.stepFormInfo.timeoutRuleTableData.ruleBody = timeoutRuleJson;
                //如果结束规则列表中存在time的 将固定时间的值赋给 endTimeRuleValue 不存在则将其赋空值
                let timeRule = this.stepFormInfo.timeoutRuleTableData.ruleList.find(item => item.ruleType === 'time')
                this.stepFormInfo.caseStepDef.endTimeRuleValue = timeRule ? timeRule.ruleValue : ''
                //如果结束规则列表中存在date的 将ruleTarget拼接ruleKey赋给 endDateRuleValue 不存在则将其赋空值
                let dateRule = this.stepFormInfo.timeoutRuleTableData.ruleList.find(item => item.ruleType === 'date')
                this.stepFormInfo.caseStepDef.endDateRuleValue = dateRule ? `${dateRule.ruleTarget || ''};${dateRule.ruleKey}` : '';
            }
            if(this.stepFormInfo?.failRuleTableData?.ruleList){
                const failRuleJson = this.$refs.failRuleTable.jsonFormatter();
                this.stepFormInfo.failRuleTableData.ruleBody = failRuleJson;
            }
        },
        async initParamsOut(){
            if(!this.caseStepDef.blockCode){
                this.stepOutParams = []
                return;
            }
            const p = this.$api.blockApi.queryBlockParams({blockCode:this.caseStepDef.blockCode,paramType:'out'});
            let resp = await this.$app.blockingApp(p);
            if(resp.data){
                this.stepOutParams = resp.data;
            }
        },
        async initDict() {
            this.baseDateParamList = [];
            if(this.caseStepDef.stepActOwner){
                this.memberRefList = JSON.parse(this.caseStepDef.stepActOwner);
            }
            if(this.caseStepDef.stepCollaborateOwner){
                this.collaborateRefList = JSON.parse(this.caseStepDef.stepCollaborateOwner);
            }
            this.setCheckParams()
            const startDay = this.caseStepDef.startDay;
            const endDay = this.caseStepDef.endDay;
            const  startCrossDayType = this.caseStepDef.startCrossDayType
            const  endCrossDayType = this.caseStepDef.endCrossDayType
            if(startDay || (startCrossDayType && startCrossDayType!=='0')){
                this.startDayChecked = '1';
            }
            if(endDay || (endCrossDayType && endCrossDayType!=='0')){
                this.endDayChecked = '1';
            }
            this.dayChecked = startDay || endDay ? '1': '0'
            this.acrosDayTypes.splice(0, 0, {'paramKey': '00', 'paramName': 'f'});
            this.acrosDayTypes.splice(1, 0, {'paramKey': '01', 'paramName': '工作日'});
            this.crossDayTypes.splice(0, 0, {'paramKey': '0', 'paramName': '固定值'});
            this.baseDateParamList.splice(0, 0, {'paramKey': '_caseStartDay', 'paramName': 'case启动日期'});
            if (this.caseStepDef.startBaseDay === '') {
                this.caseStepDef.startBaseDay = '_caseStartDay';
            }
            if (this.caseStepDef.endBaseDay === '') {
                this.caseStepDef.endBaseDay = '_caseStartDay';
            }
        },
        /**
         * 设置复选框参数
         */
        setCheckParams() {
            const activeRuleTableData = this.stepFormInfo?.activeRuleTableData?.ruleList || [];
            this.startStepRuleChecked = activeRuleTableData.length <= 0 ? '0' : '1'

            const successRuleTableData = this.stepFormInfo?.successRuleTableData?.ruleList || [];
            this.succeedRule = successRuleTableData.length <= 0 ? '0' : '1'

            const timeoutRuleTableData = this.stepFormInfo?.timeoutRuleTableData?.ruleList || [];
            this.timeoutRuleChecked = timeoutRuleTableData.length <= 0 ? '0' : '1'
            
            const failRuleTableData = this.stepFormInfo?.failRuleTableData?.ruleList || [];
            this.abnormalRule = failRuleTableData.length <= 0 ? '0' : '1'
        },
        timeChange() {
            if (this.endDayChecked === '1' || this.startDayChecked === '1') {
                this.startTimeForDay = {selectableRange: '00:00:00-23:59:59'};
                this.endTimeForDay = {selectableRange: '00:00:00-23:59:59'};
            } else {
                this.startTimeForDay = {selectableRange: `00:00:00-${this.caseStepDef.endTime ? this.caseStepDef.endTime + ':00' : '23:59:59'}`};
                this.endTimeForDay = {selectableRange: `${this.caseStepDef.startTime ? this.caseStepDef.startTime + ':00' : '00:00:00'}-23:59:59`};
            }
        },
        getMemberList(val) {
            this.memberRefList = val;
            this.stepFormInfo.caseStepDef.stepActOwner = JSON.stringify(val);
        },
        getCollaborateMemberList(val) {
            this.collaborateRefList = val;
            this.stepFormInfo.caseStepDef.stepCollaborateOwner = JSON.stringify(val);
        },

        getReqParam(data){
            if(data && data.allowManualConfirm){
                this.caseStepDef.allowManualConfirm = data.allowManualConfirm;
            }else{
                this.caseStepDef.allowManualConfirm = '0'
            }
            if(data && data.isTodo){
                this.caseStepDef.isTodo = data.isTodo;
            }else{
                this.caseStepDef.isTodo = '0'
            }
            if(data && data.isNotCrtJob){
                this.caseStepDef.isNotCrtJob = data.isNotCrtJob;
            }else{
                this.caseStepDef.isNotCrtJob = '0'
            }
            if(data && data.stepExecMode){
                this.caseStepDef.stepExecMode = data.stepExecMode;
            }else{
                this.caseStepDef.stepExecMode = 'FREQUENCY'
            }

        },
        async initCode(){
            const resp = await this.$api.codeGeneratorApi.getCode('step');
            if (resp && resp.data){
                this.stepFormInfo.caseStepDef.stepCode = resp.data;
                this.modelPrefix = resp.data.split('-')[0]
                this.stepFormInfo.caseStepDef.encodingRule = resp.data.split('-')[1]
            }
        },
        async openDialog(id,content,index) {
            let dataType = this.reTaskDef.caseKey;
            this.index = index;
            this.id = `${id}${index}-dlg`;
            this.$dialog.create({
                title: '属性字段',
                width: '900px',
                component: TextareaDialog,
                closeOnClickModal: false,
                args: {dataType: dataType, id: this.id, innerHtml: content.html, actionOk: this.setContent.bind(this)}
            })
        },
        setContent(content,data) {
            this.stepFormInfo.caseStepDef.paramsOutData[this.index].fieldValue.html = content;
            this.stepFormInfo.caseStepDef.paramsOutData[this.index].fieldValue.res = data;
        },
        addparamsOutData(){
            this.stepFormInfo.caseStepDef.paramsOutData.push({
                fieldName: '',
                fieldValue: {
                    html: '',
                    res: ''
                }
            })
        },
        deleteParamsOut(index){
            this.stepFormInfo.caseStepDef.paramsOutData.splice(index,1)
        },
        editExecTime( execScheduler,title) {
            this.showDlg(execScheduler,title, this.setExecScheduler.bind(this));
        },
        showDlg(cornObj,title, action) {
            if (this.mode === 'view') {
                return;
            }
            this.$nav.showDialog(
                'gf-cron-modal',
                {
                    args: {
                        cornObj: cornObj,
                        action,
                        showType:'second,minute,hour,extSetting'
                    },
                    width: '530px',
                    title: this.$dialog.formatTitle(title, "edit"),
                }
            );
        },
        setExecScheduler(cron) {
            this.$set(this.stepFormInfo.caseStepDef, 'execScheduler', cron);
        },
        async showRemind(remindProp,remindSort){
            this.stepFormInfo[remindSort] = remindProp;
            if(remindProp.length>0){
                this.stepFormInfo[remindSort][0].isSendAttach = JSON.stringify(
                    {sendAttachFile:remindProp[0].sendAttachFile, sendDataList:remindProp[0].sendDataList}
                )
            }
            if(remindProp.length>1){
                this.stepFormInfo[remindSort][1].isSendAttach = JSON.stringify(
                    {sendAttachFile:remindProp[1].sendAttachFile, sendDataList:remindProp[1].sendDataList}
                )
            }
        },
        // 告警方式配置，打开弹框
        openRemindDlg(remindProp,remindSort) {
            let caseKey =this.reTaskDef.caseKey;
            this.compatibleOldRemind(remindProp);
            this.showRemindDlg(remindProp,remindSort,caseKey, this.showRemind.bind(this));
        },

        showRemindDlg(remindProp,remindSort, caseKey,actionOk) {
            this.$nav.showDialog(
                'remind-def',
                {
                    args: {remindProp,remindSort,caseKey, actionOk},
                    width: '800px',
                    title: this.$dialog.formatTitle('通知方式配置',"edit"),
                }
            );
        },
        //拼接开始和结束的依赖关系
        connectActiveAndTimeoutRules(){
            let dependRules = '';
            if(this.check1()){
                let judgeScript = this.getJudgeScript(this.stepFormInfo.activeRuleTableData, 'activeRuleTable')
                dependRules+= `启动:{${judgeScript}}`
            }
            if(this.check2()){
                let judgeScript = this.getJudgeScript(this.stepFormInfo.timeoutRuleTableData, 'timeoutRuleTable')
                dependRules+= `结束:{${judgeScript}}`
            }
            dependRules = dependRules.replaceAll('&&','且')
                .replaceAll('||','或')
                .replaceAll('!', '非')
            return dependRules
        },
        /**
         * 条件判断1
         */
        check1() {
            return this.stepFormInfo.activeRuleTableData?.ruleList?.length && this.stepFormInfo.activeRuleTableData?.judgeScript
        },
        /**
         * 条件判断2
         */
        check2() {
            return this.stepFormInfo.timeoutRuleTableData?.ruleList?.length && this.stepFormInfo.timeoutRuleTableData?.judgeScript
        },
        /**
         * 获取judgeScript字段
         * @param tableData 
         * @param ref 
         */
        getJudgeScript(tableData, ref) {
            let judgeScript = tableData.judgeScript;
            let ruleList = tableData.ruleList
            ruleList.forEach(rule => {
                if(judgeScript.includes(rule.ruleTag)){
                    let ruleName = this.$app.dict.getDictItem('AGNES_RULE_TYPE', rule?.ruleType)?.dictName
                    let ruleParamName = this.$refs[ref].getRuleParamName(rule)
                    judgeScript = judgeScript.replaceAll(rule.ruleTag, ruleName ? `[${ruleName}]为"${ruleParamName}"` : '')
                }
            })
            return judgeScript
        },
        changeMsgInformParam(val){
            this.msgInfoStr.forEach((item, index) => {
                if(!val.includes(index + '')){
                    this.stepFormInfo[item] = [];
                }
            })
        },
        async serviceResChange(param) {
            this.serviceRes.forEach((item) => {
                if(item.value === param){
                    this.repeatMinutes = item.repeatMinutes
                    this.maxRepeatCount = item.maxRepeatCount
                }
            });
        },
        async getServiceResponse(){
            const serviceRes = this.$api.flowTaskApi.getServiceResponse();
            const serviceResData = await this.$app.blockingApp(serviceRes);
            if(serviceResData.data) {
                const serviceResList = serviceResData.data;
                serviceResList.forEach((item) => {
                    this.serviceRes.push({
                        label: item.serviceResponseName, value: item.serviceResponseId,
                        repeatMinutes: item.repeatMinutes, maxRepeatCount: item.maxRepeatCount
                    });
                });
            }
            if(this.stepFormInfo.serviceResponseId){
               this.serviceResChange(this.stepFormInfo.serviceResponseId);
            }
        },
        async compatibleOldRemind(remindProp){
            remindProp.forEach(item => {
                if(['2', '4'].includes(item.remindMode)){
                    if(item.remindMode==='2') {
                        item = this.getItem(item)
                    }
                    if(typeof item.remindContent === 'string'){
                        const htmlAndRes = item.remindContent
                        item.remindContent = {html: '', res: ''}
                        item.remindContent.html = htmlAndRes;
                        item.remindContent.res = htmlAndRes;
                    }
                    if(this.checkRemindUser(item)){
                        const remindUser = [{refType:'4',memberId:item.remindUser,memberDesc:item.remindUser}]
                        item.remindUserList = remindUser;
                        item.remindUser = JSON.stringify(remindUser)
                    }
                }
            });
        },
        /**
         * 校验remindUser
         * @param item 
         */
        checkRemindUser(item) {
            return !item.remindUserList && item.remindUser
        },
        /**
         * 校验remindBcc
         * @param item 
         */
        checkRemindBcc(item) {
            return !item.remindBccList && item.remindBcc
        },
        /**
         * 校验remindCc
         * @param item
         */
        checkRemindCc(item) {
            return !item.remindCcList && item.remindCc
        },
        /**
         * 获取item
         * @param item
         */
        getItem(item) {
            if(typeof item.remindTitle === 'string'){
                const htmlAndRes = item.remindTitle
                item.remindTitle= {html: '', res: ''}
                item.remindTitle.html =  htmlAndRes;
                item.remindTitle.res = htmlAndRes;
            }
            if(this.checkRemindBcc(item)){
                const remindBcc = [{refType:'4',memberId:item.remindBcc,memberDesc:item.remindBcc}]
                item.remindBccList = remindBcc;
                item.remindBcc = JSON.stringify(remindBcc)
            }
            if(this.checkRemindCc(item)){
                const remindCc = [{refType:'4',memberId:item.remindCc,memberDesc:item.remindCc}]
                item.remindCcList = remindCc;
                item.remindCc = JSON.stringify(remindCc)
            }
            return item
        },

    }
}
</script>

<style scoped>
.work-process-operating-params-form {
    padding-right: 20px;
    height: 100%;
}
.work-process-operating-params-form >>> .el-main{
    padding-top: 0;
    padding-bottom: 0;
}

>>> .el-form .el-form-item .el-form-item__content .el-input-group__prepend {
    border-color: #A8AED3;
}
>>> .el-form .encoding-rules.el-form-item .el-form-item__content input {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}
.task-rate >>> .el-form-item__content{
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 100%;
}

.task-rate .el-icon-refresh-left {
    color: #0f5eff;
    margin-left: 10px;
    vertical-align: text-top;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
}
.describe-tabs{
    display: flex;
}
.describe-tabs >>> > .el-tabs__header.is-left{
    width: 100px
}
.describe-tabs >>> .el-tabs__header.is-left .el-tabs__nav .el-tabs__item.is-active {
    background-color: #ebf3fd;
    color: #0f5eff;
    padding-left: 10px !important;
}
.describe-tabs >>> .el-tabs__header.is-left .el-tabs__nav .el-tabs__item {
    text-align: left;
    padding-left: 10px !important;
    color: #333333;
}
.describe-tabs >>> >.el-tabs__content{
  scrollbar-width: none;
}
.describe-tabs >>> .el-tabs__content .el-tab-pane {
  display: block !important;
}

.tab-left >>> .el-tabs--left .el-tabs__nav-scroll{
    position: fixed;
}

.box-right{
    width: 100%;
    height: 100%;
}

.box-right .main-right{
    width: 100%;
    min-height: 730px;
    max-height: 1800px
}
</style>


<style scoped lang="less">
.gf-quill-editor{
    height: 350px;
    /deep/ .ql-toolbar{
        .ql-formats{
            margin: 0;
            vertical-align: baseline;
            > button {
                width: 10px;
            }
        }
        .ql-picker:not(.ql-color-picker):not(.ql-icon-picker){
            svg{
                top: 60%;
            }
        }
    }
}
</style>

