<template>
    <div>
        <el-form
            ref="form"
            class="work-process-notification-form"
            :model="form"
            :rules="formRules"
            :disabled="optionType==='view'"
            label-width="90px">

            <div v-for="(msgDef,index) in form.msgDefList" :key="index" class="msg-setting">
                <div class="msg-copy-delete">
                    <el-tooltip effect="light" content="删除" placement="top">
                        <el-button type="text"  @click="removes(index)" style="color: red">
                            <i class="el-icon-delete"></i></el-button>
                    </el-tooltip>
                    <el-tooltip effect="light" content="复制" placement="top">
                        <el-button type="text" @click="addMsgDef(msgDef)">
                            <i class="el-icon-copy-document"></i></el-button>
                    </el-tooltip>
                </div>
                <el-form-item label="提醒标题:" :prop="'msgDefList.'+index+'.msgTitle.html'"
                              :rules="formRules.msgTitle">
                    <tag-textarea :id="'msgTitleInfo'+index"
                                  class="ellipsis"
                                  v-model="msgDef.msgTitle.html"
                                  @click.native="openDialog('msgTitleInfo',msgDef.msgTitle, index,'提醒标题')"
                                  style="height: 35px">
                    </tag-textarea>
                </el-form-item>
                <el-form-item label="通知类型:" :prop="'msgDefList.'+index+'.msgInfoType'">
                    <el-checkbox-group v-model="msgDef.msgInfoType">
                        <el-checkbox v-for="item in execTypeParams"
                                     :key="item.value"
                                     :label="item.value">
                            {{ item.label }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="提醒方式:" :prop="'msgDefList.'+index+'.msgType'" :rules="formRules.msgType">
                    <el-checkbox-group v-model="msgDef.msgType">
                        <el-checkbox v-for="item in msgTypeParams"
                                     :key="item.dictId"
                                     :label="item.dictId">
                            {{ item.dictName }}
                        </el-checkbox>
                    </el-checkbox-group>
                </el-form-item>
                <el-form-item label="提醒对象:" :prop="'msgDefList.'+index+'.msgRecipients'"
                              :rules="formRules.msgRecipients">
                    <gf-person-chosen ref="memberRef" style="float:left;width: 80%"
                                      :disabled="false"
                                      :memberRefList="msgDef.msgMemberRefList"
                                      chosenType="user,group"
                                      :rosterDate="msgDef.rosterDate"
                                      :index="index"
                                      @getMemberList="getMsgMemberList">
                    </gf-person-chosen>
                </el-form-item>
                <el-form-item label="提醒时间:" :prop="'msgDefList.'+index+'.msgTime'" :rules="formRules.msgTime">
                    <div style="width: 35%;float: left;">
                        <el-form-item class="el-form-item-time" :prop="'msgDefList.'+index+'.msgTimeType'"
                                      :rules="formRules.msgTimeType">
                            <gf-dict-select placeholder="请选择提醒时间类型" dict-type="MSG_TIME_TYPE"
                                            v-model="msgDef.msgTimeType"
                                            @change="setMsgTime(index)">
                            </gf-dict-select>
                        </el-form-item>
                    </div>
                    <div style="width: 65%;float: left;">
                        <el-form-item v-if="check(msgDef.msgTimeType)" style="width: 30%;margin-left: 10px;float: left">
                            <el-form-item class="el-form-item-time" :prop="'msgDefList.'+index+'.day'"
                                          :rules="formRules.day" style="width:80%;float: left;">
                                <gf-input v-model.number="msgDef.day" @change="setMsgTime(index)" placeholder="天数" :max-byte-len="50"
                                ></gf-input>
                            </el-form-item>
                            <div style="width:20%;float: left;text-align: right">天</div>
                        </el-form-item>
                        <el-form-item style="width: 60%;float: left;margin-left: 10px;">
                            <el-form-item class="el-form-item-time" :prop="'msgDefList.'+index+'.hour'"
                                          :rules="formRules.hour" style="float: left;width: 50%;">
                                <el-time-picker
                                    v-model="msgDef.hour"
                                    @change="setMsgTime(index)"
                                    :picker-options="timeForDay"
                                    placeholder="时间"
                                    format="HH:mm"
                                    value-format="HH:mm">
                                </el-time-picker>
                            </el-form-item>
                            <div style="float: left;margin-left: 5px">时</div>
                        </el-form-item>
                    </div>
                </el-form-item>
                <el-form-item label="提醒内容:" :prop="'msgDefList.'+index+'.msgContent.html'"
                              :rules="formRules.msgContent">
                    <tag-textarea :id="'msgContentInfo'+index"
                                  class="ellipsis"
                                  v-model="msgDef.msgContent.html"
                                  @click.native="openDialog('msgContentInfo',msgDef.msgContent, index,'提醒内容')"
                                  style="height: 35px">
                    </tag-textarea>
                </el-form-item>
            </div>
            <div class="add-msg-btn">
                <el-button @click="addMsgDef('')" size="mini"><i class="el-icon-plus"></i>新增</el-button>
            </div>
        </el-form>
    </div>
</template>

<script>
import TextareaDialog from "../../../../../../components/biz/tag-textarea/textarea-dialog";
export default {
    name: "notification",
  props:{
    notification: {
        type: [Array, Object],
    },
    optionType: {
      type:String,
      required: true
    },
    reTaskDef: {
        type: Object,
    },
  },
    data() {
        const checkActOwner = (rule, value, callback) => {
            if (value === '' || value === '[]') {
                callback(new Error('执行人必填'));
            } else {
                callback();
            }
        }
        const checkday = (rule, value, callback) => {
            const day = /^[0-9]*$/;
            if (value === '' || value.length === 0) {
                callback(new Error('天数必填'));
            } else if (parseInt(value) <= 0) {
                callback(new Error('天数必须大于0'));
            } else if(!day.test(value)) {
                callback(new Error('天数无效'));
            } else{
                callback();
            }
        }
        return {
            form: {
                msgDefList: [],
            },
            timeForDay: null,
            msgTypeParams: this.$app.dict.getDictItems("EVENT_STRATEGY_REMIND_TYPE"),
            execTypeParams:[{label: '提前通知', value: '0'}, {label: '完成通知', value: '1'}, {label: '超时通知', value: '2'},
                {label: '异常通知', value: '3'}],
            formRules: {
                msgTitle: [
                    {required: true, message: '请输入提醒标题', trigger: 'blur'},
                    {max: 2000, message: "提醒标题最多2000字", trigger: "change"}
                ],
                msgType: [
                    {required: true, message: '请选择提醒方式', trigger: 'change'}
                ],
                msgRecipients: [
                    {required: true, validator: checkActOwner, trigger: 'change'}
                ],
                msgTime: [
                    {required: true, message: ' ', trigger: 'change'}
                ],
                msgContent: [
                    {required: true, message: '请输入提醒内容', trigger: 'blur'}
                ],
                msgTimeType: [
                    {required: true, message: '消息提醒时效类型必选', trigger: 'change'}
                ],
                day: [
                    {required: true, validator: checkday, trigger: 'change'}
                ],
                hour: [
                    {required: true, message: '时间必填', trigger: 'change'}
                ]
            },
        }
    },
  beforeMount() {
      if(this.optionType!=='add'){
        this.form.msgDefList = this.notification;
      }
  },
  computed: {
    caseObjId() {
      return this.$app.$root.$store.state.publicData.caseObjData.objCode;
    },
    poCode() {
      return this.$app.$root.$store.state.publicData.caseObjData.poCode;
    },
  },
    methods: {
        getData() {
            let data = null;
            this.$refs['form'].validate(validate => {
                if (!validate) {
                    data = false;
                } else {
                    data = this.form
                }
            });
            return data
        },
        getMsgMemberList(val, index) {
            this.form.msgDefList[index].msgMemberRefList = val
            this.form.msgDefList[index].msgRecipients = JSON.stringify(val);
        },
        removes(index) {
            this.form.msgDefList.splice(index, 1)
        },
        addMsgDef(val) {
            this.form.msgDefList.push({
                day: val ? val.day : '',
                msgInfoType: val ? val.msgInfoType : '',
                hour: val ? val.hour : '',
                msgTitle: val ? val.msgTitle : {html: '', res: ''},                       //提醒标题
                msgType: val ? val.msgType : ['1'],                //提醒方式
                msgRecipients: val ? val.msgRecipients : '',             //任务接收人
                msgMemberRefList: val ? val.msgMemberRefList : [],       //接收人列表
                rosterDate: window.bizDate,                          //任务rosterDate
                msgTimeType: val ? val.msgTimeType : '',                //时效类型 1-提前N个工作日，2-提前N个自然日
                msgTime: val ? val.day + "-" + val.hour : '',               //提醒时间
                msgContent: val ? val.msgContent : {html: '', res: ''},                   //提醒内容
            })
        },
      async openDialog(id, content, index, title) {
          let dataType = this.reTaskDef.caseKey;
          this.index = index;
          this.id = `${id}${index}-dlg`;
          this.$dialog.create({
              title: title,
              width: '900px',
              component: TextareaDialog,
              closeOnClickModal: false,
              args: {dataType: dataType, id: this.id, innerHtml: content.html, actionOk: this.setContent.bind(this)}
          })
        },
        setContent(content, data) {
            if (this.index === 999) {
                if (this.id.indexOf('taskTitleInfo') !== -1) {
                    this.form.taskTitle.html = content
                    this.form.taskTitle.res = data
                } else {
                    this.form.taskContent.html = content
                    this.form.taskContent.res = data
                }
            } else {
                if (this.id.indexOf('msgTitleInfo') !== -1) {
                    this.form.msgDefList[this.index].msgTitle.html = content
                    this.form.msgDefList[this.index].msgTitle.res = data
                } else {
                    this.form.msgDefList[this.index].msgContent.html = content
                    this.form.msgDefList[this.index].msgContent.res = data
                }
            }
        },
        setMsgTime(index) {
            let msgTimeType = this.form.msgDefList[index].msgTimeType;
            this.form.msgDefList[index].day =
                (this.form.msgDefList[index].day === '0' || this.form.msgDefList[index].day === '*'
                    || this.form.msgDefList[index].day === '#') ? '' : this.form.msgDefList[index].day;
            let day = '';
            if (msgTimeType === '1') {
                day = '0';//基准日当天
            } else if (msgTimeType === '6') {
                day = '*';//每天提醒
            } else if (msgTimeType === '7') {
                day = '#';//超时效提醒
            } else {
                day = this.form.msgDefList[index].day;
            }
            let hour = this.form.msgDefList[index].hour;
            let msgTime = {msgTime: (day + "-" + hour)};
            this.form.msgDefList[index].msgTime = msgTime.msgTime;
        },
        check(msgTimeType) {
            if (msgTimeType === '1') {
                return false;
            }
            if (msgTimeType === '6') {
                return false;
            }
            if (msgTimeType === '7') {
                return false;
            }
            return true;
        },
    }
}
</script>

<style scoped>
.work-process-notification-form {
    padding: 0 10px;
}

.event-strategy-form {
    padding: 0px !important;
}

.msg-setting {
    width: 100%;
    border: #0f5eff 1px dashed;
    margin-top: 10px
}

.msg-setting /deep/ .el-form-item {
    width: 98%;
}

.el-form-item-processTime {
    max-width: 30%;
    float: left;
    margin-left: 10px;
}

.el-form-item-time {
    width: 35%;
    float: left;
}

.msg-copy-delete {
    width: 100%;
    position: relative;
    align-items: center;
    padding-left: 20px;
    font-size: 20px;
}

.msg-copy-delete /deep/ .el-button {
    color: #0f5eff;
    width: 30px;
}

.add-msg-btn {
    width: 100px;
    margin-top: 10px;
}

.add-msg-btn /deep/ .el-button {
    width: 100%;
    color: #0f5eff;
    border: #0f5eff 1px dashed;
    background: none;
}

.add-msg-btn /deep/ .el-button :active,
.add-msg-btn /deep/ .el-button :hover {
    background: none;
}
</style>