<template>
    <div>
        <div>
            <el-form ref="form" class="work-process-basic-info-form" :model="caseStepDef" :rules="rules" :disabled="optionType === 'view'" label-width="80px">
                <div class="line">
                    <el-form-item label="任务名称:" prop="stepName">
                        <gf-input v-model="caseStepDef.stepName" :max-byte-len="50"></gf-input>
                    </el-form-item>
                </div>

                <div class="line" v-if="args.stepExecType === 'mult-step'">
                    <el-form-item label="积木类型:" prop="dealType">
                        <el-select v-model="caseStepDef.dealType" @change="initBlockData" clearable filterable placeholder="请选择积木类型">
                            <gf-filter-option v-for="item in dealTypeOptions" :key="item.dictId" :label="item.dictName" :value="item.dictId"></gf-filter-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div class="line" v-if="args.stepExecType === 'mult-step' && caseStepDef.dealType">
                    <el-form-item label="积木块:" prop="blockCode">
                        <el-select v-model="caseStepDef.blockCode" clearable filterable @change="changeBlock" placeholder="请选择积木块">
                            <gf-filter-option v-for="item in blockInfoOptions" :key="item.blockCode" :label="item.blockName" :value="item.blockCode"></gf-filter-option>
                        </el-select>
                    </el-form-item>
                </div>
                <div class="line" v-if="!args.dealType.match(/12|21|28/) && args.stepExecType != 'mult-step'">
                    <el-form-item label="积木块:" prop="blockCode">
                        <el-select v-model="caseStepDef.blockCode" clearable filterable @change="changeBlock" placeholder="请选择积木块">
                            <gf-filter-option v-for="item in blockInfoOptions" :key="item.blockCode" :label="item.blockName" :value="item.blockCode"></gf-filter-option>
                        </el-select>
                    </el-form-item>
                </div>

                <div class="line" v-if="args.dealType === '12' || args.dealType === '08'">
                    <el-form-item label="表单:" prop="pageCode">
                        <el-select v-model="caseStepDef.pageCode" clearable placeholder="请选择表单">
                            <gf-filter-option v-for="item in objPageOption" :key="item.pageCode" :label="item.pageName" :value="item.pageCode"></gf-filter-option>
                        </el-select>
                    </el-form-item>
                </div>
              <div class="line" v-if="args.dealType==='28'">
                <el-form-item label="脚本:" prop="pageCode">
                  <el-select v-model="caseStepDef.dealId"
                             clearable
                             @change="fundChange"
                             placeholder="请选择脚本">
                    <gf-filter-option
                        v-for="item in sqlIdList"
                        :key="item.fnCode"
                        :label="item.fnName"
                        :value="item.fnCode">
                    </gf-filter-option>
                  </el-select>
                </el-form-item>
              </div>
              <div class="line rule-table" v-if="args.dealType==='28' && caseStepDef.paramsInData.length">
                <el-form-item label="入参:" prop="paramsInData">
                  <el-table
                      :data="caseStepDef.paramsInData"
                      border
                      stripe
                      header-row-class-name="rule-header-row hidden"
                      row-class-name="rule-header-row"
                      cell-class-name="rule-header-cell">
                    <el-table-column  v-for="item in this.paramsInRows" :prop="item.prop" :key="item.prop" :label="item.name" :width="item.prop === 'fieldName' ? '100px' : 'auto'">
                      <template slot-scope="scope">
                        <template v-if="item.prop === 'fieldName'">{{scope.row[item.prop]}}</template>
                        <template v-if="item.prop === 'fieldValue'">
                          <tag-textarea :id="'url'"
                                        class="ellipsis"
                                        v-model="scope.row[item.prop].html"
                                        @click.native="openDialog('fund', scope.row[item.prop], scope.$index)"
                                        style="height: 35px">
                          </tag-textarea>
                        </template>
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form-item>
              </div>
                <div class="line rule-table" v-if="caseStepDef.blockCode && caseStepDef.paramsInData.length">
                    <el-form-item label="入参:" prop="paramsInData">
                        <el-table
                            :data="caseStepDef.paramsInData"
                            border
                            stripe
                            header-row-class-name="rule-header-row hidden"
                            row-class-name="rule-header-row"
                            cell-class-name="rule-header-cell">
                            <el-table-column  v-for="item in paramsInRows" :prop="item.prop" :key="item.prop" :label="item.name" :width="item.prop === 'fieldName' ? '100px' : 'auto'">
                                <template slot-scope="scope">
                                    <template v-if="item.prop === 'fieldName'">{{scope.row[item.prop]}}</template>
                                    <template v-if="item.prop === 'fieldValue'">
                                        <tag-textarea :id="'url'"
                                                      class="ellipsis"
                                                      v-model="scope.row[item.prop].html"
                                                      @click.native="openDialog('paramsIn', scope.row[item.prop], scope.$index)"
                                                      style="height: 35px">
                                        </tag-textarea>
                                    </template>
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-form-item>
                </div>
                <viewBLockDef class="view-block-def" v-if="caseStepDef.blockCode && args.stepExecType !== 'mult-step' && caseStepDef.stepExecType !== 'mult-step'" :dealInfo="blockInfo" :blockCode="caseStepDef.blockCode" mode="caseView" :dealType="args.dealType"></viewBLockDef>
                <blockConfig v-if="args.stepExecType === 'mult-step' || caseStepDef.stepExecType === 'mult-step'" :re-task-def="reTaskDef" :case-step-def="initBlockParam" @getBlockData="getBlockData"></blockConfig>
            </el-form>
        </div>
    </div>
</template>

<script>
import viewBLockDef from './view-block-def.vue';
import TextareaDialog from '../../../../../../components/biz/tag-textarea/textarea-dialog';
import blockConfig from './block-config';
export default {
    name: 'base-info',
    components: {
        viewBLockDef,
        blockConfig
    },
    props: {
        basicInfo: {
            type: Object
        },
        reTaskDef: {
            type: Object
        },
        optionType: {
            type: String,
            required: true
        },
        stepCodeArr: {
            type: Object
        },
        args: {
            type: Object
        }
    },
    data() {
        const checkParamsInData = (rule, value, callback) => {
            if (value && value.length > 0) {
                let flag = true;
                value.forEach(item => {
                    if (!item.fieldValue.html || !item.fieldValue.res) {
                        flag = false;
                    }
                });
                if (!flag) {
                    callback(new Error('入参值不能为空'));
                } else {
                    callback();
                }
            } else {
                callback();
            }
        };
        return {
            checklistTypeOps: this.$app.dict.getDictItems('AGNES_CHECKLIST_TYPE'),
            dealTypeOptions: this.$app.dict.getDictItems('AGNES_MULTI_BLOCK_DEAL_TYPE'),
            stepFormInfo: {
                caseStepDef: {
                    stepExecType: '',
                    blockCode: '',
                    stepName: '',
                    stepActType: '',
                    dealType: '',
                    dealId: '',
                    pageCode: '',
                    objCode: '',
                    stepDefVos: [],
                    childBlocks: [],
                    paramsInData: []
                }
            },
            blockInfoOptions: [],
            bizObjTypeOptions: [],
            objPageOption: [],
            sqlIdList: [],
            fundInParams: [],
            rules: {
                stepName: [{ required: true, message: '任务名称必填', trigger: 'blur' }],
                dealType: [{ required: true, message: '请选择积木类型', trigger: 'change' }],
                blockCode: [{ required: true, message: '请选择积木类型', trigger: 'change' }],
                paramsInData: [{ required: true, trigger: 'blur', validator: checkParamsInData }]
            },
            paramsInRows: [
                {
                    name: '参数名',
                    prop: 'fieldName',
                },
                {
                    name: '值',
                    prop: 'fieldValue',
                },
            ],
            //用于处理积木块反显内容的参数
            blockInfo: {},
            initBlockParam: {}
        };
    },
    computed: {
        caseStepDef() {
            return this.stepFormInfo.caseStepDef;
        }
    },
    beforeMount() {
        // 业务标签下拉
        this.caseStepDef.stepActType = this.args.stepData;
        if (this.optionType !== 'add') {
            this.basicInfoGetData();
        }
        if (this.args.stepExecType) {
            this.caseStepDef.stepExecType = this.args.stepExecType;
        }
        if (this.args.dealType) {
            this.caseStepDef.dealType = this.args.dealType;
        }
        this.getSqlList();
        this.initBlockData();
        this.initBizObjData();
        this.changeObjCode();
    },
    methods: {
        async initBizObjData() {
            const bizObjData = await this.$api.businessObjectApi.getBizObjList();
            if (bizObjData && bizObjData.data) {
                this.bizObjTypeOptions = bizObjData.data;
            }
        },
        fundChange(val) {
            this.stepFormInfo.caseStepDef.paramsInData = [];
            this.sqlIdList.forEach(item => {
                if (item.fnCode === val) {
                    let mock = item.fnArgsModelFields ? item.fnArgsModelFields : [];
                    this.stepFormInfo.caseStepDef.paramsInData = mock.map(item1 => {
                        return {
                            fieldName: item1.fieldName,
                            fieldCode: item1.fieldKey,
                            fieldValue: {
                                res: '',
                                html: ''
                            }
                        };
                    });
                }
            });
        },
        basicInfoGetData() {
            const { blockCode, stepName, stepActType, dealType, dealId, objCode, pageCode, paramsInData, stepExecType } = this.basicInfo.caseStepDef;
            this.stepFormInfo.caseStepDef = { blockCode, stepName, stepActType, dealType, dealId, objCode, pageCode, paramsInData, stepExecType };
            this.initBlockParam = {
                childBlocks: this.basicInfo.caseStepDef.childBlocks,
                stepDefVos: this.basicInfo.caseStepDef.stepDefVos
            };
        },
        async changeObjCode() {
            try {
                if (this.reTaskDef.caseKey) {
                    this.caseStepDef.objCode = this.reTaskDef.caseKey;
                    const p = this.$api.businessObjectApi.getObjPageByObjCode(this.reTaskDef.caseKey);
                    let resp = await this.$app.blockingApp(p);
                    this.objPageOption = resp.data;
                } else {
                    this.objPageOption = [];
                }
            } catch (e) {
                this.$msg.error(e);
            }
        },
        async getSqlList() {
            try {
                const p = this.$api.ruleTableApi.getFnAndModelfields();
                let resp = await this.$app.blockingApp(p);
                this.sqlIdList = resp.data;
            } catch (e) {
                this.$msg.error(e);
            }
        },
        //验证表单规则
        validateForm() {
            let ok = null;
            this.$refs['form'].validate(validate => {
                ok = validate;
            });
            return ok;
        },
        getData() {
            let data = null;
            this.$refs['form'].validate(validate => {
                if (!validate) {
                    data = false;
                } else {
                    if (this.caseStepDef.stepActType === '05') {
                        if (!this.caseStepDef.objCode || !this.caseStepDef.pageCode) {
                            this.stepFormInfo.caseStepDef.dealType = '21';
                        } else {
                            this.stepFormInfo.caseStepDef.dealType = '12';
                        }
                    }
                    let result = true;
                    this.stepFormInfo.caseStepDef.dealId = this.caseStepDef.dealId;
                    if (this.args.stepExecType === 'mult-step') {
                        result = this.validateChildBlockIndex();
                    } else {
                        this.stepFormInfo.caseStepDef.dealType = this.args.dealType;
                    }
                    if (result) {
                        data = { stepFormInfo: this.stepFormInfo };
                    } else {
                        data = result;
                    }
                }
            });
            return data;
        },
        async initBlockData() {
            const blockData = await this.$api.blockApi.query({ dealType: this.caseStepDef.dealType, status: '03' });
            if (blockData && blockData.data) {
                this.blockInfoOptions = blockData.data;
                //初次进入赋值积木详情
                this.dealBlockData();
            }
        },
        dealBlockData() {
            let blockInfo = this.blockInfoOptions.find(item => item.blockCode === this.caseStepDef.blockCode);
            if (blockInfo) {
                this.stepFormInfo.caseStepDef.dealId = blockInfo.dealId;
            }
            //caseview字段用于在积木块显示时，做响应的展示字段及禁用处理
            if (blockInfo?.dealType.match(/01|09/)) {
                this.blockInfo = {
                    ...blockInfo.acReInterfaceDef,
                    ...blockInfo,
                    caseView: true
                };
            } else {
                this.blockInfo = {
                    ...blockInfo,
                    caseView: true
                };
            }
        },
        async changeBlock() {
            if (this.caseStepDef.blockCode) {
                let item = this.blockInfoOptions.filter(item2 => {
                    return item2.blockCode === this.caseStepDef.blockCode;
                });
                this.$app.runCmd('case-step-change-block', item);
            } else {
                this.$app.runCmd('case-step-change-block', { blockCode: this.caseStepDef.blockCode });
            }

            this.dealBlockData();
            if (!this.caseStepDef.blockCode) {
                this.stepFormInfo.caseStepDef.paramsInData = [];
                return;
            }
            let resp;
            /*         if (this.args.dealType === '02'){
           const p = this.$api.dataPipeApi.queryParamDefMsg({'dealId':this.caseStepDef.dealId});
           resp = await this.$app.blockingApp(p);
         }else {*/
            const p = this.$api.blockApi.queryBlockParams({ blockCode: this.caseStepDef.blockCode, paramType: 'in' });
            resp = await this.$app.blockingApp(p);
            //}
            if (resp.data) {
                let mock = resp.data;
                this.stepFormInfo.caseStepDef.paramsInData = mock.map(item => {
                    return {
                        fieldName: item.fieldName,
                        fieldCode: item.fieldCode,
                        fieldValue: {
                            res: '',
                            html: ''
                        }
                    };
                });
            }
        },
        async openDialog(id, content, index) {
            let dataType = this.reTaskDef.caseKey;
            this.index = index;
            this.id = `${id}${index}-dlg`;
            this.$dialog.create({
                title: '属性字段',
                width: '900px',
                component: TextareaDialog,
                closeOnClickModal: false,
                args: { dataType: dataType, id: this.id, innerHtml: content.html, actionOk: this.setContent.bind(this) }
            });
        },
        setContent(content, data) {
            this.stepFormInfo.caseStepDef.paramsInData[this.index].fieldValue.html = content;
            this.stepFormInfo.caseStepDef.paramsInData[this.index].fieldValue.res = data;
        },
        getBlockData(data, stepDefVosData) {
            this.stepFormInfo.caseStepDef.childBlocks = data;
            this.stepFormInfo.caseStepDef.stepDefVos = stepDefVosData;
        },
        validateChildBlockIndex() {
            let flag = true;
            if (this.stepFormInfo.caseStepDef.childBlocks) {
                this.stepFormInfo.caseStepDef.childBlocks.forEach(item => {
                    if (!item.childBlockType || !item.childBlockCode) {
                        flag = false;
                    }
                });
                if (!flag) {
                    return false;
                }
            }
            return flag;
        }
    }
};
</script>

<style scoped>
.line >>> .el-form-item {
    width: 90% !important;
}

.hidden {
    display: none;
}
</style>
