<template>
  <div>
    <div>
      <el-form
        ref="form"
        class="work-process-basic-info-form"
        :model="caseStepDef"
        :rules="rules"
        :disabled="optionType === 'view'"
        label-width="80px"
      >
        <div class="line">
          <el-form-item label="任务名称:" prop="stepName">
            <gf-input v-model="caseStepDef.stepName" :max-byte-len="50"></gf-input>
          </el-form-item>
        </div>
        <div class="line">
          <el-form-item label="发送方式:" prop="sendType">
            <el-checkbox-group v-model="sendTypes" @change="sendTypesChange">
              <el-checkbox label="email"></el-checkbox>
              <el-checkbox label="wechat"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>
        <div class="line">
          <el-form-item label="发送至:">
            <div
              v-for="(msgDef, index) in caseStepDef.emailDefList"
              :key="index"
              class="msg-setting"
            >
              <div class="msg-copy-delete">
                <el-tooltip effect="light" content="删除" placement="top">
                  <el-button
                    type="text"
                    @click="removes(index)"
                    style="color: red"
                  >
                    <i class="el-icon-delete"></i
                  ></el-button>
                </el-tooltip>
                <el-tooltip effect="light" content="复制" placement="top">
                  <el-button type="text" @click="addEmailDef(msgDef)">
                    <i class="el-icon-copy-document"></i
                  ></el-button>
                </el-tooltip>
              </div>
              <el-form-item v-show="caseStepDef.isSendEmailAlone === '0'">
                <el-radio-group v-model="msgDef.userType">
                  <el-radio label="1">收件人</el-radio>
                  <el-radio label="2">抄送人</el-radio>
                  <el-radio label="3">密送人</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item>
                <el-select
                  style="width: 100%"
                  v-model="msgDef.emailType"
                  placeholder="请选择"
                  filterable
                  clearable
                >
                  <gf-filter-option
                    v-for="item in emailOptions"
                    :key="item.key"
                    :label="item.value"
                    :value="item.key"
                  >
                  </gf-filter-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="msgDef.emailType === 'sryx'">
                <el-input v-model="msgDef.emailValue"></el-input>
              </el-form-item>
              <el-form-item v-if="msgDef.emailType === 'func'">
                <el-select
                  style="width: 100%"
                  v-model="msgDef.emailValue"
                  placeholder="请选择"
                  filterable
                  clearable
                >
                  <gf-filter-option
                    v-for="item in funcOptions"
                    :key="item.fnId"
                    :label="item.fnName"
                    :value="item.fnId"
                  >
                  </gf-filter-option>
                </el-select>
              </el-form-item>
              <el-form-item v-if="msgDef.emailType === 'xzlxr'">
                <gf-person-chosen
                  ref="memberRef"
                  style="float: left; width: 80%"
                  :disabled="false"
                  :memberRefList="msgDef.memberRefList"
                  chosenType="user,group,depart,role"
                  :index="index"
                  @getMemberList="getMsgMemberList"
                >
                </gf-person-chosen>
              </el-form-item>
              <el-form-item v-if="msgDef.emailType === 'xzmxzd'">
                <tag-textarea
                  :id="'emailValue'"
                  class="ellipsis"
                  v-model="msgDef.emailValueP.html"
                  @click.native="
                    openDialog('emailValue', msgDef.emailValueP, index)
                  "
                  style="height: 32px"
                >
                </tag-textarea>
              </el-form-item>
            </div>
            <div class="add-msg-btn">
              <el-button @click="addEmailDef('')" size="mini"
                ><i class="el-icon-plus"></i>新增</el-button
              >
            </div>
          </el-form-item>
        </div>
        <div class="line">
          <el-form-item>
            <el-checkbox
              style="margin-left: 10px"
              v-model="caseStepDef.isSendEmailAlone"
              :true-label="'1'"
              :false-label="'0'"
              >独立发送邮件</el-checkbox
            >
          </el-form-item>
        </div>
        <div class="line">
          <el-form-item>
            <el-checkbox
              style="margin-left: 10px"
              v-model="caseStepDef.isSendWechat"
              :true-label="'1'"
              :false-label="'0'"
              >发送指定群聊</el-checkbox
            >
          </el-form-item>
          <el-form-item
            class="is-required"
            label="群聊ID"
            prop="remindBcc"
            v-if="caseStepDef.isSendWechat === '1'"
          >
            <el-select
              v-model="caseStepDef.chatPkId"
              placeholder="请选择"
              filterable
              clearable
            >
              <gf-filter-option
                v-for="item in chatOptions"
                :key="item.chatPkId"
                :label="item.chatName"
                :value="item.chatId"
              >
              </gf-filter-option>
            </el-select>
          </el-form-item>
        </div>
        <div class="line">
          <el-form-item label="邮件主题:" v-if="showEmailInfo">
            <tag-textarea
              :id="'emailSubject'"
              class="ellipsis"
              v-model="caseStepDef.emailSubject.html"
              @click.native="
                openDialog('emailSubject', caseStepDef.emailSubject, '6666')
              "
              style="height: 32px"
            >
            </tag-textarea>
          </el-form-item>
        </div>
        <div class="line">
          <el-form-item label="邮件正文:" v-show="showEmailInfo">
            <tag-textarea
              :id="'emailContent'"
              class="ellipsis"
              v-model="caseStepDef.emailContent.html"
              @click.native="
                openDialog('emailSubject', caseStepDef.emailContent, '9999')
              "
              style="height: 80px; overflow: scroll"
            >
            </tag-textarea>
          </el-form-item>
        </div>
        <div class="line">
          <el-form-item label="微信正文:" v-show="showChatInfo">
            <tag-textarea
              :id="'wechatContent'"
              class="ellipsis"
              v-model="caseStepDef.wechatContent.html"
              @click.native="
                openDialog('wechatContent', caseStepDef.wechatContent, '7799')
              "
              style="height: 80px; overflow: scroll"
            >
            </tag-textarea>
          </el-form-item>
        </div>
        <div
          class="line"
          v-if="
            caseStepDef.sendType.includes('email') ||
            caseStepDef.sendType.includes('wechat')
          "
        >
          <el-form-item>
            <el-checkbox
              style="margin-left: 10px"
              v-model="caseStepDef.isSendFiles"
              :true-label="'1'"
              @change="isSendFilesChange"
              :false-label="'0'"
              >发送附件</el-checkbox
            >
          </el-form-item>
        </div>
        <div class="line">
          <el-form-item label="发送时间:">
            <div
              v-for="(sendInfo, index) in caseStepDef.sendInfoList"
              :key="index"
              class="msg-setting"
            >
              <div class="msg-copy-delete">
                <el-tooltip effect="light" content="删除" placement="top">
                  <el-button
                    type="text"
                    @click="removeSendInfo(index)"
                    style="color: red"
                  >
                    <i class="el-icon-delete"></i
                  ></el-button>
                </el-tooltip>
                <el-tooltip effect="light" content="复制" placement="top">
                  <el-button type="text" @click="addSendInfo(sendInfo)">
                    <i class="el-icon-copy-document"></i
                  ></el-button>
                </el-tooltip>
              </div>
              <div class="line">
                <el-form-item class="el-form-item-time" style="width: 35%">
                  <gf-dict-select
                    placeholder="请选择提醒时间类型"
                    dict-type="MSG_TIME_TYPE"
                    v-model="sendInfo.timeType"
                  >
                  </gf-dict-select>
                </el-form-item>
                <el-form-item
                  v-if="check(sendInfo.timeType)"
                  style="width: 35%"
                >
                  <el-form-item
                    class="el-form-item-time"
                    style="width: 80%; float: left"
                  >
                    <gf-input
                      v-model.number="sendInfo.day"
                      placeholder="天数"
                      :max-byte-len="50"
                    ></gf-input>
                  </el-form-item>
                  <div style="width: 20%; float: left; text-align: right">
                    天
                  </div>
                </el-form-item>
                <el-form-item
                  style="float: left; margin-left: 10px; width: 60%"
                >
                  <el-form-item
                    class="el-form-item-time"
                    style="float: left; width: 50%"
                  >
                    <el-time-picker
                      v-model="sendInfo.hour"
                      :picker-options="timeForDay"
                      placeholder="时间"
                      format="HH:mm"
                      value-format="HH:mm"
                    >
                    </el-time-picker>
                  </el-form-item>
                  <div style="float: left; margin-left: 5px">时</div>
                </el-form-item>
              </div>
            </div>
            <div class="add-msg-btn">
              <el-button @click="addSendInfo('')" size="mini"
                ><i class="el-icon-plus"></i>新增</el-button
              >
            </div>
          </el-form-item>
        </div>
        <div class="line">
          <el-form-item v-if="caseStepDef.isSendFiles === '1'">
            <el-radio-group
              v-model="caseStepDef.sendFileType"
              @change="sendFileTypeChange"
            >
              <el-radio label="1">case中所有附件</el-radio>
              <el-radio label="2">选择附件</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div class="line" v-if="caseStepDef.isSendFiles === '1'">
          <el-form-item v-if="caseStepDef.sendFileType === '2'">
            <tag-textarea
              :id="'emailFile'"
              class="ellipsis"
              v-model="caseStepDef.emailFile.html"
              @click.native="
                openDialog('emailFile', caseStepDef.emailFile, '6699')
              "
              style="height: 32px"
            >
            </tag-textarea>
          </el-form-item>
        </div>
      </el-form>
    </div>
  </div>
</template>

<script>
import TextareaDialog from "../../../../../../components/biz/tag-textarea/textarea-dialog";
import richTextDialog from "../../../../../../components/biz/tag-textarea/rich-text-dialog.vue";
export default {
    name: "base-info",
    components:{
    },
      props:{
          basicInfo: {
              type: Object
          },
          reTaskDef: {
              type: Object
          },
        optionType: {
              type:String,
              required: true
          },
        stepCodeArr: {
          type: Object
        },
        args: {
          type: Object
        },
      },
    data() {
        return {
            chatOptions: [],
            showChatInfo:false,
            showEmailInfo:false,
            sendTypes: [],
          stepFormInfo: {
            caseStepDef: {
                blockCode: '',
                sendType: '',
                isSendEmailAlone: '0',
                isSendWechat: '0',
                emailSubject:{html:'',res:''},
                wechatContent:{html:'',res:''},
                emailContent:{html:'',res:''},
                emailFile:{html:'',res:''},
                stepName:'',
                stepActType:'',
                dealType:'',
                dealId:'',
                pageCode:'',
                objCode:'',
                chatPkId:'',
                sendInfoStr:'',
                sendInfoList: [],
                paramsInData: [],
                emailDefList: [],
            },
          },
            funcOptions:[],
            timeForDay: null,
          emailOptions: [{prop:'sryx',value:'输入邮箱'},{prop:'xzlxr',value:'选择联系人'},{prop:'xzmxzd',value:'选择数据模型字段'},{prop:'func',value:'选择函数'},],
          rules: {
            stepName: [{required: true, message: '任务名称必填', trigger: 'blur'},],
              sendType: [{required: true, message: '请选择发送方式', trigger: 'blur'},],
          },
        }
    },
  computed: {
    caseStepDef() {
      return this.stepFormInfo.caseStepDef;
    },
  },
  beforeMount() {
    // 业务标签下拉
    this.caseStepDef.stepActType = this.args.stepData;
    if (this.optionType !== "add") {
      this.basicInfoGetData();
    }
    this.initDict();
  },
  methods: {
    removes(index) {
      this.caseStepDef.emailDefList.splice(index, 1);
    },
    removeSendInfo(index) {
      this.caseStepDef.sendInfoList.splice(index, 1);
    },
    addSendInfo(val) {
      this.caseStepDef.sendInfoList.push({
        timeType: val ? val.timeType : "",
        day: val ? val.day : "",
        hour: val ? val.hour : "",
      });
    },
    addEmailDef(val) {
      this.caseStepDef.emailDefList.push({
        emailType: val ? val.emailType : "",
        emailValue: val ? val.emailValue : "",
        recipients: val ? val.recipients : "",
        memberRefList: val ? val.memberRefList : [],
        emailValueP: val ? val.emailValueP : { html: "", res: "" },
      });
    },
    sendTypesChange(val) {
      let value = "";
      val.forEach((item) => {
        if (item != "[]") value += item + ",";
      });
      if (val) {
        this.caseStepDef.sendType = value.substring(0, value.length - 1);
      } else {
        this.caseStepDef.sendType = "";
      }
      if (
        this.caseStepDef.sendType &&
        this.caseStepDef.sendType.includes("email")
      ) {
        this.showEmailInfo = true;
      } else {
        this.showEmailInfo = false;
        this.caseStepDef.isSendFiles = "0";
        this.isSendFilesChange();
      }
      if (
        this.caseStepDef.sendType &&
        this.caseStepDef.sendType.includes("wechat")
      ) {
        this.showChatInfo = true;
      } else {
        this.showChatInfo = false;
      }
    },
    getMsgMemberList(val, index) {
      this.caseStepDef.emailDefList[index].memberRefList = val;
      this.caseStepDef.emailDefList[index].recipients = JSON.stringify(val);
    },
    basicInfoGetData() {
      const {
        blockCode,
        stepName,
        stepActType,
        dealType,
        dealId,
        objCode,
        isSendEmailAlone,
        isSendWechat,
        chatPkId,
        sendType,
        isSendFiles,
        sendFileType,
        timeType,
        sendInfoStr,
        sendInfoList,
        emailDefList,
      } = this.basicInfo.caseStepDef;
      this.stepFormInfo.caseStepDef = {
        blockCode,
        stepName,
        stepActType,
        dealType,
        dealId,
        objCode,
        isSendEmailAlone,
        isSendWechat,
        chatPkId,
        sendType,
        isSendFiles,
        sendFileType,
        timeType,
        sendInfoStr,
        sendInfoList,
        emailDefList,
      };
      if (!this.stepFormInfo.caseStepDef.sendInfoList) {
        this.stepFormInfo.caseStepDef.sendInfoList = [];
      }
      if (
        this.basicInfo.caseStepDef.sendType &&
        this.basicInfo.caseStepDef.sendType != "[]"
      ) {
        if (this.caseStepDef.sendType.includes("wechat")) {
          this.showChatInfo = true;
        }
        if (this.caseStepDef.sendType.includes("email")) {
          this.showEmailInfo = true;
        }
        this.sendTypes = this.basicInfo.caseStepDef.sendType.split(",");
      }
      if (this.basicInfo.caseStepDef.emailSubject) {
        this.stepFormInfo.caseStepDef.emailSubject = JSON.parse(
          this.basicInfo.caseStepDef.emailSubject
        );
      }
      if (this.basicInfo.caseStepDef.emailContent) {
        this.stepFormInfo.caseStepDef.emailContent = JSON.parse(
          this.basicInfo.caseStepDef.emailContent
        );
      }
      if (this.basicInfo.caseStepDef.wechatContent) {
        this.stepFormInfo.caseStepDef.wechatContent = JSON.parse(
          this.basicInfo.caseStepDef.wechatContent
        );
      } else {
        this.stepFormInfo.caseStepDef.wechatContent = { html: "", res: "" };
      }
      if (this.basicInfo.caseStepDef.emailFile) {
        this.stepFormInfo.caseStepDef.emailFile = JSON.parse(
          this.basicInfo.caseStepDef.emailFile
        );
      }
    },
    //验证表单规则
    validateForm() {
      let ok = null;
      this.$refs["form"].validate((validate) => {
        ok = validate;
      });
      return ok;
    },
    getData() {
      let data = null;
      const ok =  this.$refs["form"].validate()
      if (!ok) {
        return
      }        
      if (this.checkSendType()) {
        return;
      }
      if (this.caseStepDef.emailSubject) {
        this.stepFormInfo.caseStepDef.emailSubject = JSON.stringify(
          this.stepFormInfo.caseStepDef.emailSubject
        );
      }
      if (this.caseStepDef.emailContent) {
        this.stepFormInfo.caseStepDef.emailContent = JSON.stringify(
          this.stepFormInfo.caseStepDef.emailContent
        );
      }
      if (this.caseStepDef.sendInfoList) {
        this.caseStepDef.sendInfoStr = JSON.stringify(
          this.caseStepDef.sendInfoList
        );
      }
      if (this.caseStepDef.wechatContent) {
        this.stepFormInfo.caseStepDef.wechatContent = JSON.stringify(
          this.stepFormInfo.caseStepDef.wechatContent
        );
      }
      if (this.caseStepDef.emailFile) {
        this.stepFormInfo.caseStepDef.emailFile = JSON.stringify(
          this.stepFormInfo.caseStepDef.emailFile
        );
      }
      if (this.caseStepDef.isSendFiles == "0") {
        this.caseStepDef.sendFileType = "";
        this.stepFormInfo.caseStepDef.emailFile = "";
      }
      this.stepFormInfo.caseStepDef.dealType = this.args.dealType;
      this.stepFormInfo.caseStepDef.dealId = this.caseStepDef.dealId;
      data = { stepFormInfo: this.stepFormInfo };
      return data;
    },
    /**
     * 校验sendType
     */
    checkSendType() {
      return !this.caseStepDef.sendType ||  this.caseStepDef.sendType === "[]"
    },
    async initDict() {
      const resp2 = await this.$api.funDefineApi.queryFunList();
      if (resp2.data) {
        this.funcOptions = resp2.data;
      }
      const c = this.$api.ruleTableApi.getChatList({});
      const resp = await this.$app.blockingApp(c);
      if (resp) {
        this.chatOptions = resp;
      }
    },
    async openDialog(id, content, index) {
      let dataType = this.reTaskDef.caseKey;
      this.index = index;
      this.id = `${id}${index}-dlg`;
      let component = TextareaDialog;
      if (this.index === "9999") {
        component = richTextDialog;
      }
      this.$dialog.create({
        title: "属性字段",
        width: "900px",
        component: component,
        closeOnClickModal: false,
        args: {
          dataType: dataType,
          id: this.id,
          innerHtml: content.html,
          actionOk: this.setContent.bind(this),
        },
      });
    },
    setContent(content, data) {
      if (this.index === "6666") {
        this.stepFormInfo.caseStepDef.emailSubject.html = content;
        this.stepFormInfo.caseStepDef.emailSubject.res = data;
      } else if (this.index === "9999") {
        this.$set(this.stepFormInfo.caseStepDef.emailContent, "html", content);
        this.$set(this.stepFormInfo.caseStepDef.emailContent, "res", data);
      } else if (this.index === "7799") {
        this.$set(this.stepFormInfo.caseStepDef.wechatContent, "html", content);
        this.$set(this.stepFormInfo.caseStepDef.wechatContent, "res", data);
      } else if (this.index === "6699") {
        this.stepFormInfo.caseStepDef.emailFile.html = content;
        this.stepFormInfo.caseStepDef.emailFile.res = data;
      } else {
        this.stepFormInfo.caseStepDef.emailDefList[
          this.index
        ].emailValueP.html = content;
        this.stepFormInfo.caseStepDef.emailDefList[this.index].emailValueP.res =
          data;
      }
      this.$forceUpdate();
    },
    sendFileChange() {
      this.caseStepDef.sendFileType = "1";
    },
    check(timeType) {
      if (timeType === "1") {
        return false;
      }
      if (timeType === "6") {
        return false;
      }
      if (timeType === "7") {
        return false;
      }
      return true;
    },
    isSendFilesChange() {
      this.$set(this.stepFormInfo.caseStepDef, "sendFileType", "");
      this.$set(this.stepFormInfo.caseStepDef, "emailFile", {
        html: "",
        res: "",
      });
      this.$set(this.caseStepDef, "emailFile", { html: "", res: "" });
      this.$forceUpdate();
    },
    sendFileTypeChange() {
      this.$set(this.stepFormInfo.caseStepDef, "emailFile", {
        html: "",
        res: "",
      });
      this.$set(this.caseStepDef, "emailFile", { html: "", res: "" });
      this.$forceUpdate();
    },
  },
};
</script>

<style scoped>
.work-process-basic-info-form {
  width: 90%;
}
</style>
