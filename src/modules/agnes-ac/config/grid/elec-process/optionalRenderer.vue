<template>
    <div class="optional-cell" v-if="params.data">

        <el-popover ref="popover"
                    placement="top-start"
                    :title="title"
                    width="400"
                    trigger="manual"
                    v-model="popoverVisible"
        >
            <el-form v-if="actionType=='actionConfirm'  || actionType=='forcePass' || actionType=='actionV2Mot'  || actionType=='finishDetailShow' || actionType=='reloadParam' || actionType=='editEndTime' || actionType=='autoConfirm' || actionType=='forcePassV2'" ref="form" :model="form"
                     label-width="80px" :rules="rules">


                <el-form-item v-for="item in form.paramList"
                              :label="item.paramName"
                              :key="item.paramKey"
                              :prop="item.paramKey">
                    <el-input type="textarea" v-model="item.paramString" :disabled="isShowFinishDetail" v-if="item.paramType=='string'"></el-input>
                    <el-input v-model="item.paramNumber" :disabled="isShowFinishDetail" oninput="value=value.replace(/[^\d]/g,'')"
                              maxLength='9' placeholder="请输入数字" v-if="item.paramType=='number'"/>
                    <el-input v-model.number="item.paramAmount" :disabled="isShowFinishDetail" placeholder="请输入金额" v-if="item.paramType=='amount'"/>
                    <el-date-picker
                            v-model="item.paramDate"
                            type="date"
                            placeholder="选择日期" :disabled="isShowFinishDetail" v-if="item.paramType=='date'">
                    </el-date-picker>
                    <gf-dict v-model="item.paramString" v-if="item.paramType === 'boolean'"
                             dict-type="GF_BOOL_TYPE"></gf-dict>

                    <el-select v-model="item.paramString" filterable clearable placeholder="请选择"
                               v-if="item.paramType === 'list'">
                        <el-option
                                v-for="item in getLists(item.paramValueRange)"
                                :key="item.value"
                                :label="item.name"
                                :value="item.value">
                        </el-option>
                    </el-select>
                    <gf-dict v-model="item.paramString" v-if="item.paramType === 'dict'"
                             :dict-type="item.paramValueRange"></gf-dict>
                </el-form-item>
                <el-form-item label="备注" v-show="isShowRemark">
                    <el-input type="textarea" :rows="1" placeholder="请输入备注内容"
                              v-model="form.remark" :maxlength="50">
                    </el-input>
                </el-form-item>
                <el-form-item label="结束时间" v-show="isShowEndTime">
                    <el-date-picker
                            v-model="form.planEndTime"
                            type="datetime"
                            value-format="yyyy-MM-dd HH:mm:ss"
                            placeholder="选择日期时间">
                    </el-date-picker>
                </el-form-item>
            </el-form>
            <el-table header-row-class-name="rule-header-row"
                      header-cell-class-name="rule-header-cell"
                      row-class-name="rule-row"
                      cell-class-name="rule-cell"
                      v-if="actionType=='autoTaskActionConfirm'"
                      :data="autoData"
                      border stripe
                      style="width: 100%"
                      :header-cell-style="{'text-align':'center'}">
                <el-table-column prop="stepCode" label="返回参数名">
                    <template slot-scope="scope">
                        <span>{{scope.row.fieldKey}}</span>
                    </template>
                </el-table-column>
                <el-table-column prop="stepCode" label="返回参数值">
                    <template slot-scope="scope">
                        <span>{{scope.row.fieldValue}}</span>
                    </template>
                </el-table-column>
            </el-table>
            <div style="text-align: right; margin-top: 10px">
                <el-button class="op-btn" size="mini" type="text" @click="closeRemake">取消</el-button>
                <el-button class="op-btn primary"
                           v-if="actionType=='actionConfirm'||actionType=='actionV2Mot'||actionType=='forcePass'||actionType=='editEndTime'||actionType=='reloadParam' || actionType=='autoConfirm' || actionType=='forcePassV2'"
                           size="mini" @click="confirmRemark">保存
                </el-button>
            </div>
        </el-popover>
        <el-button size="mini" type="text"
                   v-popover:popover v-if="actionShow"
                   @click="popoverClick('actionConfirm')"
                   title="手工确认"
                   :disabled="isDisabled  || !isCanExec"
        >
            <span class="svgSpan" v-html="svgImg.actionConfirm"></span>
        </el-button>
        <el-button size="mini" type="text"
                   v-popover:popover v-if="actionV2MotShow"
                   @click="popoverClick('actionV2Mot')"
                   title="手工确认"
                   :disabled="isDisabled  || !isCanExec"
        >
            <span class="svgSpan" v-html="svgImg.actionConfirm"></span>
        </el-button>
        <el-button size="mini" type="text"
                   v-popover:popover
                   v-if="autoSetShow"
                   @click="popoverClick('autoConfirm')"
                   title="干预通过"
                   :disabled="isDisabled  || !isCanExec"
        >
            <span class="svgSpan" v-html="svgImg.forcePass"></span>
        </el-button>
        <el-button size="mini" type="text"
                   v-popover:popover
                   v-if="indexSetShow"
                   @click="popoverClick('forcePass')"
                   title="干预通过"
                   :disabled="isDisabled  || !isCanExec"
        >
            <span class="svgSpan" v-html="svgImg.forcePass"></span>
        </el-button>

        <el-button size="mini" type="text"
                   v-popover:popover
                   v-if="forcePassV2"
                   @click="popoverClick('forcePassV2')"
                   title="干预通过"
                   :disabled="isDisabled  || !isCanExec"
        >
            <span class="svgSpan" v-html="svgImg.forcePass"></span>
        </el-button>
        <el-button size="mini" type="text"
                   class="detail-btn"
                   v-if="reExecuteShow"
                   :disabled="!isCanExec"
                   @click="reExecute('reExecute')"
                   title="重新执行"
        >
            <span class="svgSpan" v-html="svgImg.reExecute"></span>
        </el-button>
        <el-button v-if="indexDetailShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="indexDetail('showIndexDetail')"
                   title="查看明细"
                   :disabled="isKpiDisabled"
        >
        </el-button>
        <el-button v-if="showRpaDetail" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="indexDetail('showRpaDetail')"
                   title="查看明细"
                   :disabled="isRpaDisabled"
        >
        </el-button>
        <el-button v-if="reviewV2MotShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="indexDetail('reviewV2MotShow')"
                   title="查看明细"
                   :disabled="isKpiDisabled"
        >
        </el-button>
        <el-button v-if="indexircShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="indexDetail('showIndexDetail')"
                   title="查看明细"
                   :disabled="isKpiDisabled"
        >
        </el-button>
        <el-button v-if="indexJzwShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="indexDetail('showJzwDetail')"
                   title="查看明细"
                   :disabled="isKpiDisabled"
        >
        </el-button>
        <el-button size="mini" type="text"
                   v-popover:popover
                   class="show"
                   v-if="indexFileStepShow"
                   @click="indexDetail('autoConfirmFileStep')"
                   title="干预通过"
                   :disabled="isKpiDisabled  || !isCanExec"
        >
            <span class="svgSpan" v-html="svgImg.forcePass"></span>
        </el-button>
        <el-button v-if="indexFileStepShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="indexDetail('indexFileStepShow')"
                   title="查看明细"
                   :disabled="isKpiDisabled"
        >
        </el-button>

        <el-button v-if="lookFinishDetailShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="popoverClick('finishDetailShow')"
                   title="查看回填参数"
        >
        </el-button>
        <el-button v-if="openTradeDetailShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="openKjd('openTradeDetailShow')"
                   title="查看明细"
        >
        </el-button>
        <el-button v-if="dataAuditDetailShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="openDataAuditDetail('dataAuditDetailShow')"
                   title="查看明细"
        />
        <el-button size="mini" type="text"
                   @click="editEndTime('editEndTime')"
                   title="修改结束时间"
                   :disabled="isEditDisabled  || !isCanExec"
                   v-if="indexEditShow"

        >
            <span class="svgSpan" v-html="svgImg.editEndTime"></span>
        </el-button>
        <el-button size="mini" type="text"
                   v-if="finishDetailShow"
                   :disabled="!isCanExec"
                   @click="popoverClick('reloadParam')"
                   title="重新录入"
        >

            <span class="svgSpan" v-html="svgImg.reEntry"></span>
        </el-button>

      <el-button  type="text"
                  v-if="manualStartStepShow"
                  :disabled="!isCanExec"
                  @click="manualStartStep('manualStartStep')"
                  title="启动STEP"
      >
        <span class="svgSpan" v-html="svgImg.manualStartStep"></span>
      </el-button>
      <el-button  type="text"
                  :disabled="isFinished"
                  @click="taskChangeMethod('taskChange')"
                  title="任务转交"
      >
        <span class="svgSpan" v-html="svgImg.taskChange"></span>
      </el-button>
    </div>
</template>

<script>
import Permission from "../../../../../utils/hasPermission";
    export default {
        data() {
            return {
                prdtInfo: {},
                form: {
                    paramList: [],
                    remark: '',
                    planEndTime: '',
                },
                rules:{},
                autoData:[],
                isShowFinishDetail:false,
                isReloadParam:false,
                remark: '',
                title: '手工确认',
                popoverVisible: false,
                isShowEndTime: false,
                isShowRemark:true,
                actionType: '',
                svgImg: this.$lcImg,
            }
        },
        beforeMount() {
            if (this.params.data) {
                this.remark = this.params.data.remark;
                this.form.planEndTime = this.params.data.planEndTime;
            }
        },
        computed: {
          //是否有操作权限
          isCanExec() {
            return  this.params.data.execJudgNum > 0;
          },
          manualStartStepShow(){
            return this.params.data.taskStatus ==='01' && Permission.hasPermission('agnes.elec.step.manual.start') && new Date>this.params.data.planStartTime;
          },
            actionV2MotShow() {
                return this.params.data.stepActType === '05' && (this.params.data.taskStatus != '07' || this.params.data.taskStatus != '08') && Permission.hasPermission('agnes.elec.task.confirm');
            },
            reviewV2MotShow() {
                return this.params.data.stepActType === '05' && this.params.data.taskStatus == '06'  && Permission.hasPermission('agnes.elec.task.confirm');
            },
            actionShow() {
                return this.params.data.stepActType === '6' && (this.params.data.taskStatus != '07' || this.params.data.taskStatus != '08') && Permission.hasPermission('agnes.elec.task.confirm');
            },
            isRpaDisabled() {
              return this.params.data.stepActType === '6' && this.params.data().dealType==='11';
            },
            showRpaDetail(){
              return this.params.data.dealType === '11' ;
            },
            finishDetailShow() {
                return this.params.data.stepActType === '6' && (this.params.data.taskStatus != '07' || this.params.data.taskStatus != '08') && this.params.data.stepStatus === '06' && this.params.data.hasTaskParam != '0' && Permission.hasPermission('agnes.elec.task.confirm');
            },
            lookFinishDetailShow() {
                return this.params.data.stepActType === '6' && this.params.data.stepStatus === '06' && this.params.data.hasTaskParam != '0' && Permission.hasPermission('agnes.elec.task.confirm');
            },
            openTradeDetailShow(){
                return this.params.data.stepActType === '7' ;
            },
            dataAuditDetailShow(){
              return this.params.data.dealType === '27' ;
            },
            indexSetShow() {
                return (this.params.data.stepActType === '1') && (this.params.data.taskStatus != '07' || this.params.data.taskStatus != '08') && Permission.hasPermission('agnes.elec.task.popover');
            },
            forcePassV2() {
              //allowManualConfirm 为场景配置中 1:允许干预通过 0：不允许干预通过
                return this.params.data?.allowManualConfirm === '1' && this.params.data.stepActType.match(/01|02|04|06|07/) && (this.params.data.taskStatus != '07' || this.params.data.taskStatus != '08') && Permission.hasPermission('agnes.elec.task.popover')
            },
            autoSetShow() {
                return this.params.data.stepActType === '7' && (this.params.data.taskStatus != '07' || this.params.data.taskStatus != '08') && Permission.hasPermission('agnes.elec.task.popover');
            },
            reExecuteShow() {
              //允许文件处理的07 23类型的任务 具体类型可参考mockData.js
                return (this.params.data.stepActType.match(/1|01|02|04|06|07/) || this.params.data.dealType.match(/07|23/)) && (this.params.data.taskStatus != '07' || this.params.data.taskStatus != '08') && Permission.hasPermission('agnes.elec.task.exec');
            },
          indexDetailShow() {
            return (this.params.data.stepActType === '1' || this.params.data.dealType === '05') && Permission.hasPermission('agnes.elec.task.deatil');
          },
          indexEditShow() {
            return Permission.hasPermission('agnes.elec.task.edit') && (this.params.data.taskStatus != '07' || this.params.data.taskStatus != '08') && this.params.data.allowEditEndTime === '1';
          },
          isDisabled() {
            return !this.params.data.buttonStatus;
          },
          isKpiDisabled() {
            return this.params.data.stepStatus === '01';
          },
          indexircShow() {
            return this.params.data.stepActType === '8';
          },
          indexJzwShow() {
            return this.params.data.stepActType === '10';
          },
          indexFileStepShow() {
            return this.params.data.stepActType === '9' || this.params.data.stepActType === '03';
          },
          isFinished(){
            return this.params.data.stepStatus.match(/06|07/)
          },
          isEditDisabled() {
              let isEditDisabledStatus = !(this.params.data.stepStatus === '02' || this.params.data.stepStatus ==='03');
              if (this.params.data.planEndTime) {
                let bizDate = window.bizDate+' '+new Date().getHours()+':'+new Date().getMinutes()+':'+new Date().getSeconds();
                if(new Date(bizDate)>new Date(this.params.data.planEndTime)){
                      isEditDisabledStatus = true;
                  }
              }
              return isEditDisabledStatus
            }
        },
        methods: {
            async popoverClick(actionType) {
                this.rules = {};
                this.form.paramList = [];
                this.isShowFinishDetail = false;
                this.isReloadParam = false;
                this.isShowRemark = true
                if(['actionConfirm', 'forcePass', 'editEndTime', 'reloadParam'].includes(actionType)){
                    this.$nextTick(()=>{
                        this.params.api.execCmd('clearFreshInterval', false);
                    })
                }
                if(actionType=='actionConfirm'){
                    await this.selectRollBackTaskParams()
                    this.title= '手工确认';
                    this.form.remark = this.params.data.remark;
                }else if(actionType=='autoTaskActionConfirm'){
                    await this.getAutoTaskParam()
                    this.title= '返回值';
                }else if(actionType == 'forcePass'){
                    this.title= '干预通过';
                }else if(actionType == 'autoConfirm'){
                    this.title= '干预通过';
                }else if(['finishDetailShow', 'reloadParam'].includes(actionType)){
                    await this.getCaseStepParam()
                    this.title= '查看回填参数';
                    this.isShowFinishDetail = true;
                    if(actionType == 'reloadParam'){
                        this.title= '回填参数重新录入';
                        this.isShowFinishDetail = false;
                        this.isReloadParam = true;
                    }
                    this.isShowRemark = false;
                }else if(actionType == 'actionV2Mot'){
                    if(this.params.data.dealType&&this.params.data.dealType === '21'){
                        this.title= '手工确认';
                        this.form.remark = this.params.data.remark;
                        this.popoverVisible = true;
                    }else {
                        this.handleCmd(actionType);
                    }
                }
                if(actionType != 'actionV2Mot'){
                    this.popoverVisible = true;
                }
                this.actionType = actionType;
            },
            /**
             * 手工确认，判断是否有回填参数
             */
            async selectRollBackTaskParams() {
                const p = this.$api.taskTodoApi.selectRollBackTaskParams({stepExecId:this.params.data.stepExecId})
                const resp = await this.$app.blockingApp(p);
                this.form.paramList=resp.data;
                this.form.paramList.forEach(item=>{
                    if(item.isRequire == '1'){
                        let ruleItem = item.paramKey;
                        this.rules[ruleItem] = [{validator: this.checkRule,required: true, trigger: 'change'}];
                    }
                })
            },
            /**
             * 返回值
             */
            async getAutoTaskParam() {
                this.autoData=[];
                const p = this.$api.taskTodoApi.getAutoTaskParam({stepExecId:this.params.data.stepExecId})
                const resp = await this.$app.blockingApp(p);
                if(resp.data){
                    this.autoData=resp.data;
                }
            },
            /**
             * 查看回填参数
             */
            async getCaseStepParam() {
                if(this.params.data.stepExecId && this.params.data.caseId){
                    let taskCommit = {
                        caseId:this.params.data.caseId,
                        stepExecId:this.params.data.stepExecId,
                    }
                    const s = this.$api.taskDefineApi.getCaseStepParam(taskCommit)
                    let resp = await this.$app.blockingApp(s);
                    if(resp.data){
                        this.form.paramList=resp.data;
                    }
                }
            },
            getLists(fieldValueRange) {
                let lists = [];
                const fieldRange = fieldValueRange.split(';')
                if (fieldRange) {
                    fieldRange.forEach(item => {
                        const values = item.split(':');
                        if(!values){
                            return;
                        }
                        if (values.length > 1) {
                            lists.push({
                                name: values[1],
                                value: values[0],
                            })
                        } else {
                            lists.push({
                                name: values[0],
                                value: values[0],
                            })
                        }
                    })
                }
                return lists;

            },

            checkRule(rule, value, callback) {
                callback();
            },
            async reExecute(actionType) {
                this.actionType = actionType;
                const ok = await this.$msg.ask(`是否确认重新执行`);
                if (ok) {
                    this.handleCmd(this.actionType);
                }
            },
            async openKjd(actionType) {
                this.handleCmd(actionType);
            },
            async openDataAuditDetail(actionType) {
                this.handleCmd(actionType);
            },

          async manualStartStep(actionType){
            this.actionType = actionType;
            const ok = await this.$msg.ask(`是否要手动启动任务`);
            if (ok) {
              this.handleCmd(this.actionType);
            }
          },

          async taskChangeMethod(actionType){
            this.actionType = actionType;
            this.handleCmd(this.actionType);
          },

            indexDetail(actionType) {
                this.handleCmd(actionType);
            },
            editEndTime(actionType) {
                this.title = '编辑';
                this.popoverVisible = true;
                this.isShowEndTime = true;
                this.isShowRemark = false;
                this.actionType = actionType;
            },

            // 备注确定 -- 保存
            async confirmRemark() {
                let isReturn = false;
                if(this.form.paramList && this.form.paramList.length>0){
                    this.form.paramList.forEach(item=>{
                        if(this.verifRuquire(item)){
                            this.$msg.warning('请补充完整必填项！');
                            isReturn = true;
                            return;
                        }
                        if(item.paramType=='date'){
                            this.params.data.isHasdate = '1';
                        }
                    })
                }
                if(isReturn){
                    return;
                }
                this.popoverVisible = false;
                this.isShowEndTime = false;
                this.isShowRemark = true;
                this.params.data.prdtInfo = this.prdtInfo;
                this.params.data.remark = this.form.remark;
                this.params.data.paramList = this.form.paramList;
                this.params.data.planEndTime = this.form.planEndTime;
                this.params.api.refreshCells(this.params.node);
                let type = this.actionType;
                if(type=='autoConfirm' || type=='actionV2Mot'){
                    type = 'actionConfirm';
                }
                this.handleCmd(type);
                this.$nextTick(()=>{
                    this.params.api.execCmd('startFreshInterval', false);
                })
                this.form.paramList = [];
            },
            verifRuquire(item) {
                let v = item.isRequire == '1'
                let v1 = item.paramType == 'string' && !item.paramString
                let v2 = item.paramType == 'number' && !item.paramNumber
                let v3 = item.paramType == 'amount' && !item.paramAmount
                let v4 = item.paramType == 'date' && !item.paramDate
                return v && (v1 || v2 || v3 || v4)
            },
            closeRemake() {
                this.popoverVisible = false
                this.isShowEndTime = false;
                this.isShowRemark = true;
                this.form.paramList = [];
                if(this.actionType=='actionConfirm'||this.actionType=='forcePass'||this.actionType=='editEndTime'||this.actionType=='reloadParam' || this.actionType=='autoConfirm'){
                    this.$nextTick(()=>{
                        this.params.api.execCmd('startFreshInterval', false);
                    })
                }
            },

            handleCmd(actionType) {
                this.params.api.execCmd(actionType, this.params);
            }
        }
    }
</script>


<style scoped>
    .optional-cell {
        line-height: 1;
    }

    .optional-cell .svgSpan {
        display: inline-block;
        width: 16px;
        height: 16px;
        cursor: pointer;
    }
    .optional-cell .svgSpan >>> svg {
        width: 100%;
        height: 100%;
    }

    .optional-cell > span + span {
        margin-left: 10px;
    }

    .optional-cell .svgSpan > svg {
        width: 100%;
        height: 100%;
    }

    .op-btn {
        height: 25px;
        padding: 5px 19px;
        border-color: #E1E4E8;
        color: #333;
        background: transparent;
        font-size: 14px;
    }

    .op-btn.primary {
        color: #fff;
        background: #0f5eff;
        border-color: #0f5eff;
    }

    .detail-btn {
        padding: 7px 0;
    }

    .detail-btn>>>.fa.fa-eye{
        font-size: 15px;
        vertical-align: text-bottom;
        color: #0f5eff;
        line-height: 17px;
    }

    .detail-btn.is-disabled>>>.fa.fa-eye {
        color: #ccc;
    }
    .repeat {
      font-size: 15px;
      vertical-align: text-bottom;
      color: #0f5eff;
      line-height: 17px;
    }
    .el-form{
      padding:0 20px 0 0;
    }
    .el-form >>> .el-form-item {
      margin-bottom: 10px;
    }
    .el-form >>> .el-form-item .el-form-item__label {
      width: 95px !important;
    }
    .el-form >>> .el-form-item .el-form-item__content {
      margin-left: 95px !important;
    }
</style>
