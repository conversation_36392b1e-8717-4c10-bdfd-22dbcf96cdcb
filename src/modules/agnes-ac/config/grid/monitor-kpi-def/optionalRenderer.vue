<template>
    <div class="optional-cell" v-if="params.data">
        <el-popover class="icon-popper" placement="bottom-start"
                    title="备注"
                    width="300"
                    trigger="click">
            <el-input type="textarea" :rows="1" placeholder="请输入备注内容" v-model="remark" :maxlength="50">
            </el-input>
            <div style="text-align: right; margin-top: 10px">
                <el-button class="op-btn primary" size="mini" @click="executeKpi">提交</el-button>
            </div>
            <el-button slot="reference" size="mini" type="text"
                       :disabled="isDisabled"
                       v-if="setShow"
                       @click="popoverClick" title="干预通过">
                <span class="svgSpan" v-html="svgImg.forcePass1">
                </span>
            </el-button>
        </el-popover>
        <el-button class="icon-popper" size="mini" type="text"
                   v-if="setExecShow"
                   :disabled="!setExecShow || isFinished"
                   @click="exeTaskJob" title="重新执行">
            <span class="svgSpan" v-html="svgImg.reExecute"></span>
        </el-button>
      <el-button size="mini" type="text"
                 v-if="manualStartStepShow"
                 :disabled="!isCanExec"
                 @click="manualStartStep('manualStartStep')"
                 title="启动STEP"
      >

        <span class="svgSpan" v-html="svgImg.manualStartStep"></span>
      </el-button>
      <el-button  type="text"
                  :disabled="isFinished"
                  @click="taskChangeMethod('taskChange')"
                  title="任务转交"
      >
        <span class="svgSpan" v-html="svgImg.taskChange"></span>
      </el-button>
    </div>
</template>

<script>
    import Permission from "../../../../../utils/hasPermission";

    export default {
        data() {
            return {
                remark: '',
                popoverVisible: false,
                svgImg: this.$lcImg,
            }
        },
        beforeMount() {
            if (this.params.data) {
                this.remark = this.params.data.remark;
            }
        },
        computed: {
            isDisabled() {
                return this.params.data.stepStatus === '01';
            },
          //是否有操作权限
          isCanExec() {
            return  this.params.data.execJudgNum > 0;
          },
          manualStartStepShow(){
            return this.params.data.taskStatus ==='01' && Permission.hasPermission('agnes.elec.step.manual.start');
          },
            isDisabledReExec(){
                return this.params.data.stepStatus == '01' ||
                       this.params.data.stepStatus == '06';
            },
          isFinished(){
            return this.params.data.stepStatus.match(/06|07/)
          },
            setShow(){
              return Permission.hasPermission('agnes.monitor.kpi.popover');
            },
            setExecShow(){
              return Permission.hasPermission('agnes.monitor.kpi.exec');
            }
        },
        methods: {
            // 干预通过
            async executeKpi() {
                this.params.data.remark = this.remark;
              this.handleCmd('executeKpi');
            },

          async manualStartStep(actionType){
            this.actionType = actionType;
            const ok = await this.$msg.ask(`是否要手动启动任务`);
            if (ok) {
              this.handleCmd(this.actionType);
            }
          },

          async taskChangeMethod(actionType){
            this.actionType = actionType;
            this.handleCmd(this.actionType);
          },

            popoverClick() {
                this.popoverVisible = true;
            },

            // 重新执行
            async exeTaskJob() {
                const ok = await this.$msg.ask(`是否确认重新执行`);
                if (ok) {
                    this.handleCmd('exeTaskJob');
                }
            },

            handleCmd(actionType) {
                this.params.api.execCmd(actionType, this.params);
            }
        }
    }
</script>


<style scoped>
    .optional-cell {
        line-height: 1;
    }

    .optional-cell .svgSpan {
        display: inline-block;
        width: 16px;
        height: 16px;
        cursor: pointer;
    }

    .optional-cell .svgSpan >>> svg {
        width: 100%;
        height: 100%;
    }

    .optional-cell > span + span {
        margin-left: 10px;
    }

    .optional-cell .svgSpan > svg {
        width: 100%;
        height: 100%;
    }

    .op-btn {
        height: 25px;
        padding: 5px 19px;
        border-color: #E1E4E8;
        color: #333;
        background: transparent;
        font-size: 14px;
    }

    .op-btn.primary {
        color: #fff;
        background: #0f5eff;
        border-color: #0f5eff;
    }
</style>
