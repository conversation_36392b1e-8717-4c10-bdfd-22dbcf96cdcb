<template>
    <div class="optional-cell" v-if="params.data">

        <el-popover ref="popover"
                    placement="top-start"
                    :title="title"
                    width="400"
                    trigger="manual"
                    v-model="popoverVisible"
        >
            <el-form v-if="actionType=='actionConfirm'  || actionType=='forcePass'   || actionType=='reloadParam' || actionType=='editEndTime' " ref="form" :model="form"
                     label-width="80px" :rules="rules">
                <el-form-item v-for="item in form.paramList"
                              :label="item.paramName"
                              :key="item.paramKey"
                              :prop="item.paramKey">
                    <el-input type="textarea" v-model="item.paramString" :disabled="isShowFinishDetail" v-if="item.paramType=='string'"></el-input>
                    <el-input v-model="item.paramNumber" :disabled="isShowFinishDetail" oninput="value=value.replace(/[^\d]/g,'')"
                              maxLength='9' placeholder="请输入数字" v-if="item.paramType=='number'"/>
                    <el-input v-model.number="item.paramAmount" :disabled="isShowFinishDetail" placeholder="请输入金额" v-if="item.paramType=='amount'"/>
                    <el-date-picker
                            v-model="item.paramDate"
                            type="date"
                            placeholder="选择日期" :disabled="isShowFinishDetail" v-if="item.paramType=='date'">
                    </el-date-picker>
                    <gf-dict v-model="item.paramString" v-if="item.paramType === 'boolean'"
                             dict-type="GF_BOOL_TYPE"></gf-dict>

                    <el-select v-model="item.paramString" filterable clearable placeholder="请选择"
                               v-if="item.paramType === 'list'">
                        <el-option
                                v-for="item in getLists(item.paramValueRange)"
                                :key="item.value"
                                :label="item.name"
                                :value="item.value">
                        </el-option>
                    </el-select>
                    <gf-dict v-model="item.paramString" v-if="item.paramType === 'dict'"
                             :dict-type="item.paramValueRange"></gf-dict>
                </el-form-item>
                <el-form-item label="备注" v-show="isShowRemark">
                    <el-input type="textarea" :rows="1" placeholder="请输入备注内容"
                              v-model="form.remark" :maxlength="50">
                    </el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right; margin-top: 10px">
                <el-button class="op-btn" size="mini" type="text" @click="closeRemake">取消</el-button>
                <el-button class="op-btn primary"
                           v-if="actionType=='actionConfirm'  || actionType=='forcePass'   || actionType=='reloadParam' || actionType=='editEndTime' "
                           size="mini" @click="confirmRemark">保存
                </el-button>
            </div>
        </el-popover>
          <el-button size="mini" type="text"
                     v-popover:popover v-if="isFormAction"
                     @click="popoverClick('isFormAction')"
                     title="表单录入"
                     :disabled="isDisabled"
          >
            <span class="svgSpan" v-html="svgImg.actionConfirm"></span>
          </el-button>
          <el-button size="mini" type="text"
                     v-popover:popover v-if="actionShow"
                     @click="popoverClick('actionConfirm')"
                     title="手工确认"
                     :disabled="isDisabled"
          >
              <span class="svgSpan" v-html="svgImg.actionConfirm"></span>
          </el-button>
          <el-button size="mini" type="text"
                     v-popover:popover
                     v-if="forcePass"
                     @click="popoverClick('forcePass')"
                     title="干预通过"
                     :disabled="isDisabled"
          >
            <span class="svgSpan" v-html="svgImg.forcePass"></span>
          </el-button>
          <el-button size="mini" type="text"
                     v-popover:popover
                     v-if="fileForcePass"
                     class="show"
                     @click="popoverClick('fileForcePass')"
                     title="干预通过"
          >
            <span class="svgSpan" v-html="svgImg.forcePass"></span>
          </el-button>
          <el-button size="mini" type="text"
                     class="detail-btn"
                     v-if="reExecuteShow"
                     @click="reExecute('reExecute')"
                     title="重新执行"
                     :disabled="isUnDoing"
          >
            <span class="svgSpan" v-html="svgImg.reExecute"></span>
          </el-button>
          <el-button v-if="showDetail" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                     @click="indexDetail('showDetail')"
                     title="查看明细"
          >
          </el-button>
          <el-button v-if="isFormAction" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                     @click="popoverClick('isFormAction')"
                     :disabled="isdown"
                     title="查看明细"
          >
          </el-button>
        <el-button  type="text"
                    v-if="backStepStatusShow"
                    @click="backStepStatus('backStepStatusShow')"
                    title="状态回退"
        >
          <span class="svgSpan" v-html="svgImg.stepStatusBack"></span>
        </el-button>
        <el-button  type="text"
                  @click="popoverClick('taskChane')"
                  title="任务转交"
      >
        <span class="svgSpan" v-html="svgImg.taskChange"></span>
      </el-button>
    </div>
</template>

<script>
    export default {
        data() {
            return {
                prdtInfo: {},
                form: {
                    paramList: [],
                    remark: '',
                    planEndTime: '',
                },
                rules:{},
                autoData:[],
                isShowFinishDetail:false,
                isReloadParam:false,
                remark: '',
                title: '手工确认',
                popoverVisible: false,
                isShowEndTime: false,
                isShowRemark:true,
                actionType: '',
                svgImg: this.$lcImg,
            }
        },
        beforeMount() {
            if (this.params.data) {
                this.remark = this.params.data.remark;
                this.form.planEndTime = this.params.data.planEndTime;
            }
        },
        computed: {
          //任务完成返回false
          isdown() {
            return this.params.data.stepStatus !='06' && this.params.data.stepStatus !='07';
          },
          //任务完成返回TRUE
              isDisabled() {
                return this.params.data.stepStatus =='01'  ||  this.params.data.stepStatus == '06' || this.params.data.stepStatus == '07';
              },
              //任务未开始返回TRUE
              isUnDoing() {
                return this.params.data.stepStatus =='01';
              },
          //表单录入
            isFormAction() {
                  return (this.params.data.stepType != '0001' &&this.params.data.stepType?.match(/12|20/) ) ;
              },
          //手工确认
              actionShow() {
                  return this.params.data.stepType == '0001' || (  this.params.data.stepType != '0001' && this.params.data.stepType?.match(/21/));
              },
          //文件干预通过
          fileForcePass() {
                  return (this.params.data.stepType != '0001' &&this.params.data.stepType?.match(/07/))
              },
              forcePass() {
                  return this.params.data.stepType != '0001' &&this.params.data.stepType?.match(/01|02|05|09|10|11|23/)
              },
          //重新执行
              reExecuteShow() {
                  return this.params.data.stepType != '0001' &&(this.params.data.stepActType?.match(/1|01|02|04|06|07/) || this.params.data.dealType?.match(/07|23/)) && (this.params.data.taskStatus != '07' || this.params.data.taskStatus != '08') ;
              },
          //查看明细
              showDetail() {
                return this.params.data.stepType != '0001' &&(this.params.data.stepType?.match(/05|07|11|24/) ) ;
              },
          //会退step状态按钮
          backStepStatusShow() {
                return (this.params.data.stepType == '0001' || this.params.data.stepType?.match(/21/)) && (this.params.data.stepStatus.match(/06|07/) ) ;
              },
            isEditDisabled() {
                let isEditDisabledStatus = !(this.params.data.stepStatus === '02' || this.params.data.stepStatus ==='03');
                if (this.params.data.planEndTime) {
                  let bizDate = window.bizDate+' '+new Date().getHours()+':'+new Date().getMinutes()+':'+new Date().getSeconds();
                  if(new Date(bizDate)>new Date(this.params.data.planEndTime)){
                        isEditDisabledStatus = true;
                    }
                }
                return isEditDisabledStatus
              }
        },
        methods: {
            async popoverClick(actionType) {
                if (this.$app.optionsBtnPopoverRref) {
                    if (this.$app.optionsBtnPopoverRref !== this.$refs.popover) {
                    this.$app.optionsBtnPopoverRref.value = false;
                    } else {
                    this.$app.optionsBtnPopoverRref.value = true;
                    }
                }
                this.$app.optionsBtnPopoverRref=this.$refs.popover
                this.rules = {};
                this.form.paramList = [];
                this.isShowFinishDetail = false;
                this.isReloadParam = false;
                this.isShowRemark = true
                this.actionType = actionType;
                if(actionType=='actionConfirm'){
                  //手工确认，
                    this.title= '手工确认';
                    this.form.remark = this.params.data.remark;
                    this.popoverVisible = true;
                }else if(actionType == 'forcePass'){
                  this.title= '干预通过';
                  this.popoverVisible = true;
                }else if(actionType == 'fileForcePass'){
                  this.handleCmd(actionType);
                }else if(actionType == 'reloadParam'){
                    if(this.params.data.stepExecId && this.params.data.bizNo){
                        await this.getCaseStepParam()
                    }
                    this.title= '查看回填参数';
                    this.isShowFinishDetail = true;
                    if(actionType == 'reloadParam'){
                        this.title= '回填参数重新录入';
                        this.isShowFinishDetail = false;
                        this.isReloadParam = true;
                    }
                    this.isShowRemark = false;
                    this.popoverVisible = true;
                }else if(actionType == 'isFormAction'){
                    //表单录入
                    this.handleCmd(actionType);
                }else if(actionType == 'taskChane'){
                    // 任务转交
                    this.handleCmd(actionType);
                }
            },
            /**
             * 查看回填参数
             */
             async getCaseStepParam() {
                let taskCommit = {
                    caseId:this.params.data.caseId,
                    stepExecId:this.params.data.stepExecId,
                }
                const s = this.$api.taskDefineApi.getCaseStepParam(taskCommit)
                let resp = await this.$app.blockingApp(s);
                if(resp.data){
                    this.form.paramList=resp.data;
                }
            },
            getLists(fieldValueRange) {
                let lists = [];
                const fieldRange = fieldValueRange.split(';')
                if (fieldRange) {
                    fieldRange.forEach(item => {
                        const values = item.split(':');
                        if(!values){
                            return;
                        }
                        if (values.length > 1) {
                            lists.push({
                                name: values[1],
                                value: values[0],
                            })
                        } else {
                            lists.push({
                                name: values[0],
                                value: values[0],
                            })
                        }
                    })
                }
                return lists;

            },

            checkRule(rule, value, callback) {
                callback();
            },
            async reExecute(actionType) {
                this.actionType = actionType;
                const ok = await this.$msg.ask(`是否确认重新执行`);
                if (ok) {
                    this.handleCmd(this.actionType);
                }
            },
            async openKjd(actionType) {
                this.handleCmd(actionType);
            },

          async manualStartStep(actionType){
            this.actionType = actionType;
            const ok = await this.$msg.ask(`是否要手动启动任务`);
            if (ok) {
              this.handleCmd(this.actionType);
            }
          },

          async backStepStatus(actionType){
            this.actionType = actionType;
            const ok = await this.$msg.ask(`是否要回退状态？`);
            if (ok) {
              this.handleCmd(this.actionType);
            }
          },

            indexDetail(actionType) {
                this.handleCmd(actionType);
            },
            editEndTime(actionType) {
                this.title = '编辑';
                this.popoverVisible = true;
                this.isShowEndTime = true;
                this.isShowRemark = false;
                this.actionType = actionType;
            },

            // 备注确定 -- 保存
            async confirmRemark() {
                let isReturn = false;
                if(this.form.paramList && this.form.paramList.length>0){
                    this.form.paramList.forEach(item=>{
                        if(this.verifRuquire(item)){
                            this.$msg.warning('请补充完整必填项！');
                            isReturn = true;
                            return;
                        }
                        if(item.paramType=='date'){
                            this.params.data.isHasdate = '1';
                        }
                    })
                }
                if(isReturn){
                    return;
                }
                this.popoverVisible = false;
                this.isShowEndTime = false;
                this.isShowRemark = true;
                this.params.data.prdtInfo = this.prdtInfo;
                this.params.data.remark = this.form.remark;
                this.params.data.paramList = this.form.paramList;
                this.params.data.planEndTime = this.form.planEndTime;
                this.params.api.refreshCells(this.params.node);
                let type = this.actionType;
                if(type=='actionV2Mot'){
                    type = 'actionConfirm';
                }
                this.handleCmd(type);
                this.$nextTick(()=>{
                    this.params.api.execCmd('startFreshInterval', false);
                })
                this.form.paramList = [];
            },
            verifRuquire(item) {
                let v = item.isRequire == '1'
                let v1 = item.paramType == 'string' && !item.paramString
                let v2 = item.paramType == 'number' && !item.paramNumber
                let v3 = item.paramType == 'amount' && !item.paramAmount
                let v4 = item.paramType == 'date' && !item.paramDate
                return v && (v1 || v2 || v3 || v4)
            },
            closeRemake() {
                this.popoverVisible = false
                this.isShowEndTime = false;
                this.isShowRemark = true;
                this.form.paramList = [];
                if(this.actionType=='actionConfirm'||this.actionType=='forcePass'||this.actionType=='editEndTime'||this.actionType=='reloadParam' ){
                    this.$nextTick(()=>{
                        this.params.api.execCmd('startFreshInterval', false);
                    })
                }
            },

            handleCmd(actionType) {
                this.params.api.execCmd(actionType, this.params);
            }
        }
    }
</script>


<style scoped>
    .optional-cell {
        line-height: 1;
    }

    .optional-cell .svgSpan {
        display: inline-block;
        width: 16px;
        height: 16px;
        cursor: pointer;
    }
    .optional-cell .svgSpan >>> svg {
        width: 100%;
        height: 100%;
    }

    .optional-cell > span + span {
        margin-left: 10px;
    }

    .optional-cell .svgSpan > svg {
        width: 100%;
        height: 100%;
    }

    .op-btn {
        height: 25px;
        padding: 5px 19px;
        border-color: #E1E4E8;
        color: #333;
        background: transparent;
        font-size: 14px;
    }

    .op-btn.primary {
        color: #fff;
        background: #0f5eff;
        border-color: #0f5eff;
    }

    .detail-btn {
        padding: 7px 0;
    }

    .detail-btn>>>.fa.fa-eye{
        font-size: 15px;
        vertical-align: text-bottom;
        color: #0f5eff;
        line-height: 17px;
    }

    .detail-btn.is-disabled>>>.fa.fa-eye {
        color: #ccc;
    }
    .repeat {
      font-size: 15px;
      vertical-align: text-bottom;
      color: #0f5eff;
      line-height: 17px;
    }
    .el-form{
      padding:0 20px 0 0;
    }
    .el-form >>> .el-form-item {
      margin-bottom: 10px;
    }
    .el-form >>> .el-form-item .el-form-item__label {
      width: 95px !important;
    }
    .el-form >>> .el-form-item .el-form-item__content {
      margin-left: 95px !important;
    }
</style>
