<template>
  <div class="optional-cell"  v-if="params.data && params.data.stepId">

    <el-popover ref="popover"
                placement="top-start"
                :title="title"
                width="400"
                trigger="manual"
                v-model="popoverVisible"
    >
      <el-form v-if="actionType=='actionConfirm'  || actionType=='forcePass'  || actionType=='finishDetailShow' || actionType=='reloadParam' || actionType=='editEndTime' " ref="form" :model="form"
               label-width="80px" :rules="rules">


        <el-form-item v-for="item in form.paramList"
                      :label="item.paramName"
                      :key="item.paramKey"
                      :prop="item.paramKey">
          <el-input type="textarea" v-model="item.paramString" :disabled="isShowFinishDetail" v-if="item.paramType=='string'"></el-input>
          <el-input v-model="item.paramNumber" :disabled="isShowFinishDetail" oninput="value=value.replace(/[^\d]/g,'')"
                    maxLength='9' placeholder="请输入数字" v-if="item.paramType=='number'"/>
          <el-input v-model.number="item.paramAmount" :disabled="isShowFinishDetail" placeholder="请输入金额" v-if="item.paramType=='amount'"/>
          <el-date-picker
              v-model="item.paramDate"
              type="date"
              placeholder="选择日期" :disabled="isShowFinishDetail" v-if="item.paramType=='date'">
          </el-date-picker>
          <gf-dict v-model="item.paramString" v-if="item.paramType === 'boolean'"
                   dict-type="GF_BOOL_TYPE"></gf-dict>

          <el-select v-model="item.paramString" filterable clearable placeholder="请选择"
                     v-if="item.paramType === 'list'">
            <el-option
                v-for="item in getLists(item.paramValueRange)"
                :key="item.value"
                :label="item.name"
                :value="item.value">
            </el-option>
          </el-select>
          <gf-dict v-model="item.paramString" v-if="item.paramType === 'dict'"
                   :dict-type="item.paramValueRange"></gf-dict>
        </el-form-item>
        <el-form-item label="备注" v-show="isShowRemark">
          <el-input type="textarea" :rows="1" placeholder="请输入备注内容"
                    v-model="form.remark"
                    :maxlength="50">
          </el-input>
        </el-form-item>
        <el-form-item label="结束时间" v-show="isShowEndTime">
          <el-date-picker
              v-model="form.planEndTime"
              type="datetime"
              value-format="yyyy-MM-dd HH:mm:ss"
              placeholder="选择日期时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <el-table header-row-class-name="rule-header-row"
                header-cell-class-name="rule-header-cell"
                row-class-name="rule-row"
                cell-class-name="rule-cell"
                v-if="actionType=='autoTaskActionConfirm'"
                :data="autoData"
                border stripe
                style="width: 100%"
                :header-cell-style="{'text-align':'center'}">
        <el-table-column prop="stepCode" label="返回参数名">
          <template slot-scope="scope">
            <span>{{scope.row.fieldKey}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="stepCode" label="返回参数值">
          <template slot-scope="scope">
            <span>{{scope.row.fieldValue}}</span>
          </template>
        </el-table-column>
      </el-table>
      <div style="text-align: right; margin-top: 10px">
        <el-button class="op-btn" size="mini" type="text" @click="closeRemake">取消</el-button>
        <el-button class="op-btn primary"
                           v-if="actionType=='actionConfirm'||actionType=='actionV2Mot'||actionType=='forcePass'||actionType=='editEndTime'||actionType=='reloadParam' || actionType=='autoConfirm'"
                   size="mini" @click="confirmRemark">保存
        </el-button>
      </div>
    </el-popover>
    <el-button size="mini" type="text"
               v-popover:popover v-if="isFormAction"
               @click="popoverClick('isFormAction')"
               title="表单录入"
               :disabled="isDisabled || !isCanExec"
    >
      <span class="svgSpan" v-html="svgImg.actionConfirm"></span>
    </el-button>
    <el-button size="mini" type="text"
               v-popover:popover v-if="actionShow"
               @click="popoverClick('actionConfirm')"
               title="手工确认"
               :disabled="isDisabled || !isCanExec"
    >
      <span class="svgSpan" v-html="svgImg.actionConfirm"></span>
    </el-button>
    <el-button size="mini" type="text"
               v-popover:popover
               v-if="forcePass"
               @click="popoverClick('forcePass')"
               title="干预通过"
               :disabled="isDisabled || !isCanExec"
    >
      <span class="svgSpan" v-html="svgImg.forcePass1"></span>
    </el-button>
    <el-button size="mini" type="text"
               v-popover:popover
               v-if="fileForcePass"
               class="show"
               @click="popoverClick('fileForcePass')"
               title="干预通过"
               :disabled="!isCanExec"
    >
      <span class="svgSpan" v-html="svgImg.forcePass1"></span>
    </el-button>
    <el-button size="mini" type="text"
               class="detail-btn"
               v-if="reExecuteShow"
               @click="reExecute('reExecute')"
               title="重新执行"
               :disabled="!isCanExec || isUnDoing"
    >
      <span class="svgSpan" v-html="svgImg.reExecute"></span>
    </el-button>
    <el-button v-if="showDetail" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
               @click="indexDetail('showDetail')"
               title="查看明细"
    >
    </el-button>
    <el-button v-if="isFormAction" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
               @click="popoverClick('isFormAction')"
               :disabled="isdown"
               title="查看明细"
    >
    </el-button>

    <el-button v-if="finishDetailShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
               @click="popoverClick('finishDetailShow')"
               title="查看回填参数"
    >
    </el-button>
    <el-button size="mini" type="text"
               v-if="finishDetailShow"
               @click="popoverClick('reloadParam')"
               title="重新录入"
               :disabled="!isCanExec"
    >
      <span class="svgSpan" v-html="svgImg.reEntry"></span>
    </el-button>

    <el-button  type="text"
                v-if="manualStartStepShow"
                @click="manualStartStep('manualStartStep')"
                title="启动STEP"
                :disabled="!isCanExec"
    >
      <span class="svgSpan" v-html="svgImg.manualStartStep"></span>
    </el-button>
  <el-button v-if="indexFileStepShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="indexDetail('indexFileStepShow')"
                   title="查看明细"
                   :disabled="isKpiDisabled"
        >
        </el-button>

        <el-button v-if="lookFinishDetailShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="popoverClick('finishDetailShow')"
                   title="查看回填参数"
        >
        </el-button>
        <el-button v-if="openTradeDetailShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                   @click="openKjd('openTradeDetailShow')"
                   title="查看明细"
        >
        </el-button>
        <el-button v-if="dataAuditDetailShow" class="detail-btn" size="mini" type="text" icon="fa fa-eye"
                 @click="openDataAuditDetail('dataAuditDetailShow')"
                 title="查看明细"/>

    <el-button size="mini" type="text"
               @click="editEndTime('editEndTime')"
               title="修改结束时间"
               :disabled="isEditDisabled || !isCanExec"
               v-if="indexEditShow"

    >
      <span class="svgSpan" v-html="svgImg.editEndTime"></span>
    </el-button>
    <el-button  type="text"
                :disabled="isFinished || !isCanExec"
                @click="taskChangeMethod('taskChange')"
                title="任务转交"
    >
      <span class="svgSpan" v-html="svgImg.taskChange"></span>
    </el-button>
    <el-button size="mini" type="text"
               v-popover:popover
               v-if="queryExecRecords"
               class="show"
               @click="popoverClick('queryExecRecords')"
               title="查询执行记录"
               :disabled="!isCanExec"
    >
      <span class="svgSpan" v-html="svgImg.queryExecRec"></span>
    </el-button>
  </div>
</template>

<script>
import Permission from "../../../../../utils/hasPermission";
export default {
  data() {
    return {
      prdtInfo: {},
      form: {
        paramList: [],
        remark: '',
        planEndTime: '',
      },
      rules:{},
      autoData:[],
      isShowFinishDetail:false,
      isReloadParam:false,
      remark: '',
      title: '手工确认',
      popoverVisible: false,
      isShowEndTime: false,
      isShowRemark:true,
      actionType: '',
      svgImg: this.$lcImg,
    }
  },
  beforeMount() {
    if (this.params.data) {
      this.remark = this.params.data.remark;
      this.form.planEndTime = this.params.data.planEndTime;
      if (!this.params.data.stepActType) {
        this.params.data.stepActType = ''
      }
      if (!this.params.data.stepStatus) {
        this.params.data.stepStatus = ''
      }
    }
  },
  computed: {
    //任务完成返回false
    isdown() {
      return this.params.data.stepStatus !='06' && this.params.data.stepStatus !='07';
    },
    //任务完成返回TRUE
    isDisabled() {
      return this.params.data.stepStatus =='01'  ||  this.params.data.stepStatus == '06' || this.params.data.stepStatus == '07';
    },
    //是否有操作权限
    isCanExec() {
      return  true;
    },
    //任务未开始返回TRUE
    isUnDoing() {
      return this.params.data.stepStatus =='01';
    },
    //表单录入
    isFormAction() {
      return (this.params.data.allowFormAction === '1' || this.params.data.dealType?.match(/01|12|20/) )  && Permission.hasPermission('agnes.elec.task.formAction') ;
    },
    lookFinishDetailShow() {
      return this.params.data.stepActType === '6' && this.params.data.stepStatus === '06' && this.params.data.hasTaskParam != '0' && Permission.hasPermission('agnes.elec.task.confirm');
    },
    isKpiDisabled() {
      return this.params.data.stepStatus === '01';
    },
    //手工确认
    actionShow() {
      return (this.params.data.allowActionConfirm === '1'  || this.params.data.stepActType.match(/6/) || this.params.data.dealType?.match(/21/))  && Permission.hasPermission('agnes.elec.task.actionShow');
    },
    //文件干预通过
    fileForcePass() {
      return (this.params.data.stepActType.match(/9/) || this.params.data.dealType?.match(/07/))  &&  Permission.hasPermission('agnes.elec.task.confirm')
    },
    // 查询执行记录
    queryExecRecords(){
      return (this.params.data.dealType !== undefined && this.params.data.dealType !== null && this.params.data.dealType !== '')
    },
    forcePass() {
      //allowManualConfirm 为场景配置中 1:允许干预通过 0：不允许干预通过
      return !(this.params.data.stepActType.match(/9|6|01|05|07/) || this.params.data.dealType?.match(/07/)) && (this.params.data.allowManualConfirm === '1' || this.params.data.stepActType.match(/1|3|7|10|02|04|06/) || this.params.data.stepActType==='01' || this.params.data.dealType?.match(/23/))
    },
    //重新执行
    reExecuteShow() {
      return (this.params.data.isAllowExecAgain === '1'  || this.params.data.stepActType.match(/9|02|06/) || this.params.data.stepActType==='01' || this.params.data.dealType?.match(/07/))  && Permission.hasPermission('agnes.elec.task.exec');
    },
    //查看明细
    showDetail() {
      return (this.params.data.allowShowDetail==='1' || this.params.data.stepActType.match(/9|10/) || this.params.data.stepActType==='1' || this.params.data.dealType?.match(/05|07|11|24/) )  && Permission.hasPermission('agnes.elec.task.showDetail');
    },
    //查看回填参数
    finishDetailShow() {
      return this.params.data.stepActType === '6' && (this.params.data.stepStatus === '06' || this.params.data.stepStatus === '07') && this.params.data.hasTaskParam != '0' && Permission.hasPermission('agnes.elec.task.actionShow');
    },
    openTradeDetailShow() {
      return this.params.data.stepActType === '7';
    },
    ataAuditDetailShow() {
      return this.params.data.dealType === '27';
    },
    manualStartStepShow(){
      return this.params.data.taskStatus ==='01' && Permission.hasPermission('agnes.elec.step.manual.start') && new Date>this.params.data.planStartTime;
    },
    indexEditShow() {
      return Permission.hasPermission('agnes.elec.task.edit') && this.params.data.allowEditEndTime === '1';
    },
    isFinished(){
      return this.params.data.stepStatus.match(/06|07/)
    },
    isEditDisabled() {
      let isEditDisabledStatus = !(this.params.data.stepStatus === '02' || this.params.data.stepStatus ==='03');
      if (this.params.data.planEndTime) {
        let bizDate = window.bizDate+' '+new Date().getHours()+':'+new Date().getMinutes()+':'+new Date().getSeconds();
        if(new Date(bizDate)>new Date(this.params.data.planEndTime)){
          isEditDisabledStatus = true;
        }
      }
      return isEditDisabledStatus
    }
  },
  methods: {
    async popoverClick(actionType) {
      this.rules = {};
      this.form.paramList = [];
      this.isShowFinishDetail = false;
      this.isReloadParam = false;
      this.isShowRemark = true
      if(['actionConfirm', 'forcePass', 'editEndTime', 'reloadParam'].includes(actionType)){
        this.$nextTick(()=>{
          this.params.api.execCmd('clearFreshInterval', false);
        })
      }
      this.actionType = actionType;
      if(actionType=='actionConfirm'){
        //手工确认，判断是否有回填参数
        await this.selectRollBackTaskParams()
        this.title= '手工确认';
        this.form.remark = this.params.data.remark;
        this.popoverVisible = true;
      }else if(actionType=='autoTaskActionConfirm'){
        //待确定
        await this.getAutoTaskParam()
        this.title= '返回值';
        this.popoverVisible = true;
      }else if(actionType == 'forcePass'){
        this.title= '干预通过';
        this.popoverVisible = true;
      }else if(actionType == 'fileForcePass'){
        this.handleCmd(actionType);
      }else if(actionType == 'queryExecRecords'){
        this.handleCmd(actionType);
      }else if(actionType == 'finishDetailShow' || actionType == 'reloadParam'){
        if(this.params.data.stepExecId && this.params.data.caseId){
          await this.getCaseStepParam()
        }
        this.title= '查看回填参数';
        this.isShowFinishDetail = true;
        if(actionType == 'reloadParam'){
          this.title= '回填参数重新录入';
          this.isShowFinishDetail = false;
          this.isReloadParam = true;
        }
        this.isShowRemark = false;
        this.popoverVisible = true;
      }else if(actionType == 'isFormAction'){
        //表单录入
        this.handleCmd(actionType);
      }
    },
    /**
     * 手工确认，判断是否有回填参数
     */
    async selectRollBackTaskParams() {
      const p = this.$api.taskTodoApi.selectRollBackTaskParams({stepExecId:this.params.data.stepExecId})
      const resp = await this.$app.blockingApp(p);
      this.form.paramList=resp.data;
      this.form.paramList.forEach(item=>{
        if(item.isRequire == '1'){
          let ruleItem = item.paramKey;
          this.rules[ruleItem] = [{validator: this.checkRule,required: true, trigger: 'change'}];
        }
      })
    },
    /**
     * 返回值
     */
    async getAutoTaskParam() {
      this.autoData=[];
      const p = this.$api.taskTodoApi.getAutoTaskParam({stepExecId:this.params.data.stepExecId})
      const resp = await this.$app.blockingApp(p);
      if(resp.data){
        this.autoData=resp.data;
      }
    },
    /**
     * 查看回填参数
     */
    async getCaseStepParam() {
      let taskCommit = {
        caseId:this.params.data.caseId,
        stepExecId:this.params.data.stepExecId,
      }
      const s = this.$api.taskDefineApi.getCaseStepParam(taskCommit)
      let resp = await this.$app.blockingApp(s);
      if(resp.data){
        this.form.paramList=resp.data;
      }
    },
    async taskChangeMethod(actionType){
      this.actionType = actionType;
      this.handleCmd(this.actionType);
    },
    getLists(fieldValueRange) {
      let lists = [];
      const fieldRange = fieldValueRange.split(';')
      if (fieldRange) {
        fieldRange.forEach(item => {
          const values = item.split(':');
          if(!values){
            return;
          }
          if (values.length > 1) {
            lists.push({
              name: values[1],
              value: values[0],
            })
          } else {
            lists.push({
              name: values[0],
              value: values[0],
            })
          }
        })
      }
      return lists;

    },

    checkRule(rule, value, callback) {
      callback();
    },
    async reExecute(actionType) {
      this.actionType = actionType;
      const ok = await this.$msg.ask(`是否确认重新执行`);
      if (ok) {
        this.handleCmd(this.actionType);
      }
    },
    async openKjd(actionType) {
      this.handleCmd(actionType);
    },
    async openDataAuditDetail(actionType) {
      this.handleCmd(actionType);
    },

    async manualStartStep(actionType){
      this.actionType = actionType;
      const ok = await this.$msg.ask(`是否要手动启动任务`);
      if (ok) {
        this.handleCmd(this.actionType);
      }
    },

    indexDetail(actionType) {
      this.handleCmd(actionType);
    },
    editEndTime(actionType) {
      this.title = '编辑';
      this.popoverVisible = true;
      this.isShowEndTime = true;
      this.isShowRemark = false;
      this.actionType = actionType;
    },

    // 备注确定 -- 保存
    async confirmRemark() {
      let isReturn = false;
      if(this.form.paramList && this.form.paramList.length>0){
        this.form.paramList.forEach(item=>{
          if(this.verifRuquire(item)){
            this.$msg.warning('请补充完整必填项！');
            isReturn = true;
            return;
          }
          if(item.paramType=='date'){
            this.params.data.isHasdate = '1';
          }
        })
      }
      if(isReturn){
        return;
      }
      this.popoverVisible = false;
      this.isShowEndTime = false;
      this.isShowRemark = true;
      this.params.data.prdtInfo = this.prdtInfo;
      this.params.data.remark = this.form.remark;
      this.params.data.paramList = this.form.paramList;
      this.params.data.planEndTime = this.form.planEndTime;
      this.params.api.refreshCells(this.params.node);
      let type = this.actionType;
      if(type=='actionV2Mot'){
        type = 'actionConfirm';
      }
      this.handleCmd(type);
      this.$nextTick(()=>{
        this.params.api.execCmd('startFreshInterval', false);
      })
      this.form.paramList = [];
    },
    /**
     * 校验必填项
     * @param item
     */
    verifRuquire(item) {
      let v = item.isRequire == '1'
      let v1 = item.paramType == 'string' && !item.paramString
      let v2 = item.paramType == 'number' && !item.paramNumber
      let v3 = item.paramType == 'amount' && !item.paramAmount
      let v4 = item.paramType == 'date' && !item.paramDate
      return v && (v1 || v2 || v3 || v4)
    },
    closeRemake() {
      this.popoverVisible = false
      this.isShowEndTime = false;
      this.isShowRemark = true;
      this.form.paramList = [];
      if(this.actionType=='actionConfirm'||this.actionType=='forcePass'||this.actionType=='editEndTime'||this.actionType=='reloadParam' ){
        this.$nextTick(()=>{
          this.params.api.execCmd('startFreshInterval', false);
        })
      }
    },

    handleCmd(actionType) {
      this.params.api.execCmd(actionType, this.params);
    }
  }
}
</script>


<style scoped>
.optional-cell {
  line-height: 1;
}

.optional-cell .svgSpan {
  display: inline-block;
  width: 16px;
  height: 16px;
  cursor: pointer;
}
.optional-cell .svgSpan >>> svg {
  width: 100%;
  height: 100%;
}

.optional-cell > span + span {
  margin-left: 10px;
}

.optional-cell .svgSpan > svg {
  width: 100%;
  height: 100%;
}

.op-btn {
  height: 25px;
  padding: 5px 19px;
  border-color: #E1E4E8;
  color: #333;
  background: transparent;
  font-size: 14px;
}

.op-btn.primary {
  color: #fff;
  background: #0f5eff;
  border-color: #0f5eff;
}

.detail-btn {
  padding: 7px 0;
}

.detail-btn>>>.fa.fa-eye{
  font-size: 15px;
  vertical-align: text-bottom;
  color: #0f5eff;
  line-height: 17px;
}

.detail-btn.is-disabled>>>.fa.fa-eye {
  color: #ccc;
}
.repeat {
  font-size: 15px;
  vertical-align: text-bottom;
  color: #0f5eff;
  line-height: 17px;
}
.el-form{
  padding:0 20px 0 0;
}
.el-form >>> .el-form-item {
  margin-bottom: 10px;
}
.el-form >>> .el-form-item .el-form-item__label {
  width: 95px !important;
}
.el-form >>> .el-form-item .el-form-item__content {
  margin-left: 95px !important;
}
</style>
