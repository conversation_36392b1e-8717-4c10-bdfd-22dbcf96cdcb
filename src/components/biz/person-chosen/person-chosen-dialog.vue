<template>
    <div class="person-chosen-dialog">
        <div class="left">
            <div class="tag-title">
                <el-tag size="small" v-if="chosenType.indexOf('user') > -1">人员</el-tag>
                <el-tag size="small" type="success" v-if="chosenType.indexOf('group') > -1">群组</el-tag>
                <el-tag size="small" type="warning" v-if="chosenType.indexOf('roster') > -1">值班</el-tag>
                <el-tag size="small" type="warning" v-if="chosenType.indexOf('depart') > -1">部门</el-tag>
                <el-tag size="small" type="info" v-if="chosenType.indexOf('role') > -1">角色</el-tag>
                <el-tag size="small" type="info" v-if="chosenType.indexOf('custom') > -1">自定义</el-tag>
            </div>
            <div class="tag-container">
                <chosen-list-view :personList="personList" :groupList="groupList" :rosterList="rosterList"
                    :customList="customList" @reloadData="reloadData"></chosen-list-view>
            </div>
            <div>
                <el-checkbox v-model="checked">加入我的常用</el-checkbox>
            </div>
        </div>
        <div class="right">
            <el-tabs type="card" v-model="activeTabPane" style="height: 435px;">
                <el-tab-pane name="user" v-if="chosenType.indexOf('user') > -1">
                    <span class="tab-label" slot="label">
                        <span>用户列表</span>
                    </span>
                    <gf-grid
                        :filterRemote="false"
                        ref="personGrid"
                        grid-no="agnes-dop-memo-member-user-list"
                        class="user-choose-list"
                        :query-args="userQueryArgs"
                        @row-double-click="choseUser"
                        :options="rowClassOption1"
                        @load-data="
                            params => {
                                gridLoadData(personList, 'userId', params);
                            }
                        "
                        height="100%"
                        toolbar="more"
                    >
                        <template slot="left">
                            <gf-button class="action-btn" @click="chooseAllUsers('personGrid')" size="mini">全选</gf-button>
                        </template>
                        <template slot="right-before">
                            <gf-input
                                v-model="searchValue"
                                :max-byte-len="80"
                                placeholder="快速过滤"
                                clearable
                                @keyup.enter.native="searchValueChange"
                            >
                                <em slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="searchValueChange"></em>
                            </gf-input>
                        </template>
                    </gf-grid>
                </el-tab-pane>
                <el-tab-pane name="group" v-if="chosenType.indexOf('group') > -1">
                    <span class="tab-label" slot="label">
                        <span>群组列表</span>
                    </span>
                    <gf-grid
                        :filterRemote="false"
                        class="paginationGrid"
                        ref="groupGrid"
                        grid-no="agnes-dop-memo-member-user-group-list"
                        :options="rowClassOption2"
                        @load-data="
                            params => {
                                gridLoadData(groupList, 'userGroupId', params);
                            }
                        "
                        height="100%"
                    ></gf-grid>
                </el-tab-pane>
                <el-tab-pane name="depart" v-if="chosenType.indexOf('depart') > -1">
                    <span class="tab-label" slot="label">
                        <span>部门列表</span>
                    </span>
                <gf-grid
                    :filterRemote="false"
                    class="paginationGrid"
                    ref="departGrid"
                    grid-no="agnes-dop-memo-member-depart-list"
                    :options="rowClassOption5"
                    @load-data="
                            params => {
                                gridLoadData(departList, 'orgId', params);
                            }
                        "
                    height="100%"
                ></gf-grid>
              </el-tab-pane>
                <el-tab-pane name="role" v-if="chosenType.indexOf('role') > -1">
                    <span class="tab-label" slot="label">
                        <span>角色列表</span>
                    </span>
                <gf-grid
                    :filterRemote="false"
                    class="paginationGrid"
                    ref="roleGrid"
                    grid-no="agnes-dop-memo-member-role-list"
                    :options="rowClassOption6"
                    @load-data="
                            params => {
                                gridLoadData(roleList, 'roleId', params);
                            }
                        "
                    height="100%"
                ></gf-grid>
              </el-tab-pane>
                <el-tab-pane name="roster" v-if="chosenType.indexOf('roster') > -1">
                    <span class="tab-label" slot="label">
                        <span>值班列表</span>
                    </span>
                    <gf-grid
                        :filterRemote="false"
                        ref="rosterGrid"
                        grid-no="agnes-dop-memo-member-roster-list"
                        :options="rowClassOption3"
                        :query-args="rosterQueryArgs"
                        @load-data="
                            params => {
                                gridLoadData(rosterList, 'rosterId', params);
                            }
                        "
                        height="100%"
                    ></gf-grid>
                </el-tab-pane>

                <el-tab-pane name="commonUse">
                    <span class="tab-label" slot="label">
                        <span>我的常用</span>
                    </span>
                    <gf-grid
                        :filterRemote="false"
                        class="commonUseGrid"
                        ref="commonUseGrid"
                        grid-no="agnes-dop-memo-member-common-user-list"
                        @load-data="
                            params => {
                                gridLoadData1(groupList, personList, 'memberId', params);
                            }
                        "
                        :options="rowClassOption4"
                        :query-args="queryArgs"
                        height="100%"
                    ></gf-grid>
                </el-tab-pane>

                <el-tab-pane name="custom" v-if="chosenType.indexOf('custom') > -1">
                    <span class="tab-label" slot="label">
                        <span>自定义</span>
                    </span>
                    <div class="custom-card">
                        <gf-input v-model="customEmail" placeholder="请输入"></gf-input>
                        <gf-button type="primary" class="add-btn" @click="choseCustom">添加</gf-button>
                    </div>
                </el-tab-pane>

            </el-tabs>
        </div>
        <dialog-footer :onSave="saveChosen"></dialog-footer>
    </div>
</template>

<script>
import chosenListView from './chosen-list-view';
import PinYinMatch from "pinyin-match";

const rowClassOption = {
    rowClassRules: {
        'disabled-row': function (params) {
            return params.data && params.data.checked;
        }
    }
}

export default {
    name: 'person-chosen',
    props: {
        personList: {
            type: Array
        },
        groupList: {
            type: Array
        },
        departList: {
          type: Array
        },
        roleList: {
          type: Array
        },
        rosterList: {
            type: Array
        },
        customList: {
            type: Array
        },
        rosterDate: {
            type: String
        },
        chosenType: {
            type: String
        },
        activeTab: {
            type: String,
            default: 'user'
        },
        actionOk: Function
    },
    data() {
        return {
            checked: false,
            chosenRosterDate: '',
            rosterQueryArgs: {
                dictTypeId: 'AGNES_ROSTER_TYPE',
                appId: 'AGNES'
            },
            queryArgs: {
                type: this.chosenType
            },
            rowClassOption1: {
               ...rowClassOption,
            },
            rowClassOption2: {
               ...rowClassOption,
            },
            rowClassOption3: {
               ...rowClassOption,
            },
            rowClassOption4: {
               ...rowClassOption,
            },
            rowClassOption5: {
               ...rowClassOption,
            },
            rowClassOption6: {
               ...rowClassOption,
            },
            activeTabPane: this.activeTab,
            customEmail: '', //自定义输入的名称

            userQueryArgs: {
                filterText: '',
            },
            searchValue:'',
            gridDataList:[],
        };
    },
    components: {
        'chosen-list-view': chosenListView
    },
    beforeMount() {
        this.chosenRosterDate = this.rosterDate;
        this.$refs.rosterGrid.reloadData();
    },
    methods: {
        // 表格数据初始加载 -- 已勾选项赋值
        gridLoadData(list, findId, params) {
            params.rows.forEach(oneData => {
                const hasChecked = this.$lodash.find(list, { memberId: oneData[findId] });
                if (hasChecked) {
                    oneData.checked = true;
                }
            });
        },
        // 我的常用表格数据初始加载 -- 已勾选项赋值
        gridLoadData1(list0, list1, findId, params) {
            params.rows.forEach(oneData => {
                const hasChecked = this.$lodash.find(list0, { memberId: oneData[findId] });
                if (hasChecked) {
                    oneData.checked = true;
                }
            });
            params.rows.forEach(oneData => {
                const hasChecked = this.$lodash.find(list1, { memberId: oneData[findId] });
                if (hasChecked) {
                    oneData.checked = true;
                }
            });
        },
        // 删除tag重新加载表格数据
        reloadData(type) {
            const gridType = type.replace('List', 'Grid');
            if (typeof this.$refs[gridType]?.reloadData === 'function') {
                this.$refs[gridType].reloadData();
            }
        },

        // 新增人员
        choseUser(params) {
            let userName = '';
            if(params.data?.userName){
                userName = params.data?.userName;
            }else{
                userName = params.data?.username;
            }
            const member = {
                refType: '1',
                memberId: params.data.userId,
                memberDesc: userName
            };
            const hasChecked = this.$lodash.find(this.personList, { memberId: params.data.userId });
            if (hasChecked) return this.$msg.msg('请勿重复添加', 'warning');
            this.personList.push(member);
            params.data.checked = true;
            if (params.node) {
                this.$refs.personGrid.refreshRows([params.node]);
                this.$refs.commonUseGrid.reloadData();
            }
        },

        // 新增分组
        choseUserGroup(params) {
            const member = {
                refType: '2',
                memberId: params.data.userGroupId,
                memberDesc: `群组-${params.data.userGroupName}`
            };
            const hasChecked = this.$lodash.find(this.groupList, { memberId: params.data.userGroupId });
            if (hasChecked) return this.$msg.msg('请勿重复添加', 'warning');
            this.groupList.push(member);
            params.data.checked = true;
            this.$refs.groupGrid.refreshRows([params.node]);
        },

        // 新增常用人/常用群组
        choseCommonUser(params) {
            const member = {
                refType: params.data.refType,
                memberId: params.data.memberId,
                memberDesc: params.data.memberDesc
            };
            if (params.data.refType == 1) {
                const hasChecked = this.$lodash.find(this.personList, { memberId: params.data.memberId });
                if (hasChecked) return this.$msg.msg('请勿重复添加', 'warning');
                this.personList.push(member);
                params.data.checked = true;
                params.data.userId = params.data.memberId;
                params.data.userName = params.data.memberDesc;
                if (params.node) {
                    this.$refs.commonUseGrid.refreshRows([params.node]);
                    this.$refs.personGrid.reloadData();
                }
            }
            if (params.data.refType == 2) {
                const hasChecked = this.$lodash.find(this.groupList, { memberId: params.data.memberId });
                if (hasChecked) return this.$msg.msg('请勿重复添加', 'warning');
                this.groupList.push(member);
                params.data.checked = true;
                this.$refs.commonUseGrid.refreshRows([params.node]);
                this.$refs.groupGrid.reloadData();
            }
          if (params.data.refType == 5) {
            const hasChecked = this.$lodash.find(this.departList, { memberId: params.data.memberId });
            if (hasChecked) return this.$msg.msg('请勿重复添加', 'warning');
            this.departList.push(member);
            params.data.checked = true;
            this.$refs.commonUseGrid.refreshRows([params.node]);
            this.$refs.departGrid.reloadData();
          }
          if (params.data.refType == 6) {
            const hasChecked = this.$lodash.find(this.roleList, { memberId: params.data.memberId });
            if (hasChecked) return this.$msg.msg('请勿重复添加', 'warning');
            this.roleList.push(member);
            params.data.checked = true;
            this.$refs.commonUseGrid.refreshRows([params.node]);
            this.$refs.roleGrid.reloadData();
          }
        },

        // 新增值班
        choseRoster(params) {
            const rosterName = params.data.dictName;
            const member = {
                refType: '3',
                memberId: params.data.dictId,
                // 值班-部门 值班类型 值班日期 值班时间 岗位
                memberDesc: `值班-${rosterName}`
            };
            this.rosterList.push(member);
            params.data.checked = true;
            this.$refs.rosterGrid.refreshRows([params.node]);
        },

        //新增自定义
        choseCustom() {
            if (!this.customEmail.trim()) return;
            //判断列表中是否已有该数据
            const hasChecked = this.$lodash.find(this.customList, { memberDesc: this.customEmail });
            if (hasChecked) return this.$msg.msg('请勿重复添加', 'warning');
            const member = {
                refType: '4',
                memberId: this.customEmail,
                memberDesc: this.customEmail
            };
            this.customList.push(member);
            this.customEmail = '';
        },

        //新增部门
        choseDepartMent(params) {
          const member = {
            refType: '5',
            memberId: params.data.orgId,
            memberDesc: `部门-${params.data.orgName}`
          };
          const hasChecked = this.$lodash.find(this.departList, { memberId: params.data.orgId });
          if (hasChecked) return this.$msg.msg('请勿重复添加', 'warning');
          this.departList.push(member);
          params.data.checked = true;
          this.$refs.departGrid.refreshRows([params.node]);
        },

        //新增角色
        choseRole(params) {
          const member = {
            refType: '6',
            memberId: params.data.roleId,
            memberDesc: `角色-${params.data.roleName}`
          };
          const hasChecked = this.$lodash.find(this.roleList, { memberId: params.data.roleId });
          if (hasChecked) return this.$msg.msg('请勿重复添加', 'warning');
          this.roleList.push(member);
          params.data.checked = true;
          this.$refs.roleGrid.refreshRows([params.node]);
        },
        // 保存选择信息
        async saveChosen() {
            this.actionOk(this.personList, this.groupList,this.departList,this.roleList, this.rosterList, this.customList);
            this.$dialog.close(this);
        },

        //全选所有人员
        chooseAllUsers() {
            this.$refs.personGrid.gridController.gridApi.forEachNodeAfterFilter(node => {
                if (!node.data.checked) {
                    this.choseUser(node);
                    this.$refs.personGrid.refreshRows([node]);
                }
            });
        },
        async searchValueChange(){
                let fiterList = [];
                let idFilterList = [];
                if(this.isAllEnglish(this.searchValue)){
                    this.gridDataList.forEach(item =>{
                        let matchRes = PinYinMatch.match(item.username, this.searchValue);
                        if(matchRes){
                            fiterList.push(item)
                        }
                    })
                    this.gridDataList.forEach((item)=>{
                        if(item.userId.includes(this.searchValue)){
                            idFilterList.push(item)
                        }
                    })
                    let map = new Map();
                    let temp = [...fiterList,...idFilterList];
                    for(let item of temp){
                        map.set(item.id, item);
                    }
                    let result = Array.from(map.values());
                    this.$refs.personGrid.setRowData(result);
                }else{
                    this.userQueryArgs.filterText=this.searchValue;
                    this.$refs.personGrid.reloadData();
                }
            },
            //判断是不是全是英文字母
            isAllEnglish(str){
                return /^[a-zA-Z]+$/.test(str);
            }
    }
};
</script>
<style scoped>
/*选人组件用户列表 快速过滤宽度拉长*/
.right >>> .user-choose-list .grid-action-panel .toolbar-left {
    flex-grow: 0;
}
.right >>> .user-choose-list .grid-action-panel .right .el-input {
    width: 350px !important;
}

.add-btn {
    border-color: #0f5eff;
    background-color: #0f5eff;
}
.custom-card {
    display: flex;
    width: 80%;
    margin-top: 10px;
}
.custom-card .add-btn {
    margin-left: 20px;
}
</style>
