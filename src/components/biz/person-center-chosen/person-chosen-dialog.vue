<template>
    <div class="person-chosen-dialog">
        <div class="left">
            <div class="tag-title">
                <el-tag size="small" v-if="chosenType.indexOf('user')>-1">人员</el-tag>
                <el-tag size="small" type="success" v-if="chosenType.indexOf('group')>-1">岗位</el-tag>
                <el-tag size="small" type="warning" v-if="chosenType.indexOf('roster')>-1">值班</el-tag>
                <el-tag size="small" type="info" v-if="chosenType.indexOf('custom')>-1">自定义</el-tag>
            </div>
            <div class="tag-container">
                <chosen-list-view :personList="personList"
                                  :groupList="groupList"
                                  :rosterList="rosterList"
                                  :customList="customList"
                                  @reloadData="reloadData"
                ></chosen-list-view>
            </div>
        </div>
        <div class="right">
            <el-tabs type="card" v-model="activeTabPane" style="height: 435px;">
                <el-tab-pane name="user" v-if="chosenType.indexOf('user')>-1">
                    <span class="tab-label" slot="label">
                        <span>用户列表</span>
                    </span>
                    <gf-grid :filterRemote="false" ref="personGrid"
                             grid-no="agnes-center-memo-member-user-list"
                             class="user-choose-list"
                             @row-double-click="choseUser"
                             :query-args="userQueryArgs"
                             :options="rowClassOption1"
                             @load-data="(params)=>{gridLoadData(personList, 'userId', params)}"
                             height="100%" toolbar="more">
                        <template slot="left">
                            <gf-button class="action-btn" @click="chooseAllUsers('personGrid')" size="mini">全选</gf-button>
                        </template>
                        <template slot="right-before">
                            <gf-input
                                v-model="searchValue"
                                :max-byte-len="80"
                                placeholder="快速过滤"
                                clearable
                                @keyup.enter.native="searchValueChange"
                            >
                                <em slot="suffix" class="el-input__icon el-icon-search" style="cursor: pointer;" @click="searchValueChange"></em>
                            </gf-input>
                        </template>
                    </gf-grid>
                </el-tab-pane>
                <el-tab-pane name="group" v-if="chosenType.indexOf('group')>-1">
                    <span class="tab-label" slot="label">
                        <span>岗位列表</span>
                    </span>
                    <gf-grid :filterRemote="false" class="paginationGrid"
                             ref="groupGrid"
                             :query-args="groupQueryArgs"
                             grid-no="agnes-dop-memo-member-user-group-list"
                             :options="rowClassOption2"
                             @row-double-click="choseUserGroup"
                             @load-data="(params)=>{gridLoadData(groupList, 'userGroupId', params)}"
                             height="100%">
                            <template slot="left">
                                <gf-button class="action-btn" @click="chooseAllUsers('groupGrid')" size="mini">全选</gf-button>
                            </template>
                            </gf-grid>
                </el-tab-pane>
                <el-tab-pane name="roster" v-if="chosenType.indexOf('roster')>-1">
                    <span class="tab-label" slot="label">
                        <span>值班列表</span>
                    </span>
                    <gf-grid :filterRemote="false" ref="rosterGrid"
                            grid-no="agnes-dop-memo-member-roster-list"
                             :options="rowClassOption3"
                             :query-args="rosterQueryArgs"
                             @load-data="(params)=>{gridLoadData(rosterList, 'rosterId', params)}"
                            height="100%">
                    </gf-grid>
                </el-tab-pane>
                <el-tab-pane name="custom" v-if="chosenType.indexOf('custom')>-1">
                    <span class="tab-label" slot="label">
                        <span>自定义</span>
                    </span>
                   <div class="custom-card">
                       <gf-input v-model="customEmail" placeholder="请输入"></gf-input>
                       <gf-button type="primary" class="add-btn" @click="choseCustom">添加</gf-button>
                   </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <dialog-footer :onSave="saveChosen"></dialog-footer>
    </div>
</template>

<script>
    import chosenListView from './chosen-list-view'
    import PinYinMatch from "pinyin-match";

    const rowClassOption = {
        rowClassRules: {
            'disabled-row': function (params) {
                return params.data && params.data.checked;
            }
        },
    };

    export default {
        name: 'person-chosen',
        props: {
            orgIdList:{
                type: Array,
            },
            personList: {
                type: Array
            },
            groupList: {
                type: Array
            },
            rosterList: {
                type: Array
            },
            customList: {
                type: Array
            },
            rosterDate: {
                type: String
            },
            chosenType: {
                type: String
            },
            activeTab: {
                type: String,
                default: 'user'
            },
            // 是否是转办
            isChangeHandleTaskUser: {
                type: Boolean,
                default: false
            },
            actionOk: Function
        },
        data() {
            return {
                chosenRosterDate: '',
                userQueryArgs:{
                    orgIdList: this.orgIdList || [],
                    selectAll: '1',
                    filterText:''
                },
                groupQueryArgs:{
                    orgIdList: this.orgIdList || [],
                },
                rosterQueryArgs: {
                    dictTypeId:'AGNES_ROSTER_TYPE',
                    appId:'AGNES'
                },
                rowClassOption1: {
                   ...rowClassOption
                },
                rowClassOption2:  {
                   ...rowClassOption
                },
                rowClassOption3:  {
                   ...rowClassOption
                },
                activeTabPane: this.activeTab,
                customEmail: '', //自定义输入的名称
                searchValue:'',
                gridDataList:[],
            }
        },
        components: {
            'chosen-list-view': chosenListView
        },
       async beforeMount(){
            let resp = await this.$api.MccTaskConfigApi.getPersonList(this.userQueryArgs);
            this.gridDataList = resp.data;
            this.chosenRosterDate = this.rosterDate;
           
        },
        methods: {
            // 表格数据初始加载 -- 已勾选项赋值
            gridLoadData(list, findId, params){
                params.rows.forEach((oneData)=>{
                    const hasChecked = this.$lodash.find(list, {memberId: oneData[findId]});
                    if(hasChecked){
                        oneData.checked = true;
                    }
                });
            },

            // 删除tag重新加载表格数据
            reloadData(type) {
                const gridType = type.replace('List', 'Grid');
                if(typeof this.$refs[gridType]?.reloadData === 'function'){
                    this.$refs[gridType].reloadData();
                }
            },

            // 新增人员
            choseUser(params){
                if(params.data.checked) return
                // 转办人不能选择自己
                const currentUser = window.$gfui.$app.session.data.user.userId;
                if(this.isChangeHandleTaskUser && currentUser === params.data.userId) return this.$msg.info('转办人不能选择自己！');
                const member = {
                    refType: '1',
                    memberId: params.data.userId,
                    memberDesc: params.data.username
                }
                this.personList.push(member);
                params.data.checked = true;
                if(params.node) this.$refs.personGrid.refreshRows([params.node]);
            },

            // 新增分组
            choseUserGroup(params){
                if(params.data.checked) return
                const member = {
                    refType: '2',
                    memberId: params.data.userGroupId,
                    memberDesc: `岗位-${params.data.userGroupName}`
                }
                this.groupList.push(member);
                params.data.checked = true;
                this.$refs.groupGrid.refreshRows([params.node]);
            },

            // 新增值班
            choseRoster(params){
                const rosterName = params.data.dictName;
                const member = {
                  refType: '3',
                  memberId: params.data.dictId,
                  // 值班-部门 值班类型 值班日期 值班时间 岗位
                  memberDesc: `值班-${rosterName}`
                }
                this.rosterList.push(member);
                params.data.checked = true;
                this.$refs.rosterGrid.refreshRows([params.node]);
            },

            //新增自定义
            choseCustom(){
                if(!this.customEmail.trim()) return
                //判断列表中是否已有该数据
                const hasChecked = this.$lodash.find(this.customList, {memberDesc: this.customEmail});
                if(hasChecked) return this.$msg.msg('请勿重复添加', 'warning')
                const member = {
                    refType: '4',
                    memberId: this.customEmail,
                    memberDesc: this.customEmail
                }
                this.customList.push(member);
                this.customEmail = ''
            },
            // 保存选择信息
            saveChosen(){
                let arrList = [...this.personList, ...this.groupList, ...this.rosterList, ...this.customList]
                if(arrList.length > 10) {
                    this.$msg.msg('最多只能选择10个用户', 'warning');
                    return;
                }
                this.actionOk(this.personList, this.groupList, this.rosterList, this.customList);
                this.$dialog.close(this);
            },

            //全选所有人员
            chooseAllUsers(ref){
                this.$refs[ref].gridController.gridApi.forEachNodeAfterFilter((node)=> {
                    if(!node.data.checked) {
                        if(ref === 'personGrid') {
                            this.choseUser(node)
                        }else if(ref === 'groupGrid') {
                            this.choseUserGroup(node)
                        }
                        this.$refs[ref].refreshRows([node]);
                    }
                })
            },
            async searchValueChange(){
                let fiterList = [];
                let idFilterList = [];
                if(this.isAllEnglish(this.searchValue)){
                    this.gridDataList.forEach(item =>{
                        let matchRes = PinYinMatch.match(item.username, this.searchValue);
                        if(matchRes){
                            fiterList.push(item)
                        }
                    })
                    this.gridDataList.forEach((item)=>{
                        if(item.userId.includes(this.searchValue)){
                            idFilterList.push(item)
                        }
                    })
                    let map = new Map();
                    let temp = [...fiterList,...idFilterList];
                    for(let item of temp){
                        map.set(item.id, item);
                    }
                    let result = Array.from(map.values());
                    this.$refs.personGrid.setRowData(result);
                }else{
                    this.userQueryArgs.filterText=this.searchValue;
                    this.$refs.personGrid.reloadData();
                }
            },
            //判断是不是全是英文字母
            isAllEnglish(str){
                return /^[a-zA-Z]+$/.test(str);
            }
        }
    }
</script>
<style scoped>
/*选人组件用户列表 快速过滤宽度拉长*/
.right >>> .user-choose-list .grid-action-panel .toolbar-left{
    flex-grow: 0;
}
.right >>> .user-choose-list .grid-action-panel .right .el-input{
    width: 350px !important;
}
.add-btn {
    border-color: #0f5eff;
    background-color: #0f5eff;
}
.custom-card{
    display: flex;
    width: 80%;
    margin-top: 10px;
}
.custom-card .add-btn{
    margin-left: 20px;
}
</style>
